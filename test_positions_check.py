from datetime import datetime as dtdt
import pandas as pd
from Algo.Trading.positions_check import get_diff_between_actual_and_needed

# Test with a single mode (string)
day = dtdt(2023, 1, 27)
mode_single = 'ens_cluster_dynamic_v3b_S0.5_w9'
print("Testing with single mode:", mode_single)
result_single, trades_stack_single = get_diff_between_actual_and_needed(day, mode_single, write_csv=False)
print(f"Result shape: {result_single.shape}")
print(f"Number of trades: {len(trades_stack_single)}")

# Test with multiple modes (list)
mode_list = ['ens_cluster_dynamic_v3b_S0.5_w9', 'ens_cluster_dynamic_v4_S0.5_w9x5d_loose']
print("\nTesting with multiple modes:", mode_list)
result_list, trades_stack_list = get_diff_between_actual_and_needed(day, mode_list, write_csv=False)
print(f"Result shape: {result_list.shape}")
print(f"Number of trades: {len(trades_stack_list)}")

# Print sample of results
if not result_single.empty:
    print("\nSample of single mode result:")
    print(result_single.head())

if not result_list.empty:
    print("\nSample of multiple modes result:")
    print(result_list.head())
