from selenium import webdriver
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
import time
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time
import os

from subprocess import check_call
import subprocess32
from subprocess32 import check_call as check_call32
from subprocess32 import Popen as Popen32


from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time


MAX_Z = 384
midi_above_240_zs = [12*i for i in range(20,33)]
midi_below_240 = [3*i for i in range(80)]
ALL_HORIZONS =  midi_below_240 +  midi_above_240_zs
GFS_4_HORIZONS = [z for z in ALL_HORIZONS if z <= MAX_Z]
GEFS_HORIZONS = range(0,390,6)
PARA_HORIZONS = range(0,385,3)
GEM_HORIZONS = range(0,241,3)
GEPS_HORIZONS = list(range(0,193,3))+list(range(198,385,6))

MODELS_LATENCIES = {'GFS': [3, 45], 'GEFS': [4, 50], 'PARA': [5, 30], 'GEM': [4, 0], 'GEPS': [5, 15],
                    'EC': [6,5],'EPS': [7,15], 'EPS45': [20,0]}


#LOCAL_TZ = pytz.timezone("Asia/Jerusalem")
LOCAL_TZ = pytz.timezone("UTC")

MODELS_RUNS = {'GFS': [0, 6, 12, 18], 'GEFS': [0, 6, 12, 18], 'PARA': [0, 6, 12, 18],
               'GEM': [0, 12], 'GEPS': [0, 12], 'CFS': [0,12],'EC': [0,12],'EPS':[0,12],
                'EPS45':[0]}
MODEL_GAPS = {'GFS': 6, 'GEFS': 6, 'PARA': 6, 'GEM': 12, 'GEPS': 12, 'CFS':12,
              'EPS45': 96}
MODELS_HORIZONS = {'GFS': GFS_4_HORIZONS, 'GEFS': GEFS_HORIZONS, 'PARA': PARA_HORIZONS,
                   'GEM': GEM_HORIZONS, 'GEPS': GEPS_HORIZONS, 'CFS': range(0,721,6),
                   'EC': range(0,217,6),'EPS': range(0,335,6),
                   'EPS45': range(0,1047,12)}

interpolation_methods = [None, 'none', 'nearest', 'bilinear', 'bicubic', 'spline16',
           'spline36', 'hanning', 'hamming', 'hermite', 'kaiser', 'quadric',
           'catrom', 'gaussian', 'bessel', 'mitchell', 'sinc', 'lanczos']

"""
def _last_available_model(x_dt, model):
    latency_hours, latency_minutes = MODELS_LATENCIES[model][0], MODELS_LATENCIES[model][1]
    model_runs = MODELS_RUNS[model]
    x_dt2 = x_dt - td(hours=latency_hours, minutes=latency_minutes)
    if x_dt2.hour in model_runs and x_dt2.minute == 0:
        return x_dt2
    else:
        while x_dt2.hour not in model_runs:
            x_dt2 -= td(hours=1)
        x_dt3 = dtdt(x_dt2.year, x_dt2.month, x_dt2.day, x_dt2.hour)

        return x_dt3
"""


def _last_available_model(x_dt, model,latency_delay_hours=0):
    if model != 'EPS45':
        weekdays = [0,1,2,3,4,5,6]
    else:
        weekdays = [0,3]
    latency_hours, latency_minutes = MODELS_LATENCIES[model][0], MODELS_LATENCIES[model][1]
    latency_hours += latency_delay_hours
    model_runs = MODELS_RUNS[model]
    x_dt2 = x_dt - td(hours=latency_hours, minutes=latency_minutes)
    if (x_dt2.hour in model_runs and x_dt2.minute == 0) and (x_dt2.weekday() in weekdays):
        return x_dt2
    else:
        while (x_dt2.hour not in model_runs) or (x_dt2.weekday() not in weekdays):
            x_dt2 -= td(hours=1)
        x_dt3 = dtdt(x_dt2.year, x_dt2.month, x_dt2.day, x_dt2.hour)
        return x_dt3


live_degdays_dir = "/home/<USER>/mnt/home/<USER>/degdays_csvs/Live/"
models = ['GEM','GFS','GEFS','PARA','GEPS', 'CFS']


def get_file_path(model):
    if model != 'GEPS':
        return live_degdays_dir+'\\live_degdays_%s.csv'% model
    else:
        return live_degdays_dir + '\\live_degdays_%s_Pmean.csv' % model


"""_______________________________________________________________________"""

#HOME = "/home/<USER>/mnt/home/<USER>"
HOME = "/mnt/volume_100G/home/<USER>"
chrom_driver = "%s/chromedriver" % HOME
firefox_driver = "%s/geckodriver" % HOME
user = "<EMAIL>"
password = "ML18hddcdd"
degdays_csv = HOME+"/degdays_csvs/Live/live_degdays_%s.csv"

def bring_EC_tables(model,browser='chrome'):
    if browser == 'chrome':
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless')
        
        PROXY = "203.92.196.44:8080" #"212.237.21.196:3128" #"212.192.196.6:80"
        #chrome_options.add_argument('--proxy-server=%s' % PROXY)
        print ("About to initialize driver: %s"%chrom_driver)
        driver = webdriver.Chrome(executable_path=chrom_driver, chrome_options=chrome_options,
        service_args=['--verbose', '--log-path=/tmp/chromedriver.log'])
        print ("Finished initializing chrome driver")
    elif browser == 'firefox':
        chrome_options = webdriver.FirefoxOptions()
        chrome_options.add_argument('--headless')
        driver = webdriver.Firefox(executable_path=firefox_driver, firefox_options=chrome_options,
        service_args=['--verbose', '--log-path=/tmp/chromedriver.log'])
    #chrome_options.add_argument('--no-sandbox') # required when running as root user. otherwise you would get no sandbox errors. 
    else:
        raise AssertionError("invalid browser: %s"%browser)
    
    
    #driver.minimize_window() 
    eps_url = "https://dashboard.truewx.com/energy/ecmwf-ensemble/"
    ec_url = "https://dashboard.truewx.com/energy/ecmwf/"
    print (eps_url)
    time.sleep(10)
    driver.get(eps_url)
    txt = driver.page_source
    with open("/tmp/Truex_page_source.txt","w") as f:
        f.write(txt)
    input_email = driver.find_element_by_name("email")
    input_email.send_keys(user)
    input_pass = driver.find_element_by_name("password")
    input_pass.send_keys(password)

    submit_button = driver.find_element_by_id("submit-button")
    submit_button.click()

    time.sleep(1)
    button_dict = {"EPS":3, "EC":2,"EPS45":4}
    eps_button = driver.find_elements_by_class_name("page-content-sidebar-nav-a")[button_dict[model]]
    eps_button.click()
    time.sleep(1)
    html = driver.page_source

    soup = BeautifulSoup(html)
    table = soup.findAll("table", {"class":"table"})[0]
    rows = table.findAll("tr")
    final_rows = []
    for row in rows:
        csv_row = []
        for cell in row.findAll(["td", "th"]):
            csv_row.append(cell.get_text())
        final_rows.append(csv_row)

    dt_format = "%Y-%m-%d %Hz" if model != 'EPS45' else "%Y-%m-%d"
    forecast_time = dtdt.strptime(final_rows[0][1],dt_format)
    print("Handling model: %s | Forecast timee: %s  | (running time: %s)"% (model,forecast_time,dtdt.now()))
    dic = {}
    cols = []
    for coln in final_rows[1]:
        if coln not in cols:
            cols.append(coln)
        else:
            new = coln+'_-12h'
            if new in cols:
                new = new.replace('12h','24h')
            cols.append(new)
    df = pd.DataFrame(final_rows[2:], columns=cols)
    df = df.rename(columns={"Date": "validation_day"})
    df = df[df['validation_day'].apply(lambda x: "Days" not in x)]
    df['validation_day'] = df['validation_day'].apply(lambda x: dtdt.strptime(x, "%Y-%m-%d"))
    df['forecast_time'] = forecast_time
    df['weight'] = 'population_US_weight'
    df['feature'] = 'tmp2m'
    df = df[['forecast_time','validation_day','HDD','CDD','HDD 30Y Climo','CDD 30Y Climo','weight','feature']]
    df_hdd = df[[c for c in list(df) if c != 'CDD']].rename(columns={'HDD':'Value'})
    df_cdd = df[[c for c in list(df) if c != 'HDD']].rename(columns={'CDD':'Value'})
    df_hdd['feature_type'] = 'HDD'
    df_cdd['feature_type'] = 'CDD'
    df2 = df_hdd.merge(df_cdd, on=list(df_hdd), how="outer").sort_values(['forecast_time','validation_day'])
    df2 = df2[df2['Value'] != ""]
    path = degdays_csv%model
    if not os.path.exists(path):
        df2.to_csv(path, index=False)
    else:
        dates_cols = ['forecast_time','validation_day']
        numeric = ['HDD 30Y Climo','CDD 30Y Climo','Value']
        original = pd.read_csv(path, parse_dates=['forecast_time','validation_day'])
        for col in numeric:
            try:
                df2[col] = pd.to_numeric(df2[col])
            except:
                raise
        for col in dates_cols:
            try:
                df2[col] = pd.to_datetime(df2[col])
            except:
                pass
        #df2 = df2.convert_objects(convert_numeric=True)
        final = original.merge(df2,on=list(df2), how="outer")
        final.to_csv(path,index=False)
        print("model: %s | Forecast time: %s | Finished Merging with new data | (running time: %s)"% (model,forecast_time,dtdt.now()))
    aa = 1
    driver.close()

def bring_from_ec2(model):
    local_d = "%s/degdays_csvs/Live"% HOME
    dest_d = "/home/<USER>/degdays_csvs/Live"
    cmd = "scp -i  %s/Technical/ranger_xlarge_Oct19_key.pem "%HOME+\
                "ubuntu@ip-172-31-40-27:%s/live_degdays_%s.csv %s/live_degdays_%s.csv"% (dest_d, model, local_d, model)
    print ("Model: %s is needed ... bringing it from parallel server \nCMD: %s "% (model,cmd))
    check_call(cmd, shell=True)


# scp -i  /mnt/backup_volume/home/<USER>/Technical/ranger_xlarge_Oct19_key.pem  ubuntu@ip-172-31-40-27:/home/<USER>/degdays_csvs/Live/live_degdays_EPS.csv /mnt/backup_volume/home/<USER>/degdays_csvs/Live/
# scp -i  /mnt/backup_volume/home/<USER>/Technical/ranger_xlarge_Oct19_key.pemubuntu@ip-172-31-40-27:/home/<USER>/degdays_csvs/Live/live_degdays_EC.csv /mnt/backup_volume/home/<USER>/degdays_csvs/Live/live_degdays_EC.csv
while True:
    try:
        now_naive = dtdt.now()
        local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
        utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)
        
        for model in ['EPS','EC','EPS45']:
            num_of_days = MODELS_HORIZONS[model][-1] / 24
            last_model = _last_available_model(utc_now_dt,model)
            path = degdays_csv%model
            run = False
            if os.path.exists(path):
                current_df = pd.read_csv(path,parse_dates=['forecast_time','validation_day'])
                if current_df[current_df['forecast_time'] == last_model].shape[0] < num_of_days*2:
                    run = True
            else:        
                run = True
            if run:
                print('We are missing Data for model: %s | forecast_time: %s' % (model, last_model))
                try:
                    #bring_EC_tables(model)
                    bring_from_ec2(model)
                except Exception as e:
                    print ("Failed with Error: %s | (running time: %s" % (e, dtdt.now()))
                    time.sleep(30)
            else: 
                print('didnt have to download... skipping')
        if utc_now_dt.hour in [21,9]:
            print('going to sleep for 10 hours.... Now its %s'% dtdt.now())
            time.sleep(3600*9)
        else:
            print('going to sleep for 5 minutes... | Now is %s'%dtdt.now())
            time.sleep(60*2)
    except Exception as e:
        print('Warning.... Failed with error: %s |\nSleeping 10 seconds and retrying'% e)
        time.sleep(10)