import smtplib
from datetime import datetime as dtdt
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

gmail_user = '<EMAIL>'  
gmail_password = 'SendHDD@2019'


def send_email_main(subject, body,sent_from=gmail_user,to=[gmail_user]):
	msg = MIMEMultipart()
	msg['From'] = sent_from
	msg['To'] = ",".join(to)
	msg['Subject'] = subject
	msg.attach(MIMEText(body,'plain'))
	try:  
		server = smtplib.SMTP_SSL('smtp.gmail.com', 465)
		server.ehlo() 
		server.login(gmail_user, gmail_password)
		server.sendmail(sent_from, to, msg.as_string())
		### old method
		#server.sendmail(sent_from, to, email_text)
		server.close()

		print ('Email sent!')
	except:  
		print ('Something went wrong...')
		raise

def send_update(model, model_time,ft, diffs_dic,normalization_method=0):
	normalization_str = "" if not normalization_method else "| Normalized 3day"
	subject = '%s Update | Model: %s | %s %s' % (ft, model, model_time.strftime("%d/%m/%Y %Hz"),normalization_str)  
	body_parts = ['Model: %s'%model, 'Last_model: %s'%model_time.strftime("%d/%m/%Y %Hz")]+ \
			['====================>>>>>>>>>>']+ \
			['%s%s \n -------------\n' % (k, diffs_dic[k]) for k in diffs_dic.keys()]
	body = "\n".join(body_parts)
	body = body.replace(": [",": \n [")
		
	# new method
	send_email_main(subject, body)

#send_update('GFS', dtdt.now(), {"Last Vs -6h": [12,1333,1222]})