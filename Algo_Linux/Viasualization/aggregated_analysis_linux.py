#import matplotlib
#from matplotlib import pyplot as plt
#matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
import time
from send_to_email import send_update, send_email_main

from visualize_live_degdays_24h_new import *
from visualize_live_degdays_24h_new import _last_available_model, get_file_path
from utilities import normalize_by_accuracy, get_multiplier, normalize_values

HOME = "/mnt/volume_100G/home/<USER>/"

LOCAL_TZ = pytz.timezone("UTC")
CET_TZ = pytz.timezone("CET")

# weekly analysis
SHIFT_BY_MARKET_HOURS = False #True
CFS_WEEKLY = False

# normalization options
UNITE_VALS = True   # make Values start from same point
NORMALIZE_BY_ACCURACY = 3 # 3
ROLLING_MEAN = False #True

if not ROLLING_MEAN:
	ACCURACY_CSV = "%s/TWS/%s/Viasualization/Models_accuracy_HDD_%sday_group_Daily_01.11.2019-15.12.2019.csv"% (HOME, VERSION, NORMALIZE_BY_ACCURACY)
else:
	ACCURACY_CSV = "%s/TWS/%s/Viasualization/Models_accuracy_HDD_%sday_rollingMean_10.11.2019-10.01.2020.csv"% (HOME, VERSION, NORMALIZE_BY_ACCURACY)


# Nans
FILL_NANS_METHOD = 2
NUM_OF_FILLNA_SAMPLES = 10 # last x days avg

PLOT = False
# main options
ALL_MODELS = ['PARA','GFS','GEFS','PARACO','GEFSL','GFSCO','GEM','GEPS','GEMCO','EC','EPS','CFS'] #,'CFS']
MODELS_FOR_AVG = ['PARA','GEFS','GEFSL','EC','EPS','GEM','GEPS'] 
#MODELS = ['PARA','GEFS','EC','EPS'] 
WEIGHT = 'population_US_weight' #'NG16_midwest_weight' #
FEATURE = 'CDD'
VAL_OR_DIFF = 'Diff' # #'Value'
ONLY_LAST_IN_WEEKLY = True
SEND_EMAIL = True
takeback_hours = 1 #12
LATENCY_DELAY_HOURS = 0 # considers time it takes me to move the models to analyis
LATENCY_DELAY_MINUTES = 0
DAYS_AHEAD_DEFAULT = 30
# reference times
now_naive = dtdt.now()
local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

EXPECTED_STDS_FILE = "%s/TWS/Algo4.8_Controls2/Viasualization/stds_between_models_by_daysAhead_HDD_Oct19_NotNormed.csv"%HOME 

LAST_SENT_EMAIL_BY_MODEL = {model:{ft: dtdt(2019,9,22) for ft in ['HDD','CDD']} for model in ALL_MODELS}

reference_stds_df = pd.read_csv(EXPECTED_STDS_FILE).reset_index().rename(columns={'index':'days_ahead'})

MODELS_WITHOUTH_NORMALIZATION = ['GEMCO'] if not ROLLING_MEAN else []

assert os.path.isfile(ACCURACY_CSV) or NORMALIZE_BY_ACCURACY == 0
assert FILL_NANS_METHOD in [0,1,2]

try:
	hours_back = int(sys.argv[1])
except:
	hours_back = 18
	print ('Warning... taking hours back to be 18')
try:
	days_ahead = int(sys.argv[2])
except:
	days_ahead = 0
try:
	cfs_or_normal = sys.argv[3]
except:
	cfs_or_normal = 'normal'
try:
	NORMALIZE_BY_ACCURACY = int(sys.argv[4])
except:
	pass

"""_____________________________________________________________________________________"""

def multimodel_cols_cond(x, last_model=None, val_or_diff='Value'):
	if val_or_diff == 'Diff':
		cond = val_or_diff in x and 'cumsum' not in x and '-24h' in x
	else:
		assert last_model
		cond = val_or_diff in x and 'cumsum' not in x and 'Prev' not in x and str(last_model.hour) in x
	return cond

"""____________ NANs___________"""


def fill_nans(final,method=2):
	for c in list(final):
		if final[c].isna().mean() > 0:
			if method == 1:
				final[c] = final[c].fillna(method='ffill')
			elif method == 2:
				try:
					nans_number = final[c].isna().sum()
					val_for_fill = final[c][-NUM_OF_FILLNA_SAMPLES-nans_number:].mean()
					#print ("INFO | Column: %s | #Days: %s | #Nans = %s| Value for fill = %s" % (c,NUM_OF_FILLNA_SAMPLES, nans_number,val_for_fill))
					final[c] = final[c].fillna(val_for_fill)
				except TypeError:
					#print ('Warning FillNans on Column: %s Failed' %c)
					final[c] = final[c].fillna(method='ffill')
	final = final.dropna()
	return final


"""____________Weekly Analysis_____________"""


def weekly_analysis(df, model, ft, weight, last_model, show_diff=True, norm_acc=True, only_last=False,
	send_email=False,days_ahead=DAYS_AHEAD_DEFAULT):
	subject_for_email = "Weekly Analysis for %s | Model: %s  (%s) | Last Day: %s | FillNans Days: %s" % (ft, model, last_model.strftime("%d/%m/%Y %H:%M:%S"),
								(last_model+td(days=days_ahead)).date().strftime("%d/%m/%Y"), NUM_OF_FILLNA_SAMPLES)
	body_for_email_lst = []
	
	df['forecast_time'] += td(hours=6)
	if SHIFT_BY_MARKET_HOURS:
		if model in ['EPS','CFS']:
			df['forecast_time'] += td(hours=6)
		# df['forecast_time'][df['forecast_time']!= last_model] += 
	df['fcst_date'] = df['forecast_time'].dt.date
	body_for_email_lst.append("SHIFT_BY_MARKET_HOURS = %s"% SHIFT_BY_MARKET_HOURS)
	
	new = df.groupby(['fcst_date', 'validation_day']).mean().reset_index()[['fcst_date', 'validation_day', 'Value']]
	
	final = pd.DataFrame()	
	for name, group in new.groupby('fcst_date'):
		if final.shape[0] == 0:
			final = group
			name0 = name
		else:
			final = final.merge(group[['validation_day', 'Value']], on=["validation_day"],
								suffixes=('', '_%s' % name), how="outer") 
	final = final.rename(columns={'Value': 'Value_%s' % name0})
	final = final.sort_values('validation_day')
	final = final[(final['validation_day'] >= last_model.date())&(final['validation_day'] < (last_model+td(days=days_ahead)).date())]
	
	if FILL_NANS_METHOD:
		final = fill_nans(final,FILL_NANS_METHOD)
	body_for_email_lst.append("FillNans method = %s" % FILL_NANS_METHOD)
	
	names = [c for c in sorted(list(final)) if c not in ['validation_day','fcst_date']]
	if show_diff:
		new_names = []
		last_val = final[names[-1]]
		for other in names[:-1]:
			final[other.replace('Value','Diff_last-->')] = last_val - final[other]
			new_names.append(other.replace('Value','Diff_last-->'))
		names = new_names
	if norm_acc and VAL_OR_DIFF == 'Diff':
		final = normalize_by_accuracy(final, ACCURACY_CSV, model,rolling_mean=ROLLING_MEAN)
	body_for_email_lst.append("Accuracy Normalization Method: %s"%norm_acc)
	
	if not only_last:
		daily_vectors = [final[c].cumsum().apply(lambda x: round(x,2)).tolist() for c in names]
	else:
		daily_vectors = [final[c].cumsum().apply(lambda x: round(x,2)).tolist()[-1] for c in names]
	body_for_email_lst += ["",""]
	
	real_sums = [round(final[c.replace("Diff_last-->","Value")].sum(),2) for c in names]
	
	txt1 = "\n".join(["%s  | %s | (Total: %s)"%(str(name), str(vec),total) for name,vec,total in zip(names,daily_vectors,real_sums)])
	print('--------------  Model: %s -----------------\n Weekly Anaylsis for Feature: %s | Weight: %s | FillNans Samples: %s:\n%s'%(model,ft,weight,NUM_OF_FILLNA_SAMPLES, txt1))
	body_for_email_lst.append(txt1)
	
	if send_email:
		email_body = "\n".join(body_for_email_lst)
		try:
			send_email_main(subject_for_email, email_body)
		except Exception as e:
			print("Could not send email...\nError: %s"% e)
	
	time.sleep(5)
	final['model'] = model
	return final


if __name__ == "__main__":
	while True:
		for ft_type in ['HDD']: #x, 'CDD']:
			multi_models_df = pd.DataFrame()
			multi_models_weekly_df = pd.DataFrame()
			models = ALL_MODELS
			ft = FEATURE
			for weight in [WEIGHT]: #,'NG16_midwest_weight']:
				last_models = []
				for model in models: 
					try:
						mini_cols = ['forecast_time', 'validation_day','Value']
						df = pd.read_csv(get_file_path(model), parse_dates=['forecast_time','validation_day'], error_bad_lines=False)
						last_model = _last_available_model(utc_now_dt-td(hours=takeback_hours),model,LATENCY_DELAY_HOURS,LATENCY_DELAY_MINUTES)
						
						#last_model_avail_time = _model_availability_time(last_model,model)
						now = dtdt.now()
						if LAST_SENT_EMAIL_BY_MODEL[model][ft_type] < last_model:
							send_email = SEND_EMAIL
							LAST_SENT_EMAIL_BY_MODEL[model][ft_type] = last_model
						else:
							send_email = False
						
						last_models.append((model, last_model))
						previous_24h_model = _last_available_model(utc_now_dt-td(hours=hours_back),model,LATENCY_DELAY_HOURS,LATENCY_DELAY_MINUTES)
						previous_24h_hour_str = 'Prev(-24h)_%s'%previous_24h_model.hour
						print ('******   Model: %s | last_model = %s | Prev: %s ******'%(model, last_model,previous_24h_model))
						df = df[(df['weight'] == weight)&(df['feature_type'] == ft_type)]
						df = df[(df['forecast_time'] >= previous_24h_model)&(df['forecast_time'] <= last_model)]

						df_last = df[df['forecast_time'] == last_model][mini_cols]
						df_previous24 = df[df['forecast_time'] == previous_24h_model][mini_cols]
						df_new = df_last.merge(df_previous24, on=['validation_day'],suffixes=('','_%sZ'% previous_24h_hour_str),
												  how='outer')
						if cfs_or_normal == 'CFS':
							try:
								norm_acc = NORMALIZE_BY_ACCURACY > 0 and model not in MODELS_WITHOUTH_NORMALIZATION
								final = weekly_analysis(df, model, ft_type, weight, last_model, norm_acc=norm_acc,
												only_last=ONLY_LAST_IN_WEEKLY,send_email=send_email,
												days_ahead=days_ahead)
								if model in MODELS_FOR_AVG:
									if multi_models_weekly_df.shape[0] == 0:
										multi_models_weekly_df = final
									else:
										final = final[[c for c in list(final) if c in list(multi_models_weekly_df)]]
										multi_models_weekly_df = multi_models_weekly_df.merge(final,how="outer",on=list(final))
								continue
							except Exception as e:
								print ('Failed with Error: %s ... skipping' % e)
								raise
						if FILL_NANS_METHOD:
							df_new = df_new.sort_values('validation_day')
							df_new = fill_nans(df_new,FILL_NANS_METHOD)

						df_new = df_new.rename(columns={'Value': 'Value_%sZ'% last_model.hour if 'Value_%sZ'% last_model.hour not in list(df_new) else 'Value_%sZ(2)'% last_model.hour})
						final_cols = ['validation_day'] + [l for l in list(df_new) if 'Value' in l]
						final_cols_only_last = ['validation_day'] + [l for l in list(df_new) if 'Value_%s'%last_model.hour in l]
						df_new = df_new[final_cols]

						try:
							if PLOT:
								df_new.plot(x='validation_day',kind='line', title='Model: %s'%model)
								plt.tight_layout()
						except:
							aa= 1
						if VAL_OR_DIFF != 'Value':
							df_new['Diff_%sZ-%sZ' % (last_model.hour, previous_24h_hour_str)] = df_new['Value_%sZ' % last_model.hour] - df_new['Value_%sZ' % previous_24h_hour_str]
							df_new['Diff_%sZ-%sZ_cumsum' %(last_model.hour, previous_24h_hour_str)] = df_new['Diff_%sZ-%sZ' % (last_model.hour, previous_24h_hour_str)].cumsum()

						# handle multi model charts
						cols_for_multimodel = ['validation_day'] + [l for l in list(df_new) if multimodel_cols_cond(l, last_model, VAL_OR_DIFF)]
						df_to_merge = df_new[cols_for_multimodel]
						df_to_merge = df_to_merge.rename(columns={c : '%s_%s' % (model,c) for c in list(df_to_merge) if 'Value' in c or 'Diff' in c})
						if weight in ['basic_NG_weight','population_US_weight']:
							if multi_models_df.shape[0] == 0:
								multi_models_df = df_to_merge
							else:
								multi_models_df = multi_models_df.merge(df_to_merge,on=[l for l in list(df_to_merge) if 'Value' not in l and 'Diff' not in l],
																		how='outer')
						if VAL_OR_DIFF != 'Value':
							# plot normal by model
							if PLOT:
								df_new[['validation_day']+[l for l in list(df_new) if 'cumsum' in l]].plot(x='validation_day',title='Model: %s'%model)
					except:
						aa = 1
						print('Model: %s Failed... skipping'%model)
						continue
				if PLOT:
					plt.show()

			if multi_models_df.shape[0] > 0:
				multi_models_df = multi_models_df.drop_duplicates(subset=[c for c in list(multi_models_df) if 'GFS' in c or 'PARA' in c or 'CFS' in c])
				if UNITE_VALS and VAL_OR_DIFF == 'Value':
					multi_models_df = normalize_values(multi_models_df)

				multi_models_df = multi_models_df.sort_values('validation_day')

				if NORMALIZE_BY_ACCURACY and VAL_OR_DIFF == 'Diff':
					multi_models_df = normalize_by_accuracy(multi_models_df,ACCURACY_CSV,rolling_mean=ROLLING_MEAN)

				if PLOT:
					multi_models_df.plot(x='validation_day')
				multi_models_df2 = multi_models_df.copy()

				multi_models_df2[[l for l in list(multi_models_df2) if l != 'validation_day']] = multi_models_df2[[l for l in list(multi_models_df2) if l != 'validation_day']].cumsum()
				if PLOT:
					multi_models_df2.plot(x='validation_day', title="Cummulative Graph")
					plt.show()
				
				days_gap = (multi_models_df['validation_day'] - last_model).apply(lambda x: x.total_seconds() / 3600.0 / 24)
				if days_ahead > 0:
					multi_models_df = multi_models_df[days_gap < days_ahead]
				
				multi_models_df_mean = multi_models_df.set_index('validation_day').mean(axis=1)
				multi_models_df_cumsum = multi_models_df_mean.cumsum()
				multi_models_df.to_csv("/home/<USER>/mnt/home/<USER>/degdays_csvs/tmp_%s.csv"%ft_type)
				last_model = sorted(last_models, key=lambda x: x[1])[-1]
				last_model_str = "\n".join([str((x[0], str(x[1]))) for x in last_models])
				
				txt = "%s:\n"% ("Absolute Values of last Model (%s)"% last_model if VAL_OR_DIFF == "Value" else "Diff %s - %s Hours" % (last_model, hours_back))
				txt += "last models times: \n %s \n" % last_model_str
				
				txt+= "Reaching date: %s\n----------------------------\n" % multi_models_df_mean.index.tolist()[-1]
				txt += "Daily Values:\n %s\n" % multi_models_df_mean.apply(lambda x: round(x,2)).tolist()
				txt += "Daily Cumsum:\n %s\n" % multi_models_df_cumsum.apply(lambda x: round(x, 2)).tolist()
				print('--------------  Model: MultiModels %s -----------------------  Feature: %s | Weight: %s -----------------\n%s'%(models,ft_type,weight,txt))
				if PLOT:
					multi_models_df_mean.plot(title='Average of all models',kind='bar')
					plt.tight_layout()
					plt.show()
					multi_models_df_cumsum.plot()
					plt.tight_layout()
					plt.show()
			if multi_models_weekly_df.shape[0] > 0:
				mn = multi_models_weekly_df.groupby('validation_day').mean()
				#print(list(multi_models_weekly_df))
				
				last_day_colname = 'Value_%s'% last_model.strftime("%Y-%m-%d")
				days_ahead_series = (multi_models_weekly_df['validation_day'].dt.date - last_model.date()).dt.days
				multi_models_weekly_df['days_ahead'] = days_ahead_series
								
				current_std_vec = multi_models_weekly_df[(1 <= days_ahead_series) & (days_ahead_series <= 15)].groupby('days_ahead').std()[last_day_colname].reset_index()
				reference_stds_df2 = reference_stds_df.merge(current_std_vec,on=['days_ahead'])
				current_std_vec2 = (reference_stds_df2[last_day_colname] - reference_stds_df2['mean']) / (reference_stds_df2['upper'] - reference_stds_df2['lower'])
				current_std_vec2 = current_std_vec2.apply(lambda x: round(x,2))
				print('current_std_vec: %s' % current_std_vec2.tolist())#current_std_vec = current_std_vec.apply(lambda x: round(x,2))[last_day_colname]
				print ('current_std_vec: %s' % current_std_vec2.tolist())
				#consistency_score = (reference_stds_df['mean'] - std_vec)
				if VAL_OR_DIFF == 'Diff':
					mn = mn[[c for c in list(mn) if 'Diff' in c]].cumsum()
				else:
					mn = mn[[c for c in list(mn) if 'Value' in c]]
				mn.to_csv("/tmp/Ran_%s.csv"%ft_type,index=False)
				if PLOT:
					mn.plot()
					plt.show()
				else:
					names = [c for c in list(mn) if '2019' in c or '2020' in c]
					if not ONLY_LAST_IN_WEEKLY:
						daily_vectors = [mn[c].apply(lambda x: round(x, 2)).tolist() for c in names]
						#std_vals = std_vec
					else:
						daily_vectors = [mn[c].apply(lambda x: round(x, 2)).tolist()[-1] for c in names]
					print('\n --------------Multimodel WEEKLY %s | Weight: %s -----------------\n%s'%(VAL_OR_DIFF, weight,
						"\n".join(["%s  | %s " % (str(name), str(vec)) for name, vec in zip(names, daily_vectors)])))
		print ('\n=======================================================================================\n')
		time.sleep(5)