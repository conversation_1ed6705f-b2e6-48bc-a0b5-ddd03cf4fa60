"""
built from 3.17
X Large !!! XL XL XL XL XL XL XL XL XL XL XL XL XL XL XL XL XL XLXL XL XL XL XL XLXL XL XL XL XL XLXL XL XL XL XL XLXL XL XL XL XL XL 
4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 4.18 
"""

# from pymongo import MongoClient
from datetime import datetime as dtdt
from datetime import timedelta as td
from numpy import array as npa
import pandas as pd
import urllib, bs4
import os
#from matplotlib import pyplot as plt
#import pymongo

import sys
import calendar
import numpy as np
import requests

#from urllib3 import urlopen
import json
import csv
from pytz import timezone as tz
import pytz
from subprocess import check_call

script_dir = os.path.dirname(__file__)
algo_dir = os.path.abspath(os.path.join(script_dir, os.pardir))
json_config_path = os.path.join(os.path.join(algo_dir, 'Auxiliary_Files'), 'config.json')
data_processing_path = os.path.join(algo_dir, 'Data_Processing')
data_retrieval_path = os.path.join(algo_dir, 'Data_Retrieval')

sys.path.append(data_processing_path)
sys.path.append(data_retrieval_path)


try:
	from Data_Retrieval.grib2mg import generate_mg_file
	from Data_Processing.handle_minigrib import main as main_degdays_handle
except:
	from grib2mg import generate_mg_file
	from handle_minigrib import main as main_degdays_handle

MAX_Z = 384
midi_above_240_zs = [12*i for i in range(20,33)]
midi_below_240 = [3*i for i in range(80)]
ALL_HORIZONS =  midi_below_240 +  midi_above_240_zs
GFS_4_HORIZONS = [z for z in ALL_HORIZONS if z <= MAX_Z]
GEFS_HORIZONS = range(0,390,6)
PARA_HORIZONS = range(0,385,3)
GEM_HORIZONS = range(0,241,3)
GEPS_HORIZONS = list(range(0,193,3))+list(range(198,385,6))

MODELS_LATENCIES = {'GFS': 4, 'GEFS':6,'PARA':6, 'GEM': 4, 'GEPS': 5}
MODELS_RUNS = {'GFS': [0, 6, 12, 18], 'GEFS':[0, 6, 12, 18], 'PARA': [0, 6, 12, 18],
				'GEM': [0,12], 'GEPS':[0,12]}
MODEL_GAPS = {'GFS': 6, 'GEFS':6, 'PARA':6, 'GEM':12, 'GEPS': 12}
MODELS_HORIZONS = {'GFS': GFS_4_HORIZONS, 'GEFS': GEFS_HORIZONS, 'PARA': PARA_HORIZONS,
					'GEM': GEM_HORIZONS, 'GEPS': GEPS_HORIZONS}

CANADIAN_MODES = {'GEM': {'1': 'TGL_2', '2': 'ISBL_850'},
				  'GEPS': {'1': 'TGL_2m', '2': 'ISBL_0850'},
				  'local': {'1': 'TMP2m', '2':'TMP850'}
				  }
					
GERMANY_LAT_LON = (47, 55, 5, 15)

GFS_BASE_URL = "http://nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/prod/gfs."
GFS_DATE_SUFFIX_EXAMPLE = "/gfs.2018090700/"

GEFS_BASE_URL = 'http://nomads.ncep.noaa.gov/pub/data/nccf/com/gens/prod/gefs.'
PARA_BASE_URL = 'http://para.nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/para/gfs.'

##### GEM 

GEM_EXAMPLE = "http://dd.weather.gc.ca/model_gem_global/25km/grib2/lat_lon/12/189/CMC_glb_TMP_ISBL_850_latlon.24x.24_2018121212_P189.grib2"
GEM_EXAMPLE_ground = "http://dd.weather.gc.ca/model_gem_global/25km/grib2/lat_lon/12/189/CMC_glb_TMP_TGL_2_latlon.24x.24_2018121212_P189.grib2"
					  
GEM_BASE_URL = "http://dd.weather.gc.ca/model_gem_global/25km/grib2/lat_lon/"


##### GEM ENS (GEPS)
# _ISBL_0850_
# _TGL_2m_
GEPS_EXAMPLE = "http://dd.weather.gc.ca/ensemble/geps/grib2/raw/12/015/CMC_geps-raw_TMP_TGL_2m_latlon0p5x0p5_2018121212_P015_allmbrs.grib2"
GEPS_BASE_URL = "http://dd.weather.gc.ca/ensemble/geps/grib2/raw/"

MODEL_RUN = [6 * i for i in range(4)]
MAXIMAL_HORIZON = 384
KEEP_CANADIAN_GRIBS = False

ENSEMBLE_MODELS_TO_SEPARATE = ['GEPS']
ENSEMBLE_MODELS_RANGES = {'GEPS': range(0,21)}
MINIMAL_ENSEMBLE_MEMBERS_REQUIRED = 0.999 # 99.999%

HOME = '/home/<USER>/home/<USER>' # /home/<USER>
ECCODES_PATH = '/home/<USER>/eccodes/bin/'

url_example = "http://nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/prod/gfs.2018090700/gfs.t00z.pgrb2.0p25.f384"
PARA_url_example = 'http://para.nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/para/gfs.20181120/12/gfs.t12z.pgrb2.0p50.f003'
# urllib.urlretrieve(url,"C:\Users\<USER>\Downloads\Ran.grib2.bz2")


def load_ensemble_csvs_to_df_by_model(csvs,model_dt):
	tmp_df = pd.read_csv(csvs[0],parse_dates=['forecast_time'])
	lst = list(tmp_df)
	df = pd.DataFrame({k:[] for k in lst})
	df = df[lst]
	
	for i,csv in sorted(enumerate(csvs)):
		tmp_df = pd.read_csv(csv,parse_dates=['forecast_time']).dropna()
		#tmp_df['member'] = i
		
		required_rows = tmp_df[tmp_df['forecast_time'] == model_dt]
		assert required_rows.shape[0] == 1, 'for csv: %s didnt have no nans rows' % csv
		required_row = required_rows.iloc[0]
		df = df.merge(required_rows, on=lst, how='outer')
	print ('After loading We have df shape: %s' % str(df.shape))
	return df
		

def take_vectors_mean_from_str(df, cols):
	non_used_cols_df_row1 = df[[c for c in list(df) if c not in cols]][:1]
	new_df = pd.DataFrame()
	for col in cols:
		try:
			tmp_df = df[[col]]
			tmp_df[col+'_avg3'] = tmp_df[col].apply(lambda x: np.array(eval(x.split('+')[0])))
			tmp_df[col + '_avg1'] = tmp_df[col].apply(lambda x: np.array(eval(x.split('+')[1])))
			aa = 1
			mean3 = np.around(tmp_df[col+'_avg3'].mean(),2).tolist()
			mean1 = np.around(tmp_df[col+'_avg1'].mean(),2).tolist()
			mean_str = str(mean3) + ' +'+ str(mean1)
			new_df[col] = [mean_str]
		except:
			print ('Failed on col: %s   | First row of it was %s' % (col, tmp_df.iloc[0][col]))
			print ('df.head was: %s' % tmp_df.head())
			raise
	for non_used_col in list(non_used_cols_df_row1):
		new_df[non_used_col] = non_used_cols_df_row1[non_used_col]
	new_df = new_df[list(df)]
	return new_df


"""__________ Preparation ________________"""

def get_ingrib_str(model_hour, horizon, model='GFS', model_day=None, model_mode='1'):
	"""
	canadian modes are relevant to decide whether to bring tmp2m / tmp850 in GEM & GEPS
	model_day is relevant only for the canadians
	"""
	
	if model == 'GFS':
		return "/gfs.t%sz.pgrb2.0p50.f%s"%(str(model_hour).zfill(2), str(horizon).zfill(3))
	elif model == 'GEFS':
		return "/pgrb2a/geavg.t%sz.pgrb2af%s"%(str(model_hour).zfill(2), str(horizon).zfill(2))
	elif model == 'PARA':
		if model_hour != 18:
			return '/gfs.t%sz.pgrb2.0p50.f%s'%(str(model_hour).zfill(2), str(horizon).zfill(3))
		else:
			return '/gfs.t%sz.pgrb2.0p25.f%s'%(str(model_hour).zfill(2), str(horizon).zfill(3))
	elif model == 'GEM':
		assert model_day, 'With GEM model we must have the model day for the ingrib_str'
		return 'CMC_glb_TMP_%s_latlon.24x.24_%s%s_P%s.grib2'%(CANADIAN_MODES[model][model_mode],model_day.strftime('%Y%m%d'),str(model_hour).zfill(2),str(horizon).zfill(3))
	elif model == 'GEPS':
		assert model_day, 'With GEPS model we must have the model day for the ingrib_str'
		return 'CMC_geps-raw_TMP_%s_latlon0p5x0p5_%s%s_P%s_allmbrs.grib2'%(CANADIAN_MODES[model][model_mode],model_day.strftime('%Y%m%d'),str(model_hour).zfill(2),str(horizon).zfill(3))


def get_grib_outpath_tuple(model_day, model_hour, horizon, model='GFS',model_mode='1',ensemble_member=None):
	time_str = '%s%s'%(model_day.strftime('%Y%m%d'), str(model_hour).zfill(2))
	d = '%s/%s/%s/%s' % (HOME,model,str(model_day.year)+str(model_day.month).zfill(2),time_str)
	if model == 'GFS':
		fn = 'gfs_0p50_%s_%s_%s.grb2' % (model_day.strftime('%Y%m%d'),str(model_hour).zfill(2)+'00', str(horizon).zfill(3))
	elif model == 'GEFS':
		fn = 'gefs_avg_%s_%s_%s.grb2' % (model_day.strftime('%Y%m%d'),str(model_hour).zfill(2)+'00', str(horizon).zfill(3))
	elif model == 'PARA':
		fn = 'gfs_para_0p50_%s_%s_%s.grb2' % (model_day.strftime('%Y%m%d'),str(model_hour).zfill(2)+'00', str(horizon).zfill(3))
	elif model == 'GEM': 
		fn = 'gem_0p24_%s_%s_%s_%s.grb2' % (CANADIAN_MODES['local'][model_mode],model_day.strftime('%Y%m%d'),str(model_hour).zfill(2),str(horizon).zfill(3))
	elif model == 'GEPS': 
		fn = 'geps_0p50_%s_%s_%s_%s.grb2' % (CANADIAN_MODES['local'][model_mode],model_day.strftime('%Y%m%d'),str(model_hour).zfill(2),str(horizon).zfill(3))
	
	if model in ENSEMBLE_MODELS_TO_SEPARATE:
		#print ('INFO INFO INFO MODEL REQUIRED !!!!')
		if ensemble_member in ENSEMBLE_MODELS_RANGES[model]:
			# adjust the dir
			d = d +'/%s'% ensemble_member
			#print ('INFO INFO INFO ensemble member required suffix !!!!   | %s'%d)
	return d, fn


def _last_available_model(x_dt, model):
	latency = MODELS_LATENCIES[model]
	model_runs = MODELS_RUNS[model]
	x_dt2 = x_dt - td(hours=latency)
	while x_dt2.hour not in model_runs:
		x_dt2 -= td(hours=1)
	x_dt3 = dtdt(x_dt2.year, x_dt2.month, x_dt2.day, x_dt2.hour)
	
	return x_dt3


def _model_availability_time(model_runtime, model):
	latency = MODELS_LATENCIES[model]
	availability_time = model_runtime + td(seconds=3600 * latency)
	return availability_time

	
"""__________ grib actions ________________"""


def get_full_url(horizon, model_day, model_hour,model='GFS', model_mode=1):
	"""
	canadian modes are relevant to decide whether to bring tmp2m / tmp850 in GEM & GEPS
	"""
	ingrib_str = get_ingrib_str(model_hour, horizon, model, model_day, model_mode)
	#print (' ***********************************')
	#print('Recieved the following ingrib_str:\n %s'% ingrib_str)
	if model == 'GFS':
		return GFS_BASE_URL + model_day.strftime('%Y%m%d') + str(model_hour).zfill(2) + ingrib_str
	elif model == 'GEFS':
		return GEFS_BASE_URL + model_day.strftime('%Y%m%d') + '/' + str(model_hour).zfill(2) + ingrib_str
	elif model == 'PARA':
		#print('Returning the following PARA URL: %s' % (PARA_BASE_URL + model_day.strftime('%Y%m%d') + '/' + str(model_hour).zfill(2) + ingrib_str))
		#time.sleep(10)
		return PARA_BASE_URL + model_day.strftime('%Y%m%d') + '/' + str(model_hour).zfill(2) + ingrib_str
	elif model == 'GEM':
		return GEM_BASE_URL + str(model_hour).zfill(2) + '/' + str(horizon).zfill(3) +'/' + ingrib_str
	elif model == 'GEPS':
		return GEPS_BASE_URL + str(model_hour).zfill(2) + '/' + str(horizon).zfill(3) +'/' + ingrib_str

def get_grib(url, outpath):
	print('Trying to retrieve the following URL: %s' % url)
	cmd = 'wget %s -O %s' % (url, outpath)
	print(cmd)
	check_call(cmd, shell=True)

def truncate_grib2(grib_path, mini_grib_path=None, delete_original=True):
	if not mini_grib_path:
		grib_name = grib_path.split('/')[-1]
		grib_dir = '/'.join(grib_path.split('/')[:-1])
		mini_grib_path = grib_path.split('.grb2')[0] +'.US.minigrib'
	generate_mg_file(grib_path,mini_grib_path, 'NG_USA_GFS_RAN')

	if delete_original:
		print('deleting grib file : %s' % grib_path)
		check_call('rm -rf %s'% grib_path, shell=True)
	return mini_grib_path

def seperate_to_members_dirs(minigrib_path,members_range=range(0,21)):
	d = '/'.join(minigrib_path.split('/')[:-1])
	minigrib_name = minigrib_path.split(d)[1]
	new_name_for_cmd = minigrib_name.split('.US')[0]+'_P[perturbationNumber].US.minigrib'
	for member_num in members_range:
		#suffix = 'C00' if member_num == 0 else 'P%s' % str(member_num).zfill(2)
		suffix = str(member_num)
		if not os.path.exists(d+'/'+suffix):
			os.makedirs(d+'/'+suffix)
	print('Separating members of the Ensemble into %s directories'% str(member_num+1))
	cmd = '%s/grib_copy %s %s/[perturbationNumber]/%s' % (ECCODES_PATH, minigrib_path, d, new_name_for_cmd)
	check_call(cmd, shell=True)
	
		
"""_____________________  MAIN FUNCTIONS  ______________________"""


def check_for_new_files(model='GFS', models_back=1, model_mode='1'):
	
	models_left = models_back
	last_avail_model_dt = _last_available_model(dtdt.now(), model) - td(hours=models_back*MODEL_GAPS[model])
	last_avail_model_hour = last_avail_model_dt.hour
	
	while models_left >= 0:
		print ('check_for_new_files  \\ Checking model: %s ' % last_avail_model_dt)
		
		#desired_grib_tuples = [get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, z) for z in GFS_4_HORIZONS]
		#desired_grib_fullpaths = [d+'/'+fn for d,fn in desired_grib_tuples]

		#existing = [grib for grib in desired_grib_fullpaths if not os.path.exists(grib.split('.grb2')[0] +'.US.minigrib')] # missing are minigribs
		#stats = [os.stat(grib.split('.grb2')[0] +'.US.minigrib') for grib in existing]  # zeros are normal gribs
		#sizes = [s.st_size for s in stats]
		#zeros = [grib for i, grib in  enumerate(existing) if sizes[i] == 0]  # zeros are normal gribs
		#non_existing = [grib for grib in desired_grib_fullpaths if not os.path.exists(grib.split('.grb2')[0] +'.US.minigrib')] # missing are minigribs
		#missing_grib_fullpaths = zeros + non_existing 
		if model in MODELS_HORIZONS.keys():
			horizons = MODELS_HORIZONS[model]
		else:
			raise AssertionError('required model is not in MODELS_HORIZONS.keys()')
		
		print ('model in check_for_new_files was: %s' % model)
		#time.sleep(10)
		print('about to run on all horizons for model: %s'%last_avail_model_dt.strftime('%d/%m/%Y %H:%M:%S'))
		for z in horizons:
			grib_dir, grib_name = get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, z, model, model_mode)
			if not os.path.exists(grib_dir):
				os.makedirs(grib_dir)

			grib_local_path = grib_dir+'/'+grib_name

			url = get_full_url(z, last_avail_model_dt, last_avail_model_hour, model, model_mode)
			if z % 6 == 0:
				print('reached Z = %s ... \\ trying to brine grib URL: %s ----->>> %s' % (z,url, grib_local_path))		
			if os.path.exists(grib_local_path.split('.grb2')[0]+'.US.minigrib'):
				if os.stat(grib_local_path.split('.grb2')[0] +'.US.minigrib').st_size > 0:
					continue

			# we need to download
			try:
				get_grib(url, grib_local_path)
			except Exception as e:
				print('got Exception: %s'%e)
				print('failed on z = %s .... Skipping '%z)
				continue
			
			print('about to truncate .... ')
			if model in ['GEM', 'GEPS'] and KEEP_CANADIAN_GRIBS:
				minigrib_path = truncate_grib2(grib_local_path, delete_original=False)
			else:
				minigrib_path = truncate_grib2(grib_local_path)
			# seperate the members !!! 
			if model in ENSEMBLE_MODELS_TO_SEPARATE:
				seperate_to_members_dirs(minigrib_path)
		models_left -= 1
		
		last_avail_model_dt += td(hours=MODEL_GAPS[model])
		last_avail_model_hour = (last_avail_model_hour + MODEL_GAPS[model])% 24
		
		
def update_degdays_table(model='GFS', outpath='/home/<USER>/live_degdays.csv',weights=['basic_NG_weight', 'PJMFull_weight','NG16_east_weight'],
			num_of_groups=1, models_back=1, model_mode='0',ensemble=None):
	
	print ('_last_available_model(dtdt.now(), model): %s' % _last_available_model(dtdt.now(), model))
	
	last_avail_model_dt = _last_available_model(dtdt.now(), model) - td(hours=MODEL_GAPS[model]*models_back)
	last_avail_model_hour = last_avail_model_dt.hour
	
	models_left = models_back
	print ('Warning: Now models back is pointing only at 1 model in each call')
	
	max_z = MODELS_HORIZONS[model][-1]
	
	while models_left >= 0:
		print ('update_degdays_table  \\ Checking model: %s ' % last_avail_model_dt)
		print ('INFO | models left: %s  |  last avail model dt: %s  | ' % (models_left, last_avail_model_dt))
	
		is_run_stack = []
		dirs_to_avg = []
		
		last_dir, last_grib_0 = get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, 0, model, model_mode, ensemble)
		df = pd.read_csv(outpath, parse_dates=['forecast_time'])
		run = False
		if df.shape[0] == 0:
			run = True
		elif last_avail_model_dt not in df[0-models_back-1:]['forecast_time'].tolist():   
			run = True	
		if not os.path.exists(last_dir):
			print('last_dir: %s Didnt exist for ensemble member: %s'% (last_dir, ensemble))
			run = False
			
		if model not in ['PARA','GEM','GEPS']:
			available_zs = [int(f.split('_')[4].split('.')[0]) for f in os.listdir(last_dir)]
		else:
			print ('last_dir: %s   | ' % (last_dir)) #, [f.split('_')[5] for f in os.listdir(last_dir)]))
			available_zs = [int(f.split('_')[5].split('.')[0]) for f in os.listdir(last_dir)]
		if len(available_zs) == 0:
			print ('changing to False 1')
			run = False
		# TBC here we will need to allow partial
		elif not max(available_zs) == max(MODELS_HORIZONS[model]):
			print ('changing to False 2 ([max(available_zs) ----> %s ] != max(MODELS_HORIZONS[model] ---> %s])'% (max(available_zs),max(MODELS_HORIZONS[model])))
			run = False
		is_run_stack.append(run)
		if run:
			dirs_to_avg.append(last_dir)
		# TBC | here we should adjust to handle specific Zs? limit min_z, max_z ??
		if run:
			dirs_dfs = []
			for dir_to_avg in dirs_to_avg:
				final_dfs = []
				for weight in weights:
					try:
						final_dfs.append(main_degdays_handle(dir_to_avg, weight, num_of_groups,max_z,model,model_mode))
					except Exception as e:
						print('Warning: Failed to run main_degdays_handle on \\ Weight: %s \\ dir: %s \n Exception Details : %s \n Will wait for the next iteration ' % (weight, last_dir, e))
						raise
					
				final_df = final_dfs[0]
				for other in final_dfs[1:]:
					final_df = final_df.merge(other, on=['forecast_time', 'days_ahead', 'ind_for_sum', 'Z'])
				print('After merging the different weights, we have the following columns: %s \n ------------------- ************* -------------------' % list(final_df))
				
				# here we have final_df ready, we can merge it
				dirs_dfs.append(final_df)
			
			df_to_avg = dirs_dfs[0]
			for other_dir_df in dirs_dfs[1:]:
				#assert list(df_to_avg) == list(other_dir_df), 'WARNING the columns werent equal for'
				#print ('Before merge. shape: %s' % str(other_dir_df.shape))
				df_to_avg = df_to_avg.merge(other_dir_df,how='outer',on=list(other_dir_df))
				#print ('After merge. shape: %s' % str(other_dir_df.shape))
			
			
			print ('About to take mean on the different_dirs, input has %s rows (dirs) to take mean on' % df_to_avg.shape[0])
			cols_to_avg = [l for l in list(df_to_avg) if l not in ['forecast_time','days_ahead','ind_for_sum','Z']]
			print (str(set(cols_to_avg) <= set(list(df_to_avg))))
			final_df2 = take_vectors_mean_from_str(df_to_avg,cols=cols_to_avg)
			
			if os.path.exists(outpath):
				original = pd.read_csv(outpath, parse_dates=['forecast_time'])
				common_cols = [col for col in list(final_df2) if col in list(set(list(final_df2)).intersection(set(list(original))))]
				val_cols = [c for c in common_cols if 'Value' in c]
				try:
					final_df2 = final_df2.merge(original, on=common_cols, how='outer').drop_duplicates().sort_values('forecast_time')
				except ValueError:
					for col in val_cols:
						original[col] = original[col].apply(lambda x: str(x))
					final_df2 = final_df2.merge(original, on=common_cols, how='outer').drop_duplicates().sort_values('forecast_time')
						
				except Exception as e:
					print('Couldnt merge final df and original, \n final_df2.head() = %s \\ \n original.head(): %s'% (final_df2.head(2), original.head(2)))
					raise
			
			final_df2.to_csv(outpath, index=False)
		else:
			print('There was no need to run degdays generation on model %s ... skipping' % last_avail_model_dt)
		
		models_left -= 1
		last_avail_model_dt += td(hours=MODEL_GAPS[model])
		last_avail_model_hour = last_avail_model_dt.hour

def take_mean_on_degdays_to_output_csv(input_csvs, output_csv, models_back, model):
	
	df_out = pd.read_csv(output_csv, parse_dates=['forecast_time'])
	cols1 = list(pd.read_csv(input_csvs[0]))
	
	# assertions 
	assert cols1 == list(df_out), 'input csvs columns are not equal to output'		
	for other_csv in input_csvs[1:]:
		cols2 = list(pd.read_csv(other_csv))
		assert cols1 == cols2, 'Found a csv with different columns, cant merge to take mean'
	
	# initialize 
	last_avail_model_dt = _last_available_model(dtdt.now(), model) - td(hours=MODEL_GAPS[model] * models_back)
	last_avail_model_hour = last_avail_model_dt.hour
	models_left = models_back
	while models_left >= 0:
			
		if not df_out[df_out['forecast_time'] == last_avail_model_dt].shape[0] == 0:
			print('The output csv already has an entry for this model_dt (%s)... Skipping the Avg' % last_avail_model_dt)
			models_left -= 1
			last_avail_model_dt += td(hours=MODEL_GAPS[model])
			last_avail_model_hour = last_avail_model_dt.hour
			continue
		
		df_to_avg = load_ensemble_csvs_to_df_by_model(input_csvs,last_avail_model_dt)
		#df_to_avg = df_to_avg[[l for l in list(df_to_avg) if l != 'member']]
		
		cols_to_avg = [l for l in cols1 if l not in ['forecast_time','days_ahead','ind_for_sum','Z']]
		
		final_df = take_vectors_mean_from_str(df_to_avg,cols=cols_to_avg)
		assert list(final_df) == cols1, 'The final avg df doesnt have the same columns as the output'
		assert final_df.shape[0] == 1, 'the avg_df should have 1 row !!!'
		
		df_out = df_out.append(final_df.iloc[0], ignore_index=True)	
		
		models_left -= 1
		last_avail_model_dt += td(hours=MODEL_GAPS[model])
		last_avail_model_hour = last_avail_model_dt.hour
	
	
	df_out = df_out.sort_values('forecast_time')
	df_out.to_csv(output_csv,index=False)


"""________________________________________________________"""

import time

if __name__ == '__main__':
	i = 1
	#while True:
	models = ['GEM','GEPS','GFS', 'GEFS','PARA']
	model_ind = int(sys.argv[1])
	models_back = int(sys.argv[2])
	try:
		work_mode = sys.argv[3]  
	except:
		work_mode = 'both'
	assert work_mode in ['download','degdays','both'], 'Got %s' % work_mode
	model = models[model_ind]
	now = dtdt.now()
	#for model in ['GEFS','GFS']:  # 'GFS', ּ
	print(' ********   Handling model: %s (%s models back) ***********' % (model, models_back))
	try:
		if work_mode in ['download','both']:
			check_for_new_files(models_back=models_back, model=model)
		
		if work_mode in ['degdays','both']:
			if model == 'GEFS':
				update_degdays_table(models_back=models_back, model=model,
									 outpath='%s/degdays_csvs/Final_Full_degdays/live_degdays_GEFS.csv' % HOME,
									 num_of_groups=3, model_mode='0')
			elif model == 'GFS':
				update_degdays_table(models_back=models_back, model=model,
									 outpath='%s/degdays_csvs/Final_Full_degdays/live_degdays_GFS.csv' % HOME,
									 num_of_groups=3, model_mode='0')
			elif model == 'PARA':
				update_degdays_table(models_back=models_back, model=model,
									 outpath='%s/degdays_csvs/Final_Full_degdays/live_degdays_PARA.csv' % HOME,
									 num_of_groups=3, model_mode='0')
			elif model == 'GEM': #, 'GEPS']:
				update_degdays_table(models_back=models_back, model=model,
									 outpath='%s/degdays_csvs/Final_Full_degdays/live_degdays_%s.csv' % (HOME, model),
									 num_of_groups=3, model_mode='1')
				update_degdays_table(models_back=models_back, model=model,
									 outpath='%s/degdays_csvs/Final_Full_degdays/live_degdays_%s_TDD.csv' % (
									 HOME, model), num_of_groups=3, model_mode='2')
			elif model == 'GEPS':
				out1 = "%s/degdays_csvs/Final_Full_degdays/live_degdays_GEPS.csv" % HOME
				out2 = "%s/degdays_csvs/Final_Full_degdays/live_degdays_GEPS_TDD.csv" % HOME
				csvs1 = []
				csvs2 = []
				
				for ensemble in ENSEMBLE_MODELS_RANGES[model]:
					out_csv1 = '%s/degdays_csvs/GEPS_members/live_degdays_%s_P%s.csv'%(HOME, model,str(ensemble).zfill(2))
					out_csv2 = '%s/degdays_csvs/GEPS_members/live_degdays_%s_TDD_P%s.csv'%(HOME, model,str(ensemble).zfill(2))
					update_degdays_table(models_back=models_back, model=model, outpath=out_csv1,
						num_of_groups=3, model_mode='1',ensemble=ensemble)
					#update_degdays_table(models_back=models_back, model=model, outpath=out_csv2,
					#	num_of_groups=3, model_mode='2', ensemble=ensemble)
					csvs1.append(out_csv1)
					csvs2.append(out_csv2)
				print ('GEPS | Now taking mean on ensembles to the main csv')
				take_mean_on_degdays_to_output_csv(csvs1, out1, models_back,model)
				#take_mean_on_degdays_to_output_csv(csvs2, out2, models_back,model)
				
					
	except Exception as e:
		print('At time : %s We recieved the follwoing Error: %s' % (now, e))
		raise e
	print('Finished current model: %s (%s)'% (model, now.strftime('%d/%m/%Y %H:%M:%S')))
#	time.sleep(600)
		
