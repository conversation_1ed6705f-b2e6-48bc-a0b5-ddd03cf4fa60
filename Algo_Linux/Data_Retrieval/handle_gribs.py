# from pymongo import MongoClient
from datetime import datetime as dtdt
from datetime import timedelta as td
from numpy import array as npa
# import pandas as pd
#import urllib, bs4
import os
# from matplotlib import pyplot as plt
# import pymongo

import sys
import calendar
import numpy as np
import requests

#from urllib3 import urlopen
import json
import csv
from pytz import timezone as tz
import pytz
from subprocess import check_call

from grib2mg import generate_mg_file
#from ..Data_Processing.handle_minigrib import main as main_degdays_handle

MAX_Z = 384
midi_above_240_zs = [12*i for i in range(20,33)]
midi_below_240 = [3*i for i in range(80)]
ALL_HORIZONS =  midi_below_240 +  midi_above_240_zs
GFS_4_HORIZONS = [z for z in ALL_HORIZONS if z <= MAX_Z]


MODELS_LATENCIES = {'GFS': 5}
MODELS_RUNS = {'GFS': [0, 6, 12, 18]}
MODEL_GAPS = {'GFS': 6}


GERMANY_LAT_LON = (47, 55, 5, 15)

GFS_BASE_URL = "http://nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/prod/gfs."
GFS_DATE_SUFFIX_EXAMPLE = "/gfs.2018090700/"

MODEL_RUN = [6 * i for i in range(4)]

MAXIMAL_HORIZON = 384


url_example = "http://nomads.ncep.noaa.gov/pub/data/nccf/com/gfs/prod/gfs.2018090700/gfs.t00z.pgrb2.0p25.f384"

# urllib.urlretrieve(url,"C:\Users\<USER>\Downloads\Ran.grib2.bz2")


def get_ingrib_str(model_hour, horizon):
	return "/gfs.t%sz.pgrb2.0p50.f%s"%(str(model_hour).zfill(2), str(horizon).zfill(3))


def get_grib_outpath_tuple(model_day, model_hour, horizon):
	time_str = '%s%s'%(model_day.strftime('%Y%m%d'), str(model_hour).zfill(2))
	d = '/home/<USER>/GFS/%s' % time_str
	fn = 'gfs_05_%s_%s_%s.grb2' % (model_day.strftime('%Y%m%d'),str(model_hour).zfill(2)+'00', str(horizon).zfill(3))
	return d, fn


"""__________________________"""


def _last_available_model(x_dt, model):
	latency = MODELS_LATENCIES[model]
	model_runs = MODELS_RUNS[model]

	x_dt2 = x_dt - td(hours=latency)
	while x_dt2.hour not in model_runs:
		x_dt2 -= td(hours=1)

	x_dt3 = dtdt(x_dt2.year, x_dt2.month, x_dt2.day, x_dt2.hour)
	return x_dt3


def _model_availability_time(model_runtime, model):
	latency = MODELS_LATENCIES[model]
	availability_time = model_runtime + td(seconds=3600 * latency)
	return availability_time



def get_full_url(horizon, model_day, model_hour):
	ingrib_str = get_ingrib_str(model_hour, horizon)
	return GFS_BASE_URL + model_day.strftime('%Y%m%d') + str(model_hour).zfill(2) + ingrib_str

def get_grib(url, outpath):
	print('Trying to retrieve the following URL: %s' % url)
	cmd = 'wget %s -O %s' % (url, outpath)
	print(cmd)
	check_call(cmd, shell=True)

def truncate_grib2(grib_path, mini_grib_path=None, delete_original=True):
	if not mini_grib_path:
		grib_name = grib_path.split('/')[-1]
		grib_dir = '/'.join(grib_path.split('/')[:-1])
		mini_grib_path = grib_path.split('.grb2')[0] +'.US.minigrib'
	generate_mg_file(grib_path,mini_grib_path, 'NG_USA_GFS_RAN')

	if delete_original:
		print('deleting grib file : %s' % grib_path)
		check_call('rm -rf %s'% grib_path, shell=True)

"""_______________________________________________"""


def check_for_new_files(model='GFS'):
	last_avail_model_dt = _last_available_model(dtdt.now(), model)
	last_avail_model_hour = last_avail_model_dt.hour

	desired_grib_tuples = [get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, z) for z in GFS_4_HORIZONS]
	desired_grib_fullpaths = [d+'/'+fn for d,fn in desired_grib_tuples]

	missing_grib_fullpaths = [grib for grib in desired_grib_fullpaths if not os.path.exists(grib)]

	print('about to run on all horizons for model: %s'%last_avail_model_dt.strftime('%d/%m/%Y %H:%M:%S'))
	for z in GFS_4_HORIZONS:
		grib_dir, grib_name = get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, z)
		if not os.path.exists(grib_dir):
			os.makedirs(grib_dir)

		grib_local_path = grib_dir+'/'+grib_name

		url = get_full_url(z, last_avail_model_dt, last_avail_model_hour)
		if z % 6 == 0:
			print('reached Z = %s ... \\ trying to brine grib URL: %s ----->>> %s' % (z,url, grib_local_path))

		if not os.path.exists(grib_local_path.split('.grb2')[0]+'.US.minigrib'):
			# we need to download
			try:
				get_grib(url, grib_local_path)
			except Exception as e:
				print('got Exception: %s'%e)
				print('failed on z = %s .... Skipping '%z)
				continue

			print('about to truncate .... ')
			truncate_grib2(grib_local_path)


def update_degdays_table(model='GFS', outpath='/home/<USER>/live_degdays.csv',num_of_groups=1):
	last_avail_model_dt = _last_available_model(dtdt.now(), model)
	last_avail_model_hour = last_avail_model_dt.hour

	last_dir, last_grib_0 = get_grib_outpath_tuple(last_avail_model_dt, last_avail_model_hour, 0)
	main_degdays_handle(last_dir, outpath, num_of_groups)

"""________________________________________________________"""

import time

if __name__ == '__main__':
	i = 1
	while True:
		now = dtdt.now()
		check_for_new_files()
		update_degdays_table()
		print('Going to Sleep.....  (%s)'% now.strftime('%d/%m/%Y %H:%M:%S'))
		time.sleep(1800)
		i = 1
	a = 1

