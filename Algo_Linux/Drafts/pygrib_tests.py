import pygrib as pg

file_name = "/home/<USER>/GEM/201812/2018121512/gem_0p24_TMP2m_20181215_12_015.US.minigrib"
#file_name = "/home/<USER>/GFS_test_gfs.t00z.pgrb2.0p25.f384"

grib_data = pg.open(file_name)
#print ('INFO INFO INFO first 5 msgs: %s' % grib_data.read(1))
grib_index = pg.index(file_name, 'typeOfLevel', 'level', 'name')


#  281:2 metre temperature:K (instant):regular_ll:heightAboveGround:level 2 m:fcst time 384 hrs:from 201812150000
#    1:2 metre temperature:K (instant):regular_ll:heightAboveGround:level 2 m:fcst time 15 hrs:from 201812151200

#grib_latitude = grib_latitude.reshape(grib_latitude.size)[coordinates_mask]
#grib_longitude = grib_longitude.reshape(grib_longitude.size)[coordinates_mask]
#grib_1st_message.value

for i in range(1,2): #(281,282): #500):
	try:
		grib_1st_message = grib_data[i]
		#if 'Temperature' in str(grib_1st_message):
		print ('the %s-th meassage ---- > \n %s' % (i,grib_1st_message))
	except Exception as e:
		#raise
		print ('Exception in %s-th iteration, Message: %s' %(i, str(e)))
		print ('grib message: %s' % str(grib_1st_message))

try:
	grib_latitude, grib_longitude = grib_1st_message.latlons()
except:
	pass
		
grib_index = pg.index(file_name, 'typeOfLevel', 'level', 'name')
grib_messages = grib_index.select(typeOfLevel='heightAboveGround',
												  level='2', name='2 metre temperature')

#data = grib_1st_message.data()
data = grib_messages[0]

#print (type(grib_1st_message))
print (data)