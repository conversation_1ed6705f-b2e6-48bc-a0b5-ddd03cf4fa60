import pandas as pd
import os


def adjust_csvs():
	for f in files:
		print ("File: %s" % f)
		file_path = d+f
		#tmp_df = pd.read_csv(file_path)
		try:
			with open(file_path) as g:
				lines = g.readlines()
			new_line1 = lines[1].replace(",,Channel","index_0,index_1,Channel")
			new_lines = [new_line1]+lines[2:]
			with open(file_path.replace(".csv","_new.csv"),"w") as g:
				g.writelines(new_lines)
		except Exception as e:
			print ("Skipping File due to Error: %s"%e)


d = "/mnt/volume_100G/home/<USER>/tmp/"
files = [f for f in os.listdir(d) if 'new.csv' in f]

final = pd.DataFrame()
			
for f in files:
	print ("Handling File: %s | final_df.shape = %s"% (f,str(final.shape)))
	try:
		file_path = d+f
		tmp_df = pd.read_csv(file_path)
		#tmp_df['Followers gained'] = tmp_df['Followers gained'].astype("float64") 
		if final.shape[0] == 0:
			final = tmp_df
		else:
			final = pd.concat([final,tmp_df])  #final.merge(tmp_df,on=list(tmp_df),how="outer")
	except Exception as e:
		print ("Skipping File: %s | Error : %s"% (f, e))
		print ("tmp_df.dtypes: %s"%tmp_df.dtypes)
		print ("final_df.dtypes: %s"%final.dtypes)
final = final.drop_duplicates()
print ("final_df.shape BEFORE WRITING: %s" % str(final.shape))
final.to_csv(d+"Final_Streamers_table.csv",index=False)		
	