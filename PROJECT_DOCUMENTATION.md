# TWS API Algorithmic Trading System - Project Documentation

## Overview
This is a comprehensive algorithmic trading system built around Interactive Brokers' TWS (Trader Workstation) API, primarily focused on natural gas (NG) trading with additional support for wind, coffee, and other commodities. The system integrates weather forecasting models, machine learning predictions, and automated trading strategies.

## Project Structure

### 📁 **Algo/** - Main Algorithm Directory
The core algorithmic trading system with the following major components:

#### 🔧 **Wrapper/** - Orchestration & Control
- **`outer_full_wrapper_v2.py`** - Main orchestration script that coordinates the entire trading pipeline
  - Manages time-based execution schedules (0Z, 6Z, 12Z, 18Z cycles)
  - Coordinates data retrieval, feature generation, model training, and trading decisions
  - Handles multiple trading modes (real, paper, ensemble)
  - Supports multiple assets (NG, Wind, Coffee)

- **`wrap_process.py`** - Core process wrapper for data pipeline
  - Orchestrates X (features) and Y (targets) generation
  - Manages candle data retrieval and processing
  - Handles bollinger band strategies

- **`cluster_auto_selection_utils.py`** - Automated cluster selection for ensemble trading
- **`wrap_bollinger_positions.py`** - Bollinger band trading strategy implementation

#### 🧠 **Learning/** - Machine Learning & AI
- **`algo_wrapper2.py`** - Main ML wrapper and prediction engine
  - Handles strategy backtesting and performance analysis
  - Manages ensemble model predictions
  - Generates trading signals based on ML models

- **`decision_maker.py`** - Trading decision engine
  - Converts ML predictions into trading actions
  - Manages risk and position sizing
  - Handles multiple trading strategies and modes
  - Supports wind and coffee trading decisions

#### 📊 **Data_Retrieval/** - External Data Sources
- **`ttf_retrieval.py`** - TTF (Title Transfer Facility) gas price scraping and candle data
- **`coal_retrieval.py`** - Coal price data retrieval
- **`JKM_retrieval.py`** - Japan Korea Marker (LNG) price scraping
- **`gold_trader_retrieval.py`** - Gold trading data from external sources
- **`retrieve_weathermodels.py`** - Weather model data retrieval

#### 🔄 **Data_Processing/** - Feature Engineering
- **`wind_features_generator.py`** - Wind-specific feature generation for wind trading
- **`algo_preds_features_generator.py`** - Algorithm prediction features
- **`generate_past_positions_features.py`** - Historical position-based features
- **`seasonal_values.py`** - Seasonal pattern features
- **`CFS_Monthly_features_generator.py`** - Climate Forecast System monthly features

#### 🌡️ **Xbox/** - Weather Data Processing
- **`generate_x_data.py`** - Core weather feature generation from multiple forecast models
  - Processes GFS, PARA, GEFS, EPS, EC, and other weather models
  - Generates degree day features (HDD/CDD/GDD)
  - Handles multiple forecast horizons and model ensembles

- **`retrieve_from_ec2.py`** - Downloads weather model data from AWS EC2 instances
- **`other_x_features.py`** - Additional weather-based features

#### 🤖 **AI/** - Advanced Analytics
- **`create_forecast_clusters.py`** - Weather forecast clustering for ensemble strategies
  - Groups similar weather forecasts using K-means clustering
  - Creates weighted ensemble predictions
  - Generates cluster-based trading features

#### 💹 **Trading/** - Execution Engine
- **`main_trader_v4.py`** - Core trading execution engine
- **`batch_trader.py`** - Batch trading operations
- Trading strategy implementations and position management

#### 📈 **Visualization/** - Analytics & Reporting
- **`trading_strategies_summary.py`** - Trading performance analysis and visualization
  - Position summarization and P&L analysis
  - RSI and technical indicator preparation
  - Multi-mode performance comparison

- **`aggregated_analysis_linux.py`** - Comprehensive chart analysis

#### 🧪 **Tests/** - Quality Assurance
- **`test_Xs.py`** - Weather feature validation
- **`test_daily_predictions.py`** - Prediction validation
- **`test_candles_df.py`** - Market data validation

#### 🛠️ **Utils/** - Utilities
- **`send_email.py`** - Email notifications and reporting
- **`files_handle.py`** - File path management and utilities
- **`yaml_handle.py`** - Configuration management

#### 📓 **Notebooks/** - Research & Development
- **`new_strategies_finder/`** - Strategy research and backtesting
- **`algo_wrapper_plays/`** - Algorithm experimentation
- Research notebooks for strategy development and analysis

### 📁 **IBridge/** - Interactive Brokers Integration
- **v20/**, **v201/** - Different versions of IB bridge configurations
- Settings and connection management for TWS API

### 📁 **source/** - TWS API Client Libraries
- **pythonclient/**, **pythonclient_976/** - IB Python API client libraries
- Low-level TWS API communication protocols

## System Architecture & Workflow

### 🕐 **Time-Based Execution Schedule**
The system operates on a sophisticated time-based schedule aligned with weather model release times:

1. **0Z Cycle (Early Morning)**
   - Download overnight weather model runs
   - Generate morning predictions
   - Execute early trading decisions

2. **6Z Cycle (Morning)**
   - Process 6Z weather models
   - Update features and predictions
   - Execute morning trading strategies

3. **12Z Cycle (Afternoon)**
   - Process 12Z weather models (primary cycle)
   - Generate main daily predictions
   - Execute primary trading decisions

4. **18Z Cycle (Evening)**
   - Process evening weather models
   - Prepare for next day
   - Generate overnight positions

### 🔄 **Data Pipeline Flow**

1. **Weather Data Ingestion**
   - `retrieve_from_ec2.py` downloads latest weather model runs
   - Multiple models: GFS, PARA, GEFS, EPS, EC, CFS, etc.

2. **Feature Generation**
   - `generate_x_data.py` processes weather data into trading features
   - Degree day calculations (HDD/CDD/GDD)
   - Regional and temporal aggregations

3. **Market Data Collection**
   - TTF, coal, JKM price scraping
   - Natural gas futures candle data
   - Technical indicators (RSI, Bollinger Bands)

4. **ML Prediction**
   - `algo_wrapper2.py` generates ensemble predictions
   - Multiple model strategies and timeframes
   - Cluster-based ensemble methods

5. **Trading Decision**
   - `decision_maker.py` converts predictions to trading signals
   - Risk management and position sizing
   - Multi-asset trading coordination

6. **Execution & Monitoring**
   - TWS API integration for order execution
   - Real-time position monitoring
   - Performance tracking and reporting

### 🎯 **Key Trading Strategies**

1. **Weather-Based Natural Gas Trading**
   - Primary focus on heating/cooling degree days
   - Seasonal pattern recognition
   - Multi-model weather ensemble predictions

2. **Wind Power Trading**
   - Wind speed forecasting for power generation
   - Regional wind pattern analysis
   - Texas and US wind market focus

3. **Coffee Trading**
   - Weather impact on coffee growing regions
   - Seasonal agricultural patterns
   - South American weather focus

4. **Ensemble Strategies**
   - Cluster-based forecast grouping
   - Dynamic strategy selection
   - Risk-adjusted position sizing

### 🔧 **Configuration & Modes**

- **Real Mode**: Live trading with real money
- **Paper Mode**: Simulated trading for testing
- **Research Mode**: Historical backtesting and analysis
- **Ensemble Modes**: Multiple strategy combinations

### 📊 **Key Features**

- **Multi-Asset Support**: NG, Wind, Coffee, Coal, Gold
- **Weather Integration**: 10+ weather forecast models
- **ML Ensemble**: Multiple prediction algorithms
- **Risk Management**: Position sizing and stop-loss
- **Real-time Monitoring**: Live P&L and position tracking
- **Automated Execution**: Scheduled trading cycles
- **Performance Analytics**: Comprehensive reporting and visualization

### 🚀 **Getting Started**

The main entry point is `outer_full_wrapper_v2.py` which orchestrates the entire system. The system can be run in different modes:

- Full pipeline execution
- Specific function execution (e.g., data retrieval only)
- Asset-specific trading (wind, coffee, etc.)
- Time-mode overrides for testing

This system represents a sophisticated quantitative trading platform that combines meteorological science, machine learning, and algorithmic trading to capitalize on weather-driven commodity price movements.
