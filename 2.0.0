Requirement already satisfied: pyppeteer in c:\users\<USER>\miniconda311\lib\site-packages (0.2.5)
Requirement already satisfied: appdirs<2.0.0,>=1.4.3 in c:\users\<USER>\miniconda311\lib\site-packages (from pyppeteer) (1.4.4)
Collecting pyee<9.0.0,>=8.1.0 (from pyppeteer)
  Using cached pyee-8.2.2-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: tqdm<5.0.0,>=4.42.1 in c:\users\<USER>\miniconda311\lib\site-packages (from pyppeteer) (4.46.0)
Requirement already satisfied: urllib3<2.0.0,>=1.25.8 in c:\users\<USER>\miniconda311\lib\site-packages (from pyppeteer) (1.26.18)
Collecting websockets<9.0,>=8.1 (from pyppeteer)
  Using cached websockets-8.1-cp311-cp311-win_amd64.whl
Using cached pyee-8.2.2-py2.py3-none-any.whl (12 kB)
Installing collected packages: pyee, websockets
  Attempting uninstall: pyee
    Found existing installation: pyee 13.0.0
    Uninstalling pyee-13.0.0:
      Successfully uninstalled pyee-13.0.0
  Attempting uninstall: websockets
    Found existing installation: websockets 10.4
    Uninstalling websockets-10.4:
      Successfully uninstalled websockets-10.4
Successfully installed pyee-8.2.2 websockets-8.1
