C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\bin\Release\OrderConditionsParsingTestProject.dll
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\bin\Release\OrderConditionsParsingTestProject.pdb
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\bin\Release\TWSLib.dll
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\bin\Release\TWSLib.pdb
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\obj\Release\TWSLibTestProject.csprojResolveAssemblyReference.cache
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\obj\Release\OrderConditionsParsingTestProject.dll
C:\Users\<USER>\Source\Repos\InteractiveBrokers\tws-api-installer\apiInstaller\githubTwsApiCode\tws-api\tests\TWSLibTestProject\obj\Release\OrderConditionsParsingTestProject.pdb
