2024-02-15 18:08:34.435767-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-02-15 18:08:37.588396-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-02-15 18:08:37.608672-05:00: Connected to Interactive Brokers
2024-02-15 18:08:37.632217-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-02-15 18:09:07.633530-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestR<PERSON>ord failed
2024-02-15 18:09:07.633973-05:00: {reqId=4964626,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-02-15 18:11:22.629405-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-02-15 18:11:25.742567-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-02-15 18:11:25.813518-05:00: Connected to Interactive Brokers
2024-02-15 18:11:25.820566-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-02-15 18:11:26.406430-05:00: CASH=-51865.24
2024-02-15 18:11:26.407383-05:00: portfolio_value=104634.07
2024-02-15 18:11:26.407383-05:00: positions_value=156649.37
2024-02-15 18:11:26.439301-05:00: ##    POSITIONS U2423888   ##
2024-02-15 18:11:26.440367-05:00: Symbol Amount Cost_basis
2024-02-15 18:11:26.440367-05:00: STK,,,VTRS,USD 2 14.3746
2024-02-15 18:11:26.440367-05:00: STK,,,TAN,USD 5 73.76
2024-02-15 18:11:26.441427-05:00: STK,,,AAPL,USD 14 183.7142857
2024-02-15 18:11:26.441427-05:00: FUT,,,MBT,USD, 1 5268.77
2024-02-15 18:11:26.442290-05:00: STK,,,ISTB,USD 500 47.3387112
2024-02-15 18:11:26.442290-05:00: STK,,,GOOG,USD 14 146.********
2024-02-15 18:11:26.442290-05:00: STK,,,TM,USD 14 191.********
2024-02-15 18:11:26.442290-05:00: STK,,,MSFT,USD 8 372.8995
2024-02-15 18:11:26.443288-05:00: STK,,,AMZN,USD 4 137.91
2024-02-15 18:11:26.443744-05:00: STK,,,NFLX,USD 2 364.17
2024-02-15 18:11:26.444028-05:00: STK,,,TEVA,USD 100 12.915
2024-02-15 18:11:26.444371-05:00: STK,,,META,USD 3 361.3
2024-02-15 18:11:26.444371-05:00: STK,,,NVDA,USD 3 515.********
2024-02-15 18:11:26.445308-05:00: STK,,,IJJ,USD 7 110.08
2024-02-15 18:11:26.445375-05:00: STK,,,TSLA,USD 3 272.********
2024-02-15 18:11:26.482240-05:00: ##    NO any order    ##
2024-02-15 18:11:30.032630-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964633
2024-02-15 18:11:33.866004-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964638
2024-02-15 18:11:38.804680-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964642
2024-02-15 18:11:42.615543-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964647
2024-02-15 18:11:47.633936-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964651
2024-02-15 18:11:51.611338-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964656
2024-02-15 18:11:56.716554-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964660
2024-02-15 18:12:00.643544-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964665
