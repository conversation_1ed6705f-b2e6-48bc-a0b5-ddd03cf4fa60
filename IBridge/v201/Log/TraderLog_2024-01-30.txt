2024-01-30 02:10:22.146067-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-30 02:10:25.504013-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-30 02:10:25.794516-05:00: Connected to Interactive Brokers
2024-01-30 02:10:25.810927-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-01-30 02:10:25.983424-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964419
2024-01-30 02:10:25.987356-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964424
2024-01-30 02:10:30.617183-05:00: CASH=-85744.52
2024-01-30 02:10:30.619177-05:00: portfolio_value=105059.91
2024-01-30 02:10:30.619177-05:00: positions_value=191247.52
2024-01-30 02:10:30.760844-05:00: ##    POSITIONS U2423888   ##
2024-01-30 02:10:30.761841-05:00: Symbol Amount Cost_basis
2024-01-30 02:10:30.762839-05:00: STK,,,VTRS,USD 2 14.3746
2024-01-30 02:10:30.764029-05:00: STK,,,TAN,USD 5 73.76
2024-01-30 02:10:30.776879-05:00: STK,,,SPY,USD 3 436.47
2024-01-30 02:10:30.777876-05:00: STK,,,AAPL,USD 8 180.9225
2024-01-30 02:10:30.778873-05:00: STK,,,ISTB,USD 500 47.3387112
2024-01-30 02:10:30.779870-05:00: STK,,,GOOG,USD 30 140.********
2024-01-30 02:10:30.788733-05:00: STK,,,TM,USD 12 187.********
2024-01-30 02:10:30.789730-05:00: STK,,,MSFT,USD 8 372.8995
2024-01-30 02:10:30.790726-05:00: STK,,,AMZN,USD 4 137.91
2024-01-30 02:10:30.790726-05:00: STK,,,QTEC,USD 40 178.8311675
2024-01-30 02:10:30.791725-05:00: STK,,,NFLX,USD 2 364.17
2024-01-30 02:10:30.792721-05:00: FUT,,,MCL,USD, 1 7700.77
2024-01-30 02:10:30.793719-05:00: STK,,,META,USD 3 361.3
2024-01-30 02:10:30.794716-05:00: STK,,,NVDA,USD 5 476.183
2024-01-30 02:10:30.794716-05:00: STK,,,IJJ,USD 7 110.08
2024-01-30 02:10:30.795714-05:00: STK,,,TSLA,USD 3 272.********
2024-01-30 02:10:30.961922-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964419
2024-01-30 02:10:30.965913-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964424
2024-01-30 02:10:30.969104-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib0
2024-01-30 02:10:30.971028-05:00: ##    Order Status U2423888   ##
2024-01-30 02:10:30.972025-05:00: {ibpyOrderId=ib1204297565 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-01-30 02:10:31.024660-05:00: {ibpyOrderId=ib4964419 status=PreSubmitted order=NONE contract=NONE}
2024-01-30 02:10:31.025657-05:00: {ibpyOrderId=ib1204297566 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-01-30 02:10:31.025657-05:00: {ibpyOrderId=ib4964424 status=PreSubmitted order=NONE contract=NONE}
2024-01-30 02:10:31.026654-05:00: {ibpyOrderId=ib1122506852 status=PreSubmitted order={account=U2423888 action=SELL orderType=STP amount=1 tif=GTC orderRef= stopPrice=76.63} contract={FUT,NYMEX,MCL,USD}}
2024-01-30 02:10:31.027618-05:00: {ibpyOrderId=ib0 status=PreSubmitted order=NONE contract=NONE}
2024-01-30 02:10:31.028619-05:00: ##    END    ##
2024-01-30 02:10:34.722574-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964432
2024-01-30 02:10:39.312354-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964437
