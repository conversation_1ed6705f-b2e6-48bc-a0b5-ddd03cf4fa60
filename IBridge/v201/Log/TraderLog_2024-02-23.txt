2024-02-22 17:26:26.800958-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-02-22 17:26:29.976966-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-02-22 17:26:30.004890-05:00: Connected to Interactive Brokers
2024-02-22 17:26:30.018853-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-02-22 17:27:00.019238-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestR<PERSON>ord failed
2024-02-22 17:27:00.020235-05:00: {reqId=4964719,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-02-22 17:28:59.419304-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-02-22 17:29:02.562910-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-02-22 17:29:02.594831-05:00: Connected to Interactive Brokers
2024-02-22 17:29:02.621058-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-02-22 17:29:03.020058-05:00: CASH=-61241.07
2024-02-22 17:29:03.020058-05:00: portfolio_value=107230.01
2024-02-22 17:29:03.021017-05:00: positions_value=168706.9
2024-02-22 17:29:03.096814-05:00: ##    POSITIONS U2423888   ##
2024-02-22 17:29:03.096814-05:00: Symbol Amount Cost_basis
2024-02-22 17:29:03.098816-05:00: STK,,,VTRS,USD 2 14.3746
2024-02-22 17:29:03.100807-05:00: STK,,,TAN,USD 5 73.76
2024-02-22 17:29:03.102579-05:00: STK,,,AAPL,USD 14 183.7142857
2024-02-22 17:29:03.102797-05:00: STK,,,ISTB,USD 500 47.3387112
2024-02-22 17:29:03.103794-05:00: STK,,,GOOG,USD 14 149.********
2024-02-22 17:29:03.103794-05:00: STK,,,TM,USD 14 191.********
2024-02-22 17:29:03.104791-05:00: STK,,,MSFT,USD 10 380.7156
2024-02-22 17:29:03.104791-05:00: STK,,,AMZN,USD 4 137.91
2024-02-22 17:29:03.105790-05:00: STK,,,QTEC,USD 12 189.********
2024-02-22 17:29:03.105790-05:00: STK,,,NFLX,USD 2 364.17
2024-02-22 17:29:03.106786-05:00: STK,,,TEVA,USD 100 12.915
2024-02-22 17:29:03.106786-05:00: STK,,,META,USD 3 361.3
2024-02-22 17:29:03.106786-05:00: STK,,,NVDA,USD 3 614.********
2024-02-22 17:29:03.107784-05:00: STK,,,IJJ,USD 7 110.08
2024-02-22 17:29:03.107784-05:00: STK,,,TSLA,USD 3 272.********
2024-02-22 17:29:03.238063-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib0
2024-02-22 17:29:03.239836-05:00: ##    Order Status U2423888   ##
2024-02-22 17:29:03.244565-05:00: {ibpyOrderId=ib311768222 status=PreSubmitted order={account=U2423888 action=BUY orderType=STP amount=2 tif=GTC orderRef= stopPrice=52475.0} contract={FUT,CME,MBT,USD}}
2024-02-22 17:29:03.246967-05:00: {ibpyOrderId=ib0 status=PreSubmitted order=NONE contract=NONE}
2024-02-22 17:29:03.247754-05:00: ##    END    ##
2024-02-22 17:29:06.624563-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964695
2024-02-22 17:29:10.504929-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964700
2024-02-22 17:29:15.541355-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964704
2024-02-22 17:29:19.396763-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964709
2024-02-22 17:29:24.404996-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964713
2024-02-22 17:29:28.309450-05:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4964718
