2024-01-25 02:00:57.358471-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-25 02:01:00.438273-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-25 02:01:00.480954-05:00: Connected to Interactive Brokers
2024-01-25 02:01:00.493075-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-01-25 02:01:30.494202-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-01-25 02:01:30.495250-05:00: {reqId=4964378,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-01-25 02:07:28.195602-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-25 02:07:31.368174-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-25 02:07:31.380799-05:00: Connected to Interactive Brokers
2024-01-25 02:07:31.389774-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-01-25 02:08:01.537573-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-01-25 02:08:01.539633-05:00: {reqId=4964379,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-01-25 02:10:21.323518-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-25 02:10:24.433831-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-25 02:10:24.442774-05:00: Connected to Interactive Brokers
2024-01-25 02:10:24.454778-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-01-25 02:10:54.455788-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-01-25 02:10:54.455788-05:00: {reqId=4964380,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-01-25 02:11:56.644862-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-25 02:11:59.791495-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-25 02:11:59.802466-05:00: Connected to Interactive Brokers
2024-01-25 02:11:59.810444-05:00: ##    ACCOUNT Balance  U2423888  ##
2024-01-25 02:12:29.811445-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-01-25 02:12:29.811587-05:00: {reqId=4964381,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
