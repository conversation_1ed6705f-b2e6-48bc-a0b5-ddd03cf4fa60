2024-01-16 16:59:20.466546-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-16 16:59:23.591914-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-16 16:59:31.926964-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-01-16 16:59:35.045560-05:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-01-16 16:59:40.148471-05:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=4001 clientId=9
2024-01-16 16:59:45.271546-05:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7496 clientId=9
2024-01-16 16:59:48.394036-05:00: Try to connect to Interactive Brokers: host=localhost port=7496 clientId=9
2024-01-16 16:59:48.400020-05:00: Connected to Interactive Brokers
2024-01-16 16:59:48.406005-05:00: ##    ACCOUNT Balance  DU1541008  ##
2024-01-16 17:00:18.407903-05:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-01-16 17:00:18.408227-05:00: {reqId=4964338,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:DU1541008,}}
