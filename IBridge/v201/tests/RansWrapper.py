from source.pythonclient.brokerapi.client import EClient
from source.pythonclient.brokerapi.wrapper import EWrapper
from brokerapi.common import *
from brokerapi.contract import *
from threading import Timer
from datetime import datetime as dtdt
from datetime import timedelta as td
import collections
import os
import pandas as pd
import brokerapi.decoder
import brokerapi.wrapper

from brokerapi.ticktype import TickType, TickTypeEnum
from brokerapi.comm import *
from brokerapi.message import IN, OUT
from brokerapi.connection import Connection
from brokerapi.reader import EReader
from brokerapi.utils import *
from brokerapi.execution import ExecutionFilter
from brokerapi.scanner import ScannerSubscription
from brokerapi.order_condition import *
from brokerapi.order import *
from brokerapi.order_state import *

from samples.Python.Testbed.ContractSamples import ContractSamples
import numpy as np

CONTRACTS_DICT = {"NG": {"symbol": "NG",
                         "Trading Class": "NG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "LocalSymbol": "NGZ7"
                         },
                  "KC": {"symbol": "KC",
                         "Trading Class": "KC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYBOT",
                         # "ConId": 81596359,
                         "LocalSymbol": "KCZ7"  # Dec Z | Sep U
                         },
                  "UNG":
                      {"symbol": "UNG",
                       "currency": "USD",
                       "secType": "STK",
                       "exchange": "SMART",
                       "primary_exchange": "ARCA"
                       },
                  "ZC":
                      {"symbol": "ZC",
                       "Trading Class": "ZC",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "ECBOT",
                       "LocalSymbol": "ZC   JUL 20"  # "ZCZ7"  # Dec Z | Sep U
                       # "ConID": 291854767

                       }
                  }

NG_CONTRACT_CODES = {'January': 'F', 'February': 'G', 'March': 'H', 'April': 'J',
                     'May': 'K', 'June': 'M', 'July': 'N', 'August': 'Q', 'September': 'U',
                     'October': 'V', 'November': 'X', 'December': 'Z'}

DAYS_TO_EXP = {'NG': 9, 'COFFEE': 30}

MAX_MKT_VALUE_FOR_TRADE = 2000  # USD
HOST_IP = '127.0.0.1'
PAPER_PORT = 7497


# ! [socket_declare]
class RansClient(EClient):
    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)
        # ! [socket_declare]

        # how many times a method is called to see test coverage
        self.clntMeth2callCount = collections.defaultdict(int)
        self.clntMeth2reqIdIdx = collections.defaultdict(lambda: -1)
        self.reqId2nReq = collections.defaultdict(int)
        self.setupDetectReqId()

    def countReqId(self, methName, fn):
        def countReqId_(*args, **kwargs):
            self.clntMeth2callCount[methName] += 1
            idx = self.clntMeth2reqIdIdx[methName]
            if idx >= 0:
                sign = -1 if 'cancel' in methName else 1
                self.reqId2nReq[sign * args[idx]] += 1
            return fn(*args, **kwargs)

        return countReqId_

    def setupDetectReqId(self):

        methods = inspect.getmembers(EClient, inspect.isfunction)
        for (methName, meth) in methods:
            if methName != "send_msg":
                # don't screw up the nice automated logging in the send_msg()
                self.clntMeth2callCount[methName] = 0
                # logging.debug("meth %s", name)
                sig = inspect.signature(meth)
                for (idx, pnameNparam) in enumerate(sig.parameters.items()):
                    (paramName, param) = pnameNparam
                    if paramName == "reqId":
                        self.clntMeth2reqIdIdx[methName] = idx

                setattr(RansClient, methName, self.countReqId(methName, meth))

                # print("TestClient.clntMeth2reqIdIdx", self.clntMeth2reqIdIdx)


# ! [ewrapperimpl]
class RansWrapper(EWrapper):
    # ! [ewrapperimpl]
    def __init__(self):
        EWrapper.__init__(self)

        self.wrapMeth2callCount = collections.defaultdict(int)
        self.wrapMeth2reqIdIdx = collections.defaultdict(lambda: -1)
        self.reqId2nAns = collections.defaultdict(int)
        self.setupDetectWrapperReqId()

    # TODO: see how to factor this out !!

    def countWrapReqId(self, methName, fn):
        def countWrapReqId_(*args, **kwargs):
            self.wrapMeth2callCount[methName] += 1
            idx = self.wrapMeth2reqIdIdx[methName]
            if idx >= 0:
                self.reqId2nAns[args[idx]] += 1
            return fn(*args, **kwargs)

        return countWrapReqId_

    def setupDetectWrapperReqId(self):

        methods = inspect.getmembers(EWrapper, inspect.isfunction)
        for (methName, meth) in methods:
            self.wrapMeth2callCount[methName] = 0
            # logging.debug("meth %s", name)
            sig = inspect.signature(meth)
            for (idx, pnameNparam) in enumerate(sig.parameters.items()):
                (paramName, param) = pnameNparam
                # we want to count the errors as 'error' not 'answer'
                if 'error' not in methName and paramName == "reqId":
                    self.wrapMeth2reqIdIdx[methName] = idx

            setattr(RansWrapper, methName, self.countWrapReqId(methName, meth))

            # print("TestClient.wrapMeth2reqIdIdx", self.wrapMeth2reqIdIdx)


class Main_Constructor(RansWrapper, RansClient):
    def __init__(self):
        RansWrapper.__init__(self)
        RansClient.__init__(self, wrapper=self)
        # ! [socket_init]
        self.nKeybInt = 0
        self.started = False
        self.nextValidOrderId = None
        self.permId2ord = {}
        self.reqId2nErr = collections.defaultdict(int)
        self.globalCancelOnly = False
        self.simplePlaceOid = None
        self.current_historical_stack = []


    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        super().contractDetails(reqId, contractDetails)
        print('Contract details for request # %s: %s' % (reqId, contractDetails))

    # overriding the default that writes to the logger
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print('yesssssssss')
        super().orderStatus(orderId, status, filled, remaining,
                            avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
        print("OrderStatus. Id: ", orderId, ", Status: ", status, ", Filled: ", filled,
          ", Remaining: ", remaining, ", AvgFillPrice: ", avgFillPrice,
          ", PermId: ", permId, ", ParentId: ", parentId, ", LastFillPrice: ",
          lastFillPrice, ", ClientId: ", clientId, ", WhyHeld: ",
          whyHeld, ", MktCapPrice: ", mktCapPrice)

    def openOrder(self, orderId: OrderId, contract: Contract, order: Order,
                  orderState: OrderState):
        print('yesssssssss')
        super().openOrder(orderId, contract, order, orderState)
        print("OpenOrder. PermId: ", order.permId, "ClientId:", order.clientId, " OrderId:", orderId,
              "Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
              "Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
              "TotalQty:", order.totalQuantity, "CashQty:", order.cashQty,
              "LmtPrice:", order.lmtPrice, "AuxPrice:", order.auxPrice, "Status:", orderState.status)

        order.contract = contract
        self.permId2ord[order.permId] = order

    def openOrderEnd(self):
        print('yesssssssss')
        super().openOrderEnd()
        print("OpenOrderEnd")
        logging.debug("Received %d openOrders", len(self.permId2ord))

    ### old func
    """
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print("Order Status: ID: %s | Status: %s | Filled: %s | Remaining: %s | LastFill Price: %s "% (orderId, status,filled,remaining,lastFillPrice))
    """

    def nextValidId(self, orderId: int):
        """ Receives next valid order id."""
        super().nextValidId(orderId)

        logging.debug("setting nextValidOrderId: %d", orderId)
        self.nextValidOrderId = orderId
        # ! [nextvalidid]

        # we can start now
        self.start()

    def nextOrderId(self):
        oid = self.nextValidOrderId
        self.nextValidOrderId += 1
        return oid


    def stop(self):
        self.done = True
        # self.reqIds(-1)
        # self.nextValidId(self.nextOrderId)
        # self.disconnect()

    def historicalData(self, reqId: int, bar: BarData):
        print("HistoricalData. ReqId:", reqId, "BarData.", bar)
        bar_str = str(bar).replace("  ", " ")
        bar_splitted = bar_str.split(",") + [self.current_contract_name, '\n']

        self.current_historical_stack.append(",".join(bar_splitted))
        self.logAnswer(current_fn_name(), vars())

    def connect_ran(self, host=HOST_IP, port=PAPER_PORT, clientId=0):
        self.connect(host, port, clientId)

    @iswrapper
    # ! [error]
    def error(self, reqId: TickerId, errorCode: int, errorString: str):
        super().error(reqId, errorCode, errorString)
        print("Error. Id: ", reqId, " Code: ", errorCode, " Msg: ", errorString)

    # ! [error] self.reqId2nErr[reqId] += 1


def initialize_app(host_ip=HOST_IP, paper_trading_port=PAPER_PORT):
    client_id = 0
    if paper_trading_port == 7001:
        client_id = 1
    app = Main_Constructor()  # 4002 for real trading?
    app.connect(host_ip, paper_trading_port, clientId=client_id)
    return app


def get_contract(name, local_name=None):
    contract = Contract()
    if name == 'CORN':
        name = 'ZC'
    elif name == 'COFFEE':
        name = 'KC'
    contract_dict = CONTRACTS_DICT[name]
    contract.symbol = contract_dict["symbol"]  # 'underlying' field
    contract.secType = contract_dict["secType"]
    contract.exchange = contract_dict["exchange"]
    contract.includeExpired = True
    if "ConId" in contract_dict.keys():
        contract.conId = contract_dict['ConId']
        pass
    if 'LocalSymbol' in contract_dict.keys() or local_name:
        local = contract_dict['LocalSymbol'] if not local_name else local_name
        contract.localSymbol = local
    if "primary_exchange" in contract_dict.keys():
        contract.primaryExchange = contract_dict["primary_exchange"]
    contract.currency = contract_dict["currency"]
    return contract


def generate_order(action, order_type, quantity, price=None, good_after_time=""):
    my_order = Order()
    assert order_type in ['LMT', 'MKT']
    if order_type == 'LMT':
        assert price is not None
        my_order.lmtPrice = price
        assert price * quantity <= MAX_MKT_VALUE_FOR_TRADE, 'exceeding the maximal Volume I chose'
    my_order.action = action
    my_order.orderType = order_type  # "LMT"
    my_order.totalQuantity = quantity
    my_order.goodAfterTime = good_after_time
    return my_order


def symbol_lookup():
    app = initialize_app()
    ng_cont = Contract()
    ng_cont.symbol = 'NG'
    ng_cont.currency = 'USD'
    ng_cont.secType = 'FTR'

    # app.reqContractDetails(11,ng_cont)
    app.reqMatchingSymbols(11, "NG")
    app.run()


def _get_prompt_month_name(asset, contract_dt, year):
    month_code = NG_CONTRACT_CODES[contract_dt.strftime("%B")]
    asset_code_name = asset
    if asset == 'CORN':
        asset_code_name = 'ZC'
    elif asset == 'COFFEE':
        asset_code_name = 'KC'
    if asset_code_name not in ['ZC']:
        prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-1:])
    else:
        prompt_month_local_name = '%s   %s %s' % (asset_code_name, contract_dt.strftime("%b").upper(), str(year)[-2:])
    return prompt_month_local_name


def _get_contract_dt(ref_day, asset='NG', delay_next_contract_days=0):
    if asset == 'NG':
        contract_dt = (ref_day + td(days=38)).replace(day=1)
        next_first_of_month = (ref_day + td(days=32 - ref_day.day)).replace(day=1)
        original_last_day_of_contract = next_first_of_month - td(days=DAYS_TO_EXP[asset])
        last_friday_of_contract = original_last_day_of_contract - td(days=original_last_day_of_contract.weekday() + 3)
        if delay_next_contract_days > 0:
            delay_next_contract_days = min(7, delay_next_contract_days)
            last_friday_of_contract += td(days=delay_next_contract_days)
        if last_friday_of_contract < ref_day < original_last_day_of_contract:
            contract_dt += td(days=32)
            contract_dt = contract_dt.replace(day=1)
        aa = 1
    elif asset == 'CORN':
        allowed_months = [3, 5, 7, 9, 12]
        contract_dt = (ref_day + td(days=40)).replace(day=1)
        while contract_dt.month not in allowed_months:
            contract_dt = (contract_dt + td(days=35)).replace(day=1)
    elif asset == 'COFFEE':
        allowed_months = [3, 5, 7, 9, 12]
        contract_dt = (ref_day + td(days=40)).replace(day=1)
        while contract_dt.month not in allowed_months:
            contract_dt = (contract_dt + td(days=35)).replace(day=1)
    else:
        raise AssertionError('invalid asset')
    return contract_dt


def main(year, months=range(1, 13), asset='NG', mode='paper'):
    port = (PAPER_PORT if mode == 'paper' else 4001)
    app = initialize_app(paper_trading_port=port)  # Main_Constructor()
    print('About to retrieve data with MODE = %s | port = %s' % (mode, port))
    host_ip = '127.0.0.1'
    paper_trading_port = 7497  # 4002 real trading  | 7497 paper
    # assert paper_trading_port == 7497, 'We allow use only on paper!'
    # app.connect(host_ip,paper_trading_port,0)

    # https://interactivebrokers.github.io/tws-api/basic_contracts.html#cfd
    # to find details about the desired contract one uses:
    #        tws ---> clocking on the asset ---> cotract description, get attributes  OR
    #                                       ---> financial instrument info --- > description

    corn_contract = Contract()
    corn_contract.symbol = 'CORN'  # 'underlying' field
    corn_contract.secType = 'STK'  # security type
    corn_contract.exchange = 'SMART'
    corn_contract.currency = 'USD'
    corn_contract.primaryExchange = 'ARCA'

    # app.reqContractDetails(10, ng_contract)  # 10 is an id I give to identify my callings
    # for year in [2017,2018,2019]:
    # for month in range(1,13):
    for month in months:  # ,10]: # range(1, 13):
        if asset == 'NG':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\\months\\NG_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'KC':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\COFFEE\Market_Data\\months\\KC_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'ZC':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\CORN\Market_Data\\months\\ZC_%s%s.csv" % (
            year, str(month).zfill(2))
        else:
            raise AssertionError('Do not support assets other than NG / KC / ZC')
        i = 0
        try:
            initial_candles_size = os.stat(outfile).st_size
        except:
            initial_candles_size = 0
        file_cond = lambda file: True if not os.path.exists(outfile) else (os.stat(file).st_size == 0 or
                                                                           os.stat(
                                                                               file).st_size == initial_candles_size)
        while i < 3 and (file_cond(outfile) or i < 1):
            if not app.isConnected():
                app.connect_ran()
            contract_dt = dtdt(year, month, 1)

            if contract_dt < dtdt(2017, 12, 1):
                return
            prompt_month_local_name = _get_prompt_month_name(asset, contract_dt, year)
            app.current_contract_name = contract_dt.strftime("%Y%m")
            days_back = 5 if asset == 'NG' else 2
            ref_date = contract_dt - td(days=days_back)  # todo .replace(day=25)
            ng_contract = get_contract(asset, prompt_month_local_name)

            period = '45 D'
            if asset in ['ZC', 'KC']:
                period = '90 D'
            ugaz_hist = app.reqHistoricalData(101, ng_contract, ref_date.strftime('%Y%m%d %H:%M:%S'),
                                              period, '15 mins', 'TRADES', 0, 1, False, [])
            Timer(5, app.stop).start()
            app.run()
            aa = 1
            a = 1
            lines = app.current_historical_stack
            names = ['date+open', 'high', 'low', 'close', 'vol', 'avg', 'bar_count', 'contract', 'spam']
            new_candles_df = pd.DataFrame([l.split(',') for l in lines])
            try:
                new_candles_df.columns = names
            except:
                aa = 1
            new_candles_df['spam'] = np.nan
            try:
                old_candles_df = pd.read_csv(outfile, names=names)
                final_candles_df = old_candles_df.merge(new_candles_df, on=list(old_candles_df),
                                                        how='outer')
            except:
                final_candles_df = new_candles_df
            if final_candles_df.shape[0] > 10:
                final_candles_df.to_csv(outfile, index=False, header=False)
            app.current_historical_stack = []
            i += 1


if __name__ == '__main__':
    app = initialize_app(paper_trading_port=4001)
    app.run()
    #app.reqAllOpenOrders()
    raise
    for month in [4]:  # 3,5
        main(2021, [month], 'NG')  # 7 3,5
        aa = 1

#####  DRAFT to check contract details
"""
contract = Contract()
contract.symbol = 'KC' #'ZC'
contract.secType = 'FUT'
contract.currency = 'USD'
contract.exchange = 'NYBOT' #'ECBOT'
app.reqContractDetails(11,contract)
"""