# -*- coding: utf-8 -*-
"""
There is a risk of loss when trading stocks, futures, forex, options and other
financial instruments. Please trade with capital you can afford to
lose. Past performance is not necessarily indicative of future results.
Nothing in this computer program/code is intended to be a recommendation, explicitly or implicitly, and/or
solicitation to buy or sell any stocks or futures or options or any securities/financial instruments.
All information and computer programs provided here is for education and
entertainment purpose only; accuracy and thoroughness cannot be guaranteed.
Readers/users are solely responsible for how to use these information and
are solely responsible any consequences of using these information.

If you have any questions, please send <NAME_EMAIL>
All rights reserved.
"""


def initialize(context):
    context.sec1 = superSymbol(secType='OPT', symbol='SPY',
                               currency='USD', exchange='CBOE',
                               primaryExchange='CBOE', expiry='********',
                               strike=300.0, right='C', multiplier='100')


def handle_data(context, data):
    # To get option greeks, data.current or show_real_time_price must be invoked first
    # The feature is not supported by IB's demo account
    # The geeks are provided by IB
    # If the returned values is None, it means IBridgePy has not received the value from IB server.
    # If the returned value is 1.7976931348623157e+308, it means it is an invalid value generated by IB server.
    # User should check the validity of the returned value before using them.
    print(data.current(context.sec1, 'bid_price'))
    print(get_option_info(context.sec1, ['delta', 'gamma', 'vega', 'theta', 'impliedVol']))

    # Do not end here. the greeks may not be available immediately
    # end()





