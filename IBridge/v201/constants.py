from Algo.Utils.yaml_handle import load_trading_conf_yaml

trading_conf = load_trading_conf_yaml()

REAL_ACCOUNT = '********'
PAPER_ACCOUNT = '*********'

assert trading_conf['trading_mode'] in ['real', 'paper']
DEFAULT_ACCOUNT = REAL_ACCOUNT if trading_conf['trading_mode'] == 'real' else PAPER_ACCOUNT
# DEFAULT_ACCOUNT = PAPER_ACCOUNT

# assert DEFAULT_ACCOUNT in [PAPER_ACCOUNT]

NG_CONTRACT_CODES = {'January': 'F', 'February': 'G', 'March': 'H', 'April': 'J',
                     'May': 'K', 'June': 'M', 'July': 'N', 'August': 'Q', 'September': 'U',
                     'October': 'V', 'November': 'X', 'December': 'Z'}

DAYS_TO_EXP = {'NG': 9,'QG':2, 'COFFEE': 30}


CONTRACTS_DICT = {"NG": {"symbol": "NG",
                         "Trading Class": "NG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": ********,
                         "LocalSymbol": "NGZ7"
                         },
                "QG": {"symbol": "QG",
                         "Trading Class": "QG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": ********,
                         "LocalSymbol": "QGZ7"
                         },
                  "NQ": {"symbol": "NQ",
                         "Trading Class": "NQ",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                         # "ConId": ********,
                         "LocalSymbol": "NQZ2"
                         },
                  "MNQ": {"symbol": "MNQ",
                         "Trading Class": "MNQ",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                         # "ConId": ********,
                         "LocalSymbol": "MNQZ2"
                         },
                  "KC": {"symbol": "KC",
                         "Trading Class": "KC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYBOT",
                         # "ConId": ********,
                         "LocalSymbol": "KCZ7"  # Dec Z | Sep U
                         },
                  "UNG":
                      {"symbol": "UNG",
                       "currency": "USD",
                       "secType": "STK",
                       "exchange": "SMART",
                       "primary_exchange": "ARCA"
                       },
                  "ZC":
                      {"symbol": "ZC",
                       "Trading Class": "ZC",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "ECBOT",
                       "LocalSymbol": "ZC   JUL 20"  # "ZCZ7"  # Dec Z | Sep U
                       # "ConID": 291854767

                       },
                "NCF":
                      {"symbol": "NCF",
                       "Trading Class": "NCF",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "NCFN1"
                       # "ConID": 291854767
                       },
                "NGF":
                      {"symbol": "NGF",
                       "Trading Class": "NGF",
                       "currency": "GBP",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "NGFN1"
                       # "ConID": 291854767
                       },
                "WTI":
                      {"symbol": "WTI",
                       "Trading Class": "WTI",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "WTIX1"
                       # "ConID": 291854767
                       }
                  }
