#%%
import os
import sys
sys.path.append(os.path.join(os.getcwd(), '..'))
#%%
from trader_factory import build_active_IBridgePy_plus
#%%
t = build_active_IBridgePy_plus('DU1868499')  # Create an IBridgePy_plus instance
#%%
from IBridgePy.IbridgepyTools import symbol
#%%
# Get historical data of SPY, daily bar, go back 5 days
hist = t.request_historical_data(symbol('SPY'), '1 day', '5 D')
#%%
hist
#%%
type(hist.index[-1])  # The type should be datetime.date
#%%
from tools.hist_converter import convert_hist_using_datetime_to_epoch
#%%
converted = convert_hist_using_datetime_to_epoch(hist)
#%%
type(converted.index[-1])  # The type should be numpy.int64
#%%
converted
#%%
converted.to_csv(os.path.join(os.getcwd(), '..', 'Output', 'myHist.csv'))
#%%
