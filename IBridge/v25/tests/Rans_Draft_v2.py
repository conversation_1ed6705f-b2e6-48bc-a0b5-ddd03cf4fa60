from threading import Timer
from datetime import datetime as dtdt
from datetime import timed<PERSON><PERSON> as td
import os
import pandas as pd
from samples.Python.Testbed.ContractSamples import ContractSamples
import numpy as np
import time

from Algo.Conf.assets import Asset

IBAPI_VERSION_TO_USE = '9.73'

if IBAPI_VERSION_TO_USE == '9.73':
    from source.pythonclient.brokerapi.client import EClient
    from source.pythonclient.brokerapi.wrapper import EWrapper
    from source.pythonclient.brokerapi.common import *
    from source.pythonclient.brokerapi.contract import *
    from source.pythonclient.brokerapi.utils import *
    from source.pythonclient.brokerapi.comm import *
    from source.pythonclient.brokerapi.order import *
    from source.pythonclient.brokerapi.order_state import *
elif IBAPI_VERSION_TO_USE == '9.76':
    from source.pythonclient_976.brokerapi.client import EClient
    from source.pythonclient_976.brokerapi.wrapper import <PERSON><PERSON>rapper
    from source.pythonclient_976.brokerapi.common import *
    from source.pythonclient_976.brokerapi.contract import *
    from source.pythonclient_976.brokerapi.utils import *
    from source.pythonclient_976.brokerapi.comm import *
    from source.pythonclient_976.brokerapi.order import *
    from source.pythonclient_976.brokerapi.order_state import *

import source.pythonclient.brokerapi.decoder
import source.pythonclient.brokerapi.wrapper
from source.pythonclient.brokerapi.ticktype import TickType, TickTypeEnum

from source.pythonclient.brokerapi.message import IN, OUT
from source.pythonclient.brokerapi.connection import Connection
from source.pythonclient.brokerapi.reader import EReader
from source.pythonclient.brokerapi.execution import ExecutionFilter
from source.pythonclient.brokerapi.scanner import ScannerSubscription
from source.pythonclient.brokerapi.order_condition import *

MAX_MKT_VALUE_FOR_TRADE = 2000  # USD
HOST_IP = '127.0.0.1'
PAPER_PORT = 7497


class Main_Constructor(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)
        self.current_historical_stack = []

    def error(self, reqId: TickerId, errorCode: int, errorString: str):
        print('Error: %s %s %s ' % (reqId, errorCode, errorString))

    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        print('Contract details for request # %s: %s' % (reqId, contractDetails))

    # overriding the default that writes to the logger
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print('yesssssssss')
        super().orderStatus(orderId, status, filled, remaining,
                            avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
        print("OrderStatus. Id:", orderId, "Status:", status, "Filled:", filled,
              "Remaining:", remaining, "AvgFillPrice:", avgFillPrice,
              "PermId:", permId, "ParentId:", parentId, "LastFillPrice:",
              lastFillPrice, "ClientId:", clientId, "WhyHeld:",
              whyHeld, "MktCapPrice:", mktCapPrice)

    def openOrder(self, orderId: OrderId, contract: Contract, order: Order,
                           orderState: OrderState):
        super().openOrder(orderId, contract, order, orderState)
        print(" ".join(["OpenOrder. PermId:", order.permId, "ClientId:", order.clientId, " OrderId:", orderId,
               "Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
               "Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
               "TotalQty:", order.totalQuantity, "CashQty:", order.cashQty,
               "LmtPrice:", order.lmtPrice, "AuxPrice:", order.auxPrice, "Status:", orderState.status,
               "MinTradeQty:", order.minTradeQty]))

        order.contract = contract
        #self.permId2ord[order.permId] = order

    def openOrder_old(self, orderId: OrderId, contract: Contract, order: Order,
                  orderState: OrderState):
        super().openOrder(orderId, contract, order, orderState)

        # print("OpenOrder. PermId: ", order.permId, "ClientId:", order.clientId, " OrderId:", orderId,
        #       "Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
        #       "Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
        #       "TotalQty:", order.totalQuantity, "CashQty:", order.cashQty,
        #       "LmtPrice:", order.lmtPrice, "AuxPrice:", order.auxPrice, "Status:", orderState.status)

        order.contract = contract
        #self.permId2ord[order.permId] = order

    def openOrderEnd_old(self):
        print('yesssssssss')
        super().openOrderEnd()
        print("OpenOrderEnd")
        logging.debug("Received %d openOrders", len(self.permId2ord))

    ### old func
    """
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print("Order Status: ID: %s | Status: %s | Filled: %s | Remaining: %s | LastFill Price: %s "% (orderId, status,filled,remaining,lastFillPrice))
    """
    def nextOrderId(self):
        oid = self.nextValidOrderId
        self.nextValidOrderId += 1
        return oid

    def nextValidId(self, orderId: int):
        """ Receives next valid order id."""
        # self.nextOrderId = orderId+1 #todo Ran's hack +1
        # self.logAnswer(current_fn_name(), vars())

        super().nextValidId(orderId)
        logging.debug("setting nextValidOrderId: %d", orderId)
        self.nextValidOrderId = orderId
        print("NextValidId:", orderId)

    def stop(self):
        self.done = True
        # self.reqIds(-1)
        # self.nextValidId(self.nextOrderId)
        # self.disconnect()

    def historicalData(self, reqId: int, bar: BarData):
        print("HistoricalData. ReqId:", reqId, "BarData.", bar)
        bar_str = str(bar).replace("  ", " ")
        bar_splitted = bar_str.split(",") + [self.current_contract_name, '\n']

        self.current_historical_stack.append(",".join(bar_splitted))
        self.logAnswer(current_fn_name(), vars())

    def connect_ran(self, host=HOST_IP, port=PAPER_PORT, clientId=0):
        self.connect(host, port, clientId)


"""
================================================================================================================================================================================================
"""


def initialize_app(host_ip=HOST_IP, paper_trading_port=PAPER_PORT):
    client_id = 0
    if paper_trading_port == 7001:
        client_id = 1
    app = Main_Constructor()  # 4002 for real trading?
    app.disconnect()
    app.connect(host_ip, paper_trading_port, clientId=client_id)
    return app


def generate_order(action, order_type, quantity, price=None, good_after_time=""): # todo delete?
    my_order = Order()
    assert order_type in ['LMT', 'MKT']
    if order_type == 'LMT':
        assert price is not None
        my_order.lmtPrice = price
        assert price * quantity <= MAX_MKT_VALUE_FOR_TRADE, 'exceeding the maximal Volume I chose'
    my_order.action = action
    my_order.orderType = order_type  # "LMT"
    my_order.totalQuantity = quantity
    my_order.goodAfterTime = good_after_time
    return my_order


def symbol_lookup():
    app = initialize_app()
    ng_cont = Contract()
    ng_cont.symbol = 'NG'
    ng_cont.currency = 'USD'
    ng_cont.secType = 'FTR'

    # app.reqContractDetails(11,ng_cont)
    app.reqMatchingSymbols(11, "NG")
    app.run()


def main(year, months=range(1, 13), asset_name='NG', mode='paper',
         resolution = '15 mins',app=None,port_override=None):

    # initialize asset
    asset = Asset(asset_name,resolution.strip(' ').replace('mins','m'))

    port = (PAPER_PORT if mode == 'paper' else 4001)
    if port_override is not None:
        port = port_override

    if app is None:
        app = initialize_app(paper_trading_port=port)  # Main_Constructor()
    time.sleep(2)

    if not set(months) <= set(asset.allowed_months):
        print('required months exceed the allowed months...will drop the irrelevant')
        months = [m for m in months if m in asset.allowed_months]

    for month in months:
        i = 0
        outfile = asset.get_monthly_data_outpath(year,month)
        try:
            initial_candles_size = os.stat(outfile).st_size
        except:
            initial_candles_size = 0
        file_cond = lambda file: True if not os.path.exists(outfile) else (os.stat(file).st_size == 0 or
                                                                           os.stat(
                                                                               file).st_size == initial_candles_size)
        while i < 3 and (file_cond(outfile) or i < 1):
            time.sleep(1.5)
            if not app.isConnected():
                app.connect_ran()
            contract_dt = dtdt(year, month, 1)

            if contract_dt < dtdt(2017, 12, 1):
                return
            prompt_month_local_name = asset._get_prompt_month_name(contract_dt, year)
            app.current_contract_name = contract_dt.strftime("%Y%m")

            ref_date = contract_dt - td(days=asset.days_back_candles)
            ng_contract = asset.get_contract(prompt_month_local_name)

            ugaz_hist = app.reqHistoricalData(101, ng_contract, ref_date.strftime('%Y%m%d %H:%M:%S'),
                                              asset.candles_period, resolution, 'TRADES', 0, 1, False, [])
            Timer(5, app.stop).start()
            app.run()
            lines = app.current_historical_stack
            names = ['date+open', 'high', 'low', 'close', 'vol', 'avg', 'bar_count', 'contract', 'spam']
            new_candles_df = pd.DataFrame([l.split(',') for l in lines])
            new_candles_df.columns = names
            new_candles_df['spam'] = np.nan
            try:
                old_candles_df = pd.read_csv(outfile, names=names)
                for c in list(new_candles_df)[1:]:
                    new_candles_df[c] = pd.to_numeric(new_candles_df[c])
                final_candles_df = old_candles_df.merge(new_candles_df, on=list(old_candles_df),
                                                        how='outer')
            except:
                final_candles_df = new_candles_df
            if final_candles_df.shape[0] > 10:
                final_candles_df = final_candles_df.drop_duplicates()
                final_candles_df.to_csv(outfile, index=False, header=False)
            app.current_historical_stack = []
            i += 1
    return app

if __name__ == '__main__':
    for month in [1]: #[8,9,10,11]:
        main(2022, [month], 'NG', mode='real',resolution='5 mins')  # 7 3,5
        aa = 1
