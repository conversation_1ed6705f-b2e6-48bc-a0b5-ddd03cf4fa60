import numpy as np

# noinspection PyUnresolvedReferences
from BasicPyLib.BasicTools import roundToMinTick, get_system_info
from Config.config_defs import UserConfig
# noinspection PyUnresolvedReferences
from IBridgePy.IbridgepyTools import superSymbol, check_user_sys_compatibility
from IBridgePy.MarketManagerBase import MarketManager, setup_services, setup_backtest_account_init_info
# noinspection PyUnresolvedReferences
from IBridgePy.OrderTypes import MarketOrder, StopOrder, LimitOrder, StopLimitOrder, TrailStopLimitOrder, \
    TrailStopOrder, MarketOnCloseOrder
from IBridgePy.Trader import Trader
from IBridgePy.constants import LiveBacktest, DataProviderName, LogLevel, TraderRunMode
# noinspection PyUnresolvedReferences
from IBridgePy.quantopian import date_rules, time_rules, calendars
from IBridgePy.quantopian import from_contract_to_security, from_security_to_contract
import pickle

from broker_client_factory.CustomErrors import CustomError
from broker_client_factory.CustomErrors import ErrorCode
from configuration import _build_config,_setup_dataProviderService
from v18.constants import DEFAULT_ACCOUNT, NG_CONTRACT_CODES
from Algo.Conf.assets import CONTRACTS_DICT
from Algo.Utils.timezone_definitions import LOCAL_TZ

from IBridgePy.quantopian import Security
import time
from datetime import datetime as dtdt
from datetime import timedelta as td
#check_user_sys_compatibility()

import pandas as pd
import os

from Algo.Utils.general import get_utc_now_dt
from Algo.Utils.files_handle import ACTUAL_TRADES_CSV, ORDER_IDS_CSV, IBRIDGEPY_TRADER_OBJECT_PICKLE
import pytz
from Algo.Utils.files_handle import HOME, PROJECT_ROOT
import json

from Algo.Utils.yaml_handle import load_trading_conf_yaml
from trader_factory import build_active_TD_trader, build_active_IBridgePy_plus

def initialize_trader_old():
    conf = initialize_config()
    trader = conf.trader
    return trader

def initialize_trader_v2():
    conf = load_trading_conf_yaml()
    if conf['trading_mode'] == 'real':
        trader = build_active_IBridgePy_plus('********')
    elif conf['trading_mode'] == 'paper':
        trader = build_active_IBridgePy_plus('*********')
    else:
        raise ValueError('trading_mode must be real or paper')
    return trader

def initialize_trader():
    return initialize_trader_v2()

def initialize_config_v2(userConfig=None):
    if userConfig is None:
        userConfig = UserConfig.get_config('IB')
    userConfig = _build_config(userConfig, None, None)
    if userConfig.projectConfig.brokerServiceName != userConfig.projectConfig.dataProviderName:
        print('!!! Data provider is %s but the broker service is %s. They are NOT same. !!!' % (userConfig.projectConfig.dataProviderName, userConfig.projectConfig.brokerServiceName))
        userConfig = _setup_dataProviderService(userConfig)
    return userConfig


def initialize_config(userConfig=None, autoReconnectPremium=False,
                      account_code=DEFAULT_ACCOUNT):
    global accountCode
    accountCode = account_code
    restart = None
    autoReconnectPremium = autoReconnectPremium
    while True:
        try:
            if not userConfig or restart:
                userConfig = UserConfig.get_config('IB')
            userConfig = _build_config(userConfig, globals(), 'example_show_positions.py')
            if userConfig.projectConfig.brokerServiceName != userConfig.projectConfig.dataProviderName:
                print('!!! Data provider is %s but the broker service is %s. They are NOT same. !!!' % (
                userConfig.projectConfig.dataProviderName, userConfig.projectConfig.brokerServiceName))
                userConfig = _setup_dataProviderService(userConfig)
            autoReconnectPremium = userConfig.projectConfig.autoReconnectPremium
            break
        except CustomError as e:
            if e.error_code in [509, 504, 502, 10141, ErrorCode.ERR_LOST_HEART_BEAT.code] and autoReconnectPremium:
                print('IBridgePy will sleep 10 seconds and reconnect again.')
                restart = True
                time.sleep(10)
            else:
                raise e
    return userConfig

def _parse_order(order_dict):
    id = order_dict._ibpyOrderId
    try:
        contract = order_dict.openOrderRecord.contract.get('symbol')
    except:
        print('WARNING, couldnt get contract for order %s'%id)
        contract = None
    try:
        action = order_dict.openOrderRecord.order.get('action')
    except:
        print('WARNING, couldnt get action for order %s'%id)
        action = None
    try:
        t_open = order_dict.openOrderRecord.order.get('goodAfterTime')
    except:
        print('WARNING, couldnt get t_open for order %s'%id)
        t_open = None
    try:
        order_type = order_dict.openOrderRecord.order.get('orderType')
    except:
        print('WARNING, couldnt get order_type for order %s'%id)
        order_type = None
    try:
        quantity = order_dict.openOrderRecord.order.get('totalQuantity')
    except:
        print('WARNING, couldnt get quantity for order %s'%id)
        quantity = None
    return {
            'id': id,
            'contract': contract,
            'action': action,
            't_open': t_open,
            'order_type': order_type,
            'quantity': quantity,
        }
def get_open_orders(trader=None):
    if trader is None:
        trader = initialize_trader()
        # trader.connect()
    open_orders_lst = {}
    tries = 0
    while tries < 10 and len(open_orders_lst)==0:
        open_orders_lst = trader.get_all_open_orders()
        time.sleep(1)
        tries += 1
    if len(open_orders_lst) > 0:
        open_orders_stack = [_parse_order(order_dict)
            for order_id, order_dict in open_orders_lst.items()]
    else:
        print('WARNING, didnt find any open orders')
        return []
    return open_orders_stack

def _get_prompt_month_name(asset, contract_dt, year):
    month_code = NG_CONTRACT_CODES[contract_dt.strftime("%B")]
    asset_code_name = asset
    if asset == 'CORN':
        asset_code_name = 'ZC'
    elif asset == 'COFFEE':
        asset_code_name = 'KC'

    if asset_code_name in ['ZC']:
        if contract_dt < dtdt(2024, 7, 1):
            prompt_month_local_name = '%s   %s %s' % (asset_code_name, contract_dt.strftime("%b").upper(), str(year)[-2:])
        else:
            prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-1:])
    elif asset_code_name in ['NG','QG','MHNG']:
        if contract_dt < dtdt(2025,7,1):
            prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-1:])
        else:
            prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-2:])
    else:
        prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-1:])

    print('Contract Code:',prompt_month_local_name)

    return prompt_month_local_name


def create_security(contract_dt,asset='NG',include_expired=False):
    contract_conf = CONTRACTS_DICT[asset]
    if contract_dt is not None:
        local_symbol = _get_prompt_month_name(asset,contract_dt,contract_dt.year)
        security = Security(secType=contract_conf['secType'], symbol=contract_conf['symbol'],
                            currency=contract_conf['currency'], exchange=contract_conf['exchange'],
                            localSymbol=local_symbol,includeExpired=include_expired)
    else:
        security = Security(secType=contract_conf['secType'], symbol=contract_conf['symbol'],
                            currency=contract_conf['currency'], exchange=contract_conf['exchange'],
                            includeExpired=include_expired)
    return security

def place_order(asset,contract_dt,quantity,
                start_time=None,tz='UTC',trader=None):
    """
    :param asset: 'QG' / 'NG'
    :param contract_dt: dtdt
    :param quantity: int
    :param start_time: '20221007 12:30:00'
    :param trader: trader object or None
    :param tz: 'UTC / ''
    :return:
    """
    if trader is None:
        trader = initialize_trader()
    # orders = get_open_orders(conf)
    if start_time is not None:
        start_time_str = f'{start_time} {tz}'
    else:
        start_time_str = None
    cont = create_security(contract_dt, asset)
    order_id = trader.order(cont, quantity, start_time=start_time_str)
    return order_id

def _try_to_place(trader, cont, quantity,time_str):
    tries = 0
    success = False
    while tries < 5 and not success:
        time.sleep(2.5)
        try:
            order_id = trader.order(cont, quantity, start_time=time_str)
            trader.order_status_monitor(order_id, target_status='PreSubmitted')
            success = True
        except Exception as e:
            print(f'Failed to place order {cont}x{time_str} on the {tries} try with error {e}')
            tries += 1
            continue
    return trader

def place_position(asset,contract_dt,quantity,
                    strategy,direction,order_type='MKT',
                        tz='UTC',trader=None,
                   trade_date=None,
                   hours_shift=0,skip_closing_trade=False,
                   add_noise_to_entry_time=True,entry_takeback_in_seconds=0,):
    """
    :param asset: 'QG' / 'NG'
    :param contract_dt: dtdt
    :param quantity: int
    :param start_time: '20221007 12:30:00'
    :param trader: trader object or None
    :param tz: 'UTC / ''
    :param trade_date: None -- > use current utc date, else override it (for future trades)
    :param hours_shift: int, shift the trade by X hours
    :param add_noise_to_entry_time: bool, if True, will randomize entry somewhere in first 10 seconds
    :param entry_takeback_in_seconds: int, if > 0, will take back the entry time by X seconds
    :return:
    """
    if trader is None:
        trader = initialize_trader()

    utc_now_naive = get_utc_now_dt()
    utc_now = utc_now_naive + td(hours=hours_shift)
    if add_noise_to_entry_time:
        t_open_second = utc_now.second % 10
        t_close_second = utc_now.microsecond % 10
    else:
        t_open_second = 0
        t_close_second = 0
    if trade_date is None:
        trade_date = utc_now.strftime('%Y%m%d')

    assert quantity >= 0
    quantity_open = quantity * (-1 if direction == 'SELL' else 1)
    quantity_close = quantity * (-1 if direction == 'BUY' else 1)

    t_open_str = f"{trade_date} {strategy.split('_')[-1].split('-')[0][:2]}:{strategy.split('_')[-1].split('-')[0][2:]}:{str(t_open_second).zfill(2)}{' '+tz}"
    t_close_str = f"{trade_date} {strategy.split('_')[-1].split('-')[1][:2]}:{strategy.split('_')[-1].split('-')[1][2:]}:{str(t_close_second).zfill(2)}{' '+tz}"
    t_open_dt_local = dtdt.strptime(' '.join(t_open_str.split(' ')[:-1]),'%Y%m%d %H:%M:%S')
    t_close_dt_local = dtdt.strptime(' '.join(t_close_str.split(' ')[:-1]),'%Y%m%d %H:%M:%S')

    if tz != 'UTC':
        tz_obj = pytz.timezone(tz)
        t_open_dt = tz_obj.localize(t_open_dt_local).astimezone(pytz.utc).replace(tzinfo=None)
        t_close_dt = tz_obj.localize(t_close_dt_local).astimezone(pytz.utc).replace(tzinfo=None)
    else:
        t_open_dt = t_open_dt_local
        t_close_dt = t_close_dt_local

    tz2 = 'UTC'

    if entry_takeback_in_seconds:
        t_open_dt = t_open_dt - td(seconds=entry_takeback_in_seconds)
        t_close_dt = t_close_dt - td(seconds=entry_takeback_in_seconds)

    if t_close_dt < t_open_dt:
        t_close_dt = t_close_dt + td(days=1)

    t_open_dt = max(utc_now_naive+td(minutes=2), t_open_dt)
    if t_close_dt < utc_now_naive + td(minutes=5) or abs((t_open_dt - t_close_dt).total_seconds()) < 60*6:
        print(f't_open, t_close were in the past or too close, skipping strategy {strategy}')
        return trader, []

    t_open_str = f"{t_open_dt.strftime('%Y%m%d %H:%M:%S')}{' ' + tz2}"
    t_close_str = f"{t_close_dt.strftime('%Y%m%d %H:%M:%S')}{' ' + tz2}"

    cont = create_security(contract_dt, asset)
    # open_order_id = trader.order(cont, quantity_open,
    #                              start_time=t_open_str
    #                              )
    # trader.order_status_monitor(open_order_id, target_status='PreSubmitted')
    trader = _try_to_place(trader,cont, quantity_open, t_open_str)
    if not skip_closing_trade:
        trader = _try_to_place(trader,cont, quantity_close, t_close_str)

    return trader, []


def place_one_way_position(asset,contract_dt,quantity,
                    trade_datetime,direction,
                           order_type='MKT',
                        tz='UTC',trader=None,
                   hours_shift=0,skip_closing_trade=False,
                   add_noise_to_entry_time=True,entry_takeback_in_seconds=0):
    """

    :param asset:
    :param contract_dt:
    :param quantity:
    :param trade_datetime: %Y%m%d %H%M
    :param direction:
    :param order_type:
    :param tz:
    :param trader:
    :param hours_shift:
    :param skip_closing_trade:
    :param add_noise_to_entry_time:
    :param entry_takeback_in_seconds:
    :return:
    """
    if trader is None:
        trader = initialize_trader()

    utc_now = get_utc_now_dt()+td(hours=hours_shift)
    if add_noise_to_entry_time:
        t_open_second = utc_now.second % 10
    else:
        t_open_second = 0

    assert quantity >= 0
    quantity_open = quantity * (-1 if direction == 'SELL' else 1)

    t_open_dt = dtdt.strptime(trade_datetime,'%Y%m%d %H:%M')
    t_open_dt = t_open_dt.replace(second=t_open_second)
    if entry_takeback_in_seconds:
        t_open_dt = t_open_dt - td(seconds=entry_takeback_in_seconds)

    t_open_dt = max(utc_now+td(minutes=2), t_open_dt)
    t_open_str = f"{t_open_dt.strftime('%Y%m%d %H:%M:%S')}{' ' + tz}"

    cont = create_security(contract_dt, asset)
    trader = _try_to_place(trader,cont, quantity_open, t_open_str)
    return trader

def place_position_old(asset,contract_dt,quantity,
                    strategy,direction,order_type='MKT',
                        tz='UTC',trader=None,
                   trade_date=None,
                   hours_shift=0,skip_closing_trade=False,
                   add_noise_to_entry_time=True,entry_takeback_in_seconds=0):
    """
    :param asset: 'QG' / 'NG'
    :param contract_dt: dtdt
    :param quantity: int
    :param start_time: '20221007 12:30:00'
    :param trader: trader object or None
    :param tz: 'UTC / ''
    :param trade_date: None -- > use current utc date, else override it (for future trades)
    :param hours_shift: int, shift the trade by X hours
    :param add_noise_to_entry_time: bool, if True, will randomize entry somewhere in first 10 seconds
    :param entry_takeback_in_seconds: int, if > 0, will take back the entry time by X seconds
    :return:
    """
    if trader is None:
        trader = initialize_trader()

    utc_now = get_utc_now_dt()+td(hours=hours_shift)
    if add_noise_to_entry_time:
        t_open_second = utc_now.second % 10
        t_close_second = utc_now.microsecond % 10
    else:
        t_open_second = 0
        t_close_second = 0
    if trade_date is None:
        trade_date = utc_now.strftime('%Y%m%d')

    assert quantity >= 0
    quantity_open = quantity * (-1 if direction == 'SELL' else 1)
    quantity_close = quantity * (-1 if direction == 'BUY' else 1)

    t_open_str = f"{trade_date} {strategy.split('_')[-1].split('-')[0][:2]}:{strategy.split('_')[-1].split('-')[0][2:]}:{str(t_open_second).zfill(2)}{' '+tz}"
    t_close_str = f"{trade_date} {strategy.split('_')[-1].split('-')[1][:2]}:{strategy.split('_')[-1].split('-')[1][2:]}:{str(t_close_second).zfill(2)}{' '+tz}"
    t_open_dt = dtdt.strptime(' '.join(t_open_str.split(' ')[:-1]),'%Y%m%d %H:%M:%S')
    t_close_dt = dtdt.strptime(' '.join(t_close_str.split(' ')[:-1]),'%Y%m%d %H:%M:%S')

    if entry_takeback_in_seconds:
        t_open_dt = t_open_dt - td(seconds=entry_takeback_in_seconds)
        t_close_dt = t_close_dt - td(seconds=entry_takeback_in_seconds)

    if t_close_dt < t_open_dt:
        t_close_dt = t_close_dt + td(days=1)

    t_open_dt = max(utc_now+td(minutes=2), t_open_dt)
    if t_close_dt < utc_now + td(minutes=5) or abs((t_open_dt - t_close_dt).total_seconds()) < 60*6:
        print(f't_open, t_close were in the past or too close, skipping strategy {strategy}')
        return trader, []

    t_open_str = f"{t_open_dt.strftime('%Y%m%d %H:%M:%S')}{' ' + tz}"
    t_close_str = f"{t_close_dt.strftime('%Y%m%d %H:%M:%S')}{' ' + tz}"

    cont = create_security(contract_dt, asset)


    failure = 'open'
    try:
        try:
            open_order_id = trader.order(cont, quantity_open,
                                         start_time=t_open_str
                                         )
            failure = 'close'
            time.sleep(1.5)
            try:
                trader.initialize_Function()
            except:
                bb = 0
            #trader.disconnect()
            #trader = initialize_config().trader
            if not skip_closing_trade:
                close_order_id = trader.order(cont, quantity_close, start_time=t_close_str)
        except:
            raise
            failure2 = 'open'
            try:
                raise # todo
                last_order_id = pd.DataFrame(get_open_orders(trader))['id'].max()
                if failure == 'open':
                    open_order_id = trader.order(cont, quantity_open, start_time=t_open_str,
                                                 order_id_override=int(last_order_id.split('ib')[1]) + 15)
                    failure2 = 'close'
                if not skip_closing_trade:
                    close_order_id = trader.order(cont, quantity_close, start_time=t_close_str,
                                              order_id_override=int(last_order_id.split('ib')[1]) + 20)
            except Exception as e:

                last_order_id = pd.read_csv(ORDER_IDS_CSV)['id'].max()
                if failure2 == 'open' and failure == 'open':
                    open_order_id = trader.order(cont, quantity_open, start_time=t_open_str,
                                             order_id_override=int(last_order_id.split('ib')[1]) + 30)
                if not skip_closing_trade:
                    close_order_id = trader.order(cont, quantity_close, start_time=t_close_str,
                                      order_id_override=int(last_order_id.split('ib')[1]) + 40)
        time.sleep(1)
        orders_list = [{'id':open_order_id,'t_open':t_open_str,'quantity':quantity_open},]
        if not skip_closing_trade:
            orders_list += [{'id':close_order_id,'t_open':t_close_str,'quantity':quantity_close},]
        return trader, orders_list
    except Exception as e:
        return trader, ['error',e]
def wrap_get_contract_details(asset='NQ'):
    trader = None
    for field in ['secType', 'localSymbol','symbol', 'primaryExchange', 'exchange', 'currency',
                  'Trading Class',]:
        ans, trader = get_contract_details(asset, field, trader)
        print(f'{field} = {ans}')
    bb = 0

def get_contract_details(asset='NQ',field='currency',trader=None):
    if trader is None:
        trader = initialize_trader()
    details = trader.get_contract_details(symbol=asset,field=field,secType='FUT')
    return details, trader

def update_ib_orders_csv(orders_stack):
    existing_df = pd.DataFrame()
    if os.path.exists(ORDER_IDS_CSV):
        try:
            existing_df = pd.read_csv(ORDER_IDS_CSV)
        except:
            pass

    new_df = pd.DataFrame(orders_stack)
    if new_df.shape[0] == 0:
        return
    elif existing_df.shape[0] != 0 and new_df.shape[0] !=0:
        new_df = existing_df.merge(new_df,on=list(existing_df),how='outer')
        new_df.to_csv(ORDER_IDS_CSV,index=False)


def cancel_order(order_id,trader=None):
    if trader is None:
        trader = initialize_trader()
    trader.cancel_order(order_id)
    # trader.cancelOrder(order_id)


    # from models.Order import IbridgePyOrder
    # ibridgepy_order = IbridgePyOrder(ibpyOrderId=order_id[2:])
    # trader.cancel_order(ibridgepy_order)


def modify_order(order_id,new_quantity=None,new_start_time=None,
                 trader=None):
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    trader.modify_order(order_id, newQuantity=new_quantity,newGoodAfterTime=new_start_time)

def parse_ib_timestamp_from_str(timestamp,date_format = '%Y%m%d %H:%M:%S'):
    """
    :param timestamp:
    :return: time in UTC
    """

    # Extract the timezone from the input string
    timezone_string = timestamp.split()[-1]
    # Create a timezone object
    timezone = pytz.timezone(timezone_string)
    # Parse the datetime string without timezone information
    date_object_naive = dtdt.strptime(timestamp[:-len(timezone_string)].strip(), date_format)
    # Convert to the desired timezone
    date_object = timezone.localize(date_object_naive, is_dst=None).astimezone(pytz.utc)

    return date_object


def wrap_get_open_orders(trader=None,disconnect_after=False):
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    open_orders = []
    tries = 0
    while len(open_orders) == 0 and tries < 3:
        open_orders = get_open_orders(trader)
        time.sleep(1.5)
        tries += 1
    open_orders_df = pd.DataFrame(open_orders)
    if disconnect_after:
        trader.disconnect()
    return open_orders_df


def parse_positions_from_orders(orders_df):
    """
    assuming timestamps are parsed
    :param orders_df:
    :return:
    """
    if orders_df.shape[0] == 0:
        orders_df = pd.DataFrame({'t_open':[dtdt.today().replace(hour=0,minute=0,second=0,microsecond=0)],
                                  'contract':'NG','quantity':0,'action':'BUY'})
        date = orders_df['t_open'].iloc[0]
    else:
        if type(orders_df['t_open'].iloc[0]) == str:
            orders_df['t_open'] = orders_df['t_open'].apply(parse_ib_timestamp_from_str)
        orders_df = orders_df.sort_values('t_open')
        date = orders_df['t_open'].iloc[0] - td(minutes=1)
        date = date - td(hours=date.hour, minutes=date.minute, seconds=date.second, microseconds=date.microsecond)
    # Group orders by contract
    grouped = orders_df.groupby('contract')
    # Dictionary to store the position size for each 15-minute period for each contract

    # Generate a DatetimeIndex with a frequency of 15 minutes
    dt_index = pd.date_range(date, periods=96, freq='15T')
    # Convert the DatetimeIndex to a dataframe with a single column 't_open'
    ref_df = pd.DataFrame({'t_open': dt_index})

    # Loop over each group (contract)
    for contract, group in grouped:
        # Store the result in the dictionary
        group = group.sort_values('t_open')
        group['t_open'] = group['t_open'].apply(lambda x: x - td(minutes=x.minute%15, seconds=x.second))
        group['quantity_change'] = group['quantity'] * (group['action'] == 'BUY').astype(int).replace(0,-1)
        group = group.groupby('t_open').sum().reset_index()
        group['{}_quantity_final'.format(contract)] = group['quantity_change'].cumsum()
        ref_df = ref_df.merge(group[['t_open','{}_quantity_final'.format(contract)]],on='t_open',how='outer').sort_values('t_open')
        ref_df['{}_quantity_final'.format(contract)] = ref_df['{}_quantity_final'.format(contract)].fillna(method='ffill').fillna(0)
    # Convert the dictionary to a dataframe
    return ref_df

def get_positions_from_active_orders(trader=None):
    open_orders_df = wrap_get_open_orders(trader)
    try:
        if open_orders_df.shape[0] > 0:
            open_orders_df = open_orders_df.query('action.isin(["BUY","SELL"])')
            open_orders_df['quantity'] = pd.to_numeric(open_orders_df['quantity'])
        orders_positions_df = parse_positions_from_orders(open_orders_df)
        return orders_positions_df, trader
    except Exception as e:
        print('WARNING, couldnt parse open orders due to Error: ',e)
        return pd.DataFrame(), trader

def get_total_positions(trader=None,contract_name='NG',
                        disconnect_after=False):
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    orders_positions_df,trader = get_positions_from_active_orders(trader)
    current_position = get_specific_contract_positions(contract_name,trader=trader)
    if current_position != 0:
        utc_now = get_utc_now_dt()
        quantity_col = [x for x in list(orders_positions_df.columns) if contract_name in x][0]
        orders_positions_df.loc[orders_positions_df.t_open.dt.tz_localize(None)>=utc_now-td(minutes=utc_now.minute%15+1),quantity_col] += current_position
    if disconnect_after:
        trader.disconnect()
    return orders_positions_df,trader

def cancel_all_open_orders(trader=None):
    open_orders_df = wrap_get_open_orders(trader)
    for id in open_orders_df['id'].tolist():
        cancel_order(id,trader)

def get_all_positions(trader=None):
    """
    returns a dict with localSymbol: (Contract_obj, positions_size)
    :param trader:
    :return:
    """
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    positions = trader.get_all_positions()
    new_positions = {}
    for security_name, security in positions.items():
        new_key = security.contract.symbol
        new_positions[new_key] = (security.contract, security.amount)
    return new_positions

def get_specific_contract_positions(contract_name,trader=None):
    """
    returns an integer with position size for the given contract name
    :param trader:
    :return:
    """
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    positions = trader.get_all_positions()
    for security_name, security in positions.items():
        new_key = security.contract.get('symbol')
        if new_key == contract_name:
            return float(security.amount)
    return 0

def get_positions_by_symbol(local_symbol,trader=None):
    """
    returns a dict with localSymbol: (Contract_obj, positions_size)
    :param trader:
    :return:
    """
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    positions = trader.get_all_positions()
    for security_name, security in positions.items():
        new_key = security_name.localSymbol
        if new_key == local_symbol:
            return trader, (security.contract, security.amount)
    return trader, (None,None)

def close_all_positions_by_symbol(local_symbol,quantity_limit=2):
    trader, (contract, quantity) = get_positions_by_symbol(local_symbol)
    security = from_contract_to_security(contract)

    quantity = np.clip(quantity,-quantity_limit,quantity_limit)
    last_order_id = pd.read_csv(ORDER_IDS_CSV)['id'].max()
    t_open_str = get_utc_now_dt().strftime('%Y%m%d %H:%M:%S UTC')
    try:
        open_order_id = trader.order(security, -quantity,start_time=t_open_str, order_id_override=int(last_order_id.split('ib')[1])+5)
        orders_stack = [{'id': open_order_id, 't_open': t_open_str, 'quantity': -quantity}]
        update_ib_orders_csv(orders_stack)
    except Exception as e:
        if 'This order will be directly routed to ISLAND' in str(e):
            print('Got warning regarding trading hour...ignoring it')
        else:
            raise e



def cancel_contradicting_orders(trader=None,iters=2):
    """
    run on open orders, choose first contradiction of a given t_open and cancel/moodift according to the quantity
    :param trader:
    :return:
    """
    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()
    for i in range(iters):
        open_orders_df = pd.DataFrame(get_open_orders(trader))
        open_orders_df = open_orders_df.dropna(subset=['contract'])
        try:
            open_orders_df['t_open_clean'] = pd.to_datetime(open_orders_df['t_open'].apply(lambda x: ' '.join(x.split(' ')[:-1]))).apply(lambda x: x.replace(second=0))
        except:
            print(f'couldnt parse open orders, skipping iteration {i}')
            continue
        for t_open, group in open_orders_df.groupby('t_open_clean'):
            if set(group['action'].unique().tolist()) == {'BUY','SELL'}:
                group = group.sort_values('quantity')
                first_buy = group[group['action']=='BUY'].iloc[0]
                first_sell = group[group['action']=='SELL'].iloc[0]
                is_same_quantity = first_buy['quantity'] == first_sell['quantity']
                for order in [first_buy, first_sell]:
                    if order['quantity'] in [1,'1'] or is_same_quantity:
                        #cancel_order(order['id'],trader=trader)
                        cancel_order(order['id'], trader=trader)
                    else:
                        modify_order(order['id'],new_quantity=int(order['quantity'])-1,trader=trader)
    return trader

def get_historical_data(asset,contract_dt,resolution,period,ref_date,trader=None,
                        candles_tzname='Asia/Jerusalem',old_format=True):
    assert candles_tzname in ['Asia/Jerusalem','UTC']

    if trader is None:
        trader = initialize_trader()
        # trader.connect() # todo droppped from v19 on
        # trader.initialize_Function()

    cont = create_security(contract_dt, asset,include_expired=True)
    df = trader.request_historical_data(cont,resolution,period,endTime=ref_date,
                                        timezoneOfReturn=pytz.UTC if candles_tzname=='UTC' else pytz.timezone(candles_tzname),
                                        useRTH=0).reset_index().drop('index',axis=1)
    df = df.rename(columns={'timestamp':'date'})
    if old_format:
        df['date+open'] = df['date'].dt.strftime('%Y%m%d %H:%M:%S')+':'+ df['open'].astype(str)
        df['avg'] = np.nan
        df['bar_count'] = 0
        df['contract'] = contract_dt.strftime('%Y%m')
        df = df[['date+open', 'high', 'low', 'close', 'volume','avg', 'bar_count', 'contract']]
    else:
        df = df[['date', 'open', 'high', 'low', 'close', 'volume']]
    return trader, df

def dump_trader_obj_to_pkl(trader, path=IBRIDGEPY_TRADER_OBJECT_PICKLE):
    with open(path,'w') as f:
        pickle.dump(trader,f)

def load_trader_obj_to_pkl(path=IBRIDGEPY_TRADER_OBJECT_PICKLE):
    with open(path) as f:
        trader = pickle.load(f)
    return trader


def get_this_weeks_monday():
    today = dtdt.now()+td(hours=12)
    if today.weekday() == 0:
        return today
    else:
        return today - td(days=today.weekday())

def update_nasdaq_json(trading_conf,monday_override=None):
    json_path = os.path.join(PROJECT_ROOT, 'Algo', 'Conf', 'nasdaq_trading_confs.json')
    if not os.path.exists(json_path):
        json.dump({}, open(json_path, 'w'))
    current_json = json.load(open(json_path))

    this_weeks_monday = get_this_weeks_monday().strftime('%Y%m%d')
    if monday_override is not None:
        this_weeks_monday = monday_override
    # if this_weeks_monday not in current_json.keys():
    current_json[this_weeks_monday] = trading_conf
    json.dump(current_json, open(json_path, 'w'))

def get_nyc_open_in_utc(weekday):
    last_monday = get_this_weeks_monday()
    nyc_open = last_monday.replace(hour=9,minute=30,second=18,microsecond=0)
    nyc_open = nyc_open + td(days=weekday)
    # localize
    nyc_open = pytz.timezone('America/New_York').localize(nyc_open)
    return nyc_open.astimezone(pytz.utc).replace(tzinfo=None).strftime('%Y%m%d %H:%M')

def get_nyc_close_in_utc(weekday):
    last_monday = get_this_weeks_monday()
    nyc_close = last_monday.replace(hour=16,minute=0,second=18,microsecond=0)
    nyc_close = nyc_close + td(days=weekday,minutes=-5)
    # localize
    nyc_close = pytz.timezone('America/New_York').localize(nyc_close)
    return nyc_close.astimezone(pytz.utc).replace(tzinfo=None).strftime('%Y%m%d %H:%M')

def wrap_root_positions(quantity=80,trader=None):
    conf = {
            # 0: ('open','close'),
            # 1: (None,None),
            # 2: ('open','open'),
            # 3: ('open','close'),
            4: ('open','close'),
            }
    last_monday = get_this_weeks_monday()
    todays_weekday = dtdt.now().weekday()

    def choose_time_func(open_or_close):
        if open_or_close == 'open':
            return get_nyc_open_in_utc
        elif open_or_close == 'close':
            return get_nyc_close_in_utc

    naive_trades_tuples = [[(choose_time_func(open_t)(weekday),'BUY'),
                            (choose_time_func(close_t)(weekday+1*(close_t=='open')),'SELL')
                            ]
                           for weekday, (open_t,close_t) in
        conf.items() if weekday >= todays_weekday and open_t is not None]
    flattened_trades_tuples = [item for sublist in naive_trades_tuples for item in sublist]
    # check for (t,action) tuples where with same t but different actions
    trades_per_time = {}
    for i, (t,action) in enumerate(flattened_trades_tuples):
        if pd.to_datetime(t) < get_utc_now_dt():
            print(f'skipping trade at {t} due to being in the past')
            continue
        if t not in trades_per_time.keys():
            trades_per_time[t] = []
        trades_per_time[t].append(action)
    final_time_action_tuples = [(k,v) for k,v in trades_per_time.items() if len(v) != 2]
    dropped_time_action_tuples = [(k,v) for k,v in trades_per_time.items() if len(v) == 2]
    print(f'dropped {dropped_time_action_tuples} time-action tuples due to contradiction')

    for t,final_action in final_time_action_tuples:
        print(f'About to place one way position at {t} with action {final_action}')
        trader = place_one_way_position("ROOT", None, quantity,
                           t, direction=final_action[0],trader=trader)
    return trader

def trade_NG(strats,direction,contract=dtdt(2023,1,1),trader = None,
             quantity=1,asset='QG'):

    for strat in strats:
        trader, orders = place_position(asset,contract,quantity,strat,direction,trader=trader,
                                        hours_shift=0)
        time.sleep(1)
    return trader
def trade_nasdaq(strats,direction,contract=dtdt(2023,3,1),hours_shift=2,
                 trader = None,asset_name='MNQ',tz='UTC',quantity=1):
    for strat in strats:
        trader, orders = place_position(asset_name,contract,quantity,strat,direction,trader=trader,
                                        hours_shift=hours_shift,tz=tz)
        time.sleep(1)
    return trader

def trade_gold(strats,direction,contract=dtdt(2024,6,1),hours_shift=2,
                 trader = None,asset_name='MGC',tz='UTC',quantity=1):
    for strat in strats:
        trader, orders = place_position(asset_name,contract,quantity,strat,direction,trader=trader,
                                        hours_shift=hours_shift,tz=tz)
        time.sleep(1)
    return trader


def wrap_trade_nasdaq(hours_shift=3,weekday_ref =None,
                      direction='BUY',override=None,
                      monday_override=None,trader=None,
                      contract_year=2024,contract_month=3,
                    asset_name='MNQ',
                      quantity=1,
                      tz='UTC'):

    trading_conf = {
        0: [
            'y_0615-0715',
             'y_0730-0800',
             'y_0900-0930',
             'y_1030-1100',
             'y_1500-1515',
             'y_1545-1600',
             'y_2130-2230'
        ],
        1: [
            'y_0400-0600',
             'y_0615-0700',
             'y_0715-0745',
             'y_1115-1200',
             'y_1430-1530',
             'y_1545-1600',
             'y_1715-2000'
        ],
        2: [

        ],
        3: [
            # 'y_0000-0400',

        ],
        4: [

        ],
    }

    update_nasdaq_json(trading_conf,monday_override)
    # raise
    if weekday_ref is None:
        weekday_ref = (dtdt.now()-td(hours=12)).weekday()+1

    trades = trading_conf[weekday_ref]
    if override is not None:
        trades = override
    trader = trade_nasdaq(trades,
                direction,contract=dtdt(contract_year,contract_month,1),
                 hours_shift=hours_shift,asset_name=asset_name,
                          trader=trader,tz=tz,quantity=quantity
                 )

def wrap_trade_NG(direction='BUY',override=None,contract_year=2023,contract_month=8,
                  quantity=1,hours_shift=0,asset='QG'):
    trader = trade_nasdaq(override,
                      direction, contract=dtdt(contract_year, contract_month, 1),
                      hours_shift=hours_shift, quantity=quantity, asset_name=asset
                      )

def wrap_daily_bitcoin(trader=None,only_next_day=True,
                       default_strats = ['y_0400-1200','y_2230-2300'],
                       which_week='general',
                       initial_shift=0):
    # After researching we saw super significant signal in this 8 hour window + 2230-2300 UTC
    special_days_dict = {
        2: {'+2w': ['y_0800-0900','y_1400-1600'],
            'general':['y_0400-1200']},
        3:{'general':['y_2000-2359'],
           '+2w':['y_0000-1200','y_2000-2357']},
     4:{'general':['y_0800-1400'],
        '+2w':['y_0800-1000','y_1200-1600']}
    }

    now = get_utc_now_dt() + td(hours=initial_shift)
    hours_left_until_tomorrow = 24 - now.hour
    list_of_shifts = [hours_left_until_tomorrow+24*i for i in range(0,4-now.weekday())]
    if only_next_day:
        list_of_shifts = list_of_shifts[:1]
    for shift in list_of_shifts:
        weekday = (now + td(hours=shift))   .weekday()
        strats = special_days_dict.get(weekday,[]).get(which_week,[])
        print('handling weekday',weekday, 'got strats = ', strats)
        trader = trade_nasdaq(strats,
            'BUY', contract=(dtdt(now.year, now.month, now.day)+td(days=5)+td(hours=shift)).replace(hour=0),
                              quantity=1,hours_shift=hours_left_until_tomorrow+initial_shift,
                              asset_name='MBT',
                                trader=trader,tz='UTC'
        )



if __name__ == '__main__':
    trader = None
    # raise
    #### Nasdaq

    # trader = None
    # trader = wrap_root_positions(trader=trader)
    # raise
    ##### GOLD #####
    # trader = trade_nasdaq(['y_0800-1400', 'y_1600-1800'],
    #             'BUY',contract=dtdt(2024,6,1),quantity=1,
    #              hours_shift=6,asset_name='MGC'#,tz='Asia/Jerusalem'
    #                     )
    # raise
    #### BITCOIN #####
    wrap_daily_bitcoin(initial_shift=48,which_week='+2w')
    raise
    ##### NASDAQ #####
    if 1==1:
        asset_name = 'MNQ'
        # asset_name = 'MES'
        # quantity = 4
        quantity = 1 if asset_name == 'MNQ' else 1
        wrap_trade_nasdaq(hours_shift=0,
                      weekday_ref=1,
                      direction='BUY',
                      # override=['y_0800-1400',], # Friday pce
                      override=['y_0830-0930'],
                      trader=trader,
                      contract_month=3,
                      contract_year=2025,
                          asset_name=asset_name,
                          quantity=quantity,
                      tz='America/Chicago'
                      )
        raise
    wrap_trade_NG(direction='BUY', override=[  # 'y_1030-1200',
        # 'y_1300-1415',
        #  'y_1545-1645',
        # 'y_1200-1230',
        # 'y_1330-1400',
        # 'y_1415-1515'
        'y_1400-1500',
        # 'y_1515-1715',
        # 'y_1745-1845'
        ],
                  contract_year=2025, contract_month=1, quantity=3, hours_shift=0,
                  asset='MHNG'
                  )

    raise
    # wrap_trade_NG(direction='BUY',override=[# 'y_1030-1200',
    #                                         'y_0000-0400',
    #                                         # 'y_1415-1515'
    #                                         ],
    #               contract_year=2024,contract_month=2,quantity=2,hours_shift=48,
    #               asset='NG'
    #               )
    # raise
    #dump_trader_obj_to_pkl(trader)
    # trader = load_trader_obj_to_pkl()
    # trader, orders = place_position('QG', dtdt(2022, 12, 1), 1, 'y_2230-2245', 'SELL',trader=trader)
    #dump_trader_obj_to_pkl(trader)

    # t = build_active_IBridgePy_plus('********')  # t is the IBridgePy plus object
    # t = build_active_IBridgePy_plus('*********')  # t is the IBridgePy plus object
    # open_orders_lst = t.get_all_open_orders()
    b = 0
    # cancel_all_open_orders()
    # trader = cancel_contradicting_orders(iters=3)
    # trader = cancel_all_open_orders(trader=trader)
    # trader = cancel_contradicting_orders(trader)

    # details = get_contract_details('GC')
    # wrap_get_contract_details(asset='GC')

    # open_orders = wrap_get_open_orders()
    # positions_df = parse_positions_from_orders(open_orders)
    ### todo now need to add the current positions to the orders based calculation
    bb = 0