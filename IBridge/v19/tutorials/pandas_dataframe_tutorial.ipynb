#%%
import os
import sys
sys.path.append(os.path.join(os.getcwd(), '..'))
#%%
from trader_factory import build_active_IBridgePy_plus
#%%
from IBridgePy.IbridgepyTools import symbol
#%%
t = build_active_IBridgePy_plus('DU1868499')  # Build an active trader to get connected to Interactive Brokers
#%%
hist = t.request_historical_data(symbol('SPY'), '1 day', '1 Y')  # Get historical data. Return is a pandas.DataFrame
#%%
hist  # print hist to show it
#%%
hist.columns  # print column names
#%%
hist.index  # print index
#%%
hist['open']  # print out a column
#%%
hist.iloc[0]  # print out 1st row
#%%
hist.iloc[-1]  # print out the last row
#%%
hist.iloc[0]['open']  # print out open price of the 1st row
#%%
type(hist.index[-1])  # print out the data type of index
#%%
import datetime as dt
hist.loc[dt.date(2020,12,15)]  # print out the prices of Dec 15th 2020
#%%
hist['close_yesterday'] = hist['close'].shift(1)  # add a new column, call it "close_yesterday"
#%%
hist
#%%
hist['yield_yesterday'] = (hist['close'] - hist['close_yesterday']) / hist['close_yesterday']
#%%
hist  # a column of yield_yesterday is added.
#%%
hist['yield_tomorrow'] = hist['yield_yesterday'].shift(-1)
#%%
hist  # A column of yield_tomorrow is added.
#%%
hist.dropna(inplace=True)
#%%
hist  # NaN rows are dropped from hist
#%%
from sklearn.linear_model import LinearRegression
import numpy as np
import matplotlib.pyplot as plt
#%%
hist.shape  # There are 250 rows and 8 columns
#%%
# Use machine learning package and build a linear regression model to predit tomorrow's price.
x = hist['yield_yesterday']
y = hist['yield_tomorrow']
model = LinearRegression()
model.fit(hist.loc[:, ['yield_yesterday']], y)
print(model.coef_)
print(model.intercept_)
#%%
# Create a column of predicted prices using the linear regression model
preds = model.predict(hist.loc[:, ["yield_yesterday"]])
#%%
preds
#%%
# Draw a scatter plot.
# The scatter dots are yield_tomorrow vs. yield_yesterday
# The black line is drawn to visualize the trend
hist.plot.scatter(x = "yield_yesterday", y = "yield_tomorrow")
plt.plot(hist['yield_yesterday'], preds, color="black")
#%%
import os
os.getcwd()
#%%
# Save hist to local file baseProject/tutorials/myHist.csv
hist.to_csv(os.path.join(os.getcwd(), 'myHist.csv'))
#%%
import pandas as pd
# Read hist from local file, 
newHist = pd.read_csv(os.path.join(os.getcwd(), 'myHist.csv'), header=0)
#%%
newHist
#%%
newHist.set_index('Unnamed: 0')