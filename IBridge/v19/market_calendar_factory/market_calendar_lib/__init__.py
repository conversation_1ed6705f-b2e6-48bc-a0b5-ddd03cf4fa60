# Fork of Zipline by Quantopian released under MIT license. Original Zipline license below.
#
# Copyright 2016 Quantopian, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .market_calendar import MarketCalendar
from .calendar_utils import get_calendar, merge_schedules, date_range

__version__ = "0.8"

__all__ = [
    'MarketCalendar',
    'get_calendar',
    'merge_schedules',
    'date_range'
]
