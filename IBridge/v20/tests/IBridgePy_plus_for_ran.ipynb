#%%
from trader_factory import build_active_IBridgePy_plus
from IBridgePy.IbridgepyTools import symbol, superSymbol
from IBridgePy.OrderTypes import LimitOrder, StopOrder
#%%
# t = build_active_IBridgePy_plus('DU1541008')  # t is the IBridgePy plus object
t = build_active_IBridgePy_plus('U2423888')  # t is the IBridgePy plus object
#%%
t.display_all()  # display account information
#%%
# Place a limit price order
# t.order(symbol('SPY'), 10, LimitOrder(limit_price=99.90))
#%%
t.get_all_open_orders()

#%%
t
#%%
