from threading import Timer
from datetime import datetime as dtdt
from datetime import timed<PERSON><PERSON> as td
import os
import pandas as pd
from samples.Python.Testbed.ContractSamples import ContractSamples
import numpy as np
import time

from Algo.Conf.assets import Asset

IBAPI_VERSION_TO_USE = '9.73'

if IBAPI_VERSION_TO_USE == '9.73':
    from source.pythonclient.ibapi.client import EClient
    from source.pythonclient.ibapi.wrapper import EWrapper
    from source.pythonclient.ibapi.common import *
    from source.pythonclient.ibapi.contract import *
    from source.pythonclient.ibapi.utils import *
    from source.pythonclient.ibapi.comm import *
    from source.pythonclient.ibapi.order import *
    from source.pythonclient.ibapi.order_state import *
elif IBAPI_VERSION_TO_USE == '9.76':
    from source.pythonclient_976.ibapi.client import EClient
    from source.pythonclient_976.ibapi.wrapper import EWrapper
    from source.pythonclient_976.ibapi.common import *
    from source.pythonclient_976.ibapi.contract import *
    from source.pythonclient_976.ibapi.utils import *
    from source.pythonclient_976.ibapi.comm import *
    from source.pythonclient_976.ibapi.order import *
    from source.pythonclient_976.ibapi.order_state import *

# try:
import source.pythonclient.ibapi.decoder



CONTRACTS_DICT = {"NG": {"symbol": "NG",
                         "Trading Class": "NG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "LocalSymbol": "NGZ7"
                         },
                "QG": {"symbol": "QG",
                         "Trading Class": "QG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "LocalSymbol": "QGZ7"
                         },
                  "NQ": {"symbol": "NQ",
                         "Trading Class": "NQ",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "LocalSymbol": "NQZ2"
                         },
                  "KC": {"symbol": "KC",
                         "Trading Class": "KC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYBOT",
                         # "ConId": 81596359,
                         "LocalSymbol": "KCZ7"  # Dec Z | Sep U
                         },
                  "UNG":
                      {"symbol": "UNG",
                       "currency": "USD",
                       "secType": "STK",
                       "exchange": "SMART",
                       "primary_exchange": "ARCA"
                       },
                  "ZC":
                      {"symbol": "ZC",
                       "Trading Class": "ZC",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "ECBOT",
                       "LocalSymbol": "ZC   JUL 20"  # "ZCZ7"  # Dec Z | Sep U
                       # "ConID": 291854767

                       },
                "NCF":
                      {"symbol": "NCF",
                       "Trading Class": "NCF",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "NCFN1"
                       # "ConID": 291854767
                       },
                "NGF":
                      {"symbol": "NGF",
                       "Trading Class": "NGF",
                       "currency": "GBP",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "NGFN1"
                       # "ConID": 291854767
                       },
                "WTI":
                      {"symbol": "WTI",
                       "Trading Class": "WTI",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "LocalSymbol": "WTIX1"
                       # "ConID": 291854767
                       }
                  }

NG_CONTRACT_CODES = {'January': 'F', 'February': 'G', 'March': 'H', 'April': 'J',
                     'May': 'K', 'June': 'M', 'July': 'N', 'August': 'Q', 'September': 'U',
                     'October': 'V', 'November': 'X', 'December': 'Z'}

DAYS_TO_EXP = {'NG': 9,'QG':2, 'COFFEE': 30}

MAX_MKT_VALUE_FOR_TRADE = 2000  # USD
HOST_IP = '127.0.0.1'
PAPER_PORT = 7497


class Main_Constructor(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)
        self.current_historical_stack = []

    def error(self, reqId: TickerId, errorCode: int, errorString: str):
        print('Error: %s %s %s ' % (reqId, errorCode, errorString))

    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        print('Contract details for request # %s: %s' % (reqId, contractDetails))

    # overriding the default that writes to the logger
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print('yesssssssss')
        super().orderStatus(orderId, status, filled, remaining,
                            avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
        print("OrderStatus. Id:", orderId, "Status:", status, "Filled:", filled,
              "Remaining:", remaining, "AvgFillPrice:", avgFillPrice,
              "PermId:", permId, "ParentId:", parentId, "LastFillPrice:",
              lastFillPrice, "ClientId:", clientId, "WhyHeld:",
              whyHeld, "MktCapPrice:", mktCapPrice)

    def openOrder(self, orderId: OrderId, contract: Contract, order: Order,
                           orderState: OrderState):
        super().openOrder(orderId, contract, order, orderState)
        print(" ".join(["OpenOrder. PermId:", order.permId, "ClientId:", order.clientId, " OrderId:", orderId,
               "Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
               "Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
               "TotalQty:", order.totalQuantity, "CashQty:", order.cashQty,
               "LmtPrice:", order.lmtPrice, "AuxPrice:", order.auxPrice, "Status:", orderState.status,
               "MinTradeQty:", order.minTradeQty]))

        order.contract = contract
        #self.permId2ord[order.permId] = order

    def openOrder_old(self, orderId: OrderId, contract: Contract, order: Order,
                  orderState: OrderState):
        super().openOrder(orderId, contract, order, orderState)

        # print("OpenOrder. PermId: ", order.permId, "ClientId:", order.clientId, " OrderId:", orderId,
        #       "Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
        #       "Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
        #       "TotalQty:", order.totalQuantity, "CashQty:", order.cashQty,
        #       "LmtPrice:", order.lmtPrice, "AuxPrice:", order.auxPrice, "Status:", orderState.status)

        order.contract = contract
        #self.permId2ord[order.permId] = order

    def openOrderEnd_old(self):
        print('yesssssssss')
        super().openOrderEnd()
        print("OpenOrderEnd")
        logging.debug("Received %d openOrders", len(self.permId2ord))

    ### old func
    """
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                    remaining: float, avgFillPrice: float, permId: int,
                    parentId: int, lastFillPrice: float, clientId: int,
                    whyHeld: str, mktCapPrice: float):
        print("Order Status: ID: %s | Status: %s | Filled: %s | Remaining: %s | LastFill Price: %s "% (orderId, status,filled,remaining,lastFillPrice))
    """
    def nextOrderId(self):
        oid = self.nextValidOrderId
        self.nextValidOrderId += 1
        return oid

    def nextValidId(self, orderId: int):
        """ Receives next valid order id."""
        # self.nextOrderId = orderId+1 #todo Ran's hack +1
        # self.logAnswer(current_fn_name(), vars())

        super().nextValidId(orderId)
        logging.debug("setting nextValidOrderId: %d", orderId)
        self.nextValidOrderId = orderId
        print("NextValidId:", orderId)

    def stop(self):
        self.done = True
        # self.reqIds(-1)
        # self.nextValidId(self.nextOrderId)
        # self.disconnect()

    def historicalData(self, reqId: int, bar: BarData):
        print("HistoricalData. ReqId:", reqId, "BarData.", bar)
        bar_str = str(bar).replace("  ", " ")
        bar_splitted = bar_str.split(",") + [self.current_contract_name, '\n']

        self.current_historical_stack.append(",".join(bar_splitted))
        self.logAnswer(current_fn_name(), vars())

    def connect_ran(self, host=HOST_IP, port=PAPER_PORT, clientId=0):
        self.connect(host, port, clientId)



def initialize_app(host_ip=HOST_IP, paper_trading_port=PAPER_PORT):
    client_id = 0
    if paper_trading_port == 7001:
        client_id = 1
    app = Main_Constructor()  # 4002 for real trading?
    app.disconnect()
    app.connect(host_ip, paper_trading_port, clientId=client_id)
    return app


def get_contract(name, local_name=None):
    contract = Contract()
    if name == 'CORN':
        name = 'ZC'
    elif name == 'COFFEE':
        name = 'KC'
    contract_dict = CONTRACTS_DICT[name]
    contract.symbol = contract_dict["symbol"]  # 'underlying' field
    contract.secType = contract_dict["secType"]
    contract.exchange = contract_dict["exchange"]
    contract.includeExpired = True
    if "ConId" in contract_dict.keys():
        contract.conId = contract_dict['ConId']
        pass
    if 'LocalSymbol' in contract_dict.keys() or local_name:
        local = contract_dict['LocalSymbol'] if not local_name else local_name
        contract.localSymbol = local
    if "primary_exchange" in contract_dict.keys():
        contract.primaryExchange = contract_dict["primary_exchange"]
    contract.currency = contract_dict["currency"]
    return contract


def generate_order(action, order_type, quantity, price=None, good_after_time=""):
    my_order = Order()
    assert order_type in ['LMT', 'MKT']
    if order_type == 'LMT':
        assert price is not None
        my_order.lmtPrice = price
        assert price * quantity <= MAX_MKT_VALUE_FOR_TRADE, 'exceeding the maximal Volume I chose'
    my_order.action = action
    my_order.orderType = order_type  # "LMT"
    my_order.totalQuantity = quantity
    my_order.goodAfterTime = good_after_time
    return my_order


def symbol_lookup():
    app = initialize_app()
    ng_cont = Contract()
    ng_cont.symbol = 'NG'
    ng_cont.currency = 'USD'
    ng_cont.secType = 'FTR'

    # app.reqContractDetails(11,ng_cont)
    app.reqMatchingSymbols(11, "NG")
    app.run()


def _get_prompt_month_name(asset, contract_dt, year): # todo Drop after using Asset
    month_code = NG_CONTRACT_CODES[contract_dt.strftime("%B")]
    asset_code_name = asset
    if asset == 'CORN':
        asset_code_name = 'ZC'
    elif asset == 'COFFEE':
        asset_code_name = 'KC'
    if asset_code_name not in ['ZC']:
        prompt_month_local_name = '%s%s%s' % (asset_code_name, month_code, str(year)[-1:])
    else:
        prompt_month_local_name = '%s   %s %s' % (asset_code_name, contract_dt.strftime("%b").upper(), str(year)[-2:])
    return prompt_month_local_name


def _get_contract_dt(ref_day, asset='NG', delay_next_contract_days=0): #todo drop after using Asset Class
    if asset in ['NG','QG']:
        if delay_next_contract_days > 0:
            delay_next_contract_days = min(7, delay_next_contract_days)
            #last_friday_of_contract += td(days=delay_next_contract_days)
        contract_dt = (ref_day + td(days=(38 if ref_day.month !=2 else 35))-td(days=delay_next_contract_days)).replace(day=1)
        next_first_of_month = (ref_day + td(days=32 - ref_day.day)).replace(day=1)
        original_last_day_of_contract = next_first_of_month - td(days=DAYS_TO_EXP[asset])
        last_friday_of_contract = original_last_day_of_contract - td(days=original_last_day_of_contract.weekday() + 3)

        # this work around seems to be no longer relevant (Mar22)
        if False: #last_friday_of_contract < ref_day < original_last_day_of_contract:
            # contract_dt += td(days=(26 if contract_dt.month == 2 else 30))
            contract_dt += td(days=31) # todo make sure its not runining. since contract_dt is always 1/month we can add 31 to all months
            contract_dt = contract_dt.replace(day=1)
        aa = 1
    elif asset == 'CORN':
        allowed_months = [3, 5, 7, 9, 12]
        contract_dt = (ref_day + td(days=40)).replace(day=1)
        while contract_dt.month not in allowed_months:
            contract_dt = (contract_dt + td(days=35)).replace(day=1)
    elif asset == 'COFFEE':
        allowed_months = [3, 5, 7, 9, 12]
        contract_dt = (ref_day + td(days=40)).replace(day=1)
        while contract_dt.month not in allowed_months:
            contract_dt = (contract_dt + td(days=35)).replace(day=1)
    else:
        raise AssertionError('invalid asset')
    return contract_dt


def main(year, months=range(1, 13), asset='NG', mode='paper',
         resolution = '15 mins',app=None):
    port = (PAPER_PORT if mode == 'paper' else 4001)

    if app is None:
        app = initialize_app(paper_trading_port=port)  # Main_Constructor()
    time.sleep(2)
    print('About to retrieve data with MODE = %s | port = %s' % (mode, port))
    corn_contract = Contract()
    corn_contract.symbol = 'CORN'  # 'underlying' field
    corn_contract.secType = 'STK'  # security type
    corn_contract.exchange = 'SMART'
    corn_contract.currency = 'USD'
    corn_contract.primaryExchange = 'ARCA'

    for month in months:  # ,10]: # range(1, 13):
        if asset == 'NG':
            if resolution == '15 mins':
                outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\\months\\NG_%s%s.csv" % (
                year, str(month).zfill(2))
            elif resolution == '5 mins':
                outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\\months\\NG_%s%s_5mins.csv" % (
                    year, str(month).zfill(2))
        elif asset == 'KC':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\COFFEE\Market_Data\\months\\KC_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'ZC':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\CORN\Market_Data\\months\\ZC_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'NCF': # coal
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Coal\\months\\NCF_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'NGF': # coal
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\NGF\\months\\NGF_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'WTI':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\WTI\\months\\WTI_%s%s.csv" % (
            year, str(month).zfill(2))
        elif asset == 'NQ':
            outfile = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Nasdaq\\months\\NQ_%s%s.csv" % (
            year, str(month).zfill(2))
        else:
            raise AssertionError('Do not support assets other than NG / KC / ZC')
        i = 0
        try:
            initial_candles_size = os.stat(outfile).st_size
        except:
            initial_candles_size = 0
        file_cond = lambda file: True if not os.path.exists(outfile) else (os.stat(file).st_size == 0 or
                                                                           os.stat(
                                                                               file).st_size == initial_candles_size)
        while i < 3 and (file_cond(outfile) or i < 1):
            time.sleep(1.5)
            if not app.isConnected():
                app.connect_ran()
            contract_dt = dtdt(year, month, 1)

            if contract_dt < dtdt(2017, 12, 1):
                return
            prompt_month_local_name = _get_prompt_month_name(asset, contract_dt, year)
            app.current_contract_name = contract_dt.strftime("%Y%m")
            days_back = 3 if asset in ['NG','QG'] else 2
            ref_date = contract_dt - td(days=days_back)  # todo .replace(day=25)
            ng_contract = get_contract(asset, prompt_month_local_name)

            period = '40 D'
            if asset in ['ZC', 'KC','NQ']:
                period = '90 D'

            ugaz_hist = app.reqHistoricalData(101, ng_contract, ref_date.strftime('%Y%m%d %H:%M:%S'),
                                              period, resolution, 'TRADES', 0, 1, False, [])
            Timer(5, app.stop).start()
            app.run()
            lines = app.current_historical_stack
            names = ['date+open', 'high', 'low', 'close', 'vol', 'avg', 'bar_count', 'contract', 'spam']
            new_candles_df = pd.DataFrame([l.split(',') for l in lines])
            new_candles_df.columns = names
            new_candles_df['spam'] = np.nan
            try:
                old_candles_df = pd.read_csv(outfile, names=names)
                for c in list(new_candles_df)[1:]:
                    new_candles_df[c] = pd.to_numeric(new_candles_df[c])
                final_candles_df = old_candles_df.merge(new_candles_df, on=list(old_candles_df),
                                                        how='outer')
            except:
                final_candles_df = new_candles_df
            if final_candles_df.shape[0] > 10:
                final_candles_df = final_candles_df.drop_duplicates()
                final_candles_df.to_csv(outfile, index=False, header=False)
            app.current_historical_stack = []
            i += 1
    return app

if __name__ == '__main__':
    for month in [10,9]: #[8,9,10,11]:
        #main(2021, [month], 'NGF',mode='real')  # 7 3,5
        main(2022, [month], 'NG', mode='real',resolution='15 mins')  # 7 3,5
        aa = 1

#####  DRAFT to check contract details
"""
contract = Contract()
contract.symbol = 'KC' #'ZC'
contract.secType = 'FUT'
contract.currency = 'USD'
contract.exchange = 'NYBOT' #'ECBOT'
app.reqContractDetails(11,contract)
"""