2024-04-10 13:19:52.861890-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7496 clientId=9
2024-04-10 13:19:55.938589-04:00: Try to connect to Interactive Brokers: host=localhost port=7496 clientId=9
2024-04-10 13:20:01.024512-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7496 clientId=9
2024-04-10 13:20:06.118608-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7497 clientId=9
2024-04-10 13:20:09.195034-04:00: Try to connect to Interactive Brokers: host=localhost port=7497 clientId=9
2024-04-10 13:20:14.298629-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7497 clientId=9
2024-04-10 13:20:19.398911-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:20:22.479231-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:20:22.487236-04:00: Connected to Interactive Brokers
2024-04-10 13:20:22.513395-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:20:52.514709-04:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-04-10 13:20:52.515707-04:00: {reqId=0,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-04-10 13:23:11.778451-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7496 clientId=9
2024-04-10 13:23:14.856093-04:00: Try to connect to Interactive Brokers: host=localhost port=7496 clientId=9
2024-04-10 13:23:19.938782-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7496 clientId=9
2024-04-10 13:23:25.038370-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7497 clientId=9
2024-04-10 13:23:28.143279-04:00: Try to connect to Interactive Brokers: host=localhost port=7497 clientId=9
2024-04-10 13:23:33.242240-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7497 clientId=9
2024-04-10 13:23:38.331074-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:23:41.380987-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:23:41.395543-04:00: Connected to Interactive Brokers
2024-04-10 13:23:41.400360-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:23:41.916196-04:00: CASH=-47613.05
2024-04-10 13:23:41.917013-04:00: portfolio_value=135397.29
2024-04-10 13:23:41.917013-04:00: positions_value=183175.8
2024-04-10 13:23:41.982936-04:00: ##    POSITIONS U2423888   ##
2024-04-10 13:23:41.983933-04:00: Symbol Amount Cost_basis
2024-04-10 13:23:41.985928-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 13:23:41.988458-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 13:23:41.989924-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 13:23:41.991445-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 13:23:41.991915-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 13:23:41.993547-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 13:23:41.993907-04:00: STK,,,TM,USD 5 201.794
2024-04-10 13:23:41.995703-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 13:23:41.995901-04:00: STK,,,MSFT,USD 7 423.19175
2024-04-10 13:23:41.997320-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 13:23:41.997320-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 13:23:41.998322-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 13:23:41.999319-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 13:23:42.000692-04:00: STK,,,META,USD 4 481.715
2024-04-10 13:23:42.001314-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 13:23:42.002320-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 13:23:42.003314-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 13:23:42.093045-04:00: ##    Order Status U2423888   ##
2024-04-10 13:23:42.094024-04:00: {permId=permIDatIB201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:23:42.095811-04:00: {permId=permIDatIB201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:23:42.096018-04:00: ##    END    ##
2024-04-10 13:23:42.124538-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:23:42.547534-04:00: CASH=-47613.05
2024-04-10 13:23:42.548486-04:00: portfolio_value=135397.29
2024-04-10 13:23:42.549483-04:00: positions_value=183175.8
2024-04-10 13:23:42.581797-04:00: ##    POSITIONS U2423888   ##
2024-04-10 13:23:42.583089-04:00: Symbol Amount Cost_basis
2024-04-10 13:23:42.583089-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 13:23:42.583792-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 13:23:42.584873-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 13:23:42.584873-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 13:23:42.585798-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 13:23:42.586785-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 13:23:42.587784-04:00: STK,,,TM,USD 5 201.794
2024-04-10 13:23:42.588781-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 13:23:42.588781-04:00: STK,,,MSFT,USD 7 423.19175
2024-04-10 13:23:42.589778-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 13:23:42.590775-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 13:23:42.591481-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 13:23:42.591771-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 13:23:42.591771-04:00: STK,,,META,USD 4 481.715
2024-04-10 13:23:42.593044-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 13:23:42.593766-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 13:23:42.593766-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 13:23:42.627312-04:00: ##    Order Status U2423888   ##
2024-04-10 13:23:42.628145-04:00: {permId=permIDatIB201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:23:42.629387-04:00: {permId=permIDatIB201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:23:42.629387-04:00: ##    END    ##
2024-04-10 13:27:09.594999-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7496 clientId=9
2024-04-10 13:27:12.669209-04:00: Try to connect to Interactive Brokers: host=localhost port=7496 clientId=9
2024-04-10 13:27:17.776731-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7496 clientId=9
2024-04-10 13:27:22.894371-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7497 clientId=9
2024-04-10 13:27:25.972990-04:00: Try to connect to Interactive Brokers: host=localhost port=7497 clientId=9
2024-04-10 13:27:31.109372-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7497 clientId=9
2024-04-10 13:27:36.216421-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:27:39.313241-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:27:39.328641-04:00: Connected to Interactive Brokers
2024-04-10 13:27:39.342383-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:28:09.343765-04:00: broker_client_factory.BrokerClient::request_data: EXIT, the following requestRecord failed
2024-04-10 13:28:09.343907-04:00: {reqId=0,status=Submitted,reqType=reqAccountUpdates,followUp=True,waitForFeedbackInSeconds=30,param={subscribe:True,accountCode:U2423888,}}
2024-04-10 13:28:32.208394-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7496 clientId=9
2024-04-10 13:28:35.260984-04:00: Try to connect to Interactive Brokers: host=localhost port=7496 clientId=9
2024-04-10 13:28:40.343633-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7496 clientId=9
2024-04-10 13:28:45.458803-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=7497 clientId=9
2024-04-10 13:28:48.526848-04:00: Try to connect to Interactive Brokers: host=localhost port=7497 clientId=9
2024-04-10 13:28:53.638852-04:00: Try to connect to Interactive Brokers: host=127.0.0.1 port=7497 clientId=9
2024-04-10 13:28:58.747174-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:29:01.795517-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:29:01.800532-04:00: Connected to Interactive Brokers
2024-04-10 13:29:01.806519-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:29:02.163694-04:00: CASH=-47580.52
2024-04-10 13:29:02.163694-04:00: portfolio_value=135902.21
2024-04-10 13:29:02.164628-04:00: positions_value=183648.08
2024-04-10 13:29:02.197851-04:00: ##    POSITIONS U2423888   ##
2024-04-10 13:29:02.198538-04:00: Symbol Amount Cost_basis
2024-04-10 13:29:02.199536-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 13:29:02.199536-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 13:29:02.200533-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 13:29:02.200533-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 13:29:02.200533-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 13:29:02.201623-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 13:29:02.201623-04:00: STK,,,TM,USD 5 201.794
2024-04-10 13:29:02.202527-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 13:29:02.202527-04:00: STK,,,MSFT,USD 7 423.19175
2024-04-10 13:29:02.203524-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 13:29:02.203524-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 13:29:02.204523-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 13:29:02.204523-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 13:29:02.205518-04:00: STK,,,META,USD 4 481.715
2024-04-10 13:29:02.205518-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 13:29:02.205518-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 13:29:02.206516-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 13:29:02.243640-04:00: ##    Order Status U2423888   ##
2024-04-10 13:29:02.244415-04:00: {permId=permIDatIB201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:29:02.245506-04:00: {permId=permIDatIB201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:29:02.245506-04:00: ##    END    ##
2024-04-10 13:29:02.260372-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:29:02.565922-04:00: CASH=-47580.52
2024-04-10 13:29:02.566922-04:00: portfolio_value=135902.21
2024-04-10 13:29:02.567054-04:00: positions_value=183648.08
2024-04-10 13:29:02.599001-04:00: ##    POSITIONS U2423888   ##
2024-04-10 13:29:02.599325-04:00: Symbol Amount Cost_basis
2024-04-10 13:29:02.599998-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 13:29:02.599998-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 13:29:02.600996-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 13:29:02.600996-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 13:29:02.600996-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 13:29:02.601995-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 13:29:02.601995-04:00: STK,,,TM,USD 5 201.794
2024-04-10 13:29:02.601995-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 13:29:02.602991-04:00: STK,,,MSFT,USD 7 423.19175
2024-04-10 13:29:02.602991-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 13:29:02.602991-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 13:29:02.603990-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 13:29:02.603990-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 13:29:02.603990-04:00: STK,,,META,USD 4 481.715
2024-04-10 13:29:02.604985-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 13:29:02.604985-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 13:29:02.604985-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 13:29:02.642788-04:00: ##    Order Status U2423888   ##
2024-04-10 13:29:02.643785-04:00: {permId=permIDatIB201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:29:02.643785-04:00: {permId=permIDatIB201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 13:29:02.644782-04:00: ##    END    ##
2024-04-10 13:47:57.336290-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:48:00.391019-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:48:00.398055-04:00: Connected to Interactive Brokers
2024-04-10 13:48:00.404213-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:49:47.530845-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:49:50.590171-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:49:50.596163-04:00: Connected to Interactive Brokers
2024-04-10 13:49:50.601150-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:52:10.661786-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:52:13.776546-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:52:13.785458-04:00: Connected to Interactive Brokers
2024-04-10 13:52:13.792964-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 13:57:24.670964-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 13:57:27.789730-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 13:57:27.797723-04:00: Connected to Interactive Brokers
2024-04-10 13:57:27.803692-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:15:50.296746-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 14:15:53.385901-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 14:15:53.404321-04:00: Connected to Interactive Brokers
2024-04-10 14:15:53.415910-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:15:53.892758-04:00: CASH=-46211.05
2024-04-10 14:15:53.893652-04:00: portfolio_value=135496.55
2024-04-10 14:15:53.893652-04:00: positions_value=181873.67
2024-04-10 14:15:53.924792-04:00: ##    POSITIONS U2423888   ##
2024-04-10 14:15:53.924792-04:00: Symbol Amount Cost_basis
2024-04-10 14:15:53.925801-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 14:15:53.925801-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 14:15:53.926786-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 14:15:53.926786-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 14:15:53.926786-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 14:15:53.927784-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 14:15:53.927784-04:00: STK,,,TM,USD 5 201.794
2024-04-10 14:15:53.928781-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 14:15:53.928781-04:00: STK,,,MSFT,USD 6 423.19175
2024-04-10 14:15:53.929778-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 14:15:53.929778-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 14:15:53.930776-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 14:15:53.930776-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 14:15:53.930776-04:00: STK,,,META,USD 4 481.715
2024-04-10 14:15:53.931774-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 14:15:53.931774-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 14:15:53.931774-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 14:15:53.970954-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733775
2024-04-10 14:15:53.971951-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733772
2024-04-10 14:15:53.971951-04:00: ##    Order Status U2423888   ##
2024-04-10 14:15:53.972950-04:00: {ibpyOrderId=ib201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:15:53.972950-04:00: {ibpyOrderId=permIDatIB201733775 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:15:53.973948-04:00: {ibpyOrderId=ib201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:15:53.973948-04:00: {ibpyOrderId=permIDatIB201733772 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:15:53.974962-04:00: ##    END    ##
2024-04-10 14:15:53.988909-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:15:54.404873-04:00: CASH=-46211.05
2024-04-10 14:15:54.405762-04:00: portfolio_value=135496.55
2024-04-10 14:15:54.405762-04:00: positions_value=181873.67
2024-04-10 14:15:54.436823-04:00: ##    POSITIONS U2423888   ##
2024-04-10 14:15:54.438122-04:00: Symbol Amount Cost_basis
2024-04-10 14:15:54.438818-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 14:15:54.438833-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 14:15:54.438833-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 14:15:54.439814-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 14:15:54.439814-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 14:15:54.440812-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 14:15:54.440812-04:00: STK,,,TM,USD 5 201.794
2024-04-10 14:15:54.440812-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 14:15:54.441811-04:00: STK,,,MSFT,USD 6 423.19175
2024-04-10 14:15:54.441811-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 14:15:54.442805-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 14:15:54.442805-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 14:15:54.442805-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 14:15:54.443803-04:00: STK,,,META,USD 4 481.715
2024-04-10 14:15:54.443803-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 14:15:54.443803-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 14:15:54.444801-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 14:15:54.483266-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733775
2024-04-10 14:15:54.484262-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733772
2024-04-10 14:15:54.484262-04:00: ##    Order Status U2423888   ##
2024-04-10 14:15:54.485401-04:00: {ibpyOrderId=ib201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:15:54.485401-04:00: {ibpyOrderId=permIDatIB201733775 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:15:54.486257-04:00: {ibpyOrderId=ib201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:15:54.486257-04:00: {ibpyOrderId=permIDatIB201733772 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:15:54.486257-04:00: ##    END    ##
2024-04-10 14:16:23.557508-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 14:16:26.614933-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 14:16:26.632302-04:00: Connected to Interactive Brokers
2024-04-10 14:16:26.639281-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:16:27.065749-04:00: CASH=-46209.5
2024-04-10 14:16:27.065749-04:00: portfolio_value=135642.38
2024-04-10 14:16:27.066639-04:00: positions_value=182017.94
2024-04-10 14:16:27.098785-04:00: ##    POSITIONS U2423888   ##
2024-04-10 14:16:27.098785-04:00: Symbol Amount Cost_basis
2024-04-10 14:16:27.099373-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 14:16:27.099783-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 14:16:27.099783-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 14:16:27.099783-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 14:16:27.100780-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 14:16:27.100886-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 14:16:27.100886-04:00: STK,,,TM,USD 5 201.794
2024-04-10 14:16:27.100886-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 14:16:27.101778-04:00: STK,,,MSFT,USD 6 423.19175
2024-04-10 14:16:27.101778-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 14:16:27.101778-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 14:16:27.102775-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 14:16:27.102775-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 14:16:27.103542-04:00: STK,,,META,USD 4 481.715
2024-04-10 14:16:27.103772-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 14:16:27.103772-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 14:16:27.103772-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 14:16:27.128855-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733775
2024-04-10 14:16:27.129854-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733772
2024-04-10 14:16:27.130851-04:00: ##    Order Status U2423888   ##
2024-04-10 14:16:27.130851-04:00: {ibpyOrderId=ib201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:16:27.131850-04:00: {ibpyOrderId=permIDatIB201733775 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:16:27.131850-04:00: {ibpyOrderId=ib201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:16:27.132309-04:00: {ibpyOrderId=permIDatIB201733772 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:16:27.132847-04:00: ##    END    ##
2024-04-10 14:17:35.979840-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 14:17:39.030553-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 14:17:39.037535-04:00: Connected to Interactive Brokers
2024-04-10 14:17:39.043523-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:17:39.433867-04:00: CASH=-46209.5
2024-04-10 14:17:39.433867-04:00: portfolio_value=135642.38
2024-04-10 14:17:39.434865-04:00: positions_value=182017.94
2024-04-10 14:17:39.465200-04:00: ##    POSITIONS U2423888   ##
2024-04-10 14:17:39.466376-04:00: Symbol Amount Cost_basis
2024-04-10 14:17:39.466376-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 14:17:39.466880-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 14:17:39.466893-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 14:17:39.466893-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 14:17:39.466893-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 14:17:39.467885-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 14:17:39.467885-04:00: STK,,,TM,USD 5 201.794
2024-04-10 14:17:39.467885-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 14:17:39.467885-04:00: STK,,,MSFT,USD 6 423.19175
2024-04-10 14:17:39.468931-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 14:17:39.468931-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 14:17:39.468931-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 14:17:39.469875-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 14:17:39.469875-04:00: STK,,,META,USD 4 481.715
2024-04-10 14:17:39.469875-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 14:17:39.470872-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 14:17:39.470872-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 14:17:39.496848-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733775
2024-04-10 14:17:39.497846-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733772
2024-04-10 14:17:39.497846-04:00: ##    Order Status U2423888   ##
2024-04-10 14:17:39.497846-04:00: {ibpyOrderId=ib201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:17:39.498843-04:00: {ibpyOrderId=permIDatIB201733775 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:17:39.498843-04:00: {ibpyOrderId=ib201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:17:39.498843-04:00: {ibpyOrderId=permIDatIB201733772 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:17:39.498843-04:00: ##    END    ##
2024-04-10 14:17:43.036495-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4965253
2024-04-10 14:17:44.042703-04:00: broker_client_factory.CallBacks::error: errorId=4965253 errorCode=201 errorMessage=Order rejected - reason:Effective time is too near to the market close. advancedOrderRejectJson=
2024-04-10 14:18:13.623892-04:00: broker_service_factory.BrokerService_web::order_status_monitor: EXIT, waiting time is too long, >30
2024-04-10 14:18:16.364643-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4965299
2024-04-10 14:18:16.365640-04:00: broker_client_factory.CallBacks::error: errorId=4965299 errorCode=201 errorMessage=Order rejected - reason:Effective time is too near to the market close. advancedOrderRejectJson=
2024-04-10 14:18:34.601244-04:00: Try to connect to Interactive Brokers: host=host.docker.internal port=4001 clientId=9
2024-04-10 14:18:37.692013-04:00: Try to connect to Interactive Brokers: host=localhost port=4001 clientId=9
2024-04-10 14:18:37.699990-04:00: Connected to Interactive Brokers
2024-04-10 14:18:37.707056-04:00: ##    ACCOUNT Balance  U2423888  ##
2024-04-10 14:18:38.115154-04:00: CASH=-46209.5
2024-04-10 14:18:38.115154-04:00: portfolio_value=135642.38
2024-04-10 14:18:38.115154-04:00: positions_value=182017.94
2024-04-10 14:18:38.147083-04:00: ##    POSITIONS U2423888   ##
2024-04-10 14:18:38.147989-04:00: Symbol Amount Cost_basis
2024-04-10 14:18:38.148487-04:00: STK,,,VTRS,USD 2 14.3746
2024-04-10 14:18:38.148988-04:00: STK,,,TAN,USD 5 73.76
2024-04-10 14:18:38.148988-04:00: STK,,,ROOT,USD 90 77.********
2024-04-10 14:18:38.148988-04:00: STK,,,AAPL,USD 5 187.43666
2024-04-10 14:18:38.149981-04:00: STK,,,ISTB,USD 300 47.2419
2024-04-10 14:18:38.149981-04:00: STK,,,GOOG,USD 24 156.160875
2024-04-10 14:18:38.149981-04:00: STK,,,TM,USD 5 201.794
2024-04-10 14:18:38.149981-04:00: STK,,,PANW,USD 2 313.35
2024-04-10 14:18:38.150977-04:00: STK,,,MSFT,USD 6 423.19175
2024-04-10 14:18:38.150977-04:00: STK,,,AMZN,USD 10 174.********
2024-04-10 14:18:38.150977-04:00: STK,,,NFLX,USD 2 364.17
2024-04-10 14:18:38.150977-04:00: STK,,,TEVA,USD 100 12.915
2024-04-10 14:18:38.151940-04:00: STK,,,PGR,USD 42 207.********
2024-04-10 14:18:38.151940-04:00: STK,,,META,USD 4 481.715
2024-04-10 14:18:38.151940-04:00: STK,,,NVDA,USD 2 912.165
2024-04-10 14:18:38.152974-04:00: STK,,,IJJ,USD 7 110.08
2024-04-10 14:18:38.152974-04:00: STK,,,TSLA,USD 3 272.********
2024-04-10 14:18:38.162913-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733775
2024-04-10 14:18:38.163910-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=permIDatIB201733772
2024-04-10 14:18:38.164704-04:00: ##    Order Status U2423888   ##
2024-04-10 14:18:38.164908-04:00: {ibpyOrderId=ib201733775 status=PreSubmitted order={account=U2423888 action=BUY orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:18:38.164908-04:00: {ibpyOrderId=permIDatIB201733775 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:18:38.164908-04:00: {ibpyOrderId=ib201733772 status=PreSubmitted order={account=U2423888 action=SELL orderType=MKT amount=1 tif=DAY orderRef=} contract={FUT,CME,MNQ,USD}}
2024-04-10 14:18:38.164908-04:00: {ibpyOrderId=permIDatIB201733772 status=PreSubmitted order=NONE contract=NONE}
2024-04-10 14:18:38.165906-04:00: ##    END    ##
2024-04-10 14:18:40.974817-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4965307
2024-04-10 14:18:44.857084-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4965312
2024-04-10 14:18:48.744077-04:00: models.SingleTrader::get_accountCode_by_ibpyOrderId: Cannot find accountCode by ibpyOrderId=ib4965317
