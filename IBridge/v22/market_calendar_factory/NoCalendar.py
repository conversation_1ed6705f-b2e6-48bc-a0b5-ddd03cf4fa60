# -*- coding: utf-8 -*-
"""
@author: <EMAIL>
All rights reserved.
"""
from .MarketCalendarBase import MarketCalendarBase


class NoCalendar(MarketCalendarBase):
    @property
    def name(self):
        return 'NoCalendar'

    def isTradingDay(self, timeNow):
        return True

    def is_market_open_at_this_moment(self, aDatetime):
        return True

    def isEarlyClose(self, aDatetime):
        raise False
