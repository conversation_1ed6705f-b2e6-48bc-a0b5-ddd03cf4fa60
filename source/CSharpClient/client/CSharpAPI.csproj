<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8EDE0744-96C2-40B0-A8DC-37DF79370203}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IBApi</RootNamespace>
    <AssemblyName>CSharpAPI</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountSummaryTags.cs" />
    <Compile Include="Bar.cs" />
    <Compile Include="BitMask.cs" />
    <Compile Include="ComboLeg.cs" />
    <Compile Include="CommissionReport.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="Contract.cs" />
    <Compile Include="ContractCondition.cs" />
    <Compile Include="ContractDescription.cs" />
    <Compile Include="ContractDetails.cs" />
    <Compile Include="DeltaNeutralContract.cs" />
    <Compile Include="HistoricalTick.cs" />
    <Compile Include="HistoricalTickBidAsk.cs" />
    <Compile Include="HistoricalTickLast.cs" />
    <Compile Include="PriceIncrement.cs" />
    <Compile Include="HistogramEntry.cs" />
    <Compile Include="NewsProvider.cs" />
    <Compile Include="DepthMktDataDescription.cs" />
    <Compile Include="DefaultEWrapper.cs" />
    <Compile Include="EClientErrors.cs" />
    <Compile Include="EClientException.cs" />
    <Compile Include="EClientMsgSink.cs" />
    <Compile Include="EClient.cs" />
    <Compile Include="EClientSocket.cs" />
    <Compile Include="EClientSocketSSL.cs" />
    <Compile Include="EDecoder.cs" />
    <Compile Include="EMessage.cs" />
    <Compile Include="EReader.cs" />
    <Compile Include="EReaderMonitorSignal.cs" />
    <Compile Include="EReaderSignal.cs" />
    <Compile Include="ESocket.cs" />
    <Compile Include="ETransport.cs" />
    <Compile Include="EWrapper.cs" />
    <Compile Include="Execution.cs" />
    <Compile Include="ExecutionCondition.cs" />
    <Compile Include="ExecutionFilter.cs" />
    <Compile Include="FamilyCode.cs" />
    <Compile Include="IBParamsList.cs" />
    <Compile Include="IDecoder.cs" />
    <Compile Include="IncomingMessage.cs" />
    <Compile Include="MarginCondition.cs" />
    <Compile Include="MessageValidator.cs" />
    <Compile Include="MinServerVer.cs" />
    <Compile Include="OperatorCondition.cs" />
    <Compile Include="Order.cs" />
    <Compile Include="OrderComboLeg.cs" />
    <Compile Include="OrderCondition.cs" />
    <Compile Include="OrderState.cs" />
    <Compile Include="OutgoingMessages.cs" />
    <Compile Include="PercentChangeCondition.cs" />
    <Compile Include="PriceCondition.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScannerSubscription.cs" />
    <Compile Include="SoftDollarTier.cs" />
    <Compile Include="TagValue.cs" />
    <Compile Include="TickAttrib.cs" />
    <Compile Include="TickType.cs" />
    <Compile Include="TimeCondition.cs" />
    <Compile Include="Util.cs" />
    <Compile Include="VolumeCondition.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>