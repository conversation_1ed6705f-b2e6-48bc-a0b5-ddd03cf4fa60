import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
import quandl

quandl_api_key = 'wL5Pr93p7GCLavwvf8k2'


UNG_DIR = r'C:\Users\<USER>\Documents\Work\IB\UNG'
UNG_15_MINS = r"C:\Users\<USER>\Documents\Work\IB\UNG\UNG_15 mins_Trades.csv"


def main():
    open = (16,30)
    close = (19,30)

    df = pd.read_csv(UNG_15_MINS, parse_dates=['time'])
    #df['day'] = df['time'].apply(lambda x: (x.year, x.month, x.day))
    df['year'] = df.time.dt.year
    df['month'] = df.time.dt.month
    df['day'] = df.time.dt.day

    method = 'mean'  # high (stricter)
    stack = []

    for name, day_group in df.groupby(['year','month','day']):
        if name[2] == 1:
            print('Reached %s' % str(name))
        day_dt = dtdt(name[0], name[1], name[2])
        opens_price = day_group[day_group['time'] >= day_dt+ td(hours=open[0], minutes=open[1])].sort_values('time').iloc[:2].mean()['Open']
        close_price = day_group[day_group['time'] >= day_dt+ td(hours=close[0], minutes=close[1])].sort_values('time').iloc[:2].mean()['Open']
        stack.append({'time': day_dt+ td(hours=open[0], minutes=open[1]),
                      'Open': opens_price, 'Close': close_price, 'deltaY': close_price-opens_price, 'deltaY_percent': close_price/opens_price - 1})

    y_df = pd.DataFrame(stack)
    y_df.to_csv(r'C:\Users\<USER>\Documents\Work\IB\UNG\Y_by15min_meanMethod_%s-%s.csv'%(str(open[0])+':'+str(open[1]), str(close[0])+':'+str(close[1])))


def predict(hdd_diff, saf):
    if abs(hdd_diff) >= saf:
        return 1 if hdd_diff > 0 else -1
    else:
        return 0

def voting(pred1, pred2):
    if pred1 == 1 and pred2 == 1:
        return 1
    elif pred1 == -1 and pred2 == -1:
        return -1
    else:
        return 0

load = False
if load:
    ung_df = pd.read_csv(r"C:\Users\<USER>\Documents\Work\IB\UNG\Y_by15min_meanMethod_1630-1930.csv")
    ng_df = pd.read_csv(r"C:\Users\<USER>\Documents\Work\IB\UNG\Priority1A+2A_RansFilter_TradingTimes=0815-1115_timeFormatted_EXTENDED.csv")

    ung_df['time'] = ung_df['time'].apply(lambda x: dtdt.strptime(x, '%d/%m/%Y %H:%M'))
    ng_df['time'] = ng_df['time'].apply(lambda x: dtdt.strptime(x, '%d/%m/%Y %H:%M:%S'))

    ng_df['day'] = ng_df['time'].apply(lambda x: x.date())
    ung_df['day'] = ung_df['time'].apply(lambda x: x.date())
    final = ng_df.merge(ung_df, on=['day'])
else:
    final = pd.read_csv(r"C:\Users\<USER>\Documents\Work\IB\UNG\UNG+NG_merged_XYs.csv", parse_dates=['day', 'time_x','time_y'])

hdd_18_col = 'HDD_diff0-18h_sum+d_0_1_2_3_4_5_6_7_8_9_10_11_12_13_14'
hdd_6_col = 'HDD_diff0-6h_sum+d_0_1_2_3_4_5_6_7_8_9_10_11_12_13_14'

final_winter = final[final['day'].apply(lambda x: x.month in [11,12,1,2,3])]

predictor_col = hdd_6_col
drop_1_5s = True
for saf in range(10):
    final_winter['y_tag_hdd6'] = final_winter[hdd_6_col].apply(lambda x: predict(x, saf))
    final_winter['y_tag_hdd18'] = final_winter[hdd_6_col].apply(lambda x: predict(x, saf))
    final_winter['y_tag'] = final_winter.apply(lambda x: voting(x['y_tag_hdd6'],x['y_tag_hdd18']),axis=1)

    final_winter = final_winter[final_winter['y_tag'].apply(lambda x: x != 0)]

    if drop_1_5s:
        final_winter = final_winter[final_winter['time_x'].apply(lambda x: x.weekday() in range(5))]

    try:
        final_winter['is_hit_1s'] = np.sign(final_winter['y_tag']) * np.sign(final_winter['y_percent'])
    except:
        continue
    final_winter['is_hit_01'] = final_winter['is_hit_1s'].apply(lambda x: 0 if x == -1 else x)
    hr = final_winter['is_hit_01'].mean()
    print('Saf: %s \\ #trades in 2016-7: %s\\ total trades: %s \\  HR: %s' % (saf, final_winter[final_winter['time_x'] >= dtdt(2016,11,1)].shape[0],final_winter.shape[0],hr))
    aa = 1
