<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CB7BDB4D-923F-4C4E-BB20-68DBDC33C10A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IBSampleApp</RootNamespace>
    <AssemblyName>IBSampleApp</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>
    </ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="messages\TickByTickMidPointMessage.cs" />
    <Compile Include="messages\TickByTickBidAskMessage.cs" />
    <Compile Include="messages\TickByTickAllLastMessage.cs" />
    <Compile Include="messages\HistoricalTickLastMessage.cs" />
    <Compile Include="messages\HistoricalTickLastEndMessage.cs" />
    <Compile Include="messages\HistoricalTickBidAsk.cs" />
    <Compile Include="messages\HistoricalTickEndMessage.cs" />
    <Compile Include="messages\HistoricalTickBidAskEndMessage.cs" />
    <Compile Include="messages\HistoricalTickMessage.cs" />
    <Compile Include="messages\MarketRuleMessage.cs" />
    <Compile Include="messages\HeadTimestampMessage.cs" />
    <Compile Include="messages\DailyPnLSingleMessage.cs" />
    <Compile Include="messages\DailyPnLMessage.cs" />
    <Compile Include="messages\HistogramDataMessage.cs" />
    <Compile Include="messages\AccountDownloadEndMessage.cs" />
    <Compile Include="messages\AccountSummaryEndMessage.cs" />
    <Compile Include="messages\AccountSummaryMessage.cs" />
    <Compile Include="messages\AdvisorDataMessage.cs" />
    <Compile Include="messages\CommissionMessage.cs" />
    <Compile Include="messages\ConnectionStatusMessage.cs" />
    <Compile Include="messages\ContractDetailsEndMessage.cs" />
    <Compile Include="messages\ContractDetailsMessage.cs" />
    <Compile Include="messages\AccountUpdateMultiMessage.cs" />
    <Compile Include="messages\AccountUpdateMultiEndMessage.cs" />
    <Compile Include="messages\BondContractDetailsMessage.cs" />
    <Compile Include="messages\HistoricalNewsEndMessage.cs" />
    <Compile Include="messages\HistoricalNewsMessage.cs" />
    <Compile Include="messages\TickNewsMessage.cs" />
    <Compile Include="messages\MarketDataTypeMessage.cs" />
    <Compile Include="messages\NewsArticleMessage.cs" />
    <Compile Include="messages\FamilyCodesMessage.cs" />
    <Compile Include="messages\MktDepthExchangesMessage.cs" />
    <Compile Include="messages\NewsProvidersMessage.cs" />
    <Compile Include="messages\PositionMultiEndMessage.cs" />
    <Compile Include="messages\PositionMultiMessage.cs" />
    <Compile Include="messages\DeepBookMessage.cs" />
    <Compile Include="messages\ExecutionMessage.cs" />
    <Compile Include="messages\FundamentalsMessage.cs" />
    <Compile Include="messages\HistoricalDataEndMessage.cs" />
    <Compile Include="messages\HistoricalDataMessage.cs" />
    <Compile Include="messages\ManagedAccountsMessage.cs" />
    <Compile Include="messages\OpenOrderMessage.cs" />
    <Compile Include="messages\OrderMessage.cs" />
    <Compile Include="messages\OrderStatusMessage.cs" />
    <Compile Include="messages\PositionMessage.cs" />
    <Compile Include="messages\RealTimeBarMessage.cs" />
    <Compile Include="messages\ScannerEndMessage.cs" />
    <Compile Include="messages\ScannerMessage.cs" />
    <Compile Include="messages\AccountValueMessage.cs" />
    <Compile Include="messages\ScannerParametersMessage.cs" />
    <Compile Include="messages\SymbolSamplesMessage.cs" />
    <Compile Include="messages\TickOptionMessage.cs" />
    <Compile Include="messages\UpdateAccountTimeMessage.cs" />
    <Compile Include="messages\UpdatePortfolioMessage.cs" />
    <Compile Include="types\FinancialAdvisorTypes.cs" />
    <Compile Include="types\IBTypes.cs" />
    <Compile Include="ui\AccountManager.cs" />
    <Compile Include="ui\AdvisorManager.cs" />
    <Compile Include="ui\ComboContractResults.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\ComboContractResults.Designer.cs">
      <DependentUpon>ComboContractResults.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\ConditionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\ConditionDialog.Designer.cs">
      <DependentUpon>ConditionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\ContractManager.cs" />
    <Compile Include="ui\ContractSearchControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ui\ContractSearchControl.Designer.cs">
      <DependentUpon>ContractSearchControl.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\ContractSearchDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\ContractSearchDialog.Designer.cs">
      <DependentUpon>ContractSearchDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\PnLManager.cs" />
    <Compile Include="ui\NewsManager.cs" />
    <Compile Include="ui\SmartComponentsMessage.cs" />
    <Compile Include="ui\SymbolSamplesManager.cs" />
    <Compile Include="ui\MarginDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\MarginDialog.Designer.cs">
      <DependentUpon>MarginDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\OptionsManager.cs" />
    <Compile Include="ui\OrderDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\OrderDialog.Designer.cs">
      <DependentUpon>OrderDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ui\AcctPosMultiManager.cs" />
    <Compile Include="ui\DataManager.cs" />
    <Compile Include="ui\DeepBookManager.cs" />
    <Compile Include="backend\IBClient.cs" />
    <Compile Include="messages\ErrorMessage.cs" />
    <Compile Include="messages\MarketDataMessage.cs" />
    <Compile Include="messages\TickPriceMessage.cs" />
    <Compile Include="messages\TickSizeMessage.cs" />
    <Compile Include="ui\HistoricalDataManager.cs" />
    <Compile Include="ui\IBSampleApp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ui\IBSampleApp.Designer.cs">
      <DependentUpon>IBSampleApp.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ui\MarketDataManager.cs" />
    <Compile Include="ui\OrderManager.cs" />
    <Compile Include="ui\RealTimeBarsManager.cs" />
    <Compile Include="ui\ScannerManager.cs" />
    <Compile Include="messages\SecurityDefinitionOptionParameterEndMessage.cs" />
    <Compile Include="messages\SecurityDefinitionOptionParameterMessage.cs" />
    <Compile Include="ui\SecurityDefinitionOptionParameterKey.cs" />
    <Compile Include="messages\SoftDollarTiersMessage.cs" />
    <Compile Include="ui\TickReqParamsMessage.cs" />
    <Compile Include="util\Utils.cs" />
    <Compile Include="util\XmlHelper.cs" />
    <EmbeddedResource Include="ui\ComboContractResults.resx">
      <DependentUpon>ComboContractResults.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ui\ConditionDialog.resx">
      <DependentUpon>ConditionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ui\ContractSearchControl.resx">
      <DependentUpon>ContractSearchControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ui\ContractSearchDialog.resx">
      <DependentUpon>ContractSearchDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ui\IBSampleApp.resx">
      <DependentUpon>IBSampleApp.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="ui\MarginDialog.resx">
      <DependentUpon>MarginDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ui\OrderDialog.resx">
      <DependentUpon>OrderDialog.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="Properties\DataSources\IBApi.Order.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\source\csharpclient\client\CSharpAPI.csproj">
      <Project>{8EDE0744-96C2-40B0-A8DC-37DF79370203}</Project>
      <Name>CSharpAPI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ib_logo_2015.ico" />
    <Content Include="LogoIcon.gif" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>