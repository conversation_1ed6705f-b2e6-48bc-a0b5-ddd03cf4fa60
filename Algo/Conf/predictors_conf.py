from datetime import datetime as dtdt



### The following ratios are based on Nov19-Now history!
nov_2017 = dtdt(2017,12,1)
july_19_start = dtdt(2019, 7, 1)
march_start = dtdt(2020,3,1)
april_start = dtdt(2020,4,1)
nov_start = dtdt(2019,11,1)
august20_start = dtdt(2020,8,1)



def generate_conf(chosen_ratio,preds,start=dtdt(2021,1,1)):
    return {'start': start, 'chosen_ratio': chosen_ratio,
                 'preds_for_comb': preds,
                    'is_prod': True}


COMBS_PREDICTORS_DICT = {
                        "y_1100-1745_real-1d6to8": generate_conf(0.6,["real-1d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-1d6to8": generate_conf(0.6,["real-1d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1745_real-7d6to8": generate_conf(0.6,["real-7d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-7d6to8": generate_conf(0.6,["real-7d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-1d8to11": generate_conf(0.6,["real-1d_8-11"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-7d12to13Neg": generate_conf(0.6,["real-7d_12-13-"],start=dtdt(2022,1,1)),
                        "y_1300-1400_real-3d13to14": generate_conf(0.6,["real-3d_13-14"],start=dtdt(2022,1,1)),
                        "y_0800-1100_real-7d14to15Neg": generate_conf(0.6,["real-7d_14-15-"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-2d15to16": generate_conf(0.6,["real-2d_15-16"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-2d15to16Neg": generate_conf(0.6,["real-2d_15-16-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-7d16to18Neg": generate_conf(0.6,["real-7d_16-18-"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-7d12to14Neg": generate_conf(0.6,["real-7d_12-14-"],start=dtdt(2022,1,1)),

                        "y_0000-0600_real-1d0to6": generate_conf(0.6,["real-1d_0-6"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-1d0to6": generate_conf(0.6,["real-1d_0-6"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-2d0to6Neg": generate_conf(0.6,["real-2d_0-6-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d0to6Neg": generate_conf(0.6,["real-2d_0-6-"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-2d0to6Neg": generate_conf(0.6,["real-2d_0-6-"],start=dtdt(2022,1,1)),
                        "y_0800-1100_real-3d0to6": generate_conf(0.6,["real-3d_0-6"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-3d0to6": generate_conf(0.6,["real-3d_0-6"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-3d0to6Neg": generate_conf(0.6,["real-3d_0-6-"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d0to6Neg": generate_conf(0.6,["real-3d_0-6-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-4d0to6Neg": generate_conf(0.6,["real-4d_0-6-"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-7d0to6": generate_conf(0.6,["real-7d_0-6"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-2d6to8": generate_conf(0.6,["real-2d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1745_real-2d6to8": generate_conf(0.6,["real-2d_6-8"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-2d6to8": generate_conf(0.6,["real-2d_6-8"],start=dtdt(2022,1,1)),
                        "y_0800-1845_real-2d6to8": generate_conf(0.6,["real-2d_6-8"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-2d6to8Neg": generate_conf(0.6,["real-2d_6-8-"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-4d6to8Neg": generate_conf(0.6,["real-4d_6-8-"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-7d6to8": generate_conf(0.6,["real-7d_6-8"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d8to11": generate_conf(0.6,["real-2d_8-11"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d8to11": generate_conf(0.6,["real-3d_8-11"],start=dtdt(2022,1,1)),
                        "y_0800-1845_real-7d8to11Neg": generate_conf(0.6,["real-7d_8-11-"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-1d11to12Neg": generate_conf(0.6,["real-1d_11-12-"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-2d11to12": generate_conf(0.6,["real-2d_11-12"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d11to12": generate_conf(0.6,["real-2d_11-12"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d11to12": generate_conf(0.6,["real-3d_11-12"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d11to12": generate_conf(0.6,["real-3d_11-12"],start=dtdt(2022,1,1)),
                        "y_1100-1200_real-7d11to12Neg": generate_conf(0.6,["real-7d_11-12-"],start=dtdt(2022,1,1)),
                        "y_0800-1100_real-7d11to12Neg": generate_conf(0.6,["real-7d_11-12-"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-1d12to13Neg": generate_conf(0.6,["real-1d_12-13-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d12to13": generate_conf(0.6,["real-2d_12-13"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d12to13": generate_conf(0.6,["real-3d_12-13"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d12to13": generate_conf(0.6,["real-3d_12-13"],start=dtdt(2022,1,1)),
                        "y_1200-1300_real-3d12to13Neg": generate_conf(0.6,["real-3d_12-13-"],start=dtdt(2022,1,1)),
                        "y_1200-1300_real-4d12to13": generate_conf(0.6,["real-4d_12-13"],start=dtdt(2022,1,1)),
                        "y_1300-1400_real-2d13to14": generate_conf(0.6,["real-2d_13-14"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d13to14": generate_conf(0.6,["real-3d_13-14"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d13to14": generate_conf(0.6,["real-3d_13-14"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d13to14Neg": generate_conf(0.6,["real-3d_13-14-"],start=dtdt(2022,1,1)),
                        "y_1300-1400_real-7d13to14": generate_conf(0.6,["real-7d_13-14"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-2d14to15": generate_conf(0.6,["real-2d_14-15"],start=dtdt(2022,1,1)),
                        "y_1100-1745_real-3d14to15": generate_conf(0.6,["real-3d_14-15"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-4d14to15Neg": generate_conf(0.6,["real-4d_14-15-"],start=dtdt(2022,1,1)),
                        "y_1400-1500_real-7d14to15Neg": generate_conf(0.6,["real-7d_14-15-"],start=dtdt(2022,1,1)),
                        "y_1100-1745_real-3d15to16": generate_conf(0.6,["real-3d_15-16"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-4d15to16Neg": generate_conf(0.6,["real-4d_15-16-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-4d15to16Neg": generate_conf(0.6,["real-4d_15-16-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d16to18": generate_conf(0.6,["real-2d_16-18"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-1d11to13Neg": generate_conf(0.6,["real-1d_11-13-"],start=dtdt(2022,1,1)),
                        "y_0600-0800_real-2d11to13": generate_conf(0.6,["real-2d_11-13"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-2d11to13": generate_conf(0.6,["real-2d_11-13"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d11to13": generate_conf(0.6,["real-2d_11-13"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d11to13": generate_conf(0.6,["real-3d_11-13"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d11to13": generate_conf(0.6,["real-3d_11-13"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d11to13Neg": generate_conf(0.6,["real-3d_11-13-"],start=dtdt(2022,1,1)),
                        "y_1415-1745_real-1d12to14Neg": generate_conf(0.6,["real-1d_12-14-"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-2d12to14": generate_conf(0.6,["real-2d_12-14"],start=dtdt(2022,1,1)),
                        "y_1100-1415_real-3d12to14": generate_conf(0.6,["real-3d_12-14"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d12to14": generate_conf(0.6,["real-3d_12-14"],start=dtdt(2022,1,1)),
                        "y_0800-1400_real-3d12to14Neg": generate_conf(0.6,["real-3d_12-14-"],start=dtdt(2022,1,1)),
                        "y_1100-1745_real-3d14to17": generate_conf(0.6,["real-3d_14-17"],start=dtdt(2022,1,1)),
                        "y_0800-1200_real-4d14to17Neg": generate_conf(0.6,["real-4d_14-17-"],start=dtdt(2022,1,1)),

                        # 1315-1615 research
                        "y_1315-1615_CFSCO0z10to21autoV1": generate_conf(0.4,["diff_0Z_10-21_Prev1D-Prev3D_CFSCO"],start=dtdt(2022,1,1)),
                        "y_1315-1615_CFSM0zautoV1": generate_conf(0.4,["Diff2_0Z_2M_CFSM_GDD_US"],start=dtdt(2022,1,1)),
                        "y_1315-1615_CFSCO0z10to21autoV2": generate_conf(0.4,["diff_0Z_10-21_last-Prev3D_CFSCO"],start=dtdt(2022,1,1)),
                        "y_1315-1615_GEPS0z0dautoV1": generate_conf(0.4,["diff_0Z_0-0_last-Prev2_GEPS"],start=dtdt(2022,1,1)),
                        "y_1315-1615_PARA18z0dautoV1": generate_conf(0.4,["diff_18Z_0-0_last-Prev2_PARA"],start=dtdt(2022,1,1)),
                        "y_1315-1615_PARA0z0dautoV1": generate_conf(0.4,["diff_0Z_0-0_Prev1-Prev3_PARA"],start=dtdt(2022,1,1)),
                        "y_1315-1615_CFS18z10to21autoV1": generate_conf(0.4,["diff_18Z_10-21_Prev1D-Prev2D_CFS"],start=dtdt(2022,1,1)),
                        "y_1315-1615_CFSCO6z14to21autoV1": generate_conf(0.4,["diff_6Z_14-21_Prev1D-Prev3D_CFSCO"],start=dtdt(2022,1,1)),
                        "y_1315-1615_PARA18z0dautoV2": generate_conf(0.4,["diff_18Z_0-0_last-Prev1_PARA"],start=dtdt(2022,1,1)),
                        "y_1315-1615_CFS12z10to21autoV1": generate_conf(0.4,["diff_12Z_10-21_last-Prev2D_CFS"],start=dtdt(2022,1,1)),
                        "y_1315-1615_epsFcst6Zb": generate_conf(0.4,["GEFS_6Zb_pred_EnsTop2_window=5"],start=dtdt(2022,1,1)),
                        "y_1315-1615_GFSv1618z0dautoV1": generate_conf(0.4,["diff_18Z_0-0_last-Prev1_GFSv16"],start=dtdt(2022,1,1)),
                        "y_1315-1615_GEMCO0z0dautoV1": generate_conf(0.4,["diff_0Z_0-0_last-Prev2_GEMCO"],start=dtdt(2022,1,1)),

                        # Algo preds
                        "y_0800-1845_gefs_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gefs_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_paraco_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para8to16_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para8to16_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gefs0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_paraco0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_american0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_strict_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gefs_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_strict_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_strict_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_paraco_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_strict_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_american_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_ec_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_eps_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_strict_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_euro_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_full0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_full0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian0z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian12z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gemco_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gem_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_ec_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_eps_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_paraco_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_shorterm_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gefs_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_ec_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_eps_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_euro_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro12z_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_euro12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gem_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gemco_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_para_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_paraco_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_gefs_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_american_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american12z_daily_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_american12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_euro_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_canadian_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_4_100_d123": generate_conf(0.4,["prediction_y_0800-1845_american_4_100_d123"],start=dtdt(2022,7,1)),

                        "y_0800-1845_gefs6z_4_100_d123": generate_conf(0.4,["prediction_y_1200-1745_gefs6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para6z_4_100_d123": generate_conf(0.4,["prediction_y_1200-1745_para6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco6z_4_100_d123": generate_conf(0.4,["prediction_y_1200-1745_paraco6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para6z_midterm_4_100_d123": generate_conf(0.4,["prediction_y_1200-1745_para6z_midterm_4_100_d123"],start=dtdt(2022,7,1)),
                        ### loose
                        "y_0800-1845_gefs_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gefs_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_paraco_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para8to16_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para8to16_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gefs0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_paraco0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_american0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_strict_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gefs_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_strict_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_strict_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_paraco_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_strict_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_american_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_ec_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_eps_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_strict_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_euro_strict_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_full0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_full0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian0z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian0z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian12z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gemco_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gem_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_ec_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_eps_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_paraco_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_shorterm_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gefs_shorterm_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_ec_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_eps_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_euro_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro12z_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_euro12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gem_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gemco_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_para_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_paraco_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_gefs_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_american_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american12z_daily_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_american12z_daily_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_euro_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_euro_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_canadian_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_4_100_d123loose": generate_conf(0.72,["prediction_y_0800-1845_american_4_100_d123"],start=dtdt(2022,7,1)),

                        "y_0800-1845_gefs6z_4_100_d123loose": generate_conf(0.72,["prediction_y_1200-1745_gefs6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para6z_4_100_d123loose": generate_conf(0.72,["prediction_y_1200-1745_para6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco6z_4_100_d123loose": generate_conf(0.72,["prediction_y_1200-1745_paraco6z_4_100_d123"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para6z_midterm_4_100_d123loose": generate_conf(0.72,["prediction_y_1200-1745_para6z_midterm_4_100_d123"],start=dtdt(2022,7,1)),
                        # d04
                        "y_0800-1845_canadian12z_daily_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_canadian12z_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american12z_daily_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_american12z_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_daily_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_para_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_daily_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_eps_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_shorterm_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_paraco_shorterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_shorterm_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_para_shorterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_midterm_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_paraco_midterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_midterm_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_para_midterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_para_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para0z_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_para0z_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_gem_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_gemco_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_strict_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_american_strict_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_strict_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_gefs_strict_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_gefs_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_ec_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian0z_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_canadian0z_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_4_150_d04": generate_conf(0.4,["prediction_y_0800-1845_canadian12z_4_150_d04"],start=dtdt(2022,7,1)),
                        # d04 loose
                        "y_0800-1845_canadian12z_daily_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_canadian12z_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american12z_daily_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_american12z_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_daily_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_para_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_eps_daily_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_eps_daily_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_shorterm_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_paraco_shorterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_shorterm_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_para_shorterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_paraco_midterm_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_paraco_midterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_midterm_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_para_midterm_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_para_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_para0z_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_para0z_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gem_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_gem_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gemco_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_gemco_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_american_strict_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_american_strict_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_strict_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_gefs_strict_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_gefs_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_gefs_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_ec_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_ec_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian0z_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_canadian0z_4_150_d04"],start=dtdt(2022,7,1)),
                        "y_0800-1845_canadian12z_4_150_d04loose": generate_conf(0.7,["prediction_y_0800-1845_canadian12z_4_150_d04"],start=dtdt(2022,7,1)),

                        'y_0930-1100':{'start': march_start, 'chosen_ratio':0.5,
                                    'preds_for_comb': ['AM_Ens1', 'EPSpost_pred2b', 'GEFSL_PM_pred1_Prev1',
                                                       'diff_0Z_0-8_last-Prev2_GFSCO','diff_0Z_0-13_last-Prev1_GEFSLCO','GFS_Super_PM_pred2','PARA_pre6Z_pred1'],
                                                            'is_prod': False},
                             'y_1100-1200_b': {'start': april_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['AM_Ens1', 'EPSpost_pred2b', 'GEFSL_PM_pred1_Prev1',
                                                                'diff_0Z_0-8_last-Prev2_GFSCO', 'PARA_pre6Z_pred1'],'is_prod': False},
                              'y_0800-0930': {'start': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['GEFSL_PM_pred1_Prev1','diff_0Z_0-13_last-Prev1_GEFSLCO',
                                                                'EPSpost_pred2b','CFS_AM_pred1','EC_AM_Summer_pred1'],'is_prod': False},
                            'y_1615-1745':{'start': march_start, 'chosen_ratio':0.4,'preds_for_comb': ['PM_hybrid_pred2', 'PARA_PM_pred1', 'PM_Ens1', 'GFSEFS_PM_pred2', 'GEFSL_PM_pred1_Prev1'],'is_prod': False},
                            'y_1100-1200': {'star  t': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['PARA_6Z_pred1','GEFSL_PM_pred1_Prev1'],'is_prod': False},
                             'y_1100-1200_friday': {'start': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['AM_Ens1'],'is_prod': False}, # EPSpost_pred2b dominant more than CFS

                             'y_1200-1415_b': {'start': april_start, 'chosen_ratio': 0.7,
                                             'preds_for_comb': ['AM_Ens1', 'GEMCO_PM_pred2','PARA_6Z_pred1','PM_hybrid_pred2'],'is_prod': False}, # GFS_Super_PM_pred2
                             'y_1415-1745_b': {'start': april_start, 'chosen_ratio': 0.45,
                                               'preds_for_comb': ['CFS_AM_pred1', 'EPSpost_pred2b', 'GFSEFS_PM_pred2',
                                                                  'PM_hybrid_pred2'],'is_prod': False},  # GFS_Super_PM_pred2
                             'y_1200-1230_tu-we': {'start': march_start, 'chosen_ratio': 0.5,
                                                    'preds_for_comb': ['GEFSL_PM_pred1_Prev1', 'CFS_AM_pred1'],'is_prod': False},
                             'y_1200-1230_th': {'start': march_start, 'chosen_ratio': 0.5,
                                                   'preds_for_comb': ['GFSEFS_PM_pred2','PARA_6Z_pred1'],'is_prod': False},
                             'y_1200-1230_friday': {'start': march_start, 'chosen_ratio': 0.5,
                                                    'preds_for_comb': ['CFS_AM_Daily_pred1', 'PARA_PM_pred1',
                                                                       'CFS_AM_pred1', 'AM_Ens1', 'GFSEFS_PM_pred2'],'is_prod': False},
                             'y_1245-1845': {'start': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['CFS_AM_pred1', 'PM_hybrid_pred2', 'EPSpost_pred2b'],'is_prod': False},
                             'y_1245-1845_friday': {'start': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['PM_hybrid_pred2','CFS_AM_pred1','CFS_AM_Daily_pred1'],'is_prod': False},
                             'y_1245-1845_monday': {'start': march_start, 'chosen_ratio': 0.85, # 0.5 original
                                                    'preds_for_comb': ['CFS_AM_Daily_pred1','PM_hybrid_pred2','PM_Ens2', 'CFS_AM_pred1'],'is_prod': False},
                             'y_1245-1745' : {'start': march_start, 'chosen_ratio': 0.5,
                                             'preds_for_comb': ['CFS_AM_pred1', 'PM_hybrid_pred2', 'EPSpost_pred2b'],'is_prod': False},
                             'y_1245-1745_winter': {'start':nov_start, 'chosen_ratio': 0.7, 'preds_for_comb':['PM_hybrid_pred2'],'is_prod': False},

                            'y_1745-1845_mon': {'start': march_start, 'chosen_ratio':[0.4,0.4,0.5,0.4,0.4,0.4,0.4],
                                'preds_for_comb': ['diff_0Z_14-35_last-Prev2D_CFS','diff_0Z_0-2_last-Prev2_GEFSL',
                                                   'diff_0Z_0-13_last-Prev3_EPS','diff_12Z_0-16_last-Prev1_GEPS',
                                                   'diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_PARA','PARACO_PM_pred1'], 'is_prod': False},
                            'y_1845-1945_mon': {'start': march_start, 'chosen_ratio': [0.5, 0.5,0.5],
                                'preds_for_comb': ['diff_0Z_0-2_last-Prev2_GEFSL','diff_0Z_0-13_last-Prev3_EPS',
                                                   'diff_0Z_0-8_last-Prev1D_EC'], 'is_prod': False},
                             'y_1745-1845_tuesday': {'start': march_start, 'chosen_ratio': 0.4,
                                                    'preds_for_comb': ['AM_Ens1','PM_hybrid_pred2','EarlyPM_midweek'],'is_prod': False},
                            'y_1745-1845_012': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.65,0.35],
                                            'preds_for_comb': ['diff_0Z_0-2_last-Prev2_GFSCO',
                                                               'diff_6Z_0-10_last-Prev4D_PARACO'],
                                                'is_prod': False},
                             'y_1615-1745_tuesday': {'start': march_start, 'chosen_ratio': 0.5,
                                                     'preds_for_comb': ['GFSEFS_PM_pred2', 'GFS_Super_PM_pred2'],'is_prod': False},
                             'y_1100-1245_mon-tue': {'start': march_start, 'chosen_ratio': 0.6, # 0.4
                                                     'preds_for_comb': ['GEFSL_PM_pred1_Prev1','GEMCO_PM_pred1',
                                                                        'GFSEFS_PM_pred2'],'is_prod': False},
                             'y_1245-1615_tuesday': {'start': nov_start, 'chosen_ratio': 0.75,
                                                     'preds_for_comb': ['PM_Early_Ens3'],'is_prod': False}, # in winter looks like better to put EarlyPM first and then AM_Ens1 | 'CFS_AM_Daily_pred1'
                             'y_1245-1345_wednesday': {'start': march_start, 'chosen_ratio': 0.4,
                                                       'preds_for_comb': ['y_tag_bollinger_v1','y_tag_bollinger_v1_hack'],'is_prod': False},
                             'y_1315-1745_wednesday': {'start': nov_start, 'chosen_ratio': 0.5,
                                                                'preds_for_comb': ['y_tag_bollinger_v1_hack','GFSEFS_PM_pred2'],'is_prod': False},
                             'y_1000-1100_wednesday': {'start': nov_start, 'chosen_ratio': 0.5,
                                                                'preds_for_comb': ['EPSpost_pred2b','AM_Ens1'],'is_prod': False},
                            'y_1100-1315_wed': {'start': march_start, 'chosen_ratio': 0.4,
                                                                'preds_for_comb': ['GEMCO_PM_pred2','GEFSL_PM_pred1_Prev1','GFS_Super_PM_pred2','AM_Ens1'],'is_prod': False},
                             'y_1100-1315_thu': {'start': march_start, 'chosen_ratio': 0.8,
                                                 'preds_for_comb': ['GEMCO_PM_pred1','GEMCO_PM_pred2', 'GEFSL_PM_pred1_Prev1'],'is_prod': False},
                             'y_1145-1200_th-f': {'start': march_start, 'chosen_ratio': 0.6,
                                                 'preds_for_comb': ['EPSpost_pred2b'],'is_prod': False},
                             'y_1145-1200_tu-w': {'start': march_start, 'chosen_ratio': 0.6,
                                                'preds_for_comb': ['GFS_Super_PM_pred2'],'is_prod': False},
                             #### Prod confs
                             'y_1130-1315_024': {'start': march_start, 'chosen_ratio': 1,  ## GDD ONLY from April!
                                               'preds_for_comb': ['diff_0Z_0-2_last-Prev4_EC','diff_0Z_0-2_last-Prev2_EC','diff_0Z_0-2_last-Prev2_EPS'],
                                               'is_prod': True},
                             'y_1245-1315_b': {'start': april_start, 'chosen_ratio': 0.6,  ## CDD ONLY from April!
                                               'preds_for_comb': ['GFSEFS_PM_pred2', 'GFS_Super_PM_pred2'],
                                               'is_prod': True},

                             'y_1245-1415_b': {'start': april_start, 'chosen_ratio': 0.5,  ## CDD ONLY from April!
                                               'preds_for_comb': ['GFSEFS_PM_pred2', 'GEFSL_PM_pred1_Prev1'],
                                               'is_prod': True},
                             'y_1245-1615_b': {'start': april_start, 'chosen_ratio': 0.5,  ## CDD ONLY from April!
                                               'preds_for_comb': ['GFSEFS_PM_pred2', 'GEFSL_PM_pred1_Prev1'],
                                               'is_prod': True},
                             'y_1245-1415_midweek': {'start': april_start, 'chosen_ratio': 0.8,  ## CDD ONLY from April!
                                               'preds_for_comb': ['diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev2D_CFS'],
                                               'is_prod': True},
                             'y_1245-1615_c': {'start': april_start, 'chosen_ratio': 0.8,  ## CDD ONLY from April!
                                                     'preds_for_comb': ['CFS_AM_Daily_hybrid_pred1'],
                                                     'is_prod': True}, # tuesdays less significant, sometimes happens in 1130-1245
                             'y_1245-1615_fri': {'start': april_start, 'chosen_ratio': 0.8,  ## CDD ONLY from April!
                                                 'preds_for_comb': ['diff_0Z_14-28_last-Prev2D_CFS'],
                                                 'is_prod': True},
                             'y_1130-1230_b': {'start': april_start, 'chosen_ratio': 0.6,  ## HDD/GDD from April!
                                               'preds_for_comb': ['AM_Ens1', 'GEFSL_PM_pred1_Prev1'],
                                               'is_prod': True},
                             'y_1200-1230_b': {'start': april_start, 'chosen_ratio': 0.7,  ## HDD/GDD from April!
                                               'preds_for_comb': ['GEMCO_PM_pred2', 'GEFSL_PM_pred1_Prev1','AM_Ens1'],
                                               'is_prod': True},
                             'y_1315-1415_b': {'start': april_start, 'chosen_ratio': 0.4,  # GDD from April!
                                               'preds_for_comb': ['EPSpost_pred2b','GEFSL_PM_pred1_Prev1'],
                                               'is_prod': True},
                             'y_1415-1515_b': {'start': april_start, 'chosen_ratio': 0.35,  # GDD from April!
                                               'preds_for_comb': ['EPSpost_pred2b','Avg_PM_pred3D'],
                                               'is_prod': True},
                             'y_1315-1515_b': {'start': april_start, 'chosen_ratio': 0.4,  # GDD from April!
                                               'preds_for_comb': ['EPSpost_pred2b', 'Avg_PM_pred3D'],
                                               'is_prod': True},
                             'y_1000-1100_fri': {'start': nov_start, 'chosen_ratio': 0.5,  # GDD from April!
                                               'preds_for_comb': ['y_tag_bollinger_v1_hack'],
                                               'is_prod': True},
                             'y_1245-1415_fri': {'start': nov_start, 'chosen_ratio': 0.5,  # GDD from April!
                                                 'preds_for_comb': ['y_tag_bollinger_v1_hack'],
                                                 'is_prod': True},
                             'y_1315-1415_fri': {'start': march_start, 'chosen_ratio': 0.7,  # GDD from April!
                                                 'preds_for_comb': ['CFS_AM_Daily_hybrid_pred1','y_tag_bollinger_v1_hack'],
                                                 'is_prod': True},
                             'y_0600-0700': {'start': march_start, 'chosen_ratio': 0.6,  # GDD from April!
                                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev4_PARACO'],
                                                 'is_prod': True},
                             'y_1115-1200': {'start': march_start, 'chosen_ratio': 0.6,  # GDD from April!
                                             'preds_for_comb': ['diff_0Z_0-16_last-Prev2D_PARACO'],
                                             'is_prod': True},
                             'y_1245-1745_024': {'start': march_start, 'chosen_ratio': 0.75,  # 0.4 for 1245-1345  # GDD from April!
                             'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO', 'diff_0Z_0-13_last-Prev1D_EPS',
                                                'diff_0Z_0-16_last-Prev4D_PARA',
                                                'diff_0Z_0-16_last-Prev4D_GFSCO', 'diff_0Z_0-16_last-Prev2D_GEFSL',
                                                'diff_0Z_14-35_last-Prev4D_CFS'],
                             'is_prod': True},
                             'y_1245-1415_american4D': {'start': march_start, 'chosen_ratio': [0.5]*3+[0.2,0.6], #0.4 for 1245-1345  # GDD from April!
                                             'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_0-16_last-Prev4D_GEFS','diff_0Z_0-16_last-Prev4D_PARA',
                                                                'diff_0Z_0-13_last-Prev1D_EPS','MEAN'],
                                             'is_prod': True},
                            'y_1245-1415_american4Db': {'start': march_start, 'chosen_ratio': [0.5] * 2 + [0.35, 0.2, 0.4],
                                   # 0.4 for 1245-1345  # GDD from April!
                                   'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO',
                                                      'diff_0Z_0-16_last-Prev4D_GEFS', 'diff_0Z_0-16_last-Prev4D_PARA',
                                                      'diff_0Z_0-13_last-Prev1D_EPS', 'MEAN'],
                                   'is_prod': True},
                            'y_1245-1415_american4Dstrict': {'start': march_start, 'chosen_ratio': 0.3,  # 0.4 for 1245-1345  # GDD from April!
                                   'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO',
                                                      'diff_0Z_0-16_last-Prev4D_GEFS','diff_0Z_0-16_last-Prev4D_PARA',
                                                      'diff_0Z_0-13_last-Prev1D_EPS'],
                                   'is_prod': True},
                            'y_0800-1315_american18z4D': {'start': dtdt(2021,3,1), 'chosen_ratio': [0.6,0.5,0.4],  # 0.4 for 1245-1345  # GDD from April!
                                   'preds_for_comb': ['diff_18Z_8-16_last-Prev4D_GEFS','MEAN','diff_18Z_8-16_last-Prev4D_PARACO',],
                                   'is_prod': True},
                            'y_1245-1415_2': {'start': march_start, 'chosen_ratio': [0.65,0.5,0.4],  # 0.4 for 1245-1345  # GDD from April!
                                     'preds_for_comb': ['EPS_daily_hybrid_pred1','Cash_hybrid_pred1','y_tag_bollinger_v1_hack'],
                             'is_prod': True},
                            'y_1245-1515_mon': {'start': march_start, 'chosen_ratio': [0.6,0.5,0.5,0.5,0.3,0.3],  # 0.4 for 1245-1345  # GDD from April!
                                'preds_for_comb': ['GFSEFS_PM_pred2', 'CFS_AM_Daily_pred1','CFS_AM_Daily_hybrid_pred1','PM_hybrid_pred2','GFS_Super_PM_pred2','Avg_PM_pred3D'],
                                                'is_prod': True},
                            'y_1515-1745_mon': {'start': march_start, 'chosen_ratio': [0.6, 0.3, 0.3, 0.3],
                                # 0.4 for 1245-1345  # GDD from April!
                                    'preds_for_comb': ['PM_hybrid_pred2', 'Avg_PM_pred3D','GFS_Super_PM_pred2','EPS_daily_hybrid_pred1'],
                                        'is_prod': True},
                            'y_1130-1245_tue': {'start': march_start, 'chosen_ratio': [0.8,0.4, 0.4, 0.4],
                                # 0.4 for 1245-1345  # GDD from April!
                                    'preds_for_comb': ['ECGEM_AM_pred1','diff_0Z_0-13_last-Prev2_EC','GFSEFS_6Z_pred3_basic', 'CFS_AM_pred1'],
                            'is_prod': True},
                            'y_1245-1315_tue': {'start': march_start, 'chosen_ratio': [0.75,0.1,0.1,0.1,0.1],
                                # 0.4 for 1245-1345  # GDD from April!
                                'preds_for_comb': ['MEAN','CFS_AM_Daily_hybrid_pred1','diff_6Z_0-2_last-Prev1_PARACO',
                                                   'diff_6Z_0-2_last-Prev1_PARA','diff_0Z_0-2_last-Prev1_GEM'],
                                    'is_prod': True},
                            'y_1245-1300_1c': {'start': march_start, 'chosen_ratio': 0.5,
                            # 0.4 for 1245-1345  # GDD from April!
                            'preds_for_comb': ['diff_0Z_10-21_last-Prev3D_CFS','diff_0Z_8-16_last-Prev1D_GEFSL',
                                               'ECGEM_AM_pred1'],
                            'is_prod': True},

                            'y_1415-1745_0234': {'start': march_start, 'chosen_ratio': 0.75,  # 0.4 for 1245-1345  # GDD from April!
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO', 'diff_0Z_0-13_last-Prev1D_EPS',
                                                'diff_0Z_0-16_last-Prev4D_PARA',
                                                'diff_0Z_0-16_last-Prev4D_GFSCO', 'diff_0Z_0-16_last-Prev2D_GEFSL',
                                                'diff_0Z_14-35_last-Prev4D_CFS'],
                             'is_prod': True},
                            'y_1200-1300_mon': {'start': march_start, 'chosen_ratio': [0.6,0.6,0.4,0.3],  # GDD from April!
                                        'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARA', 'Cash_pred3',
                                                           'Cash_hybrid_pred1','diff_0Z_0-16_last-Prev3D_PARA'],
                            'is_prod': True},
                        'y_1615-1715_cash': {'start': march_start, 'chosen_ratio': 0.5,
                            'preds_for_comb': ['Cash_pred3'],
                            'is_prod': True},
                            'y_1315-1745' : {'start': march_start, 'chosen_ratio': 0.35,  # GDD from April!
                                    'preds_for_comb' : ['EPS_daily_hybrid_pred1'],
                                    'is_prod': True},
                            'y_1245-1315_1': {'start': march_start, 'chosen_ratio': 0.4,  # GDD from April!
                                'preds_for_comb': ['EPS45_rolling_diff3','CFS_AM_Daily_hybrid_pred1'],
                            'is_prod': True},
                        'y_1245-1415_1': {'start': march_start, 'chosen_ratio': 1,  # GDD from April!
                              'preds_for_comb': ['EPSpost_pred2b','CFS_AM_pred1'],
                                'is_prod': True},
                            'y_1315-1415_012': {'start': march_start, 'chosen_ratio': 0.4,  # GDD from April!
                                    'preds_for_comb': ['CFS_AM_pred1', 'EPSpost_pred2b', 'EPS_daily_hybrid_pred1','diff_0Z_0-13_last-Prev2_EPS','GEFS_PM_pred1'],
                                            'is_prod': True},
                            'y_1315-1415_2': {'start': march_start, 'chosen_ratio': 0.4,  # GDD from April!
                            'preds_for_comb': ['CFS_AM_pred1', 'EPSpost_pred2b', 'EPS_daily_hybrid_pred1',
                                               'diff_0Z_0-13_last-Prev2_EPS'],
                            'is_prod': True},
                            'y_1745-1845_13': {'start': march_start, 'chosen_ratio': [0.7,0.4,0.4,0.3,0.7],  # GDD from April!
                                    'preds_for_comb': ['EPSpost_pred2','AM_Ens1','diff_0Z_0-13_last-Prev2_EPS','EC_AM_Summer_pred1','PARA_AM_basic_pred1'],
                                        'is_prod': True},
                            'y_1745-1845_1': {'start': march_start, 'chosen_ratio': 0.65,  # GDD from April!
                                    'preds_for_comb': ['Avg_PM_pred3D','EC_AM_Summer_pred1','AM_Ens1', 'diff_0Z_0-13_last-Prev2_EPS',
                                                        'GEFSL_PM_pred1_Prev2','GEFS_PM_pred1','CFS_AM_pred1'],
                                            'is_prod': True},
                                'y_1130-1245_12': {'start': march_start, 'chosen_ratio': 1,  # GDD from April!
                                'preds_for_comb': ['GFSEFS_6Z_pred3_basic'],'is_prod': True},
                        'y_1145-1215_13': {'start': march_start, 'chosen_ratio': [0.5,0.3,0.3,0.3],  # GDD from April!
                           'preds_for_comb': ['GEFSL_PM_pred1_Prev1','EPS_daily_hybrid_pred1','ECGEM_AM_pred1','GEMCO_PM_pred2'], 'is_prod': True},
                        'y_1100-1230_123': {'start': march_start, 'chosen_ratio': 0.65,#0.4,  # GDD from April!
                           'preds_for_comb': ['EPS_daily_hybrid_pred1','GEFSL_PM_pred1_Prev1', 'ECGEM_AM_pred1','GEMCO_PM_pred2','GFSEFS_6Z_pred3_basic'], 'is_prod': True},
                    'y_1200-1245_6Zb': {'start': march_start, 'chosen_ratio': 0.65,
                            'preds_for_comb': ['GFSEFS_6Z_pred3_basic'], 'is_prod': True},
                    'y_1200-1245_6Zclean': {'start': march_start, 'chosen_ratio': [0.65],  
                           'preds_for_comb': ['GFSEFS_6Z_pred3_basic'], 'is_prod': True},
                    'y_1200-1245_6ZcleanStrict': {'start': march_start, 'chosen_ratio': [0.35],  
                           'preds_for_comb': ['GFSEFS_6Z_pred3_basic'], 'is_prod': True},
                    'y_1315-1615_PARA6Zp1': {'start': march_start, 'chosen_ratio': [0.2,0.4,0.03],  
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1_PARA',
                                                   'diff_6Z_0-16_last-Prev1_PARACO'], 'is_prod': True},
                    'y_1100-1230_PARA6Zp30to8': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_6Z_0-8_last-Prev3_PARA'], 'is_prod': True},
                    'y_1100-1230_PARA6Zp30to8Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                'preds_for_comb': ['diff_6Z_0-8_last-Prev3_PARA'], 'is_prod': True},
                    'y_1115-1245_PARA6Zp30to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev3_PARA','diff_6Z_8-16_last-Prev3_PARA',
                                                   ], 'is_prod': True},
                    'y_1115-1245_PARA6Zp30to16Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev3_PARA','diff_6Z_8-16_last-Prev3_PARA',
                                                   ], 'is_prod': True},
                    'y_1115-1245_PARA6Zp20to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev2_PARA','diff_6Z_8-16_last-Prev2_PARA',
                                                   ], 'is_prod': True},
                    'y_1100-1200_PARA6Zp28to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_6Z_8-16_last-Prev2_PARA'
                                                   ], 'is_prod': True},
                   'y_1100-1200_PARA6Zp214to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_6Z_14-16_last-Prev2_PARA'
                                                   ], 'is_prod': True},
                   'y_1200-1245_PARA6Zp40to16Neg': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev4_PARA-'
                                                   ], 'is_prod': True},
                    'y_1515-1615_PARA6Zp24Strict': {'start': march_start, 'chosen_ratio': 0.3,
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev2_PARA',
                                                   'diff_6Z_0-16_last-Prev4_PARA'], 'is_prod': True},
                    'y_1515-1715_GFSEFSCO0Zp20to16': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.6]+[0.1]*6,
                                'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_8-16_last-Prev2_PARACO',
                                                        'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_8-16_last-Prev2_PARA',
                                                        'diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_8-16_last-Prev2_GEFS',
                                                   ]
                                    , 'is_prod': True},
                    'y_1515-1745_PARACO0Zp20to16': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.4,0.45,0.45],
                                'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_8-16_last-Prev2_PARACO',
                                                   ]
                                    , 'is_prod': True},
                    'y_1515-1745_GEFS0Zp20to16': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.5,0.5,0.45],
                                'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_8-16_last-Prev2_GEFS']
                                    , 'is_prod': True},
                    'y_0800-1945_GEFS0Zp10to16': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev1_GEFS']
                                    , 'is_prod': True},
                    'y_0800-1945_GEFS0Zp20to16': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEFS']
                                    , 'is_prod': True},
                    'y_1745-1945_GEFS12Zp20to16': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev2_GEFS']
                                    , 'is_prod': True},
                    'y_1745-1945_GEFS12ZScan': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_GEFS',
                                                   'diff_12Z_0-16_last-Prev3_GEFS','diff_12Z_0-16_last-Prev4_GEFS'],
                                    'is_prod': True},
                    'y_1745-1945_GEFS12ZScanD': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev1D_GEFS','diff_12Z_0-16_last-Prev2D_GEFS',
                                                   'diff_12Z_0-16_last-Prev3D_GEFS','diff_12Z_0-16_last-Prev4D_GEFS'],
                                    'is_prod': True},
                    'y_0800-1945_GEFS0Zp20to16Strict': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.25,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEFS']
                                    , 'is_prod': True},
                    'y_1545-1715_PARA0Zp1': {'start': march_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev1_PARA']
                                    , 'is_prod': True},
                    'y_1545-1715_PARA0Zp2': {'start': march_start, 'chosen_ratio': [0.45,0.45,0.45],
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev2_PARA','diff_0Z_8-16_last-Prev2_PARA','MEAN']
                                    , 'is_prod': True},
                    'y_1315-1415_PARA0Zp3': {'start': march_start, 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-16_last-Prev3_PARA','MEAN']
                                    , 'is_prod': True},
                    'y_1315-1415_PARA0Zp3Strict': {'start': march_start, 'chosen_ratio': 0.33,
                                'preds_for_comb': ['diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-16_last-Prev3_PARA','MEAN']
                                    , 'is_prod': True},
                    'y_1315-1415_PARA0Zp3MEAN': {'start': march_start, 'chosen_ratio': [0.35,0.01,0.01],
                                'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-16_last-Prev3_PARA']
                                    , 'is_prod': True},
                    'y_0800-1745_PARA0Zp4': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev4_PARA']
                                    , 'is_prod': True},
                    'y_1545-1715_PARA0Zp4': {'start': march_start, 'chosen_ratio': [0.55,0.55,0.45],
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev4_PARA','diff_0Z_8-16_last-Prev4_PARA','MEAN']
                                    , 'is_prod': True},
                    'y_1100-1415_PARA6Zp12': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1_PARA','diff_6Z_0-16_last-Prev2_PARA'], 'is_prod': True},
                    'y_1100-1415_PARA6Zp120to10': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-10_last-Prev1_PARA','diff_6Z_0-10_last-Prev2_PARA'], 'is_prod': True},
                    'y_1100-1415_PARA6Zp34': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev3_PARA','diff_6Z_0-16_last-Prev4_PARA'], 'is_prod': True},
                    'y_1145-1415_PARACO6Zp12': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO'], 'is_prod': True},
                    'y_1145-1415_PARACO6Zp120to10': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-10_last-Prev1_PARACO','diff_6Z_0-10_last-Prev2_PARACO'], 'is_prod': True},
                    'y_1115-1215_PARACO6Zp20to10': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_6Z_0-10_last-Prev2_PARACO'], 'is_prod': True},
                    'y_1145-1415_PARACO6Zp34': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO'], 'is_prod': True},
                    'y_1315-1615_PARA6Zp1only': {'start': march_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev1_PARA'], 'is_prod': True},
                    'y_1245-1415_PARA6Zp18to16': {'start': march_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev1_PARA'], 'is_prod': True},
                    'y_1315-1615_PARA6Zp1onlyStrict': {'start': march_start, 'chosen_ratio': 0.25,
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev1_PARA'], 'is_prod': True},
                    'y_1230-1315_6ZPM': {'start': march_start, 'chosen_ratio': [0.45],
                                'preds_for_comb': ['GFSEFS_PM_pred1'], 'is_prod': True},
                    'y_1415-1645_6Z': {'start': march_start, 'chosen_ratio': 0.6,  
                           'preds_for_comb': ['GFSEFS_6Z_pred3_basic'], 'is_prod': True},

        'y_1100-1230_1c': {'start': march_start, 'chosen_ratio': [0.5]+[0.4]*5,  # GDD from April!
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev12_GEFSLCO','diff_0Z_10-21_last-Prev3D_CFS', 'ECGEM_AM_pred1','EPS_daily_hybrid_pred1',
                                               'GEMCO_PM_pred2', 'GFSEFS_6Z_pred3_basic'], 'is_prod': True},
        'y_1100-1130_1c': {'start': march_start, 'chosen_ratio': 0.45,
                           'preds_for_comb': ['PARACO_AM_0-8_pred1','diff_0Z_0-2_last-Prev12_GEM','diff_0Z_0-16_last-Prev1D_CFS'], 'is_prod': True},

                        'y_1415-1745_wed': {'start': march_start, 'chosen_ratio': 0.8,  # basically for spring, probably CFS will weaken in Summer, needs follow-up
                           'preds_for_comb': ['y_tag_bollinger_v1_hack','CFS_AM_Daily_hybrid_pred1','GFSEFS_6Z_pred3_basic','EPS_PM_pred1'], 'is_prod': True},
                        'y_1415-1745_wed3': {'start': march_start, 'chosen_ratio': [0.6,0.6,0.5,0.4,0.3,0.3],
                            # basically for spring, probably CFS will weaken in Summer, needs follow-up
                            'preds_for_comb': ['y_tag_bollinger_v1_hack', 'CFS_AM_Daily_hybrid_pred1','CFS_AM_pred1','EPSL_daily_hybrid_pred4','GFSEFS_6Z_pred3_basic', 'EPS_PM_pred1'], 'is_prod': True},
                        'y_1645-1745_wed3': {'start': march_start, 'chosen_ratio': [0.5, 0.4, 0.4, 0.3, 0.3],
                             # basically for spring, probably CFS will weaken in Summer, needs follow-up
                             'preds_for_comb': ['CFS_AM_Daily_hybrid_pred1','EPS_daily_hybrid_pred1','CFS_AM_pred1',
                                                'GFSEFS_6Z_pred3_basic', 'EPS_PM_pred1'],'is_prod': True},
                        'y_1415-1745_wed2': {'start': march_start, 'chosen_ratio': 0.75, #[0.65,0.5,0.5,0.5],
                                        # basically for spring, probably CFS will weaken in Summer, needs follow-up
                                            'preds_for_comb': ['y_tag_bollinger_v1_hack', 'CFS_AM_Daily_hybrid_pred1',
                                                   'GFSEFS_6Z_pred3_basic', 'EPS_PM_pred1'], 'is_prod': True},
                        'y_1415-1745_wed3dailies': {'start': march_start, 'chosen_ratio': [0.4,0.4,0.4,0.3,0.3,0.3,0.3],
                                                     # basically for spring, probably CFS will weaken in Summer, needs follow-up
                                                     'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO', 'diff_0Z_0-16_last-Prev1D_GEFS',
                                                            'diff_0Z_0-16_last-Prev1D_GEFSL','diff_0Z_14-35_last-Prev1D_CFS', 'diff_0Z_0-16_last-Prev1D_GFSCO',
                                                            'diff_0Z_0-16_last-Prev1D_GEPS', 'diff_0Z_0-16_last-Prev1D_GEMCO'],
                                                     'is_prod': True},
                            'y_1245-1415_wed4dailies': {'start': march_start, 'chosen_ratio': [0.42,0.35], #5,4#,0.3,0.3,0.3],
                                    'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARA',
                                                       'diff_0Z_0-8_last-Prev4D_EC'],
                                                    #,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                                    'is_prod': True},
                    'y_1215-1245_124': {'start': march_start, 'chosen_ratio': 0.4,  # [0.5,0.4], #5,4#,0.3,0.3,0.3],

                                    'preds_for_comb': ['CFS_AM_pred1'],
                                    # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                                    'is_prod': True},
                    'y_1215-1245_124b': {'start': march_start, 'chosen_ratio': 0.4,  # [0.5,0.4], #5,4#,0.3,0.3,0.3],

                            'preds_for_comb': ['CFS_AM_Daily_hybrid_pred1'],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
                    'y_1615-1745_cfs': {'start': march_start, 'chosen_ratio': [0.65,0.3],  # [0.5,0.4], #5,4#,0.3,0.3,0.3],

                            'preds_for_comb': ['CFS_AM_pred1','CFS_PM_Daily_hybrid_pred2'],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
                'y_1615-1745_234': {'start': march_start, 'chosen_ratio': [0.4,0.4,0.3,0.3,0.4],
                            'preds_for_comb': ['diff_0Z_8-16_last-Prev1D_PARACO','MEAN',
                                               'diff_6Z_0-13_last-Prev3_CFS','diff_0Z_0-21_last-Prev4_CFS',
                                               'diff_0Z_0-2_last-Prev1_EC'],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
                'y_1415-1645_23': {'start': march_start, 'chosen_ratio': [0.4,0.25, 0.4, 0.25],  # [0.4,0.3,0.3,0.4],
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_8-16_last-Prev1D_PARACO','MEAN',
                                               'diff_0Z_0-2_last-Prev1_EC',
                                               ],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
        'y_1315-1745_234new': {'start': march_start, 'chosen_ratio': [0.5, 0.4, 0.25],  # [0.4,0.3,0.3,0.4],
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO', 'diff_0Z_0-2_last-Prev1_EC',
                                               'MEAN'
                                               ],
                            'is_prod': True},
        'y_1145-1215_c': {'start': march_start, 'chosen_ratio': 0.4,  # [0.4,0.3,0.3,0.4],
                            'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS', 'MEAN',
                                                'diff_0Z_0-2_last-Prev1_EC','diff_0Z_0-21_last-Prev4_CFS','diff_0Z_0-16_last-Prev1D_PARACO'],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
        'y_1145-1300_ECcash': {'start': march_start, 'chosen_ratio': [0.4],  # [0.4,0.3,0.3,0.4],
                          'preds_for_comb': ['diff_0Z_0-2_last-Prev1_EC'],
                          # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                          'is_prod': True},
                    'y_1345-1415_cfsMean': {'start': march_start, 'chosen_ratio': 0.65,  # [0.65,0.3],  # [0.5,0.4], #5,4#,0.3,0.3,0.3],

                            'preds_for_comb': ['CFS_AM_pred1', 'CFS_PM_Daily_hybrid_pred2'],
                            # ,'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO','diff_0Z_14-35_last-Prev4D_CFS'],
                            'is_prod': True},
                    'y_0800-0930_13': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.5,0.3,0.4,0.4],

                             'preds_for_comb': ['diff_0Z_0-13_last-Prev1_GEFSLCO','GEFS_AM_basic_pred1','diff_0Z_0-16_last-Prev1_GEFSL',
                                                'EPSpost_pred2b','mix_0600-0700_pred1',
                                                'PARACO_AM_basic_pred1'], 'is_prod': True},
                    'y_0800-0930_13b': {'start': march_start, 'chosen_ratio': [0.5, 0.5, 0.5, 0.3, 0.4, 0.4, 0.4],

                           'preds_for_comb': ['diff_0Z_0-13_last-Prev1_GEFSLCO', 'GEFS_AM_basic_pred1',
                                              'diff_0Z_0-16_last-Prev1_GEFSL',
                                              'EPSpost_pred2b','GFSCO_daily_hybrid_pred1', 'mix_0600-0700_pred1',
                                              'PARACO_AM_basic_pred1'], 'is_prod': True},
                    'y_0800-1000_13c': {'start': march_start, 'chosen_ratio':0.6,

                            'preds_for_comb': ['diff_0Z_8-16_last-Prev1_GEFSL','diff_0Z_9-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev1_GEFSLCO','diff_0Z_9-13_last-Prev4_EPS', 'GEFS_AM_basic_pred1'], 'is_prod': True},
                    'y_0800-1000_1c': {'start': march_start, 'chosen_ratio': [0.6]+[0.3]*5,#[0.6]+[0.4]*3+[0.1,0.1],
                            'preds_for_comb': ['MEAN','CFS_AM_Daily_pred1', 'diff_0Z_9-13_last-Prev1_EPS','EPS45_pred2',
                                               'GEFSL_PM_pred1_Prev1','diff_0Z_10-21_last-Prev3D_CFS'],#,'diff_0Z_0-16_last-Prev4_GFSCO'],#'GEFS_AM_basic_pred1'],
                                               'is_prod': True},
                        'y_0800-1200_1c': {'start': march_start, 'chosen_ratio': 0.5,

                            'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS','diff_0Z_0-16_last-Prev12_GEFSLCO',
                                               'diff_0Z_10-21_last-Prev3D_CFS'], 'is_prod': True},
                        'y_0800-1200_3c': {'start': march_start, 'chosen_ratio': 0.5, #0.5,

                           'preds_for_comb': ['diff_0Z_8-16_last-Prev1D_GEFSL','GEFS_pre6Z_pred2', #'diff_0Z_0-16_last-Prev12_GEFSLCO',
                                              'diff_0Z_10-21_last-Prev3D_CFS','diff_0Z_9-13_last-Prev1_EPS'], 'is_prod': True},
                        'y_1000-1200_3c': {'start': march_start, 'chosen_ratio': 0.5,

                            'preds_for_comb': ['diff_0Z_0-13_last-Prev1_GEFSLCO','GEFS_AM_basic_pred1','PARACO_AM_pred3'], 'is_prod': True},
                        'y_1000-1200_1c': {'start': march_start, 'chosen_ratio': 0.5,

                           'preds_for_comb': ['EPS_daily_hybrid_pred2','EPSL_daily_hybrid_pred4'], 'is_prod': True},
                    'y_0930-1100_1b': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.3,0.3],

                           'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS','CFS_AM_pred1','EPSpost_pred2b','EPS_daily_hybrid_pred2'], 'is_prod': True},
                        'y_0800-1100_c': {'start': march_start, 'chosen_ratio': 0.35,#[0.5, 0.5,0.4],

                           'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS', 'EPSpost_pred2b','diff_0Z_9-13_last-Prev2_EPS'], 'is_prod': True},
                        'y_1745-1845_12Z': {'start': march_start, 'chosen_ratio': [0.25,0.4,0.25],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS','diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev1_GEMCO'], 'is_prod': True},
                        'y_1745-1945_12Zb': {'start': march_start, 'chosen_ratio': 0.4,#[0.65, 0.4,0.4,0.4, 0.4],

                            'preds_for_comb': ['diff_12Z_8-16_last-Prev1_GEPS', 'diff_12Z_8-16_last-Prev1_GEFS',
                                               'diff_0Z_0-2_last-Prev2_GEFSL','diff_12Z_0-16_last-Prev1_GEPS',
                                               'diff_12Z_8-16_last-Prev2_GEPS',
                                               'diff_12Z_0-16_last-Prev4_PARACO'], 'is_prod': True},
                            'y_1745-1945_12Zb2': {'start': march_start, 'chosen_ratio': [0.75]+[0.5]*6,

                             'preds_for_comb': ['MEAN','diff_12Z_8-16_last-Prev1_GEPS', 'diff_12Z_8-16_last-Prev1_GEFS',
                                                'EPSpost_pred2.2',
                                                'diff_0Z_0-2_last-Prev2_GEFSL', 'diff_12Z_0-16_last-Prev1_GEPS',
                                                'diff_12Z_0-16_last-Prev24_PARACO'
                                                ], 'is_prod': True},
                            'y_1745-2045_12Zb124': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.5,0.5,0.75,0.5],

                              'preds_for_comb': ['EPSpost_pred2.2','diff_12Z_8-16_last-Prev1_GEFS',
                                                 'diff_0Z_0-16_last-Prev1_GEFSL',
                                                'diff_0Z_0-2_last-Prev2_GEFSL','MEAN',
                                                 'diff_12Z_0-16_last-Prev1_GEPS',
                                                 ], 'is_prod': True},
                        'y_1745-2045_12Zb124b': {'start': march_start, 'chosen_ratio': [0.5, 0.5, 0.5, 0.75, 0.5],

                                'preds_for_comb': ['EPSpost_pred2.2', 'diff_12Z_8-16_last-Prev1_GEFS',
                                                   'diff_0Z_0-2_last-Prev2_GEFSL', 'MEAN',
                                                   'diff_12Z_0-16_last-Prev1_GEPS',
                                                   ], 'is_prod': True},
                        'y_1815-1915_34EC': {'start': august20_start, 'chosen_ratio': [0.6,0.4],  # [0.65, 0.4,0.4,0.4, 0.4],

                             'preds_for_comb': ['diff_0Z_0-8_last-Prev1_EC','EPSpost_pred2.2'], 'is_prod': True},
                        'y_1815-1945_12ECGEM': {'start': august20_start, 'chosen_ratio': [0.45],  # [0.65, 0.4,0.4,0.4, 0.4],

                             'preds_for_comb': ['ECGEM_AM_pred1'], 'is_prod': True},
                        'y_1315-1745_ec12zfcst': {'start': april_start, 'chosen_ratio': 0.4,  # [0.65, 0.4,0.4,0.4, 0.4],

                             'preds_for_comb': ['diff_0Z_0-8_last-Prev3_EC-', 'diff_12Z_5-8_last-Prev3_EC', 'diff_12Z_0-8_Prev1-Prev2_EC-', 'diff_12Z_0-13_last-Prev1_EC', 'diff_0Z_0-13_last-Prev1_EC-', 'diff_12Z_5-8_last-Prev1_EC', 'diff_0Z_0-13_Prev1-Prev2_EC', 'diff_0Z_5-8_last-Prev3_EC-', 'diff_0Z_5-8_Prev1-Prev2_EC', 'diff_0Z_0-8_last-Prev1_EC-', 'diff_12Z_5-8_Prev1-Prev2_EC-', 'diff_0Z_5-8_last-Prev1_EC-'
                                                ], 'is_prod': True},
                        'y_1745-1945_12Zc': {'start': march_start, 'chosen_ratio': 0.4,#0.4,  # [0.65, 0.4,0.4,0.4, 0.4],

                             'preds_for_comb': ['diff_0Z_0-2_last-Prev2_GEFSL','diff_12Z_8-16_last-Prev1_GEPS', 'diff_12Z_8-16_last-Prev1_GEFS',
                                                'diff_12Z_0-16_last-Prev1_GEPS',
                                                'diff_12Z_8-16_last-Prev2_GEPS',
                                                'diff_12Z_0-16_last-Prev4_PARACO'], 'is_prod': True},
                    'y_1745-1945_12Ze': {'start': march_start, 'chosen_ratio': 0.65,

                             'preds_for_comb': ['diff_0Z_0-2_last-Prev2_GEFSL','diff_12Z_8-16_last-Prev1_GEFS',
                                                'diff_12Z_8-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev1_GEPS',
                                                'diff_12Z_8-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev4_PARACO'], 'is_prod': True},
                    'y_1745-2045_GEFS12Z': {'start': april_start, 'chosen_ratio': [0.5, 0.5, 0.6],  # [0.5, 0.5,0.4],

                               'preds_for_comb': ['diff_12Z_8-16_last-Prev1_PARACO','diff_12Z_8-16_last-Prev1_GEFS',
                                                  'MEAN'], 'is_prod': True},
                    'y_1815-1945_GEFS12Zp1': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.65,0.5],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_8-16_last-Prev1_GEFS'],
                                                   'is_prod': True},
                    'y_0800-1945_GEFS12Zp1': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.48],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEFS'],
                                                   'is_prod': True},
                    'y_0800-1945_GEFS12Zp2': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.48],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev2_GEFS'],
                                                   'is_prod': True},
                    'y_0800-1945_GEFS12Zp3': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.48],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev2_GEFS'],
                                                   'is_prod': True},
                    'y_0800-1945_GEFS12Zp4': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.48],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev2_GEFS'],
                                                   'is_prod': True},
                    'y_1615-1730_PARACO12zp2b': {'start': april_start, 'chosen_ratio': [0.55,0.55,0.2],  # [0.5, 0.5,0.4],

                               'preds_for_comb': ['diff_12Z_8-16_last-Prev2_PARACO','MEAN',
                                                  'diff_12Z_0-16_last-Prev2_PARACO'], 'is_prod': True},
                    'y_1300-1315_PARA12zp28to16': {'start': april_start, 'chosen_ratio': [0.55],
                               'preds_for_comb': ['diff_12Z_8-16_last-Prev2_PARA'], 'is_prod': True},
                    'y_1900-1945_PARA12zp20to10': {'start': april_start, 'chosen_ratio': [0.45,0.4,0.33],
                               'preds_for_comb': ['diff_12Z_0-10_last-Prev2_PARA','MEAN',
                                                  'diff_12Z_0-16_last-Prev2_PARA'], 'is_prod': True},
                    'y_1845-2030_GEFS12Z': {'start': march_start, 'chosen_ratio': [0.6,0.25,0.4],
                                'preds_for_comb': ['diff_12Z_0-16_last-Prev2_GEFS', 'diff_12Z_0-16_last-Prev4_GEFS',
                                                   'MEAN'], 'is_prod': True},
                    'y_1745-1845_GEPS12Z': {'start': august20_start, 'chosen_ratio': [0.3,0.4,0.3,0.5,0.3],
                                'preds_for_comb': ['diff_12Z_8-16_last-Prev4_GEPS', 'diff_12Z_8-16_last-Prev3_GEPS',
                                                   'diff_12Z_8-16_last-Prev2_GEPS','MEAN','diff_12Z_0-16_last-Prev1_GEPS'],
                                                'is_prod': True},
                        'y_1815-1900_GEPS12Zp1': {'start': august20_start, 'chosen_ratio': 0.35,
                                  'preds_for_comb': ['diff_12Z_0-15_last-Prev1_GEPS','diff_12Z_0-15_last-Prev3_GEPS'
                                                     ],
                                  'is_prod': True},
                        'y_0800-1745_GEPS0Zp1': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.6,
                                  'preds_for_comb': ['diff_0Z_0-15_last-Prev1_GEPS'],
                                  'is_prod': True},
                        'y_0800-1745_GEPS0Zp2': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.6,
                                  'preds_for_comb': ['diff_0Z_0-15_last-Prev2_GEPS'],
                                  'is_prod': True},
                        'y_0800-1745_GEPS0Zp4': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_0Z_0-15_last-Prev4_GEPS'],
                                  'is_prod': True},
                        'y_0800-1945_GEMCO0Zp1': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_0Z_0-13_last-Prev1_GEMCO'],
                                  'is_prod': True},
                        'y_0800-1945_GEMCO0Zp2': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_0Z_0-13_last-Prev2_GEMCO'],
                                  'is_prod': True},
                        'y_0800-1945_GEMCO0Zp4': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_0Z_0-13_last-Prev4_GEMCO'],
                                  'is_prod': True},
                        'y_0630-0730_GEPS0Zp1': {'start': august20_start, 'chosen_ratio': 0.6,
                                  'preds_for_comb': ['diff_0Z_0-15_last-Prev1_GEPS','diff_0Z_8-16_last-Prev1_GEPS'],
                                  'is_prod': True},
                        'y_0600-0700_GEPS0Zp2': {'start': august20_start, 'chosen_ratio': [0.5,0.4,0.4,0.5],
                                  'preds_for_comb': ['GEPS_PM_pred1','diff_0Z_0-13_last-Prev2_GEPS','diff_0Z_0-15_last-Prev2_GEPS',
                                                    'MEAN'
                                                     ],
                                  'is_prod': True},
                        'y_1845-1900_GEPS12Zp4': {'start': august20_start, 'chosen_ratio': [0.4,0.3],
                                'preds_for_comb': ['diff_12Z_0-15_last-Prev4_GEPS','diff_12Z_0-15_last-Prev2_GEPS',
                                                   ],
                                'is_prod': True},
                    'y_0400-0700_GEPS12Zp2': {'start': march_start, 'chosen_ratio': [0.4,0.2],
                                  'preds_for_comb': ['diff_12Z_0-15_last-Prev2_GEPS','diff_12Z_0-15_last-Prev4_GEPS'
                                                     ],
                                  'is_prod': True},
                    'y_1100-1400_GEPSCO12Zp30to8': {'start': march_start, 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_12Z_0-8_last-Prev3_GEPS'
                                                     ],
                                  'is_prod': True},
                    'y_0000-0800_GEPSCO12Zcomb': {'start': march_start, 'chosen_ratio': [0.44]+[0.1]*6,
                                  'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev3_GEPS',
                                         'diff_12Z_0-16_last-Prev1_GEMCO','diff_12Z_0-16_last-Prev2_GEMCO','diff_12Z_0-16_last-Prev3_GEMCO'],
                                  'is_prod': True},
                    'y_0000-0800_GEPSCO12ZcombStrict': {'start': march_start, 'chosen_ratio': [0.28]+[0.01]*6,
                                  'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev3_GEPS',
                                         'diff_12Z_0-16_last-Prev1_GEMCO','diff_12Z_0-16_last-Prev2_GEMCO','diff_12Z_0-16_last-Prev3_GEMCO'],
                                  'is_prod': True},
                    'y_1330-1415_GEPSam': {'start': march_start, 'chosen_ratio': [0.4,0.3, 0.2],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GEPS','diff_0Z_0-16_last-Prev123_GEPS','GEPS_AM_basic_pred1'
                                                     ],
                                  'is_prod': True},
                    'y_0630-0800_GEPSamloose': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.4,0.4,0.4, 0.3],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GEPS','diff_0Z_0-16_last-Prev123_GEPS',
                                                     'diff_0Z_8-16_last-Prev1_GEPS','GEPS_AM_basic_pred1'
                                                     ],
                                  'is_prod': True},
                    'y_0630-0730_GEPSam2': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.45,0.4,0.45],
                                 'preds_for_comb': ['MEAN','diff_0Z_0-15_last-Prev4_GEPS','diff_0Z_0-15_last-Prev1_GEPS'],
                                 'is_prod': True},
                   'y_0800-1200_GEPS0Z12': {'start': march_start, 'chosen_ratio': [0.5,0.35,0.4,
                                                                                   0.3],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev1_GEPS',
                                                     'diff_0Z_8-16_last-Prev1D_GEPS',
                                                    'diff_12Z_0-10_last-Prev1D_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp1': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.45],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev1_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp2': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.45],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp1Strict': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.25],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev1_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp2Strict': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.25],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp3': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.45],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev3_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp3Strict': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.3],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev3_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp4': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.45],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GEPS'
                                                     ],
                                  'is_prod': True},
                   'y_0800-1845_GEPS0Zp4Strict': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.3],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GEPS'
                                                     ],
                                  'is_prod': True},
                    'y_1115-1815_GEPSpm': {'start': march_start, 'chosen_ratio': [0.5,0.3],
                               'preds_for_comb': ['GEPS_PM_pred1','GEM_AM_pred1'
                                                  ],
                               'is_prod': True},
                     'y_1715-1915_GEFSComb': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.6,0.5,0.4,0.4,0.4],

                                'preds_for_comb': ['MEAN','diff_0Z_0-10_last-Prev2D_GEFS',
                                                   'diff_0Z_0-2_last-Prev4_GEFS','diff_0Z_0-10_Prev1D-Prev2D_GEFS','diff_0Z_0-0_last-Prev4_GEFS'], 'is_prod': True},
                    'y_1815-1945_GEFS0to8': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6,0.3],

                                 'preds_for_comb': ['GEFS_AM_0-8_pred1','diff_0Z_0-16_last-Prev2_GEFS'], 'is_prod': True},
                     'y_0600-0800_GEFS0zCombFull': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.44] + [0.05] * 8,
                                                    'preds_for_comb':
                                                        ['MEAN',
                                                         'diff_0Z_8-16_last-Prev1_GEFS','diff_0Z_8-16_last-Prev2_GEFS',
                                                         'diff_0Z_8-16_last-Prev3_GEFS','diff_0Z_8-16_last-Prev4_GEFS',
                                                         'diff_0Z_0-8_last-Prev1_GEFS','diff_0Z_0-8_last-Prev2_GEFS',
                                                         'diff_0Z_0-8_last-Prev3_GEFS','diff_0Z_0-8_last-Prev4_GEFS'
                                                         ],
                                                    'is_prod': True},

                         'y_0600-0800_GEFS0zComb': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.5,0.4,0.3,0.3],
                                'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_8-16_last-Prev1_GEFS',
                                                   'diff_0Z_0-16_last-Prev4_GEFS'], 'is_prod': True},
                    'y_0800-1945_GEFS0z0to8': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_GEFS','diff_0Z_0-8_last-Prev4_GEFS'], 'is_prod': True},
                    'y_0600-0800_GEFS0z0to8p34': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev3_GEFS','diff_0Z_0-8_last-Prev4_GEFS'], 'is_prod': True},
                    'y_1100-1200_GEFS0z0to8': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.4,
                                'preds_for_comb': ['diff_0Z_0-8_last-Prev1_GEFS','diff_0Z_0-8_last-Prev2_GEFS'], 'is_prod': True},
                    'y_1230-1315_GEFS0zComb2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.44,
                                'preds_for_comb': ['GEFS_AM_basic_pred1'], 'is_prod': True},
                    'y_0800-1315_GEFS0z0to8Prev1d': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_0Z_0-8_last-Prev1_GEFS-1d-'], 'is_prod': True},
                    'y_1445-1645_GEFS12z0to8Prev1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.45,
                                'preds_for_comb': ['diff_12Z_0-8_Prev1-Prev3_GEFS'], 'is_prod': True},
                    'y_1315-1415_GEFS12z0to8p2': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.55,
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev2_GEFS','diff_18Z_0-8_Prev1-Prev3_GEFS'], 'is_prod': True},
                    'y_1715-1745_GEFS12z0to8p2clean': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.55,
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev2_GEFS'], 'is_prod': True},
                     'y_1715-1915_GEPSComb': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6,0.5,0.4,0.3],

                                 'preds_for_comb': [
                                                    #'diff_12Z_0-10_Prev1D-Prev2D_GEPS','diff_0Z_0-13_Prev1-Prev2_GEPS',
                                                    #'diff_0Z_0-8_Prev1-Prev3_GEPS',
                                                    'diff_12Z_0-10_last-Prev1D_GEPS','diff_12Z_0-13_last-Prev1_GEPS',
                                                    'diff_12Z_0-8_last-Prev2_GEPS',
                                                    'MEAN'
                                                    ], 'is_prod': True},
                        'y_1715-1915_GEPSComb2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.6,0.5,0.4,0.3],
                                 'preds_for_comb': [
                                                    'diff_12Z_0-10_last-Prev1D_GEPS','diff_12Z_0-13_last-Prev1_GEPS',
                                                    'diff_12Z_0-8_last-Prev2_GEPS',
                                                    'MEAN'
                                                    ], 'is_prod': True},
                        'y_1315-1415_GEPSComb3': {'start': dtdt(2020,7, 1), 'chosen_ratio': [0.5,0.5,0.5,0.3],
                                 'preds_for_comb': ['MEAN',
                                                    'diff_12Z_0-15_last-Prev2_GEPS','diff_12Z_0-8_last-Prev2_GEPS',
                                                    'diff_12Z_0-2_last-Prev2_GEPS',
                                                    ], 'is_prod': True},
                        'y_0800-1845_GEPSCOCombD': {'start': dtdt(2020,7, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['GEPS_daily_hybrid_pred3','diff_0Z_0-16_Prev1D-Prev3D_GEMCO',
                                                    'diff_0Z_0-16_last-Prev2D_GEPS',
                                                    'diff_0Z_0-16_last-Prev3D_GEMCO', 'diff_0Z_0-16_last-Prev4D_GEMCO'],
                                                    'is_prod': True},
                        'y_1745-1845_12ZMon': {'start': march_start, 'chosen_ratio': [0.25, 0.4,0.25, 0.25],  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS', 'diff_12Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev1_GEPS',
                                               'diff_12Z_0-16_last-Prev1_GEMCO'], 'is_prod': True},
                        'y_0600-0700_c': {'start': march_start, 'chosen_ratio': [0.4, 0.6, 0.3],  # [0.5, 0.5,0.4],

                                   'preds_for_comb': ['diff_0Z_0-13_Prev1-Prev2_EPS', 'mix_0600-0700_pred2','diff_0Z_0-8_Prev1-Prev2_EC'], 'is_prod': True},
        'y_0800-1200_d': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.6, #[0.5,0.4,0.3,0.4,0.4,0.4],  # 0.4[0.5, 0.5,0.4],

                          'preds_for_comb': ['EPSpost_pred2b','EC_AM_Summer_pred2','EPS_daily_hybrid_pred1','GEFSL_PM_pred1_Prev1','ECGEM_AM_pred1','GFSEFS_PM_pred2'], 'is_prod': True},
        'y_0800-1200_g': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.4,0.4,0.5,0.7],#[0.4,0.3,0.3,0.4,0.6],
                          # [0.5,0.4,0.3,0.4,0.4,0.4],  # 0.4[0.5, 0.5,0.4],

                          'preds_for_comb': ['GEFSL_PM_pred1_Prev1', 'EPS_PM_pred1','diff_0Z_0-14_last-Prev4_EPS',
                                             'diff_0Z_14-21_last-Prev1_CFS','MEAN'
                                             ],'is_prod': True},
        'y_1300-1330_fri': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.3,0.3,0.3,0.2,0.2],#[0.35,0.2],#[0.4,0.2,0.2],
                          'preds_for_comb': ['MEAN','diff_0Z_14-28_last-Prev112_EPS45',
                                             'diff_0Z_14-35_last-Prev2D_EPS45',
                                             'diff_0Z_14-35_last-Prev4D_EPS45',
                                             'diff_0Z_14-35_last-Prev3D_EPS45',
                                             ], 'is_prod': True},
                                                     # 3
        'y_1315-1745_americanV3old': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5]*4+[0.6], # 0.3,#
                                   'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_GEFS','PARACO_PM_pred2D',
                                                      'diff_6Z_0-16_last-Prev2D_GEFS',
                                                      'CFS_PM_Daily_hybrid_pred2',
                                                      'MEAN'
                                                      ], 'is_prod': True},
        'y_1315-1745_americanV3b': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5] * 3 + [0.6],  # 0.3,#
                                      'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_GEFS', 'PARACO_PM_pred2D',
                                                         'CFS_PM_Daily_hybrid_pred2',
                                                         'MEAN'
                                                         ], 'is_prod': True},
        'y_1415-1615_americanSeasonal': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,#[0.65,0.45,0.45,0.35,0.75],#[0.5] * 3,
                                    'preds_for_comb': ['MEAN','Value_0Z_0-16D_GEPS_rs',
                                                        'Value_0Z_0-16D_GEFS_rs',
                                                        'Value_0Z_0-16D_PARACO_rs',
                                                       'Value_0Z_0-16D_PARA_rs'
                                                       ], 'is_prod': True},
        'y_0800-1845_americanV4': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.6,#[0.5]+[0.5]*4, #+# [0.6],  # 0.3,#
                                   'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev1D_GEFS',
                                                      'PARACO_PM_pred2D',
                                                      'diff_0Z_0-16_last-Prev2D_GEFS','CFS_PM_Daily_hybrid_pred2',

                                                      ], 'is_prod': True},
        'y_0800-1100_cfsHDD': {'start': dtdt(2019, 9, 1), 'chosen_ratio': [0.6,0.6,0.7], #,0.6,0.4,0.75],  # 0.3,#
                                   'preds_for_comb': ['diff_0Z_14-28_last-Prev2D_CFS',
                                                      'diff_0Z_14-28_last-Prev1D_CFS',
                                                      'MEAN'
                                                        ], 'is_prod': True},
        'y_0800-1100_comb': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.4,0.3],
                               'preds_for_comb': ['diff_0Z_0-13_last-rolling2_GEFSL','diff_12Z_14-28_Prev1D-Prev2D_CFS',
                                                  ], 'is_prod': True},
        'y_0800-1100_combMean': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.4, 0.3],
                             'preds_for_comb': ['diff_0Z_0-13_last-rolling2_GEFSL', 'diff_12Z_14-28_Prev1D-Prev2D_CFS',
                                                ], 'is_prod': True},

        'y_0800-1200_EPSCFS': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5,0.5,0.4, 0.3,0.2,0.2],  # ,0.6,0.4,0.75],  # 0.3,#
                               'preds_for_comb': ['MEAN','EPSpost_pred2.2','diff_0Z_0-13_last-Prev2_EPS',
                                                  'CFS_AM_pred1','ECGEM_AM_pred1','diff_0Z_0-16_last-Prev12_GEFSLCO',
                                                  ], 'is_prod': True},
        'y_0800-1200_EPSCFSstrict': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.4,  # ,0.6,0.4,0.75],  # 0.3,#
                               'preds_for_comb': ['MEAN','EPSpost_pred2b',
                                                  'CFS_AM_pred1','diff_0Z_28-42_last-Prev2_CFS','diff_0Z_0-16_last-Prev12_GEFSLCO',
                                                  ], 'is_prod': True},
        'y_1200-1215_EPSpred2b': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['EPSpost_pred2b'
                                                  ], 'is_prod': True},
        'y_0800-1000_GEFSLp4a': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6,0.5,0.4,0.4,0.4],
                                     'preds_for_comb': ['MEAN','GEFSL_PM_pred1_Prev4','GEFSL_PM_pred1_Prev2','diff_0Z_8-16_last-Prev234_GEFSL','GEFSL_PM_pred1_Prev1',
                                                        ], 'is_prod': True},
        'y_0800-1000_GEFSLp1a': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6, 0.5, 0.4, 0.4, 0.4],
                                 'preds_for_comb': ['MEAN','GEFSL_PM_pred1_Prev2','diff_0Z_0-16_last-Prev12_GEFSLCO',
                                                    'diff_0Z_8-16_last-Prev234_GEFSL', 'GEFSL_PM_pred1_Prev1',
                                                    ], 'is_prod': True},
        'y_1415-1745_GEFSLp4': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6, 0.5, 0.4, 0.4, 0.6],
                                'preds_for_comb': ['MEAN', 'GEFSL_PM_pred1_Prev4', 'GEFSL_PM_pred1_Prev2',
                                                   'GEFSL_PM_pred1_Prev1',
                                                   'EPS_daily_hybrid_pred2.2'
                                                   # 'GEFSL_PM_pred1_Prev1','EPS_daily_hybrid_pred2.2'
                                                   ], 'is_prod': True},
        'y_1415-1745_GEFSLp4b': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.4,0.7,0.1],
                                'preds_for_comb': ['MEAN','GEFSL_PM_pred1_Prev4','GEFSL_PM_pred1_Prev1'
                                                   ], 'is_prod': True},
        'y_1745-2030_GEFSLCO': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.7,
                                'preds_for_comb': ['diff_0Z_0-13_last-Prev1_GEFSLCO'
                                                   ], 'is_prod': True},

        'y_1745-1945_fri': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.5]*4+[0.75],
                                   'preds_for_comb': ['EPSpost_pred2.2',
                                                      'diff_12Z_8-16_last-Prev1_GEPS',
                                                      'diff_12Z_8-16_last-Prev1_GEFS',
                                                      'EPSL_daily_hybrid_pred4',
                                                      'MEAN'
                                                      ], 'is_prod': True},
        'y_0800-1000_d': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.4,
                          # [0.5,0.4,0.3,0.4,0.4,0.4],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['EC_AM_Summer_pred2', 'ECGEM_AM_pred1', 'EPSpost_pred2.2'],
                          'is_prod': True},
        'y_1100-1200_d': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5,0.5,0.4,0.2],

                          'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1D_PARA','CFS_AM_pred1','diff_0Z_0-13_last-Prev1_EPS'],
                          'is_prod': True},
        'y_1100-1115_PARA6z': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.85],
                          'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_PARA'],
                          'is_prod': True},
        'y_1245-1300_PARA6z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.25,0.4,0.1],
                               'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1_PARA','PARA_AM_basic_pred1'],
                               'is_prod': True},
        'y_1615-1645_PARAGEM': {'start': dtdt(2019, 7, 1), 'chosen_ratio': [0.57,0.2,0.2,0.2],
                               'preds_for_comb': ['MEAN','GEMEMCO_AM_pred2','PARA_6Z_pred1','GFSEFS_6Z_pred1'],
                               'is_prod': True},
        'y_1615-1645_PARA6Z': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.3]+[0.5]*4,
                                'preds_for_comb': ['MEAN','diff_0Z_0-13_last-Prev3_PARA','diff_6Z_0-13_last-Prev4_PARA',
                                                   'diff_18Z_0-13_last-Prev2_PARA','diff_0Z_0-13_last-Prev4_PARA',
                                                   ],
                                'is_prod': True},
        'y_1530-1645_pre12Z': {'start': august20_start, 'chosen_ratio': [0.15,0.35,0.35,0.4,0.4],
                               'preds_for_comb': ['MEAN','diff_12Z_21-42_last-Prev4D_CFS','GEFS_PM_pred1','diff_6Z_8-16_last-Prev1_GEFS','diff_12Z_0-21_last-Prev4_CFS',

                                                  ],
                               'is_prod': True},
        'y_1100-1200_d12': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.4,0.6, 0.4],

                          'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1D_PARA','CFS_AM_pred1'],
                          'is_prod': True},
        'y_0800-1200_e': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4, 0.3, 0.2, 0.5,0.3],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['EPSL_daily_hybrid_pred4', 'GEFS_AM_0-8_pred1','PARACO_AM_0-8_pred1','EPSpost_pred2b','EC_AM_Summer_pred2'],'is_prod': True},
        'y_0800-1200_f': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,#[0.4,0.3, 0.3, 0.2, 0.5, 0.3],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['EPSL_daily_hybrid_pred4', 'diff_0Z_9-13_last-Prev1_EPS','GEFS_AM_0-8_pred1', 'PARACO_AM_0-8_pred1',
                                             'EPSpost_pred2b', 'EC_AM_Summer_pred2'], 'is_prod': True},
        'y_0800-1200_fMean': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,

                          'preds_for_comb': ['EPSL_daily_hybrid_pred4', 'diff_0Z_9-13_last-Prev1_EPS','GEFS_AM_0-8_pred1', 'PARACO_AM_0-8_pred1',
                                             'EPSpost_pred2b', 'EC_AM_Summer_pred2'], 'is_prod': True},
        'y_1000-1200_f2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.35,0.35,0.7],
                          'preds_for_comb': ['diff_0Z_14-28_last-Prev4_CFS',
                                             'GEFS_AM_0-8_pred1', 'PARACO_AM_0-8_pred1',
                                             'GEFS_AM_basic_pred1'], 'is_prod': True},
        'y_1515-1745_GEFS18z14d': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.55,
                           'preds_for_comb': ['diff_18Z_14-14_last-Prev2_GEFS'], 'is_prod': True},
        'y_1000-1400_PARACO18z': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.5,0.6, 0.4, 0.5, 0.5], #[0.7, 0.4, 0.4, 0.5],
                           'preds_for_comb': ['diff_18Z_0-16_last-Prev2_PARACO','MEAN','diff_18Z_0-16_Prev1-Prev2_PARACO',
                                              'diff_18Z_0-16_last-Prev1_PARACO',
                                              'diff_0Z_0-16_last-Prev3_PARACO'], 'is_prod': True},
        'y_1000-1400_PARACO18zStrict': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.3,0.4, 0.3, 0.35, 0.35], #[0.7, 0.4, 0.4, 0.5],
                           'preds_for_comb': ['diff_18Z_0-16_last-Prev2_PARACO','MEAN','diff_18Z_0-16_Prev1-Prev2_PARACO',
                                              'diff_18Z_0-16_last-Prev1_PARACO',
                                              'diff_0Z_0-16_last-Prev3_PARACO'], 'is_prod': True},
        'y_1100-1200_PARACO18zp1': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_0-16_last-Prev1_PARACO','diff_18Z_0-16_last-Prev2_PARACO', 'MEAN',
                                                     'diff_18Z_0-16_Prev1-Prev2_PARACO',
                                                     'diff_0Z_0-16_last-Prev3_PARACO'], 'is_prod': True},
        'y_0800-1845_PARACO0zp4W': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.52,
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_PARACO','diff_0Z_0-13_last-Prev4_PARACO'], 'is_prod': True},
        'y_1200-1330_PARACO18zp4': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_0-16_last-Prev4_PARACO'], 'is_prod': True},
        'y_1315-1515_PARACO18zp30to8': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.55,
                                  'preds_for_comb': ['diff_18Z_0-8_last-Prev3_PARACO'], 'is_prod': True},
        'y_0800-1845_PARACO18zp20to13': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_0-13_last-Prev2_PARACO'], 'is_prod': True},
        'y_1615-1745_PARACO18zp1clean': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_0-16_last-Prev1_PARACO'], 'is_prod': True},
        'y_1130-1415_PARACO18zp4comb': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.4,0.5,0.5,0.35],
                                  'preds_for_comb': ['diff_18Z_0-15_last-Prev3_PARACO','MEAN','diff_18Z_8-16_last-Prev4_PARACO', 'diff_18Z_0-15_last-Prev4_PARACO'],
                                        'is_prod': True},
        'y_1415-1845_PARACO18zp10to10': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_0-10_last-Prev1_PARACO',
                                                     'diff_18Z_0-16_last-Prev1_PARACO'], 'is_prod': True},
        'y_1415-1845_PARACO18zp10to10Strict': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.25,
                                  'preds_for_comb': ['diff_18Z_0-10_last-Prev1_PARACO',
                                                     'diff_18Z_0-16_last-Prev1_PARACO'], 'is_prod': True},
        'y_0800-1745_PARA18z4D': {'start': march_start, 'chosen_ratio': [0.65,0.4, 0.75, 0.6],
                                  'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev4D_PARA', 'diff_18Z_0-16_last-Prev1234_PARA',
                                                     'diff_18Z_0-16_last-Prev4_PARA'], 'is_prod': True},
        'y_0930-1030_GEFS18z': {'start': march_start, 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_18Z_14-16_last-Prev3_GEFS','diff_18Z_0-13_last-Prev1_PARACO'], 'is_prod': True},
        'y_1315-1400_GEFS18z14to16': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5,0.4,0.4],
                                  'preds_for_comb': ['diff_18Z_11-15_last-Prev1_GEFS','diff_18Z_14-14_last-Prev1_GEFS',
                                                     'diff_18Z_14-16_last-Prev3_GEFS'], 'is_prod': True},
        'y_0000-0800_GEPFS4D': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5,0.5,0.4,0.4],
                                'preds_for_comb': ['MEAN','diff_12Z_0-10_last-Prev4D_GEPS', 'diff_12Z_0-16_last-Prev4D_GEPS','GEFS_daily_hybrid_pred3'],
                                'is_prod': True},
        'y_1100-1200_GFSCO14D': {'start': march_start, 'chosen_ratio': [0.3,0.3,0.3,0.25,0.3],
                                'preds_for_comb': ['diff_0Z_8-16_last-Prev124_GFSCO','diff_0Z_8-16_last-Prev4D_GEFSL',
                                                   'diff_0Z_14-16_last-Prev1_GFSCO',
                                                   'diff_0Z_8-16_last-Prev4D_GFSCO',
                                                   'diff_0Z_8-16_last-Prev1D_GFSCO'
                                                   ],
                                'is_prod': True},
        'y_1415-1845_PARACO6z': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                  # [0.7, 0.4, 0.4, 0.5],
                                  'preds_for_comb': ['diff_6Z_0-16_last-Prev2_PARACO',
                                                     'diff_6Z_0-16_Prev1-Prev2_PARACO',
                                                     'diff_6Z_0-16_last-Prev1_PARACO',
                                                     'diff_0Z_0-16_last-Prev1_PARACO'], 'is_prod': True},
                'y_1515-1745_PARACO6zp3': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5,0.3],
                                  'preds_for_comb': ['diff_6Z_0-16_last-Prev3_PARACO',
                                                     'diff_6Z_8-16_last-Prev3_PARACO',
                                                     ], 'is_prod': True},
        'y_1230-1330_PARACO6zp4': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,
                                  'preds_for_comb': ['diff_6Z_0-16_last-Prev4_PARACO',
                                                     ], 'is_prod': True},
        'y_1200-1300_PARACO1D': {'start': dtdt(2020,4, 1), 'chosen_ratio': 0.6, #dtdt(2020, 8, 1), 'chosen_ratio': 0.6,
                                 # [0.7, 0.4, 0.4, 0.5],
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO',
                                                    'diff_6Z_0-16_last-Prev1D_PARACO',
                                                    ], 'is_prod': True},
        'y_1315-1415_PARACO18zPrev1D8to16': {'start': dtdt(2020,7, 1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_18Z_8-16_Prev1D-Prev3D_PARACO'
                                                    ], 'is_prod': True},
        'y_1230-1315_PARACO1D0zStrict': {'start': dtdt(2020,4, 1), 'chosen_ratio': [0.4,0.35], #dtdt(2020, 8, 1), 'chosen_ratio': 0.6,
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO',
                                                    'diff_0Z_8-16_last-Prev1D_PARACO',
                                                    ], 'is_prod': True},
        'y_0800-1300_PARACO1D6z': {'start': dtdt(2020,3, 1), 'chosen_ratio': 0.55, #dtdt(2020, 8, 1), 'chosen_ratio': 0.6,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_PARACO'], 'is_prod': True},
         'y_1415-1845_PARACO6z1D0to10': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                  'preds_for_comb':
                                      ['diff_6Z_0-10_last-Prev1D_PARACO',
                                       'diff_6Z_0-10_last-Prev2D_PARACO',
                                       'diff_6Z_0-16_last-Prev1D_PARACO'
                                       ],
                                  'is_prod': True},

                         'y_1200-1300_PARA1D': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARA'], 'is_prod': True},
        'y_1215-1315_PARA6z1D0to10': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_6Z_0-10_last-Prev1D_PARA'], 'is_prod': True},
        'y_1615-1945_PARA1D18Z': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.57,
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev1D_PARA'], 'is_prod': True},
        'y_0800-1745_PARA1D0to10': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_18Z_0-10_last-Prev1D_GFSv16','diff_12Z_0-10_last-Prev1D_GFSv16',
                                                    'diff_0Z_0-10_last-Prev1D_GFSv16'], 'is_prod': True},
        'y_1415-1615_PARAPrev1D': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['MEAN','diff_18Z_0-10_Prev1D-Prev2D_PARA',
                                                    'diff_0Z_0-10_Prev1D-Prev2D_PARA'], 'is_prod': True},
        'y_1645-1745_PARAPrev1D8to16': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['diff_6Z_8-16_Prev1D-Prev3D_PARA'],
                                        'is_prod': True},
        'y_1330-1430_PARAPrev1Dx2D8to16': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['diff_0Z_8-16_Prev1D-Prev2D_PARA'],
                                        'is_prod': True},
        'y_0900-1200_PARA6zPrev1d': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_0-16_Prev1-Prev3_PARA','diff_12Z_0-16_Prev1-Prev2_PARA'],
                                        'is_prod': True},
        'y_1615-1645_GEFS12zPrev1': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_0-16_Prev1-Prev2_GEFS','diff_12Z_0-13_Prev1-Prev2_GEFS'],
                                        'is_prod': True},
        'y_1400-1500_GEFSCO12z14to16Prev1': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_14-16_Prev1-Prev2_GEFS','diff_12Z_14-16_Prev1-Prev2_PARACO'],
                                        'is_prod': True},
         'y_0800-1315_PARA0zPrev1D8to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.6,0.5],
                                     'preds_for_comb': ['diff_0Z_8-16_Prev1D-Prev3D_PARA',
                                                        'diff_6Z_8-16_Prev1D-Prev3D_PARA'],
                                     'is_prod': True},
        'y_0800-1315_PARA18zPrev1D8to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                     'preds_for_comb': ['diff_18Z_8-16_Prev1D-Prev3D_PARA','diff_12Z_8-16_Prev1D-Prev3D_PARA',
                                                        'diff_18Z_0-16_Prev1D-Prev3D_PARA','diff_12Z_0-16_Prev1D-Prev3D_PARA',],
                                     'is_prod': True},
                         'y_1100-1400_GFSv1612zp4': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.33,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev4_GFSv16',
                                                    'diff_12Z_8-16_last-Prev4_GFSv16',],
                                  'is_prod': True},
        'y_1100-1400_GFSv1612zp4Strict': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.2,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev4_GFSv16',
                                                    'diff_12Z_8-16_last-Prev4_GFSv16',],
                                  'is_prod': True},
        'y_1815-1900_GFScash': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4,0.3,0.25,0.25],
                               'preds_for_comb': ['diff_0Z_0-2_last-Prev4_GEFS',
                                                  'diff_0Z_0-2_last-Prev4_PARACO',
                                                  'diff_0Z_0-2_last-Prev2_GEFS',
                                                  'diff_0Z_0-2_last-Prev1_GEFS'
                                                  ], 'is_prod': True},
        'y_1415-1745_gfsPM': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,#[0.75]+[0.5]*4,
                              'preds_for_comb': ['PARACO_6Z_pred1','diff_0Z_0-16_last-Prev4_PARACO',
                                                    'GFSEFS_PM_pred1','GFSEFS_6Z_pred3_basic'], 'is_prod': True},
        'y_0800-1200_f234': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.45,0.6,0.6,0.6], #0.7,#[0.5, 0.3, 0.5,0.6],
                           'preds_for_comb': ['GEMCO_PM_pred1','MEAN','ECGEM_AM_pred1','diff_0Z_0-8_last-Prev2_EC'], 'is_prod': True},
        'y_0800-1200_f234b': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6,0.6,0.4,0.6],
                             'preds_for_comb': ['MEAN', 'ECGEM_AM_pred1',
                                                'diff_0Z_0-8_last-Prev2_EC',
                                                'EC_AM_Summer_pred1'], 'is_prod': True},
        'y_0600-1200_GEFSCOam': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.55,0.35],
                              'preds_for_comb': ['GEFS_AM_basic_pred1',
                                                'PARACO_AM_basic_pred1'], 'is_prod': True},
        'y_0800-1200_f234c': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5,0.3, 0.6],
                              'preds_for_comb': ['MEAN','ECGEM_AM_pred1',
                                                 'GEMEMCO_AM_pred2'], 'is_prod': True},
        'y_1100-1315_ECGEM': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['ECGEM_AM_pred1'], 'is_prod': True},
        'y_0800-1400_EPSlast': {'start': dtdt(2019, 7, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS','EPSpost_pred2b','Value_0Z_day15trend_EPS'
                                                 ], 'is_prod': True},
        'y_0800-1400_EPSlastStrict': {'start': dtdt(2019, 7, 1), 'chosen_ratio': 0.25,
                                'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS', 'EPSpost_pred2b',
                                                   'Value_0Z_day15trend_EPS'
                                                   ], 'is_prod': True},
        'y_0800-1400_EPSlastclean': {'start': dtdt(2019, 7, 1), 'chosen_ratio': [0.5,0.3,0.2],
                                'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS','MEAN','diff_0Z_0-13_last-Prev2_EPS'
                                                  ], 'is_prod': True},
        'y_1200-1500_EPSlast': {'start': dtdt(2019, 7, 1), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-13_last-Prev1_EPS','Value_0Z_day15trend_EPS_diff2'
                                                   ], 'is_prod': True},
        'y_1200-1500_EPStrend15': {'start': august20_start, 'chosen_ratio': [0.5,0.4,0.25,0.35,0.05],
                                'preds_for_comb': ['Value_0Z_day15trend_EPS_diff2','Value_0Z_day15trend_GEFS',
                                        'Value_0Z_day15trend_EPS','MEAN','Value_0Z_day15trend_EPS_Fdiff1'
                                                   ], 'is_prod': True},
        'y_1530-1645_GEFStrend15': {'start': august20_start, 'chosen_ratio': 0.3,
                                   'preds_for_comb': ['Value_0Z_day15trend_GEFS',
                                                      'Value_0Z_day15trend_GEFS_diff1',
                                                      ], 'is_prod': True},
        'y_1215-1515_GEFStrend15': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.6,0.65,0.45,0.4],
                                   'preds_for_comb': ['MEAN','Value_0Z_day15trend_GEFS_diff2',
                                                      'Value_0Z_day15trend_GEFS','Value_0Z_day15trend_GEFS_diff1',
                                                      ], 'is_prod': True},
        'y_1530-1645_GEFStrend156z': {'start': august20_start, 'chosen_ratio': 0.5,
                                    'preds_for_comb': ['Value_6Z_day15trend_GEFS'
                                                       ], 'is_prod': True},
        'y_1530-1645_EPStrend15b': {'start': august20_start, 'chosen_ratio': 0.3,
                                    'preds_for_comb': ['Value_0Z_day15trend_EPS'
                                                       ], 'is_prod': True},
        'y_0800-1400_EPShybrid1b': {'start': dtdt(2019, 7, 1), 'chosen_ratio': 0.5,#[0.5, 0.4,0.33],
                                'preds_for_comb': ['EPS_daily_hybrid_pred1b','diff_0Z_0-13_last-Prev1_EPS','GEFSL_daily_hybrid_pred3'],
                                                    'is_prod': True},
        'y_1400-1500_EC': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6,0.5,0.5,0.4,0.2,0.4],
                             'preds_for_comb': ['ECGEM_AM_pred1','EC_AM_Summer_pred1',
                                                'diff_0Z_0-8_last-Prev2_EC','diff_0Z_0-8_last-Prev4_EC',
                                                'diff_0Z_0-8_last-Prev1_EC',
                                                'MEAN'], 'is_prod': True},
        'y_0715-0745_EC0Zp34': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55,0.5,0.45],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev4_EC','diff_0Z_0-8_last-Prev3_EC',
                                                ], 'is_prod': True},
        'y_0600-0800_EC12Zp3': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55,0.5,0.45],
                             'preds_for_comb': ['MEAN','diff_12Z_0-8_last-Prev3_EC',
                                                'diff_12Z_0-13_last-Prev3_EC',
                                                ], 'is_prod': True},
        'y_0800-1415_EC0Zp12': {'start': dtdt(2022, 1, 1), 'chosen_ratio': [0.5,0.5,0.5],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_EC','diff_0Z_5-8_last-Prev2_EC',
                                                ], 'is_prod': True},
        'y_0800-1415_EC0Zp12Strict': {'start': dtdt(2022, 1, 1), 'chosen_ratio': [0.3,0.25,0.2],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_EC','diff_0Z_5-8_last-Prev2_EC',
                                                ], 'is_prod': True},
        'y_0800-1745_EC0Zp12': {'start': dtdt(2021, 5, 1), 'chosen_ratio': [0.4,0.4,0.4],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC',
                                                ], 'is_prod': True},
        'y_0715-0745_EC0Zp12': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55,0.5,0.45],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC',
                                                ], 'is_prod': True},
        'y_0715-0745_EC0Zp12Yest': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55,0.5,0.45],
                             'preds_for_comb': ['MEAN','diff_0Z_0-8_last-Prev1_EC-1d','diff_0Z_0-8_last-Prev2_EC-1d',
                                                ], 'is_prod': True},
        'y_0800-1200_mon': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4,0.4, 0.3,0.4, 0.3, 0.2, 0.3],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['CFS_AM_Daily_pred1', 'EPSL_daily_hybrid_pred4',
                                               'diff_0Z_9-13_last-Prev1_EPS',
                                               'EPSpost_pred2b', 'GEFS_AM_0-8_pred1', 'PARACO_AM_0-8_pred1',
                                               'EC_AM_Summer_pred2'], 'is_prod': True},
        'y_0800-1200_mon2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5, 0.4, 0.4,0.4,0.3,0.4], # 0.8
                            'preds_for_comb': ['CFS_AM_Daily_pred1','diff_0Z_0-13_last-Prev2D_EPS',
                                             'PARA_AM_0-8_pred1','GEFSL_daily_hybrid_pred2', 'PARACO_AM_pred3', #PARACO 0-8 was instead of pred3
                                               'GEFSL_PM_pred1_Prev4'],
                             'is_prod': True},
        'y_0800-1200_mon3': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4, 0.5,0.4, 0.4, 0.4, 0.3, 0.4,0.6], #0.9],  # 0.8
                             'preds_for_comb': ['CFS_AM_Daily_pred1', 'EPS_PM_pred2D','diff_0Z_0-13_last-Prev2D_EPS',
                                                'PARA_AM_0-8_pred1', 'GEFSL_daily_hybrid_pred2', 'PARACO_AM_pred3',
                                                # PARACO 0-8 was instead of pred3
                                                'GEFSL_PM_pred1_Prev4','MEAN'],
                             'is_prod': True},
        'y_1645-1730_PARA12Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.45,0.5],
                             # 0.9],  # 0.8
                             'preds_for_comb': ['diff_12Z_0-16_last-Prev1_PARA','diff_12Z_0-16_last-Prev3_PARA','diff_12Z_0-10_last-Prev3_GEM'],
                                        'is_prod': True},
        'y_1745-1945_PARA12Zp4': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.4,
                             'preds_for_comb': ['diff_12Z_8-16_last-Prev4_PARA','diff_12Z_8-16_last-Prev3_PARA'],
                                        'is_prod': True},
        'y_1700-1745_PARA12Zp34': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.45,0.3,0.3],
                             'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev4_PARA','diff_12Z_0-16_last-Prev3_PARA'],
                                        'is_prod': True},
        'y_1745-1945_PARA12Zp1': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                             'preds_for_comb': ['diff_12Z_0-16_last-Prev1_PARA'],
                                        'is_prod': True},
        'y_1745-1945_PARA12Zp12': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.4,
                             'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev1_PARA',
                                                'diff_12Z_0-16_last-Prev2_PARA'],
                                        'is_prod': True},
        'y_1745-1945_PARA12Zp1Strict': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.4,
                             'preds_for_comb': ['diff_12Z_0-16_last-Prev1_PARA'],
                                        'is_prod': True},
        'y_1845-1945_PARA12Zp40to10': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                             'preds_for_comb': ['diff_12Z_0-10_last-Prev4_PARA'],
                                        'is_prod': True},
         'y_0800-1315_PARA12z8to16p3': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.45,0.4],
                                    'preds_for_comb':
                                        ['diff_12Z_8-16_last-Prev3_PARA',
                                        'diff_12Z_0-16_last-Prev3_PARA',

                                         ],
                                    'is_prod': True},

                         'y_1645-1715_GEM12Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.3, 0.25],
                                'preds_for_comb': ['diff_12Z_0-10_last-Prev1_GEM','diff_12Z_0-10_last-Prev2_GEM'],
                                'is_prod': True},
        'y_1500-1545_GEMPrev1D': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-10_Prev1D-Prev2D_GEM'],
                                'is_prod': True},
        'y_1645-1715_GEM12Zloose': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5, 0.4],
                                'preds_for_comb': ['diff_12Z_0-10_last-Prev1_GEM','diff_12Z_0-10_last-Prev2_GEM'],
                                'is_prod': True},
        'y_1645-1715_GEM12Zb': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.3, 0.25],
                               'preds_for_comb': ['diff_12Z_0-10_last-Prev2_GEM',
                                                  'diff_12Z_0-10_last-Prev1_GEM'],
                               'is_prod': True},
        'y_1645-1730_GEM12Zp2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.6,
                               'preds_for_comb': ['diff_12Z_0-10_last-Prev2_GEM'],
                               'is_prod': True},
        'y_1645-1730_GEM12Zprev1': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_12Z_0-8_Prev1-Prev2_GEM'],
                               'is_prod': True},
        'y_0000-0400_GEM12Zc': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.5,0.3,0.2,0.3],
                               'preds_for_comb': ['diff_12Z_0-8_last-Prev2_GEM','MEAN',
                                                'diff_12Z_0-8_last-Prev1_GEM','diff_12Z_0-8_last-Prev3_GEM',
                                                ],
                               'is_prod': True},
        'y_1730-1745_GEFS12Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.65,0.65],
                                # 0.9],  # 0.8
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev3_GEFS',
                                                   'diff_12Z_0-8_last-Prev4_GEFS',],
                                'is_prod': True},

        'y_1715-1815_PARA12Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5, 0.45],
                                # 0.9],  # 0.8
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev1_PARA',
                                                   'diff_12Z_0-8_last-Prev1_PARACO',
                                                   ],
                                'is_prod': True},

        # 'EPS_PM_pred2D',
        'y_0900-1000_mon2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4, 0.4, 0.4,0.4,0.3,0.3],  # 0.8
                             'preds_for_comb': ['GEFSL_daily_hybrid_pred2','CFS_AM_Daily_pred1','GEFSL_PM_pred1_Prev4',
                                                'diff_0Z_0-13_last-Prev2D_EPS',
                                             'PARA_AM_0-8_pred1','PARACO_AM_0-8_pred1'],
                             'is_prod': True},
                         'y_1315-1415_mon': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.4,0.25],
                                  'preds_for_comb': ['EPSpost_pred2b','EPSL_daily_hybrid_pred4', #### maybe put EPSL first
                                               'diff_6Z_0-16_last-Prev1D_GEFS','diff_0Z_14-21_last-Prev2D_CFS'], 'is_prod': True},
                        'y_1100-1515_mon': {'start': march_start, 'chosen_ratio': [0.7, 0.5,0.3],
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev3_PARA','PARA_AM_basic_pred1'],#'diff_6Z_0-16_last-Prev1_PARA'],
                            'is_prod': True},
                        'y_1415-1745_monComb': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.5]+[0.35]*5,
                            'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev2_PARA',
                                               'diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev3_PARA',
                                               'PARACO_6Z_pred1'],  # 'diff_6Z_0-16_last-Prev1_PARA'],
                            'is_prod': True},
                        'y_1415-1645_mon': {'start': march_start, 'chosen_ratio': [0.75, 0.25, 0.25],  # [0.5, 0.5,0.4],
                            'preds_for_comb': ['EPS_daily_hybrid_pred1', 'CFS_AM_Daily_hybrid_pred1',
                                               'diff_6Z_0-16_last-Prev1D_GEFS'], 'is_prod': True},
                        'y_1315-1745_d': {'start': march_start, 'chosen_ratio': [0.7,0.8, 0.3, 0.6],
                          'preds_for_comb': ['MEAN','EPS_daily_hybrid_pred2', 'GEFSL_PM_pred1_Prev4','diff_6Z_0-16_last-Prev1D_GEFS'], 'is_prod': True},
                    'y_1315-1745_dX1': {'start': march_start, 'chosen_ratio': [0.8, 0.3, 0.3],
                          'preds_for_comb': ['EPS_daily_hybrid_pred2', 'GEFSL_PM_pred1_Prev4',
                                             'diff_6Z_0-16_last-Prev1D_GEFS'], 'is_prod': True},

        'y_1315-1745_e': {'start': march_start, 'chosen_ratio': [0.7, 0.3,0.3, 0.3],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['EPS_daily_hybrid_pred2', 'CFS_PM_Daily_hybrid_pred2', 'GEFSL_PM_pred1_Prev4',
                                             'diff_6Z_0-16_last-Prev1D_GEFS'], 'is_prod': True},
                        # less relevant for end of week there GEFS 1D is giving the tone
                        'y_1315-1745_american024': {'start': march_start, 'chosen_ratio': 0.7,#[0.8,0.7,0.5,0.5,0.5],

                                    'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARA','diff_0Z_0-16_last-Prev4D_GEFS',
                                                       'EPS_daily_hybrid_pred2','CFS_PM_Daily_hybrid_pred2',
                                                       'GEFSL_PM_pred1_Prev4'], 'is_prod': True},
                            'y_1315-1745_3c': {'start': march_start, 'chosen_ratio': [0.75,0.4,0.4,0.3,0.3], #0.75,

                                    'preds_for_comb': ['diff_6Z_0-10_last-Prev4D_PARA',
                                                       'diff_6Z_0-13_last-Prev1_CFS',
                                                       'diff_6Z_0-2_last-Prev3_PARA',
                                                       'PARACO_AM_8-16_pred2',
                                                       'diff_0Z_0-2_last-Prev2_PARA'], 'is_prod': True},
            'y_1100-1200_wed': {'start': march_start, 'chosen_ratio': [0.4,0.4,0.4],  # [0.5, 0.5,0.4],

                          'preds_for_comb': ['GFSEFS_6Z_pred3_basic','PARACO_PM_pred0','CFS_AM_pred1'], 'is_prod': True},
            'y_1200-1300_wed': {'start': march_start, 'chosen_ratio': [0.4, 0.4, 0.4],  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['diff_0Z_28-42_last-Prev2D_CFS','y_tag_bollinger_v1_hack', 'GFSEFS_6Z_pred3_basic'],
                            'is_prod': True},
            'y_1300-1400_wed': {'start': march_start, 'chosen_ratio': [0.4, 0.4, 0.4],  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['y_tag_bollinger_v1_hack','EPS_daily_hybrid_pred1','CFS_AM_Daily_hybrid_pred1'],
                            'is_prod': True},
            'y_1145-1315_fri': {'start': march_start, 'chosen_ratio': [0.6,0.4, 0.4,0.4],  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['y_1030-1100','CFS_AM_pred1','CFS_AM_Daily_pred1','diff_0Z_14-21_last-Prev1D_CFS'],
                            'is_prod': True},
        'y_1145-1315_fri2': {'start': march_start, 'chosen_ratio': [0.6, 0.5, 0.4, 0.4, 0.4],  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['y_1030-1100', 'diff_0Z_8-16_last-Prev2_GFSCO', 'CFS_AM_pred1',
                                               'CFS_AM_Daily_pred1', 'diff_0Z_14-21_last-Prev1D_CFS'],
                            'is_prod': True},
        'y_1415-1615_fri': {'start': march_start, 'chosen_ratio': [0.65],  # [0.5, 0.5,0.4],
                             'preds_for_comb': ['PARACO_6Z_pred1'],
                             'is_prod': True},

        'y_1200-1230_fri': {'start': march_start, 'chosen_ratio': [0.6, 0.5, 0.4, 0.4, 0.4],  # [0.5, 0.5,0.4],

                             'preds_for_comb': ['y_1030-1100', 'diff_0Z_8-16_last-Prev2_GFSCO', 'CFS_AM_pred1',
                                                'CFS_AM_Daily_pred1', 'diff_0Z_14-21_last-Prev1D_CFS'],
                             'is_prod': True},
            'y_0800-1945_mon': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.65],#[0.85,0.4],

                              'preds_for_comb': ['EPSL_daily_hybrid_pred4','diff_0Z_14-21_last-Prev1D_CFS','MEAN'],
                              'is_prod': True},
            'y_1100-1315_mon': {'start': dtdt(2020,4,15), 'chosen_ratio': 0.5,  # [0.85,0.4],

                            'preds_for_comb': ['GEPS_daily_hybrid_pred2','GFS_Super_PM_pred2','GEFS_daily_hybrid_pred2',
                                               'mix_0600-0700_pred1','diff_0Z_0-16_last-Prev2_GEMCO',
                                               'GFSEFS_PM_pred1', 'PARA_6Z_pred1','PARA_PM_pred1'],
                                                    #'diff_6Z_0-16_last-Prev1D_PARA'],
                            'is_prod': True},
                'y_0800-1945_daily': {'start': march_start, 'chosen_ratio': [0.8,0.8,0.7,0.8],

                            'preds_for_comb': ['EPSpost_pred2.2','diff_0Z_8-16_last-Prev1D_GEFSL',
                                               'diff_0Z_8-16_last-Prev2D_GEFSL','MEAN'],
                            'is_prod': True},
                'y_0800-1945_dailyMean': {'start': march_start, 'chosen_ratio': [0.6, 0.6, 0.6, 0.6,0.6],

                              'preds_for_comb': ['EPSpost_pred2.2','EPS_daily_hybrid_pred2.2', 'diff_0Z_8-16_last-Prev1D_GEFSL',
                                                 'diff_0Z_8-16_last-Prev2D_GEFSL','MEAN'],
                              'is_prod': True},
                'y_1945-2045_4a': {'start': march_start, 'chosen_ratio': [0.6,0.4,0.4,0.4],

                                  'preds_for_comb': ['diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev1_EPS',
                                                     'diff_12Z_0-13_last-Prev2_EC',
                                                     'diff_12Z_0-13_last-Prev4_EPS'],
                                  'is_prod': True},
                    'y_2300-2345_EPS12Z': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.6, 0.4, 0.2],

                           'preds_for_comb': ['diff_12Z_0-13_last-Prev1_EPS', 'diff_12Z_0-13_last-Prev2_EPS',
                                              'diff_12Z_0-13_last-Prev3_EPS'],
                           'is_prod': True},
                'y_1845-2030_EC12Z': {'start': march_start, 'chosen_ratio': [0.5,0.5, 0.05, 0.05, 0.05,0.05],

                           'preds_for_comb': ['diff_12Z_0-13_last-Prev2_EC','diff_12Z_0-13_last-Prev1_EC',
                                              'diff_12Z_0-13_last-Prev2_EPS', 'diff_12Z_0-13_last-Prev1_EPS',
                                              'diff_12Z_0-13_last-Prev4_EPS','diff_12Z_9-13_last-Prev1_EPS',
                                              ],
                           'is_prod': True},
                'y_1200-1500_EC12Z0to4': {'start': dtdt(2020,7,1), 'chosen_ratio':0.5,
                        'preds_for_comb': ['diff_12Z_0-4_last-Prev2_EC'],
                           'is_prod': True},
                'y_1945-2145_EC12ZComb': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5]+[0.05]*6,

                           'preds_for_comb': ['MEAN','diff_12Z_0-4_last-Prev1_EC','diff_12Z_0-8_last-Prev1_EC',
                                       'diff_12Z_0-4_last-Prev2_EC','diff_12Z_0-8_last-Prev2_EC',
                                       'diff_12Z_0-8_last-Prev3_EC','diff_12Z_0-8_last-Prev4_EC'],
                           'is_prod': True},
                'y_1300-1330_EC12Zb1': {'start': march_start, 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_12Z_0-13_last-Prev1_EC'],
                              'is_prod': True},
                'y_1100-1230_EChybrid1': {'start': march_start, 'chosen_ratio': 0.4,
                                'preds_for_comb': ['EC_daily_hybrid_pred1'],
                                'is_prod': True},
                'y_0600-0800_EC12z2D': {'start': march_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev2D_EC'],
                                'is_prod': True},
                'y_0600-0800_EC12z3D': {'start': march_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev3D_EC'],
                                'is_prod': True},
                'y_1615-1645_EC12Zb2': {'start': march_start, 'chosen_ratio': 0.5,#[0.5,0.2,0.2],
                               'preds_for_comb': ['diff_12Z_0-13_last-Prev2_EC',
                                                  'diff_12Z_0-13_last-Prev3_EC','diff_12Z_0-13_last-Prev4_EC'
                                                  ],
                               'is_prod': True},
                'y_0800-1945_EPShybrid2.2': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.8,

                              'preds_for_comb': ['EPS_daily_hybrid_pred2.2'],
                              'is_prod': True},
                'y_0800-1945_EPSPrev1D': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.5,0.45,0.03],

                              'preds_for_comb': ['diff_0Z_0-13_Prev1D-Prev2D_EPS','MEAN',
                                                 'diff_12Z_0-13_Prev1D-Prev2D_EPS'],
                              'is_prod': True},
                'y_0800-1945_EPSPrev1DStrict': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.3,0.25,0.03],

                              'preds_for_comb': ['diff_0Z_0-13_Prev1D-Prev2D_EPS','MEAN',
                                                 'diff_12Z_0-13_Prev1D-Prev2D_EPS'],
                              'is_prod': True},
                'y_0700-0830_EPSPrev1D8to13': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_0Z_8-13_Prev1D-Prev2D_EPS'],
                              'is_prod': True},
                'y_0800-1845_ECEPSPrev1D': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.45,0.5,0.5],
                              'preds_for_comb': ['diff_12Z_0-8_Prev1D-Prev3D_EC','diff_12Z_0-13_Prev1D-Prev3D_EPS',
                                                 'MEAN'],
                              'is_prod': True},
                'y_0800-1845_EPSPrev1D': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_0Z_0-8_Prev1D-Prev2D_EPS'],
                              'is_prod': True},
                'y_0800-1945_GEFSdaily': {'start': july_19_start, 'chosen_ratio': [0.7,0.7,0.7,0.8],
                                  'preds_for_comb': ['EPS_daily_hybrid_pred2.2',
                                                        'diff_0Z_0-16_last-Prev1D_GEFS',
                                                        'diff_0Z_0-16_last-Prev1D_PARACO',
                                                        'diff_0Z_0-16_last-Prev1D_GEFSL'],
                                            'is_prod': True},
                'y_0800-1945_EPShybrid2.2Strict': {'start': march_start, 'chosen_ratio': [0.5],

                                     'preds_for_comb': ['EPS_daily_hybrid_pred2.2'],
                                     'is_prod': True},
                'y_0800-1745_EPS24D': {'start': march_start, 'chosen_ratio': 0.5,

                                     'preds_for_comb': ['MEAN','diff_0Z_0-13_last-Prev2D_EPS',
                                                        'diff_0Z_0-13_last-Prev4D_EPS'],
                                     'is_prod': True},
                'y_1200-1500_EPS2D': {'start': march_start, 'chosen_ratio': 0.5,

                                     'preds_for_comb': ['diff_0Z_0-13_last-Prev2D_EPS',
                                                        ],
                                     'is_prod': True},
                 'y_0800-1415_EPS0zp2': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev2_EPS'],
                                         'is_prod': True},

                 'y_0800-1745_EPS12zScan': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.44,
                                         'preds_for_comb':
                                             ['diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                              'diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS',],
                                         'is_prod': True},

                 'y_0700-0800_EPS12z': {'start': dtdt(2021, 5, 1), 'chosen_ratio': [0.44,0.4,0.01,0.01,0.01],
                                         'preds_for_comb':
                                             ['MEAN','diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                              'diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp1': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.4,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev1_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp1Strict': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.25,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev1_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp2': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.4,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev2_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp2Strict': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.25,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev2_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp4': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.4,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev4_EPS',],
                                         'is_prod': True},

                 'y_0800-1745_EPS0zp4Strict': {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.25,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev4_EPS',],
                                         'is_prod': True},

                 'y_1300-1400_EPS0zp12': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,
                                         'preds_for_comb':
                                             ['MEAN','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev1_EPS',],
                                         'is_prod': True},

                 'y_0800-1415_EPS0zp12loose': {'start': dtdt(2021, 4, 1), 'chosen_ratio': [0.65,0.3,0.3],
                                         'preds_for_comb':
                                             ['MEAN','diff_0Z_0-13_last-Prev1_EPS',
                                              'diff_0Z_0-13_last-Prev2_EPS',],
                                         'is_prod': True},

                 'y_1300-1400_EPS0zp12Strict': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.45,0.2,0.2],
                                         'preds_for_comb':
                                             ['MEAN','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev1_EPS',],
                                         'is_prod': True},

                 'y_1300-1400_EPS12zp12': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,
                                         'preds_for_comb':
                                             ['MEAN','diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev1_EPS',],
                                         'is_prod': True},

                 'y_0800-1415_EPS0zp2Strict': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.3,
                                         'preds_for_comb':
                                             ['diff_0Z_0-13_last-Prev2_EPS'],
                                         'is_prod': True},

                         'y_1145-1745_GEFSEPShybrid3': {'start': march_start, 'chosen_ratio': 0.65,

                                           'preds_for_comb': ['EPS_daily_hybrid_pred2.2','diff_0Z_0-16_last-Prev1D_GEFS',
                                            'diff_0Z_0-13_last-Prev1D_EPS',
                                            'diff_0Z_0-16_last-Prev1D_PARACO', 'diff_0Z_0-16_last-Prev4D_PARACO', 'diff_0Z_0-16_last-Prev4D_PARA'],
                                           'is_prod': True},
                    'y_1145-1745_GEFS1D': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.7,#[0.4]+[0.6]*2+[0.5]*2,

                                       'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev1D_GEFSfltrd',
                                                          'diff_0Z_0-16_last-Prev1D_PARACO',
                                                          'diff_0Z_0-16_last-Prev4D_PARACO',
                                                          'diff_0Z_0-16_last-Prev4D_PARA'],
                                       'is_prod': True},
                    'y_1145-1745_GEFS1D6z': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6,0.6,0.6,0.3,0.3],  # [0.4]+[0.6]*2+[0.5]*2,

                               'preds_for_comb': ['MEAN', 'diff_6Z_0-16_last-Prev1D_GEFSfltrd',
                                                  'diff_6Z_0-16_last-Prev1D_PARACO',
                                                  'diff_6Z_0-16_last-Prev4D_PARACO',
                                                  'diff_6Z_0-16_last-Prev4D_PARA'],
                               'is_prod': True},
                        'y_1145-1745_GEFS1D6zStrict': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.3,0.3,0.2,0.1,0.1],  # [0.4]+[0.6]*2+[0.5]*2,

                               'preds_for_comb': ['MEAN', 'diff_6Z_0-16_last-Prev1D_GEFSfltrd',
                                                  'diff_6Z_0-16_last-Prev1D_PARACO',
                                                  'diff_6Z_0-16_last-Prev4D_PARACO',
                                                  'diff_6Z_0-16_last-Prev4D_PARA'],
                               'is_prod': True},
                        'y_1645-1815_PARACO6Z12D': {'start': august20_start, 'chosen_ratio':0.55,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_PARACO',
                                                    'diff_6Z_0-16_last-Prev2D_PARACO',
                                                    ],
                                 'is_prod': True},
                    'y_1300-1515_GEFSCO6Z23D': {'start': march_start, 'chosen_ratio':0.5,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev2D_PARACO',
                                                    'diff_6Z_0-16_last-Prev4D_GEFS','diff_6Z_0-16_last-Prev3D_GEFS'],
                                 'is_prod': True},
                    'y_1400-1545_GEFS0z3D0to10': {'start': april_start, 'chosen_ratio':0.55,
                                 'preds_for_comb': ['diff_0Z_0-10_last-Prev3D_GEFS'],
                                 'is_prod': True},
                    'y_1115-1415_PARACO6Z23D': {'start': march_start, 'chosen_ratio':0.45,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev2D_PARACO',
                                                    'diff_6Z_0-16_last-Prev3D_PARACO'],
                                 'is_prod': True},
                        'y_1645-1815_PARACO12Z12D': {'start': august20_start, 'chosen_ratio': 0.5,
                                    'preds_for_comb': ['diff_12Z_0-16_last-Prev1D_PARACO',
                                                       'diff_12Z_0-16_last-Prev2D_PARACO',
                                                       ],
                                    'is_prod': True},
                     'y_1715-1915_PARACO12Z1D8to16': {'start': august20_start, 'chosen_ratio': 0.4,
                                    'preds_for_comb': ['diff_12Z_8-16_last-Prev1D_PARACO'
                                                       ],
                                    'is_prod': True},
                     'y_1145-1745_GEFSCO1D': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.7,  # [0.4]+[0.6]*2+[0.5]*2,

                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO',
                                                    'diff_0Z_8-16_last-Prev1D_GEFS'],
                                 'is_prod': True},
                    'y_1845-1915_GEFS1D': {'start': august20_start, 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['diff_0Z_0-10_last-Prev1D_GEFS',
                                                    #'diff_0Z_0-10_last-Prev1D_PARA'
                                                    ],
                                 'is_prod': True},

                'y_1415-1645_PARACO6z': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.65,0.3,0.3,0.1],

                               'preds_for_comb': ['PARACO_6Z_pred1','GEFS_PM_pred1','GFSEFS_6Z_pred3_basic',
                                                  'diff_6Z_0-16_last-Prev1_PARA'],
                               'is_prod': True},
            'y_1645-1745_PARACO6zStrict2Mean': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.4,0.2],

                               'preds_for_comb': ['PARACO_6Z_pred1','GEFS_PM_pred1'],
                               'is_prod': True},
            'y_1415-1645_PARACO6zStrict': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.3,0.15,0.15,0.1],
                               'preds_for_comb': ['PARACO_6Z_pred1','GEFS_PM_pred1','GFSEFS_6Z_pred3_basic',
                                                  'diff_6Z_0-16_last-Prev1_PARA'],
                               'is_prod': True},
                'y_1415-1545_PARACO6zp1': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.4,0.4],
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_PARACO',
                                                  'diff_6Z_8-16_last-Prev1_PARACO',
                                                  'diff_6Z_14-16_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1415-1545_PARACO6zp10to8': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1130-1745_PARACO6zp10to10': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_6Z_0-10_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1215-1300_PARA6zp10to8': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.4,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1_PARA'],
                               'is_prod': True},
                'y_1100-1200_PARA6zp10to10': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_6Z_0-10_last-Prev1_PARA'],
                               'is_prod': True},
                'y_1130-1745_PARACO6zp10to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1115-1245_PARACO6zp10to8': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.45,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1115-1245_PARACO6zp10to8Strict': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.25,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1245-1330_PARACO6zp10to10': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.4,
                               'preds_for_comb': ['diff_6Z_0-10_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1300-1330_PARACO6zMean': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.6,

                                 'preds_for_comb': ['PARACO_6Z_pred1', 'GEFS_PM_pred1', 'GFSEFS_6Z_pred3_basic'],
                                 'is_prod': True},
                'y_1645-1715_PARACO6z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.4,0.4],

                                 'preds_for_comb': ['diff_6Z_8-16_last-Prev2_PARACO','diff_6Z_8-16_last-Prev1_PARACO','diff_6Z_8-16_last-Prev3_PARACO'],
                                 'is_prod': True},
                'y_1815-1915_GEFS6Z': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5, 0.4, 0.4],
                                 'preds_for_comb': ['GEFS_6Z_pred1','diff_6Z_0-16_last-Prev4_GEFS',
                                                    'diff_18Z_0-16_last-Prev1_GEFS'],
                                 'is_prod': True},
        'y_1415-1615_GFS6Zrolling2': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.4,0.1,0.1,0.1,0.1],
                               'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev2_PARACO',
                                                  'diff_6Z_0-16_last-rolling2_PARACO',
                                                  'diff_6Z_0-16_last-rolling2_GFSv16',
                                                  'diff_6Z_0-16_last-Prev2_PARA'],
                               'is_prod': True},
        'y_1815-1915_GFSv16Zrolling2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5],
                                      'preds_for_comb': ['diff_6Z_0-16_last-rolling2_GFSv16',
                                                         ],
                                      'is_prod': True},
        'y_1100-1615_GFSv16Zp2': {'start': august20_start, 'chosen_ratio': [0.55,0.4],
                                        'preds_for_comb': ['diff_6Z_0-16_last-Prev2_GFSv16','diff_6Z_0-16_last-Prev1_GFSv16'],
                                        'is_prod': True},
        'y_1430-1500_GFSv16Zp40to10': {'start': august20_start, 'chosen_ratio': 0.66,
                                        'preds_for_comb': ['diff_6Z_0-10_last-Prev4_GFSv16'],
                                        'is_prod': True},

        'y_1100-1145_PARA6Zp4': {'start': august20_start, 'chosen_ratio': 0.44,
                                        'preds_for_comb': ['diff_6Z_0-16_last-Prev4_PARA','diff_6Z_8-16_last-Prev4_PARA','MEAN'],
                                        'is_prod': True},
        'y_1115-1215_PARA6Zp48to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                        'preds_for_comb': ['diff_6Z_8-16_last-Prev4_PARA'],
                                        'is_prod': True},

        'y_1415-1715_PARA6Zp14comb': {'start': august20_start, 'chosen_ratio': 0.44,
                                'preds_for_comb': ['diff_6Z_0-8_last-Prev1_PARA','diff_6Z_0-16_last-Prev4_PARA','diff_6Z_0-8_last-Prev4_PARA','MEAN'],
                                        'is_prod': True},


                         'y_1100-1615_PARAp2': {'start': august20_start, 'chosen_ratio': [0.55,0.4],
                                        'preds_for_comb': ['diff_6Z_0-16_last-Prev2_PARA',
                                                           'diff_6Z_0-16_last-Prev1_PARA'],
                                        'is_prod': True},


                    'y_1645-1745_GEFSPM': {'start': dtdt(2020, 8, 1), 'chosen_ratio':0.4,
                               'preds_for_comb': ['GEFS_PM_pred1','diff_6Z_0-16_last-Prev3_GEFS',
                                                  'diff_6Z_0-16_last-Prev1_GEFS'],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp1$W': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.52,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp2$W': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.48,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp3$W': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.45,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp4$W': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.4,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp1$WStrict': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.3,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp2$WStrict': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.3,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp3$WStrict': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.28,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_0800-1745_GEFS6zp4$WStrict': {'start': dtdt(2022, 4, 1), 'chosen_ratio':0.28,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1215-1745_PARACO6zp4$W': {'start': dtdt(2022, 4, 1), 'chosen_ratio': 0.48,
                                           'preds_for_comb': ['diff_6Z_0-16_last-Prev4_PARACO',
                                                              ],
                                           'is_prod': True},

                'y_1130-1300_GEFS6zp1': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.45,0.4],
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev1_GEFS',
                                                  'diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1200-1415_GEFS6zp12': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.5,0.5,0.44],
                               'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev1_GEFS',
                                                  'diff_6Z_0-16_last-Prev2_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1130-1300_GEFS6zp1Strict': {'start': dtdt(2020, 3, 1), 'chosen_ratio':[0.25,0.2],
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev1_GEFS',
                                                  'diff_6Z_0-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1530-1615_GEFS6zp18to16': {'start': dtdt(2020, 3, 1), 'chosen_ratio':0.5,
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1530-1615_GEFS6zp18to16Strict': {'start': dtdt(2020, 3, 1), 'chosen_ratio':0.24,
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev1_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1615-1715_GEFS6zp248to16': {'start': dtdt(2020, 9, 1), 'chosen_ratio':0.4,
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev2_GEFS','diff_6Z_8-16_last-Prev4_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1245-1945_GEFS6zp1348to16': {'start': dtdt(2021, 5, 1), 'chosen_ratio':[0.44,0.44,0.3,0.1],
                               'preds_for_comb': ['MEAN','diff_6Z_8-16_last-Prev1_GEFS','diff_6Z_8-16_last-Prev3_GEFS',
                                                    'diff_6Z_8-16_last-Prev4_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1315-1645_GEFS6zp48to16Strict': {'start': dtdt(2020, 9, 1), 'chosen_ratio':0.33,
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev4_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1315-1645_GEFS6zp48to16': {'start': dtdt(2020, 9, 1), 'chosen_ratio':0.5,
                               'preds_for_comb': ['diff_6Z_8-16_last-Prev4_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1145-1945_GEFS6zp40to16': {'start': dtdt(2021, 9, 1), 'chosen_ratio':0.5,
                               'preds_for_comb': ['diff_6Z_0-16_last-Prev4_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1215-1415_GEFS6zp11to15': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.4,
                               'preds_for_comb': ['diff_6Z_11-15_last-Prev1_GEFS'
                                                  ],
                               'is_prod': True},
                'y_1215-1415_GEFS6z8to16Comb': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.55,
                               'preds_for_comb': ['MEAN','diff_6Z_8-16_last-Prev1_GEFS','diff_6Z_8-16_last-Prev2_GEFS',
                                                'diff_6Z_8-16_last-Prev3_GEFS','diff_6Z_8-16_last-Prev4_GEFS',
                                                'diff_6Z_11-15_last-Prev1_GEFS','diff_6Z_11-15_last-Prev2_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1215-1415_GEFS6z8to16CombStrict': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.35,
                               'preds_for_comb': ['MEAN','diff_6Z_8-16_last-Prev1_GEFS','diff_6Z_8-16_last-Prev2_GEFS',
                                                'diff_6Z_8-16_last-Prev3_GEFS','diff_6Z_8-16_last-Prev4_GEFS',
                                                'diff_6Z_11-15_last-Prev1_GEFS','diff_6Z_11-15_last-Prev2_GEFS',
                                                  ],
                               'is_prod': True},
                'y_1315-1415_GEFS6zp48to16Comb': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['diff_6Z_11-15_last-Prev4_GEFS','diff_6Z_8-16_last-Prev4_GEFS'
                                                  ],
                               'is_prod': True},
                'y_1115-1145_GEFS6z0to8': {'start': dtdt(2020, 3, 1), 'chosen_ratio':0.35,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1234_GEFS'
                                                  ],
                               'is_prod': True},
                'y_1515-1615_GEFS6z0to8p1': {'start': dtdt(2020, 3, 1), 'chosen_ratio':0.6,
                               'preds_for_comb': ['diff_6Z_0-8_last-Prev1_GEFS'
                                                  ],
                               'is_prod': True},
                'y_1400-1430_PARARACOam': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.65,0.3,0.3,0.4,0.25],
                                    # out of winter maybe leave only PARACO
                                     'preds_for_comb': ['PARACO_AM_basic_pred1', 'MEAN','PARA_AM_basic_pred1',
                                                        'diff_0Z_0-8_Prev1-Prev2_GEPS','diff_12Z_0-2_last-Prev4_PARACO'],
                                     'is_prod': True},
                    'y_0700-0800_PARACO0z': {'start': dtdt(2019,11, 1), 'chosen_ratio': 0.5,
                                   'preds_for_comb': ['diff_0Z_0-16_last-Prev3_PARACO',
                                                      'diff_0Z_0-16_last-Prev2_PARACO',
                                                      'diff_0Z_0-16_last-Prev1_PARACO',
                                                      'diff_0Z_0-16_last-Prev4_PARACO',
                                                      ],
                                   'is_prod': True},
                    'y_0800-1415_PARACO0zp3': {'start': dtdt(2020,9, 1), 'chosen_ratio': 0.5,
                                   'preds_for_comb': ['diff_0Z_0-15_last-Prev3_PARACO',
                                                      'diff_0Z_8-16_last-Prev3_PARACO',
                                                      ],
                                   'is_prod': True},
                    'y_0800-1845_PARACO0zComb': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.5]+[0.4]*2+[0]*7,
                               'preds_for_comb': ['MEAN',
                                            # 'diff_0Z_0-16_last-Prev1D_PARACO','diff_12Z_0-16_last-Prev1D_PARACO',
                                            'diff_0Z_0-16_last-Prev4_PARACO',
                                                'diff_12Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev3_PARACO',
                                        'diff_18Z_0-16_last-Prev1_PARACO','diff_18Z_0-16_last-Prev2_PARACO','diff_18Z_0-16_last-Prev3_PARACO',
                                            'diff_18Z_0-16_last-Prev4_PARACO',
                                    'diff_12Z_0-16_last-Prev2_PARACO',
                                                  # 'diff_12Z_0-16_last-Prev3_PARACO',
                                                  'diff_12Z_0-16_last-Prev4_PARACO',
                                        ],
                               'is_prod': True},
                 'y_0800-1845_PARACO0zCombStrict': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.3]+[0.1]*2+[0]*7,
                               'preds_for_comb': ['MEAN',
                                            'diff_0Z_0-16_last-Prev4_PARACO','diff_12Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev3_PARACO',
                                        'diff_18Z_0-16_last-Prev1_PARACO','diff_18Z_0-16_last-Prev2_PARACO','diff_18Z_0-16_last-Prev3_PARACO',
                                        'diff_18Z_0-16_last-Prev4_PARACO','diff_12Z_0-16_last-Prev2_PARACO', 'diff_12Z_0-16_last-Prev4_PARACO',
                                        ],
                               'is_prod': True},
                 'y_1000-1200_PARACO0zp1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.4,0.4,0.33],
                               'preds_for_comb': ['diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-13_last-Prev1_PARACO',
                                                  'diff_0Z_8-16_last-Prev1_PARACO'],
                               'is_prod': True},
                'y_1715-1845_PARACO018z0to10': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                               'preds_for_comb': ['MEAN','diff_18Z_0-10_last-Prev2_PARACO',
                                                  'diff_0Z_0-10_last-Prev3_PARACO'],
                               'is_prod': True},
                'y_1745-1945_PARACO0zp13': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.5,0.6,0.5,0.4,0.4],
                               'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-13_last-Prev1_PARACO',
                                                  'diff_0Z_8-16_last-Prev1_PARACO',
                                                  'diff_0Z_0-16_last-Prev3_PARACO'
                                                  ],
                               'is_prod': True},
                'y_0600-0800_PARACO0AMpred3': {'start': dtdt(2020,3, 1), 'chosen_ratio': [0.35,0.5],
                                   'preds_for_comb': ['PARACO_AM_pred3','diff_0Z_0-16_last-Prev2_PARACO'],
                                   'is_prod': True},
            'y_0600-0800_PARACO0AMpred3Thu': {'start': dtdt(2020,3, 1), 'chosen_ratio': 0.5,
                                   'preds_for_comb': ['PARACO_AM_pred3'], #'PARACO_PM_pred1','PARACO_6Z_pred1'],
                                   'is_prod': True},
        'y_0600-0800_GFSv160z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.6,
                                 'preds_for_comb': ['MEAN','diff_0Z_8-16_last-Prev4_GFSv16',
                                                    'diff_0Z_8-16_last-Prev2_GFSv16',
                                                    'diff_0Z_8-16_last-Prev1_GFSv16',
                                                    ],
                                 'is_prod': True},
                'y_1400-1415_fri': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                                   # out of winter maybe leave only PARACO
                                   'preds_for_comb': ['diff_18Z_8-16_last-Prev3_PARA','diff_18Z_0-15_last-Prev3_PARA',
                                                      'diff_0Z_0-8_last-Prev1_EC','diff_0Z_28-42_last-Prev4_CFS',
                                                      'diff_0Z_28-42_last-Prev2_CFS'],
                                   'is_prod': True},
                'y_1515-1545_EIA': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.3,0.3,0.5,0.4],#[0.6,0.3,0.3],

                                   'preds_for_comb': ['diff_0Z_14-28_last-Prev1_EPS45',
                                                      'CFS_PM_daily_basic_pred1','PARACO_6Z_pred1',
                                                      'diff_0Z_14-21_last-Prev2D_GEFS35'],
                                   'is_prod': True},
        'y_1515-1545_EIA2': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.4,0.4, 0.3, 0.3],  # [0.6,0.3,0.3],
                            'preds_for_comb': ['MEAN','CFS_PM_daily_basic_pred1','diff_0Z_14-21_last-Prev2D_GEFS35',
                                                'PARACO_6Z_pred1'
                                               ],
                            'is_prod': True},
                'y_1400-1630_GFSEFS6z': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.3,0.5],

                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev1D_PARACO','GFSEFS_6Z_pred3_basic'],
                                 'is_prod': True},
                'y_1415-1745_PMComb': {'start': august20_start, 'chosen_ratio': [0.25,0.4,0.4,0.5,0.5],
                                 'preds_for_comb': ['MEAN','diff_6Z_14-28_Prev1D-Prev2D_CFS','diff_6Z_14-35_Prev1D-Prev2D_CFS',
                                                   'diff_0Z_0-13_last-rolling2_GEFSL','diff_12Z_0-8_last-Prev4_GEM'],
                                 'is_prod': True},
                'y_1415-1745_PMCombMean': {'start': august20_start, 'chosen_ratio': [0.25, 0.4, 0.4, 0.5, 0.4],
                               'preds_for_comb': ['MEAN','diff_12Z_0-8_last-Prev4_GEM', 'diff_6Z_14-28_Prev1D-Prev2D_CFS',
                                                  'diff_6Z_14-35_Prev1D-Prev2D_CFS',
                                                  'diff_0Z_0-13_last-rolling2_GEFSL'],
                               'is_prod': True},
        'y_1415-1845_MonPM': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,
                               'preds_for_comb': ['diff_0Z_21-42_last-Prev4D_CFS','diff_12Z_10-21_last-Prev2D_CFS',
                        'diff_12Z_21-35_last-Prev4D_CFS','diff_12Z_10-21_last-Prev1D_CFS',
                           'diff_12Z_14-16_last-Prev3_PARACO','diff_6Z_14-16_Prev1-Prev2_GEFS',
                           'diff_6Z_0-16_Prev1D-Prev2D_PARA','diff_6Z_0-13_last-Prev3_PARA',
                            'diff_0Z_8-16_Prev1D-Prev2D_GEFSL',
                            'diff_0Z_21-42_last-Prev2D_CFS'],
                               'is_prod': True},
                'y_1245-1745_GEFSCO4D': {'start': march_start, 'chosen_ratio': [0.5,0.5,0.3],

                                       'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_PARACO',
                                                          'diff_0Z_0-16_last-Prev4D_GEFS','diff_0Z_0-16_last-Prev4D_GEFSL'],
                                       'is_prod': True},
                'y_0800-1745_yearly': {'start': july_19_start, 'chosen_ratio': [0.7,0.6,0.5,0.5],

                              'preds_for_comb': ['EPSL_daily_hybrid_pred4','EPSL_daily_hybrid_pred3', 'EPSL_daily_hybrid_pred4.2',
                                                 'GEFSL_PM_pred1_Prev4'],
                              'is_prod': True},
            'y_0800-1945_boll': {'start': dtdt(2018,4,1), 'chosen_ratio': 0.35,

                               'preds_for_comb': ['macd_sign_4H_trend4','macd_sign_4H_trend8','macd_sign_15M_trend4','macd_sign_4H_diff1'],
                               'is_prod': True},
            'y_0800-1400_macdComb': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.48]+[0.1]*5,

                               'preds_for_comb': ['MEAN','location_1H_20_diff1',
                                                  'macd_sign_15M_diff1', 'macd_sign_15M_diff2', 'macd_sign_15M_trend4',
                                                  'WTI_open-open-1', ],
                               'is_prod': True},
            'y_0800-1400_macdCombStrict': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.25]+[0.01]*5,

                               'preds_for_comb': ['MEAN','location_1H_20_diff1',
                                                  'macd_sign_15M_diff1', 'macd_sign_15M_diff2', 'macd_sign_15M_trend4',
                                                  'WTI_open-open-1', ],
                               'is_prod': True},
            'y_0800-1945_PARAVs1Y': {'start': dtdt(2019, 9, 1), 'chosen_ratio': 0.7,#[0.6,0.7,0.6],

                             'preds_for_comb': ['Value_0Z_0-15_-1Y_PARA','MEAN',
                                                'Value_0Z_0-15_-1Y_ma4_PARA'],'is_prod': True},
            'y_1315-1415_americanSeasonalb': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6]+[0.5]*3,  # [0.6,0.7,0.6],

                                     'preds_for_comb': ['Value_6Z_14-16_PARACO_rs','Value_0Z_0-16D_PARACO_rs','Value_0Z_0-16D_GFSCO_rs',
                                                        'Value_0Z_14-16_PARACO_rs'], 'is_prod': True},
        'y_1315-1415_americanSeasonal': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                                    'preds_for_comb': ['Value_0Z_0-16D_PARACO_rs','Value_0Z_0-16D_GFSCO_rs'], 'is_prod': True},
        'y_1445-1545_paracoSeasonal14to16': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.55,
                                          'preds_for_comb': ['Value_6Z_14-16_PARACO_rs',
                                                            'Value_0Z_14-16_PARACO_rs'
                                                             ], 'is_prod': True},
        'y_1200-1330_paracoSeasonal8to16D': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.55,
                                          'preds_for_comb': ['Value_0Z_8-16D_PARACO_rs'], 'is_prod': True},
        'y_1215-1515_PARACO6z14to16': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.65],
                                             'preds_for_comb': ['diff_6Z_14-16_last-Prev1_PARACO'
                                                                ], 'is_prod': True},
        'y_1230-1315_PARACO18z14to16p1': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.44],
                                             'preds_for_comb': ['diff_18Z_14-16_last-Prev1_PARACO'
                                                                ], 'is_prod': True},
        'y_1415-1615_PARACO0z14to16': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4],
                                             'preds_for_comb': ['diff_0Z_14-16_last-Prev2_PARACO'
                                                                ], 'is_prod': True},
        'y_1545-1845_PARACO0z14to16p4': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.4],
                                             'preds_for_comb': ['diff_0Z_14-16_last-Prev4_PARACO'
                                                                ], 'is_prod': True},
        'y_1530-1715_PARACO18z14to16': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.45,
                                             'preds_for_comb': ['diff_18Z_14-16_last-Prev3_PARACO'], 'is_prod': True},

        'y_0800-1100_PARA0z14to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.4],
                            'preds_for_comb': ['diff_0Z_14-16_last-Prev2_PARA'], 'is_prod': True},

        'y_1445-1545_PARA0z14to16p4': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                            'preds_for_comb': ['diff_0Z_14-16_last-Prev4_PARA','diff_0Z_14-16_last-Prev4_GFSv16',
                                               ], 'is_prod': True},

        'y_1200-1315_PARA6z14to16p1': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                            'preds_for_comb': ['diff_6Z_14-16_last-Prev1_PARA'], 'is_prod': True},

        'y_1130-1200_PARA12zp1Neg': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5,0.33,0.1],
                            'preds_for_comb': ['diff_12Z_8-16_last-Prev1_PARA-','MEAN',
                                               'diff_12Z_0-16_last-Prev2_PARA-',
                                               ], 'is_prod': True},

        'y_1545-1645_PARA12z14to16p1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.45],
                            'preds_for_comb': ['diff_12Z_14-16_last-Prev1_GFSv16'], 'is_prod': True},

        'y_1100-1315_PARA12z14to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.5],
                            'preds_for_comb': ['diff_12Z_14-16_last-Prev2_PARA'], 'is_prod': True},

                         'y_0800-1945_PARAVs1Yb': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.7, 0.5, 0.65, 0.65],  # [0.6,0.7,0.6],

                                  'preds_for_comb': ['Value_0Z_0-15_-1Y_PARA', 'Value_0Z_14-16_-1Y_ma4_PARA', 'MEAN',
                                                     'Value_0Z_0-15_-1Y_ma4_PARA'], 'is_prod': True},
        'y_0800-1945_PARAVs1YbStrict': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.3, 0.3, 0.25, 0.25],  # [0.6,0.7,0.6],

                                  'preds_for_comb': ['Value_0Z_0-15_-1Y_PARA', 'Value_0Z_14-16_-1Y_ma4_PARA', 'MEAN',
                                                     'Value_0Z_0-15_-1Y_ma4_PARA'], 'is_prod': True},
        'y_0800-1945_PARAVs1Y14to16': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,

                                  'preds_for_comb': ['Value_0Z_14-16_-1Y_ma4_PARA'], 'is_prod': True},
            'y_0800-1945_PARAVs1Yseasonal': {'start': dtdt(2019, 9, 1), 'chosen_ratio': [0.6, 0.6, 0.6,0.5],

                                 'preds_for_comb': ['Value_0Z_0-15_-1Y_PARA', 'MEAN',
                                                    'Value_0Z_0-15_-1Y_ma4_PARA','Value_0Z_0-13D_EPS_rs'], 'is_prod': True},
            'y_0800-1945_Seasonal': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5,0.5, 0.35, 0.5],
                                 'preds_for_comb': ['Value_0Z_0-16D_PARACO_rs',
                                                    'Value_0Z_0-13D_EPS_rs','MEAN',
                                                    'Value_0Z_0-16_GEFS_rs'], 'is_prod': True},
            'y_0800-1100_PARA0ZSeasonal': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['Value_0Z_0-16_PARA_rs'], 'is_prod': True},
            'y_0800-1100_GEFS0Z14to16Seasonal': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['Value_0Z_14-16_GEFS_rs'], 'is_prod': True},
            'y_0400-0800_Seasonal': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5,0.5, 0.5, 0.5],
                                 'preds_for_comb': ['MEAN','Value_12Z_0-13_EPS_rs',  # 'Value_0Z_0-13D_EPS_rs',
                                                    'Value_0Z_0-16D_PARACO_rs', 'Value_0Z_0-16_GEFS_rs'],
                                 'is_prod': True},
            'y_0000-0600_18Z': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.6,0.6,0.4],#[0.6, 0.4,0.4],
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev1_PARA','MEAN',
                                                    'diff_18Z_0-16_last-Prev2_PARA'],
                                 'is_prod': True},
            'y_1300-1330_PARA018Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev1_PARA',
                                                    'diff_0Z_0-16_last-Prev2_PARA',
                                                    'diff_0Z_0-16_last-Prev1234_PARA',
                                                    'diff_0Z_0-16_last-Prev4_PARA',

                                                    'diff_18Z_0-16_last-Prev2_PARA',
                                                    'diff_18Z_0-16_last-Prev3_PARA',
                                                    'diff_0Z_0-16_last-Prev1_PARA',
                                                    'diff_0Z_0-16_last-Prev3_PARA'
                                                    ][:3],
                                 'is_prod': True},
            'y_1300-1545_PARA018Z0to8': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.4,0.6],
                                 'preds_for_comb': ['diff_18Z_0-8_last-Prev1_PARA',
                                                    'diff_0Z_0-8_last-Prev2_PARA',
                                                    'MEAN'
                                                    ],
                                 'is_prod': True},
        'y_1430-1500_PARApre6z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.6,
                                 'preds_for_comb': ['PARA_pre6Z_pred2'],
                                 'is_prod': True},
        'y_1200-1315_PARA012Z0to8': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.55,
                                 'preds_for_comb': ['diff_12Z_0-8_Prev1-Prev2_PARA'
                                                    ],
                                 'is_prod': True},
        'y_0800-1200_PARA18Zp3': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55],
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev3_PARA',
                                                    ],
                                 'is_prod': True},
        'y_0400-0600_PARA18Zp23': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev2_PARA','diff_18Z_8-16_last-Prev3_PARA',
                                                    ],
                                 'is_prod': True},
        'y_1300-1430_PARA18Zp2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev2_PARA',
                                                    ],
                                 'is_prod': True},
        'y_0700-0730_PARA18Zp14': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['MEAN','diff_18Z_0-16_last-Prev4_PARA','diff_18Z_0-16_last-Prev1_PARA','diff_18Z_8-16_last-Prev1_PARA'
                                                    ],
                                 'is_prod': True},
        'y_0800-1200_PARA18Zp3Strict': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.25],
                                 'preds_for_comb': ['diff_18Z_0-16_last-Prev3_PARA',
                                                    ],
                                 'is_prod': True},
        'y_0800-1200_PARACO12Zp1': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev1_PARACO',

                                                    ],
                                 'is_prod': True},
        'y_1100-1745_PARACO12Zp2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.63,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev2_PARACO',
                                                    ],
                                 'is_prod': True},
        'y_1145-1245_PARACO12Zp3': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': ['MEAN',
                                                'diff_12Z_0-15_last-Prev3_PARACO', 'diff_12Z_0-13_last-Prev3_PARACO',
                                                 ],
                                 'is_prod': True},
        'y_1145-1245_PARACO12Zprev1p30to10': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.55,0.33,0.33],
                                 'preds_for_comb': ['MEAN',
                                                'diff_12Z_0-10_Prev1-Prev3_PARACO','diff_12Z_0-13_Prev1-Prev3_PARACO',
                                                #'diff_12Z_0-8_Prev1-Prev3_PARACO'
                                                 ],
                                 'is_prod': True},
        'y_0800-1200_PARARACO18ZComb': {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['MEAN','diff_18Z_0-16_last-Prev1_PARA','diff_18Z_0-16_last-Prev2_PARA','diff_18Z_0-16_last-Prev3_PARA','diff_18Z_0-16_last-Prev4_PARA',
                                                'diff_18Z_0-16_last-Prev2_PARACO','diff_18Z_0-16_last-Prev3_PARACO','diff_18Z_0-16_last-Prev4_PARACO'],
                                 'is_prod': True},
        'y_1300-1545_PARA018Z8to16': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.4,0.6],
                                 'preds_for_comb': ['diff_18Z_8-16_last-Prev1_PARA',
                                                    'diff_0Z_8-16_last-Prev2_PARA',
                                                    'MEAN'
                                                    ],
                                 'is_prod': True},
        'y_1300-1545_PARA018Z0to8Strict': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.3,0.2,0.2],
                                 'preds_for_comb': ['diff_18Z_0-8_last-Prev1_PARA',
                                                    'diff_0Z_0-8_last-Prev2_PARA',
                                                    'MEAN'
                                                    ],
                                 'is_prod': True},
            'y_0000-0600_cash': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                            'preds_for_comb': ['diff_12Z_0-2_last-Prev2_GEPS','diff_12Z_0-0_last-Prev2_GEPS',
                                               'diff_12Z_0-2_last-Prev2_GEMCO',
                                               #'diff_12Z_0-2_last-Prev2_PARA']
                                               ][:2],
                            'is_prod': True},
        'y_0800-1400_cash': {'start': dtdt(2020, 2, 1), 'chosen_ratio': 0.4,
                             'preds_for_comb': ['diff_0Z_0-0_Prev1-Prev3_PARACO','diff_0Z_0-0_Prev1-Prev3_GEFS',
                                                'diff_0Z_0-0_last-Prev3_PARACO','diff_0Z_0-2_last-Prev2_EPS',
                                                 'diff_0Z_0-2_last-Prev4_EPS',
                            'diff_0Z_0-2_last-Prev4_EC','diff_0Z_0-2_last-Prev2_EC',
                         'diff_0Z_0-0_Prev1-Prev3_EC','diff_0Z_0-0_last-Prev2_EC',
                            'diff_12Z_0-0_last-Prev2_EPS','diff_12Z_0-0_last-Prev4_EPS',
                            'diff_12Z_0-8_last-Prev4_GEM',
                                                     ][:7],
                             'is_prod': True},
        'y_0800-1400_cash2': {'start': dtdt(2020, 2, 1), 'chosen_ratio': [0.4,0.6]+[0.5,0.5,0.5,0.5,0.5],
                             'preds_for_comb': ['diff_0Z_0-0_last-Prev3_GEFS',
                                                'diff_18Z_0-0_last-Prev2_PARACO',
                                                #'MEAN',
                                                'diff_0Z_0-0_Prev1-Prev3_GEFS',
                                               'diff_12Z_0-0_last-Prev3_EPS', 'diff_0Z_0-0_last-Prev3_PARACO',
                                                #'diff_0Z_0-2_last-Prev4_EPS',
                                                'diff_0Z_0-2_last-Prev4_EC', #'diff_0Z_0-2_last-Prev2_EC',
                                                'diff_0Z_0-0_Prev1-Prev3_EC'
                                                ],
                             'is_prod': True},
        'y_1215-1315_cashEC': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.5,0.4],
                              'preds_for_comb': ['diff_12Z_0-0_last-Prev4_EPS',
                                                 'diff_12Z_0-0_last-Prev4_EC',
                                                 'diff_12Z_0-8_last-Prev4_GEM'],
                              'is_prod': True},
        'y_0530-0630_cashGEPSEPS': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.5,0.5,0.5,0.3,0.2],
                              'preds_for_comb': ['MEAN','diff_12Z_0-0_Prev1-Prev2_EPS',
                                                 'diff_12Z_0-2_Prev1-Prev2_EPS','diff_12Z_0-0_last-Prev4_EPS', 'diff_12Z_0-2_last-Prev3_GEFS'],
                              'is_prod': True},
        'y_1430-1615_AmericanCashComb0d': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.4]+[0.15]*5,
                              'preds_for_comb': ['MEAN','diff_0Z_0-0_last-Prev3_GEFS', 'diff_12Z_0-0_last-Prev1_GFSv16', 'diff_0Z_0-0_last-Prev4_GFSv16', 'diff_12Z_0-0_last-Prev2_GEFS', 'diff_0Z_0-0_last-Prev4_GEFS'],
                              'is_prod': True},
        'y_1145-1415_cashGFS': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.6,0.55],
                              'preds_for_comb': ['diff_6Z_0-2_last-Prev4_GFSv16',
                                                 'diff_18Z_0-2_last-Prev2_GFSv16'],
                              'is_prod': True},
        'y_1300-1315_PARA18z0to2p3': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_18Z_0-2_last-Prev3_PARA'],
                              'is_prod': True},
        'y_1745-1845_cashPARACO12z0to2': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.55,
                              'preds_for_comb': ['diff_12Z_0-2_last-Prev2_PARACO'],
                              'is_prod': True},
        'y_1430-1615_cashPARACO12z0to2p1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_12Z_0-2_last-Prev1_PARACO', 'diff_18Z_0-2_Prev1-Prev2_PARACO'],
                              'is_prod': True},
        'y_1100-1245_cashGFS0d': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.55,
                              'preds_for_comb': ['diff_12Z_0-0_last-Prev3_GFSv16'
                                                 ],
                              'is_prod': True},
        'y_0800-1415_cashGEF35S0d': {'start': dtdt(2021, 7, 1), 'chosen_ratio': 0.4,
                              'preds_for_comb': ['MEAN','diff_0Z_0-0_last-Prev3_GEFS35',
                                                  'diff_0Z_0-0_last-Prev2_GEFS35','diff_0Z_0-0_Prev1-Prev2_GEFSCO35'
                                                 ],
                              'is_prod': True},
        'y_1200-1315_cashGFS0d': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.4,
                              'preds_for_comb': ['diff_6Z_0-0_Prev1-Prev2_PARA'
                                                 ],
                              'is_prod': True},
        'y_1715-1845_cashGFS0z2d': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_0Z_0-2_last-Prev1_PARA'
                                                 ],
                              'is_prod': True},
        'y_1915-2030_cashGFS12z0dp1': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_18Z_0-0_Prev1-Prev2_PARA'
                                                 ],
                              'is_prod': True},
        'y_1300-1430_cashGEM': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_12Z_0-2_last-Prev4_GEM'],
                              'is_prod': True},
        'y_1100-1200_cashGEM0zp10to2': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_0Z_0-2_last-Prev1_GEM'],
                              'is_prod': True},
        'y_0800-1845_cashGEM0zp10d': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.4,
                              'preds_for_comb': ['diff_0Z_0-0_last-Prev1_GEM'],
                              'is_prod': True},
        'y_1430-1500_cashGEM0zp30to2': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_0Z_0-2_last-Prev3_GEM'],
                              'is_prod': True},
        'y_1515-1845_GEM0zp20to8': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.4,
                              'preds_for_comb': ['diff_0Z_0-8_last-Prev2_GEM'],
                              'is_prod': True},
        'y_1515-1845_GEM0zp20to10': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_0Z_0-10_last-Prev2_GEM'],
                              'is_prod': True},
        'y_0800-1215_cashGFSEPS0d': {'start': dtdt(2020, 6, 1), 'chosen_ratio': [0.5,0.45,0.45],
                              'preds_for_comb': ['MEAN','diff_12Z_0-0_last-Prev2_EPS',
                                                 'diff_18Z_0-0_last-Prev4_GEFS'],
                              'is_prod': True},
        'y_1200-1400_cashGEFS0d': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.55,
                              'preds_for_comb': ['diff_12Z_0-0_last-Prev2_GEFS'],
                              'is_prod': True},
        'y_1100-1230_cashGEFS6z0d': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_6Z_0-0_last-Prev1_GEFS'],
                              'is_prod': True},
        'y_1100-1230_cashGEFS6z0dStrict': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.33,
                              'preds_for_comb': ['diff_6Z_0-0_last-Prev1_GEFS'],
                              'is_prod': True},
        'y_0800-1945_GEFS18zp1': {'start': dtdt(2021, 6, 1), 'chosen_ratio': 0.48,
                              'preds_for_comb': ['diff_18Z_0-16_last-Prev1_GEFS'],
                              'is_prod': True},
        'y_0800-1945_GEFS18zp2': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.48,
                              'preds_for_comb': ['diff_18Z_0-16_last-Prev2_GEFS'],
                              'is_prod': True},
        'y_0800-1945_GEFS18zp3': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.48,
                              'preds_for_comb': ['diff_18Z_0-16_last-Prev3_GEFS'],
                              'is_prod': True},
        'y_0800-1945_GEFS18zp4': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.48,
                              'preds_for_comb': ['diff_18Z_0-16_last-Prev4_GEFS'],
                              'is_prod': True},
        'y_0800-1845_cashGEFS18z0to2d': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_0Z_0-2_Prev1-Prev2_GEFS'],
                              'is_prod': True},
        'y_1430-1615_cashGEFS18z0to2dp3': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['MEAN','diff_18Z_0-2_last-Prev3_GEFS',
                                                 'diff_0Z_0-2_last-Prev3_PARACO'],
                              'is_prod': True},
        'y_1230-1315_GEPS0Z0dp3': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,
                              'preds_for_comb': ['diff_0Z_0-0_last-Prev3_GEPS'],
                              'is_prod': True},
        'y_1700-1745_cashGEFS18Z0to2p4': {'start': dtdt(2020, 6, 1), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_18Z_0-2_last-Prev4_GEFS'],
                              'is_prod': True},
        'y_1100-1300_cashCFS': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_0Z_0-2_last-Prev2_CFS'],
                              'is_prod': True},
        'y_0000-0600_CFScash12z0d': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,
                              'preds_for_comb':['MEAN',
                                     'diff_12Z_0-0_last-Prev2_CFS',
                                     'diff_12Z_0-0_last-Prev3_CFSCO','diff_12Z_0-0_last-Prev4_CFSCO',
                                     'diff_12Z_0-0_last-Prev1_CFS','diff_12Z_0-0_last-Prev2_CFSCO',
                                     'diff_12Z_0-0_last-Prev1_CFSCO'],
                              'is_prod': True},
        'y_1100-1300_cashCFStrict': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.25,
                              'preds_for_comb': ['diff_0Z_0-2_last-Prev2_CFS'],
                              'is_prod': True},
        'y_1700-1745_cashCFSCO0d': {'start': dtdt(2021, 4, 15), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_0Z_0-0_last-Prev4_CFSCO'],
                              'is_prod': True},
        'y_1330-1430_cashCFS6zp1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                              'preds_for_comb': ['diff_6Z_0-2_last-Prev1_CFS',
                                                 'diff_6Z_0-2_last-Prev3_CFS'],
                              'is_prod': True},
        'y_1400-1530_cashPARACO0Z0to2p2': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.45,
                              'preds_for_comb': ['diff_6Z_0-2_Prev1-Prev3_PARACO'],
                              'is_prod': True},
        'y_1330-1415_cashPARA6Z0to2': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5]+[0.4]*3+[0.1],
                              'preds_for_comb': ['MEAN','diff_6Z_0-2_Prev1-Prev3_PARA',
                                                 'diff_0Z_0-2_last-Prev3_PARA','diff_6Z_0-2_Prev1-Prev2_PARA',
                                                 'diff_0Z_0-2_Prev1-Prev3_PARA'],
                              'is_prod': True},
        'y_0400-0600_american': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
                             'preds_for_comb': ['diff_12Z_0-16_last-Prev2D_PARACO','diff_12Z_0-16_last-Prev2D_GEFS',
                                                'diff_18Z_0-16_last-Prev1_GEFS'],
                             'is_prod': True},
        'y_0600-0800_GEPFS1D': {'start':nov_start, 'chosen_ratio': [0.7,0.4,0.4],
                                 'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev1D_GEPS',
                                                    'diff_0Z_8-16_last-Prev1D_GEFS'],
                                 'is_prod': True},
        'y_0600-0815_GEMCO': {'start':march_start, 'chosen_ratio': [0.5,0.4],
                                 'preds_for_comb': ['GEMCO_PM_pred1','diff_0Z_8-16_last-Prev2_GEMCO'],
                                 'is_prod': True},
        'y_0600-0815_GEMCOSummer': {'start':march_start, 'chosen_ratio': [0.5,0.4],
                                 'preds_for_comb': ['GEMCO_PM_pred1','diff_0Z_8-16_last-Prev2_GEMCO'],
                                 'is_prod': True},
        'y_0730-0815_GEMCO0z14to16': {'start':dtdt(2020,7,1), 'chosen_ratio': [0.5,0.5,0.45],
                                 'preds_for_comb': ['MEAN','diff_0Z_14-16_last-Prev3_GEMCO','diff_0Z_14-16_last-Prev2_GEMCO'],
                                 'is_prod': True},
        'y_0800-1845_GEMCO0z': {'start':dtdt(2020,7,1), 'chosen_ratio':0.45,
                                 'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev1_GEMCO',
                                                    'diff_0Z_0-16_last-Prev2_GEMCO',
                                                    # 'diff_0Z_0-16_last-Prev3_GEMCO','diff_0Z_0-16_last-Prev4_GEMCO',
                                                    ],
                                 'is_prod': True},
        'y_0800-1200_GEM0Z': {'start':march_start, 'chosen_ratio': [0.5,0.4,0.4],
                                 'preds_for_comb': ['GEM_AM_pred1','diff_0Z_0-10_last-Prev1D_GEM',
                                                    'diff_0Z_0-10_last-Prev2D_GEM'],
                                 'is_prod': True},
        'y_0800-1200_GEM0Zp1Strict': {'start':march_start, 'chosen_ratio': 0.33,
                                 'preds_for_comb': ['diff_0Z_0-10_last-Prev1_GEM'],
                                 'is_prod': True},
        'y_0700-0730_GEM0Zp4': {'start':march_start, 'chosen_ratio': [0.5,0.4],
                                 'preds_for_comb': ['GEM_AM_pred2','diff_0Z_0-10_last-Prev4_GEM'],
                                 'is_prod': True},
        'y_0800-1945_PARACO24D': {'start': nov_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev14D_PARARACO','GEFSL_daily_hybrid_pred3'],
                                'is_prod': True},

        'y_0800-1945_PARACOhybrid1': {'start': nov_start, 'chosen_ratio': 0.6,
                                'preds_for_comb': ['PARACO_daily_hybrid_pred1'],
                                'is_prod': True},

        'y_0800-1945_GEPSCO': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.45,
                                  'preds_for_comb': ['GEPS_daily_hybrid_pred3','diff_0Z_0-16_last-Prev24D_GEMCO'],
                                  'is_prod': True},
        'y_0630-0800_GEMCOPrev1D0z8to16':{'start': dtdt(2020,11,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_0Z_8-16_Prev1D-Prev2D_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEMCOOPrev14D0to10':{'start': dtdt(2020,7,1), 'chosen_ratio': [0.35,0.45,0.45],
                                  'preds_for_comb':['diff_12Z_0-10_Prev1D-Prev2D_GEMCO','diff_12Z_0-10_last-Prev4D_GEMCO',
                                                    'MEAN'],
                                  'is_prod': True},
        'y_0800-1845_GEMCOOPrev14D0to10Strict':{'start': dtdt(2020,7,1), 'chosen_ratio': [0.25,0.2,0.3],
                                  'preds_for_comb':['diff_12Z_0-10_Prev1D-Prev2D_GEMCO','diff_12Z_0-10_last-Prev4D_GEMCO',
                                                    'MEAN'],
                                  'is_prod': True},
        'y_0800-1415_GEMCO12z2D0to16':{'start': dtdt(2020,9,1), 'chosen_ratio': 0.4,
                                  'preds_for_comb': ['diff_12Z_0-16_last-Prev2D_GEMCO'],
                                  'is_prod': True},
        'y_1100-1415_GEMCOPrev1D':{'start': dtdt(2020,4,1), 'chosen_ratio': [0.4,0.33,0.15],
                                  'preds_for_comb': ['diff_0Z_0-16_Prev1D-Prev2D_GEMCO','diff_0Z_8-16_Prev1D-Prev3D_GEMCO',
                                        'diff_0Z_0-16_Prev1D-Prev3D_GEMCO'],
                                  'is_prod': True},
        'y_1100-1415_GEMCOPrev1DSummer':{'start': dtdt(2020,4,1), 'chosen_ratio': [0.5,0.33,0.33],
                                  'preds_for_comb': ['diff_0Z_8-16_Prev1D-Prev3D_GEMCO','diff_0Z_0-16_Prev1D-Prev2D_GEMCO',
                                        'diff_0Z_0-16_Prev1D-Prev3D_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEMCOam':{'start': dtdt(2020,4,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['GEMCO_AM_pred2'],
                                  'is_prod': True},
        'y_1100-1415_GEMCOPrev1DSummerMean':{'start': dtdt(2020,4,1), 'chosen_ratio': [0.5,0.05,0.05,0.05],
                                  'preds_for_comb': ['MEAN','diff_0Z_8-16_Prev1D-Prev3D_GEMCO','diff_0Z_0-16_Prev1D-Prev2D_GEMCO',
                                        'diff_0Z_0-16_Prev1D-Prev3D_GEMCO'],
                                  'is_prod': True},
        'y_1200-1415_GEMCO12zPrev1Doto10':{'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                  'preds_for_comb': ['diff_12Z_0-10_Prev1D-Prev2D_GEMCO'],
                                  'is_prod': True},
        'y_1815-1915_GEMCO0zPrev1D0to10': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_0Z_0-10_Prev1D-Prev3D_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEPS12zPrev1D0to10': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_12Z_0-10_Prev1D-Prev3D_GEPS'],
                                  'is_prod': True},
        'y_0800-1845_GEPS0zPrev1D0to10': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.55,
                                  'preds_for_comb': ['diff_0Z_0-10_Prev1D-Prev3D_GEPS'],
                                  'is_prod': True},
        'y_1200-1415_GEMCO12z1Doto10':{'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                  'preds_for_comb': ['diff_12Z_0-10_last-Prev1D_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEMCO12z1Doto16':{'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                  'preds_for_comb': ['diff_12Z_0-16_last-Prev1D_GEMCO'],
                                  'is_prod': True},
        'y_1200-1415_GEMCO12zPrev1Dbasic':{'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_12Z_0-16_Prev1D-Prev3D_GEMCO',
                                                     'diff_12Z_8-16_Prev1D-Prev3D_GEMCO'],
                                  'is_prod': True},
        'y_1745-1915_GEMCO12zp1':{'start': dtdt(2020,8,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEMCO12zComb':{'start': dtdt(2020,8,1), 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev1_GEMCO','diff_12Z_0-16_last-Prev2_GEMCO',
                                                     'diff_12Z_0-16_last-Prev3_GEMCO','diff_12Z_0-16_last-Prev4_GEMCO'],
                                  'is_prod': True},
        'y_0800-1845_GEMCO12zCombStrict':{'start': dtdt(2020,8,1), 'chosen_ratio': 0.3,
                                  'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev1_GEMCO','diff_12Z_0-16_last-Prev2_GEMCO',
                                                     'diff_12Z_0-16_last-Prev3_GEMCO','diff_12Z_0-16_last-Prev4_GEMCO'],
                                  'is_prod': True},
        'y_1400-1745_GFSv1614D': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.6,0.4,0.3,0.3],
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_GFSv16', 'diff_0Z_0-16_last-Prev2D_GFSv16',
                                                     'diff_0Z_0-16_last-Prev4D_GFSv16','diff_6Z_0-16_last-Prev1_GFSv16'],
                                  'is_prod': True},
        'y_1100-1130_GFSv164D': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5, 0.3, 0.3],
                               'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_GFSv16','diff_0Z_0-16_last-Prev2D_GFSv16',
                                                  'diff_0Z_0-16_last-Prev1D_GFSv16'
                                                  ],
                               'is_prod': True},
        'y_0600-1945_GFSv163D': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.75, 0.6, 0.6],
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev3D_GFSv16',
                                                    'diff_0Z_0-16_last-Prev2D_GFSv16',
                                                    'diff_0Z_0-16_last-Prev1D_GFSv16'
                                                    ],
                                 'is_prod': True},
        'y_1515-1915_PARA6Z3D0to10': {'start': dtdt(2020, 7, 1), 'chosen_ratio': 0.6,
                                 'preds_for_comb': ['diff_6Z_0-10_last-Prev3D_GFSv16'],
                                 'is_prod': True},
        'y_1100-1300_PARA18Z3D8to16': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.66,
                                 'preds_for_comb': ['diff_18Z_8-16_last-Prev3D_PARA'],
                                 'is_prod': True},
        'y_1100-1300_PARA18Z3D8to16Strict': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_18Z_8-16_last-Prev3D_PARA'],
                                 'is_prod': True},
            'y_0800-1945_PARAVs1Ystrict': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.5,

                                 'preds_for_comb': ['Value_0Z_0-15_-1Y_ma4_PARA',
                                                    'Value_0Z_0-15_-1Y_PARA'],
                                 'is_prod': True},
        'y_1245-1545_EPS45': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.45, 0.4, 0.3],

                                       'preds_for_comb': ['diff_0Z_14-35_last-Prev4D_EPS45',
                                                          'diff_0Z_14-35_last-Prev3D_EPS45',
                                                          'diff_0Z_14-35_last-Prev2D_EPS45'],
                                       'is_prod': True},
        'y_0600-0700_EPS45week3': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5, 0.5, 0.35],

                              'preds_for_comb': ['diff_0Z_14-21_last-Prev2_EPS45',
                                                 'diff_0Z_14-28_last-Prev2_EPS45',
                                                 'diff_0Z_14-21_last-Prev1_EPS45',
                                                 ],
                              'is_prod': True},
            'y_0600-0700_EPS45week3Mean': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5, 0.5, 0.35],

                                   'preds_for_comb': ['diff_0Z_14-21_last-Prev2_EPS45',
                                                      'diff_0Z_14-28_last-Prev2_EPS45',
                                                      'diff_0Z_14-21_last-Prev1_EPS45',
                                                      ],
                                   'is_prod': True},
        'y_0400-0800_EPS4528to42p1': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5, 0.4, 0.3],
                                    'preds_for_comb': ['diff_0Z_28-42_last-Prev1_EPS45',
                                                       'diff_0Z_28-42_last-Prev1_EPS45',
                                                       'diff_0Z_28-42_last-Prev2_EPS45',
                                                       ],
                                    'is_prod': True},
        'y_0400-0800_EPS4528to42': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5, 0.4, 0.5,0.3],

                                       'preds_for_comb': ['diff_0Z_28-42_last-Prev2_EPS45',
                                                            'EPS45_rolling_diff3',
                                                          'diff_0Z_28-42_last-Prev3_EPS45','diff_0Z_28-42_last-Prev1_EPS45'
                                                          ],
                                       'is_prod': True},
        'y_0400-0800_EPS4528to42Mean': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5, 0.4,0.4, 0.4, 0.3],

                                    'preds_for_comb': ['MEAN','diff_0Z_28-42_last-Prev2_EPS45',
                                                       'EPS45_rolling_diff3',
                                                       'diff_0Z_28-42_last-Prev3_EPS45',
                                                       'diff_0Z_28-42_last-Prev1_EPS45'
                                                       ],
                                    'is_prod': True},
        'y_1100-1200_EPS4528to42b': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.35, #[0.4, 0.4, 0.4],

                                    'preds_for_comb': ['diff_0Z_28-42_last-Prev2_EPS45',
                                                       'diff_0Z_28-42_last-Prev1_EPS45',
                                                       'diff_0Z_28-42_last-Prev3_EPS45',
                                                       'diff_0Z_14-28_last-Prev1_EPS45'
                                                       ],
                                    'is_prod': True},
        'y_1100-1200_EPS4528to42bNeg': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.35, #[0.4, 0.4, 0.4],

                                    'preds_for_comb': ['diff_0Z_28-42_last-Prev2_EPS45-',
                                                       'diff_0Z_28-42_last-Prev1_EPS45-',
                                                       'diff_0Z_28-42_last-Prev3_EPS45-',
                                                       'diff_0Z_14-28_last-Prev1_EPS45-'
                                                       ],
                                    'is_prod': True},
        'y_1200-1300_EPS4514to28': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.4, 0.4, 0.3, 0.3],

                                    'preds_for_comb': ['diff_0Z_14-21_last-Prev3_EPS45','diff_0Z_14-21_last-Prev2_EPS45',
                                                       'diff_0Z_14-28_last-Prev4_EPS45',
                                                       'diff_0Z_14-21_last-Prev1_EPS45'
                                                       ],
                                    'is_prod': True},
        'y_1100-1745_EPS4514to28rolling2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.65,

                                    'preds_for_comb': ['diff_0Z_14-28_last-Prev12_EPS45'],
                                    'is_prod': True},
        'y_1200-1300_EPS4514to281D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.3],

                                    'preds_for_comb': ['diff_0Z_14-28_last-Prev1_EPS45'],
                                    'is_prod': True},
        'y_1200-1300_EPS4514to421D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.3],

                                    'preds_for_comb': ['diff_0Z_14-42_last-Prev1_EPS45'],
                                    'is_prod': True},
        'y_1200-1300_EPS4514to421Dloose': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.45],

                                    'preds_for_comb': ['diff_0Z_14-42_last-Prev1_EPS45'],
                                    'is_prod': True},
        'y_1200-1300_EPS4514to422D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.35,

                                      'preds_for_comb': ['diff_0Z_14-28_last-Prev2_EPS45','diff_0Z_14-28_last-Prev4_EPS45',
                                                         'diff_0Z_28-42_last-Prev2_EPS45'],
                                      'is_prod': True},
        'y_1315-1745_EPS4528to423D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,

                                      'preds_for_comb': ['MEAN','diff_0Z_28-42_last-Prev3_EPS45','diff_0Z_28-42_Prev1-Prev3_EPS45',
                                                         'diff_0Z_28-42_last-Prev4_EPS45'],
                                      'is_prod': True},
        'y_1315-1745_EPS4528to423DNeg': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,

                                      'preds_for_comb': ['MEAN','diff_0Z_28-42_last-Prev3_EPS45-','diff_0Z_28-42_Prev1-Prev3_EPS45-',
                                                         'diff_0Z_28-42_last-Prev4_EPS45-'],
                                      'is_prod': True},
        'y_1315-1745_EPS4528to423DPrev1': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,

                                      'preds_for_comb': ['diff_0Z_28-42_Prev1-Prev3_EPS45'],
                                      'is_prod': True},
        'y_1315-1745_EPS4528to423DPrev1Neg': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.45,

                                      'preds_for_comb': ['diff_0Z_28-42_Prev1-Prev3_EPS45-'],
                                      'is_prod': True},
        'y_1315-1745_EPS4528to423DStrict': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.2,

                                      'preds_for_comb': ['MEAN','diff_0Z_28-42_last-Prev3_EPS45','diff_0Z_28-42_Prev1-Prev3_EPS45',
                                                         'diff_0Z_28-42_last-Prev4_EPS45'],
                                      'is_prod': True},
        'y_1200-1300_EPS4514to423D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.35,

                                      'preds_for_comb': ['diff_0Z_14-28_last-Prev3_EPS45',
                                                         'diff_0Z_14-21_last-Prev4_EPS45',
                                                         'diff_0Z_14-28_last-Prev4_EPS45',
                                                         'diff_0Z_28-42_last-Prev2_EPS45'],
                                      'is_prod': True},
        'y_1200-1300_EPS4528to422D': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.5,

                                      'preds_for_comb': ['diff_0Z_28-42_last-Prev2_EPS45'],
                                      'is_prod': True},
        'y_1100-1200_EPS4514to21Prev1D': {'start': dtdt(2022, 4, 1), 'chosen_ratio': 0.5,

                                      'preds_for_comb': ['diff_0Z_14-21_Prev1D-Prev3D_EPS45'],
                                      'is_prod': True},
        'y_1100-1200_EPS4514to35Prev1D': {'start': dtdt(2022, 4, 1), 'chosen_ratio': 0.44,

                                      'preds_for_comb': ['diff_0Z_14-35_Prev1D-Prev3D_EPS45'],
                                      'is_prod': True},
        'y_1715-1915_EPS450to13': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.5,
                                      'preds_for_comb': ['diff_0Z_0-13_last-Prev2_EPS45',
                                                         'diff_0Z_0-13_last-Prev3_EPS45'],
                                      'is_prod': True},
        'y_0800-1845_EPS450to2': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.48,
                                      'preds_for_comb': ['MEAN','diff_0Z_0-2_last-Prev2_EPS45','diff_0Z_0-2_Prev1-Prev2_EPS45'],
                                      'is_prod': True},
        'y_0800-1845_EPS450to13prev1': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.48,
                                      'preds_for_comb': ['diff_0Z_0-13_Prev1-Prev3_EPS45'],
                                      'is_prod': True},
        'y_1315-1415_EPS450to13Comb': {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5,0.3,0.2,0.2,0.2,0.2,0.2],
                                      'preds_for_comb': ['MEAN','diff_0Z_0-13_last-Prev3_EPS45','diff_0Z_0-2_last-Prev4_EPS45','diff_0Z_0-2_last-Prev3_EPS45',
                                             'diff_0Z_0-13_Prev1-Prev3_EPS45','diff_0Z_0-2_Prev1-Prev3_EPS45','diff_0Z_0-13_Prev1-Prev2_EPS45'],
                                      'is_prod': True},
            'y_1300-1345_cfs28to42': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.35,0.35],
                                 'preds_for_comb': ['diff_0Z_28-42_last-Prev1D_CFS',
                                                    'diff_12Z_28-42_last-Prev1234_CFS'],
                                 'is_prod': True},
            'y_1715-1815_cfs6Z28to42': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6, 0.4,0.2,0.2],  # 0.7

                                  'preds_for_comb': ['diff_6Z_28-42_last-Prev4_CFS',
                                                     'diff_6Z_28-42_last-Prev3_CFS',
                                            'diff_0Z_28-42_Prev1-Prev3_CFS','MEAN'],
                                            'is_prod': True},
            'y_1715-1815_cfs6Z28to42p2': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.44,
                                  'preds_for_comb': ['diff_6Z_28-42_last-Prev2_CFS'],
                                  'is_prod': True},
        'y_1315-1715_cfs6Z0to21': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5, 0.5],  # 0.7

                                    'preds_for_comb': ['diff_6Z_0-21_last-Prev2_CFS',
                                                       'diff_6Z_0-21_last-Prev1234_CFS'],
                                    'is_prod': True},
        'y_0100-0400_cfs18Z0to16': {'start': dtdt(2021, 8, 1), 'chosen_ratio':[0.44,0.54,0.4],

                                    'preds_for_comb': ['MEAN','diff_18Z_8-16_last-Prev2_CFS','diff_18Z_0-16_last-Prev2_CFS',],
                                    'is_prod': True},
        'y_1445-1645_cfs18Z0to21': {'start': dtdt(2020, 5, 1), 'chosen_ratio':0.5,

                                    'preds_for_comb': ['diff_18Z_0-21_last-Prev2_CFS'],
                                    'is_prod': True},
        'y_1315-1415_cfs18Z14to18p4': {'start': dtdt(2020, 5, 1), 'chosen_ratio':[0.45,0.4,0.3],

                                    'preds_for_comb': ['diff_18Z_14-18_last-Prev4_CFS','MEAN',
                                                       'diff_0Z_14-18_Prev1-Prev3_CFS'],
                                    'is_prod': True},
        'y_1200-1330_CFSpre6z': {'start': dtdt(2020, 5, 1), 'chosen_ratio':0.55,
                                    'preds_for_comb': ['CFSpre_6Z_pred2'
                                                       ],
                                    'is_prod': True},
        'y_1300-1400_cfs0Z0to21': {'start': dtdt(2020, 5, 1), 'chosen_ratio':0.55,

                                    'preds_for_comb': ['diff_0Z_0-21_last-Prev2_CFS','diff_0Z_0-21_last-Prev1234_CFS',
                                                       ],
                                    'is_prod': True},
        'y_1300-1400_cfs0Z0to21Strict': {'start': dtdt(2020, 5, 1), 'chosen_ratio':0.3,

                                    'preds_for_comb': ['diff_0Z_0-21_last-Prev2_CFS','diff_0Z_0-21_last-Prev1234_CFS',
                                                       ],
                                    'is_prod': True},
        'y_1315-1715_CFSCO6ZComb': {'start': dtdt(2021, 1, 1), 'chosen_ratio':0.5,

                                    'preds_for_comb': ['diff_6Z_0-21_last-Prev2_CFSCO',
                                                    'diff_6Z_14-28_last-Prev1_CFSCO','diff_6Z_14-28_last-Prev1_CFSCO',
                                                    'diff_6Z_14-28_last-Prev3_CFSCO','diff_6Z_14-28_last-Prev4_CFSCO',
                                                    'diff_6Z_28-42_last-Prev1_CFSCO','diff_6Z_28-42_last-Prev1_CFSCO',
                                                    'diff_6Z_28-42_last-Prev3_CFSCO','diff_6Z_28-42_last-Prev4_CFSCO',
                                                       ],
                                    'is_prod': True},
        'y_1715-1915_CFSCO6Zp10to13Comb': {'start': dtdt(2021, 1, 1), 'chosen_ratio':0.6,

                                    'preds_for_comb': ['diff_6Z_0-13_last-Prev1_CFSCO',
                                                       ],
                                    'is_prod': True},
        'y_1315-1745_CFSCO6Zp314to21Comb': {'start': dtdt(2021, 1, 1), 'chosen_ratio':0.6,
                                    'preds_for_comb': ['MEAN','diff_6Z_14-18_last-Prev3_CFSCO',
                                                       'diff_6Z_14-21_last-Prev3_CFSCO'
                                                       ],
                                    'is_prod': True},
            'y_1315-1415_cfs6Z28to42': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.65,#[0.4, 0.45,0.2],  # 0.7

                                    'preds_for_comb': ['diff_0Z_28-42_Prev1-Prev3_CFS',
                                                       'diff_6Z_28-42_last-Prev3_CFS','MEAN'],
                                    'is_prod': True},
            'y_1315-1415_cfs6Z14to42': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5,0.25,0.4],  # [0.4, 0.45,0.2],  # 0.7

                                    'preds_for_comb': ['diff_6Z_14-21_last-Prev1D_CFS','diff_6Z_14-28_last-Prev4_CFS','diff_6Z_28-42_last-Prev1_CFS'],
                                    'is_prod': True},
        'y_1315-1415_cfs6Z14to42on12': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.4, 0.2, 0.25],
                                    # [0.4, 0.45,0.2],  # 0.7

                                    'preds_for_comb': ['diff_6Z_14-21_last-Prev1D_CFS', 'diff_6Z_14-28_last-Prev3_CFS',
                                                       'diff_6Z_28-42_last-Prev1_CFS'],
                                    'is_prod': True},
        'y_1400-1445_cfs6Z14to21D': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5, 0.4],
                                        'preds_for_comb': ['diff_6Z_14-21_last-Prev1D_CFS',
                                                           'diff_6Z_14-28_last-Prev1D_CFS',
                                                          ],
                                        'is_prod': True},
            'y_1200-1315_gfsv16': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.7,0.7,0.7],
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1_GFSv16','diff_0Z_0-16_last-Prev2_GFSv16',
                        'diff_6Z_0-16_last-Prev2_GFSv16'],
                                 'is_prod': True},
        'y_1545-1645_GFSv16z': {'start': august20_start, 'chosen_ratio': 0.5,
                               'preds_for_comb': ['MEAN','diff_6Z_0-16_last-Prev3_GFSv16','diff_6Z_0-16_last-Prev2_GFSv16',
                                                  'diff_6Z_0-16_last-Prev1_GFSv16','diff_6Z_0-16_last-Prev4_GFSv16'],
                               'is_prod': True},
        'y_1545-1645_GFSv16z8to16': {'start': august20_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['MEAN','diff_6Z_8-16_last-Prev3_GFSv16',
                                                   'diff_0Z_8-16_last-Prev2_GFSv16','diff_6Z_8-16_last-Prev1_GFSv16'],
                                'is_prod': True},
        'y_1130-1200_GFSv16z': {'start': august20_start, 'chosen_ratio': [0.6,0.4,0.4,0.4],
                                'preds_for_comb': ['diff_6Z_0-16_last-Prev2_GFSv16',
                                                   #'MEAN',
                                                   'diff_0Z_0-16_last-Prev2_GFSv16',
                                                   'diff_0Z_0-16_last-Prev1_GFSv16',
                                                   'diff_6Z_0-16_last-Prev1_GFSv16' #
                                                   ],
                                'is_prod': True},
        'y_1100-1230_GFSv16z0to8': {'start': august20_start, 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_6Z_0-8_last-Prev1_GFSv16'],
                                 # 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                 'is_prod': True},
        'y_1100-1230_GFSv16z0to8Strict': {'start': august20_start, 'chosen_ratio': 0.25,
                                 'preds_for_comb': ['diff_6Z_0-8_last-Prev1_GFSv16'],
                                 # 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                 'is_prod': True},
        'y_1100-1615_GFSv16z0to8p2': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.4,0.35,0.35],
                                 'preds_for_comb': ['diff_6Z_0-8_last-Prev2_GFSv16',
                                                    'diff_6Z_0-8_last-Prev4_GFSv16','diff_6Z_0-8_last-Prev1_GFSv16'],
                                 # 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                 'is_prod': True},
        'y_1100-1615_GFSv16z0to8p34': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.5,0.5,0.4],
                                 'preds_for_comb': ['MEAN','diff_6Z_0-8_last-Prev4_PARA','diff_6Z_0-8_last-Prev4_PARA'],
                                 # 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                 'is_prod': True},
        'y_1100-1615_GFSv16z0to8p2Strict': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.2,0.1,0.1,0.15],
                                 'preds_for_comb': ['diff_6Z_0-8_last-Prev2_GFSv16',
                                                    'diff_6Z_0-8_last-Prev4_GFSv16','diff_6Z_0-8_last-Prev1_GFSv16',
                                                    'MEAN'],
                                 'is_prod': True},
        'y_1100-1615_GFSv16z0to10p2': {'start': dtdt(2020,8,1), 'chosen_ratio': [0.5,0.25],
                                 'preds_for_comb': ['diff_6Z_0-10_last-Prev2_PARA',
                                                    'diff_6Z_0-8_last-Prev2_PARA'],
                                 # 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                 'is_prod': True},
        'y_1000-1100_GFSv160z': {'start': august20_start, 'chosen_ratio': [0.4,0.4,0.5,0.35],
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GFSv16', 'MEAN',
                                                   'diff_0Z_0-16_last-Prev3_GFSv16',
                                                   'diff_0Z_0-16_last-Prev1_GFSv16'],# 'diff_0Z_0-16_last-Prev4_GFSv16'],
                                'is_prod': True},
        'y_1000-1100_GFSv160z2': {'start': august20_start, 'chosen_ratio': [0.6,0.4,0.3,0.3],
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev1_GFSv16',
                                                    'diff_0Z_0-16_last-Prev2_GFSv16', 'MEAN',
                                                    'diff_0Z_0-16_last-Prev3_GFSv16',
                                                    ],
                                 'is_prod': True},
        'y_1845-1915_GFSv160zp4': {'start': august20_start, 'chosen_ratio': 0.5,
                                  'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GFSv16'
                                                     ],
                                  'is_prod': True},
        'y_1415-1715_GFSv16pre12Z': {'start': march_start, 'chosen_ratio': 0.5,
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GFSv16','diff_6Z_0-16_last-Prev3_GFSv16'],
                            'is_prod': True},
        'y_1100-1615_gfsv160z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.65,0.5],
                               'preds_for_comb': ['diff_0Z_0-16_last-Prev4_GFSv16',
                                                  'diff_0Z_0-16_last-Prev4D_GFSv16'],
                               'is_prod': True},
            'y_1315-1745_daily34': {'start': march_start, 'chosen_ratio': [0.5],#[0.75, 0.4, 0.4, 0.5],

                              'preds_for_comb': ['GEFS_daily_hybrid_pred2'],
                              'is_prod': True},

        'y_1300-1745_daily2': {'start': march_start, 'chosen_ratio': [0.75, 0.4, 0.4, 0.5],

                               'preds_for_comb': ['diff_0Z_8-16_last-Prev2D_GEFSL', 'EPSpost_pred2.2',
                                                  'diff_0Z_8-16_last-Prev1D_GEFSL',
                                                  'MEAN'],
                               'is_prod': True},
            'y_0600-0800_24': {'start': march_start, 'chosen_ratio': [0.7,0.4,0.4],  # [0.5, 0.5,0.4],

                              'preds_for_comb': ['diff_0Z_8-16_last-Prev2_GEPS','diff_0Z_8-16_last-Prev1_GEPS','PARACO_AM_pred3'],
                              'is_prod': True},
            'y_0700-0730_EC': {'start': march_start, 'chosen_ratio': [0.3,0.3,0.25,0.25],  # [0.5, 0.5,0.4],

                           'preds_for_comb': ['MEAN','EC_AM_Summer_pred1','diff_0Z_0-8_last-Prev3_EC','diff_0Z_5-8_last-Prev3_EC'],
                           'is_prod': True},
            'y_0730-0800_EPS0to8': {'start': july_19_start, 'chosen_ratio': [0.2,0.6,0.2,0.2],
                           'preds_for_comb': ['diff_0Z_0-8_last-Prev4_EPS','MEAN','diff_0Z_0-8_last-Prev3_EPS','diff_0Z_0-8_last-Prev1_EPS'],
                           'is_prod': True},
            'y_1930-2000_EPS0to8': {'start': july_19_start, 'chosen_ratio': 0.5,#[0.5,0.3],
                                'preds_for_comb': ['diff_12Z_0-8_last-Prev1_EPS',
                                                   'diff_12Z_0-8_last-Prev2_EPS' ],
                                'is_prod': True},
        'y_0800-0900_ECPS': {'start': july_19_start, 'chosen_ratio': 0.25,
                                'preds_for_comb': ['EC_AM_Summer_pred1','diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EPS'],
                                'is_prod': True},
        'y_1745-1845_fritest': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.4,0.4]+[0.25]*3+[0.1],  # [0.5, 0.5,0.4],
                        'preds_for_comb':['diff_0Z_0-2_last-Prev1_GEFSL','MEAN',
                                          'diff_0Z_0-2_last-Prev2_GEFSL',
                            'diff_0Z_0-2_last-Prev1_GFSCO',
                            # 'diff_12Z_0-8_last-Prev1_GEFS', ##
                            'diff_0Z_0-2_last-Prev4_GEFS',
                            #'diff_12Z_0-2_last-Prev2_GEFS', ##
                            'diff_12Z_0-8_last-Prev1_PARACO',
                                          #'diff_0Z_0-2_last-Prev1_GEFS', ##
                                          ],
                           'is_prod': True},
        'y_1745-1845_fritest0': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.25, 0.25,0.15,0.15,0.1],
                                # [0.5, 0.5,0.4],
                                'preds_for_comb': ['diff_0Z_0-2_last-Prev4_GEFS',
                                                   'MEAN',
                                                   'diff_0Z_0-2_last-Prev1_GEFSL',
                                                   'diff_0Z_0-2_last-Prev2_GEFSL',
                                                   'diff_0Z_0-2_last-Prev1_GFSCO',

                                                   ],
                                'is_prod': True},
        'y_1000-1615_Moncash': {'start': dtdt(2019, 11, 1), 'chosen_ratio': [0.4, 0.4] +[0.3]*3,
                                'preds_for_comb': ['diff_0Z_0-2_last-Prev4_GEFS',
                                                   'MEAN','diff_0Z_0-2_last-Prev1_GEFSL',
                                                   'diff_0Z_0-2_last-Prev4_GFSv16',
                                                   'diff_0Z_0-2_last-Prev4_PARA'
                                                   ],
                                'is_prod': True},
        'y_0600-0800_3c': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.75,  # [0.5, 0.5,0.4],

                           'preds_for_comb': ['GEPS_AM_basic_pred1','diff_0Z_0-16_last-Prev3_GEPS',
                                              'diff_0Z_0-8_last-Prev1_GEM'],
                           'is_prod': True},
        'y_0600-0800_0c': {'start': march_start, 'chosen_ratio': [0.65,0.4,0.4],  # [0.5, 0.5,0.4], march_start

                           'preds_for_comb': ['diff_0Z_0-16_Prev1D-Prev3D_GEPS','diff_0Z_0-16_last-Prev3D_GEPS',
                                              'PARACO_AM_0-8_pred1'],
                           'is_prod': True},
        'y_0700-0745_general': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.6]+[0.45]*4,#[0.45,0.35, 0.35,0.35], #,0.25],  # [0.5, 0.5,0.4], march_start
                           'preds_for_comb': ['diff_0Z_0-4_last-Prev1_EC','diff_0Z_0-8_last-Prev2_GEPS','diff_0Z_8-16_last-Prev2_GEPS',
                                              'diff_0Z_8-16_last-Prev4_GEMCO','diff_0Z_0-8_last-Prev2_GEM',
                                              #'EC_AM_Summer_pred1','ECGEM_AM_pred1'],
                                              ],
                           'is_prod': True},
        'y_0715-0730_GEPFS': {'start': july_19_start, 'chosen_ratio': 0.5,
                                'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEFS',
                                                   'diff_0Z_0-16_last-Prev2_GEPS',],
                                'is_prod': True},
        'y_0700-0745_loose': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.65,
                                # [0.5, 0.5,0.4], march_start

                                'preds_for_comb': ['diff_0Z_8-16_last-Prev2_GEPS', 'diff_0Z_0-8_last-Prev2_GEPS',
                                                   'EC_AM_Summer_pred1', 'ECGEM_AM_pred1'],
                                'is_prod': True},

        'y_1815-2030_epsFcst12Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5,0.6,0.4,0.4,0.3,0.3],
                              'preds_for_comb': ['MEAN','diff_0Z_0-10_last-Prev1D_GEFSL',
                                                'diff_6Z_0-8_last-Prev4_PARACO',
                                                 'diff_0Z_0-13_last-Prev1_GEFSLCO','diff_6Z_0-8_last-Prev4_GEFS',
                                                #'diff_6Z_0-8_last-Prev3_GEFS',
                                                 'diff_0Z_0-16_last-Prev2_GEFS'
                                        ],
                              'is_prod': True},
        'y_1815-2030_epsFcst12ZCor': {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,
                                   # [0.65]+[0.5]*6,#[0.3,0.5,0.3]+[0.5]*4,
                                   'preds_for_comb': ['EPS_12Z_pred_EnsTop2_window=25','EPS_12Z_pred_EnsTop4_window=5'],
                                   'is_prod': True},
        'y_1815-2030_epsFcst12ZbCor': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.65,0.65,0.2,0.2],
                                      # [0.65]+[0.5]*6,#[0.3,0.5,0.3]+[0.5]*4,
                                      'preds_for_comb': ['MEAN','EPS_12Zb_pred_EnsTop5_window=15','EPS_12Zb_pred_EnsTop5_window=5','EPS_12Zb_pred_EnsTop5_window=25'],
                                      'is_prod': True},
        'y_1200-1945_epsFcst12ZbCorNEW': {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.6,0.6,0.2,0.2],
                                      # [0.65]+[0.5]*6,#[0.3,0.5,0.3]+[0.5]*4,
                                      'preds_for_comb': ['MEAN','EPS_12Zb_pred_EnsTop5_window=15','EPS_12Zb_pred_EnsTop5_window=5','EPS_12Zb_pred_EnsTop5_window=25'],
                                      'is_prod': True},
        'y_1200-1945_epsFcst12ZbCorW15': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.6,
                                      'preds_for_comb': ['MEAN','EPS_12Zb_pred_EnsTop5_window=15'],
                                      'is_prod': True},
        'y_1200-1415_epsFcst12ZbCor': {'start': august20_start, 'chosen_ratio': [0.3, 0.3, 0.3, 0.4],
                                       'preds_for_comb': ['EPS_12Zb_pred_EnsTop5_window=25','EPS_12Zb_pred_EnsTop5_window=5',
                                                          'MEAN', 'EPS_12Zb_pred_EnsTop5_window=15'],
                                       'is_prod': True},
        'y_1200-1415_epsFcst12ZbCorloose': {'start': august20_start, 'chosen_ratio': [0.25, 0.33, 0.4, 0.4],
                                       'preds_for_comb': ['EPS_12Zb_pred_EnsTop5_window=25',
                                                          'EPS_12Zb_pred_EnsTop5_window=5',
                                                          'MEAN', 'EPS_12Zb_pred_EnsTop5_window=15'],
                                       'is_prod': True},
        'y_1815-2030_epsFcst12ZbCorStrict': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.4, 0.6, 0.2, 0.2],
                                       'preds_for_comb': ['MEAN', 'EPS_12Zb_pred_EnsTop5_window=15',
                                                          'EPS_12Zb_pred_EnsTop5_window=25',
                                                          'EPS_12Zb_pred_EnsTop5_window=5'],
                                       'is_prod': True},
        'y_1200-2030_epsFcstGEFS6Z': {'start': august20_start, 'chosen_ratio': [0.3,0.2],
                                       'preds_for_comb': ['EPS_12Z_Pgefs6Z_window=5',
                                                          'EPS_12Z_Pgefs6Z_window=10'],
                                       'is_prod': True},

        'y_1900-1915_epsFcstGEFS12Z': {'start': august20_start, 'chosen_ratio': 0.4,
                                      'preds_for_comb': ['EPS_12Z_Pgefs12Z_window=10'
                                                        ],
                                      'is_prod': True},
        'y_1245-1615_epsFcstGEFS12Zb': {'start': august20_start, 'chosen_ratio': 0.7,
                                       'preds_for_comb': ['EPS_12Z_Pgefs12Z_window=10'
                                                          ],
                                       'is_prod': True},
        'y_1100-1400_epsFcstGFSv16': {'start': august20_start, 'chosen_ratio': 0.5,
                                       'preds_for_comb': ['EPS_12Z_Pgfsv1612Z2_window=20'
                                                          ],
                                       'is_prod': True},
        'y_1200-1400_epsFcstGEPS0z13': {'start': august20_start, 'chosen_ratio': 0.5,
                                       'preds_for_comb': ['EPS_12Z_Pgeps0Z13_window=20'
                                                          ],
                                       'is_prod': True},
        'y_1815-1945_epsFcstGEPS0Z': {'start': august20_start, 'chosen_ratio': [0.4],
                                      'preds_for_comb': ['EPS_12Z_Pgeps0Z13_window=10'],
                                      'is_prod': True},
        'y_1815-1945_epsFcstGEPFS0Zgap': {'start': august20_start, 'chosen_ratio': 0.5,
                                      'preds_for_comb': ['Value_0Z_0-16_GEPS-EPS_rs_diff1',
                                                         ],
                                      'is_prod': True},
        'y_1815-1945_epsFcstGEPFS012Zgap': {'start': august20_start, 'chosen_ratio': [0.5,0.3],
                                          'preds_for_comb': ['Value_0Z_0-16_GEPS-EPS_rs_diff1',
                                                             'Value_12Z_0-16_GEFS-EPS(-1)_rs_diff1'
                                                             ],
                                          'is_prod': True},
        'y_0600-0800_epsFcstGEPFS0Zgap': {'start': dtdt(2020,10,1), 'chosen_ratio': 0.5,#[0.5, 0.3,0.3],
                                          'preds_for_comb': ['Value_0Z_0-16_GEPS-EPS(-1)_rs',
                                                             'Value_0Z_0-16_GEFS-EPS(-1)_rs_diff1',
                                                             'Value_0Z_0-16_GEPS-EPS(-1)_rs_diff1',
                                                             'Value_0Z_0-16_GEFS-EPS(-1)_rs',
                                                             ],
                                          'is_prod': True},
        'y_1200-1945_epsFcstGEPS12Zgap': {'start': dtdt(2020, 10, 1), 'chosen_ratio': 0.5,
                                          'preds_for_comb': ['Value_12Z_0-16_GEPS-EPS_rs'
                                                             ],
                                          'is_prod': True},
        'y_1745-1945_GEPS12zVsEPS0zgap': {'start': dtdt(2020, 10, 1), 'chosen_ratio': 0.5,
                                          'preds_for_comb': ['Value_12Z_0-16_GEPS-EPS(-1)_rs',
                                                             ],
                                          'is_prod': True},
        'y_1745-1945_GEFS12zVsEPS0zgap': {'start': dtdt(2020, 10, 1), 'chosen_ratio': 0.4,
                                          'preds_for_comb': ['Value_12Z_0-16_GEFS-EPS(-1)_rs',
                                                             ],
                                          'is_prod': True},
        'y_1815-1945_epsFcstAll06Z': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.7,
                                      'preds_for_comb': ['EPS_12Z_Pgefs12Z_window=10', 'EPS_12Z_Pgefs18Z_window=10', 'EPS_12Z_Pgefs6Z_window=10',
                                                         'EPS_12Z_Pgefs6Z2_window=10', 'EPS_12Z_Pgefs6Z3_window=10', 'EPS_12Z_Pgefs6Z4_window=10',
                                                         'EPS_12Z_Pgefs12Z2_window=10', 'EPS_12Z_Pgeps0Z_window=10', 'EPS_12Z_Pgeps12Z_window=10',
                                                         'EPS_12Z_Pgeps12Z3_window=10', 'EPS_12Z_Pgeps0Z2_window=5', 'EPS_12Z_Pgeps0Z3_window=5',
                                                         'EPS_12Z_Pgeps0Z13_window=10', 'EPS_12Z_Ppara12Z_window=10', 'EPS_12Z_Ppara12Z2_window=10',
                                                         'EPS_12Z_Pparaco12Z_window=10', 'EPS_12Z_Pparaco12Z2_window=10',
                                                         'EPS_12Z_Pgfsv166Z_window=10','EPS_12Z_Pgfsv166Z2_window=10',
                                                         'EPS_12Z_Pgfsv160Z_window=10', 'EPS_12Z_Pgfsv160Z2_window=10',
                                                         'EPS_12Z_Pgfsv1612Z_window=10', 'EPS_12Z_Pgfsv1612Z2_window=10', 'EPS_12Z_PgefsMEAN_window=10'][:10],
                                      'is_prod': True},
        'y_1100-1715_epsFcstPgeps0Z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.35, 0.65],

                                   'preds_for_comb': ['EPS_12Z_Pgeps0Z13_window=10',
                                                      'EPS_12Z_Pgeps0Z3_window=10'],
                                   'is_prod': True},
        'y_0600-1415_epsFcstPgeps0Z': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.45, 0.35],

                                   'preds_for_comb': ['EPS_12Z_Pgeps0Z3_window=10',
                                                      'EPS_12Z_Pgeps0Z13_window=10'],
                                   'is_prod': True},
            'y_0600-1415_epsFcstPgeps0Zc2': {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.45, 0.35],

                                   'preds_for_comb': ['EPS_12Z_Pgeps0Z3_window=10',
                                                      'EPS_12Z_Pgeps0Z13_window=10'],
                                   'is_prod': True},
        'y_1100-1745_epsFcstPcomb06Z': {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.35,0.35, 0.65],
                                       'preds_for_comb': ['EPS_12Z_Pgeps0Z13_window=10',
                                                          'EPS_12Z_Pgfsv166Z_window=10',
                                                          'EPS_12Z_Pgeps0Z3_window=10'],
                                       'is_prod': True},
        'y_1100-1200_gefsFcst6Z': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.45,0.45,0.4,0.3,0.2],  # [0.3,0.2,0.2,0.2],
                                   'preds_for_comb': ['MEAN',
                                                      'GEFS_6Zb_pred_EnsTop9_window=50',
                                                      'GEFS_6Zb_pred_EnsTop6_window=20',
                                                      'GEFS_6Zb_pred_EnsTop5_window=100',
                                                      'GEFS_6Zb_pred_EnsTop4_window=10'
                                                      ],
                                   'is_prod': True},
        'y_0800-1845_Daily0ZFcst': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.4,
                                       'preds_for_comb': ['daily_0Zb_pred_EnsTop3_window=15',
                                                          'daily_0Zb_pred_EnsTop6_window=100',
                                                          'daily_0Zb_pred_EnsTop6_window=20',
                                                          'daily_0Zb_pred_EnsTop8_window=10','MEAN'
                                                          ],
                                       'is_prod': True},
        'y_0800-1845_Daily0ZFcstw100': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.5,
                                       'preds_for_comb': ['daily_0Zb_pred_EnsTop6_window=100',
                                                          ],
                                       'is_prod': True},
        'y_0800-1845_Daily0ZFcstOld': {'start': dtdt(2020, 4, 1), 'chosen_ratio': 0.6,
                                    'preds_for_comb': ['daily_0Zb_pred_EnsTop8_window=10',
                                                       'daily_0Zb_pred_EnsTop9_window=15',
                                                       'daily_0Zb_pred_EnsTop3_window=20',
                                                       'daily_0Zb_pred_EnsTop5_window=100'],
                                    'is_prod': True},
        'y_0600-0800_epsFcst': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.4,0.35,0.35] + [0.5]*4+[0.25,0.25],
                            'preds_for_comb': ['MEAN',
                                               'EPS_0Zb_pred_EnsTop3_window=10','EPS_0Zb_pred_EnsTop6_window=5',
                                               'diff_0Z_0-4_last-Prev1_EC',
                                               'diff_12Z_0-10_Prev1-Prev3_GEM-','diff_0Z_0-10_last-Prev1D_GEM',
                                               'diff_18Z_14-16_last-Prev2_GEFS-','diff_12Z_14-28_last-Prev4_CFS','diff_18Z_0-8_last-Prev1_GEFS'],
                                'is_prod': True},
        'y_0600-0800_epsFcstMEAN': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.45]+[0.05]*8,
                                'preds_for_comb': ['MEAN',
                                                   'EPS_0Zb_pred_EnsTop3_window=10', 'EPS_0Zb_pred_EnsTop6_window=5',
                                                   'diff_0Z_0-4_last-Prev1_EC',
                                                   'diff_12Z_0-10_Prev1-Prev3_GEM-', 'diff_0Z_0-10_last-Prev1D_GEM',
                                                   'diff_18Z_14-16_last-Prev2_GEFS-', 'diff_12Z_14-28_last-Prev4_CFS',
                                                   'diff_18Z_0-8_last-Prev1_GEFS'],
                                'is_prod': True},
        'y_0600-0800_epsFcstStrict': {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.25, 0.15, 0.15] + [0.01] * 4 + [0.01, 0.01],
                                'preds_for_comb': ['MEAN',
                                                   'EPS_0Zb_pred_EnsTop3_window=10', 'EPS_0Zb_pred_EnsTop6_window=5',
                                                   'diff_0Z_0-4_last-Prev1_EC',
                                                   'diff_12Z_0-10_Prev1-Prev3_GEM-', 'diff_0Z_0-10_last-Prev1D_GEM',
                                                   'diff_18Z_14-16_last-Prev2_GEFS-','diff_12Z_14-28_last-Prev4_CFS',
                                                   'diff_18Z_0-8_last-Prev1_GEFS'],
                                'is_prod': True},
            'y_1245-1415_loose': {'start': march_start, 'chosen_ratio': 0.65,  # [0.5, 0.5,0.4],

                           'preds_for_comb': ['PARA_daily_hybrid_pred2'],
                           'is_prod': True},
            'y_1145-1215_234': {'start': march_start, 'chosen_ratio': 0.5,  # [0.5, 0.5,0.4],

                              'preds_for_comb': ['GEFSL_daily_hybrid_8-16_pred2','PARA_daily_hybrid_6Z_pred2'],
                              'is_prod': True},
            'y_1145-1215_2c': {'start': march_start, 'chosen_ratio': 0.5,  # [0.5, 0.5,0.4],

                            'preds_for_comb': ['GEMCO_PM_pred2','GEFSL_daily_hybrid_8-16_pred2', 'PARA_daily_hybrid_6Z_pred2'],
                            'is_prod': True},
        'y_1245-1415_strict': {'start': march_start, 'chosen_ratio': 0.4,  # [0.5, 0.5,0.4],

                               'preds_for_comb': ['PARA_daily_hybrid_pred2'],
                               'is_prod': True},
            'y_1245-1445_strict2': {'start': march_start, 'chosen_ratio': [0.6,0.3,0.6],  # [0.5, 0.5,0.4],

                              'preds_for_comb': ['GEFS_daily_hybrid_pred2','PARA_daily_hybrid_pred2',
                                                 'MEAN'],
                              'is_prod': True},
            'y_1245-1515_d': {'start': march_start, 'chosen_ratio': [0.6,0.4,0.4,0.4],  # [0.5, 0.5,0.4],

                               'preds_for_comb': ['PARA_daily_hybrid_pred2','GEFS_daily_hybrid_pred2',
                                                  'EPSL_daily_hybrid_pred4','GEFSL_daily_hybrid_pred2'],
                               'is_prod': True},
        'y_1315-1745_cfs28to42':
                        {'start': dtdt(2020,8,1), 'chosen_ratio': [0.5,0.35,0.35],  # [0.5, 0.5,0.4],

                         'preds_for_comb': ['diff_0Z_28-42_last-Prev1D_CFS','diff_0Z_21-42_last-Prev1D_CFS',
                                        'diff_0Z_28-42_last-Prev3D_CFS'],
                            'is_prod': True},
        'y_0800-1845_CFS3D':
                        {'start': dtdt(2020,8,1), 'chosen_ratio': [0.44],
                         'preds_for_comb': ['diff_18Z_21-42_last-Prev3D_CFS'
                                         ],
                            'is_prod': True},
        'y_0800-1845_CFS3DStrict':
                        {'start': dtdt(2020,8,1), 'chosen_ratio': [0.24],
                         'preds_for_comb': ['diff_18Z_21-42_last-Prev3D_CFS'
                                         ],
                            'is_prod': True},
        'y_1000-1300_cfs28to422D':
                        {'start': dtdt(2020,3,1), 'chosen_ratio': [0.5,0.25],
                         'preds_for_comb': ['diff_0Z_28-42_last-Prev2D_CFS',
                                            'diff_0Z_14-28_last-Prev1D_CFS'],
                            'is_prod': True},
        'y_1100-1315_cfsMon':
            {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.55, 0.4],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_0Z_28-42_last-Prev3D_CFS','diff_12Z_14-28_last-Prev1D_CFS',
                                ],
             'is_prod': True},
        'y_1245-1415_cfs28to42':
                    {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.45],  # [0.5, 0.5,0.4],

                    'preds_for_comb': ['diff_6Z_28-42_last-Prev1D_CFS'],
                        'is_prod': True},
        'y_1330-1400_cfs6Z':
            {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_6Z_0-13_last-Prev3_CFS'],
             'is_prod': True},

        'y_0800-1000_cfs28to42':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5, 0.4,0.4],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_0Z_28-42_last-Prev2_CFS',
                                'diff_0Z_28-42_last-Prev4_CFS','diff_0Z_14-28_last-Prev2_CFS'],
             'is_prod': True},
        'y_1400-1500_cfs0Z':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.4, 0.55, 0.4,0.2],

             'preds_for_comb': ['MEAN', 'CFS_0Z_week2', 'diff_0Z_14-28_last-Prev2_CFS',
                                'diff_0Z_14-28_last-Prev1234_CFS'
                                ],
             'is_prod': True},
        'y_1200-1500_cfs0Z28to42':
            {'start': dtdt(2020, 3, 1), 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_0Z_28-42_last-Prev4_CFS'
                                ],
             'is_prod': True},
        'y_0800-1745_cfsDaily0Z':
            {'start': august20_start, 'chosen_ratio': [0.3,0.5,0.35],#[0.3,0.5,0.35],
             'preds_for_comb': ['diff_0Z_14-28_last-Prev4D_CFS','diff_0Z_14-35_last-Prev4D_CFS',
                                'diff_0Z_28-42_last-Prev1D_CFS'],
             'is_prod': True},
        'y_1100-1815_cfsDaily0Z0to16Winter':
            {'start': march_start, 'chosen_ratio': [0.35],
             'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_CFS'],
             'is_prod': True},
        'y_1100-1815_cfsDaily0Z0to16WinterStrict':
            {'start': march_start, 'chosen_ratio': [0.2],
             'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_CFS'],
             'is_prod': True},
        'y_1100-1815_cfsDaily0Z0to16Summer':
            {'start': march_start, 'chosen_ratio': [0.55],
             'preds_for_comb': ['diff_0Z_0-16_last-Prev4D_CFS'],
             'is_prod': True},
        'y_1615-1745_cfsDaily0Z0to162D':
            {'start': march_start, 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_12Z_0-16_last-Prev2D_CFS',
                                'diff_0Z_0-16_last-Prev3D_CFS'],
             'is_prod': True},
        'y_0800-1845_cfsDaily0Z0to162Dclean':
            {'start': march_start, 'chosen_ratio': 0.37,
             'preds_for_comb': ['diff_0Z_0-16_last-Prev2D_CFS'],
             'is_prod': True},
        'y_0800-1845_cfsDaily12Z14to284Dclean':
            {'start': march_start, 'chosen_ratio': 0.45,
             'preds_for_comb': ['diff_12Z_14-28_last-Prev4D_CFS'],
             'is_prod': True},
        'y_1615-1745_cfsDaily0Z0to162DMean':
            {'start': march_start, 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_12Z_0-16_last-Prev2D_CFS',
                                'diff_0Z_0-16_last-Prev3D_CFS'],
             'is_prod': True},
        'y_0800-1745_cfs12Z0to16p3':
            {'start': dtdt(2020,5,15), 'chosen_ratio': [0.3,0.3]+[0.5]*2+[0.3,0.3,0.3],
             'preds_for_comb': ['diff_12Z_0-16_last-Prev2_CFS','MEAN',
                                'diff_12Z_0-16_last-Prev3_CFS',
                                # 'diff_12Z_0-16_last-Prev1_CFS',
                                # 'diff_12Z_0-16_last-Prev4_CFS',
                                'diff_0Z_0-16_last-Prev3_CFS',
                                #'diff_0Z_0-16_last-Prev2_CFS',
                                # 'diff_0Z_0-16_last-Prev1_CFS',
                                # 'diff_0Z_0-16_last-Prev4_CFS',
                                'diff_18Z_0-16_last-Prev3_CFS',
                                'diff_18Z_0-16_last-Prev2_CFS',
                                'diff_18Z_0-16_last-Prev1_CFS',
                                #'diff_18Z_0-16_last-Prev4_CFS',
                                # 'diff_6Z_0-16_last-Prev3_CFS', 'diff_6Z_0-16_last-Prev2_CFS',
                                # 'diff_6Z_0-16_last-Prev1_CFS',
                                # 'diff_6Z_0-16_last-Prev4_CFS',


                                ],
             'is_prod': True},
        'y_0400-0800_cfs12Zp2comb':
            {'start': dtdt(2020,5,15), 'chosen_ratio': 0.3,
             'preds_for_comb': ['MEAN','diff_12Z_8-16_last-Prev2_CFS','diff_12Z_0-21_last-Prev2_CFS',

                                ],
             'is_prod': True},
        'y_0600-0800_cfs12Zp4comb':
            {'start': dtdt(2020,5,15), 'chosen_ratio': [0.44]+[0.15]*6,
             'preds_for_comb': ['MEAN',
                                'diff_12Z_14-18_last-Prev4_CFS', 'diff_12Z_0-16_last-Prev4_CFSCO',
                                'diff_12Z_0-21_last-Prev4_CFSCO', 'diff_12Z_14-21_last-Prev4_CFS',
                                'diff_12Z_0-21_last-Prev4_CFS', 'diff_12Z_14-28_last-Prev4_CFS'],

             'is_prod': True},
        'y_1200-1415_CFSvsEPS0Z':
            {'start': march_start, 'chosen_ratio': 0.4,
             'preds_for_comb': ['Value_0Z_0-16_CFS-EPS_rs'],
             'is_prod': True},
        'y_0000-0800_CFSvsEPS12Z':
            {'start': march_start, 'chosen_ratio': 0.4,
             'preds_for_comb': ['Value_12Z_0-16_CFS-EPS_rs'],
             'is_prod': True},
        'y_0800-1745_cfsDaily06Z':
            {'start': august20_start, 'chosen_ratio': 0.3,
             'preds_for_comb': ['diff_0Z_14-28_last-Prev4D_CFS','diff_6Z_14-35_last-Prev1D_CFS',
                                'diff_11Zb_14-35_last-Prev1D_CFS'],
             'is_prod': True},
        'y_0800-1745_cfsDaily6Z':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.3, 0.5, 0.35],
             'preds_for_comb': ['diff_6Z_14-28_last-Prev4D_CFS', 'diff_6Z_14-35_last-Prev4D_CFS',
                                'diff_6Z_28-42_last-Prev1D_CFS'],
             'is_prod': True},
        'y_0800-1745_cfsDaily12Z':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.3, 0.5, 0.35],
             'preds_for_comb': ['diff_12Z_14-28_last-Prev4D_CFS', 'diff_12Z_14-35_last-Prev4D_CFS',
                                'diff_12Z_28-42_last-Prev1D_CFS'],
             'is_prod': True},
        'y_0800-1745_cfs14to211D':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,
             'preds_for_comb': ['diff_0Z_14-21_last-Prev1D_CFS'],
             'is_prod': True},
        'y_1545-1715_CFS14to214D':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_18Z_14-21_last-Prev4D_CFS'],
             'is_prod': True},
        'y_0600-0800_CFSPrev1D21to35':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_0Z_21-35_Prev1D-Prev3D_CFS'],
             'is_prod': True},
        'y_0800-1845_CFSPrev1D10to21':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.45,0.33,0.33],
             'preds_for_comb': ['MEAN','diff_0Z_10-21_Prev1D-Prev3D_CFS','diff_0Z_10-21_last-Prev2D_CFS-1d'],
             'is_prod': True},
        'y_0630-0730_cfs12z10to211D':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.6,
             'preds_for_comb': ['diff_12Z_10-21_last-Prev1D_CFS'],
             'is_prod': True},
        'y_0800-1845_cfs0z10to21CombD':
            {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.5,0.5,0.4],
             'preds_for_comb': ['diff_0Z_10-21_last-Prev1D_CFS-1d',
                                'diff_0Z_10-21_last-Prev2D_CFS-1d','diff_0Z_10-21_Prev1D-Prev2D_CFS-1d'],
             'is_prod': True},
        'y_1100-1745_cfs12z10to212D':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_12Z_10-21_last-Prev2D_CFS'],
             'is_prod': True},
        'y_0600-0800_cfs12z14to212D':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.44,
             'preds_for_comb': ['diff_12Z_14-21_last-Prev2D_CFS'],
             'is_prod': True},
        'y_0600-0800_cfs12zComb':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.44]+[0.1]*3,
             'preds_for_comb': ['MEAN','diff_12Z_14-21_last-Prev2D_CFS',
                             'diff_0Z_14-35_Prev1-Prev2_GEFSCO35', 'diff_0Z_14-35_Prev1D-Prev2D_GEFSCO35',],
             'is_prod': True},
        'y_0800-1400_cfs12zComb28to35':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.44]+[0.15]*7,
             'preds_for_comb': ['MEAN',
                                'diff_12Z_28-35_last-Prev1_CFS','diff_12Z_28-35_last-Prev1_CFSCO',
                                 'diff_12Z_28-35_last-Prev3_CFSCO','diff_12Z_28-42_last-Prev1_CFS',
                                 'diff_12Z_28-42_last-Prev1_CFSCO',
                                 'diff_18Z_28-35_Prev1-Prev2_CFS','diff_18Z_28-42_Prev1-Prev2_CFS',
                                ],
             'is_prod': True},
        'y_1100-1215_cfs0z10to214D':
            {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.6,
             'preds_for_comb': ['diff_0Z_10-21_last-Prev4D_CFS'],
             'is_prod': True},
        'y_1100-1200_cfsco0z10to214D':
            {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.45,
             'preds_for_comb': ['diff_0Z_10-21_last-Prev4D_CFSCO'],
             'is_prod': True},
        'y_0800-1945_cfs6z10to21D':
            {'start': dtdt(2021, 3, 1), 'chosen_ratio': 0.5,
             'preds_for_comb': ['diff_6Z_10-21_last-Prev1D_CFS'],
             'is_prod': True},
        'y_1430-1615_CFS12z21to32comb':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.46,
             'preds_for_comb': ['diff_12Z_28-42_Prev1D-Prev3D_CFS', 'diff_0Z_21-28_last-Prev1_CFS','MEAN'],
             'is_prod': True},
        'y_1845-1945_cfs12z12D':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.65,0.2,0.2],
             'preds_for_comb': ['MEAN','diff_12Z_28-42_last-Prev1D_CFS','diff_12Z_28-42_last-Prev12D_CFS'],
             'is_prod': True},
        'y_1200-1715_GEFS35':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.6,0.4,0.4,0.4,0.4],
             'preds_for_comb': ['MEAN','diff_0Z_0-21_last-Prev2D_GEFS35','diff_0Z_0-21_last-Prev1D_GEFS35',
                                'diff_0Z_14-28_last-Prev1D_GEFS35','diff_0Z_14-28_last-Prev3D_GEFS35',
                                ],
             'is_prod': True},
        'y_0800-1400_GEFS353D':
            {'start': dtdt(2021, 10, 1), 'chosen_ratio': 0.42,
             'preds_for_comb': ['MEAN','diff_0Z_14-21_Prev1D-Prev3D_GEFS35',
                                'diff_0Z_14-21_last-Prev3D_GEFS35'],
             'is_prod': True},
        'y_0800-1315_GEFSCO354D':
            {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.6,
             'preds_for_comb': ['MEAN','diff_0Z_14-35_last-Prev4_GEFSCO35',
                                'diff_0Z_14-28_last-Prev4_GEFSCO35'],
             'is_prod': True},
        'y_1100-1315_GEFSCO352D':
            {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.6,
             'preds_for_comb': ['MEAN','diff_0Z_14-35_last-Prev2_GEFSCO35',
                                'diff_0Z_14-28_last-Prev2_GEFSCO35'],
             'is_prod': True},
        'y_0800-1500_GEFSCO35Prev1mix0to13':
            {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.45,
             'preds_for_comb': ['MEAN',
                                'diff_0Z_0-13_Prev1-Prev3_GEFSCO35',
                                'diff_0Z_0-2_Prev1-Prev3_GEFSCO35'],
             'is_prod': True},
        'y_0800-1500_GEFS350to13p2':
            {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.55,
             'preds_for_comb': ['diff_0Z_0-13_last-Prev2_GEFS35'],
             'is_prod': True},
        'y_1230-1400_GEFSCO35Prev10to2':
            {'start': dtdt(2021, 5, 1), 'chosen_ratio': 0.35,
             'preds_for_comb': ['diff_0Z_0-2_Prev1-Prev3_GEFSCO35'],
             'is_prod': True},
        'y_1200-1715_GEFS353D':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.4,0.2,0.2,0.1],
             'preds_for_comb': ['MEAN','diff_0Z_14-28_last-Prev3D_GEFS35','diff_0Z_14-35_last-Prev3D_GEFS35',
                                'diff_0Z_14-35_last-Prev3D_CFS'],
             'is_prod': True},
        'y_0400-0600_GEFS354D':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.5,0.2,0.2],
             'preds_for_comb': ['MEAN','diff_0Z_14-21_last-Prev4_GEFS35',
                                'diff_0Z_14-28_last-Prev4D_GEFS35'],
             'is_prod': True},
        'y_0800-1845_GEFS351DComb':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': 0.45,
             'preds_for_comb': ['MEAN','diff_0Z_14-28_last-Prev1D_GEFS35','diff_0Z_21-28_last-Prev2D_GEFS35',
                                 'diff_0Z_14-35_last-Prev1_GEFS35','diff_0Z_14-35_last-Prev1D_GEFS35'],
             'is_prod': True},
        'y_0800-1845_GEFS354DComb':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.5,0.25,0.01],
             'preds_for_comb': ['MEAN','diff_0Z_21-28_last-Prev4D_GEFS35', 'diff_0Z_21-28_last-Prev4_GEFSCO35'],
             'is_prod': True},
        'y_0800-1745_GEFS354D14to28':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': 0.45,
             'preds_for_comb': ['diff_0Z_14-28_last-Prev4_GEFS35'],
             'is_prod': True},
        'y_0000-0800_GEFS352D':
            {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.5,0.5,0.2],
             'preds_for_comb': ['MEAN','diff_0Z_14-28_last-Prev2D_GEFS35',
                                'diff_0Z_14-35_last-Prev2_GEFS35'],
             'is_prod': True},
        'y_0800-1945_GEFS35yesterday':
                    {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.45,0.2,0.2],
                        'preds_for_comb': ['MEAN','diff_0Z_0-21_last-Prev1D_GEFS35-1d',
                                            'diff_0Z_0-21_last-Prev2D_GEFS35-1d'
                                           ],
                            'is_prod': True},
        'y_0800-1945_GEFS35yesterdayStrict':
                    {'start': dtdt(2020, 10, 1), 'chosen_ratio': [0.25,0.2,0.2],
                        'preds_for_comb': ['MEAN','diff_0Z_0-21_last-Prev1D_GEFS35-1d',
                                            'diff_0Z_0-21_last-Prev2D_GEFS35-1d'
                                           ],
                            'is_prod': True},
        'y_1100-1430_WedComb':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.65,
             'preds_for_comb': ['CFS_AM_pred1'],
             'is_prod': True},
        'y_0800-1100_WedComb2MEAN':
            {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.55,
             'preds_for_comb': ['MEAN','diff_6Z_28-42_Prev1-Prev2_CFS','diff_6Z_21-28_Prev1-Prev2_CFS','diff_18Z_0-16_Prev1D-Prev3D_CFS',
                                         'diff_18Z_0-15_last-Prev4_PARA','diff_12Z_0-2_Prev1-Prev2_PARACO'],
             'is_prod': True},
        'y_1100-1430_WedCombStrict':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.35,
             'preds_for_comb': ['CFS_AM_pred1'],
             'is_prod': True},
        'y_1400-1500_cfs0ZMean':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5, 0.55, 0.5],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['MEAN', 'CFS_0Z_week2', 'diff_0Z_14-28_last-Prev2_CFS'
                                ],
             'is_prod': True},
        'y_1815-2030_cfs0Z28to42':
            {'start': dtdt(2020, 4, 1), 'chosen_ratio': [0.5],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_0Z_28-42_last-Prev1234_CFS'
                                ],
             'is_prod': True},
        'y_1945-2045_cfs12Z28to42':
            {'start': dtdt(2020, 3, 1), 'chosen_ratio': [0.5],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_12Z_14-28_last-Prev4_CFS'
                                ],
             'is_prod': True},
        'y_0730-0800_cfs12Z28to42':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.5],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_12Z_28-42_last-Prev1234_CFS'
                                ],
             'is_prod': True},
        'y_0600-0930_cfs12Z':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.4,  # [0.5, 0.5,0.4],
                 'preds_for_comb': ['diff_12Z_14-28_last-Prev1234_CFS',
                                'diff_12Z_14-28_last-Prev1D_CFS','diff_12Z_28-42_last-Prev1234_CFS',
                                ],
             'is_prod': True},
        'y_0000-0600_cfs12Z28to42':
            {'start': dtdt(2020, 5, 15), 'chosen_ratio': [0.55, 0.4,0.4],
                 'preds_for_comb': ['MEAN','diff_12Z_28-35_last-Prev2_CFS','diff_12Z_28-42_last-Prev2_CFS',
                                        ],
             'is_prod': True},
        'y_0400-0700_cfs18Z14to28':
            {'start': dtdt(2020,8,1), 'chosen_ratio': 0.55,  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_18Z_14-28_last-Prev1234_CFS'],
             'is_prod': True},
        'y_0700-0800_cfs18Z28to42':
            {'start': march_start, 'chosen_ratio': 0.55,  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_18Z_28-42_last-Prev1234_CFS'],
             'is_prod': True},
        'y_0600-0700_cfs1218ZMix':
                {'start': dtdt(2020,5,15), 'chosen_ratio': [0.55]+[0.02]*12, #[0.6,0.5],
                 'preds_for_comb': ['MEAN','diff_12Z_14-21_last-Prev3D_CFS', 'diff_0Z_0-8_last-Prev2_GEFS', 'diff_18Z_14-35_last-Prev4D_CFS', 'diff_12Z_14-35_last-Prev4D_CFS', 'diff_18Z_8-16_last-Prev4D_PARA', 'diff_18Z_14-28_last-Prev1D_CFS',
                 'diff_18Z_14-21_last-Prev4D_CFS', 'diff_18Z_14-28_last-Prev4D_CFS', 'diff_18Z_28-35_last-Prev2_CFS', 'diff_12Z_14-28_last-Prev4D_CFS',
                 'diff_18Z_28-42_last-Prev2_CFS', 'diff_12Z_14-21_last-Prev4D_CFS'],
                    'is_prod': True},
        'y_0800-1000_eps9to13':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.6,0.5,0.3,0.3],  # [0.5, 0.5,0.4],

             'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS','MEAN','diff_0Z_9-13_last-Prev2_EPS',
                                  'diff_0Z_9-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_0800-1000_eps9to13p2':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,

             'preds_for_comb': ['diff_0Z_9-13_last-Prev2_EPS',
                                ],
             'is_prod': True},
        'y_0800-1000_eps0to13p2':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.45,

             'preds_for_comb': ['diff_0Z_0-13_last-Prev2_EPS',
                                ],
             'is_prod': True},
        'y_0800-1000_eps9to13MEAN':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.44,0.2,0.2,0.2],

             'preds_for_comb': ['MEAN','diff_0Z_9-13_last-Prev1_EPS','diff_0Z_9-13_last-Prev2_EPS',
                                  'diff_0Z_9-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_0800-1000_eps9to13MEANStrict':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.3,0.1,0.1,0.1],

             'preds_for_comb': ['MEAN','diff_0Z_9-13_last-Prev1_EPS','diff_0Z_9-13_last-Prev2_EPS',
                                  'diff_0Z_9-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_0800-1000_eps0to13MEAN':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': [0.44,0.2,0.2,0.2],

             'preds_for_comb': ['MEAN','diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                  'diff_0Z_0-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_1300-1330_eps9to13p4':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.48,

             'preds_for_comb': ['diff_0Z_9-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_1300-1330_eps9to13p4Strict':
            {'start': dtdt(2020, 8, 1), 'chosen_ratio': 0.3,

             'preds_for_comb': ['diff_0Z_9-13_last-Prev4_EPS'
                                ],
             'is_prod': True},
        'y_1100-1315_EPSVsGEPS':
            {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.5,0.4,0.4],
            'preds_for_comb': ['Value_12Z_0-16_GEPS-EPS(-1)_rs',
                               'Value_0Z_0-16_GEPS-EPS_rs','Value_0Z_0-16_GEPS-EPS(-1)_rs',
                                ],
             'is_prod': True},
        'y_1100-1315_EPSVsGEPStrict':
            {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.5],
            'preds_for_comb': ['Value_12Z_0-16_GEPS-EPS(-1)_rs',
                                ],
             'is_prod': True},
        'y_1315-1515_EPS9to13rs':
            {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.5],
            'preds_for_comb': ['Value_0Z_9-13_EPS_rs'],
             'is_prod': True},
        'y_1315-1515_EPS9to13rsStrict':
            {'start': dtdt(2020, 7, 1), 'chosen_ratio': [0.25],
            'preds_for_comb': ['Value_0Z_9-13_EPS_rs'],
             'is_prod': True},
        'y_0745-0815_eps9to13':
            {'start': march_start, 'chosen_ratio': 0.5,#[0.5, 0.4, 0.3], todo

             'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS','diff_0Z_9-13_last-Prev2_EPS',
                                'diff_0Z_9-13_last-Prev3_EPS'
                                ],
             'is_prod': True},
        'y_1315-1745_eps9to13basic':
            {'start': march_start, 'chosen_ratio': 0.6,

             'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS'
                                ],
             'is_prod': True},
        'y_1315-1745_eps9to13basicStrict':
            {'start': march_start, 'chosen_ratio': 0.3,

             'preds_for_comb': ['diff_0Z_9-13_last-Prev1_EPS'
                                ],
             'is_prod': True},
        'y_0800-1415_eps12Z':
            {'start': march_start, 'chosen_ratio': 0.4,#[0.5,0.3,0.3,0.3,0.3],
             'preds_for_comb': ['diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev1_EPS',
                                'diff_12Z_0-16_last-Prev2_GEPS'],
             'is_prod': True},
        'y_1215-1315_eps12Zp40to8':
            {'start': march_start, 'chosen_ratio': 0.6,
             'preds_for_comb': ['diff_12Z_0-8_last-Prev4_EPS'],
             'is_prod': True},
        'y_0600-0800_eps12Z':
            {'start': july_19_start, 'chosen_ratio': [0.4],
             'preds_for_comb': ['diff_12Z_0-13_last-Prev1_EPS',
                                #'diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-16_last-Prev2_GEPS'
                                ],
             'is_prod': True},
        'y_0800-1400_EPS12ZComb':
            {'start': dtdt(2021,1,1), 'chosen_ratio': [0.5]*1+[0.4]*6,
             'preds_for_comb': ['MEAN',
                                'diff_12Z_0-13_last-Prev1_EPS',
                                 'diff_12Z_9-13_last-Prev1_EPS','diff_12Z_9-13_last-Prev2_EPS',
                                'diff_12Z_0-13_last-Prev2_EPS',
                                 'diff_12Z_12-13_last-Prev1_EPS','diff_12Z_12-13_last-Prev2_EPS',
                                 ],
             'is_prod': True},
        'y_1000-1200_eps12Z9to13':
            {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5],
             'preds_for_comb': ['diff_12Z_9-13_last-Prev2_EPS',
                                #'diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-16_last-Prev2_GEPS'
                                ],
             'is_prod': True},
        'y_1730-1830_eps12Z9to13p1':
            {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5],
             'preds_for_comb': ['diff_12Z_9-13_last-Prev1_EPS',
                                ],
             'is_prod': True},
        'y_1200-1400_EPSday15':
            {'start': dtdt(2020, 5, 1), 'chosen_ratio': [0.5,0.4,0.4],

             'preds_for_comb': ['Value_0Z_day15trend_EPS_diff2',
                                'Value_0Z_day15trend_EPS_diff1','Value_0Z_day15trend_EPS'
                                ],
             'is_prod': True},

        'y_1515-1845_cashFt123': {'start': march_start, 'chosen_ratio': [0.6],#[0.75],  # [0.5, 0.5,0.4],

                    'preds_for_comb': ['diff_cashVprompt_diff1'],
                          'is_prod': True},
        'y_1745-1815_cashPrice': {'start': march_start, 'chosen_ratio': 0.65,
                                  'preds_for_comb': ['diff_cashVprompt_diff1'],
                                  'is_prod': True},
            'y_1200-1245_mon': {'start': march_start, 'chosen_ratio': [0.5,0.4],  # GDD from April!
                            'preds_for_comb': ['Cash_pred2','diff_0Z_0-16_last-Prev1D_PARA'],
                            'is_prod': True},
        'y_2300-2345_GFS12Z': {'start': march_start, 'chosen_ratio': [0.45,0.35],
                            'preds_for_comb': ['diff_12Z_0-16_last-Prev2_PARA',
                                               'diff_12Z_0-16_last-Prev2_PARACO'],
                                 'is_prod': True},
        'y_2300-2345_GFS12ZMean': {'start': march_start, 'chosen_ratio': [0.45, 0.35],
                               'preds_for_comb': ['diff_12Z_0-16_last-Prev2_PARA',
                                                  'diff_12Z_0-16_last-Prev2_PARACO'],
                               'is_prod': True},
            'y_1945-2145_EC12Z': {'start': march_start, 'chosen_ratio': [0.55,0.3],
                            'preds_for_comb': ['diff_12Z_0-8_last-Prev2_EC',
                                               'diff_12Z_0-8_last-Prev1_EC'],
                            'is_prod': True},
        'y_1300-1430_EC0dChange': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['rans_test_04_pred1','rans_test_pred2',
                                                    'rans_test_04_pred3'],
                                 'is_prod': True},
        'y_1100-1200_0dtrend': {'start': dtdt(2022,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb': ['diff_0Z_0-0_last-7D'],
                                 'is_prod': True},
        'y_1300-1430_EC0dChangeStrict': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.2,
                                 'preds_for_comb': ['rans_test_04_pred1','rans_test_pred2',
                                                    'rans_test_04_pred3'],
                                 'is_prod': True},
            'y_1745-1815_GEPS12z0to8': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev2_GEPS',
                                                    'diff_12Z_0-8_last-Prev1_GEPS',
                                                    'diff_12Z_0-8_last-Prev3_GEPS',
                                                    ],
                                 'is_prod': True},
            'y_1700-1745_PARA12z0to8p34': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': ['MEAN','diff_12Z_0-8_last-Prev3_PARA',
                                                    'diff_12Z_0-8_last-Prev4_PARA',
                                                    ],
                                 'is_prod': True},
            'y_1715-1845_PARACO12z0to8': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev2_PARACO',
                                                    'diff_12Z_0-8_last-Prev1_PARACO',
                                                    'diff_12Z_0-8_last-Prev3_PARACO',
                                                    ],
                                 'is_prod': True},
                 'y_1815-1915_PARACO12z0to8p1': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                               'preds_for_comb':
                                                   ['diff_12Z_0-8_last-Prev1_PARACO',
                                                    'diff_12Z_0-8_last-Prev3_PARACO',
                                                    'diff_12Z_0-8_last-Prev4_PARACO'],
                                               'is_prod': True},

                 'y_1815-1845_PARACO12z0to10p1': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                               'preds_for_comb':
                                                   ['diff_12Z_0-10_last-Prev1_PARACO',
                                                    ],
                                               'is_prod': True},

                 'y_1815-1845_PARACO12z0to10p14': {'start': dtdt(2020, 5, 15), 'chosen_ratio': 0.5,
                                               'preds_for_comb':
                                                   ['MEAN','diff_12Z_0-10_last-Prev4_PARACO','diff_12Z_0-10_last-Prev1_PARACO',
                                                    ],
                                               'is_prod': True},

                         'y_1745-1945_GEFS12z8to16': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.45,0.4,0.4,0.25,0.1,0.1,0.1],
                                 'preds_for_comb': ['MEAN','diff_12Z_8-16_last-Prev4_GEFS',
                                                    'diff_12Z_8-16_last-Prev2_GEFS','diff_12Z_8-16_last-Prev1_GEFS','diff_12Z_8-16_last-Prev3_GEFS',
                                                    'diff_12Z_11-15_last-Prev4_GEFS','diff_12Z_11-15_last-Prev2_GEFS',

                                                    ],
                                 'is_prod': True},
                    'y_1745-1945_GEFS12z0to8': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.5,0.4,0.4,0.4],
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev3_GEFS',
                                                    'MEAN','diff_12Z_0-8_last-Prev2_GEFS',
                                                    'diff_12Z_0-8_last-Prev1_GEFS',
                                                    ],
                                 'is_prod': True},
                    'y_1745-1945_GEFS12z0to8Strict': {'start': dtdt(2020,3,1), 'chosen_ratio': [0.25,0.1,0.1,0.1],
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev3_GEFS',
                                                    'MEAN','diff_12Z_0-8_last-Prev2_GEFS',
                                                    'diff_12Z_0-8_last-Prev1_GEFS',
                                                    ],
                                 'is_prod': True},
            'y_1845-1945_GEPS12z0to8p1': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev1_GEPS',
                                                    ],
                                 'is_prod': True},
                'y_1845-1945_GEPS12z0to8p1Strict': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb': ['diff_12Z_0-8_last-Prev1_GEPS',
                                                    ],
                                 'is_prod': True},
                'y_1815-1845_GEPS12zp1': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.3,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEPS'
                                                    ],
                                 'is_prod': True},
                'y_1100-1200_GEPS12zp1loose': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_0-16_last-Prev1_GEPS'
                                                    ],
                                 'is_prod': True},
                'y_1815-1945_CFScomb': {'start': dtdt(2021,2,1), 'chosen_ratio': [0.5,0.2,0.2,0.2,0.2,0.2],
                                 'preds_for_comb': ['MEAN','diff_12Z_28-42_last-Prev1_CFS', 'diff_12Z_28-35_last-Prev4_CFS', 'diff_12Z_28-42_last-Prev4_CFS',
                                                    #'diff_6Z_0-2_last-Prev3_CFS', 'diff_6Z_14-16_Prev1-Prev2_GFSv16',
                                                    'diff_18Z_14-28_last-Prev2D_CFS',
                                                    'diff_18Z_28-35_Prev1-Prev2_CFS'],
                                         'is_prod': True},
                'y_0600-0900_GFSCO0zp23Strict': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb': ['MEAN','diff_0Z_0-16_last-Prev3_PARACO','diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev2_PARACO'],
                                 'is_prod': True},
                'y_0800-1845_Daily6ZScan': {'start': dtdt(2021,4,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb': [
                                                    'diff_6Z_0-16_last-Prev1_GEFS','diff_6Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                                                    'diff_6Z_11-15_last-Prev1_GEFS','diff_6Z_11-15_last-Prev2_GEFS','diff_6Z_11-15_last-Prev3_GEFS','diff_6Z_11-15_last-Prev4_GEFS',
                                                    'diff_6Z_0-16_last-Prev1_PARA','diff_6Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev3_PARA','diff_6Z_0-16_last-Prev4_PARA',
                                                    'diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO','diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO',


                                                    ],
                                 'is_prod': True},
                'y_0800-1845_Daily0ZminiScan': {'start': dtdt(2022,4,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb': [
                                                    'diff_0Z_0-13_last-Prev1_EPS',
                                                    'diff_0Z_0-13_last-Prev2_EPS',
                                                    'diff_0Z_0-13_last-Prev4_EPS',
                                                    'diff_0Z_0-8_last-Prev1_EC',
                                                    'diff_0Z_0-8_last-Prev2_EC',
                                                    'diff_0Z_0-15_last-Prev1_EPSCO_population_US',
                                                    'diff_0Z_0-15_last-Prev2_EPSCO_population_US',

                                                    'diff_0Z_0-16_last-Prev1_GEFS',
                                                    'diff_0Z_0-16_last-Prev2_GEFS',
                                                    'diff_0Z_0-16_last-Prev4_GEFS',
                                                    'diff_0Z_0-13_last-Prev1_PARA',
                                                    'diff_0Z_0-13_last-Prev2_PARA',
                                                    'diff_0Z_0-13_last-Prev4_PARA',
                                                    'diff_0Z_0-16_last-Prev1D_GEFS',
                                                    'diff_0Z_0-16_last-Prev1_GEPS',
                                                    'diff_0Z_0-16_last-Prev2_GEPS',
                                                    'diff_0Z_0-16_last-Prev4_GEPS',
                                                    # 'diff_0Z_0-16_last-Prev4_GEFS',
                                                    # 'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev4_PARA',
                                                    ],
                                 'is_prod': True},
                'y_0800-1845_Daily12ZminiScan': {'start': dtdt(2022,4,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': [
                                                    'diff_12Z_0-13_last-Prev1_EPS',
                                                    'diff_12Z_0-13_last-Prev2_EPS',
                                                    'diff_12Z_0-8_last-Prev1_EC',
                                                    'diff_12Z_0-8_last-Prev2_EC',
                                                    'diff_12Z_0-15_last-Prev1_EPSCO_population_US',
                                                    'diff_12Z_0-15_last-Prev2_EPSCO_population_US',

                                                    'diff_12Z_0-16_last-Prev1_GEFS',
                                                    'diff_12Z_0-16_last-Prev2_GEFS',
                                                    'diff_12Z_0-16_last-Prev4_GEFS',
                                                     # 'diff_12Z_0-13_last-Prev1_PARA','diff_12Z_0-13_last-Prev2_PARA','diff_12Z_0-13_last-Prev3_PARA','diff_12Z_0-13_last-Prev4_PARA',
                                                    'diff_12Z_0-16_last-Prev1_PARA','diff_12Z_0-16_last-Prev2_PARA',
                                                    'diff_12Z_0-16_last-Prev3_PARA','diff_12Z_0-16_last-Prev4_PARA',
                                                     # 'diff_12Z_0-8_last-Prev1_PARA',
                                                     # 'diff_12Z_0-8_last-Prev2_PARA',
                                                     # 'diff_12Z_0-8_last-Prev4_PARA',

                                     'diff_12Z_0-16_last-Prev1_GEPS',
                                                    'diff_12Z_0-16_last-Prev2_GEPS',
                                                    # 'diff_0Z_0-16_last-Prev4_GEFS',
                                                    # 'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev4_PARA',
                                                    ],
                                 'is_prod': True},
                'y_0800-1845_DailyEPScan': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': [
                                                    'diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                                    'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev4_EPS',
                                                    'diff_0Z_0-13_last-Prev1_EPS+1d','diff_0Z_0-13_last-Prev2_EPS+1d','diff_0Z_0-13_last-Prev4_EPS+1d',
                                                    'diff_12Z_0-13_last-Prev1_EPS+1d','diff_12Z_0-13_last-Prev2_EPS+1d','diff_12Z_0-13_last-Prev4_EPS+1d',


                                                    ],
                                 'is_prod': True},
                'y_0800-1845_DailyGEPScan': {'start': dtdt(2023,1,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb': [
                                                    'diff_0Z_0-16_last-Prev1_GEPS','diff_0Z_0-16_last-Prev2_GEPS','diff_0Z_0-16_last-Prev4_GEPS',
                                                    'diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev4_GEPS',
                                                    'diff_0Z_0-16_last-Prev1_GEMCO','diff_0Z_0-16_last-Prev2_GEMCO','diff_0Z_0-16_last-Prev4_GEMCO',
                                                    'diff_12Z_0-16_last-Prev1_GEMCO','diff_12Z_0-16_last-Prev2_GEMCO','diff_12Z_0-16_last-Prev4_GEMCO',
                                                    # 'diff_0Z_0-16_last-Prev1_GEPS+1d','diff_0Z_0-16_last-Prev2_GEPS+1d','diff_0Z_0-16_last-Prev4_GEPS+1d',
                                                    # 'diff_12Z_0-16_last-Prev1_GEPS+1d','diff_12Z_0-16_last-Prev2_GEPS+1d','diff_12Z_0-16_last-Prev4_GEPS+1d',


                                                    ],
                                 'is_prod': True},
                'y_0800-1845_GEFS_6zp3singleton': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev3_GEFS',
                                                    ],
                                 'is_prod': True},
                'y_0800-1845_GEFS_0zp2singleton': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_0Z_0-16_last-Prev2_GEFS',
                                                    ],
                                 'is_prod': True},
                'y_0800-1845_DailyGEFScan': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': [
                                                    'diff_0Z_0-16_last-Prev1_GEFS','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev3_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                                                    'diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                                                    'diff_6Z_0-16_last-Prev1_GEFS','diff_6Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                                                    'diff_18Z_0-16_last-Prev1_GEFS','diff_18Z_0-16_last-Prev2_GEFS','diff_18Z_0-16_last-Prev3_GEFS','diff_18Z_0-16_last-Prev4_GEFS',
                                                    # 'diff_0Z_0-16_last-Prev1_GEFS+1d','diff_0Z_0-16_last-Prev2_GEFS+1d','diff_0Z_0-16_last-Prev4_GEFS+1d',
                                                    # 'diff_12Z_0-16_last-Prev1_GEFS+1d','diff_12Z_0-16_last-Prev2_GEFS+1d','diff_12Z_0-16_last-Prev4_GEFS+1d',
                                                    'diff_0Z_0-16_last-Prev1D_GEFS','diff_0Z_0-16_last-Prev2D_GEFS','diff_0Z_0-16_last-Prev3D_GEFS',
                                                    ],
                                 'is_prod': True},
                'y_0800-1845_Daily12ZScan': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': [
                                                    'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev4_EPS',
                                                    'diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev4_GEPS',
                                                    'diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                                                    'diff_12Z_0-16_last-Prev1_PARA','diff_12Z_0-16_last-Prev2_PARA','diff_12Z_0-16_last-Prev4_PARA',
                                                    'diff_12Z_0-16_last-Prev1_PARACO','diff_12Z_0-16_last-Prev2_PARACO','diff_12Z_0-16_last-Prev4_PARACO',
                                                    'diff_12Z_0-8_last-Prev1_EC','diff_12Z_0-8_last-Prev2_EC','diff_12Z_0-8_last-Prev4_EC',

                                                    ],
                                 'is_prod': True},
                'y_0800-1845_Daily0ZScan': {'start': dtdt(2021,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': [
                                                    'diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                                    'diff_0Z_0-16_last-Prev1_GEPS','diff_0Z_0-16_last-Prev2_GEPS','diff_0Z_0-16_last-Prev4_GEPS',
                                                    'diff_0Z_0-16_last-Prev1_GEFS','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                                                    'diff_0Z_0-16_last-Prev1_PARA','diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev4_PARA',
                                                    'diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
                                                    'diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC','diff_0Z_0-8_last-Prev4_EC',

                                                    ],
                                 'is_prod': True},
                'y_0800-1845_DailyPreds': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     ['diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-13_last-Prev1D_EPS',
                                         'EPS_daily_hybrid_pred2.2', 'EPSpost_pred2.2',
                                      'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev1D_PARA',
                                      'diff_0Z_0-16_last-Prev2D_GEFS', 'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev2D_PARA',
                                      'diff_0Z_0-16_last-Prev14D_PARARACO', 'diff_0Z_0-16_last-Prev24D_GEMCO',
                                      'GEPS_daily_hybrid_pred3', 'GEFSL_daily_hybrid_pred3',
                                    'diff_0Z_0-16_last-Prev1D_GEPS', 'diff_0Z_0-16_last-Prev2D_GEPS',
                                    'diff_0Z_0-16_last-Prev1D_GEMCO', 'diff_0Z_0-16_last-Prev2D_GEMCO',
                                      'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO',
                                      'diff_0Z_0-16_last-Prev4D_PARA',
                                          ],
                                         'is_prod': True},
                'y_0800-1845_DailyPredsLongT': {'start': dtdt(2022,8,1), 'chosen_ratio': 0.44,
                             'preds_for_comb':
                                     [
                                         'diff_0Z_10-21_last-Prev1D_CFS','diff_0Z_10-21_last-Prev2D_CFS',
                                         'diff_0Z_10-21_last-Prev3D_CFS','diff_0Z_10-21_last-Prev4D_CFS',
                                         'diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev2D_CFS',
                                         'diff_0Z_14-28_last-Prev3D_CFS','diff_0Z_14-28_last-Prev4D_CFS',
                                         'diff_0Z_21-42_last-Prev1D_CFS','diff_0Z_21-42_last-Prev2D_CFS',
                                         'diff_0Z_21-42_last-Prev3D_CFS','diff_0Z_21-42_last-Prev4D_CFS',

                                         'diff_0Z_10-21_last-Prev1D_CFSCO','diff_0Z_10-21_last-Prev2D_CFSCO',
                                         'diff_0Z_10-21_last-Prev3D_CFSCO','diff_0Z_10-21_last-Prev4D_CFSCO',
                                         'diff_0Z_14-28_last-Prev1D_CFSCO','diff_0Z_14-28_last-Prev2D_CFSCO',
                                         'diff_0Z_14-28_last-Prev3D_CFSCO','diff_0Z_14-28_last-Prev4D_CFSCO',
                                         'diff_0Z_21-42_last-Prev1D_CFSCO','diff_0Z_21-42_last-Prev2D_CFSCO',
                                         'diff_0Z_21-42_last-Prev3D_CFSCO','diff_0Z_21-42_last-Prev4D_CFSCO',

                                        ],
                                         'is_prod': True},
                'y_0800-1845_DailyPredsPrev1d': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     [x+'-1d' if '-1d' not in x else x
                                         for x in ['diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-13_last-Prev1D_EPS',
                                         'EPS_daily_hybrid_pred2.2', 'EPSpost_pred2.2',
                                       'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev1D_PARA',
                                       'diff_0Z_0-16_last-Prev2D_GEFS', 'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev2D_PARA',

                                       'diff_0Z_0-16_last-Prev14D_PARARACO', 'diff_0Z_0-16_last-Prev24D_GEMCO',
                                      'GEPS_daily_hybrid_pred3', 'GEFSL_daily_hybrid_pred3',
                                    'diff_0Z_0-16_last-Prev1D_GEPS', 'diff_0Z_0-16_last-Prev2D_GEPS',
                                    'diff_0Z_0-16_last-Prev1D_GEMCO', 'diff_0Z_0-16_last-Prev2D_GEMCO',
                                      'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO',
                                      'diff_0Z_0-16_last-Prev4D_PARA',
                                        'diff_0Z_10-21_last-Prev1D_CFS','diff_0Z_10-21_Prev1D-Prev2D_CFS',
                                    'diff_0Z_10-21_last-Prev2D_CFS',
                                        ]],
                                         'is_prod': True},

                'y_0800-1845_DailyPredsLongTPrev1d': {'start': dtdt(2022,8,1), 'chosen_ratio': 0.44,
                             'preds_for_comb':
                                     [x+'-1d' if '-1d' not in x else x
                                         for x in [
                                          'diff_0Z_10-21_last-Prev1D_CFS', 'diff_0Z_10-21_last-Prev2D_CFS',
                                          'diff_0Z_10-21_last-Prev3D_CFS', 'diff_0Z_10-21_last-Prev4D_CFS',
                                          'diff_0Z_14-28_last-Prev1D_CFS', 'diff_0Z_14-28_last-Prev2D_CFS',
                                          'diff_0Z_14-28_last-Prev3D_CFS', 'diff_0Z_14-28_last-Prev4D_CFS',
                                          'diff_0Z_21-42_last-Prev1D_CFS', 'diff_0Z_21-42_last-Prev2D_CFS',
                                          'diff_0Z_21-42_last-Prev3D_CFS', 'diff_0Z_21-42_last-Prev4D_CFS',

                                          'diff_0Z_10-21_last-Prev1D_CFSCO', 'diff_0Z_10-21_last-Prev2D_CFSCO',
                                          'diff_0Z_10-21_last-Prev3D_CFSCO', 'diff_0Z_10-21_last-Prev4D_CFSCO',
                                          'diff_0Z_14-28_last-Prev1D_CFSCO', 'diff_0Z_14-28_last-Prev2D_CFSCO',
                                          'diff_0Z_14-28_last-Prev3D_CFSCO', 'diff_0Z_14-28_last-Prev4D_CFSCO',
                                          'diff_0Z_21-42_last-Prev1D_CFSCO', 'diff_0Z_21-42_last-Prev2D_CFSCO',
                                          'diff_0Z_21-42_last-Prev3D_CFSCO', 'diff_0Z_21-42_last-Prev4D_CFSCO',

                                      ]],
                                         'is_prod': True},

                'y_0800-1845_DailyPredsNext1d': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     [x+'+1d' if '1d' not in x else x
                                         for x in ['diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-13_last-Prev1D_EPS',
                                        'diff_0Z_0-8_last-Prev2_EC','diff_0Z_0-13_last-Prev2_EPS',
                                        'diff_12Z_0-8_last-Prev2_EC','diff_12Z_0-13_last-Prev2_EPS',
                                         'EPS_daily_hybrid_pred2.2', 'EPSpost_pred2.2',
                                       'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev1D_PARA',
                                       'diff_0Z_0-16_last-Prev2D_GEFS', 'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev2D_PARA',

                                   'diff_0Z_0-16_last-Prev14D_PARARACO', 'diff_0Z_0-16_last-Prev24D_GEMCO',
                                      'GEPS_daily_hybrid_pred3', 'GEFSL_daily_hybrid_pred3',
                                    'diff_0Z_0-16_last-Prev1D_GEPS', 'diff_0Z_0-16_last-Prev2D_GEPS',
                                    'diff_0Z_0-16_last-Prev1D_GEMCO', 'diff_0Z_0-16_last-Prev2D_GEMCO',
                                      'diff_0Z_0-13_last-Prev4D_EPS', 'diff_0Z_0-16_last-Prev4D_PARACO',
                                      'diff_0Z_0-16_last-Prev4D_PARA',
                                        'diff_0Z_10-21_last-Prev1D_CFS','diff_0Z_10-21_Prev1D-Prev2D_CFS',
                                    'diff_0Z_10-21_last-Prev2D_CFS',

                                      'diff_0Z_14-28_last-Prev3D_CFS','diff_0Z_14-28_Prev1D-Prev3D_CFS',
                                        'diff_0Z_14-28_Prev1D-Prev2D_CFS',
                                            'diff_12Z_14-28_last-Prev4D_CFS',
                                        'diff_0Z_28-42_Prev1D-Prev3D_CFS','diff_0Z_28-42_Prev1D-Prev3D_CFS',
                                        'diff_0Z_0-16_last-Prev1D_GFSv16', 'diff_0Z_0-16_last-Prev2D_GFSv16',
                                      'diff_0Z_0-16_last-Prev3D_GFSv16', 'diff_0Z_0-16_last-Prev4D_GFSv16',

                                      'diff_0Z_0-16_last-Prev2D_CFS','diff_12Z_0-16_last-Prev1D_CFS',
                                        'diff_0Z_0-16_last-Prev1D_CFS',#'diff_0Z_0-16_Prev1D-Prev2D_CFS',
                                        ][:16]],
                                         'is_prod': True},

               'y_0800-1845_DailyPredsNext1d_v2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     [x+'+1d' if '1d' not in x else x
                                         for x in [
                                          'diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-13_last-Prev1D_EPS',
                                        'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                        'diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                        'diff_12Z_0-8_last-Prev1_EC','diff_12Z_0-8_last-Prev2_EC',
                                        'diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC',
                                        'diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                        'diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                        'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev4_PARA',
                                        'diff_12Z_0-16_last-Prev2_PARA','diff_12Z_0-16_last-Prev4_PARA',
                                        'diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                                        'diff_12Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                                        'diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
                                        'diff_12Z_0-16_last-Prev2_PARACO','diff_12Z_0-16_last-Prev4_PARACO',
                                       'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev1D_PARA',
                                       'diff_0Z_0-16_last-Prev2D_GEFS', 'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev2D_PARA',
                                      ]],
                                         'is_prod': True},

               'y_0800-1845_DailyPredsNext1d_12z': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     [x+'+1d' if '1d' not in x else x
                                         for x in [
                                          'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                        'diff_12Z_0-8_last-Prev1_EC','diff_12Z_0-8_last-Prev2_EC',
                                        'diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                        'diff_12Z_0-16_last-Prev2_PARA','diff_12Z_0-16_last-Prev4_PARA',
                                        'diff_12Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                                        'diff_12Z_0-16_last-Prev2_PARACO','diff_12Z_0-16_last-Prev4_PARACO',
                                       'diff_12Z_0-16_last-Prev1D_GEFS', 'diff_12Z_0-16_last-Prev1D_PARACO','diff_12Z_0-16_last-Prev1D_PARA',
                                       'diff_12Z_0-16_last-Prev2D_GEFS', 'diff_12Z_0-16_last-Prev2D_PARACO','diff_12Z_0-16_last-Prev2D_PARA',
                                      ]],
                                         'is_prod': True},

               'y_0800-1845_DailyPredsNext1d_0z': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                             'preds_for_comb':
                                     [x+'+1d' if '1d' not in x else x
                                         for x in [
                                          'diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                        'diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC',
                                        'diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                        'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev4_PARA',
                                        'diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                                        'diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
                                       'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_0-16_last-Prev1D_PARACO','diff_0Z_0-16_last-Prev1D_PARA',
                                       'diff_0Z_0-16_last-Prev2D_GEFS', 'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev2D_PARA',
                                      ]],
                                         'is_prod': True},

               # longT
              'y_0800-1415_CFSDailyComb10to20Prev1d': {'start': dtdt(2022, 8, 1), 'chosen_ratio': [0.44,0.3,0.2],
                                          'preds_for_comb':
                                              ['MEAN']+[x + '-1d' if '-1d' not in x else x
                                               for x in [
                                                    'diff_0Z_10-21_last-Prev4D_CFS',
                                                   'diff_0Z_10-21_last-Prev1D_CFS',
                                               ]],
                                          'is_prod': True},
             'y_0800-1415_CFSDailyComb14to28Prev1d': {'start': dtdt(2022, 8, 1), 'chosen_ratio': 0.44,
                                          'preds_for_comb':
                                              ['MEAN']+[x + '-1d' if '-1d' not in x else x
                                               for x in [
                                                   # 'diff_0Z_14-28_last-Prev1D_CFS',
                                                    'diff_0Z_14-28_last-Prev2D_CFS',
                                                   # 'diff_0Z_14-28_last-Prev3D_CFS',
                                                   'diff_0Z_14-28_last-Prev4D_CFS',
                                               ]],
                                          'is_prod': True},
             'y_0800-1415_CFSCODailyComb10to20Prev1d': {'start': dtdt(2022, 8, 1), 'chosen_ratio': 0.44,
                                          'preds_for_comb':
                                              ['MEAN']+[x + '-1d' if '-1d' not in x else x
                                               for x in [
                                                   'diff_0Z_10-21_last-Prev1D_CFSCO', 'diff_0Z_10-21_last-Prev2D_CFS',
                                                   # 'diff_0Z_14-28_last-Prev3D_CFS',
                                                   'diff_0Z_10-21_last-Prev4D_CFS',
                                               ]],
                                          'is_prod': True},
            'y_0800-1415_CFSCODailyComb14to28Prev1d': {'start': dtdt(2022, 8, 1), 'chosen_ratio': [0.44,0.0001,0.0001,0.0001],
                                          'preds_for_comb':
                                              ['MEAN']+[x + '-1d' if '-1d' not in x else x
                                               for x in [
                                                   'diff_0Z_14-28_last-Prev1D_CFSCO', 'diff_0Z_14-28_last-Prev2D_CFS',
                                                   # 'diff_0Z_14-28_last-Prev3D_CFS',
                                                   'diff_0Z_14-28_last-Prev4D_CFS',
                                               ]],
                                          'is_prod': True},

    'y_0800-1845_PARACO0zp38to16W': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_8-16_last-Prev3_PARACO'
                                 ],

                            'is_prod': True},
                'y_0800-1845_PARA0zp12W': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['MEAN',
                                 'diff_0Z_8-16_last-Prev1_PARA',
                                 'diff_0Z_8-16_last-Prev3_PARA','diff_0Z_8-16_last-Prev2_PARA',
                                 ],
                                'is_prod': True},
                'y_0800-1845_PARA0zp1W': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['diff_0Z_0-16_last-Prev1_PARA',
                                 ],
                                'is_prod': True},
                'y_0800-1845_GEFS0zp28to16W': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['diff_0Z_8-16_last-Prev2_GEFS',
                                 ],
                                'is_prod': True},
                'y_0800-1845_AmericanDaily0zW': {'start': dtdt(2020,4,1), 'chosen_ratio': [0.45]+[0.0001]*9,
                                 'preds_for_comb':
                                ['MEAN',
                                 'diff_0Z_0-16_last-Prev4_PARACO','diff_0Z_0-16_last-Prev2_PARACO',
                                 'diff_0Z_8-16_last-Prev2_PARACO','diff_0Z_8-16_last-Prev3_PARACO',
                                    'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_8-16_last-Prev2_PARA',
                                        'diff_0Z_8-16_last-Prev3_PARA','diff_0Z_0-16_last-Prev1_PARA',
                                'diff_0Z_8-16_last-Prev2_GEFS'],

                            'is_prod': True},
                    'y_0800-1845_AmericanDaily18zW': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                [
                                    'diff_18Z_0-16_last-Prev4_PARA',
                                    # separate
                                    'diff_18Z_0-16_last-Prev2_PARACO','diff_18Z_0-16_last-Prev2_PARACO'],

                            'is_prod': True},
                'y_0000-0600_PARA18zp2': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_18Z_0-16_last-Prev2_PARA',
                                    ],

                            'is_prod': True},
                'y_0800-1845_American06Z': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                               [f'diff_{model_run}_{days}_last-Prev{prev}_{model}'
                                for model_run in ['6Z','0Z','18Z','12Z'][:2]
                                for days in ['0-16','8-16'][:1]
                                for prev in [2,4]
                                for model in ['PARA','PARACO','GEFS','CFS']
                                ],
                            'is_prod': True},
                'y_0800-1845_American1218Z': {'start': dtdt(2020,4,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                               [f'diff_{model_run}_{days}_last-Prev{prev}_{model}'
                                for model_run in ['6Z','0Z','18Z','12Z'][2:]
                                for days in ['0-16','8-16'][:1]
                                for prev in [2,4]
                                for model in ['PARA','PARACO','GEFS','CFS']
                                ],
                            'is_prod': True},
                'y_1245-1745_SeasonalComb2': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.55]*3+[0.44]*2+[0.25]*2,
                                 'preds_for_comb':
                                ['MEAN','Value_0Z_0-16_PARA_rs',
                                 'PARA_daily_hybrid_8-16_pred2', 'diff_0Z_8-16_last-Prev4D_PARA',
                                 'Value_6Z_14-16_GEFS_rs', 'Value_6Z_8-16_GFSv16_rs','Value_0Z_14-16_GEFS_rs',
                                 ],
                            # ['diff_12Z_0-16_Prev1-Prev2_CFSCO', 'diff_6Z_14-28_last-Prev1_CFSCO', 'std_0-16_top3_rs', 'diff_6Z_14-28_last-Prev2_CFSCO', 'diff_0Z_21-28_last-Prev3_EPS45', 'diff_12Z_8-16_Prev1-Prev2_CFSCO', 'diff_12Z_8-16_last-Prev1D_GEMCO', 'diff_0Z_8-16_last-Prev4D_GFSv16', 'PARA_daily_hybrid_8-16_pred2', 'diff_0Z_8-16_last-Prev4D_PARA', 'diff_6Z_0-13_last-Prev2_CFS']
                            # 'EPS_12Z_9to13_Pgefs12Z_window=15','EPS_12Z_9to13_Pparaco12Z_window=10','EPS_12Z_9to13_Ppara12Z_window=5',
                            'is_prod': True},
                'y_1100-1745_EPS9to13comb': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                                ['diff_0Z_13-13_last-Prev1_EPS', 'diff_0Z_9-13_last-Prev1_EPS',
                                 'diff_0Z_12-13_last-Prev1_EPS'
                                 ],
                            'is_prod': True},
                'y_0800-1845_EPS12to13p2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_12-13_last-Prev2_EPS'
                                 ],
                            'is_prod': True},
                'y_0800-1845_EPS12to13p2Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['diff_0Z_12-13_last-Prev2_EPS'
                                 ],
                            'is_prod': True},
                'y_0800-1845_EPS12to13p2comb': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_12-13_last-Prev2_EPS','diff_12Z_12-13_last-Prev1_EPS'
                                 ],
                            'is_prod': True},
                'y_1800-1830_EPS12ZFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                 'diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS'],
                            'is_prod': True},
                'y_1800-1830_EPS0ZFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                 'diff_0Z_0-13_last-Prev3_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                     'diff_0Z_0-13_last-Prev1D_EPS','diff_0Z_0-13_last-Prev2D_EPS',
                                     'diff_0Z_0-13_last-Prev3D_EPS','diff_0Z_0-13_last-Prev4D_EPS',
                                     'diff_0Z_0-13_Prev1D-Prev2D_EPS','diff_0Z_0-13_Prev1D-Prev3D_EPS',
                                 ],
                            'is_prod': True},
                'y_1800-1830_EPSCOTest': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.7,
                                 'preds_for_comb':
                                [x+'-1d' for x in ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                                    'diff_0Z_0-15_Prev1-Prev2_EPSCO_population_US','diff_0Z_0-15_Prev1-Prev3_EPSCO_population_US',
                                        'diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                        'diff_12Z_0-15_Prev1-Prev2_EPSCO_population_US','diff_12Z_0-15_Prev1-Prev3_EPSCO_population_US',
                                                   ]]+\
                                [
                                'diff_0Z_0-15_last-Prev1_EPSCO_population_US',
                                 'diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                 'diff_12Z_0-15_last-Prev1_EPSCO_population_US',
                                 'diff_12Z_0-15_last-Prev2_EPSCO_population_US'],
                                  ### todo ?!?!!?!?!
                                  # 0 x 0800-1845
                                  # ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                    # 'diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                          # 'diff_0Z_0-15_last-Prev2_EPSCO_population_US-1d','diff_12Z_0-15_last-Prev1_EPSCO_population_US-1d']
                                  # 1 x 0800-1845
                                  # ['diff_0Z_0-15_last-Prev2_EPSCO_population_US','diff_12Z_0-15_last-Prev1_EPSCO_population_US',
                                          # 'diff_12Z_0-15_last-Prev2_EPSCO_population_US']
                                  # 2 x 0800-1845
                                  # ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US-1d-']
                                  # 3 x 0800-1845
                                  # ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_Prev1-Prev2_EPSCO_population_US-1d-']
                                  # 4 x 0800-1845
                                  # ['diff_12Z_0-15_last-Prev2_EPSCO_population_US']
                                        'is_prod': True},
                'y_1800-1830_tmpStrat': {'start': dtdt(2024,12,10), 'chosen_ratio': 0.8,
                                 'preds_for_comb':
                                     [x+'-' for x in [
 'EPO_EPS_diff_last-12h_8-15_0Z',
 'EPO_EPS_diff_last-24h_8-15_0Z',
 'EPO_GEFS_diff_last-12h_8-15_0Z',
 'EPO_GEFS_diff_last-24h_8-15_0Z',
 'EPO_EPS_diff_last-12h_8-15_12Z',
 'EPO_EPS_diff_last-24h_8-15_12Z',
 'EPO_GEFS_diff_last-12h_8-15_12Z',
 'EPO_GEFS_diff_last-24h_8-15_12Z',
 'EPO_EPS_8-15_0Z',
 'EPO_GEFS_8-15_0Z',
 # 'NAO_EPS_diff_last-12h_8-15_0Z',
 # 'NAO_EPS_diff_last-24h_8-15_0Z',
 # 'NAO_GEFS_diff_last-12h_8-15_0Z',
 # 'NAO_GEFS_diff_last-24h_8-15_0Z',
 # 'NAO_EPS_diff_last-12h_8-15_12Z',
 # 'NAO_EPS_diff_last-24h_8-15_12Z',
 # 'NAO_GEFS_diff_last-12h_8-15_12Z',
 # 'NAO_GEFS_diff_last-24h_8-15_12Z',
 # 'NAO_EPS_8-15_0Z',
 # 'NAO_GEFS_8-15_0Z',
                                      ]],
                            'is_prod': True}, #
                'y_0800-1845_windScan': {'start': dtdt(2022,7,1), 'chosen_ratio': 0.3,
                                 'preds_for_comb':
                                [
                                'diff_0Z_0-8_last-Prev1_EC_ws10mUS','diff_0Z_0-8_last-Prev2_EC_ws10mUS','diff_0Z_0-8_last-Prev4_EC_ws10mUS',
                                 'diff_0Z_0-4_last-Prev1_EC_ws10mUS','diff_0Z_0-4_last-Prev2_EC_ws10mUS','diff_0Z_0-4_last-Prev4_EC_ws10mUS',
                                'diff_0Z_0-8_last-Prev1_EC_ws10mTX','diff_0Z_0-8_last-Prev2_EC_ws10mTX','diff_0Z_0-8_last-Prev4_EC_ws10mTX',
                                'diff_0Z_0-4_last-Prev1_EC_ws10mTX','diff_0Z_0-4_last-Prev2_EC_ws10mTX','diff_0Z_0-4_last-Prev4_EC_ws10mTX',
                                # 'diff_0Z_0-8_last-Prev1_EPS_ws10mUS','diff_0Z_0-8_last-Prev2_EPS_ws10mUS','diff_0Z_0-8_last-Prev4_EPS_ws10mUS',
                                # 'diff_0Z_0-13_last-Prev1_EPS_ws10mUS','diff_0Z_0-13_last-Prev2_EPS_ws10mUS','diff_0Z_0-13_last-Prev4_EPS_ws10mUS',
                                # 'diff_0Z_0-13_last-Prev1_EPSCO_ws10mUS','diff_0Z_0-13_last-Prev2_EPSCO_ws10mUS','diff_0Z_0-13_last-Prev4_EPSCO_ws10mUS',
                                # 'diff_0Z_0-8_last-Prev1_PARA_ws10mUS','diff_0Z_0-8_last-Prev2_PARA_ws10mUS','diff_0Z_0-8_last-Prev4_PARA_ws10mUS',
                                # 'diff_12Z_0-13_last-Prev1_EPS_ws10mUS','diff_12Z_0-13_last-Prev2_EPS_ws10mUS','diff_12Z_0-13_last-Prev4_EPS_ws10mUS',
                                # 'diff_0Z_0-8_last-Prev1_EC_ws10mTX','diff_0Z_0-8_last-Prev2_EC_ws10mTX','diff_0Z_0-8_last-Prev4_EC_ws10mTX',
                                 ]
                                    ,
                            'is_prod': True}, #
                'y_0800-1845_windScan_wide': {'start': dtdt(2022,7,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                [
                                'diff_0Z_0-8_last-Prev2D_EC_ws10mTX', 'diff_12Z_0-13_Prev1D-Prev2D_EPS_ws10mTX', 'diff_0Z_0-8_last-Prev4_EPSCO_ws10mTX', 'diff_0Z_0-13_last-Prev3D_EPSCO_ws10mTX', 'diff_12Z_0-8_last-Prev2D_EC_ws10mTX', 'diff_12Z_0-14_last-Prev1_EPSCO_ws10mTX', 'diff_12Z_0-13_last-Prev3D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mTX', 'diff_0Z_0-14_last-Prev4_EPSCO_ws10mUS', 'diff_12Z_0-8_last-Prev1D_EC_ws10mUS', 'diff_0Z_0-8_last-Prev4_EC_ws10mUS', 'diff_12Z_0-14_last-Prev2_EPSCO_ws10mUS', 'diff_12Z_0-14_last-Prev1_EPSCO_ws10mUS', 'diff_0Z_0-13_Prev1D-Prev2D_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev2D_EC_ws10mUS', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev4_EPSCO_ws10mUS', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mUS', 'diff_0Z_0-8_Prev1D-Prev2D_EC_ws10mUS'
                                # 1415-1845 
                                # 'diff_12Z_0-14_last-Prev2_EPSCO_ws10mTX', 'diff_0Z_0-8_last-Prev4_EPS_ws10mTX', 'diff_0Z_0-8_last-Prev3_EPS_ws10mTX', 'diff_12Z_0-13_Prev1D-Prev3D_EPS_ws10mTX', 'diff_12Z_0-13_Prev1D-Prev2D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev3D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mTX', 'diff_0Z_0-8_last-Prev1D_EC_ws10mTX-', 'diff_0Z_0-8_last-Prev2D_EC_ws10mTX-', 'diff_0Z_0-2_last-Prev1_EPSCO_ws10mTX-', 'diff_0Z_0-2_last-Prev1_EC_ws10mTX-', 'diff_0Z_0-8_last-Prev3D_EC_ws10mTX-', 'diff_12Z_0-8_last-Prev2D_EC_ws10mTX-', 'diff_0Z_0-14_last-Prev1_EPSCO_ws10mTX-', 'diff_0Z_0-2_last-Prev1_EPS_ws10mTX-', 'diff_12Z_0-8_Prev1D-Prev2D_EC_ws10mTX-', 'diff_0Z_0-14_last-Prev2_EPSCO_ws10mTX-', 'diff_0Z_0-8_last-Prev1_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev2_EPS_ws10mUS', 'diff_12Z_0-13_Prev1D-Prev2D_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev3_EPS_ws10mUS', 'diff_12Z_0-14_last-Prev2_EPSCO_ws10mUS', 'diff_0Z_0-8_last-Prev3_EPSCO_ws10mUS', 'diff_0Z_0-8_last-Prev4_EPS_ws10mUS', 'diff_12Z_0-13_Prev1D-Prev3D_EPS_ws10mUS', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mUS', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mUS', 'diff_12Z_0-13_last-Prev3D_EPS_ws10mUS', 'diff_12Z_0-14_last-Prev1_EPS_ws10mUS-', 'diff_12Z_0-14_last-Prev1_EPS_ws10mUS-', 'diff_0Z_0-2_last-Prev1_EPSCO_ws10mUS-', 'diff_12Z_0-8_Prev1D-Prev2D_EC_ws10mUS-', 'diff_0Z_0-2_last-Prev1_EC_ws10mUS-', 'diff_0Z_0-2_last-Prev1_EPS_ws10mUS-', 'diff_0Z_0-14_last-Prev2_EPSCO_ws10mUS-'
                                # 0800-1845
                                # 'diff_0Z_0-14_last-Prev3_EPSCO_ws10mTX', 'diff_0Z_0-14_last-Prev4_EPS_ws10mTX', 'diff_0Z_0-14_last-Prev3_EPS_ws10mTX', 'diff_0Z_0-8_last-Prev3_EPSCO_ws10mTX', 'diff_0Z_0-14_last-Prev1_EPS_ws10mTX', 'diff_0Z_0-13_last-Prev3D_EPSCO_ws10mTX', 'diff_0Z_0-8_last-Prev4_EPSCO_ws10mTX', 'diff_0Z_0-13_last-Prev2D_EPSCO_ws10mTX', 'diff_0Z_0-8_last-Prev4_EPS_ws10mTX', 'diff_12Z_0-13_Prev1D-Prev2D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev3D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mTX', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mTX', 'diff_0Z_0-4_last-Prev2_EC_ws10mTX-', 'diff_12Z_0-14_last-Prev1_EPS_ws10mTX-', 'diff_0Z_0-0_last-Prev1_EC_ws10mTX-', 'diff_0Z_0-13_Prev1D-Prev2D_EPSCO_ws10mUS', 'diff_12Z_0-8_last-Prev3D_EC_ws10mUS', 'diff_0Z_0-8_last-Prev3_EPS_ws10mUS', 'diff_0Z_0-14_last-Prev3_EPSCO_ws10mUS', 'diff_0Z_0-8_last-Prev4_EC_ws10mUS', 'diff_0Z_0-8_last-Prev1_EPSCO_ws10mUS', 'diff_0Z_0-14_last-Prev4_EPSCO_ws10mUS', 'diff_0Z_0-8_last-Prev1_EPS_ws10mUS', 'diff_0Z_0-13_last-Prev2D_EPSCO_ws10mUS', 'diff_12Z_0-13_last-Prev3D_EPS_ws10mUS', 'diff_0Z_0-14_last-Prev1_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev4_EPS_ws10mUS', 'diff_12Z_0-13_last-Prev2D_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev3_EPSCO_ws10mUS', 'diff_12Z_0-13_last-Prev1D_EPS_ws10mUS', 'diff_0Z_0-8_last-Prev4_EPSCO_ws10mUS', 'diff_0Z_0-2_last-Prev1_EC_ws10mUS-', 'diff_0Z_0-4_last-Prev2_EC_ws10mUS-', 'diff_12Z_0-14_last-Prev1_EPS_ws10mUS-', 'diff_12Z_0-14_last-Prev1_EPSCO_ws10mUS-'

                                 ]
                                    ,
                            'is_prod': True}, #
                'y_0800-1845_american06zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                     ['MEAN','diff_0Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS',
                                        'diff_0Z_0-16_last-Prev2_PARACO','diff_6Z_0-16_last-Prev3_PARACO',
                                        'diff_0Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev3_PARA',

                                    # 'diff_0Z_0-13_last-Prev3_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_eps0zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                     ['MEAN','diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                    # 'diff_0Z_0-13_last-Prev3_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_eps12zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                                    'diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_geps0zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_0Z_0-16_last-Prev1_GEPS','diff_0Z_0-16_last-Prev2_GEPS',
                                    'diff_0Z_0-16_last-Prev3_GEPS','diff_0Z_0-16_last-Prev4_GEPS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_geps12zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS',
                                    'diff_12Z_0-16_last-Prev3_GEPS','diff_12Z_0-16_last-Prev4_GEPS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_gefs0zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_0Z_0-16_last-Prev1_GEFS','diff_0Z_0-16_last-Prev2_GEFS',
                                    'diff_0Z_0-16_last-Prev3_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_gefs12zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_GEFS',
                                    'diff_12Z_0-16_last-Prev3_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_paraco0zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev2_PARACO',
                                    'diff_0Z_0-16_last-Prev3_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
                                 ],
                            'is_prod': True}, #
                'y_0800-1845_paraco12zScan': {'start': dtdt(2019,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     ['diff_12Z_0-16_last-Prev1_PARACO','diff_12Z_0-16_last-Prev2_PARACO',
                                    'diff_12Z_0-16_last-Prev3_PARACO','diff_12Z_0-16_last-Prev4_PARACO',
                                 ],
                            'is_prod': True}, #
                'y_1200-1400_EPSCO12z': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_0-15_last-Prev1_EPSCO_population_US', 'diff_12Z_7-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1315-1745_EPSCO12z': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_7-15_last-Prev2_EPSCO_population_US','diff_12Z_0-15_last-Prev1_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1315-1745_EPSCO12zStrict': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_7-15_last-Prev2_EPSCO_population_US','diff_12Z_0-15_last-Prev1_EPSCO_population_US'],
                                    'is_prod': True},
                'y_0800-1845_EPSCO0zFullTest': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                 'diff_0Z_7-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
                                 'diff_0Z_11-15_last-Prev1_EPSCO_population_US','diff_0Z_11-15_last-Prev2_EPSCO_population_US'
                                 ],
                                    'is_prod': True},
                'y_0800-1845_EPSCO12zFullTest': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                 'diff_12Z_7-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US',
                                 'diff_12Z_11-15_last-Prev1_EPSCO_population_US','diff_12Z_11-15_last-Prev2_EPSCO_population_US'
                                 ],
                                    'is_prod': True},
                'y_1200-1400_EPSCO12zStrict': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_0-15_last-Prev1_EPSCO_population_US', 'diff_12Z_7-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO12z11to15': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_11-15_last-Prev1_EPSCO_population_US', 'diff_12Z_11-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO12z11to15Strict': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_11-15_last-Prev1_EPSCO_population_US', 'diff_12Z_11-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO0z11to15': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.67,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_11-15_last-Prev1_EPSCO_population_US', 'diff_0Z_11-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO0z11to15Strict': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_11-15_last-Prev1_EPSCO_population_US', 'diff_0Z_11-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO0z': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-15_last-Prev1_EPSCO_population_US', 'diff_0Z_7-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO0zStrict': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-15_last-Prev1_EPSCO_population_US', 'diff_0Z_7-15_last-Prev2_EPSCO_population_US'],
                                    'is_prod': True},
                'y_1200-1400_EPSCO0zStrictNeg': {'start': dtdt(2022,2,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-15_last-Prev1_EPSCO_population_US-', 'diff_0Z_7-15_last-Prev2_EPSCO_population_US-'],
                                    'is_prod': True},
                'y_0000-0800_clustersAvg12Z': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.5]+[0.2]*5,
                                 'preds_for_comb': # cluster 48 x 4
                                    ['MEAN','diff_12Z_avg_d11to15_(0-6)h_cluster48','diff_12Z_avg_d11to15_(0-12)h_cluster48','diff_12Z_avg_d11to15_(0-18)h_cluster48','diff_12Z_avg_d11to15_(0-24)h_cluster48','diff_12Z_avg_d11to15_(0-36)h_cluster48'],
                                        'is_prod': True},
                'y_1100-1230_clustersAvg12Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.46,
                                 'preds_for_comb': # cluster 48 x 4
                                    ['diff_12Z_avg_d11to15_(0-18)h_cluster48'],
                                        'is_prod': True},
                'y_1100-1230_clustersBest0Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb': # cluster 48 x 4
                                    ['MEAN','diff_0Z_best_d7to15_(0-18)h_cluster48','diff_0Z_best_d0to15_(0-18)h_cluster48'],
                                        'is_prod': True},
                'y_0000-0800_clustersAvg18Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_18Z_avg_d11to15_(0-12)h_cluster48'],
                                            'is_prod': True},
                'y_0800-1845_clustersBest12Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['MEAN','diff_12Z_avg_d11to15_(0-48)h_cluster48','diff_12Z_avg_d0to15_(0-6)h_cluster48',
                                         'diff_18Z_avg_d11to15_(0-48)h_cluster48'],
                                            'is_prod': True},
                'y_0800-1845_clustersBest0Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['MEAN','diff_0Z_avg_d11to15_(0-36)h_cluster48','diff_0Z_avg_d11to15_(0-48)h_cluster48'],
                                            'is_prod': True},
                'y_0800-1845_clustersBest0Z6h': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_0Z_best_d0to15_(0-6)h_cluster48'],
                                            'is_prod': True},
                'y_0800-1845_clustersAvg18Z': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_18Z_avg_d11to15_(0-24)h_cluster48'],
                                        'is_prod': True},
                'y_1200-1945_clustersAvg6Z48h': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_6Z_avg_d11to15_(0-48)h_cluster48'],
                                        'is_prod': True},
                'y_1200-1945_clustersAvg6Z12h': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_6Z_best_d11to15_(0-12)h_cluster48'],
                                        'is_prod': True},
                'y_1200-1945_clustersAvg6Z18h0to15': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_6Z_best_d0to15_(0-18)h_cluster48'],
                                        'is_prod': True},
                'y_1200-1945_clustersAvg6Z24h0to15': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb': # cluster 48 x 4
                                        ['diff_6Z_best_d0to15_(0-24)h_cluster48'],
                                        'is_prod': True},
                'y_1100-1415_clustersBestVs2nd': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['Value_12Z_best_Vs_second_best_d0to15_cluster48'],
                                        'is_prod': True},
                'y_0800-1845_EPSsouth': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':['MEAN','diff_0Z_NG16_SouthCentral_weight_last-Prev1_EPS','diff_0Z_NG16_SouthCentral_weight_last-Prev2_EPS'
                                                    ],
                            'is_prod': True},
                'y_0800-1845_EPSsouthStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.25,0.05,0.01],
                                 'preds_for_comb':['MEAN','diff_0Z_NG16_SouthCentral_weight_last-Prev1_EPS','diff_0Z_NG16_SouthCentral_weight_last-Prev2_EPS'
                                                    ],
                            'is_prod': True},
                'y_0800-1315_EPSregionalComb': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.45]+[0.2]*6,
                                 'preds_for_comb':['MEAN',
                                                   'diff_0Z_NG16_SouthCentral_weight_Prev1-Prev2_EPS', 'diff_0Z_NG16_SouthCentral_weight_last-Prev2_EPS',
                                                   'diff_0Z_NG16_east_weight_Prev1-Prev2_EPS', 'diff_0Z_NG16_midwest_weight_Prev1-Prev2_EPS',
                                                   'diff_0Z_NG16_midwest_weight_last-Prev2_EPS', 'diff_0Z_population_US_weight_Prev1-Prev2_EPS'],
                            'is_prod': True},
                'y_0800-1845_GDDVs30d': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.44]+[0.2,0.2,0.2,0.1,0.1,0.01],
                                 'preds_for_comb':['MEAN',
                                                   'diff_0Z_0-0_0d-ma10D_PARA',
                                                   'diff_0Z_0-0_0d-ma20D_PARA','diff_0Z_0-0_0d-ma30D_PARA',
                                                   'diff_0Z_0-0_5d-ma10D_PARA',
                                                   'diff_0Z_0-0_5d-ma5D_PARA','diff_0Z_0-0_0d-ma5D_PARA',
                                                   ],
                            'is_prod': True},
                'y_0800-1415_WindVs30dTX': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.55]+[0.1]*2+[0.01]*3,
                                 'preds_for_comb':['MEAN','diff_0Z_0-0_0d-ma5D_PARA_ws10mTX-', 'diff_0Z_0-0_0d-ma10D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-0_0d-ma20D_PARA_ws10mTX-',
                                                   'diff_0Z_0-0_0d-ma30D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-0_5d-ma5D_PARA_ws10mTX-','diff_0Z_0-0_5d-ma10D_PARA_ws10mTX-', 'diff_0Z_0-0_5d-ma20D_PARA_ws10mTX-', 'diff_0Z_0-0_5d-ma30D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-8_0d-ma5D_PARA_ws10mTX-','diff_0Z_0-8_0d-ma10D_PARA_ws10mTX-', 'diff_0Z_0-8_0d-ma20D_PARA_ws10mTX-',
                                                   'diff_0Z_0-8_0d-ma30D_PARA_ws10mTX-','diff_0Z_0-16_0d-ma10D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-16_0d-ma5D_PARA_ws10mTX-','diff_0Z_0-16_0d-ma20D_PARA_ws10mTX-', 'diff_0Z_0-16_0d-ma30D_PARA_ws10mTX-',
                                                   ],
                            'is_prod': True},
                'y_0800-1415_WindVs30dTXStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.3]+[0.1]*2+[0.01]*3,
                                 'preds_for_comb':['MEAN','diff_0Z_0-0_0d-ma5D_PARA_ws10mTX-', 'diff_0Z_0-0_0d-ma10D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-0_0d-ma20D_PARA_ws10mTX-',
                                                   'diff_0Z_0-0_0d-ma30D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-0_5d-ma5D_PARA_ws10mTX-','diff_0Z_0-0_5d-ma10D_PARA_ws10mTX-', 'diff_0Z_0-0_5d-ma20D_PARA_ws10mTX-', 'diff_0Z_0-0_5d-ma30D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-8_0d-ma5D_PARA_ws10mTX-','diff_0Z_0-8_0d-ma10D_PARA_ws10mTX-', 'diff_0Z_0-8_0d-ma20D_PARA_ws10mTX-',
                                                   'diff_0Z_0-8_0d-ma30D_PARA_ws10mTX-','diff_0Z_0-16_0d-ma10D_PARA_ws10mTX-',
                                                   # 'diff_0Z_0-16_0d-ma5D_PARA_ws10mTX-','diff_0Z_0-16_0d-ma20D_PARA_ws10mTX-', 'diff_0Z_0-16_0d-ma30D_PARA_ws10mTX-',
                                                   ],
                            'is_prod': True},
                'y_0800-1500_CFSPrev1D14to21': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':['diff_18Z_14-21_Prev1D-Prev3D_CFS'],
                            'is_prod': True},
                'y_0800-1845_CFSCO12zPrev1D21to42': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':['diff_12Z_21-42_Prev1D-Prev3D_CFSCO'],
                            'is_prod': True},
                'y_0800-1500_CFSPrev1D14to21Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb':['diff_18Z_14-21_Prev1D-Prev3D_CFS'],
                            'is_prod': True},
                'y_1800-1830_tmpStrat$b': {'start': dtdt(2022,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                               [x+'_population_US' for x in ['diff_0Z_7-15_last-Prev1D_EPSCO','diff_0Z_7-15_last-Prev2D_EPSCO','diff_0Z_7-15_last-Prev3D_EPSCO','diff_0Z_7-15_last-Prev4D_EPSCO',
                                'diff_12Z_7-15_last-Prev1D_EPSCO','diff_12Z_7-15_last-Prev2D_EPSCO','diff_12Z_7-15_last-Prev3D_EPSCO','diff_12Z_7-15_last-Prev4D_EPSCO']
                                ],
                            'is_prod': True},
                'y_0800-1845_GEFSCFSCO14to21': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                                ['MEAN']+['diff_0Z_14-21_last-Prev2_GEFSCO35','diff_0Z_14-21_last-Prev4D_GEFS35','diff_12Z_14-21_last-Prev4D_CFSCO',
                                          'diff_0Z_0-21_last-Prev4_GEFSCO35'],
                            'is_prod': True},
                'y_0800-1845_GEFSCFSCO14to21Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                ['MEAN']+['diff_0Z_14-21_last-Prev2_GEFSCO35','diff_0Z_14-21_last-Prev4D_GEFS35','diff_12Z_14-21_last-Prev4D_CFSCO',
                                          'diff_0Z_0-21_last-Prev4_GEFSCO35'],
                            'is_prod': True},
                'y_0800-1845_0Dtrend': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.44,0.2,0.2,0.2,0.2,0.2],
                                 'preds_for_comb':
                                ['MEAN']+['diff_0Z_0-0_last-%sD'%i
                                  for i in [1,2,3,5,7]],
                            'is_prod': True},
                'y_1800-1830_12ZFullTest2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                                ['diff_12Z_0-16_last-Prev1_PARA','diff_12Z_0-16_last-Prev2_PARA',
                                 'diff_12Z_0-16_last-Prev3_PARA','diff_12Z_0-16_last-Prev4_PARA',
                                 'diff_12Z_0-16_last-Prev1_PARACO', 'diff_12Z_0-16_last-Prev2_PARACO',
                                 'diff_12Z_0-16_last-Prev3_PARACO', 'diff_12Z_0-16_last-Prev4_PARACO',
                                'diff_12Z_0-8_last-Prev1_GEFS','diff_12Z_0-8_last-Prev2_GEFS',
                                 'diff_12Z_0-8_last-Prev3_GEFS','diff_12Z_0-8_last-Prev4_GEFS'
                                 ],
                            'is_prod': True},
                'y_1800-1830_PARA12zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                    ['diff_12Z_0-16_last-Prev1_PARA','diff_12Z_0-16_last-Prev2_PARA','diff_12Z_0-16_last-Prev3_PARA','diff_12Z_0-16_last-Prev4_PARA',
                     'diff_12Z_8-16_last-Prev1_PARA','diff_12Z_8-16_last-Prev2_PARA','diff_12Z_8-16_last-Prev3_PARA','diff_12Z_8-16_last-Prev4_PARA',
                    # 'diff_12Z_0-8_last-Prev1_PARA','diff_12Z_0-8_last-Prev2_PARA','diff_12Z_0-8_last-Prev3_PARA','diff_12Z_0-8_last-Prev4_PARA'
                    ],
                            'is_prod': True},
                'y_1800-1830_GFSEFSCO6zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                    ['MEAN','diff_6Z_0-16_last-Prev1_PARA','diff_6Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev3_PARA','diff_6Z_0-16_last-Prev4_PARA',
                    'diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO','diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO',
                     'diff_6Z_0-16_last-Prev1_GEFS','diff_6Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',],
                            'is_prod': True},
                'y_1800-1830_PARA6zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                    ['MEAN','diff_6Z_0-16_last-Prev1_PARA','diff_6Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev3_PARA','diff_6Z_0-16_last-Prev4_PARA',
                     'diff_6Z_8-16_last-Prev1_PARA','diff_6Z_8-16_last-Prev2_PARA','diff_6Z_8-16_last-Prev3_PARA',
                     'diff_6Z_8-16_last-Prev4_PARA',
                     'diff_6Z_0-8_last-Prev1_PARA','diff_6Z_0-8_last-Prev2_PARA','diff_6Z_0-8_last-Prev3_PARA','diff_6Z_0-8_last-Prev4_PARA'
                    ],
                            'is_prod': True},
                'y_1800-1830_12zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                    ['MEAN',
                        'diff_12Z_0-16_last-Prev4_PARA','diff_6Z_0-16_last-Prev3_PARA',
                            'diff_0Z_0-16_last-Prev2_PARA','diff_18Z_0-16_last-Prev1_PARA',
                     #'diff_12Z_0-16_last-Prev4_PARACO',
                     #'diff_12Z_0-8_last-Prev4_PARA',
                    # 'diff_12Z_0-16_last-Prev2_PARA','diff_12Z_0-16_last-Prev3_PARA',
                    # 'diff_12Z_0-16_last-Prev2_PARACO','diff_12Z_0-16_last-Prev3_PARACO',
                    'diff_12Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev3_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                    #18z
                    'diff_18Z_0-16_last-Prev1_GEFS','diff_0Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS',
                    'diff_0Z_0-16_last-Prev4_GEFS',
                    # 'diff_18Z_0-16_last-Prev2_PARA','diff_18Z_0-16_last-Prev3_PARA','diff_18Z_0-16_last-Prev4_PARA',
                    # eps
                    # 'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS',
                    # 'diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                    # geps
                    # 'diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEPS',

                     ],
                            'is_prod': True},
                'y_1800-1830_GapsGEFS12zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                    ['MEAN',
                    'gap_0-16_PARAACO6z-GEFS0z', 'gap_0-16_PARACO6z-GEFS0z', 'gap_0-16_PARA6z-GEFS0z', 'gap_0-16_PARAACO6z-GEFS12z', 'gap_0-16_PARACO6z-GEFS12z', 'gap_0-16_PARA6z-GEFS12z', 'gap_0-16_PARAACO0z-GEFS12z', 'gap_0-16_PARACO0z-GEFS12z', 'gap_0-16_PARA0z-GEFS12z', 'gap_0-16_PARAACO18z-GEFS12z', 'gap_0-16_PARACO18z-GEFS12z', 'gap_0-16_PARA18z-GEFS12z',
                    'gap_0-13_EPS0z-GEFS12z','gap_0-13_EPS12z-GEFS12z','gap_0-16_GEPS0z-GEFS12z','gap_0-16_GEPS12z-GEFS12z'
                     ],
                            'is_prod': True},
                'y_0800-1745_GEFSFcst12z': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5]+[0.05]*15,
                                 'preds_for_comb':
                    ['MEAN',
                     'diff_12Z_8-16_Prev1-Prev2_GEPS','diff_12Z_0-13_last-Prev3_GEFS-',
                     # 'diff_12Z_8-16_last-Prev3_GEFS-','diff_12Z_0-8_last-Prev2_GEFS-',
                     'diff_12Z_0-16_last-Prev1_GEFS-','diff_12Z_0-16_Prev1D-Prev2D_CFSCO-',
                     'diff_12Z_0-2_Prev1-Prev3_GEFS-',
                     'diff_0Z_9-13_last-Prev2_EPS',
                     'diff_0Z_14-18_last-Prev1_CFSCO-',
                     'diff_6Z_0-13_last-Prev3_GEFS','diff_0Z_0-13_last-Prev2_GEFS',
                     'diff_0Z_0-8_last-Prev2_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                    'diff_0Z_0-8_last-Prev2_PARACO','diff_6Z_0-8_last-Prev3_GFSv16',
                     'diff_0Z_0-8_last-Prev4_PARACO','diff_6Z_0-2_last-Prev4_PARA',
                        ],
                            'is_prod': True},
                'y_0800-1745_GEFSFcst12zStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.3]+[0.0]*15,
                                 'preds_for_comb':
                    ['MEAN',
                     'diff_12Z_8-16_Prev1-Prev2_GEPS','diff_12Z_0-13_last-Prev3_GEFS-',
                     # 'diff_12Z_8-16_last-Prev3_GEFS-','diff_12Z_0-8_last-Prev2_GEFS-',
                     'diff_12Z_0-16_last-Prev1_GEFS-','diff_12Z_0-16_Prev1D-Prev2D_CFSCO-',
                     'diff_12Z_0-2_Prev1-Prev3_GEFS-',
                     'diff_0Z_9-13_last-Prev2_EPS',
                     'diff_0Z_14-18_last-Prev1_CFSCO-',
                     'diff_6Z_0-13_last-Prev3_GEFS','diff_0Z_0-13_last-Prev2_GEFS',
                     'diff_0Z_0-8_last-Prev2_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                    'diff_0Z_0-8_last-Prev2_PARACO','diff_6Z_0-8_last-Prev3_GFSv16',
                     'diff_0Z_0-8_last-Prev4_PARACO','diff_6Z_0-2_last-Prev4_PARA',
                        ],
                            'is_prod': True},
                'y_0800-1400_GEFSFcst12zAM': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5]+[0.0]*7,
                                 'preds_for_comb':
                    ['MEAN',
                     'diff_12Z_8-16_Prev1-Prev2_GEPS','diff_12Z_0-13_last-Prev3_GEFS-',
                     'diff_12Z_8-16_last-Prev3_GEFS-','diff_12Z_0-8_last-Prev2_GEFS-',
                     'diff_12Z_0-16_last-Prev1_GEFS-','diff_12Z_0-16_Prev1D-Prev2D_CFSCO-',
                     'diff_12Z_0-2_Prev1-Prev3_GEFS-',
                         ],
                            'is_prod': True},
                'y_0800-1845_GEFS35': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['diff_0Z_14-28_last-Prev1_GEFS35','diff_0Z_14-35_last-Prev1_GEFS35',
                     'diff_0Z_14-28_last-Prev2_GEFS35','diff_0Z_14-35_last-Prev2_GEFS35',
                     'diff_0Z_14-28_last-Prev3_GEFS35','diff_0Z_14-35_last-Prev3_GEFS35',
                     'diff_0Z_14-28_last-Prev4_GEFS35','diff_0Z_14-35_last-Prev4_GEFS35',
                     'diff_0Z_14-28_Prev1-Prev3_GEFS35','diff_0Z_14-35_Prev1-Prev3_GEFS35',
                     ],
                            'is_prod': True},
                'y_0800-1415_GEFS351D': {'start': dtdt(2022,4,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                    ['MEAN','diff_0Z_14-28_last-Prev1D_GEFS35','diff_0Z_21-28_last-Prev1D_GEFS35',
                     
                     ],
                            'is_prod': True},
                'y_0800-1415_GEFS351DStrict': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.25,0.25,0.1],
                                 'preds_for_comb':
                    ['MEAN','diff_0Z_14-28_last-Prev1D_GEFS35','diff_0Z_21-28_last-Prev1D_GEFS35',

                     ],
                            'is_prod': True},
                'y_1800-1830_GEFS6zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                    ['diff_6Z_0-16_last-Prev1_GEFS','diff_6Z_0-16_last-Prev2_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                     'diff_6Z_8-16_last-Prev1_GEFS','diff_6Z_8-16_last-Prev2_GEFS','diff_6Z_8-16_last-Prev3_GEFS','diff_6Z_8-16_last-Prev4_GEFS',
                     'diff_6Z_0-8_last-Prev1_GEFS','diff_6Z_0-8_last-Prev2_GEFS','diff_6Z_0-8_last-Prev3_GEFS','diff_6Z_0-8_last-Prev4_GEFS'],
                            'is_prod': True},
                'y_1800-1830_PARACO6zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO','diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO',
                     'diff_6Z_8-16_last-Prev1_PARACO','diff_6Z_8-16_last-Prev2_PARACO','diff_6Z_8-16_last-Prev3_PARACO','diff_6Z_8-16_last-Prev4_PARACO',
                     'diff_6Z_0-10_last-Prev1_PARACO','diff_6Z_0-10_last-Prev2_PARACO','diff_6Z_0-10_last-Prev3_PARACO','diff_6Z_0-10_last-Prev4_PARACO'],
                            'is_prod': True},
                'y_1800-1830_PARACO6zComb': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.44]+[0.15]*12,
                                 'preds_for_comb':
                    ['MEAN','diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO','diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO',
                     'diff_6Z_8-16_last-Prev1_PARACO','diff_6Z_8-16_last-Prev2_PARACO','diff_6Z_8-16_last-Prev3_PARACO','diff_6Z_8-16_last-Prev4_PARACO',
                     'diff_6Z_0-10_last-Prev1_PARACO','diff_6Z_0-10_last-Prev2_PARACO','diff_6Z_0-10_last-Prev3_PARACO','diff_6Z_0-10_last-Prev4_PARACO'],
                            'is_prod': True},
                'y_1800-1830_GEM0zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['diff_0Z_0-2_last-Prev1_GEM','diff_0Z_0-2_last-Prev2_GEM',
                     'diff_0Z_0-10_last-Prev1_GEM','diff_0Z_0-10_last-Prev2_GEM',
                        'diff_0Z_0-10_last-Prev3_GEM','diff_0Z_0-10_last-Prev4_GEM'],
                            'is_prod': True},
                'y_1800-1830_PARA0zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['diff_0Z_0-16_last-Prev1_PARA','diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev3_PARA','diff_0Z_0-16_last-Prev4_PARA',
                     'diff_0Z_8-16_last-Prev1_PARA','diff_0Z_8-16_last-Prev2_PARA','diff_0Z_8-16_last-Prev3_PARA','diff_0Z_8-16_last-Prev4_PARA',
                     'diff_0Z_0-8_last-Prev1_PARA','diff_0Z_0-8_last-Prev2_PARA','diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-8_last-Prev4_PARA'],
                            'is_prod': True},
                'y_1415-1715_PARA0zFullNeg': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['MEAN']+[x+'-' for x in ['diff_0Z_0-16_last-Prev1_PARA','diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev3_PARA','diff_0Z_0-16_last-Prev4_PARA',
                     'diff_0Z_8-16_last-Prev1_PARA','diff_0Z_8-16_last-Prev2_PARA','diff_0Z_8-16_last-Prev3_PARA','diff_0Z_8-16_last-Prev4_PARA',
                     'diff_0Z_0-8_last-Prev1_PARA','diff_0Z_0-8_last-Prev2_PARA','diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-8_last-Prev4_PARA']],
                            'is_prod': True},
                'y_1800-1830_GEFS0zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.44]+[0.05]*12,
                                 'preds_for_comb':
                    ['MEAN','diff_0Z_0-16_last-Prev1_GEFS','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev3_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                     'diff_0Z_8-16_last-Prev1_GEFS','diff_0Z_8-16_last-Prev2_GEFS','diff_0Z_8-16_last-Prev3_GEFS','diff_0Z_8-16_last-Prev4_GEFS',
                     'diff_0Z_0-8_last-Prev1_GEFS','diff_0Z_0-8_last-Prev2_GEFS','diff_0Z_0-8_last-Prev3_GEFS','diff_0Z_0-8_last-Prev4_GEFS'
                     ],
                            'is_prod': True},
                'y_1800-1830_GEFS18zFullTest': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['MEAN','diff_18Z_0-16_last-Prev1_GEFS','diff_18Z_0-16_last-Prev2_GEFS','diff_18Z_0-16_last-Prev3_GEFS','diff_18Z_0-16_last-Prev4_GEFS',
                     'diff_18Z_8-16_last-Prev1_GEFS','diff_18Z_8-16_last-Prev2_GEFS','diff_18Z_8-16_last-Prev3_GEFS','diff_18Z_8-16_last-Prev4_GEFS',
                     'diff_18Z_0-8_last-Prev1_GEFS','diff_18Z_0-8_last-Prev2_GEFS','diff_18Z_0-8_last-Prev3_GEFS','diff_18Z_0-8_last-Prev4_GEFS'
                     ],
                            'is_prod': True},
                'y_0000-0600_GEFS18zComb': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                    ['MEAN','diff_18Z_0-16_last-Prev4_GEFS',
                     'diff_18Z_0-16_last-Prev3_GEFS','diff_18Z_0-16_last-Prev1_GEFS','diff_18Z_0-16_last-Prev2_GEFS',
                     # 'diff_18Z_8-16_last-Prev1_GEFS','diff_18Z_8-16_last-Prev2_GEFS','diff_18Z_8-16_last-Prev3_GEFS','diff_18Z_8-16_last-Prev4_GEFS',
                     'diff_18Z_0-8_last-Prev4_GEFS','diff_18Z_0-8_last-Prev1_GEFS','diff_18Z_0-8_last-Prev2_GEFS','diff_18Z_0-8_last-Prev3_GEFS',
                     ],
                            'is_prod': True},
                'y_1800-1830_PARACO0zFullTest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                    ['diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev3_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
                     'diff_0Z_8-16_last-Prev1_PARACO','diff_0Z_8-16_last-Prev2_PARACO','diff_0Z_8-16_last-Prev3_PARACO','diff_0Z_8-16_last-Prev4_PARACO',
                     'diff_0Z_0-8_last-Prev1_PARACO','diff_0Z_0-8_last-Prev2_PARACO','diff_0Z_0-8_last-Prev3_PARACO','diff_0Z_0-8_last-Prev4_PARACO'],
                            'is_prod': True},


                'y_1200-1315_PARA6zws10mTX': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.3,
                                 'preds_for_comb':
                            ['MEAN','diff_6Z_0-16_last-Prev1_PARA_ws10mTX-',#'diff_6Z_0-16_last-Prev2_PARA_ws10mTX-',
                             'diff_6Z_0-16_last-Prev3_PARA_ws10mTX-'],#,'diff_6Z_0-16_last-Prev4_PARA_ws10mTX-',],
                            'is_prod': True},
                'y_1230-1300_NGFYestComb12d': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                            ['NGF_open-open-2_-1d','NGF_open-close-1_-1d',
                                'TTF_open-Prev1_d-1'],
                            'is_prod': True},
                'y_0800-1845_CFS1D0to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_0-16_last-Prev1D_CFS','diff_6Z_0-16_last-Prev1D_CFS'
                                 ],
                            'is_prod': True},
                'y_0800-1030_PARApre6Z': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                ['PARA_pre6Z_pred1'],
                            'is_prod': True},

                'y_0800-1845_CFSCombDaily': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5,0.45,0.45,0.45,
                                                                                   0.45,0.45],
                                 'preds_for_comb':
                            ['MEAN','diff_0Z_14-21_last-Prev4_CFS','diff_0Z_14-28_last-Prev4_CFS',
                             'CFS_PM_pred1','diff_0Z_14-21_last-Prev1_CFS',
                             #'diff_0Z_21-28_last-Prev4_CFS',
                             'diff_12Z_14-28_Prev1-Prev2_CFS'],
                                                 'is_prod': True},
                'y_0800-1845_CFSComb2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                            ['MEAN','diff_12Z_0-21_Prev1-Prev3_CFSCO','diff_12Z_14-21_last-Prev2_CFS'],
                                                 'is_prod': True},
                'y_1200-1745_GEFSCO35Prev1D': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                ['diff_0Z_0-21_Prev1D-Prev2D_GEFSCO35','diff_0Z_0-21_Prev1-Prev2_GEFSCO35'],
                                                 'is_prod': True},

                'y_1200-1745_GEFSCO35Prev1DNeg': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_0-21_Prev1D-Prev2D_GEFSCO35-','diff_0Z_0-21_Prev1-Prev2_GEFSCO35-'],
                                                 'is_prod': True},

                'y_1200-1745_GEFS6zfcst': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                ['GEFS_6Zb_pred_EnsTop5_window=100','GEFS_6Zb_pred_EnsTop6_window=20'],
                                                 'is_prod': True},

                'y_1100-1745_MonComb1021': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5,0.45,0.3,0.3,0.4],
                                 'preds_for_comb':
                                                ['MEAN','std_0-8_american_rs','diff_0Z_14-16_Prev1-Prev3_GEMCO', 'diff_0Z_14-16_last-Prev3_GEMCO',
                                                 'diff_6Z_0-8_last-Prev1_GFSv16'],
                                                 'is_prod': True},
                'y_1100-1745_MonComb1021Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.3,0.1,0.1,0.01,0.01],
                                 'preds_for_comb':
                                                ['MEAN','std_0-8_american_rs','diff_0Z_14-16_Prev1-Prev3_GEMCO', 'diff_0Z_14-16_last-Prev3_GEMCO',
                                                 'diff_6Z_0-8_last-Prev1_GFSv16'],
                                                 'is_prod': True},
                'y_1100-1745_std0to8': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                                ['std_0-8_american_rs'],
                                                 'is_prod': True},
                'y_1100-1745_GEMCO14to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                                ['MEAN','diff_0Z_14-16_Prev1-Prev3_GEMCO',
                                                 'diff_0Z_14-16_last-Prev3_GEMCO',
                                                 'diff_0Z_8-16_Prev1-Prev3_GEMCO',
                                                 'diff_0Z_8-16_Prev1-Prev2_GEMCO',
                                                'diff_12Z_8-16_last-Prev1_GEMCO',
                                                 'diff_12Z_8-16_last-Prev2_GEMCO',
                                                 'diff_12Z_14-16_last-Prev1_GEMCO',
                                                 'diff_12Z_14-16_last-Prev2_GEMCO',

                                                 ],
                                                 'is_prod': True},
                'y_0800-1845_GEMCO12zprev10to13': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5]+[0.45,0.4,0.4,0.4],
                                 'preds_for_comb':
                                                ['MEAN','diff_12Z_0-13_Prev1-Prev2_GEMCO','diff_12Z_0-13_Prev1-Prev3_GEMCO',
                                                 'diff_12Z_8-16_Prev1-Prev2_GEMCO','diff_12Z_0-16_Prev1-Prev3_GEMCO'],
                                                 'is_prod': True},
                'y_0400-0800_GEMCO12zprev10to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.22,
                                 'preds_for_comb':
                                                ['diff_12Z_0-16_Prev1-Prev3_GEMCO'],
                                                 'is_prod': True},
                'y_0400-0800_GEMCO12zprev10to16Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.15,
                                 'preds_for_comb':
                                                ['diff_12Z_0-16_Prev1-Prev3_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1845_GEMCO12zprev10to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                                ['diff_12Z_0-13_Prev1-Prev3_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1845_GEMCO12zprev10to16Strict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.42,
                                 'preds_for_comb':
                                                ['diff_12Z_0-13_Prev1-Prev3_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1415_GEMCO1D': {'start': dtdt(2021,10,1), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                                                ['diff_0Z_0-16_last-Prev1D_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1400_GEMCO2D': {'start': dtdt(2021,10,1), 'chosen_ratio': 0.42,
                                 'preds_for_comb':
                                                ['diff_0Z_0-16_last-Prev2D_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1400_GEMCO3D': {'start': dtdt(2021,10,1), 'chosen_ratio': 0.42,
                                 'preds_for_comb':
                                                ['diff_0Z_0-16_last-Prev3D_GEMCO'],
                                                 'is_prod': True},
                'y_0800-1845_GEPS12zprev10to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                                ['diff_12Z_0-16_Prev1-Prev2_GEPS'],
                                                 'is_prod': True},
                'y_1330-1415_GEPS12z14to16': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                                ['MEAN','diff_0Z_14-16_Prev1-Prev2_GEPS'],
                                                 'is_prod': True},
                'y_1100-1745_PARAws10mNeg': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['diff_0Z_0-10_Prev1-Prev3_PARA_ws10mTX'],
                                                 'is_prod': True},
                'y_1615-1645_GEFS6zws10mTXNeg': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['diff_6Z_11-15_last-Prev4_GEFS_ws10mTX'],
                                                 'is_prod': True},
                'y_0800-1845_WindPreds': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['diff_0Z_0-10_Prev1-Prev2_PARACO_ws10mTX', 'diff_0Z_0-10_Prev1-Prev3_PARA_ws10mTX', 'diff_0Z_0-10_last-Prev2_PARACO_ws10mTX', 'diff_0Z_0-10_last-Prev3_PARACO_ws10mTX', 'diff_0Z_0-16_Prev1-Prev2_PARACO_ws10mTX', 'diff_0Z_0-16_Prev1D-Prev2D_PARA_ws10mTX', 'diff_0Z_14-16_last-Prev1_GEFS_ws10mTX', 'diff_0Z_8-16_Prev1-Prev2_PARA_ws10mTX', 'diff_0Z_8-16_Prev1D-Prev2D_PARA_ws10mTX', 'diff_0Z_8-16_last-Prev2_PARACO_ws10mTX', 'diff_6Z_11-15_last-Prev2_GEFS_ws10mTX', 'diff_6Z_11-15_last-Prev4_GEFS_ws10mTX', 'diff_6Z_0-16_last-Prev3_PARACO_ws10mUS', 'diff_6Z_8-16_last-Prev3_PARACO_ws10mUS', 'diff_6Z_0-10_last-Prev3_PARACO_ws10mUS', 'diff_6Z_11-15_Prev1-Prev2_GEFS_ws10mUS', 'diff_6Z_0-16_last-Prev1D_GEFS_ws10mUS', 'diff_6Z_14-16_last-Prev1_PARA_ws10mUS', 'diff_6Z_0-16_last-Prev2_PARACO_ws10mUS', 'diff_6Z_0-16_Prev1-Prev3_PARACO_ws10mUS', 'diff_6Z_8-16_Prev1-Prev3_PARACO_ws10mUS', 'diff_0Z_8-16_last-Prev1D_GEFS_ws10mUS', 'diff_0Z_14-16_last-Prev1_GEFS_ws10mUS', 'diff_0Z_0-16_last-Prev1D_GEFS_ws10mUS', 'diff_0Z_8-16_last-Prev4D_GEFS_ws10mUS', 'diff_0Z_8-16_last-Prev3D_GEFS_ws10mUS'],
                                                 'is_prod': True},
                'y_1130-1315_paraco6zWind0to10p3': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.33,
                                 'preds_for_comb':
                                            ['diff_6Z_0-10_last-Prev3_PARACO_ws10mUS'],
                                                 'is_prod': True},
                'y_1715-1915_PARACO6z0to8WindTXNeg': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                            ['diff_6Z_0-8_last-Prev3_PARACO_ws10mTX'],
                                                 'is_prod': True},
                'y_1115-1215_PARACO6z0to8WindTX': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                            ['diff_6Z_0-8_last-Prev3_PARACO_ws10mTX-'],
                                                 'is_prod': True},
                'y_1130-1200_PARACO0z0to8WindTXNeg': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.55,0.45],
                                 'preds_for_comb':
                                            ['diff_0Z_0-8_last-Prev1_PARACO_ws10mTX','diff_0Z_0-8_last-Prev2_PARACO_ws10mTX'],
                                                 'is_prod': True},
                'y_0600-0900_PARA0z0to8WindTX': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                            ['diff_0Z_0-8_last-Prev2_PARA_ws10mTX-','MEAN',
                                            'diff_0Z_0-8_last-Prev3_PARA_ws10mTX-','diff_0Z_0-8_last-Prev4_PARA_ws10mTX-'],
                                                 'is_prod': True},
                'y_0600-0900_PARA0z0to16WindTX': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                            ['diff_0Z_0-16_last-Prev2_PARA_ws10mTX-','MEAN',
                                            'diff_0Z_0-16_last-Prev3_PARA_ws10mTX-','diff_0Z_0-16_last-Prev4_PARA_ws10mTX-'],
                                                 'is_prod': True},
                'y_0800-1845_CFSMComb': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['MEAN','Diff1_0Z_2M_CFSM_GDD_US','Diff1_0Z_1M_CFSM_GDD_US', 'Diff1_0Z_1to2M_CFSM_GDD_US',
                                     'Diff1_0Z_3M_CFSM_GDD_US', 'Diff1to3_0Z_1M_CFSM_GDD_US', 'Diff1to3_0Z_1to2M_CFSM_GDD_US',
                                     'Diff1to3_0Z_2M_CFSM_GDD_US', 'Diff1to3_0Z_3M_CFSM_GDD_US',
                                     'Diff2_0Z_1M_CFSM_GDD_US', 'Diff2_0Z_1to2M_CFSM_GDD_US',
                                     'Diff2_0Z_2M_CFSM_GDD_US', 'Diff2_0Z_3M_CFSM_GDD_US',
                                     ],
                                                 'is_prod': True},
                'y_0800-1400_CFSMCombWed': {'start': dtdt(2021,5,1), 'chosen_ratio': [0.5]+[0.1]*5,
                                 'preds_for_comb':
                                     ['MEAN','Diff1_0Z_3M_CFSM_GDD_US', 'Diff1to7_0Z_2M_CFSM_GDD_US',
                                      'Diff1to7_0Z_3M_CFSM_GDD_US', 'Diff2_0Z_3M_CFSM_GDD_US',
                                      'Diff3_0Z_2M_CFSM_GDD_US'],
                                                 'is_prod': True},
                'y_1715-1915_CFSMComb': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                ['Diff3_0Z_3M_CFSM_GDD_US'],
                                                 'is_prod': True},
                'y_0800-1100_CFSM12M': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                                ['Diff3_0Z_1M_CFSM_GDD_US','Diff1to7_0Z_2M_CFSM_GDD_US'],
                                                 'is_prod': True},
                'y_0800-1845_CFSM12M': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['Diff1_0Z_1to2M_CFSM_GDD_US'],
                                                 'is_prod': True},
                'y_0800-1845_CFSMCombPrev1D': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['Diff1_0Z_2M_CFSM_GDD_US-1d','Diff1_0Z_1M_CFSM_GDD_US-1d', 'Diff1_0Z_1to2M_CFSM_GDD_US-1d',
                                     'Diff1_0Z_3M_CFSM_GDD_US-1d', 'Diff1to3_0Z_1M_CFSM_GDD_US-1d', 'Diff1to3_0Z_1to2M_CFSM_GDD_US-1d',
                                     'Diff1to3_0Z_2M_CFSM_GDD_US-1d', 'Diff1to3_0Z_3M_CFSM_GDD_US-1d',
                                     'Diff2_0Z_1M_CFSM_GDD_US-1d', 'Diff2_0Z_1to2M_CFSM_GDD_US-1d',
                                     'Diff2_0Z_2M_CFSM_GDD_US-1d', 'Diff2_0Z_3M_CFSM_GDD_US-1d',
                                     ],
                                                 'is_prod': True},
                'y_0800-1845_CFSM3M':{
                                    'start': dtdt(2021,5,1), 'chosen_ratio': 0.5,
                                     'preds_for_comb':
                                        ['Diff1to3_0Z_3M_CFSM_GDD_US','Diff2_0Z_3M_CFSM_GDD_US','Diff1_0Z_3M_CFSM_GDD_US'],
                                                 'is_prod': True},
                'y_0800-1400_CFSM3Mv2':{
                                    'start': dtdt(2021,5,1), 'chosen_ratio': 0.4,
                                     'preds_for_comb':
                                        ['MEAN','Diff1_0Z_3M_CFSM_GDD_US',
                                     'Diff1to7_0Z_3M_CFSM_GDD_US',
                                     'Diff3_0Z_1to2M_CFSM_GDD_US',
                                     'Diff3_0Z_2M_CFSM_GDD_US',],
                                                 'is_prod': True},
                'y_1200-1745_WindTX': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_11-15_last-Prev2_GEFS_ws10mTX-', 'diff_6Z_0-13_last-Prev2_GEFS_ws10mTX-',
                                    'diff_6Z_0-0_last-Prev2_GEFS_ws10mTX-', 'diff_6Z_11-15_last-Prev4_GEFS_ws10mTX-',
                                     'diff_0Z_8-16_last-Prev2_PARACO_ws10mTX-'],
                                                 'is_prod': True},
                'y_1200-1430_WindTXPARACO0z': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                    ['diff_0Z_8-16_last-Prev2_PARACO_ws10mTX-'],
                                                 'is_prod': True},
                'y_1200-1430_WindTXPARACO0zStrict': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.22,
                                 'preds_for_comb':
                                    ['diff_0Z_8-16_last-Prev2_PARACO_ws10mTX-'],
                                                 'is_prod': True},
                'y_0800-1100_WindTX': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                     ['MEAN','diff_0Z_14-16_last-Prev1_GEFS_ws10mTX-', 'diff_0Z_14-14_last-Prev1_GEFS_ws10mTX-',
                                      'diff_0Z_0-10_last-Prev3_PARACO_ws10mTX-', 'diff_0Z_0-10_Prev1-Prev3_PARA_ws10mTX-'],
                                                 'is_prod': True},
                'y_1515-1845_WindTX': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-0_last-Prev1_PARACO_ws10mTX-', 'diff_0Z_0-0_last-Prev4_GEFS_ws10mTX-', 'diff_0Z_0-0_last-Prev2_GEFS_ws10mTX-', 'diff_0Z_0-0_last-Prev1_GEFS_ws10mTX-', 'diff_0Z_0-0_last-Prev4_PARACO_ws10mTX-', 'diff_0Z_0-0_last-Prev3_GEFS_ws10mTX-', 'diff_6Z_0-8_last-Prev1_GEFS_ws10mTX-',
                                        'diff_0Z_0-2_last-Prev4_PARACO_ws10mTX-'],
                                                 'is_prod': True},
                'y_1515-1845_WindTXv2': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-2_last-Prev4_PARACO_ws10mTX-','diff_6Z_0-8_last-Prev1_GEFS_ws10mTX-'],
                                                 'is_prod': True},
                'y_1515-1845_PARAPrev1DWind': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.4,0.3,0.1],
                                 'preds_for_comb':
                                ['diff_0Z_0-16_Prev1D-Prev2D_PARA_ws10mTX-','MEAN','diff_0Z_8-16_Prev1D-Prev2D_PARA_ws10mTX-'],
                                                 'is_prod': True},
                'y_0800-1845_PARAPrev1DWindNeg0to16': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                                ['diff_0Z_0-16_Prev1D-Prev2D_PARA_ws10mTX'],
                                                 'is_prod': True},
                'y_1300-1400_PARAPrev1DWindNeg': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['diff_0Z_8-16_Prev1D-Prev2D_PARA_ws10mTX'],
                                                 'is_prod': True},
                'y_1245-1745_GEFS6zWind': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['MEAN','diff_6Z_0-8_last-Prev1_GEFS_ws10mTX','diff_6Z_0-13_last-Prev2_GEFS_ws10mTX'],
                                                 'is_prod': True},
                'y_0800-1845_WindUS': {'start': dtdt(2021,5,1), 'chosen_ratio': [0.45]+[0.3]*14,
                                 'preds_for_comb':
                                     ['MEAN','diff_6Z_0-16_last-Prev3_PARACO_ws10mUS-', 'diff_6Z_8-16_last-Prev3_PARACO_ws10mUS-',
                                      'diff_6Z_0-13_last-Prev3_PARACO_ws10mUS-', 'diff_6Z_0-10_last-Prev3_PARACO_ws10mUS-',
                                      'diff_6Z_11-15_Prev1-Prev2_GEFS_ws10mUS-',
                                      'diff_6Z_14-16_last-Prev1_PARA_ws10mUS-','diff_0Z_14-16_last-Prev1_GEFS_ws10mUS-',
                                      'diff_6Z_0-8_last-Prev3_PARACO_ws10mUS-', 'diff_6Z_0-16_last-Prev2_PARACO_ws10mUS-',
                                      'diff_6Z_0-16_Prev1-Prev3_PARACO_ws10mUS-', 'diff_6Z_8-16_Prev1-Prev3_PARACO_ws10mUS-',
                                      'diff_0Z_0-13_last-Prev2_PARACO_ws10mUS-',
                                      'diff_6Z_0-16_last-Prev1D_GEFS_ws10mUS-','diff_0Z_8-16_last-Prev1D_GEFS_ws10mUS-',
                                      # 'diff_0Z_8-16_last-Prev4D_GEFS_ws10mUS-', 'diff_0Z_8-16_last-Prev3D_GEFS_ws10mUS-'
                                      ],
                                                 'is_prod': True},

                'y_1400-1530_Comb': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['MEAN',
                                 'diff_0Z_0-16_Prev1D-Prev2D_GFSv16','Value_0Z_0-0_EC_rs_ma_13','Value_12Z_0-16_GEFS-EPS(-1)_rs_diff1'
                                 ],
                                                 'is_prod': True},
                'y_1315-1745_CFSFSCOComb6Z0to16': {'start': dtdt(2020,11,1), 'chosen_ratio': [0.5]+[0.1]*8,
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_0-16_last-Prev1_CFS','diff_6Z_0-16_last-Prev2_CFS','diff_6Z_0-16_last-Prev3_CFS','diff_6Z_0-16_last-Prev4_CFS',
                                        'diff_6Z_0-16_last-Prev1_CFSCO','diff_6Z_0-16_last-Prev2_CFSCO','diff_6Z_0-16_last-Prev3_CFSCO','diff_6Z_0-16_last-Prev4_CFSCO',
                                        ],
                                                 'is_prod': True},
                'y_1330-1415_CFSCO6Zp18to16': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.6],
                                 'preds_for_comb':
                                    ['diff_6Z_8-16_last-Prev1_CFSCO'],
                                                 'is_prod': True},
                'y_0800-1845_CFSCOCombWed': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5,0.3,0.3,0.1,0.2],
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_28-42_last-Prev1_CFS', 'diff_6Z_14-18_last-Prev3_CFS', 'diff_0Z_14-28_Prev1-Prev2_CFSCO', 'diff_0Z_14-28_last-Prev2_CFSCO'],
                                                 'is_prod': True},
                'y_0800-1415_PARACOCombWed': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                ['MEAN','diff_18Z_0-13_last-Prev2_PARACO', 'diff_0Z_0-13_Prev1-Prev3_PARACO', 'diff_18Z_8-16_last-Prev1D_PARACO', 'diff_0Z_0-8_Prev1-Prev2_PARACO', 'diff_18Z_0-8_last-Prev1_PARACO', 'diff_6Z_0-8_last-Prev4_PARACO', 'diff_18Z_0-10_last-Prev1_PARACO', 'diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_18Z_0-8_last-Prev2_PARACO', 'diff_0Z_0-8_Prev1-Prev3_PARACO', 'diff_0Z_0-10_Prev1-Prev3_PARACO', 'diff_18Z_0-10_last-Prev2_PARACO'],
                                                 'is_prod': True},
                'y_0800-1415_GEFSCombWed': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                ['MEAN','diff_18Z_0-16_last-Prev3_GEFS', 'diff_0Z_8-16_Prev1-Prev3_GEFS', 'diff_18Z_8-16_last-Prev2_GEFS', 'diff_18Z_0-8_last-Prev2_GEFS', 'diff_0Z_0-8_Prev1-Prev3_GEFS', 'diff_18Z_0-16_last-Prev2_GEFS', 'diff_6Z_0-13_last-Prev4_GEFS', 'diff_18Z_0-13_last-Prev2_GEFS', 'diff_0Z_0-13_Prev1-Prev3_GEFS'],
                                                 'is_prod': True},
                'y_0800-1845_SeasonalCombWed': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5]+[0.1]*8,
                                 'preds_for_comb':
                                ['MEAN','Value_0Z_0-16D_PARA_rs', 'Value_6Z_14-16_GEFS_rs', 'Value_0Z_8-16D_PARACO_rs', 'Value_12Z_0-16_GEFS_rs', 'Value_12Z_0-16_PARACO_rs', 'Value_0Z_0-16D_PARACO_rs', 'Value_0Z_0-16D_GEFS_rs', 'Value_0Z_9-13_EPS_rs'],
                                                 'is_prod': True},
                'y_0800-1845_MTFCombStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.3,0.3,0.2,0.2,0.01,
                                                                                        0.01,0.01,0.01,0.01],
                                 'preds_for_comb':
                                    ['MTF_close-1-Prev1to7','MEAN','MTF_open-close-1',
                                     'MTF_open-Prev1', 'MTF_open-Prev2',
                                     'MTF_open-Prev1to7',
                                     'MTF_open-Prev1to3', 'MTF_close-1-Prev1to3','MTF_open-Prev1_d-1'],
                                         'is_prod': True},
                'y_0800-1845_MTFComb': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5,0.5,0.4,0.4,0.3,0.2,0.2,0.2,0.2],
                                 'preds_for_comb':
                                    ['MTF_close-1-Prev1to7','MEAN','MTF_open-close-1',
                                     'MTF_open-Prev1', 'MTF_open-Prev2',
                                     'MTF_open-Prev1to7',
                                     'MTF_open-Prev1to3', 'MTF_close-1-Prev1to3','MTF_open-Prev1_d-1'],
                                         'is_prod': True},
                'y_0800-1845_MTFbasic': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5],
                                 'preds_for_comb':
                                    ['MTF_close-1-Prev1to7'],
                                         'is_prod': True},
                'y_0900-1200_MTFopenPrev13': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.5],
                                 'preds_for_comb':
                                    ['MTF_open-Prev1to3'],
                                         'is_prod': True},
                'y_0400-0700_CFSCOPrev1D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                   ['MEAN','diff_0Z_21-42_Prev1D-Prev3D_CFSCO','diff_0Z_21-35_Prev1D-Prev3D_CFSCO',
                                      'diff_12Z_0-0_last-Prev2_CFSCO'],
                                         'is_prod': True},
                'y_1400-1530_CFSCOPrev12D': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.65]+[0.3]*4+[0.05]*3,
                                 'preds_for_comb':
                                ['MEAN','diff_6Z_21-35_Prev1D-Prev2D_CFSCO','diff_6Z_21-42_Prev1D-Prev2D_CFSCO','diff_0Z_14-35_last-Prev4_GEFS35',
                                    'diff_6Z_14-35_Prev1D-Prev2D_CFSCO', 'diff_0Z_21-28_Prev1D-Prev2D_GEFSCO35',
                                  'diff_0Z_28-42_Prev1D-Prev2D_CFSCO', 'diff_0Z_21-42_Prev1D-Prev2D_CFSCO'],
                                         'is_prod': True},
                'y_1400-1530_CFSCOPrev12DStrict': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.5]+[0.05]*4+[0.01]*3,
                                 'preds_for_comb':
                                ['MEAN','diff_6Z_21-35_Prev1D-Prev2D_CFSCO','diff_6Z_21-42_Prev1D-Prev2D_CFSCO','diff_0Z_14-35_last-Prev4_GEFS35',
                                    'diff_6Z_14-35_Prev1D-Prev2D_CFSCO', 'diff_0Z_21-28_Prev1D-Prev2D_GEFSCO35',
                                  'diff_0Z_28-42_Prev1D-Prev2D_CFSCO', 'diff_0Z_21-42_Prev1D-Prev2D_CFSCO'],
                                         'is_prod': True},
                'y_0000-0600_CFSCOPrev2D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                                ['diff_12Z_21-35_last-Prev2D_CFSCO'],
                                         'is_prod': True},
                'y_0800-1100_NGFopen': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['MEAN','NGF_open-close','NGF_open-open-1', 'NGF_open-open-2', 'NGF_open-close-1',
                                'NGF_open-close-1_-1d', 'NGF_open-open-2_-1d','NGF_open-open-1_-1d',
                                 'NGF_open-close-2'
                                # 'NGF_midday-open', 'NGF_midday-close-1', 'NGF_midday-open-1',
                                 # 'NGF_midday-close-2', 'NGF_midday-close-3', 'NGF_close-open', 'NGF_close-close-1',
                                 # 'NGF_close-open-1', 'NGF_close-close-2', 'NGF_close-close-3'
                                 ],
                                         'is_prod': True},
                'y_0800-1100_NGFopenVsclose': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['MEAN','NGF_open-close','NGF_open-open-1'],
                                         'is_prod': True},
                'y_0800-1100_NGFopenVscloseStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                ['MEAN','NGF_open-close','NGF_open-open-1'],
                                         'is_prod': True},
                'y_0800-1100_NGFopenloose': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['NGF_open-close'],
                                         'is_prod': True},
                'y_0800-1100_NGFopenStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.3,
                                 'preds_for_comb':
                                ['MEAN','NGF_open-close','NGF_open-open-1', 'NGF_open-open-2', 'NGF_open-close-1',
                                'NGF_open-close-1_-1d', 'NGF_open-open-2_-1d','NGF_open-open-1_-1d',
                                 'NGF_open-close-2'
                                # 'NGF_midday-open', 'NGF_midday-close-1', 'NGF_midday-open-1',
                                 # 'NGF_midday-close-2', 'NGF_midday-close-3', 'NGF_close-open', 'NGF_close-close-1',
                                 # 'NGF_close-open-1', 'NGF_close-close-2', 'NGF_close-close-3'
                                 ],
                                         'is_prod': True},
                'y_1100-1300_NGFmidday': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['NGF_midday-open'], #'NGF_midday-close','NGF_midday-close-1'],
                                         'is_prod': True},
                'y_1100-1300_NGFmiddayStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                    ['NGF_midday-open'], #'NGF_midday-close','NGF_midday-close-1'],
                                         'is_prod': True},
                'y_0800-1100_NGFclose': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['NGF_close-open','NGF_close-close-1'],
                                         'is_prod': True},
                 'y_0800-1845_WTIclose': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                                'preds_for_comb':
                                                    ['WTI_close-open'],
                                                'is_prod': True},
                'y_0800-1845_WTIcloseYest': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                                'preds_for_comb':
                                                    ['WTI_close-open-1d'],
                                                'is_prod': True},
                 'y_0800-1845_WTIopen': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                                'preds_for_comb':
                                                    ['WTI_open-open-1','WTI_open-close'],
                                                'is_prod': True},
                 'y_0800-1845_WTIopenStrict': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.3, 0.24],
                                               'preds_for_comb':
                                                   ['WTI_open-open-1', 'WTI_open-close'],
                                               'is_prod': True},

                 'y_0800-1845_NGFopen2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                                'preds_for_comb':
                                                    ['NGF_open-open-1','NGF_open-close'],
                                                'is_prod': True},
                 'y_0800-1845_NGFopenStrict2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.3, 0.24],
                                               'preds_for_comb':
                                                   ['NGF_open-open-1', 'NGF_open-close'],
                                               'is_prod': True},
                 'y_1500-1545_NGFopenStrict2Neg': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.3, 0.24],
                                               'preds_for_comb':
                                                   ['NGF_open-open-1-', 'NGF_open-close-'],
                                               'is_prod': True},
                 'y_0800-1845_NGFmidday2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                           'preds_for_comb':
                                        ['NGF_midday-open-1', 'NGF_midday-close-1', 'NGF_midday-open'],
                                           'is_prod': True},

                         'y_0800-1845_WTIopenYest': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                                'preds_for_comb':
                                                    ['WTI_open-open-1-1d','WTI_open-close-1d'],
                                                'is_prod': True},
                 'y_0800-1845_WTImidday': {'start': dtdt(2020, 9, 1), 'chosen_ratio': 0.5,
                                         'preds_for_comb':
                                             ['WTI_midday-open-1','WTI_midday-close-1','WTI_midday-open'],
                                         'is_prod': True},
                         'y_0800-1845_weeklyMomentumd0': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_0800-1415_d1-1w','y_0800-1845_d2-1w-','y_0800-1845_d2-2w'],
                                         'is_prod': True},
                'y_0800-1845_weeklyMomentumd1': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                        ['y_0800-1845_d1-3w','y_0800-1845_d4-2w','y_0800-1415_d1-3w'],
                                         'is_prod': True},
                'y_0800-1845_weeklyMomentumd2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                        ['y_0800-1845_d0-2w','y_0800-1415_d0-2w',
                                         'y_0800-1415_d4-3w','y_0800-1415_d1-0w-','y_0800-1415_d4-2w'],
                                         'is_prod': True},
                'y_0800-1845_weeklyMomentumd3': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_0800-1845_d1-0w-','MEAN','y_0800-1415_d4-1w','y_0800-1415_d1-0w-','y_0800-1415_d0-3w',
                                         'y_0800-1415_d2-0w'],
                                         'is_prod': True},
                'y_0800-1845_weeklyMomentumd4': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['y_0800-1845_d1-1w-','y_0800-1845_d0-1w','MEAN','y_0800-1845_d1-3w','y_0800-1415_d1-3w',
                                         'y_0800-1845_d4-3w'],
                                         'is_prod': True},
                'y_1000-1200_MonComb': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.55]+[0.01]*13,#16,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-10_Prev1D-Prev3D_GEMCO', 'diff_12Z_28-42_last-Prev4D_CFS', 'diff_6Z_0-2_Prev1-Prev2_GFSv16', 'diff_12Z_14-16_last-Prev2_GFSv16', 'diff_18Z_14-16_Prev1-Prev3_GFSv16', 'PARACO_AM_basic_pred1', 'diff_0Z_0-15_last-Prev3_PARACO', 'diff_12Z_14-16_last-Prev2_PARA', 'diff_0Z_8-16_last-Prev3_PARACO', 'diff_0Z_14-16_last-Prev3_PARACO', 'diff_18Z_8-16_last-Prev2_PARACO', 'diff_18Z_28-42_last-Prev4D_CFS', 'PARACO_AM_pred3'],
                                     'is_prod': True},
                'y_1000-1200_MonCombStrict': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.3]+[0.01]*13,#16,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-10_Prev1D-Prev3D_GEMCO', 'diff_12Z_28-42_last-Prev4D_CFS', 'diff_6Z_0-2_Prev1-Prev2_GFSv16', 'diff_12Z_14-16_last-Prev2_GFSv16', 'diff_18Z_14-16_Prev1-Prev3_GFSv16', 'PARACO_AM_basic_pred1', 'diff_0Z_0-15_last-Prev3_PARACO', 'diff_12Z_14-16_last-Prev2_PARA', 'diff_0Z_8-16_last-Prev3_PARACO', 'diff_0Z_14-16_last-Prev3_PARACO', 'diff_18Z_8-16_last-Prev2_PARACO', 'diff_18Z_28-42_last-Prev4D_CFS', 'PARACO_AM_pred3'],
                                     'is_prod': True},
                'y_1100-1745_ThuMomentumEIAWed': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    # wed 1200-1500?
                                    #['diff_0Z_21-28_Prev1-Prev2_CFSCO', 'diff_0Z_21-28_Prev1-Prev2_CFS', 'diff_0Z_0-2_last-Prev3_CFSCO', 'diff_0Z_0-21_Prev1-Prev2_CFSCO', 'diff_0Z_0-2_last-Prev4_CFS', 'diff_0Z_21-28_last-Prev2_CFS', 'diff_0Z_0-13_last-Prev2_CFSCO', 'diff_0Z_0-21_Prev1-Prev2_CFS', 'diff_0Z_14-28_Prev1-Prev2_CFS', 'diff_0Z_0-13_Prev1-Prev2_CFS', 'diff_0Z_14-28_last-Prev2_CFSCO', 'diff_0Z_14-28_Prev1-Prev2_CFSCO', 'diff_0Z_0-2_last-Prev1_CFSCO', 'diff_0Z_0-21_last-Prev2_CFS', 'diff_0Z_14-28_last-Prev2_CFS', 'CFS_AM_pred1'],
                                     ['y_1415-1500_-1d','y_1200-1515_-1d','y_1200-1500_-1d','y_1230-1330_-1d','y_1245-1415_-1d','y_1100-1415_-1d'],
                                         'is_prod': True},
                'y_1615-1845_GEM12zp20to2': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.5,0.3], # todo change icon to 0.5 after we have 6 months of it
                                 'preds_for_comb':
                                    ['diff_12Z_0-2_last-Prev1_GEM', 'diff_12Z_0-2_last-Prev2_ICON'],
                                         'is_prod': True},

                'y_1415-1545_PARACashComb6zp3': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['diff_6Z_0-2_last-Prev3_PARA','diff_6Z_0-0_last-Prev3_PARA'
                                     ],
                                         'is_prod': True},
                    'y_1545-1645_PARACashComb6zp3Negative': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_0-2_last-Prev3_PARA-','diff_6Z_0-0_last-Prev3_PARA-'
                                     ],
                                         'is_prod': True},
                    'y_1715-1915_GEFSCO3514to35p3': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                     [
                                    'diff_0Z_14-35_Prev1-Prev3_GEFSCO35','diff_0Z_14-35_last-Prev3_GEFSCO35',
                                    'MEAN'
                                    # 'diff_0Z_14-35_last-Prev3D_GEFSCO35',
                                    # 'diff_0Z_14-28_last-Prev3D_GEFSCO35',
                                    #   'diff_0Z_21-28_Prev1-Prev3_GEFSCO35',
                                    #   'diff_0Z_14-21_last-Prev3_GEFSCO35', 'diff_0Z_14-28_Prev1-Prev3_GEFSCO35',
                                      ],
                                         'is_prod': True},
                'y_1415-1845_GEFSCO3521to28p3': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                     [
                                       'diff_0Z_21-28_Prev1-Prev3_GEFSCO35',
                                      ],
                                         'is_prod': True},
                'y_1515-1745_GEFSCO3514to28Prev1D': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                     [
                                       'diff_0Z_14-28_Prev1D-Prev3D_GEFSCO35',
                                      ],
                                         'is_prod': True},
                'y_1515-1745_GEFSCO3514to21Prev1D': {'start': dtdt(2021,6,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                     [
                                       'diff_0Z_14-21_Prev1D-Prev3D_GEFSCO35',
                                      ],
                                         'is_prod': True},
                'y_0800-1100_CFS0zrolling4': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.5]+[0.1]*4,
                                 'preds_for_comb':
                                        ['MEAN','diff_0Z_0-16_last-Prev1234_CFS','diff_0Z_14-28_last-Prev1234_CFS',
                                         'diff_0Z_14-35_last-Prev1234_CFS','diff_0Z_0-21_last-Prev1234_CFS'],
                                         'is_prod': True},
                'y_1845-1945_CFS0z14to21p3': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['diff_0Z_14-21_last-Prev3_CFS'],
                                         'is_prod': True},
                'y_1845-1945_CFS0z14to21p3Strict': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                        ['diff_0Z_14-21_last-Prev3_CFS'],
                                         'is_prod': True},
                'y_1215-1315_CFS0z0to16rolling4': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['diff_0Z_0-16_last-Prev1234_CFS'],
                                         'is_prod': True},
                'y_1200-1315_CFS0z0to16p34': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['diff_0Z_0-16_last-Prev4_CFS','diff_0Z_0-16_last-Prev3_CFS',
                                         'MEAN'],
                                         'is_prod': True},
                'y_1200-1315_CFS0z0to16p4': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                 'preds_for_comb':
                        ['diff_0Z_0-16_last-Prev4_CFS'],
                         'is_prod': True},
                'y_1315-1715_CFS6z0to16p34': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.4,0.6,0.6,0.6,0.5,0.5,0.4],
                                 'preds_for_comb':
                                        ['diff_6Z_0-16_last-Prev2_CFS','diff_6Z_0-16_last-Prev3_CFS','diff_6Z_0-16_last-Prev4_CFS',
                                            'MEAN',
                                            'diff_6Z_0-21_last-Prev4_CFS','diff_6Z_0-21_last-Prev3_CFS',

                                        'diff_6Z_0-16_last-Prev1_CFS',
                                         ],
                                         'is_prod': True},
                'y_0800-1500_EPSComb': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5]*2+[0.5]*6+[0.5]*2,
                                 'preds_for_comb':
                                    ['diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS',
                                     'diff_0Z_0-13_last-Prev3_EPS','diff_0Z_0-13_last-Prev4_EPS',
                                     'diff_0Z_9-13_last-Prev1_EPS',
                                     'diff_0Z_9-13_last-Prev2_EPS','diff_0Z_0-8_last-Prev2_EPS',
                                     'diff_0Z_9-13_last-Prev3_EPS',
                                     'diff_0Z_0-13_last-Prev1D_EPS',
                                        'diff_0Z_0-13_last-Prev4D_EPS'
                                     ],
                                         'is_prod': True},
                'y_0800-1845_CFSCO0z14to28p1': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                    ['diff_0Z_14-28_last-Prev1_CFSCO'
                                      ],

                                         'is_prod': True},
                'y_0730-0800_CFSCO0z0to16p1': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                    ['diff_0Z_0-16_last-Prev1_CFSCO'
                                      ],

                                         'is_prod': True},
                'y_0730-0800_CFSCO0z0to16p24': {'start': dtdt(2021,5,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                    ['diff_0Z_0-16_last-Prev2_CFSCO',
                                      'diff_0Z_0-16_last-Prev4_CFSCO',
                                      ],

                                         'is_prod': True},
                'y_0800-1200_PARARACO0to8Comb': {'start': dtdt(2021,3,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['MEAN','diff_0Z_0-10_last-Prev1D_PARA','diff_0Z_0-8_last-Prev4_PARA',
                                            'diff_0Z_0-10_last-Prev1D_PARACO','diff_6Z_0-10_last-Prev1D_PARACO',
                                            'diff_12Z_0-10_last-Prev2_PARA','diff_12Z_0-8_last-Prev2_PARA',
                                            'diff_18Z_0-8_last-Prev3_PARA'
                                             ],
                                             'is_prod': True},
                'y_1815-1945_CFSCO14to21Comb': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.65,0.6,0.4,0.4],
                                 'preds_for_comb':
                                   ['diff_12Z_14-21_last-Prev3D_CFSCO','MEAN',
                                    'diff_0Z_14-21_Prev1D-Prev2D_CFSCO', 'diff_12Z_10-21_Prev1D-Prev3D_CFSCO',
                                    ],
                                         'is_prod': True},
                'y_1515-1645_CFSCO0Z14to281D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                   ['diff_0Z_14-28_last-Prev1D_CFSCO','diff_0Z_14-21_last-Prev1D_CFSCO'
                                    ],
                                         'is_prod': True},
                'y_0800-1845_CFS0Z14to28Yest': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                   ['diff_0Z_14-28_last-Prev3D_CFS-1d','diff_0Z_14-28_Prev1D-Prev3D_CFS-1d',
                                        'diff_0Z_14-28_Prev1D-Prev2D_CFS-1d',
                                    ],
                                         'is_prod': True},
                'y_0800-1845_CFS0Z14to284D': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                   ['diff_0Z_14-28_last-Prev4D_CFS'
                                    ],
                                         'is_prod': True},
                'y_0800-1845_CFS0Z14to28Prev1D': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                   ['diff_0Z_14-28_Prev1D-Prev3D_CFS'
                                    ],
                                         'is_prod': True},
                'y_1515-1645_CFSCO0Z14to2812D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                   ['diff_0Z_14-28_last-Prev1D_CFSCO',
                                    'diff_0Z_14-21_last-Prev1D_CFSCO'],
                                         'is_prod': True},
                'y_1845-1945_GEFSPrev1dp4': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['MEAN',
                                     'diff_0Z_0-13_last-Prev1_GEFS35',
                                     'diff_0Z_0-13_last-Prev4_GEFS-1d',
                                    'diff_0Z_0-13_last-Prev2_GEFS-1d'
                                    ],
                                         'is_prod': True},
                'y_1800-1900_CFSFSCO6Zcomb': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['MEAN',
                                    'diff_6Z_14-18_last-Prev2_CFS',
                                     'diff_6Z_14-18_last-Prev2_CFSCO','diff_12Z_21-35_last-Prev3D_CFSCO','diff_6Z_14-21_last-Prev2_CFSCO',
                                     'diff_6Z_28-35_last-Prev2_CFSCO', 'diff_6Z_14-18_last-Prev3_CFSCO'],
                                         'is_prod': True},
                'y_1300-1315_CFSCO6Z21to353D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_21-35_last-Prev3D_CFSCO','diff_6Z_28-42_last-Prev2D_CFSCO'],
                                         'is_prod': True},
                'y_0800-1415_TueComb': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                            ['MEAN','diff_12Z_0-8_Prev1-Prev3_GEMCO', 'diff_6Z_0-0_Prev1-Prev2_PARA','diff_0Z_0-4_Prev1-Prev3_EC', 'diff_12Z_0-4_last-Prev2_EC',
                             'diff_12Z_0-4_last-Prev3_EC', 'diff_12Z_0-8_last-Prev4_PARA', 'diff_12Z_0-8_last-Prev3_EPS',
                             'diff_18Z_0-10_last-Prev1D_PARACO', 'diff_12Z_0-8_last-Prev4_GFSv16',
                             'Value_0Z_9-13_EPS_rs', 'Value_12Z_0-16_PARACO_rs'],
                                         'is_prod': True},
                'y_0800-1400_CFSCOPrev1D0to16': {'start': dtdt(2021,4,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                        ['diff_12Z_0-16_Prev1D-Prev2D_CFSCO','diff_12Z_0-16_Prev1D-Prev2D_CFS','MEAN',
                                         'diff_12Z_0-16_Prev1D-Prev3D_CFSCO'],
                                        'is_prod': True},
                'y_1230-1315_GEFS12zp12': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                       ['MEAN','diff_12Z_0-16_last-Prev1_GEFS','diff_12Z_0-16_last-Prev2_GEFS',
                                         ],
                                        'is_prod': True},
                'y_0700-0800_EC0zbasic': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.4,0.5,0.2],
                                 'preds_for_comb':
                                        ['diff_0Z_0-8_last-Prev2_EC','diff_0Z_5-8_last-Prev1_EC','MEAN'],
                                        'is_prod': True},
                'y_1200-1230_EC0zbasicYest': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.4,0.5,0.2],
                                 'preds_for_comb':
                                        ['diff_0Z_0-8_last-Prev2_EC-1d','diff_0Z_5-8_last-Prev1_EC-1d','MEAN'],
                                        'is_prod': True},
                'y_1200-1230_EC0zbasicYestStrict': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.2,0.15,0.15],#[0.4,0.5,0.2],
                                 'preds_for_comb':
                                        ['diff_0Z_0-8_last-Prev2_EC-1d','diff_0Z_5-8_last-Prev1_EC-1d','MEAN'],
                                        'is_prod': True},
                'y_1100-1200_GEMCash0d': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_0-0_last-Prev4_GEM', 'diff_0Z_0-0_Prev1-Prev2_GEPS', 'diff_12Z_0-0_last-Prev1_GEPS'
                                    ],
                                        'is_prod': True},
                 'y_0800-1845_PARA0Zrolling2': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.5, 0.5, 0.5, 0.3],
                                          'preds_for_comb':
                                              ['MEAN', 'diff_18Z_0-16_last-Prev3_PARA',
                                               'diff_0Z_0-16_last-Prev4_PARA',
                                               'diff_0Z_0-16_last-Prev2_PARA',
                                               ],
                                                  'is_prod': True},
                 'y_0800-1845_PARA0Zrolling2Strict': {'start': dtdt(2020, 9, 1), 'chosen_ratio': [0.3, 0.25, 0.25, 0.2],
                                          'preds_for_comb':
                                              ['MEAN', 'diff_18Z_0-16_last-Prev3_PARA',
                                               'diff_0Z_0-16_last-Prev4_PARA',
                                               'diff_0Z_0-16_last-Prev2_PARA',
                                               ],
                                                  'is_prod': True},
                         'y_0800-1100_PARA1DComb': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_8-16_Prev1D-Prev2D_GFSv16','diff_12Z_8-16_last-Prev1D_PARA',
                                    'diff_12Z_0-16_last-Prev1D_PARA', 'diff_6Z_8-16_Prev1D-Prev2D_PARA',
                                 'diff_6Z_0-16_Prev1D-Prev2D_PARA'],
                                        'is_prod': True},
                'y_0800-1100_PARA1DCombStrict': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.2,
                                 'preds_for_comb':
                                ['MEAN','diff_0Z_8-16_Prev1D-Prev2D_GFSv16','diff_12Z_8-16_last-Prev1D_PARA',
                                    'diff_12Z_0-16_last-Prev1D_PARA', 'diff_6Z_8-16_Prev1D-Prev2D_PARA',
                                 'diff_6Z_0-16_Prev1D-Prev2D_PARA'],
                                        'is_prod': True},
                'y_1100-1200_MondaysMomentum': {'start': dtdt(2020, 5, 1), 'chosen_ratio': 0.5,
                                                     'preds_for_comb':
                                                         ['MEAN', 'y_1515-1615_d0ma3', 'y_0800-1745_d0ma3',
                                                          'y_1100-1200_d0ma3', 'y_1845-1945_d0ma3',
                                                          'y_1745-1845_d0ma3', 'y_1415-1515_d0ma3',
                                                          'y_1315-1415_d0ma3'],
                                                     'is_prod': True},
                'y_1415-1745_TuesdaysMomentum': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.3,0.5,0.5],
                                 'preds_for_comb':
                                ['MEAN','y_1745-1845_d1ma3', 'y_1845-1945_d1ma3'],
                                        'is_prod': True},
                 'y_0800-1415_ThursdaysMomentum': {'start': dtdt(2020, 5, 1),
                                                   'chosen_ratio': [0.5] * 1 + [0.2] * 4,
                                                   'preds_for_comb':
                                                       ['MEAN',
                                                        'y_1515-1615_d3ma3', 'y_1845-1945_d3ma3',
                                                        'y_1745-1845_d3ma3',
                                                        'y_1415-1515_d3ma3',
                                                        ],
                                                   'is_prod': True},
                 'y_0800-1415_ThursdaysMomentumStrict': {'start': dtdt(2020, 5, 1),
                                                   'chosen_ratio': [0.3] * 1 + [0.05] * 4,
                                                   'preds_for_comb':
                                                       ['MEAN',
                                                        'y_1515-1615_d3ma3', 'y_1845-1945_d3ma3',
                                                        'y_1745-1845_d3ma3',
                                                        'y_1415-1515_d3ma3',
                                                        ],
                                                   'is_prod': True},
                 'y_1200-1415_FridaysMomentum': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.5]*3+[0.3,0.5,0.5,0.5]+[0.25,0.25],
                         'preds_for_comb':
                            ['MEAN','y_1615-1745_d4ma3','y_0800-1745_d4ma3','y_1200-1300_d4ma3',
                              'y_1415-1515_d4ma3',
                         'y_0800-1315_d4ma3','y_0800-1100_d4ma3',
                           'y_1315-1415_d4ma3',
                            'y_1845-1945_d4ma3'],
                                'is_prod': True},
                'y_1100-1400_ECFcst12zb': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                        ['EC_12Zb_pred_EnsTop7_window=20',
                                         'EC_12Zb_pred_EnsTop6_window=50'
                                        ],
                                        'is_prod': True},
                'y_0800-1315_cashVsettle1d': {'start': dtdt(2021,2,15), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                            ['diff_cashVsettle-1d'],
                                   'is_prod': True},
                'y_1415-1845_cashVsettlediff': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['diff_cashVsettle_diff1'],
                                   'is_prod': True},
                'y_1415-1845_cashMomentum': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['price_cash_diff1','price_cash_diff2','price_cash_diff3',
                                             'price_cash_diff4','price_cash_diff5',
                                            'price_cash_diff1-1d','price_cash_diff2-1d','price_cash_diff3-1d',
                                             'price_cash_diff4-1d','price_cash_diff5-1d',
                                             ],
                                   'is_prod': True},
                'y_0800-1315_cashVsettle': {'start': dtdt(2021,2,15), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                            ['diff_cashVsettle'],
                                   'is_prod': True},
                'y_0630-0800_MorningComb': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.5,0.4,0.4,0.4,0.4,0.25],#[0.5,0.4,0.4,0.4,0.4,0.25],
                                 'preds_for_comb':
                                    ['MEAN','mix_0600-0700_pred1', 'diff_0Z_0-10_last-Prev1_GFSv16',
                                     'diff_0Z_8-16_last-Prev4_GEFS','diff_6Z_8-16_last-Prev1D_GEFS',
                                     'diff_6Z_8-16_last-Prev2D_PARACO'],

                                 'is_prod': True},
                'y_1200-1400_MixComb': {'start': dtdt(2020,5,1), 'chosen_ratio': [0.5,0.5],
                                 'preds_for_comb':
                                    ['mix_0600-0700_pred1','mix_0600-0700_pred2'],

                                 'is_prod': True},
                'y_1800-1830_fridaymomentum': {'start': dtdt(2020,5,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['MEAN','y_1100-1315_friday_-1d','y_1200-1300_friday_-1d',
                                     'y_1100-1400_monday_-1d'],

                                 'is_prod': True},
                'y_1100-1300_EPSSeasonalday15': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5,0.4],
                                 'preds_for_comb':
                                    ['Value_0Z_13-13_EPS_rs','Value_12Z_13-13_EPS_rs'],
                                 'is_prod': True},
                'y_1100-1300_EPSSeasonalday15Strict': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.35,0.3],
                                 'preds_for_comb':
                                    ['Value_0Z_13-13_EPS_rs','Value_12Z_13-13_EPS_rs'],
                                 'is_prod': True},
                'y_1100-1400_GEFS18Zday15': {'start': dtdt(2021,3,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['diff_18Z_14-16_last-Prev2_GEFS', 'diff_18Z_14-14_last-Prev2_GEFS', 'diff_18Z_11-15_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_1100-1315_GEFS18Zday15p3': {'start': dtdt(2021,3,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['diff_18Z_11-15_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_0800-1845_GEFS18Zday15p3': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['diff_18Z_11-15_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_1100-1400_GEFS18Zday15Strict': {'start': dtdt(2021,3,1), 'chosen_ratio': 0.3,
                                 'preds_for_comb':
                                    ['diff_18Z_14-16_last-Prev2_GEFS', 'diff_18Z_14-14_last-Prev2_GEFS', 'diff_18Z_11-15_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_1100-1400_GEFS18Zday15SuperStrict': {'start': dtdt(2021,3,1), 'chosen_ratio': 0.15,
                                 'preds_for_comb':
                                    ['diff_18Z_14-16_last-Prev2_GEFS', 'diff_18Z_14-14_last-Prev2_GEFS', 'diff_18Z_11-15_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_1100-1945_CFS14to28Comb': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5]*6+[0.6],
                                 'preds_for_comb':
                                    ['diff_12Z_14-28_last-Prev4_CFS', 'diff_18Z_14-21_last-Prev1D_CFS',
                                                     'diff_12Z_10-21_last-Prev2D_CFS', 'diff_12Z_14-18_last-Prev4_CFS',
                                                     'diff_12Z_0-21_last-Prev4_CFS', 'diff_12Z_14-21_last-Prev4_CFS']+['MEAN'],
                                 'is_prod': True},
                'y_1200-1315_ICONCash0dp2': {'start': dtdt(2021,4,15), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                ['diff_0Z_0-0_last-Prev2_ICON'],
                                 'is_prod': True},
                'y_1130-1300_ICONCash': {'start': dtdt(2021,4,15), 'chosen_ratio': 0.8,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_0-4_Prev1-Prev2_ICON','diff_0Z_0-4_last-Prev1_ICON', 'diff_0Z_0-2_last-Prev1_ICON', 'diff_0Z_0-2_last-Prev2_ICON', 'diff_0Z_0-2_last-Prev3_ICON'],
                                 'is_prod': True},
                'y_1130-1300_ICONCashStrict': {'start': dtdt(2021,4,15), 'chosen_ratio': [0.4]+[0.01]*5,
                                 'preds_for_comb':
                                ['MEAN','diff_12Z_0-4_Prev1-Prev2_ICON','diff_0Z_0-4_last-Prev1_ICON', 'diff_0Z_0-2_last-Prev1_ICON', 'diff_0Z_0-2_last-Prev2_ICON', 'diff_0Z_0-2_last-Prev3_ICON'],
                                 'is_prod': True},
                'y_0800-1845_ICON0zprev10to4': {'start': dtdt(2021,4,15), 'chosen_ratio': 0.48,
                                 'preds_for_comb':
                                ['diff_0Z_0-4_Prev1-Prev3_ICON'],
                                 'is_prod': True},
                'y_0400-0800_EIAeffect': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['y_1015-1045_EasternEIA_-1d'],
                                 'is_prod': True},
                'y_0400-0800_EIAeffectNegative': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['y_1015-1045_EasternEIA_-1d-'],
                                 'is_prod': True},
                'y_0400-0800_EIAeffectloose': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                 ['y_1015-1045_EasternEIA_-1d'],
                                 'is_prod': True},
                'y_1230-1315_GEFSCO18z0dCash': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.44]*2+[0.3,0.2],
                                 'preds_for_comb':
                                 ['MEAN','diff_18Z_0-0_last-Prev2_GEFS','diff_18Z_0-0_last-Prev2_PARACO',
                                  'diff_18Z_0-0_last-Prev1_GEFS'],
                                 'is_prod': True},
                'y_1745-1945_Cash0dGEFS': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['diff_12Z_0-0_Prev1-Prev2_GEFS','diff_18Z_0-0_Prev1-Prev3_GEFS'],
                                 'is_prod': True},
                'y_1745-1945_Cash0dGEFSv2': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                 ['diff_18Z_0-0_Prev1-Prev3_GEFS','diff_12Z_0-0_Prev1-Prev2_GEFS'],
                                 'is_prod': True},
                'y_1000-1300_GEFS12zComb': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5,0.5,0.5,0.45],
                                 'preds_for_comb':
                                    ['diff_18Z_0-8_last-Prev2_GEFS',
                                    'diff_12Z_0-8_last-Prev1_GEFS','MEAN',
                                     'diff_18Z_8-16_last-Prev3_GEFS'],
                                 'is_prod': True},
                'y_0800-1315_TTFStrict': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['TTF_open-Prev1','TTF_open-close-1', 'TTF_close-open'],
                                 'is_prod': True},
                'y_0800-1315_TTFopenBasic': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['TTF_open-Prev1'],
                                 'is_prod': True},
                'y_0800-1315_TTFStrictYest': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.5,0.45,0.45],
                                 'preds_for_comb':
                                    ['TTF_open-Prev1-1d','TTF_open-close-1-1d', 'TTF_close-open-1d'],
                                 'is_prod': True},
                'y_0800-1315_TTFclose': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['TTF_close-open'],
                                 'is_prod': True},
                'y_0800-1315_TTFcloseYest': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['TTF_close-open-1d'],
                                 'is_prod': True},
                'y_0800-1315_TTFcloseYestStrict': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                    ['TTF_close-open-1d'],
                                 'is_prod': True},
                'y_0800-1315_TTFcloseStrict': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.25,
                                 'preds_for_comb':
                                    ['TTF_close-open'],
                                 'is_prod': True},
                'y_0800-1315_TTFcloseNegative': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['TTF_close-open-'],
                                 'is_prod': True},
                'y_1645-1745_TTFclosevs1to3': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['TTF_close-1-Prev1to3'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['NCF_open-close'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose1d2': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                                    ['MEAN','NCF_open1100-close','NCF_open1100-open-1'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose1d': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['NCF_open1100-close-1','NCF_open1100-open-1'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose1d8am': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['NCF_open-close-1'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose2d1': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['NCF_open-close-2'],
                                 'is_prod': True},
                'y_0800-1845_COALopenclose2d': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['NCF_open1100-close-2'],
                                 'is_prod': True},
                'y_1100-1315_COALopen': {'start': dtdt(2021,3,1), 'chosen_ratio': [0.5,0.5,0.5,0.25],
                                 'preds_for_comb':
                                    ['MEAN','NCF_open-open-1','NCF_open-close-1','NCF_open-open-2'],
                                 'is_prod': True},
                'y_1430-1715_COALopenYestd': {'start': dtdt(2021,3,1), 'chosen_ratio': [0.5,0.5,0.5,0.25],
                                 'preds_for_comb':
                                    ['MEAN','NCF_open-open-1_-1d','NCF_open-close-1_-1d','NCF_open-open-2_-1d'],
                                 'is_prod': True},
                'y_0800-1315_momentumFri1d': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_1315-1745_-1d-'],
                                 'is_prod': True},
                'y_0800-1315_momentumWed1d': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_1200-1515_-1d-'],
                                 'is_prod': True},
                'y_1315-1415_momentumWed1d': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_1100-1415_-1d'],
                                 'is_prod': True},
                'y_1100-1515_momentumThu1d': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_1100-1415_-1d'],
                                 'is_prod': True},
                'y_1100-1515_momentumThu1dNegative': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['y_1100-1415_-1d-'],
                                 'is_prod': True},
                'y_1100-1515_momentumThFr': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.6,0.5,0.5,0.5],
                                 'preds_for_comb':
                                    ['MEAN','y_1200-1515_-2d','y_1000-1200_-1to3d','y_1000-1200_-1to5d'
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumThFrNegative': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.6,0.5,0.5,0.5],
                                 'preds_for_comb':
                                    ['MEAN','y_1200-1515_-2d-','y_1000-1200_-1to3d-','y_1000-1200_-1to5d-'
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumTuWe': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.6],
                                 'preds_for_comb':
                                    ['y_1000-1200_-2d'
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumTuWeStrict': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.3],
                                 'preds_for_comb':
                                    ['y_1000-1200_-2d'
                                    ],
                                 'is_prod': True},
                'y_1315-1745_momentumWed': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['y_1515-1745_-2d'
                                    ],
                                 'is_prod': True},
                'y_1315-1745_momentumWed2': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['y_1515-1745_-1to3d'
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumMon': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.55,0.55,0.1],
                                 'preds_for_comb':
                                    ['y_1200-1515_-1to3d','MEAN',
                                     'y_1000-1200_-1to3d',
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumMonNegative': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.55,0.55,0.1],
                                 'preds_for_comb':
                                    ['y_1200-1515_-1to3d-','MEAN',
                                     'y_1000-1200_-1to3d-',
                                    ],
                                 'is_prod': True},
                'y_1100-1515_momentumMonNegativeMEAN': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.55,0.1,0.1],
                                 'preds_for_comb':
                                    ['MEAN','y_1200-1515_-1to3d-',
                                     'y_1000-1200_-1to3d-',
                                    ],
                                 'is_prod': True},
                'y_1200-1515_momentumBollinger1Ht8': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.45,
                                 'preds_for_comb':
                                    ['macd_sign_1H_trend8'
                                    ],
                                 'is_prod': True},
                'y_1100-1745_momentumBollinger2Ht8': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['macd_sign_2H_trend8'
                                    ],
                                 'is_prod': True},
                'y_0900-1200_momentumBollinger4H': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['macd_sign_4H_diff2','macd_sign_4H_diff1'
                                    ],
                                 'is_prod': True},
                'y_1300-1400_momentumBollingerComb': {'start': dtdt(2020,7,1), 'chosen_ratio': [0.55,0.1,0.1,0.1],
                                 'preds_for_comb':
                                    ['MEAN','macd_sign_1H_trend8','macd_sign_2H_diff2','macd_sign_2H_trend4',
                                    ],
                                 'is_prod': True},
                'y_1100-1945_RSIS': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['RSIS-'
                                    ],
                                 'is_prod': True},
                'y_0800-1845_RelStrengthSell': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['RSISdiff1-'
                                    ],
                                 'is_prod': True},
                'y_0800-1845_RelStrengthSellloose': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['RSISdiff1-'
                                    ],
                                 'is_prod': True},
                'y_0800-1845_RelStrengthSelllooseNeg': {'start': dtdt(2020,8,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['RSISdiff1'
                                    ],
                                 'is_prod': True},
                'y_1415-1545_Comb': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                     ['diff_12Z_14-21_last-Prev1_CFS','diff_6Z_0-16_last-Prev2D_PARA',
                                         'diff_6Z_0-2_last-Prev2_PARA','GEPS_PM_pred2D'],
                                     'is_prod': True},
                'y_1145-1230_CFSCOcomb': {'start': dtdt(2020,5,15), 'chosen_ratio': [0.55]+[0.05]*12,
                                 'preds_for_comb':
                                    ['MEAN','diff_0Z_0-2_last-Prev3_CFS', 'diff_0Z_0-2_Prev1-Prev3_CFSCO',
                                     'diff_0Z_14-21_Prev1-Prev3_CFS', 'diff_18Z_14-21_last-Prev2_CFS', 'diff_0Z_14-18_Prev1-Prev3_CFS',
                                     'diff_18Z_14-18_last-Prev2_CFS', 'diff_12Z_21-42_last-Prev2D_CFSCO', 'diff_12Z_14-28_Prev1D-Prev3D_CFSCO',
                                     'diff_6Z_14-28_Prev1D-Prev2D_CFSCO', 'diff_12Z_14-35_last-Prev4D_CFSCO',
                                     'diff_12Z_14-28_last-Prev4D_CFSCO', 'diff_0Z_14-21_Prev1D-Prev2D_CFSCO'],
                                 'is_prod': True},
                'y_1100-1400_CFSCOcomb21to42': {'start': dtdt(2021,4,15), 'chosen_ratio': [0.65,0.65,0.4],
                                 'preds_for_comb':
                                    ['MEAN','diff_0Z_21-35_last-Prev4D_CFSCO','diff_0Z_21-35_last-Prev1D_CFSCO'],
                                 'is_prod': True},
                'y_1615-1645_CFSCO12Z4D21to35': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['diff_12Z_21-35_last-Prev4D_CFSCO'],
                                 'is_prod': True},
                'y_1400-1500_CFS12Z4D21to35': {'start': dtdt(2020,9,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['diff_12Z_21-35_last-Prev4D_CFS'],
                                 'is_prod': True},
                'y_1200-1745_CFSCOp234': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                            ['diff_0Z_14-28_last-Prev4_CFSCO','diff_0Z_14-28_last-Prev2_CFSCO','diff_0Z_28-42_last-Prev3_CFSCO','diff_0Z_28-42_last-Prev2_CFSCO'],
                                 'is_prod': True},
                'y_1715-1915_CFSCO0z21to28': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                            ['diff_0Z_21-28_last-Prev2_CFSCO',
                                             'diff_6Z_21-28_Prev1-Prev3_CFSCO'],
                                 'is_prod': True},
                'y_0800-1845_CFSCO14to28p2': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                            ['diff_0Z_14-28_last-Prev2_CFSCO'],
                                 'is_prod': True},
                'y_1100-1415_CFSCO21to351D': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.4,
                                 'preds_for_comb':
                                    ['diff_0Z_21-35_last-Prev1D_CFSCO'],
                                 'is_prod': True},
                'y_1300-1315_CFSFSCO21to351Dcomb': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.5]*2+[0.35]*2+[0.1]*2,
                                 'preds_for_comb':
                                    ['MEAN','diff_0Z_28-42_last-Prev1D_CFSCO','diff_0Z_21-35_last-Prev1D_CFSCO','diff_0Z_21-42_last-Prev1D_CFSCO',
                                     'diff_0Z_28-35_last-Prev3_CFSCO','diff_0Z_28-35_last-Prev3_CFS'],
                                 'is_prod': True},
                'y_0800-1415_CFSCO28to35p3': {'start': dtdt(2022,4,1), 'chosen_ratio': [0.44,0.4,0.2],
                                 'preds_for_comb':
                                    ['MEAN','diff_0Z_28-35_last-Prev3_CFSCO','diff_0Z_28-35_Prev1-Prev3_CFSCO'],
                                 'is_prod': True},
                'y_1915-2030_CFSCOcomb28to42': {'start': dtdt(2021,4,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_28-42_last-Prev2D_CFSCO','diff_0Z_28-42_last-Prev1D_CFSCO','diff_6Z_28-42_last-Prev2D_CFSCO'],
                                 'is_prod': True},
                'y_1300-1545_CFS6ZPrev1D28to42': {'start': dtdt(2020,7,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                    ['diff_6Z_28-42_Prev1D-Prev2D_CFS'],
                                 'is_prod': True},
                'y_0800-1845_CFSFSCOcomb28to4214D': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.65,0.8],
                                 'preds_for_comb':
                                    ['diff_0Z_21-42_last-Prev4D_CFSCO','diff_0Z_28-42_last-Prev4D_CFSCO'],
                                 'is_prod': True},
                'y_0800-1845_CFSEFSCOcomb14to42': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.55,
                                 'preds_for_comb':
                                ['diff_0Z_14-28_last-Prev4D_CFSCO','diff_12Z_28-42_last-Prev4D_CFS','diff_0Z_14-28_last-Prev1D_GEFS35-1d'],
                                 'is_prod': True},
                'y_0800-1845_CFSFSCOcomb28to4214DStrict': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.35,0.45],
                                 'preds_for_comb':
                                    ['diff_0Z_21-42_last-Prev4D_CFSCO','diff_0Z_28-42_last-Prev4D_CFSCO'],
                                 'is_prod': True},
                'y_0800-1845_CFSFSCO6zcomb28to424D': {'start': dtdt(2021,1,1), 'chosen_ratio': [0.55,0.55,0.5],
                                 'preds_for_comb':
                                    ['MEAN','diff_6Z_21-42_last-Prev4D_CFS','diff_6Z_28-42_last-Prev4D_CFSCO'],
                                 'is_prod': True},
                'y_1915-2030_GEFSCFSCOcomb': {'start': dtdt(2021,4,1), 'chosen_ratio': [0.5,0.5,0.4,0.4],
                                 'preds_for_comb':
                                    ['MEAN','diff_0Z_14-28_last-Prev2_GEFS35','diff_6Z_28-42_last-Prev2D_CFS','diff_0Z_28-42_last-Prev1D_CFSCO'],
                                 'is_prod': True},
                'y_1815-1915_CFSCO12zPrev1D21to42': {'start': dtdt(2021,4,15), 'chosen_ratio': 0.65,
                                 'preds_for_comb':
                                    ['diff_12Z_21-35_Prev1D-Prev2D_CFSCO','diff_12Z_21-42_Prev1D-Prev2D_CFSCO'],
                                 'is_prod': True},
                'y_1515-1645_CFSCO12zp24': {'start': dtdt(2021,4,15), 'chosen_ratio': [0.65,0.5],
                                 'preds_for_comb':
                                    ['diff_12Z_14-28_last-Prev4_CFSCO','diff_12Z_14-28_last-Prev2_CFSCO'],
                                 'is_prod': True},
                'y_1430-1500_CFSCO12zp1': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['diff_12Z_0-16_last-Prev1_CFSCO'],
                                 'is_prod': True},
                'y_1100-1945_CFSCO6z0to16Yest': {'start': dtdt(2021,1,1), 'chosen_ratio': 0.6,
                                 'preds_for_comb':
                                    ['MEAN','diff_12Z_0-16_Prev1-Prev2_CFSCO',
                                     'diff_12Z_8-16_Prev1-Prev2_CFSCO'],
                                 'is_prod': True},
                'y_1945-2030_CFSCO18z14to28': {'start': dtdt(2021,4,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb':
                                    ['diff_0Z_14-28_Prev1-Prev2_CFSCO','diff_0Z_14-28_Prev1-Prev3_CFSCO'],
                                 'is_prod': True},
                'y_1945-2345_d3': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.45, #[0.6,0.5],
                                 'preds_for_comb': ['diff_0Z_8-16_Prev1-Prev3_GEMCO','diff_12Z_5-8_last-Prev2D_EC',
                                                    'diff_12Z_0-8_last-Prev1D_EC','MEAN',
                                                    'diff_12Z_0-8_last-Prev1_EPS','diff_0Z_8-16_last-Prev1D_GEMCO',],
                                 'is_prod': True},
                'y_1815-1915_PARACO12zPrev1D': {'start': dtdt(2020,9,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_0-13_Prev1-Prev2_PARACO',
                                                    'diff_12Z_0-8_Prev1-Prev2_PARACO'
                                                    ],
                                 'is_prod': True},
                'y_1130-1200_PARACO12z23DComb': {'start': dtdt(2020,9,15), 'chosen_ratio': [0.55,0.5,0.5,0.3],
                                 'preds_for_comb': ['MEAN','diff_12Z_0-16_last-Prev2D_PARACO',
                                                    'diff_12Z_0-16_Prev1D-Prev2D_PARACO','diff_12Z_8-16_last-Prev3D_PARACO',
                                                    ],
                                 'is_prod': True},
                'y_0800-0930_PARACO12zPrev1D8to16': {'start': dtdt(2020,9,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_12Z_0-16_Prev1D-Prev2D_PARACO'
                                                    ],
                                 'is_prod': True},
                'y_0800-1945_PARACO0zPrev1D0to16': {'start': dtdt(2020,9,15), 'chosen_ratio': 0.5,
                                 'preds_for_comb': ['diff_0Z_0-16_Prev1D-Prev2D_PARACO'
                                                    ],
                                 'is_prod': True},
                'y_1130-1200_PARA12z13DComb': {'start': dtdt(2020,9,15), 'chosen_ratio': [0.55,0.5,0.5,0.5],
                                 'preds_for_comb': ['MEAN','diff_12Z_8-16_last-Prev1D_PARA',
                                                    'diff_12Z_8-16_Prev1D-Prev2D_PARACO','diff_12Z_0-16_last-Prev3D_PARACO',
                                                    ],
                                 'is_prod': True},
                'y_1130-1200_PARA6z4D': {'start': dtdt(2020,9,15), 'chosen_ratio': 0.55,
                                 'preds_for_comb': ['diff_6Z_0-16_last-Prev4D_PARA'
                                                    ],
                                 'is_prod': True},
                'y_0800-1300_PARACO12zPrev1p2': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.4,
                                 'preds_for_comb': ['diff_12Z_0-15_Prev1-Prev3_PARACO',
                                                    ],
                                 'is_prod': True},
                'y_0800-1000_PARACO12zPrev1': {'start': dtdt(2020,5,15), 'chosen_ratio': 0.5,
                         'preds_for_comb': ['diff_12Z_0-10_Prev1-Prev3_PARACO'
                                            ],
                         'is_prod': True},
                'y_1115-1215_PARACO6z0to10': {'start': dtdt(2021,2,1), 'chosen_ratio': [0.5,0.4,0.4,0.35],
                                                        # can change to:  dtdt(2020,7,1), 'chosen_ratio': [0.45,0.35,0.35,0.35],
                                 'preds_for_comb': ['MEAN','diff_6Z_0-10_last-Prev4_PARACO',
                                                    'diff_6Z_0-10_last-Prev2_PARACO','diff_6Z_0-10_last-Prev3_PARACO'
                                                    ],
                                     'is_prod': True},

                'y_1130-1200_PARACO6z0to2prev1': {'start': dtdt(2020,9,1), 'chosen_ratio': [0.55,0.3,0.2],
                                 'preds_for_comb': ['diff_6Z_0-2_Prev1-Prev2_PARACO','MEAN',
                                                    'diff_6Z_0-8_Prev1-Prev2_PARACO'
                                                    ],
                                     'is_prod': True},

                'y_1745-1945_amerimix23D': {'start': dtdt(2020,4,1), 'chosen_ratio': [0.5,0.4,0.4,0.4],
                                 'preds_for_comb': ['MEAN','diff_12Z_0-16_Prev1D-Prev2D_GFSv16',
                                                    'diff_18Z_0-16_last-Prev2D_CFS',
                                                    'diff_6Z_0-16_last-Prev3D_GEFS',
                                                   ],
                                 'is_prod': True},
                            'y_1100-1315_amerimix23D': {'start': dtdt(2020,4,1), 'chosen_ratio': [0.5,0.4,0.4,0.4],
                                 'preds_for_comb': ['MEAN','diff_12Z_0-16_Prev1D-Prev2D_GFSv16',
                                                    'diff_18Z_0-16_last-Prev2D_CFS',
                                                    'diff_6Z_0-16_last-Prev3D_GEFS',
                                                   ],
                                 'is_prod': True},
                'y_1200-1400_TueComb': {'start': dtdt(2020,3,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb': ['diff_6Z_0-10_last-Prev4D_GFSv16','MEAN', 'diff_0Z_8-13_last-Prev1D_EPS', 'diff_6Z_0-16_last-Prev4D_GFSv16',
                                                    'diff_0Z_9-13_Prev1-Prev3_EPS'],
                                 'is_prod': True},
            'y_1200-1245_monMean': {'start': march_start, 'chosen_ratio': [0.5, 0.1,0.1],  # GDD from April!
                            'preds_for_comb': ['MEAN','Cash_pred2', 'diff_0Z_0-16_last-Prev1D_PARA'],
                            'is_prod': True},
                'y_1245-1315_mon': {'start': march_start, 'chosen_ratio': 0.4,  # GDD from April!
                            'preds_for_comb': ['GEPS_daily_hybrid_pred2','GEFS_6Z_pred1','PARA_AM_basic_pred1',
                                               'diff_0Z_0-10_last-Prev3D_GEM','diff_0Z_14-21_last-Prev1D_CFS'],
                            'is_prod': True},
                'y_1315-1615_mon': {'start': april_start, 'chosen_ratio': [0.4,0.5,0.5,0.6],
                            'preds_for_comb': ['diff_0Z_28-42_last-Prev3D_CFS','PARACO_PM_pred1','diff_0Z_28-42_last-Prev2D_CFS','MEAN'],
                            'is_prod': True},
                'y_1315-1645_34c': {'start': march_start, 'chosen_ratio': 0.5,  # GDD from April!
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev2D_GEFS','PARACO_daily_hybrid_pred2'],
                            'is_prod': True},
                'y_0800-1400_GEFS2D': {'start': march_start, 'chosen_ratio': 0.42,  # GDD from April!
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev2D_GEFS'],
                            'is_prod': True},
                'y_0800-1400_GEFSPrev1D': {'start': march_start, 'chosen_ratio': 0.42,  # GDD from April!
                            'preds_for_comb': ['diff_0Z_0-16_Prev1D-Prev2D_GEFS'],
                            'is_prod': True},
                'y_1000-1100_04c': {'start': march_start, 'chosen_ratio': [0.6,0.3],  # GDD from April!
                            'preds_for_comb': ['GEFS_pre6Z_pred2', 'PARA_pre6Z_pred2'],
                            'is_prod': True},

                'y_1645-1745_134c': {'start': march_start, 'chosen_ratio': [0.7,0.5],  # GDD from April!
                            'preds_for_comb': ['diff_0Z_0-16_last-Prev1D_PARACO', 'diff_6Z_8-16_last-Prev3D_PARACO'],
                        'is_prod': True},
        'y_2145-2345_6c': {'start': march_start, 'chosen_ratio': [0.6,0.6, 0.5,0.5],  # GDD from April!
                             'preds_for_comb': ['MEAN','diff_0Z_8-16_last-Prev2D_PARA','diff_12Z_0-13_last-Prev4_EPS',
                                                'diff_0Z_0-16_last-Prev2D_GEFSL'],
                             'is_prod': True},
        'y_1400-1500_wed': {'start': march_start, 'chosen_ratio': 0.5,
                           'preds_for_comb': ['diff_0Z_8-16_last-Prev1D_GEFS','diff_6Z_0-16_last-Prev3D_GEFS',
                                                #'diff_6Z_0-16_last-Prev1D_PARACO', 'diff_6Z_0-16_last-Prev1D_PARA',
                                              ], # 'diff_0Z_8-16_last-Prev2D_GEFSL'
                           'is_prod': True},
    }
