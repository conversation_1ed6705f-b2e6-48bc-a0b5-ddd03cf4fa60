from Algo.Utils.files_handle import get_clusters_json_path, get_chosen_strats_json, get_dynamic_clusters_json
from Algo.Utils.general import format_cluster_json_back


import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import copy
import json

from Algo.Trading.constants import TZ_DELTA_FROM_TLV_TIME

CHOSEN_STRATS_JSON = json.load(open(get_chosen_strats_json()))
DYNAMIC_CLUSTERS_JSON = json.load(open(get_dynamic_clusters_json()))




last_sunday = dtdt.today()-td(hours=12+TZ_DELTA_FROM_TLV_TIME)
while last_sunday.weekday() != 6:
    last_sunday -= td(days=1)

prev_sunday_str = (last_sunday - td(days=7)).strftime('%Y%m%d')
last_sunday_str = last_sunday.strftime('%Y%m%d')


try:
    CURRENT_WEEK_JSON = CHOSEN_STRATS_JSON[last_sunday_str]
except:
    prev_sunday_str = (last_sunday - td(days=7*2)).strftime('%Y%m%d')
    last_sunday_str = (last_sunday-td(days=7)).strftime('%Y%m%d')
    CURRENT_WEEK_JSON = CHOSEN_STRATS_JSON[last_sunday_str]

LAST_WEEK_JSON = CHOSEN_STRATS_JSON[prev_sunday_str]
DEFAULT_WEEK_JSON = CHOSEN_STRATS_JSON['20221106']

try:
    CURRENT_WEEK_CLUSTERS = DYNAMIC_CLUSTERS_JSON[last_sunday_str]
except:
    CURRENT_WEEK_CLUSTERS = {last_sunday_str: {}, prev_sunday_str:{}}
try:
    LAST_WEEK_CLUSTERS = DYNAMIC_CLUSTERS_JSON[prev_sunday_str]
except:
    LAST_WEEK_CLUSTERS = {last_sunday_str: {}, prev_sunday_str:{}}


# take chosen strats
try:
    HYBRID_CONF = CURRENT_WEEK_JSON['GDD']['hybrid']
    HYBRIDP20_CONF = CURRENT_WEEK_JSON['GDD']['hybridP20']
    STRICT_CONF = CURRENT_WEEK_JSON['GDD']['strict']
except:
    print(f'Warning, didnt find chosen strats for last_sundy {last_sunday_str}, bringing from last week')
    HYBRID_CONF = DEFAULT_WEEK_JSON['GDD']['hybrid']
    HYBRIDP20_CONF = DEFAULT_WEEK_JSON['GDD']['hybridP20']
    STRICT_CONF = DEFAULT_WEEK_JSON['GDD']['strict']
try:
    HDD_CONF = CURRENT_WEEK_JSON['HDD']['hybrid']
    CDD_CONF = CURRENT_WEEK_JSON['CDD']['hybrid']
except:
    HDD_CONF = DEFAULT_WEEK_JSON['HDD']['hybrid']
    CDD_CONF = DEFAULT_WEEK_JSON['CDD']['hybrid']

WEEKLY_ADDITIONS = HYBRID_CONF['weekly']
WEEKLY_ADDITIONS_STRICT = STRICT_CONF['weekly']
WEEKLY_ADDITIONS_P20 = HYBRIDP20_CONF['weekly']

CHOSEN_OPTIONAL_BY_DAY = {
                    0:WEEKLY_ADDITIONS,
                    1:WEEKLY_ADDITIONS,
                    2:WEEKLY_ADDITIONS,
                    3:WEEKLY_ADDITIONS,
                    4: WEEKLY_ADDITIONS,
                         }

ALL_CONFS_COMBINED_BY_DAY = {
                            0:CHOSEN_OPTIONAL_BY_DAY[0] + HYBRID_CONF["0"],
                            1:CHOSEN_OPTIONAL_BY_DAY[1] + HYBRID_CONF["1"],
                            2:CHOSEN_OPTIONAL_BY_DAY[2] + HYBRID_CONF["2"],
                            3:CHOSEN_OPTIONAL_BY_DAY[3] + HYBRID_CONF["3"],
                            4:CHOSEN_OPTIONAL_BY_DAY[4] + HYBRID_CONF["4"],
                             }

ALL_CONFS_COMBINED_BY_DAY_LOOSE = {
                            0:CHOSEN_OPTIONAL_BY_DAY[0] + HYBRIDP20_CONF["0"],
                            1:CHOSEN_OPTIONAL_BY_DAY[1] + HYBRIDP20_CONF["1"],
                            2:CHOSEN_OPTIONAL_BY_DAY[2] + HYBRIDP20_CONF["2"],
                            3:CHOSEN_OPTIONAL_BY_DAY[3] + HYBRIDP20_CONF["3"],
                            4:CHOSEN_OPTIONAL_BY_DAY[4] + HYBRIDP20_CONF["4"],
                             }


ALL_CONFS_COMBINED_BY_DAY_STRICT = {
                                    0: WEEKLY_ADDITIONS_STRICT + STRICT_CONF["0"],
                                    1: WEEKLY_ADDITIONS_STRICT + STRICT_CONF["1"],
                                    2: WEEKLY_ADDITIONS_STRICT + STRICT_CONF["2"],
                                    3: WEEKLY_ADDITIONS_STRICT + STRICT_CONF["3"],
                                    4: WEEKLY_ADDITIONS_STRICT + STRICT_CONF["4"],

                                    }


WEEKLY_ADDITIONS_CDD = []
ALL_CONFS_COMBINED_BY_DAY_CDD = {
                                    0: WEEKLY_ADDITIONS_CDD + CDD_CONF["0"],
                                    1: WEEKLY_ADDITIONS_CDD + CDD_CONF["1"],
                                    2: WEEKLY_ADDITIONS_CDD + CDD_CONF["2"],
                                    3: WEEKLY_ADDITIONS_CDD + CDD_CONF["3"],
                                    4: WEEKLY_ADDITIONS_CDD + CDD_CONF["4"],

                                 }


WEEKLY_ADDITIONS_HDD = []
ALL_CONFS_COMBINED_BY_DAY_HDD = {
                                    0: WEEKLY_ADDITIONS_HDD + HDD_CONF["0"],
                                    1: WEEKLY_ADDITIONS_HDD + HDD_CONF["1"],
                                    2: WEEKLY_ADDITIONS_HDD + HDD_CONF["2"],
                                    3: WEEKLY_ADDITIONS_HDD + HDD_CONF["3"],
                                    4: WEEKLY_ADDITIONS_HDD + HDD_CONF["4"],

                                 }

ALL_CLUSTERS = ['windNew', 'cash', 'seasonal', 'eps', 'ec', 'canadian', 'gfs', 'paraco', 'longT', 'american', 'cfs0to16', 'momentum', 'external', 'weatherGen', '12Z', 'residual']
#  ========================================= CLUSTERS FILTERING  ========================================= #


DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR = {'ens_cluster_filter_v1':
                                            {
                                            0:{
                                                (8,11):[],
                                                (11,12):['american','d8to16','weatherGen',
                                                         'canadian','longT','external2'],
                                                (12,13):['cash','american','d8to16','weatherGen',
                                                         'canadian','longT','external2'],
                                                (13,15):['d8to16','weatherGen','longT'],
                                                (15,17):['cash','american','1D'],
                                                (17,20):[], # todo
                                                 },
                                            1:{
                                                (8,11):['american','d8to16','1D','eps'],
                                                (11,13):'all',
                                                (13,14):['1D','longT','d8to16'],
                                                (14,17):['longT','cash','external'],
                                                (17,20):['d0to8','coal','cash','windNew'],
                                                 },
                                            2:{
                                                (8,11):['eps','1D','american','canadian','d8to16'],
                                                (11,12):['canadian','american','6Z','windNew',
                                                         'eps','longT','external'],
                                                (12,14):['canadian','american','6Z','windNew','external',
                                                         'cfs0to16'],
                                                (14,15):['weatherGenExclusive','d0to8'],
                                                (15,16):['d0to8','cash'],
                                                (16,18):['momentum','weatherGen','eps'],
                                                (18,20):['paraco','12Z','d0to8'],
                                                 },
                                            3:{
                                                (8,10):['longT','american','external2'],
                                                (10,11):['d8to16'],
                                                (11,14):['american','eps','d0to8','gfs',
                                                         'momentum','canadian','external2'],
                                                (14,15):['d8to16',
                                                         ],
                                                (15,16):['cash','momentum','d0to8',
                                                         'd8to16','longT','12Z','6Z'],
                                                (16,18):['1D','external2','american','cash',
                                                         'longT','12Z','6Z'],
                                                (18,20):['1D','american','longT','coal'],
                                                 },
                                            4:{
                                                (8,11):['american','longT','momentum','eps','d8to16'],
                                                (11,13):['d8to16','d0to8'],
                                                (13,14):['6Z','external','american','6Z'],
                                                (14,16):['eps'],
                                                (16,18):['d0to8','external2'], #
                                                (18,20):['6Z','longT'], #
                                                 },
                                              },
                                        'ens_cluster_filter_v2':
                                            {

                                            0:{
                                                (0,8):['paraco','weatherGen','ec'],
                                                (8,11):['longT','canadian','external'],
                                                (11,12):['eps','paraco','weatherGen','american','canadian'],
                                                (12,13):['gfs','canadian','paraco','longT','weatherGen'],
                                                (13,14):['windNew','american'],
                                                (14,15):['longT','paraco','canadian','residual','external','momentum'],
                                                (15,18):['ec','residual','eps','longT','external','canadian','paraco'],
                                                (18,20):['longT','windNew','paraco','residual','canadian','external'], # paraco
                                                 },
                                            1:{
                                                (0,8):[],
                                                (8,10):[],
                                                (10,11):['eps','longT'],
                                                (11,12):['eps','longT','american','weatherGen'],
                                                (12,13):['gfs','eps','longT','american','weatherGen'],
                                                (13,14):['weatherGen','momentum'],
                                                (14,15):[],
                                                (15,16):['american','paraco','canadian'],
                                                (16,17):['longT','weatherGen','canadian','windNew','external'],
                                                (17,18):['weatherGen','eps','canadian'],
                                                (18,20):[],
                                                 },
                                            2:{
                                                (0,8):['american','gfs'],
                                                (8,10): ['external','seasonal'],
                                                (10,11):['longT','american','paraco','external'],
                                                (11,12):['residual','weatherGen'],
                                                (12,13):['eps','canadian'],
                                                (13,14):['eps','canadian','weatherGen','paraco'],
                                                (14,15):['gfs','paraco'],
                                                (15,16):['gfs','paraco','american','canadian'],
                                                (16,17):['eps'],
                                                (18,20):['eps','external'],

                                                 },
                                            3:{
                                                (0,8):[],
                                                (8,9):[x for x in ALL_CLUSTERS],
                                                (9,10):[x for x in ALL_CLUSTERS if x not in ['eps','momentum','longT']],
                                                (10,11):ALL_CLUSTERS,
                                                (11,12):[x for x in ALL_CLUSTERS if x not in ['weatherGen','american','momentum','paraco']],
                                                (12,13):[x for x in ALL_CLUSTERS if x not in ['cfs0to16']],
                                                (13,14):['gfs','momentum','canadian','paraco'],
                                                (14,15):['gfs','american','external'],
                                                (15,16):['paraco','eps','cash','cfs0to16'],
                                                (16,17):[x for x in ALL_CLUSTERS if x not in ['momentum']],
                                                (17,18):[x for x in ALL_CLUSTERS if x not in ['momentum','gfs']],
                                                (18,20):['paraco'],
                                                 },
                                            4:{
                                                (0,8):['canadian','eps'],
                                                (8,10): ALL_CLUSTERS,
                                                (10,11): [x for x in ALL_CLUSTERS if x not in ['eps','seasonal']],
                                                (11,12): [x for x in ALL_CLUSTERS if x not in ['gfs','longT']],

                                                (12,13): ['gfs','weatherGen','momentum','paraco'],
                                                (13,14):['external','canadian'],
                                                (14,15):['cash','canadian'],
                                                (15,17):ALL_CLUSTERS,
                                                (17,18):['weatherGen'],
                                                (18,20):[x for x in ALL_CLUSTERS if x not in ['gfs','canadian']]
                                                 },
                                              },
                                        # v1 is calmar | v2 with sum
                                        'ens_cluster_dynamic_v1_S0.87_w14': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v1_S0.87_w14"]) if "ens_cluster_dynamic_v2_S0.5_w11" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w3': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w3"]) if "ens_cluster_dynamic_v2_S0.5_w3" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w4': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w4"]) if "ens_cluster_dynamic_v2_S0.5_w4" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w5': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w5"]) if "ens_cluster_dynamic_v2_S0.5_w5" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w7': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w7"]) if "ens_cluster_dynamic_v2_S0.5_w7" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w9': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w9"]) if "ens_cluster_dynamic_v2_S0.5_w11" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w10': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w10"]) if "ens_cluster_dynamic_v2_S0.5_w10" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w11': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w11"]) if "ens_cluster_dynamic_v2_S0.5_w11" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w14': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w11"]) if "ens_cluster_dynamic_v2_S0.5_w14" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v1_S0.87_w14_prev1w': format_cluster_json_back(LAST_WEEK_CLUSTERS["ens_cluster_dynamic_v1_S0.87_w14"]) if "ens_cluster_dynamic_v1_S0.87_w14" in LAST_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w5_prev1w': format_cluster_json_back(LAST_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w5"]) if "ens_cluster_dynamic_v2_S0.5_w5" in LAST_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w9_prev1w': format_cluster_json_back(LAST_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w9"]) if "ens_cluster_dynamic_v2_S0.5_w9" in LAST_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v2_S0.5_w11_prev1w': format_cluster_json_back(LAST_WEEK_CLUSTERS["ens_cluster_dynamic_v2_S0.5_w11"]) if "ens_cluster_dynamic_v2_S0.5_w11" in LAST_WEEK_CLUSTERS.keys() else {},
                                        # New methods after Bug (V3)
                                        'ens_cluster_dynamic_v3_S0.5_w3': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w3"]) if "ens_cluster_dynamic_v3_S0.5_w3" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3_S0.5_w7_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w7_loose"]) if "ens_cluster_dynamic_v3_S0.5_w7_loose" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3_S0.5_w3c3': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w3c3"]) if "ens_cluster_dynamic_v3_S0.5_w3c3" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3_S0.5_w5': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w5"]) if "ens_cluster_dynamic_v3_S0.5_w5" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3_S0.5_w7': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w7"]) if "ens_cluster_dynamic_v3_S0.5_w7" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3_S0.5_w9': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3_S0.5_w9"]) if "ens_cluster_dynamic_v3_S0.5_w9" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v3b_S0.5_w9': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v3b_S0.5_w9"]) if "ens_cluster_dynamic_v3b_S0.5_w9" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'real_v3T5_S0.5_w9': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['real_w9']) if 'real_w9' in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'real_v3T5_S0.5_w3': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['real_w3']) if 'real_w3' in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v4_S0.5_w9"]) if "ens_cluster_dynamic_v4_S0.5_w9" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x3D': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v4_S0.5_w9x3d"]) if "ens_cluster_dynamic_v4_S0.5_w9x3d" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x3d': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v4_S0.5_w9x3d"]) if "ens_cluster_dynamic_v4_S0.5_w9x3d" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x3d_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v4_S0.5_w9x3d_loose"]) if "ens_cluster_dynamic_v4_S0.5_w9x3d_loose" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w7x4d_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['ens_cluster_dynamic_v4_S0.5_w7x4d_loose']) if 'ens_cluster_dynamic_v4_S0.5_w7x4d_loose' in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x1d_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['ens_cluster_dynamic_v4_S0.5_w9x1d_loose']) if 'ens_cluster_dynamic_v4_S0.5_w9x1d_loose' in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x4d_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['ens_cluster_dynamic_v4_S0.5_w9x4d_loose']) if 'ens_cluster_dynamic_v4_S0.5_w9x4d_loose' in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v4_S0.5_w9x5d_loose': format_cluster_json_back(CURRENT_WEEK_CLUSTERS['ens_cluster_dynamic_v4_S0.5_w9x5d_loose']) if 'ens_cluster_dynamic_v4_S0.5_w9x5d_loose' in CURRENT_WEEK_CLUSTERS.keys() else {},

                                        'ens_cluster_dynamic_v4_S0.5_w9_hybrid': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v4_S0.5_w9_hybrid"]) if "ens_cluster_dynamic_v4_S0.5_w9_hybrid" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        'ens_cluster_dynamic_v5_S0.5_dynamic': format_cluster_json_back(CURRENT_WEEK_CLUSTERS["ens_cluster_dynamic_v5_S0.5_dynamic"]) if "ens_cluster_dynamic_v5_S0.5_dynamic" in CURRENT_WEEK_CLUSTERS.keys() else {},
                                        }

bb= 0