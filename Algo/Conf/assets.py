import os
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td

from Algo.Utils.files_handle import HOME, MARKET_DATA_DIRECTORIES_BY_ASSET
try:
    from ibapi.contract import *
except:
    from brokerapi.contract import *


CONTRACTS_DICT = {"NG": {"symbol": "NG",
                         "Trading Class": "NG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "localSymbol": "NGZ7"
                         },
                "QG": {"symbol": "QG",
                         "Trading Class": "QG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "localSymbol": "QGZ7"
                         },
                "MHNG": {"symbol": "MHNG",
                         "Trading Class": "MNG",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYMEX",
                         # "ConId": 81596359,
                         "localSymbol": "MNGG5"
                         },
                  "NQ": {"symbol": "NQ",
                         "Trading Class": "NQ",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", # "GLOBEX",
                        'primaryExchange': '',
                         # "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "localSymbol": "NQZ2"
                         },
                  "MNQ": {"symbol": "MNQ",
                          "Trading Class": "MNQ",
                          "currency": "USD",
                          "secType": "FUT",
                          "exchange": "CME", #, #"GLOBEX",
                          # "exchange": "GLOBEX",
                          # "ConId": 81596359,
                          "localSymbol": "MNQZ2"
                          },
                  "MGC": {"symbol": "MGC",
                          "Trading Class": "MGC",
                          "currency": "USD",
                          "secType": "FUT",
                          "exchange": "COMEX",
                          # "ConId": 81596359,
                          "LocalSymbol": "MGCZ2"
                          },

                  "ES": {"symbol": "ES",
                         "Trading Class": "ES",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                         # "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "localSymbol": "ESZ2"
                         },
                  "MES": {"symbol": "MES",
                         "Trading Class": "MES",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                         # "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "localSymbol": "MESZ2"
                         },
                  "BRR": {"symbol": "BRR",
                         "Trading Class": "BTC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                        'primaryExchange': "",
                         # "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "localSymbol": "BTCZ3"
                         },
                    "MBT": {"symbol": "MBT",
                         "Trading Class": "MBT",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "CME", #"GLOBEX",
                         # "exchange": "GLOBEX",
                         # "ConId": 81596359,
                         "localSymbol": "MBTZ2"
                         },
                    "GC": {"symbol": "GC",
                         "Trading Class": "GC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "COMEX",
                         "localSymbol": "GCJ3"
                         },

                  "KC": {"symbol": "KC",
                         "Trading Class": "KC",
                         "currency": "USD",
                         "secType": "FUT",
                         "exchange": "NYBOT",
                         # "ConId": 81596359,
                         "localSymbol": "KCZ7"  # Dec Z | Sep U
                         },
                  "UNG":
                      {"symbol": "UNG",
                       "currency": "USD",
                       "secType": "STK",
                       "exchange": "SMART",
                       "primary_exchange": "ARCA"
                       },
                  "ZC":
                      {"symbol": "ZC",
                       "Trading Class": "ZC",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "ECBOT",
                       "localSymbol": "ZC   JUL 20"  # "ZCZ7"  # Dec Z | Sep U
                       # "ConID": 291854767

                       },
                "NCF":
                      {"symbol": "NCF",
                       "Trading Class": "NCF",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "localSymbol": "NCFN1"
                       # "ConID": 291854767
                       },
                "NGF":
                      {"symbol": "NGF",
                       "Trading Class": "NGF",
                       "currency": "GBP",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "localSymbol": "NGFN1"
                       # "ConID": 291854767
                       },
                "WTI":
                      {"symbol": "WTI",
                       "Trading Class": "WTI",
                       "currency": "USD",
                       "secType": "FUT",
                       "exchange": "IPE",
                       "localSymbol": "WTIX1"
                       # "ConID": 291854767
                       },
                  "ROOT":
                      {"symbol": "ROOT",
                          "currency": "USD",
                            # "secType": "Stocks",
                            "secType": "STK",
                            "exchange": "SMART",
                            "primary_exchange": "NASDAQ"
                          },
                  }

NG_CONTRACT_CODES = {'January': 'F', 'February': 'G', 'March': 'H', 'April': 'J',
                     'May': 'K', 'June': 'M', 'July': 'N', 'August': 'Q', 'September': 'U',
                     'October': 'V', 'November': 'X', 'December': 'Z'}


DAYS_BEFORE_EXPIRY = {'NG':9,'QG':9,'COFFEE':9,'CORN':9,'NCF':2,
                      'NQ':-10,
                      'ES':-10,
                      'BRR':-20,
                      'MBT':-20,
                      'GC':-15,
                      }  # 5 for COFFEE, 2, 30
DAYS_BEFORE_EXPIRY['MHNG'] = DAYS_BEFORE_EXPIRY['NG']

FUTURES_MONTHS_BY_ASSET = {'COFFEE':[3, 5, 7, 9, 12],
                            'CORN':[3, 5, 7, 9, 12],
                            'NQ':[3, 6, 9, 12],
                            'ES':[3, 6, 9, 12],
                            'BRR':[1,2,3,4,5,6,7,8,9,10,11,12],
                            'MBT':[1,2,3,4,5,6,7,8,9,10,11,12],
                            'GC':[1,2,3,4,5,6,7,8,9,10,11,12],
                            }


ASSET_SHORTNAMES = {# 'Nasdaq':'NQ',
                    'CORN':'ZC',
                    'COFFEE':'KC'}

PERIODS_BY_ASSET = {'NG': '30 D',
                    'QG': '30 D',
                    'MHNG': '30 D',
                    'CORN':'90 D',
                    'COFFEE':'90 D',
                    'NQ':'90 D',
                    'ES':'90 D',
                    'BRR':'45 D',
                    'MBT':'45 D',
                    'GC':'45 D',
                    }

SPECIAL_RESOLUTION_ASSETS = {'NQ': '1 Hour',
                             'BRR': '1 Hour',
                             'MBT': '1 Hour',
                             'GC': '30 mins',
                           'ES': '30 mins'}
ASSETS_WITHOUT_TZ_IN_CANDLES = []

class Asset(object):

    def __init__(self,asset_name='NG'): #,resolution='15m'):
        self.name = asset_name
        self.short_name = ASSET_SHORTNAMES.get(asset_name,asset_name)
        self.days_to_exp = DAYS_BEFORE_EXPIRY.get(asset_name,1)

        self.contract_dict = None
        # self.resolution = resolution

        self.symbol = CONTRACTS_DICT[asset_name]['symbol']
        self.trading_class = CONTRACTS_DICT[asset_name]["Trading Class"]
        self.currency = CONTRACTS_DICT[asset_name]["currency"]
        self.sec_type = CONTRACTS_DICT[asset_name]['secType']
        self.exchange = CONTRACTS_DICT[asset_name]["exchange"]
        self.local_symbol = CONTRACTS_DICT[asset_name].get("localSymbol",None)

        self.default_resolution = "15 mins" if asset_name not in ['NQ','ES','BRR','MBT'] else '1 Hour'
        if asset_name in ['GC']: # todo
            self.default_resolution = SPECIAL_RESOLUTION_ASSETS.get(asset_name,'30 mins')
        # PATHS
        self.market_data_dir = MARKET_DATA_DIRECTORIES_BY_ASSET[asset_name]
        self.delta_ys_dir = os.path.join(self.market_data_dir,'Ys3')
        self.months_dir = os.path.join(self.market_data_dir,'months')
        self.add_tz_to_candles = self.name not in ASSETS_WITHOUT_TZ_IN_CANDLES
        self._set_candles_outpath(add_tzs=self.add_tz_to_candles)

        self.candles_period = PERIODS_BY_ASSET[asset_name]
        self.allowed_months = FUTURES_MONTHS_BY_ASSET.get(asset_name, list(range(1, 13)))

        self.days_back_candles = 2
        if asset_name in ['MHNG','QG','NG']:
            self.days_back_candles = 5
        elif asset_name in ['NQ','ES']:
            self.days_back_candles = -10
        elif asset_name in ['BRR','MBT']:
            self.days_back_candles = -20
        elif asset_name in ['GC']:
            self.days_back_candles = -15


    def _set_candles_outpath(self,add_tzs):
        if self.name in ['MHNG','QG','NG']:
            candles_outpath = os.path.join(self.market_data_dir,'NG_2018-19_frontMonth_%sLive.csv' % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'CORN':
            candles_outpath = os.path.join(self.market_data_dir,"CORN_2018-19_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'COFFEE':
            candles_outpath = os.path.join(self.market_data_dir, "COFFEE_2018-19_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'NCF':
            candles_outpath = os.path.join(self.market_data_dir,"COAL_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'NQ':
            candles_outpath = os.path.join(self.market_data_dir,"NQ_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'ES':
            candles_outpath = os.path.join(self.market_data_dir,"ES_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'BRR':
            candles_outpath = os.path.join(self.market_data_dir,"BTC_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'MBT':
            candles_outpath = os.path.join(self.market_data_dir,"MBT_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        elif self.name == 'GC':
            candles_outpath = os.path.join(self.market_data_dir,"GC_frontMonth_%sLive.csv" % (
                "" if not add_tzs else "tz_"))
        else:
            raise AssertionError('Invalid asset')
        self.candles_outpath = candles_outpath


    def _get_prompt_month_name(self, contract_dt, year):
        month_code = NG_CONTRACT_CODES[contract_dt.strftime("%B")]
        if self.short_name not in ['ZC']:
            prompt_month_local_name = '%s%s%s' % (self.short_name, month_code, str(year)[-1:])
        else:
            prompt_month_local_name = '%s   %s %s' % (self.short_name, contract_dt.strftime("%b").upper(), str(year)[-2:])
        return prompt_month_local_name


    def _get_contract_dt(self, ref_day, delay_next_contract_days=0):

        if self.name in ['NG','QG','MHNG']:
            if delay_next_contract_days > 0:
                delay_next_contract_days = min(7, delay_next_contract_days)
                #last_friday_of_contract += td(days=delay_next_contract_days)
            contract_dt = (ref_day + td(days=(38 if ref_day.month !=2 else 35))-td(days=delay_next_contract_days)).replace(day=1)
            next_first_of_month = (ref_day + td(days=32 - ref_day.day)).replace(day=1)
            original_last_day_of_contract = next_first_of_month - td(days=self.days_to_exp)
            last_friday_of_contract = original_last_day_of_contract - td(days=original_last_day_of_contract.weekday() + 3)

            # this work around seems to be no longer relevant (Mar22) todo
            # if False: #last_friday_of_contract < ref_day < original_last_day_of_contract:
            #     # contract_dt += td(days=(26 if contract_dt.month == 2 else 30))
            #     contract_dt += td(days=31)
            #     contract_dt = contract_dt.replace(day=1)
            # aa = 1
        elif self.name == 'CORN':
            contract_dt = (ref_day + td(days=40)).replace(day=1)
            while contract_dt.month not in self.allowed_months:
                contract_dt = (contract_dt + td(days=35)).replace(day=1)
        elif self.name == 'COFFEE':
            contract_dt = (ref_day + td(days=40)).replace(day=1)
            while contract_dt.month not in self.allowed_months:
                contract_dt = (contract_dt + td(days=35)).replace(day=1)
        else:
            raise AssertionError('invalid asset')
        return contract_dt

    def _calc_next_contract(self, current_contract_str):
        if self.name in ['NG','NCF']:
            return int((dtdt(int(str(current_contract_str)[:4]), int(str(current_contract_str)[-2:]), 20) + td(days=14)).strftime("%Y%m"))
        elif self.name in ['CORN','COFFEE']:
            current_month_dt = dtdt(int(str(current_contract_str)[:4]), int(str(current_contract_str)[-2:]), 20)
            if current_month_dt.month not in [9,12]:
                days_gap = 60
            else:
                days_gap = 90
            return int((current_month_dt+td(days=days_gap)).strftime('%Y%m'))
        elif self.name in ['NQ','ES']:
            current_month_dt = dtdt(int(str(current_contract_str)[:4]), int(str(current_contract_str)[-2:]), 20)
            days_gap = 90
            return int((current_month_dt+td(days=days_gap)).strftime('%Y%m'))

    def get_monthly_data_outpath(self,year,month, resolution):
        return os.path.join(self.months_dir,f'{self.name}_{year}{str(month).zfill(2)}{"_%s"%resolution.replace(" ","") if resolution!= self.default_resolution else ""}.csv')

    def get_modified_candles_df_outpath(self, resolution):
        if resolution != self.default_resolution:
            return self.candles_outpath.replace('.csv',f'_{resolution.replace(" ","")}.csv')
        else:
            return self.candles_outpath
    def get_contract(self, local_name=None):
        contract = Contract()
        name = self.short_name

        contract.symbol = self.symbol  # 'underlying' field
        contract.secType = self.sec_type
        contract.exchange = self.exchange
        contract.includeExpired = True
        contract.currency = self.currency

        contract.localSymbol = self.local_symbol
        if local_name is not None:
            contract.localSymbol = local_name

        return contract
