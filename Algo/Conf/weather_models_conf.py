MAX_Z = 384
midi_above_240_zs = [12*i for i in range(20,33)]
midi_below_240 = [3*i for i in range(80)]
ALL_HORIZONS =  midi_below_240 +  midi_above_240_zs
GFS_4_HORIZONS = [z for z in ALL_HORIZONS if z <= MAX_Z]
GEFS_HORIZONS = range(0,390,6)
PARA_HORIZONS = range(0,385,3)
GEM_HORIZONS = range(0,241,3)
GEPS_HORIZONS = list(range(0,193,3))+list(range(198,385,6))


MODELS_LATENCIES = {'GFS': [3, 45], 'GEFS': [4, 50], 'PARA': [3, 45], 'GEM': [3, 35], 'GEPS': [5, 15],
                    'GFSv16': [4,30],'GEFSv16':[4,40],
                    'CFS': [7,0], 'EC': [6,5],'EPS': [7,15],'GEFSL': [7,30],'EPS45': [20,0],
                    'GEFS35':[25,0],'ICON':[3,30],
                    'EPS2':[9,0]}

MODELS_LATENCIES['GFSCO'] = MODELS_LATENCIES['GEFSL']
MODELS_LATENCIES['PARACO'] = MODELS_LATENCIES['GEFS']
MODELS_LATENCIES['GFSCOv16'] = MODELS_LATENCIES['GEFSv16']
MODELS_LATENCIES['GEMCO'] = MODELS_LATENCIES['GEPS']
MODELS_LATENCIES['GEFSCO35'] = MODELS_LATENCIES['GEFS35']
MODELS_LATENCIES['CFSCO'] = MODELS_LATENCIES['CFS']
MODELS_LATENCIES['CFSM'] = MODELS_LATENCIES['CFS']
MODELS_LATENCIES['EPSCO'] = MODELS_LATENCIES['EPS2']
MODELS_LATENCIES['ECAI'] = MODELS_LATENCIES['EPS2']
MODELS_LATENCIES['GCGFS'] = MODELS_LATENCIES['EPS2']

# Runs
MODELS_RUNS = {'GFS': [0, 6, 12, 18], 'GEFS': [0, 6, 12, 18], 'PARA': [0, 6, 12, 18],
               'GEM': [0, 12], 'GEPS': [0, 12], 'CFS': [0,6,12,18],'EC': [0,12],'EPS':[0,12],
                        'GEFSL': [0], 'EPS45': [0],'GEFS35':[0],'ICON':[0,6,12,18]}
MODELS_RUNS['GFSCO'] = MODELS_RUNS['GEFSL']
MODELS_RUNS['PARACO'] = MODELS_RUNS['GEFS']
MODELS_RUNS['EPSCO'] = MODELS_RUNS['EPS']
MODELS_RUNS['GEMCO'] = MODELS_RUNS['GEPS']
MODELS_RUNS['GFSv16'] = MODELS_RUNS['PARA']
MODELS_RUNS['GEFSv16'] = MODELS_RUNS['PARA'] # [0]
MODELS_RUNS['GFSCOv16'] = MODELS_RUNS['PARA']
MODELS_RUNS['GEFSCO35'] = MODELS_RUNS['GEFS35']
MODELS_RUNS['CFSCO'] = MODELS_RUNS['CFS']
MODELS_RUNS['CFSM'] = MODELS_RUNS['CFS']
MODELS_RUNS['ECAI'] = [0,6,12,18]
MODELS_RUNS['GCGFS'] = [0,6,12,18]

# Gaps
MODEL_GAPS = {'GFS': 6, 'GEFS': 6, 'PARA': 6, 'GEM': 12, 'GEPS': 12, 'CFS': 6,
                'GEFSv16':6, 'EC':12, 'EPS':12, 'GEFSL': 24, 'EPS45':24,
              'GEFS35':24,'ICON':6}
MODEL_GAPS['GFSCO'] = MODEL_GAPS['GEFSL']
MODEL_GAPS['PARACO'] = MODEL_GAPS['GEFS']
MODEL_GAPS['EPSCO'] = MODEL_GAPS['EPS']
MODEL_GAPS['GEMCO'] = MODEL_GAPS['GEPS']
MODEL_GAPS['GFSv16'] = MODEL_GAPS['PARA'] # 24 temporary Dec 2020....
MODEL_GAPS['GFSCOv16'] = MODEL_GAPS['PARA']
MODEL_GAPS['GEFSCO35'] = MODEL_GAPS['GEFS35']
MODEL_GAPS['CFSCO'] = MODEL_GAPS['CFS']
MODEL_GAPS['CFSM'] = MODEL_GAPS['CFS']
MODEL_GAPS['ECAI'] = 6
MODEL_GAPS['GCGFS'] = 6

# Horizons
MODELS_HORIZONS = {'GFS': GFS_4_HORIZONS, 'GEFS': GEFS_HORIZONS, 'PARA': PARA_HORIZONS,
                   'GEM': GEM_HORIZONS, 'GEPS': GEPS_HORIZONS, 'CFS': range(0,1081,6),
                   'EC': range(0,217,6),'EPS': range(0,360,6), 'GEFSL': GEFS_HORIZONS,
                   'EPS45': range(1,1081,6),
                    'GEFSv16': list(range(0,240,3))+list(range(240,385,6)),
                   'GEFS35': list(range(0,240,3))+list(range(240,841,6)),
                    'ICON': list(range(0,181,3))
                   }
MODELS_HORIZONS['GFSCOv16'] = MODELS_HORIZONS['GEFSv16']
MODELS_HORIZONS['GFSCO'] = MODELS_HORIZONS['GEFSL']
MODELS_HORIZONS['PARACO'] = MODELS_HORIZONS['GEFS']
MODELS_HORIZONS['EPSCO'] = MODELS_HORIZONS['EPS']
MODELS_HORIZONS['GEMCO'] = MODELS_HORIZONS['GEPS']
MODELS_HORIZONS['GFSv16'] = MODELS_HORIZONS['PARA']
MODELS_HORIZONS['GEFSCO35'] = MODELS_HORIZONS['GEFS35']
MODELS_HORIZONS['CFSCO'] = MODELS_HORIZONS['CFS']
MODELS_HORIZONS['CFSM'] = [0,720,720*2,720*3]
MODELS_HORIZONS['ECAI'] = MODELS_HORIZONS['EPSCO']
MODELS_HORIZONS['GCGFS'] = MODELS_HORIZONS['GEFS']

