"""
Add here the updading choice of strategies
"""


TOP15_20D_SLIDE1_LAST0_UPDATING = ['y_1815-1900_GEPS12Zp1$2', 'y_1330-1430_cashCFS6zp1', 'y_1745-1945_Cash0dGEFS', 'y_1100-1515_momentumTuWe', 'y_1100-1515_momentumThFrNegative', 'y_1100-1300_cashCFS', 'y_1300-1430_cashGEM', 'y_0600-1415_epsFcstPgeps0Z', 'y_0800-1400_EPShybrid1b', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_1645-1745_TTFclosevs1to3', 'y_1530-1715_PARACO18z14to16', 'y_1200-1945_epsFcst12ZbCorW15', 'y_0800-1400_cash2', 'y_1715-1915_GEFSComb', 'y_1645-1745_PARAPrev1D8to16', 'y_1200-1245_6Zb', 'y_1100-1615_GFSv16Zp2', 'y_1200-1415_CFSvsEPS0Z$2', 'y_1315-1615_PARA6Zp1only', 'y_0800-1000_PARACO12zPrev1', 'y_1100-1300_PARA18Z3D8to16', 'y_1145-1230_CFSCOcomb', 'y_1300-1545_PARA018Z0to8', 'y_1415-1645_PARACO6z', 'y_0800-1200_GEM0Z', 'y_1100-1315_ECGEM', 'y_0800-1745_yearly', 'y_1115-1245_PARACO6zp10to8', 'y_1415-1545_Comb', 'y_1130-1300_ICONCashStrict', 'y_1100-1615_GFSv16z0to8p2']
LAST_UPDATED_TOP = TOP15_20D_SLIDE1_LAST0_UPDATING #TOP30_10D_SLIDE1_LAST0_UPDATING

ALL_CONFS_COMBINED_BY_DAY = {
                            0: ['y_1100-1400_GEFS18Zday15','y_1100-1400_CFSCOcomb21to42']+['y_1100-1400_GFSv1612zp4', 'y_1145-1745_GEFSCO1D', 'y_0400-0700_GEPS12Zp2', 'y_1000-1615_Moncash', 'y_0800-1200_f234b$2', 'y_1145-1230_CFSCOcomb', 'y_1715-1915_GEFSComb', 'y_1300-1545_PARA018Z0to8', 'y_1615-1745_cfsDaily0Z0to162D', 'y_1100-1615_GFSv16Zp2$2', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_0600-0800_epsFcst$2', 'y_1100-1315_ECGEM', 'y_1100-1230_GFSv16z0to8', 'y_1200-1245_monMean', 'y_1415-1545_Comb', 'y_1315-1745_american024', 'y_0800-1945_EPShybrid2.2', 'y_1245-1515_d', 'y_1200-1945_epsFcst12ZbCorW15', 'y_1300-1515_GEFSCO6Z23D', 'y_1245-1415_american4D', 'y_0800-1400_EPShybrid1b', 'y_0800-1745_yearly', 'y_1200-1415_CFSvsEPS0Z', 'y_2300-2345_EPS12Z', 'y_1000-1400_PARACO18z', 'y_1315-1415_americanSeasonalb', 'y_1115-1815_GEPSpm', 'y_0800-1200_GEM0Z', 'y_0000-0600_18Z', 'y_0600-0800_EC12z2D', 'y_1100-1315_cfsMon', 'y_0800-1945_EPShybrid2.2Strict', 'y_1815-1945_epsFcstGEPFS012Zgap', 'y_1330-1415_GEPSam', 'y_1100-1200_d', 'y_1815-2030_epsFcst12ZbCor', 'y_1000-1200_eps12Z9to13', 'y_0800-1945_EPSPrev1DStrict', 'y_1115-1145_GEFS6z0to8', 'y_0730-0800_cfs12Z28to42', 'y_0800-1745_PARA18z4D', 'y_1315-1745_d', 'y_1400-1630_GFSEFS6z'],
                            1: ['y_1100-1515_momentumTuWe', 'y_1100-1615_GFSv16z0to8p2', 'y_0600-1415_epsFcstPgeps0Z', 'y_1100-1615_GFSv16Zp2', 'y_1415-1645_PARACO6z', 'y_1300-1545_PARA018Z0to8', 'y_0800-1745_yearly', 'y_0800-1945_EPShybrid2.2', 'y_1115-1245_PARACO6zp10to8', 'y_0800-1200_EPSCFS', 'y_1200-1245_6Zclean', 'y_1200-1245_6Zb', 'y_1215-1315_eps12Zp40to8$2', 'y_1100-1315_ECGEM', 'y_1400-1500_cfs0Z', 'y_1315-1615_PARA6Zp1only', 'y_1130-1300_GEFS6zp1', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_0800-1315_TTFStrict', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_0800-1400_cash2', 'y_0800-1200_GEM0Z', 'y_0600-0800_epsFcst', 'y_0600-0800_EC12z3D', 'y_1530-1715_PARACO18z14to16', 'y_0800-1945_EPShybrid2.2$2', 'y_0800-1945_GEPSCO', 'y_0800-1745_cfs14to211D', 'y_1100-1300_cashCFS', 'y_1130-1300_ICONCashStrict', 'y_1715-1815_cfs6Z28to42', 'y_1815-2030_cfs0Z28to42'],
                            2: ['y_1100-1315_COALopen','y_1100-1400_GEFS18Zday15','y_1100-1400_CFSCOcomb21to42']+ ['y_0800-1400_cash2', 'y_1300-1430_cashGEM', 'y_1100-1515_momentumThFrNegative', 'y_1330-1430_cashCFS6zp1', 'y_0800-1400_EPShybrid1b', 'y_1645-1745_TTFclosevs1to3', 'y_1715-1915_GEFSComb', 'y_1615-1715_cash', 'y_1330-1430_PARAPrev1Dx2D8to16', 'y_1000-1300_GEFS12zComb', 'y_1145-1415_cashGFS', 'y_0800-1315_TTFStrict', 'y_0800-1000_PARACO12zPrev1', 'y_1130-1300_ICONCash', 'y_1200-1945_epsFcstGEPS12Zgap', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_1415-1645_PARACO6z', 'y_1730-1830_eps12Z9to13p1', 'y_0730-0800_EPS0to8', 'y_0800-1945_EPSPrev1D', 'y_1615-1730_PARACO12zp2b', 'y_1445-1545_paracoSeasonal14to16', 'y_0800-1415_eps12Z', 'y_1745-2045_12Zb124b', 'y_1415-1615_GFS6Zrolling2', 'y_1715-1845_PARACO12z0to8', 'y_0000-0600_18Z', 'y_0630-0730_cfs12z10to211D', 'y_0800-1745_cfsDaily0Z'],
                            3: ['y_1100-1400_GEFS18Zday15','y_1100-1400_CFSCOcomb21to42']+['y_1100-1615_GFSv16z0to8p2Strict', 'y_1100-1615_GFSv16z0to8p2', 'y_1815-2030_epsFcst12Z', 'y_1315-1745_3c', 'y_1215-1515_PARACO6z14to16', 'y_1645-1745_GEFSPM', 'y_1815-1945_GEFS0to8', 'y_1115-1245_PARACO6zp10to8', 'y_1200-2030_epsFcstGEFS6Z', 'y_1545-1645_GFSv16z', 'y_1000-1100_GFSv160z', 'y_1745-1945_Cash0dGEFS', 'y_1315-1615_PARA6Zp1only', 'y_1100-1400_epsFcstGFSv16', 'y_1815-2030_epsFcst12ZCor', 'y_0600-0700_cfs1218ZMix', 'y_1815-1900_GFScash', 'y_1815-1900_GEPS12Zp1$2', 'y_0800-1745_yearly', 'y_1645-1745_PARAPrev1D8to16', 'y_1745-1815_cashPrice', 'y_0800-1945_EPShybrid2.2', 'y_1100-1615_GFSv16Zp2', 'y_1200-1415_CFSvsEPS0Z$2', 'y_1645-1745_PARACO6zStrict2Mean', 'y_1415-1615_GFS6Zrolling2', 'y_1200-1300_EPS4514to423D', 'y_1515-1845_cashFt123', 'y_1100-1130_GFSv164D'],
                            4: ['y_1100-1315_COALopen','y_1100-1400_GEFS18Zday15','y_1100-1400_CFSCOcomb21to42']+['y_1100-1615_GFSv16Zp2', 'y_1145-1230_CFSCOcomb', 'y_1000-1200_eps12Z9to13$2', 'y_1130-1300_ICONCash', 'y_1315-1745_momentumWed', 'y_1200-1415_CFSvsEPS0Z', 'y_1300-1545_PARA018Z0to8', 'y_1615-1745_PARACO18zp1clean$2', 'y_0800-1400_EPShybrid1b', 'y_1115-1415_PARACO6Z23D', 'y_1315-1615_PARA6Zp1only', 'y_0400-0800_EIAeffect', 'y_1100-1515_momentumThFr', 'y_0800-1315_TTFclose', 'y_1130-1300_ICONCashStrict', 'y_1100-1300_PARA18Z3D8to16', 'y_1100-1130_GFSv164D', 'y_1645-1745_TTFclosevs1to3', 'y_1215-1515_PARACO6z14to16', 'y_1845-2030_GEFS12Z', 'y_0800-1745_yearly', 'y_1215-1315_eps12Zp40to8', 'y_0630-0730_GEPSam2', 'y_1400-1430_PARARACOam', 'y_1645-1745_TTFclosevs1to3$2', 'y_1745-1945_Cash0dGEFS$2']
                            }

RESTRICTED_CONFS_BY_DAY = {0:['y_1100-1400_GFSv1612zp4', 'y_1145-1745_GEFSCO1D', 'y_0400-0700_GEPS12Zp2', 'y_1000-1615_Moncash', 'y_0800-1200_f234b$2', 'y_1145-1230_CFSCOcomb', 'y_1715-1915_GEFSComb', 'y_1300-1545_PARA018Z0to8', 'y_1615-1745_cfsDaily0Z0to162D', 'y_1100-1615_GFSv16Zp2$2', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_0600-0800_epsFcst$2', 'y_1100-1315_ECGEM', 'y_1100-1230_GFSv16z0to8', 'y_1200-1245_monMean', 'y_1415-1545_Comb'],
                           1:[],2:[],
                           3:[],
                           4:[]}


SPECIAL_IGNORES_FOR_SLIDE_BY_DAY = {0:['y_1100-1615_GFSv16Zp2'],
                                       1:[],
                                       2:[],
                                       3:[],
                                       4:[]}


USE_WINTER_MAPPING = False #False


cond_top20_sliding_by_day_ext_combinedlist = lambda x,weekday: x in LAST_UPDATED_TOP+ALL_CONFS_COMBINED_BY_DAY[weekday]
cond_top20x034_sliding_by_day_ext_combinedlist = lambda x,weekday: x in (LAST_UPDATED_TOP+ALL_CONFS_COMBINED_BY_DAY[weekday] if weekday in [0,3,4] else ALL_CONFS_COMBINED_BY_DAY[weekday])
cond_top20x034_sliding_by_day_ext_combinedlist_dropignores = lambda x,weekday: x in (LAST_UPDATED_TOP+ALL_CONFS_COMBINED_BY_DAY[weekday] if weekday in [0,3,4] else ALL_CONFS_COMBINED_BY_DAY[weekday]) and x not in SPECIAL_IGNORES_FOR_SLIDE_BY_DAY[weekday]
cond_restricted_by_day_ext = lambda x,weekday: x in RESTRICTED_CONFS_BY_DAY[weekday]

ADDITIONAL_STR_COND_FOR_TRADING = {'real': cond_top20x034_sliding_by_day_ext_combinedlist_dropignores, #cond_top20_sliding_by_day_ext_with_calmar,
                                    'realG': cond_top20_sliding_by_day_ext_combinedlist,
                                   'paper': cond_restricted_by_day_ext,#cond_top20_sliding_by_day_ext_with_calmar,
                                   'paperCDD':cond_top20_sliding_by_day_ext_combinedlist,#cond_top20_sliding_by_day_ext_with_calmar,
                                   'paperHDD':cond_top20_sliding_by_day_ext_combinedlist
                                   } # None

