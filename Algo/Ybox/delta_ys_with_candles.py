from datetime import time
from datetime import timedelta as td
from datetime import datetime as dtdt
import pandas as pd
import json, os
from Algo.Utils.files_handle import get_candles_outpath, get_delta_ys_full_df_path, MARKET_DATA_DIRECTORIES_BY_ASSET

# a = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\XYs\Enriched_XYs\XY_a_GDD_v8_0Zb.csv",
#                 parse_dates=['date'])
# a = a[['date']+[x for x in list(a) if x.startswith('y_') and '-' in x
#                 and len(x.split('-')[1]) == 4]]
#
# df_8to18 = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\Ys3\Y_0800-1845_15Mins_utc.csv",
#                        parse_dates=['time_utc'])[['time_utc', 'y']].rename(
#     columns={'time_utc': 'date', 'y': 'y_0800-1845_csv'})


ASSET = 'NG'
NG_CANDLES = get_candles_outpath('NG')

# Get the directory that the script is located in
script_dir = os.path.dirname(os.path.abspath(__file__))

# Construct the path to your JSON file
delta_y_filename = 'delta_ys_list.json'
json_file_path = os.path.join(script_dir, delta_y_filename)

with open(json_file_path) as f:
    DELTA_YS_DICT = json.load(f)


def main(start=None,asset=ASSET,base_on_existing=False,verbose=False,
         selected_cols_to_write=['y_0800-1100','y_1100-1415','y_1415-1745','y_0800-1845']):
    candles_csv = get_candles_outpath(asset,add_tzs=True)
    outpath = get_delta_ys_full_df_path(asset)
    delta_ys_lst = DELTA_YS_DICT[asset]

    if base_on_existing:
        existing_df = pd.read_csv(outpath,parse_dates=['date'])
        start = existing_df['date'].max() -td(days=2)
    else:
        existing_df = pd.DataFrame()

    candles_df = pd.read_csv(candles_csv,
                             parse_dates=['time_utc'])[['time_utc','time_chicago', 'open', 'close', 'contract', 'chosen_contract']]
    candles_df['date'] = candles_df['time_utc'].copy()

    contract_mapping = candles_df[['date','contract']].groupby(candles_df.date.dt.date).agg('first')
    contract_mapping = contract_mapping.rename(columns={'date':'date2'}).reset_index()[['date2','contract']].rename(columns={'date2':'date'})
    contract_mapping['date'] += td(hours=8)

    if start is not None:
        candles_df = candles_df[candles_df['date'] >= start]

    hour_minute_delta_ys = []
    for c in delta_ys_lst:
        start_hour = int(c.split('_')[1].split('-')[0][:2])
        start_minute = int(c.split('_')[1].split('-')[0][2:])
        end_hour = int(c.split('_')[1].split('-')[1][:2])
        end_minute = int(c.split('_')[1].split('-')[1][2:])
        hour_minute_delta_ys.append((start_hour, start_minute, end_hour, end_minute))

    # shift so that 12:00 will contain the open(1215) - open(1200
    candles_df['y'] = candles_df['open'].diff().shift(-1)

    cols = []
    y_col = candles_df['y'].copy()
    for start_hour, start_min, end_hour, end_min in hour_minute_delta_ys:
        s = str(start_hour).zfill(2) + str(start_min).zfill(2) + '-' + str(end_hour).zfill(2) + str(end_min).zfill(2)
        c = f'y_{s}'
        if len(cols) % 10 == 0 and verbose:
            print(f'processing {c}')
        cols.append(c)
        new_col = pd.Series(y_col.copy(),name=c)
        #candles_df[c] = new_col
        candles_df = pd.concat([candles_df,new_col],axis=1)
        if start_hour <= end_hour:
            candles_df.loc[(candles_df['date'].dt.time < time(start_hour, start_min)) |
                           (candles_df['date'].dt.time >= time(end_hour, end_min)),
                           c] = 0
        else:
            candles_df.loc[((candles_df['date'].dt.time < time(start_hour, start_min))&
                           (candles_df['date'].dt.time >= time(end_hour, end_min))),
                           c] = 0
            # take back 12 hours to fit to the start date
            candles_df[c] = candles_df[c].shift(-12*4)
    agg_dict = {'open': 'first', 'close': 'last', 'y': 'sum',
                'contract': 'first','time_chicago': 'first','time_utc': 'first'}
    for c in cols:
        agg_dict[c] = 'sum'
    daily_candles = candles_df.groupby(candles_df.date.dt.date).agg(agg_dict)

    daily_candles[cols] *= 1000
    daily_candles[cols] = daily_candles[cols].round(2)
    daily_candles = daily_candles.reset_index()
    daily_candles['date'] = pd.to_datetime(daily_candles['date']) + td(hours=8)

    if existing_df.shape[0] > 0:
        daily_candles = pd.concat([existing_df,daily_candles],axis=0)
        daily_candles = daily_candles.drop_duplicates(subset=['date'])
        daily_candles = daily_candles.sort_values(by='date')

    for c in selected_cols_to_write:
        tmp_outpath = os.path.join(MARKET_DATA_DIRECTORIES_BY_ASSET[asset],'Ys3',f'{c.replace("y","Y")}_15Mins_utc.csv')
        tmp_df = daily_candles[['date','time_chicago','time_utc',c]].rename(columns={c:'y'})
        tmp_df = tmp_df.merge(contract_mapping,on='date',how='left')
        utc_hour = int(c.split('_')[1].split('-')[0][:2])
        tmp_df['time_utc'] = tmp_df['date'] + td(hours=utc_hour-8)
        tmp_df['time_chicago'] = tmp_df['time_utc'].dt.tz_localize('UTC')
        tmp_df['time_chicago'] = tmp_df['time_chicago'].dt.tz_convert('America/Chicago').dt.tz_localize(None)
        tmp_df[['time_chicago','time_utc','y','contract']].to_csv(tmp_outpath,index=False)

    daily_candles[['date']+cols].to_csv(outpath, index=False)


if __name__ == '__main__':
    main(base_on_existing=True,verbose=True)