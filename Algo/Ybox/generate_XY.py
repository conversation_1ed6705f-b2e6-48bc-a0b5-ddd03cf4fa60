import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import os
from Algo.Utils.files_handle import HOME

TIME_KEY = "time_utc"  # "time_chicago" #
COND_ON_YS = lambda y: True  # '1415-1715' in y #"2330" in y

MONTHS = range(1,13) #[10, 11, 12, 1, 2, 3, 4]
LIVE_BOLLINGER_FILE = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_Live_withBollinger.csv")

def get_yfile_by_hm(y_dir,open_hm_tuple, close_hm_tuple):
    os.path.join(HOME,"Market_Data","Ys3","GFS_0Z","Y_0345-1000_15Mins_utc.csv")
    return "%s\\Y_%s%s-%s%s_15Mins_utc.csv"%(y_dir,str(open_hm_tuple[0]).zfill(2),
                                         str(open_hm_tuple[1]).zfill(2),
                                         str(close_hm_tuple[0]).zfill(2),
                                         str(close_hm_tuple[1]).zfill(2))

def _add_bollinger_trends(bollinger_df,bollinger_resolutions):
    # Add macd trends + location trends
    for res in ['15M','30M','1H','2H','4H']:
        if res == '4H':
            a = 1
        if res in bollinger_resolutions:
            if 'H' in res:
                multiplier = int(res.split('H')[0]) * 4
            elif 'M' in res:
                multiplier = int(int(res.split('M')[0])/15)
            else:
                raise AssertionError("invalid res")

            bollinger_df['location_%s_20_diff1'%res] = bollinger_df['location_%s_20'%res].diff(multiplier)
            bollinger_df['location_%s_20_diff2'%res] = bollinger_df['location_%s_20'%res].diff(multiplier*2)

            bollinger_df['macd_sign_%s_diff1'%res] = bollinger_df['macd_sign_%s'%res].diff(multiplier)
            bollinger_df['macd_sign_%s_diff2' % res] = bollinger_df['macd_sign_%s' % res].diff(multiplier*2)
            bollinger_df['macd_sign_%s_diff4' % res] = bollinger_df['macd_sign_%s' % res].diff(multiplier*4)
            bollinger_df['macd_sign_%s_trend4'% res] = (bollinger_df['macd_sign_%s'%res] - bollinger_df['macd_sign_%s'%res].rolling(multiplier*4 ,multiplier*4).mean())
            bollinger_df['macd_sign_%s_trend8' % res] = (bollinger_df['macd_sign_%s' % res] - bollinger_df['macd_sign_%s'%res].rolling(multiplier * 8,multiplier * 8).mean())
    return bollinger_df


def add_bollinger(xy_df, bollinger_file,bollinger_resolutions=["1H"],xy_time_key=TIME_KEY,
                  start=dtdt(2016,1,1)):
    # load
    bollinger_df = pd.read_csv(bollinger_file, parse_dates=['date','time_utc','time_chicago'])
    bollinger_df = bollinger_df[bollinger_df['date']>=start]
    bollinger_df = bollinger_df[['date','time_utc','time_chicago']+[x for x in list(bollinger_df) if \
                                        sum([st in x for st in ['ma','location','signal','sign','macd','price_ref']]) and \
                                        sum([st in x for st in bollinger_resolutions])]]
    # add shift to be able to forecast 30 minutes to the future
    bollinger_df['order'] =1
    bollinger_shifted_15mins = bollinger_df.copy()
    bollinger_shifted_15mins['order'] = 2
    bollinger_shifted_15mins[['time_utc', 'time_chicago']] += td(minutes=15)
    bollinger_shifted_30mins = bollinger_df.copy()
    bollinger_shifted_30mins['order'] = 3
    bollinger_shifted_30mins[['time_utc', 'time_chicago']] += td(minutes=30)
    # combine the shifted dfs
    for complement_df in [bollinger_shifted_15mins,bollinger_shifted_30mins]:
        bollinger_df = bollinger_df.merge(complement_df[-100:],on=list(bollinger_df),
                                          how="outer").sort_values(['date','time_utc','time_chicago','order'])
    bollinger_df = bollinger_df.drop_duplicates(subset=['date', 'time_utc', 'time_chicago'])

    # Add bollinger trends
    bollinger_df = _add_bollinger_trends(bollinger_df,bollinger_resolutions)
    # merge with XY
    merged = xy_df.merge(bollinger_df,on=[xy_time_key],how="left")
    merged = merged[[x for x in list(merged) if ('time' not in x and 'date' not in x) or x == xy_time_key]]

    return merged

def add_prev_ys_features(xy_df,ys_dir,prev_hours_lst=[],xy_time_key=TIME_KEY,
                         start=dtdt(2016,1,1)):
    """
    :param xy_df:
    :param prev_hours_lst: list of tuples [[open_h,open_m],[close_h,close_m]]
    :param bollinger_dict:
    :return:
    """
    for open_close_lst in prev_hours_lst:
        print ('XY | Adding Y: %s | Shape = %s'%(str(open_close_lst),xy_df.shape[0]))
        open_hm, close_hm = open_close_lst[0][0],open_close_lst[0][1]
        y_name = "y_%s%s-%s%s" % (str(open_hm[0]).zfill(2),
                                  str(open_hm[1]).zfill(2),
                                  str(close_hm[0]).zfill(2),
                                  str(close_hm[1]).zfill(2))

        days_diff = open_close_lst[1]
        ref_y_file = get_yfile_by_hm(ys_dir,open_hm,close_hm)
        try: #if os.path.exists(ref_y_file): #if ref_y_df.isna().mean().max() == 1:
            ref_y_df = pd.read_csv(ref_y_file)
            assert xy_time_key in list(ref_y_df)
            ref_y_df[xy_time_key] = pd.to_datetime(ref_y_df[xy_time_key], dayfirst=True)
            ref_y_df[xy_time_key] += td(days=days_diff)
            ref_y_df = ref_y_df[ref_y_df[xy_time_key]>=start]
            ref_y_df = ref_y_df[[xy_time_key,'y']]

            ref_y_df = ref_y_df.rename(columns={"y":y_name})
            ref_y_df['date'] = ref_y_df[xy_time_key].dt.date
            try:
                xy_df = xy_df.merge(ref_y_df,on=['date'],suffixes=("","_%s"%y_name),how="left")
                xy_df = xy_df.drop_duplicates(subset=['date'])
            except:
                bb = 0
        except:
            xy_df[y_name] = np.nan
        try:
            assert xy_df[xy_time_key].iloc[0] > xy_df[xy_time_key+"_%s"%y_name].iloc[0] or days_diff > 0
        except:
            pass

    return xy_df


def generate_xy(x_df,xname, y_filename,xy_dir,y_files_dir,months=MONTHS,y_time_key=TIME_KEY,write=False,
                start=dtdt(2016,1,1)):
    cols_to_drop = list(set(['weight', 'contract', "time_chicago", "time_utc"]) - {y_time_key})
    assert y_time_key not in cols_to_drop

    if '2330' in y_filename:
        jump_1day = True
    else:
        jump_1day = False
    y_part = y_filename.replace("Y_", "").replace(".csv", "")
    x_part = xname.replace("X_file_","") #.replace(".csv","")
    outpath = xy_dir+"\\XY_file_%s_%s"% (y_part,x_part)#,start.strftime('%Y%m%d'))
    outpath.replace("Months=[10-4]","Months=[%s-%s]"%(months[0],months[-1]))

    y_file = y_files_dir+"\\"+y_filename
    y_df = pd.read_csv(y_file,parse_dates=[y_time_key])
    y_df["date"] = (y_df[y_time_key]+td(days=(1 if jump_1day else 0))).dt.date
    y_df = y_df[y_df[y_time_key]>=start]
    xy = y_df[[y_time_key,"date","y"]].merge(x_df, on=['date'],how='outer')
    xy = xy.sort_values("forecast_time")
    h,m = y_df.iloc[0]['time_utc'].hour, y_df.iloc[0]['time_utc'].minute
    xy = xy.dropna(subset=['date'])
    xy['time_utc'] =xy['time_utc'].fillna(xy['date'].apply(lambda x: dtdt(x.year,x.month,x.day,h,m)))
    xy = xy.sort_values("time_utc")
    xy = xy[[x for x in list(xy) if x not in (cols_to_drop+["date","forecast_time"])]]

    # filter momths
    xy = xy[xy[y_time_key].apply(lambda x: x.month in months)]

    #print ("Yfile: %s | abs(move) = %s"%(y_filename,abs(xy["y"]).mean()))
    if write:
        xy.to_csv(outpath,index=False)
    return xy, outpath


main_prices_list = [([[0, 0], [23, 30]], 0),
                        ([[0, 0], [4, 0]], 0),
                        ([[4, 0], [6, 0]], 0),
                        ([[0, 0], [6, 0]], 0),
                        ([[6,0],[7,0]],0),
                        ([[7,0],[8,0]],0),
                        ([[6,0],[8,0]],0),
                             ([[4, 0], [5, 30]],0),
                            ([[4, 30], [5, 30]],0),
                            ([[5, 0], [5, 30]],0),
                            ([[5, 30], [6, 30]],0),
                             ([[5, 30], [7, 0]],0),
                            ([[5, 30], [7, 30]],0),
                            ([[5, 30], [8, 30]],0),
                            ([[7, 0], [8, 0]],0),
                            ([[7, 0], [7, 15]],0),
                            ([[7, 0], [7, 30]],0),
                            ([[7,30], [8, 0]],0),
                            ([[9, 0], [9, 30]],0),
                            ([[7, 0], [9, 0]],0),

                            ([[7, 0], [9, 30]],0),
                            ([[7, 45], [8, 15]],0),
                            ([[7, 45], [11, 0]],0),
                            ([[7, 45], [8, 0]],0),
                            ([[8, 0], [8, 30]],0),
                            ([[8, 45], [9, 15]],0),
                            ([[7, 45], [10, 30]],0),
                            ([[8, 0], [9, 0]], 0),
                            ([[8, 0], [10, 30]], 0),
                            ([[8, 0], [11, 0]], 0),
                            ([[9, 0], [10, 0]], 0),
                            ([[9, 0], [10, 30]],0),
                            ([[8, 45], [9, 0]],0),

                            ([[9, 30], [11, 0]],0),
                            ([[9, 45], [10, 0]],0),
                            ([[10, 0], [11, 0]],0),
                            ([[10, 0], [10, 30]],0),
                            ([[10, 30], [11, 0]],0),
                            ([[10, 45], [11, 15]], 0),

                        ([[14, 15], [15, 0]],0),
                        ([[14, 15], [15, 15]],0),
                        ([[14, 15], [16, 45]],0),
                        ([[14, 15], [17, 45]],0),
                        ([[13, 15], [15, 15]],0),
                        ([[13, 15], [16, 15]],0),
                        ([[13, 15], [17, 15]],0),
                        ([[13, 15], [17, 45]],0),
                        ([[14, 45], [17, 45]],0),
                        ([[14, 0], [14, 15]],0),
                        ([[14, 15], [14, 30]],0),
                        ([[14, 15], [14, 45]],0),
                        ([[14, 0], [14, 30]],0),
                        ([[14, 15], [17, 15]],0),
                        ([[14, 15], [16, 45]],0),
                        ([[14, 30], [17, 0]],0),
                        ([[14, 30], [17, 15]],0),

                        ([[15, 30], [16, 45]],0),
                        ([[15, 30], [17, 15]],0),
                        ([[15, 15], [16, 45]],0),
                        ([[15, 15], [15, 45]],0),
                        ([[15, 15], [16, 15]],0),
                        ([[15, 15], [17, 15]],0),
                        ([[15, 15], [17, 45]],0),
                        ([[15, 15], [17, 0]],0),
                        ([[15, 0], [17, 15]],0),
                        ([[14, 15], [17, 0]],0),
                        ([[14, 45], [16, 45]],0),
                        ([[14, 45], [17, 15]],0),
                        ([[15,45], [16, 15]],0),
                        ([[16, 15], [17, 45]],0),
                        ([[16, 30], [17, 45]],0),
                        ([[16, 45], [17, 45]],0),

                        ([[17, 0], [17, 45]],0),
                        ([[17, 15], [17, 45]],0),
                        ([[17, 15], [18, 15]],0),

                        #([[9,45],[11,45]],0),

                        ([[11,30],[14,15]],0),
                        ([[11,30],[13,15]],0),
                        ([[11,45],[12,15]],0),
                        ([[11,45],[14,15]],0),
                        ([[12,0],[14,15]],0),
                        ([[12,0],[14,30]],0),
                        ([[12,0],[14,0]],0),
                        ([[12,0],[12,45]],0),
                        ([[12,30],[13,15]],0),
                        ([[12,15],[12,30]],0),
                        ([[11,0],[11,30]],0),
                        ([[11,15],[11,30]],0),
                        ([[11,30],[12,0]],0),
                        ([[11,30],[12,30]],0),
                        ([[12,30],[13,15]],0),
                        ([[12,15],[12,45]],0),
                        ([[12,15],[13,15]],0),

                        ([[13,15],[14,15]],0),
                        ([[12,45],[13,15]],0),

                        ([[12,45],[13,30]],0),
                        ([[13,0],[13,15]],0),
                        ([[13,15],[13,45]],0),
                        ([[13,45],[14,0]],0),
                        ([[13,45],[14,15]],0),

                        ([[13,0], [13,30]],0),
                        ([[13, 0], [14, 0]],0),
                        ([[14, 0], [15, 0]],0),
                        ([[15, 0], [15, 15]],0),

                        #([[13,45],[14,0]],0),
                        #([[14,0],[14,15]],0),
                        #([[14,0],[14,30]],0),
                        ]
evening_prices_list = [
                    ([[16,45],[17,45]],0),
                    ([[17,0],[17,45]],0),
                    ([[17,15],[17,45]],0),
                    ([[17,0],[17,30]],0),
                    ([[17,45],[18,15]],0),
                    ([[17,45],[18,45]],0),
                    ([[18,15],[18,45]],0),
                    ([[18,45],[19,15]],0),
                    ([[18,45],[19,45]],0),
                    ([[19,15],[21,0]],0),
                    ([[19,15],[22,0]],0),
                    ([[20,0],[21,45]],0),

                    ([[17,30],[18,0]],0),
                    ([[18,0],[18,30]],0),
                    ([[18,0],[19,0]],0),
                     ([[19,0],[19,30]],0),
                    ([[19,0],[20,0]],0),

                    ([[19,45],[20,45]],0),
                    ([[19,45],[20,30]],0),
                    ([[19,45],[21,45]],0),
                    ([[20,30],[21,0]],0),
                    ([[20,45],[21,45]],0),
                    ([[21,0],[21,45]],0),
                    ([[22,0],[23,30]],0),
                    ([[22,0],[23,0]],0),
                    ([[23,0],[23,45]],0),
                    ([[21,45],[23,30]],0),
                    ([[21,45],[23,45]],0),
                    ([[21,45],[22,45]],0),
                    ([[19,45],[0,0]],0), ### might make problems
                    ([[0,0],[3,0]],0),
                    ([[0,0],[4,0]],0),
                    ([[23,0],[0,0]],0),
                    ([[0,0],[1,0]],0),
                    ([[1,0],[2,0]],0),
                    ([[2,0],[3,0]],0),
                    ([[0,0],[3,0]],0),

                        ]
production_price_list = [
                        ([[10,0],[12,0]],0),
                        ([[12,0],[15,15]],0),
                        ([[15,15],[17,45]],0),
                        ([[8,0],[18,45]],0)
                        ]

y_ref_hours_dict = {0: [
                        ([[1,0],[3,30]],0),
                        ([[19,0],[20,30]],1) # day before
                        ],
                    12:
                        main_prices_list+\
                        evening_prices_list+production_price_list
                        }


def main_generator(last_model,cond_on_xs,cond_on_ys, market_features=True, bollinger_file=LIVE_BOLLINGER_FILE,
                   bollinger_resolutions=['15M','1H','4H'],x_suffix='take7',xy_suffix="_PrevYs+Bollinger_v3", ys_dir = 'Ys3'
                   ,asset='NG',start=dtdt(2016,1,1)):

    asset_dir_str = '' if asset == 'NG' else asset
    xy_dir = os.path.join(HOME,"%s","XYs","Full2019_prev24")%(asset_dir_str)
    y_ref_hours = y_ref_hours_dict[last_model]
    if last_model == 0:
        x_dir = os.path.join(HOME,"Xdata","X_files_GFS_0Z_prev24")
        y_files_dir = os.path.join(HOME,"%s","Market_Data","%s")%(asset_dir_str,ys_dir)
        reference_Ys_dir = y_files_dir
    elif last_model == 12:
        x_dir = os.path.join(HOME,"Xdata","X_files_GFS_12z_prev24") #_Session_Opening+GFS12Z
        y_files_dir = os.path.join(HOME,"%s","Market_Data","%s")%(asset_dir_str,ys_dir) # GFS_0Z" #
        reference_Ys_dir = y_files_dir
    else:
        raise AssertionError("Bad last_model")
    for xname in os.listdir(x_dir):
        if cond_on_xs(xname):
            x_file = x_dir+"\\"+xname
            if x_suffix in xname:
                x_df = pd.read_csv(x_file,parse_dates=["forecast_time"]).drop_duplicates(subset=['forecast_time'])
                x_df["date"] = x_df['forecast_time'].dt.date
                x_df = x_df[x_df['forecast_time']>=start]
                for y_filename in os.listdir(y_files_dir):
                    print('XY building | bringing Yfile: %s'%y_filename)
                    if cond_on_ys(y_filename):
                        xy_df, xy_outpath = generate_xy(x_df,xname,y_filename,xy_dir,y_files_dir,write=False,
                                                        start=start)
                        new_outpath = xy_outpath.replace(".csv", "%s.csv" % xy_suffix)
                        try:
                            original_df = pd.read_csv(new_outpath,parse_dates=[TIME_KEY])
                        except:
                            original_df = pd.DataFrame()

                        xy_df['date'] = xy_df[TIME_KEY].dt.date
                        if market_features:
                            print ('adding prevYs')
                            xy_df = add_prev_ys_features(xy_df,reference_Ys_dir,prev_hours_lst=y_ref_hours,
                                                         start=start)
                            print('adding Bollinger')
                            xy_df = add_bollinger(xy_df,bollinger_file,bollinger_resolutions=bollinger_resolutions)
                        if original_df.shape[0] > 0:
                            common = [c for c in list(xy_df) if c in list(original_df)]
                            xy_df = original_df.merge(xy_df,on=common,how='outer').sort_values(TIME_KEY)
                            xy_df = xy_df.drop_duplicates(subset=[TIME_KEY],keep='last')
                        print ('Handling %s'% new_outpath)
                        xy_df.to_csv(new_outpath,index=False)


if __name__ == "__main__":
    last_model = 12
    cond_on_xs = lambda x: 'd=[1-15]' in x
    allowed_y_strings = ['1315-','1415','1445-','1515-','1530-','1500']
    allowed_y_strings = ['1230']
    if last_model == 12:
        cond_on_ys = lambda x: sum([st in x for st in allowed_y_strings])  # ,'1430','1445','1515','1530']])
    else:
        cond_on_ys = lambda x: sum([st in x for st in ['0345', '0445']])

    main_generator(last_model,cond_on_xs,cond_on_ys)