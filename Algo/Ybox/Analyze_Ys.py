import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc

import plotly.graph_objects as go
import time
import sys

from Algo.Conf.assets import Asset
import os
from Algo.Utils.files_handle import HOME

LOCAL_TZ = pytz.timezone("Asia/Jerusalem")
CET_TZ = pytz.timezone("CET")
CHICAGO_TZ = pytz.timezone("America/Chicago")


USE_OLD_FRIDAYS_LOGIC = False
"""
{'GFS': {'start': [3, 40],
'mid': [4,15],
'end': [4,40]}}
"""
MODELS_LATENCIES = {'GFS': [4,0],'GEFS': [4, 50], 'PARA': [5, 30], 'GEM': [4, 0], 'GEPS': [5, 15],
                    'EC': [6,5],'EPS': [7,15],'CFS': [9,5],'GEFSL': [8,0]}
MODELS_RUNS = {'GFS': [0, 6, 12, 18], 'GEFS': [0, 6, 12, 18], 'PARA': [0, 6, 12, 18],
               'GEM': [0, 12], 'GEPS': [0, 12], 'CFS': [0,12],'EC': [0,12],'EPS':[0,12],'GEFSL': [0]}
outpath = os.path.join(HOME,"Market_Data","","NG_2018-19_frontMonth.csv")
market_data_dirs_dict = {'NG': os.path.join(HOME,"Market_Data","months"),
                         'CORN': os.path.join(HOME,"CORN","Market_Data","months"),
                         'COFFEE': os.path.join(HOME,"COFFEE","Market_Data","months"),
                         'NCF': os.path.join(HOME,"Market_Data_General","Coal","months"),
                         'NQ': os.path.join(HOME,"Market_Data_General","Nasdaq","months")}

MONTHS = range(1,13) #[10,11,12,1,2,3,4]
BOLLINGER_WINDOWS_DICT = {res: [20] for res in ['15M', '30M', '1H', '2H', '4H','1D']}
BOLLINGER_WINDOWS_DICT.update({'1D':[10,20],'1H':[10,20]})

DAYS_BEFORE_EXPIRY = {'NG':9,'COFFEE':9,'CORN':9,'NCF':2,
                      'NQ':1}  # 5 for COFFEE, 2, 30
assert DAYS_BEFORE_EXPIRY['NG'] == 9

def add_dates(df,original_tz=LOCAL_TZ):
    df['time_jerusalem'] = df['date'].apply(lambda x: original_tz.localize(x, is_dst=None))
    df['time_utc'] = df['time_jerusalem'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))
    df['time_chicago'] = df['time_jerusalem'].apply(lambda x: x.astimezone(CHICAGO_TZ).replace(tzinfo=None))
    df['time_jerusalem'] = df['time_jerusalem'].apply(lambda x: x.replace(tzinfo=None))
    return df
    #pass

"""
df = add_dates(df)
for c in 'open,close,high,low,avg'.split(","):
    df[c] = df[c].apply(lambda x: round(x, 4))
df.to_csv(outpath.replace(".csv","_tz.csv"),index=False)
"""

def add_grouper_column(df,dest_resolution='1H',tz='utc'):
    assert dest_resolution in ['15M','30M','1H','2H','4H','1D']
    if dest_resolution == '15M':
        df['grouper_%s' % dest_resolution] = df['time_%s' % tz].dt.strftime("%H:%M")
    elif dest_resolution == '30M':
        df['grouper_%s' % dest_resolution] = list(zip(df['time_%s' % tz].dt.hour,(df['time_%s' % tz].dt.minute / 30).astype(int)))
        df['grouper_%s' % dest_resolution] = df['grouper_%s' % dest_resolution].astype(str)
    elif dest_resolution == '1H':
        df['grouper_%s'%dest_resolution] = df['time_%s'%tz].dt.hour
    elif dest_resolution == '2H':
        df['grouper_%s' % dest_resolution] = (df['time_%s'%tz].dt.hour /2).apply(lambda x: int(x))
    elif dest_resolution == '4H':
        #df['grouper_%s'%dest_resolution] = (((((df['time_%s'%tz].dt.hour + 2) % 24) / 4).apply(lambda x: int(x)) * 4) - 2) % 24
        df['grouper_%s_a' % dest_resolution] = (((((df['time_%s' % tz].dt.hour + 2) % 24) / 4).apply(lambda x: int(x)) * 4) - 2) % 24
        df['grouper_%s_b' % dest_resolution] = (df['time_%s' % tz]+td(hours=2)).dt.strftime("%Y%m%d")
    elif dest_resolution == '1D':
        df['grouper_%s'%dest_resolution] = (df['time_chicago'] + td(hours=7)).dt.date
    return df

def group_candles(df,res='15M'):
    d = df.reset_index().iloc[0]
    try:
        if d['date'].day % 10 == 0 and (d['grouper_15M'] == '00:00'):
            print ("Reached %s for Resolution: %s"%(d['date'], res))
    except:
        aa = 1

    groups_open = df.iloc[0]['open']
    groups_close = df.iloc[-1]['close']
    groups_avg = df['avg'].mean()
    groups_vol = df['vol'].sum()
    groups_high = df['high'].max()
    groups_low = df['low'].min()

    grouped_df = df[:1].copy()
    grouped_df["open"] = groups_open
    grouped_df['close'] = groups_close
    grouped_df['vol'] = groups_vol
    grouped_df['avg'] = groups_avg; grouped_df['high'] = groups_high; grouped_df['low'] = groups_low

    return grouped_df[[x for x in list(grouped_df) if x not in ['grouper_%s'%res,'date']]]

def add_bollinger(df, resoultion='15M',window=20,K=2,price_col='open',
                  add_gap=True):
    try:
        assert price_col in list(df),'price - %s -  not in list(df)' % price_col
        assert resoultion in ['15M','30M','1H','2H','4H','1D'], 'invalid resolution'
    except:
        raise

    # calculate Simple Moving Average with 20 days window
    sma = df[price_col].rolling(window=window).mean()
    # calculate the standar deviation
    rstd = df[price_col].rolling(window=window).std()

    upper_band = sma + 2 * rstd
    lower_band = sma - 2 * rstd
    df['ma_%s_%s'%(resoultion, window)] = sma
    df['upper_%s_%s'%(resoultion, window)] = upper_band
    df['lower_%s_%s'%(resoultion, window)] = lower_band
    if add_gap:
        gap = upper_band - lower_band
        df['location_%s_%s' % (resoultion,window)] = (df['open'] - sma) / gap
    df['price_ref_%s_%s'%(resoultion,window)] = df[price_col]
    return df

def add_macd(df,resolution,price_col='close'):
    exp1 = df[price_col].ewm(span=12, adjust=False,min_periods=10).mean()
    exp2 = df[price_col].ewm(span=26, adjust=False,min_periods=10).mean()
    macd = exp1 - exp2
    signal_line = macd.ewm(span=9, adjust=False,min_periods=10).mean()
    df['signal_line_%s'%resolution] = signal_line
    df['macd_%s' % resolution] = macd
    df['macd_sign_%s'%resolution] = macd - signal_line
    return df

def create_deltaYs(path,outdir,strategy,tz="chicago",plot=False,write=True,base_on_existing=False):
    assert tz in ['chicago','utc']
    df = pd.read_csv(path, parse_dates=['date','time_jerusalem','time_utc','time_chicago'])

    strategy_open_ref_time = dtdt(1990, 1, 1, strategy[0][0], strategy[0][1])
    strategy_close_ref_time = dtdt(1990, 1, 1, strategy[1][0], strategy[1][1])
    strategy_str = "%s-%s" % (strategy_open_ref_time.strftime("%H%M"), strategy_close_ref_time.strftime("%H%M"))
    outfile = r"%s\Y_%s_15Mins_%s.csv" % (outdir, strategy_str, tz)

    original_df = pd.DataFrame()
    try:
        original_df = pd.read_csv(outfile,parse_dates=['time_chicago','time_utc']).dropna(susbet=['y'])
        last_existing_time = original_df.iloc[-1]['time_%s'%tz]
    except:
        last_existing_time = dtdt(2017,11,1)

    if base_on_existing:
        df = df[df['time_%s'%tz]>=last_existing_time - td(days=1)]

    df['%s_hour'%tz] = df['time_%s'%tz].dt.hour
    df['%s_min'%tz] = df['time_%s'%tz].dt.minute
    df['%s_ref_time'%tz] = df['time_%s'%tz].apply(lambda x: x.replace(year=1990,month=1,day=1))
    df['is_trade'] = False

    #df = df[(df['%s_ref_time'%tz] >= strategy_open_ref_time)&(df['%s_ref_time'%tz] <= strategy_close_ref_time)]

    df['is_open'] = False
    df['is_close'] = False
    df['is_second'] = False
    df['price'] = np.nan

    df['is_open'][df['%s_ref_time'%tz] == strategy_open_ref_time] = True
    df['is_close'][df['%s_ref_time'%tz] == strategy_close_ref_time] = True
    df['is_second'][df['%s_ref_time'%tz] == strategy_open_ref_time+td(minutes=15)] = True
    df['price'][df['is_open']] = df['open'][df['is_open']]
    df['price'][df['is_close']] = df['open'][df['is_close']]
    df['price'] = df['price'].fillna(method='bfill')
    df['price_diff'] = df['price'].diff() * 1000
    df['price_diff_abs'] = abs(df['price_diff'])

    for time_col in ['date','time_chicago','time_jerusalem','time_utc']:
        df[time_col] -=td(minutes=15)

    df2 = df[df['is_second']]
    if plot:
        df2.groupby(df2['time_%s'%tz].dt.month).mean()['price_diff_abs'].plot(kind='bar')
    df2['y'] = df['price_diff']
    print('Strategy: %s | Avg movement in 10-3: %s' % (str(strategy),df2[df2['time_%s'%tz].dt.month.isin([11,12,1,2,3])]['price_diff_abs'].mean()))

    if base_on_existing and original_df.shape[0]>0:
        df2 = original_df.merge(df2,on=list(original_df),how='outer').drop_duplicates(subset=['time_utc'],keep='first').sort_values('time_utc')
    if write:
        df2[['time_chicago','time_utc','y','contract']].to_csv(outfile, index=False)
    if plot:
        plt.show()
    return df2

def plot_candles(aggregated_df,tz):
    aggregated_df = aggregated_df.rename(columns={'vol': 'volume'})
    aggregated_df['date'] = aggregated_df['time_%s'%tz].apply(mdates.date2num)
    aggregated_df = aggregated_df[['date', 'open', 'high', 'low', 'close', 'volume']]
    f1 = plt.subplot()  # 2grid((6, 4), (1, 0), rowspan=6, colspan=4)
    candlestick_ohlc(f1, aggregated_df.values, width=.6, colorup='#53c156', colordown='#ff1717')
    f1.xaxis_date()
    f1.xaxis.set_major_formatter(mdates.DateFormatter('%y-%m-%d %H:%M:%S'))

    plt.xticks(rotation=45)
    plt.ylabel('Stock Price')
    plt.xlabel('Date Hours:Minutes')
    plt.tight_layout()
    plt.show()
    aa = 1

def wrap_bollingers_generation(path,write=False,base_on_existing=True,windows_dict=BOLLINGER_WINDOWS_DICT,price_col='close',
                               start=dtdt(2018,1,1),end=dtdt(2030,1,1),suffix=""):
    print ('Generating Bollinger data with start = %s | end = %s'%(start,end))
    resolutions = ['15M', '30M', '1H', '2H', '4H','1D']
    original_df = pd.DataFrame()

    outpath = "%s_withBollinger%s.csv"%(path.split(".csv")[0],suffix)
    if base_on_existing:
        if os.path.exists(outpath):
            original_df = pd.read_csv(outpath,parse_dates=['date','time_utc','time_chicago'])

    df = pd.read_csv(path, parse_dates=['date','time_jerusalem','time_utc','time_chicago'])
    first_df = pd.DataFrame()

    df = df[(df['time_chicago'] <= end) & (df['time_chicago'] >= start)]
    df['date'] = df.time_utc.apply(lambda x: x.replace(hour=0, minute=0))
    for res in resolutions: # ['15M','1H','4H']: #
        print ("Building bollinger for resolution: %s" % res)
        if res == '4H':
            aa = 1
        df = add_grouper_column(df,dest_resolution=res)
        if res != '15M':
            if res  == '4H':
                groupers =['grouper_4H_a', 'grouper_4H_b']
            elif res == '1D':
                groupers = ['grouper_%s' % res]
            else:
                groupers = ['date', 'grouper_%s' % res]
            aggregated_df = df.groupby(groupers).apply(lambda x: group_candles(x,res))
            aggregated_df = aggregated_df[[x for x in list(aggregated_df) if x not in groupers]].reset_index()
            aggregated_df['date'] = pd.to_datetime(aggregated_df.time_utc.dt.date)
        else:
            aggregated_df = df
        if aggregated_df.shape[0] == 0:
            raise AssertionError("aggregated bollinger df is empty")
        aggregated_df = aggregated_df.sort_values(['time_utc'])

        aggregated_df = add_macd(aggregated_df, res, price_col)
        date_cols = ['date', 'time_chicago', 'time_utc']
        for window in windows_dict[res]:
            print ('Window: %s'%window)
            aggregated_df2 = aggregated_df[[x for x in list(aggregated_df) if x != 'level_2']]
            aggregated_df2 = add_bollinger(aggregated_df2,resoultion=res,window=window,K=2,price_col=price_col)
            aggregated_df = aggregated_df.merge(aggregated_df2[date_cols+[x for x in list(aggregated_df2) if x.endswith('_%s'%window)]],on=date_cols)

        # drop nans
        aggregated_df = aggregated_df2.dropna(subset=['open','close','high','low'])
        ### merge the different resolutions
        if first_df.shape[0] == 0:
            first_df = aggregated_df
        else:
            if aggregated_df.shape[0] == 0:
                t0 = dtdt(2017,10,1)
            else:
                t0 = aggregated_df.iloc[0]['time_utc']
            first_df = first_df[first_df['time_utc']>=t0]

            cols = date_cols + [x for x in list(aggregated_df) if sum([y in x for y in \
                                    ['ma_%s'%(res),'upper_%s'%(res),'lower_%s'%(res),'macd_%s'%(res),
                                     'signal_line_%s'%(res),
                                     'macd_sign_%s'%(res),'location_%s'%(res),'price_ref_%s'%(res)]])]
            first_df = first_df.merge(aggregated_df[cols], on=date_cols,how='outer')
        aa = 1

    cols_to_backfill = [x for x in list(first_df) if sum([st in x for st in resolutions[1:]]) and 'location' not in x]
    location_cols = [x for x in list(first_df) if 'location' in x]
    final = first_df.copy()
    final[cols_to_backfill] = first_df[cols_to_backfill].fillna(method='ffill').fillna(method='bfill')
    for location_col in location_cols:
        res = location_col.split("_")[1]
        window = location_col.split("_")[2]
        new_location = (final['open']- final['ma_%s_%s'%(res,window)]) / (final['upper_%s_%s'%(res,window)] - final['lower_%s_%s'%(res,window)])
        final[location_col] = final[location_col].fillna(new_location)

    a = 1
    numeric = 'open,close,high,low,avg,vol'.split(",")+ ['%s_%s_20'%(name, res) for res in resolutions for name in ['ma','upper','lower']]
    numeric += ['location_%s_%s'%(res,window) for res in resolutions]
    numeric += ['macd_%s'%res for res in resolutions] + \
               ['signal_line_%s' % res for res in resolutions] + \
               ['macd_sign_%s' % res for res in resolutions]

    #for c in numeric:
    final[numeric] = final[numeric].apply(lambda x: round(x,4))
    dates = ['date','time_utc','time_chicago']
    final = final[dates + [x for x in list(final) if x not in dates+['time_jerusalem']]]


    final['contract'] = final['contract'].astype(float)
    if original_df.shape[0] and base_on_existing:
        original_df['contract'] = original_df['contract'].astype(float)
        try:
            common = [x for x in list(original_df) if x in list(final)]
            final = original_df.merge(final,on=common,how="outer")
        except:
            for c in list(final):
                if c in list(original_df):
                    if final[c].dtype != original_df[c].dtype:
                        print(f'final[c].dtype = {final[c].dtype} | original[c].dtype = {original_df[c].dtype}')
            raise
    final = final.drop_duplicates(subset=['time_utc'])

    final = final.sort_values("time_utc")
    del original_df

    time.sleep(1)
    if write:
        final.to_csv(outpath, index=False)


def wrap_delta_Ys_generation(candles_file_tz,outdir,strategies,write=True,tz='utc',
                             months=MONTHS,base_on_existing=False):

    strats_df = pd.DataFrame()
    first_str = None
    for strat in strategies:
        print ("preparing strategy: %s"%strat)
        strat_str = '%s%s-%s%s'%(strat[0][0],str(strat[0][1]).zfill(2),strat[1][0],str(strat[1][1]).zfill(2))
        if '1130-1415' in strat_str:
            aa =1
        current_df = create_deltaYs(candles_file_tz,outdir,strat,tz,write=write,base_on_existing=base_on_existing)
        current_df = current_df[current_df['time_%s'%tz].dt.month.isin(months)]
        current_df['date'] = current_df['time_%s'%tz].dt.date
        current_df = current_df[['date','y']]
        print('strat: %s | last 5: %s' % (strat, current_df[-5:].set_index('date')['y']))
        if strats_df.shape[0] == 0:
            strats_df = current_df
            first_str = strat_str
        else:
            strats_df = strats_df.drop_duplicates(subset=['date'])
            strats_df = strats_df.merge(current_df,on=['date'],how="outer",suffixes=("","_%s"%strat_str))
            print ('after merging, shape was: %s'%str(strats_df.shape))
    strats_df = strats_df.rename(columns={'y':'y_%s'%first_str})
    return strats_df

def analyze_y_to_y_relation(strats_df,months=MONTHS):
    col1 = 'y_445-545'#'y_630-800'
    col2s = ['y_1400-1600','y_1400-1430','y_1300-1330',
             'y_1300-1315','y_1300-1330','y_1315-1330','y_1315-1345',
              'y_1400-1430','y_1415-1430','y_1415-1715','y_1315-1615','y_1615-1715','y_1415-1615']
    col2s = ['y_1245-1300','y_1245-1315','y_1315-1345','y_1345-1415','y_1245-1345','y_1415-1715',
             'y_1415-1445','y_1415-1615']

    d = os.path.join(HOME,"Market_Data","XYs_marketFeatures")
    conf = 'XY_1245-1330'
    f = "%s\\%s.csv" % (d,conf)
    target_col = 'y_%s' % conf.split("XY_")[-1]
    fts_cols = ['y_445-545','y_1045-1145','y_630-700','y_630-800'] #,'y_1245-1315','y_1315-1345','y_1345-1415','y_1315-1415']
    strats_df['time'] = strats_df['date'].apply(lambda x: dtdt(x.year,x.month,x.day))
    strats_df[['time',target_col]+fts_cols].to_csv(f,index=False)

    T = 1000
    for col in col2s:
        strats_df[col] = strats_df[col].apply(lambda x: min(x,T) if x>0 else max(x,-T))

    weekdays = [0,1,2,3,4]
    strats_df = strats_df[strats_df['date'].apply(lambda x: x.weekday() in weekdays and x.month in months)]

    stack = []
    ratios = [0.9,0.7,0.5,0.4,0.3,0.2,0.1]
    df_hr = pd.DataFrame({"ratio": ratios})
    for col2 in col2s:
        col2_hrs = []
        for trades_ratio in ratios:
            ratio = 1 - trades_ratio
            quant = abs(strats_df[col1]).quantile(ratio)
            #strats_df2 = strats_df[abs(strats_df[col1]) >= quant]
            # (abs(strats_df['y_700-800'])>=abs(strats_df['y_700-800']).quantile(ratio))&
            strats_df2 = strats_df[(abs(strats_df[col1])>=abs(strats_df[col1]).quantile(ratio))]
            #strats_df2[[col1,'y_1315-1345']].plot(x=col1,y='y_1315-1345',kind='scatter')

            strats_df2['is_hit'] = np.sign(strats_df2[col1]*strats_df2[col2]).apply(lambda x: x if x ==1 else 0)
            hr = strats_df2['is_hit'].mean()
            print('%s Vs %s | %s'%(col1,col2,hr))
            col2_hrs.append(hr)
        df_hr[col2] = col2_hrs
    df_hr.plot(kind='bar',x='ratio',title='HR between Avg (10-12) to Afternoon [3,4,5,6] x weekdays=all')
    plt.show()

def _parse_ib_trades_csv(csv,year,month,asset_obj=None):
    df = pd.read_csv(csv, names=['date+open', 'high', 'low', 'close', 'vol', 'avg', 'bar_count', 'contract', 'spam'])
    # df['contract'] = df['contract'].fillna(method='ffill').astype(int).astype(str)

    if df.shape[0] == 0:
        return df
    df['open'] = df['date+open'].apply(lambda x: str(x).split(":")[-1])
    df['date'] = df['date+open'].apply(lambda x: ":".join(str(x).split(":")[:-1]))
    df = df[['date', 'open', 'close', 'high', 'low', 'avg', 'vol', 'contract']]

    df['date'] = pd.to_datetime(df['date'], format="%Y%m%d %H:%M:%S")
    contract_ref_dt = (dtdt(year, month, 1) - td(days=1)).replace(day=26)

    if asset_obj is not None:
        pass
        # days_to_exp = asset_obj.days_to_exp
        # if days_to_exp < 0:
        #     contract_ref_dt = contract_ref_dt + td(days=days_to_exp)
    df['diff_from_expiry'] = (df['date'] - contract_ref_dt).dt.total_seconds() / 3600.0 / 24
    df = df[df['contract'].astype(float)>201700]
    return df


def get_last_fridays(df,time_col='date', days_before_expiration=9):
    """
    return last trading day of the last desired week of the month
    if no friday will return Thu/Wed/..
    :param df:
    :param time_col:
    :param days_before_expiration:
    :return:
    """
    # Add a column to represent the week number for each date
    df['week'] = df[time_col].dt.week
    df['week2'] = df[time_col] - df[time_col].dt.weekday.apply(lambda x: td(days=x))

    # Add a column to represent the month number for each date
    df['month'] = df[time_col].dt.month
    df['year'] = df[time_col].dt.year

    # Find the last full week for each month where the last day is at least 5 days away from month end
    last_weeks = df[(df['week2'].dt.day <= (df[time_col].dt.daysinmonth - days_before_expiration -1))&
                    (df['week2'].dt.day > (df[time_col].dt.daysinmonth - days_before_expiration - 1 - 12))
                    ].groupby(['year', 'month']).last().reset_index()#.groupby(['year','month']).max()
    return last_weeks

def _calc_next_contract(current_contract_str, asset):
    if isinstance(current_contract_str,float):
        current_contract_str = int(current_contract_str)
    if asset in ['NG','NCF','MBT','GC']:
        return int((dtdt(int(float((str(current_contract_str)[:4]))), int(float(str(current_contract_str)[-2:])), 20) + td(days=14)).strftime("%Y%m"))
    elif asset in ['CORN','COFFEE']:
        current_month_dt = dtdt(int(float(str(current_contract_str)[:4])), int(float(str(current_contract_str)[-2:])), 20)
        if current_month_dt.month not in [9,12]:
            days_gap = 60
        else:
            days_gap = 90
        return int((current_month_dt+td(days=days_gap)).strftime('%Y%m'))
    elif asset in ['NQ','ES']:
        current_month_dt = dtdt(int(str(current_contract_str)[:4]), int(str(current_contract_str)[-2:]), 20)
        days_gap = 90
        return int((current_month_dt+td(days=days_gap)).strftime('%Y%m'))

def _filter_contract_old_method(final_df, days_before_expiry,resolution):
    if days_before_expiry >= 9:
        last_friday_of_contract_indices = (np.array(final_df[(1 < final_df['diff_dt'].rolling(3, 3).max().shift(
            -3)) & (final_df['diff_dt'].shift(-1) < 3.5) & (
                                                                     final_df[
                                                                         'diff_from_expiry'] > -days_before_expiry)].drop_duplicates(
            subset='contract', keep='last').index.tolist()) - 1).tolist()
    else:  # We dont necessarily have fridays in this range, we will take just the closest day to expiration
        # if days_before_expiry >= 0:
        cond1 = (final_df['diff_from_expiry'] < -days_before_expiry)
        cond2 = final_df['diff_from_expiry'] > -9
        cond3 = (final_df['diff_dt'] > 0.011) # allow every midnight
        last_friday_of_contract_indices = final_df.loc[cond1 & cond2 & cond3].drop_duplicates(
            subset='contract', keep='last').index.tolist()
    final_df = final_df.reset_index()
    final_df = final_df.sort_values(['contract'], ascending=True).sort_values(['date'])
    final_df = final_df.reset_index()

    if len(last_friday_of_contract_indices) > 0:
        final_df['switch_contract'][final_df['index'].isin(last_friday_of_contract_indices)] = True
    else:
        final_df['chosen_contract'] = final_df['contract'].rolling(3, 3).min()
    final_df['chosen_contract'][final_df['switch_contract']] = final_df['next_contract'][
        final_df['switch_contract']]
    final_df['chosen_contract'] = final_df['chosen_contract'].fillna(method='ffill')
    if resolution == '5m' and len(last_friday_of_contract_indices) > 0:
        final_df.loc[last_friday_of_contract_indices[0] - 1, 'chosen_contract'] = final_df.loc[
                                                                                  last_friday_of_contract_indices[
                                                                                      0] - 10:
                                                                                  last_friday_of_contract_indices[
                                                                                      0] - 1, 'contract'].min()
    final_df['chosen_contract'] = final_df['chosen_contract'].fillna(method='bfill')

    matching_contract_cond = final_df['contract'] == final_df['chosen_contract']
    last_date = final_df[matching_contract_cond]['date'].max()
    last_diff_from_expiry = final_df.set_index('date').loc[last_date]['diff_from_expiry']
    if not isinstance(last_diff_from_expiry, float):
        last_diff_from_expiry = final_df[matching_contract_cond].set_index('date').loc[last_date]['diff_from_expiry']
    if last_diff_from_expiry + 1 < -days_before_expiry:
        multiplier = 1 if resolution in ['1h','1H','1hour'] else 4
        additional_cond = final_df['chosen_contract'].shift(24*multiplier) == final_df['contract']
        additional_cond = additional_cond & (final_df['date'] >= last_date)
    else:
        additional_cond = False
    final_df2 = final_df[matching_contract_cond | additional_cond]
    # final_df[['date','contract','next_contract','switch_contract','chosen_contract']][final_df['switch_contract']]

    final_df2 = final_df2[
        [x for x in list(final_df2) if x not in ['level_0', 'index', 'diff_from_expiry  ', 'diff_dt']]]
    return final_df2

# take monthly files to 1 big candle file
def unite_monthly_contracts(outpath, add_tzs,base_on_existing=False,start=dtdt(2017,10,1),asset='NG',resolution='15m'):
    if resolution is None:
        resolution = '15m'
    resolutions_fullname_mapping = {'15m': '15 mins', '1H': '1 Hour', '1D': '1 day',
                                    '30m': '30 mins', '5m': '5 mins', '1m': '1 min'}
    asset_obj = Asset(asset)

    if asset in ['NQ','ES','MBT']:
        # Here we want to allow candles_outpath based on Asset class
        outpath = asset_obj.get_modified_candles_df_outpath(resolutions_fullname_mapping[resolution])

    days_before_expiry = asset_obj.days_to_exp
    if not base_on_existing:
        start = dtdt(2017,10,1)
    final_df = pd.DataFrame()
    if base_on_existing and os.path.exists(outpath):
        current_df = pd.read_csv(outpath,parse_dates=['date','time_jerusalem','time_utc','time_chicago'])
        start = max(start, current_df['date'].min()-td(days=7))
    else:
        current_df = pd.DataFrame()
    market_data_dir = asset_obj.months_dir
    for fname in os.listdir(market_data_dir):
        if asset != 'NG':
            if asset_obj.default_resolution == resolutions_fullname_mapping[resolution]:
                if 'mins' in fname or 'hour' in fname.lower():
                    continue
            else:
                if resolutions_fullname_mapping[resolution].replace(' ','') not in fname:
                    continue
        else:
            if resolution == '15m':
                if '5mins' in fname:
                    continue
            elif resolution == '5m':
                if not '5mins' in fname:
                    continue
        path = market_data_dir+"\\"+fname

        if os.path.isfile(path) and fname.endswith('.csv'):
            try:
                year_month = fname.split(".csv")[0].split("_")[1]
            except:
                bb = 0
            year = int(year_month[:4])
            month = int(year_month[-2:])
            ref_dt = dtdt(year,month,1)
            if ref_dt >= start:
                df = _parse_ib_trades_csv(path,year,month,asset_obj)
                df = df.drop_duplicates(subset=['date'])
                for c in ['close','avg','open','high','low']:
                    df[c] = pd.to_numeric(df[c],errors='coerce')
                if df.shape[0] == 0:
                    print('Warning! Month: %s Y file was empty... Skipping' % str(year) + str(month))
                    continue
                print('handling YearMonth: %s'% year_month)
                if final_df.shape[0] == 0:
                    final_df = df
                else:
                    try:
                        final_df = final_df.merge(df,on=list(df), how="outer")
                    except:
                        dtypes_comparison = final_df.dtypes == df.dtypes
    #final_df['contract'] = final_df['contract'].apply(lambda x: np.nan if x == 'nan' else x).fillna(method='ffill')
    final_df = final_df.sort_values(['date','contract'])
    final_df['diff_dt'] = final_df['date'].diff().dt.total_seconds() / 3600 / 24
    final_df['weekday'] = final_df['date'].dt.weekday
    final_df['chosen_contract'] = np.nan #final_df['contract'] #np.nan
    final_df['next_contract'] = final_df['contract'].apply(lambda x: _calc_next_contract(x, asset))
    final_df['switch_contract'] = False

    use_old_frinday_logic = USE_OLD_FRIDAYS_LOGIC
    if asset != 'NG':
        use_old_frinday_logic = True # todo need to support this too
    if use_old_frinday_logic:
        final_df2 = _filter_contract_old_method(final_df, days_before_expiry, resolution)
    else:
        last_fridays_df = get_last_fridays(final_df[['date']],days_before_expiration=days_before_expiry)
        last_fridays_df['chosen_contract'] = (last_fridays_df['date'] + td(days=16+30)).dt.strftime('%Y%m').astype(int)
        #last_fridays_df['chosen_contract_planB'] = (last_fridays_df['date'] + td(days=16+60)).dt.strftime('%Y%m').astype(int)

        final_df2 = final_df.drop('chosen_contract',axis=1).merge(last_fridays_df[['date','chosen_contract']],on='date',how='left').sort_values(['date','contract'])
        final_df2['chosen_contract'] = final_df2['chosen_contract'].fillna(method='ffill')
        cond_a = final_df2['contract'] == final_df2['chosen_contract']
        # final_df2['allowed_contract_planB'] = final_df2['allowed_contract_planB'].fillna(method='ffill')
        # cond_b = final_df2['contract'] == final_df2['allowed_contract_planB']
        final_df2 = final_df2[cond_a]


    final_df2['open'] = pd.to_numeric(final_df2['open'])
    if add_tzs:
        final_df2 = add_dates(final_df2)

    ### Add closing 15Mins
    last_15mins_of_day = final_df2[(final_df2.time_chicago.dt.hour == 15) & (final_df2.time_chicago.dt.minute == 45)]
    last_15mins_of_day['open'] = last_15mins_of_day['close']
    last_15mins_of_day[['date', 'time_jerusalem', 'time_utc', 'time_chicago']] += td(minutes=60)
    # combine it with original
    final_df2 = final_df2.merge(last_15mins_of_day, on=list(last_15mins_of_day), how='outer').sort_values('time_utc')

    ####

    if base_on_existing and current_df.shape[0]>0:
        date_threshold = min(final_df2.date.min() + td(days=30), current_df.date.max())
        current_df = current_df.query('date<=@date_threshold')
        current_df = current_df.merge(final_df2,on=list(final_df2),how='outer').sort_values(['date','contract']).drop_duplicates(subset=['date'],keep='last')
    else:
        current_df = final_df2

    current_df[['open','close','high','low','avg','diff_from_expiry']] = np.round(current_df[['open','close','high','low','avg','diff_from_expiry']],3)
    current_df.to_csv(outpath,index=False)

    if asset == 'ES':
        csv_15min = asset_obj.get_modified_candles_df_outpath(resolutions_fullname_mapping['15m'])
        csv_30min = csv_15min.replace('_15mins', '')
        csv_mixed = csv_15min.replace('15mins', '15+30mins')
        df_15min = pd.read_csv(csv_15min, parse_dates=['date', 'time_utc'])
        df_30min = pd.read_csv(csv_30min, parse_dates=['date', 'time_utc'])
        df_mixed = pd.concat([df_30min, df_15min]).drop_duplicates(subset=['date'], keep='last').sort_values('date')
        df_mixed.to_csv(csv_mixed, index=False)




outpath_tz = outpath.replace(".csv", "_tz.csv")
data_with_bollinger = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_withBollinger.csv")

strategies_chicago = [
    [[1, 0], [4, 0]],
    [[3, 0], [4, 0]],
    [[5, 0], [6, 30]],
    [[6, 45], [7, 30]],
    [[7, 45], [8, 30]],
    [[7, 45], [9, 0]],
    [[8, 15], [10, 45]],
    [[8, 15], [11, 15]],
    [[9, 15], [11, 45]],

]

# EC 7
# EPS 8
# GFS 5
# GEFS 6
strategies_utc = [
    [[5, 0], [10, 0]],
    [[6, 0], [10, 0]],
    [[7, 0], [10, 0]],
    [[7, 0], [8, 0]],
    [[7, 45], [16, 0]],
    [[7, 45], [10, 0]],
    [[10, 0], [12, 0]],
    [[12, 0], [16, 0]],
    [[14, 0], [16, 0]],
    [[13, 0], [13, 15]],
    [[13, 0], [13, 30]],
    [[13, 15], [13, 30]],
    [[13, 15], [13, 45]],
    [[13, 15], [14, 0]],
    [[14, 0], [14, 30]],
    [[14, 15], [14, 30]],
    [[14, 15], [17, 15]],
    [[13, 15], [16, 15]],
    [[14, 45], [15, 15]],
    [[14, 45], [16, 15]],
    [[16, 15], [17, 15]],

]

strategies_post_12z = [
                [[17, 30], [18, 15]],
                [[16, 45], [18, 15]],

                    ]

strategies_gfs_0z = [
    [[3, 0], [4, 45]],
    [[3, 45], [4, 45]],
    [[3, 45], [5, 45]],
    [[4, 45], [5, 45]],
    [[4, 45], [5, 15]],
    [[4, 45], [5, 30]],
    [[5, 0], [5, 30]],
    [[5, 15], [5, 45]],
    [[5, 0], [5, 45]],
    [[4, 45], [6, 0]],
]

strategies_gfs_0z_am = [
                    [[23, 30], [6, 15]]
                        ]

strategies_gfs_6z = [
    [[9, 45], [11, 45]],
    [[11, 15], [12, 30]]
]

strategies_gfs_12z = [
                    [[11, 30], [12, 30]],
                    [[12, 45], [13, 45]],
                    [[12, 45], [14, 45]],
                    [[15, 30], [17, 30]],
                    [[15, 45], [17, 45]],
                    [[15, 30], [18, 15]],
                    [[14, 15], [17, 15]],
                    [[14, 15], [15, 30]],
                    [[13, 45], [15, 30]],
                    [[13, 45], [17, 30]],
                    [[17, 30], [18, 15]],
                    [[14, 15], [17, 45]],
                    [[14, 15], [18, 15]],
                    [[14, 15], [17, 30]],
                    [[14, 45], [17, 15]],
                    [[14, 45], [17, 30]],
                    [[14, 45], [17, 45]]
                    ]

strategies_session_opening = []

strategies_utc2 = [
    [[6, 30], [8, 0]],
    [[7, 45], [16, 0]],
    [[7, 0], [8, 0]],
    [[7, 0], [8, 30]],
    [[10, 45], [11, 45]],
    [[12, 45], [13, 0]],
    [[12, 45], [13, 15]],
    [[12, 45], [13, 30]],
    [[13, 15], [13, 45]],
    [[13, 0], [13, 15]],
    [[13, 15], [14, 15]],
    [[13, 45], [14, 15]],
    [[13, 45], [14, 30]],
    [[12, 45], [13, 45]],
    [[14, 15], [16, 15]],
    [[14, 15], [17, 15]],
    [[14, 15], [14, 45]],
]

strategies_for_market_features= [
                                    [[1, 0], [3, 30]],
                                    [[19, 0], [20, 30]],  # day before
                                    [[6, 0], [7, 0]],
                                    [[7, 0], [8, 0]],
                                    [[6, 0], [8, 0]],
                                    [[10,0],[12,0]],
                                    [[12,0],[15,15]],
                                    [[15,15],[17,45]],

                                ]

strategies = [
            [[8, 0], [11, 0]],
            [[14, 15], [17, 45]],
            ]

post_geps_0z_strategies = [
                    [[6, 30], [8, 0]],
                    [[7, 0], [8, 0]],
                    [[7, 0], [8, 30]],
                    ]

evening_strategies =  [
                    [[16,45],[17,45]],
                    [[17,0],[17,45]],
                    [[17,15],[17,45]],
                    [[17,45],[18,15]],
                    [[17,45],[18,45]],
                    [[18,15],[18,45]],
                    [[18,45],[19,45]],
                    [[19,15],[21,0]],
                    [[19,15],[22,0]],
                    [[20,0],[21,45]],

                    [[17,30],[18,0]],
                    [[18,0],[18,30]],
                    [[18,0],[19,0]],
                    [[19,0],[19,30]],
                    [[19,0],[20,0]],

                    [[19,45],[20,45]],
                    [[19,45],[20,30]],
                    [[19,45],[21,45]],
                    [[20,45],[21,45]],
                    [[20,30],[21,0]],
                    [[21,0],[21,45]],

                    [[22,0],[23,0]],
                    [[23,0],[23,45]],
                    [[22,0],[23,30]],
                    [[21,45],[23,30]],
                    [[20,45],[23,45]],
                    [[21,45],[23,45]],
                    [[21,45],[22,45]],
                    [[23,0],[0,0]],
                    [[0,0],[1,0]],
                    [[1,0],[2,0]],
                    [[2,0],[3,0]],
                    [[0,0],[3,0]],
                    [[0,0],[4,0]]
                       ]
main_day_strategies = [  [[0,0],[23,30]],
                         [[4,0],[6,0]],
                        [[0,0],[6,0]],
                         [[19,45],[0,0]],
                           [[4, 0], [5, 30]],
                            [[4, 30], [5, 30]],
                            [[5, 0], [5, 30]],
                            [[5, 30], [6, 30]],
                            [[5, 30], [7, 0]],
                            [[5, 30], [7, 30]],
                            [[5, 30], [8, 30]],
                            [[6, 0], [7, 0]],
                            [[7, 0], [7, 15]],
                            [[7, 0], [7, 30]],
                            [[7, 30], [8, 0]],
                            [[7, 0], [8, 0]],
                            [[7, 0], [9, 0]],
                            [[7, 0], [9, 30]],
                            [[7, 45], [8, 15]],
                            [[7, 45], [8, 0]],
                            [[8, 45], [9, 15]],
                            [[7, 45], [10, 30]],
                            [[7, 45], [11, 0]],
                            [[8, 0], [8, 30]],
                            [[8, 0], [9, 0]],
                           [[8, 0], [10, 30]],
                            [[8, 0], [11, 0]],
                            [[8, 45], [9, 0]],
                            [[9, 0], [9, 30]],
                            [[9, 0], [10, 0]],
                           [[9, 0], [10, 30]],
                            [[9, 30], [11, 0]],

                            [[9, 45], [10, 0]],
                            [[10, 0], [11, 0]],
                            [[10, 0], [10, 30]],
                            [[10, 30], [11, 0]],
                            [[10, 45], [11, 15]],
                            [[11, 30], [14, 15]],
                            [[11, 45], [12, 15]],
                            [[11, 45], [14, 15]],
                            [[11, 30], [17, 15]],
                            [[12,0],[12,45]],

                        [[11, 30], [14, 15]],
                        [[11, 30], [13, 15]],
                        [[12, 0], [14, 15]],
                        [[12, 0], [14, 30]],
                        [[12, 0], [14, 0]],
                        [[12, 15], [12, 45]],
                        [[12, 15], [13, 15]],

                        [[11, 0], [11, 30]],
                        [[11, 15], [11, 30]],
                        [[11, 30], [12, 0]],
                        [[11, 30], [12, 30]],
                        [[12,15],[12,30]],
                        [[12,30],[13,15]],
                        [[12,45],[13,15]],
                        [[12,45],[13,30]],
                        [[13,0],[13,15]],
                        [[13,15],[13,45]],
                        [[13,15],[14,15]],
                        [[12,45],[13,15]],
                        [[13,45],[14,15]],
                        [[13,45],[14,0]],
                        #[[13,45],[14,0]],
                        #[[14,0],[14,15]],
                        #[[14,0],[14,30]],
                        [[13,0], [13,30]],
                        [[13, 0], [14, 0]],
                        [[14, 0], [15, 0]],
                        [[15, 0], [15, 15]],
                            [[14, 0], [14, 15]],
                            [[14, 15], [14, 30]],
                            [[14, 0], [14, 30]],
                            [[14, 15], [16, 45]],
                            [[14, 15], [17,0]],
                            [[14, 15], [14, 45]],
                           [[14, 15], [15, 0]],
                            [[14, 15], [15, 15]],
                           [[14, 15], [17, 15]],
                            [[13, 15], [15, 15]],
                            [[13, 15], [16, 15]],
                            [[13, 15], [17, 15]],
                           [[14, 15], [17,45]],
                           [[13, 15], [17, 45]],
                            [[14, 30], [17,15]],
                            [[14, 30], [17,0]],
                            [[14, 45], [16,45]],
                            [[14, 45], [17,15]],

                            [[15, 30], [16, 45]],
                            [[15, 30], [17, 15]],
                            [[15, 15], [15, 45]],
                            [[15, 15], [16, 15]],
                            [[15, 15], [16, 45]],
                            [[15, 15], [17, 15]],
                            [[15, 15], [17, 0]],
                            [[15, 15], [17, 45]],
                            [[15, 0], [17, 15]],

                            [[16, 15], [17, 45]],
                            [[16, 30], [17, 45]],
                            [[16, 45], [17, 45]],
                            [[15, 30], [16,45]],
                            [[15, 45], [16,15]],

                            [[17, 0], [17, 45]],
                            [[17, 15], [17, 45]],
                            [[17, 0], [17, 30]],
                            [[17, 15], [18, 15]],
                            [[18, 45], [19, 15]],
                        ]
research_strategies = strategies_for_market_features + \
                        main_day_strategies +\
                        evening_strategies


production_strategies = [
                        [[0, 0], [4, 0]],
                        [[8, 0], [11, 0]],
                        [[8, 0], [18, 45]],
                        [[10,30],[11,0]],
                        [[10,0],[12,0]],
                        [[11,0],[14,15]],

                        [[12,0],[15,15]],
                        [[15,15],[17,45]],


                        [[11, 30], [14, 15]],
                        [[14, 15], [17, 45]],
                        ]

if __name__ == "__main__":
    add_tzs = True
    mode = 'open'

    mode_suffix = "_V3d10" if mode == "open" else ""
    # V2D for Dailies!
    candles_outpath = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_%sLive.csv")% ("" if not add_tzs else "tz_")
    # unite_monthly_contracts(candles_outpath,add_tzs,base_on_existing=True,start=dtdt(2022,1,1))
    unite_monthly_contracts(candles_outpath,add_tzs,base_on_existing=True,start=dtdt(2023,4,1))
    # wrap_bollingers_generation(candles_outpath,True,base_on_existing=True,start=dtdt(2022,9,1),price_col=mode,
    #                            suffix=mode_suffix)

    raise
    ###  build Ys by strategies

    months = MONTHS

    outdir = os.path.join(HOME,"Market_Data","Ys3")
    strats_df = wrap_delta_Ys_generation(candles_outpath, outdir, research_strategies, True, 'utc')
    #strats_df = wrap_delta_Ys_generation(candles_outpath,outdir,strategies_gfs_12z, True, 'utc')


