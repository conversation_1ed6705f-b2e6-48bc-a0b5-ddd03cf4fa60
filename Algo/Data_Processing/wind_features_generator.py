import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td
import pandas as pd
import numpy as np
import os

from Algo.General.wind_initial_research import prepare_xy
import os
from Algo.Utils.files_handle import HOME
from Algo.Learning.feature_engineering import light_X_loader

"""
We will generate wind XY file, then save only sepcific features out of it
"""

XY_0800_REF = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_0800-1100_15Mins_utc_DailyDiffs_Full_lastModel=12_daysAhead=[1-15]_Months=[1-12]_take9_prev24_PrevYs+Bollinger_V3d10.csv")
WIND_SUFFIXES = ['v15_WindTX','v14_WindUS']
WIND_FEATURES_CSV = os.path.join(HOME,"General_Stocks","Wind_features.csv")
WIND_TYPE = 'ws10m'


def get_model_features(model,model_run,days_range):
    additional_fts = []
    if days_range in ['0-13','0-14','0-16'] or days_range == '0-8' and model == 'EC':
        days_range2 = days_range if days_range != '0-14' else '0-13'
        additional_fts = [
            f'diff_{model_run}_{days_range2}_last-Prev{i}D_{model}' for i in [1, 2, 3]] + [
            f'diff_{model_run}_{days_range2}_Prev1D-Prev{i}D_{model}' for i in [2, 3, 4]]
    return [f'diff_{model_run}_{days_range}_{model_diff}_{model}'
            for model_diff in ['last-Prev1','last-Prev2','last-Prev3','last-Prev4']] + additional_fts





WIND_FEATURES_FOR_ENRICHMENT = {'v15_WindTX':[
                'Value_0Z_0-0_PARA','Value_0Z_0-16_PARA','Value_0Z_0-0_PARA','Value_0Z_0-16_PARA',
                'diff_0Z_0-0_last-Prev1_PARACO', 'diff_0Z_0-0_last-Prev3_PARA', 'diff_0Z_0-0_last-Prev4_PARA', 'diff_0Z_0-0_last-Prev4_PARACO', 'diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_0Z_0-10_Prev1-Prev3_PARA', 'diff_0Z_0-10_last-Prev2_PARACO', 'diff_0Z_0-10_last-Prev3_PARACO', 'diff_0Z_0-13_Prev1-Prev2_PARACO', 'diff_0Z_0-16_Prev1-Prev2_PARACO', 'diff_0Z_0-16_Prev1D-Prev2D_PARA', 'diff_0Z_0-2_last-Prev4_PARACO', 'diff_0Z_0-8_Prev1-Prev2_PARACO', 'diff_0Z_8-16_Prev1-Prev2_PARA', 'diff_0Z_8-16_Prev1D-Prev2D_PARA', 'diff_0Z_8-16_last-Prev2_PARACO', 'diff_6Z_0-8_last-Prev3_PARACO', 'Value_0Z_8-16_PARA', 'Value_0Z_14-16_PARA', 'Value_0Z_0-16_PARACO', 'Value_0Z_8-16_PARACO', 'Value_0Z_14-16_PARACO', 'diff_0Z_0-8_last-Prev1_PARA', 'diff_0Z_0-8_last-Prev2_PARA', 'diff_0Z_0-8_last-Prev3_PARA', 'diff_0Z_0-8_last-Prev4_PARA', 'diff_0Z_0-16_last-Prev1_PARA', 'diff_0Z_0-16_last-Prev2_PARA', 'diff_0Z_0-16_last-Prev3_PARA', 'diff_0Z_0-16_last-Prev4_PARA', 'diff_0Z_0-8_last-Prev1_PARACO', 'diff_0Z_0-8_last-Prev2_PARACO', 'diff_0Z_0-8_last-Prev3_PARACO', 'diff_0Z_0-8_last-Prev4_PARACO', 'diff_0Z_0-16_last-Prev1_PARACO', 'diff_0Z_0-16_last-Prev2_PARACO', 'diff_0Z_0-16_last-Prev3_PARACO', 'diff_0Z_0-16_last-Prev4_PARACO', 'Value_0Z_0-8_PARA', 'diff_6Z_0-16_last-Prev1_PARACO', 'diff_6Z_0-16_last-Prev3_PARA', 'diff_6Z_0-16_last-Prev3_PARACO'
                                              ]+ \
            get_model_features('GEFS','0Z','0-16') + get_model_features('PARACO','0Z','0-16')+get_model_features('PARA','0Z','0-16')+\
            get_model_features('GEFS','0Z','0-8') + get_model_features('PARACO','0Z','0-8')+get_model_features('PARA','0Z','0-8')+\
            get_model_features('GEFS','6Z','0-8') + get_model_features('PARACO','6Z','0-8')+get_model_features('PARA','6Z','0-8')+\
            get_model_features('GEFS','6Z','0-16') + get_model_features('PARACO','6Z','0-16')+get_model_features('PARA','6Z','0-16')+\
            get_model_features('EC','0Z','0-0')+\
            get_model_features('EC','0Z','0-8')+get_model_features('EC','0Z','0-4')+get_model_features('EC','0Z','0-2')+get_model_features('EC','12Z','0-8')+\
            get_model_features('EPS','0Z','0-14')+get_model_features('EPS','0Z','0-8')+get_model_features('EPS','0Z','0-2')+get_model_features('EPS','12Z','0-14')+\
            get_model_features('EPSCO','0Z','0-14')+get_model_features('EPSCO','0Z','0-8')+get_model_features('EPSCO','0Z','0-2')+get_model_features('EPSCO','12Z','0-14')
    ,
    'v14_WindUS':['diff_6Z_8-16_last-Prev3_PARACO',
                 'diff_6Z_0-13_last-Prev3_PARACO', 'diff_6Z_0-10_last-Prev3_PARACO',
                'diff_6Z_14-16_last-Prev1_PARA','diff_6Z_0-8_last-Prev3_PARACO',
                 'diff_6Z_0-16_Prev1-Prev3_PARACO', 'diff_6Z_8-16_Prev1-Prev3_PARACO',
                 'diff_0Z_0-13_last-Prev2_PARACO',
                  'Value_0Z_0-0_PARA', 'Value_0Z_0-16_PARA', 'Value_0Z_8-16_PARA', 'Value_0Z_14-16_PARA',
                  'Value_0Z_0-16_PARACO', 'Value_0Z_8-16_PARACO', 'Value_0Z_14-16_PARACO',
                  ] + get_model_features('PARA','0Z','0-16')+get_model_features('PARA','6Z','0-16')+\
                        get_model_features('PARA','18Z','0-16')+\
                            get_model_features('PARACO','0Z','0-16')+get_model_features('PARACO','6Z','0-16')+\
                                get_model_features('PARACO','18Z','0-16')+\
                        get_model_features('PARA','0Z','0-8')+get_model_features('PARA','6Z','0-8')+\
                            get_model_features('PARA','18Z','0-8')+\
                                get_model_features('PARACO','0Z','0-8')+get_model_features('PARACO','6Z','0-8')+\
                                    get_model_features('PARACO','18Z','0-8')+\
                        get_model_features('PARA','0Z','0-2')+get_model_features('PARA','6Z','0-2')+\
                            get_model_features('PARA','18Z','0-2')+\
                                get_model_features('PARACO','0Z','0-2')+get_model_features('PARACO','6Z','0-2')+\
                                    get_model_features('PARACO','18Z','0-2')+\
                        get_model_features('EC','0Z','0-0')+\
                        get_model_features('EC','0Z','0-8')+get_model_features('EC','0Z','0-4')+get_model_features('EC','0Z','0-2')+get_model_features('EC','12Z','0-8')+\
                        get_model_features('EPS','0Z','0-14')+get_model_features('EPS','0Z','0-8')+get_model_features('EPS','0Z','0-2')+get_model_features('EPS','12Z','0-14')+\
                        get_model_features('EPSCO','0Z','0-14')+get_model_features('EPSCO','0Z','0-8')+get_model_features('EPSCO','0Z','0-2')+get_model_features('EPSCO','12Z','0-14')



                                }

# assert that all features are unique
# assert len(set(WIND_FEATURES_FOR_ENRICHMENT['v15_WindTX']+WIND_FEATURES_FOR_ENRICHMENT['v14_WindUS'])) == \
#          len(WIND_FEATURES_FOR_ENRICHMENT['v15_WindTX']+WIND_FEATURES_FOR_ENRICHMENT['v14_WindUS']), [x for x in WIND_FEATURES_FOR_ENRICHMENT['v15_WindTX']+WIND_FEATURES_FOR_ENRICHMENT['v14_WindUS'] if (WIND_FEATURES_FOR_ENRICHMENT['v15_WindTX']+WIND_FEATURES_FOR_ENRICHMENT['v14_WindUS']).count(x)>1
# raise

def main(start=None):
    if start is None:
        start = dtdt.now()-td(days=30)
    if os.path.exists(WIND_FEATURES_CSV):
        original_df = pd.read_csv(WIND_FEATURES_CSV,parse_dates=['date'])
    else:
        original_df = pd.DataFrame()

    xy_ref = XY_0800_REF
    final_df = pd.DataFrame()
    for wind_suffix in WIND_SUFFIXES:
        weight_suffix = {'v15_WindTX':'TX','v14_WindUS':'US'}[wind_suffix]
        tmp_xy_df = prepare_xy(xy_ref,[WIND_TYPE],wind_suffix,start=start)
        fts_lst = sorted(list(set(WIND_FEATURES_FOR_ENRICHMENT[wind_suffix])))
        for ft in fts_lst:
            if ft not in list(tmp_xy_df):
                tmp_xy_df[ft] = np.nan
        tmp_xy_df = tmp_xy_df[['date']+fts_lst]
        tmp_xy_df = tmp_xy_df.rename(columns={x: x+'_%s%s'%(WIND_TYPE,weight_suffix) for x in fts_lst})
        if final_df.shape[0] == 0:
            final_df = tmp_xy_df
        else:
            final_df = final_df.merge(tmp_xy_df,on=['date'])
    final_df['date'] += td(hours=8)
    if original_df.shape[0]>0:
        final_df = original_df.merge(final_df,on=list(original_df),how='outer').drop_duplicates(subset=['date'],keep='last').sort_values('date')
    final_df = final_df.round(2)
    final_df.to_csv(WIND_FEATURES_CSV,index=False)


def main_v2(start=None,base_on_existing=True):
    if start is None:
        start = dtdt.now()-td(days=30)
    if os.path.exists(WIND_FEATURES_CSV):
        original_df = pd.read_csv(WIND_FEATURES_CSV,parse_dates=['date'])
    else:
        original_df = pd.DataFrame()

    xy_ref = XY_0800_REF
    final_df = pd.DataFrame()
    for wind_suffix in WIND_SUFFIXES:
        weight_suffix = {'v15_WindTX':'TX','v14_WindUS':'US'}[wind_suffix]
        # tmp_xy_df = prepare_xy(xy_ref,[WIND_TYPE],wind_suffix,start=start)
        tmp_xy_df = pd.DataFrame()
        for model in ['PARA','PARACO','EC','EPSCO','EPS']:
            model_xs_df = light_X_loader(model,os.path.join(HOME,'Xdata',
                                          f'X_file_DailyDiffs_{model}_{wind_suffix}.csv'),WIND_TYPE,[0,6,12,18],
                                         start=start,allow_value_columns=True).reset_index()
            model_xs_df.columns = ['date']+[x+weight_suffix for x in list(model_xs_df)[1:]]
            if tmp_xy_df.shape[0]==0:
                tmp_xy_df = model_xs_df
            else:
                tmp_xy_df = tmp_xy_df.merge(model_xs_df,on=['date'],how='outer').sort_values('date')
        tmp_xy_df = tmp_xy_df.sort_values('date')
        fts_lst = [x+f'_{WIND_TYPE}{weight_suffix}' for x in sorted(list(set(WIND_FEATURES_FOR_ENRICHMENT[wind_suffix])))]
        for ft in fts_lst:
            if ft not in list(tmp_xy_df):
                tmp_xy_df[ft] = np.nan
        tmp_xy_df = tmp_xy_df[['date']+fts_lst]

        if final_df.shape[0] == 0:
            final_df = tmp_xy_df
        else:
            final_df = final_df.merge(tmp_xy_df,on=['date'])
    final_df = final_df.sort_values('date')

    if original_df.shape[0]>0 and base_on_existing:
        if final_df.shape[0] == 0:
            final_df = original_df
        else:
            final_df = original_df.merge(final_df,on=list(original_df),how='outer').drop_duplicates(subset=['date'],keep='last').sort_values('date')
    final_df = final_df.round(5)
    print(f'about to write {final_df.shape[0]} rows')
    final_df.to_csv(WIND_FEATURES_CSV,index=False)


if __name__ == '__main__':
    main_v2(start=dtdt(2020,9,1),base_on_existing=True)
    # main()

