####### IMPORTS #######
import pygrib as pg
import numpy as np
from datetime import datetime, timedelta
import re
import pandas as pd

####### CONSTANTS #######

FILE_NAME_TEMPLATES = {
    'GFS': {'grib': 'gfs_._........_...._....grb2',
            'mg': 'gfs_._........_...._....grb2.%s.small',
            'midi': 'gfs_._........_...._....grb2.midi',
            'datetime_str': '%Y%m%d%H%M',
            'date_ind': 2, 'time_ind': 3, 'horizon_ind': 4,
            'horizon_characters': 3, 'seperator': '_'},
    'RAP': {'grib': 'rap_252_........_...._....grb2',
            'mg': 'rap_252_........_...._....grb2.%s.small',
            'datetime_str': '%Y%m%d%H%M',
            'date_ind': 2, 'time_ind': 3, 'horizon_ind': 4,
            'horizon_characters': 3, 'seperator': '_'}
}
ML_NAME = 0
LEVEL_TYPE = 1
LEVEL = 2
PY_GRIB_NAME = 3
DATE_STR = '%Y-%m-%dT%H:%M:%S.000Z'

# Weather definitions
REFERENCE_BUILDINGS_TEMP = 18  # 18 celsius (65F) according to \


# https://www.investopedia.com/terms/h/heatingdegreeday.asp

####### CODE #######

def is_valid_grib(file_name, model, grib_type, zone=None):
    """
    check if filename is a valid name for the model / type
    """
    # by default - file name is invalid
    is_valid = False
    time = None
    horizon = None

    # compare file_name to expected grib name pattern
    params = FILE_NAME_TEMPLATES[model]
    if zone == None:
        name_pattern = params[grib_type]
    else:
        name_pattern = params[grib_type] % (zone)
    if re.match(name_pattern, file_name):
        # if pattern matches extract from file_name the grib horizon and effective time
        is_valid = True
        file_name_seperated = file_name.split(params['seperator'])

        horizon = int(file_name_seperated[params['horizon_ind']][:params['horizon_characters']])
        datetime_str = '%s%s' % (file_name_seperated[params['date_ind']], file_name_seperated[params['time_ind']])
        time = datetime.strptime(datetime_str, params['datetime_str']) + \
               timedelta(seconds=3600 * horizon)

    return (is_valid, horizon, time)


def parse_grib_data(grib_file, configuration, features_ml_name, logger):
    """
    parse mini-grib file into a dict corresponding to database format
    """
    # extract configuration parameters
    meteo_features = configuration[0]['model_features']
    coordinates_mask = np.array(configuration[0]['coordinates_mask'])
    dc_latitude = np.array(configuration[0]['latitude'])
    dc_longitude = np.array(configuration[0]['longitude'])

    # parse grib file into a dict
    grib_data = read_grib_file(
        grib_file, dc_latitude, dc_longitude, coordinates_mask, meteo_features, logger)
    # evaluate additional meteorological features
    grib_full_data = extract_new_features(grib_data, grib_file, logger)
    # extract prediction time and missing_value symbol
    grib_time = grib_full_data['t']
    grib_time_str = datetime.strftime(grib_time, DATE_STR)
    missing_value = grib_full_data['missingValue']

    # collect all data in a dict
    full_data = {}
    for feature_name in features_ml_name:
        if feature_name in grib_full_data.keys():
            feature_entry = {'time': grib_time_str, 'hour': grib_time.hour,
                             'value': grib_full_data[feature_name], 'missingValue': missing_value}
            full_data.update({feature_name: feature_entry})
        else:
            logger.warning('no data was extracted for %s in file %s' % (feature_name, grib_file))

    return full_data


def read_grib_file(file_name, model_latitude, model_longitude, coordinates_mask,
                   meteo_features, logger):
    """
    read grib file into a python dictionary
    """
    # read and index grib
    grib_data = pg.open(file_name)
    grib_index = pg.index(file_name, 'typeOfLevel', 'level', 'name')
    grib_1st_message = grib_data[1]

    # load, reshape and truncate grib file lat & lon
    grib_latitude, grib_longitude = grib_1st_message.latlons()
    grib_latitude = grib_latitude.reshape(grib_latitude.size)[coordinates_mask]
    grib_longitude = grib_longitude.reshape(grib_longitude.size)[coordinates_mask]

    # verify lat / lon values validity
    coordinates_diff = (np.abs(grib_latitude - model_latitude) + np.abs(grib_longitude - model_longitude)).max()
    if coordinates_diff > 0:
        raise AssertionError('coordinates value of %s do not match expected configuration.' % (file_name))

    # store grib parameters in a dict
    data = {'t': grib_1st_message.validDate, 'missingValue': grib_1st_message.missingValue,
            'Z': int((grib_1st_message.validDate - grib_1st_message.analDate).total_seconds() / 3600)}
    data['init'] = data['t'] - timedelta(hours=data['Z'])

    # add meteorological information to the dict
    for feature in meteo_features:
        try:
            # for all messages but wind, use the faster index.select method
            if feature[ML_NAME].find('grd') == -1:
                grib_messages = grib_index.select(typeOfLevel=str(feature[LEVEL_TYPE]),
                                                  level=float(feature[LEVEL]), name=str(feature[PY_GRIB_NAME]))
            # for wind, use the slower grb.select method
            else:
                grib_messages = grib_data.select(typeOfLevel=str(feature[LEVEL_TYPE]),
                                                 level=float(feature[LEVEL]), name=str(feature[PY_GRIB_NAME]))
            # verify that only a single message corresponds to query
            assert len(grib_messages) == 1, (
            'multiple grib messages were found for feature %s in grib file %s'(feature, file_name))
            # parse meteorological data into data dict
            feature_value = grib_messages[0].values
            data[feature[ML_NAME]] = np.array(feature_value.reshape(feature_value.size))[coordinates_mask]
        except ValueError:
            if logger:
                logger.warning(('no data was found for %s in file %s' % (feature[ML_NAME], file_name)))
            else:
                print(('no data was found for %s in file %s' % (feature[ML_NAME], file_name)))

    return data


def read_grib_file2(file_name, desired_latlon_df, features, logger):
    """
    read grib file into a python dictionary
    """
    # read and index grib
    grib_data = pg.open(file_name)
    grib_index = pg.index(file_name, 'typeOfLevel', 'level', 'name')
    grib_1st_message = grib_data[1]

    # load, reshape and truncate grib file lat & lon
    grib_latitude, grib_longitude = grib_1st_message.latlons()
    grib_latitude_array = grib_latitude.reshape(grib_latitude.size)
    grib_longitude_array = grib_longitude.reshape(grib_longitude.size)

    grib_df = pd.DataFrame({'lat': grib_latitude_array, 'lon': grib_longitude_array})
    # verify lat / lon values validity
    coordinates_diff = (np.abs(grib_latitude - dc_latitude) + np.abs(grib_longitude - dc_longitude)).max()
    if coordinates_diff > 0:
        raise AssertionError('coordinates value of %s do not match expected configuration.' % (file_name))

    # store grib parameters in a dict
    data = {'t': grib_1st_message.validDate, 'missingValue': grib_1st_message.missingValue,
            'Z': int((grib_1st_message.validDate - grib_1st_message.analDate).total_seconds() / 3600)}

    # add meteorological information to the dict
    for feature in meteo_features:
        try:
            # for all messages but wind, use the faster index.select method
            if feature[ML_NAME].find('grd') == -1:
                grib_messages = grib_index.select(typeOfLevel=str(feature[LEVEL_TYPE]),
                                                  level=float(feature[LEVEL]), name=str(feature[PY_GRIB_NAME]))
            # for wind, use the slower grb.select method
            else:
                grib_messages = grib_data.select(typeOfLevel=str(feature[LEVEL_TYPE]),
                                                 level=float(feature[LEVEL]), name=str(feature[PY_GRIB_NAME]))
            # verify that only a single message corresponds to query
            assert len(grib_messages) == 1, (
            'multiple grib messages were found for feature %s in grib file %s'(feature, file_name))
            # parse meteorological data into data dict
            feature_value = grib_messages[0].values
            data[feature[ML_NAME]] = np.array(feature_value.reshape(feature_value.size))[coordinates_mask]
        except ValueError:
            logger.warning(('no data was found for %s in file %s' % (feature[ML_NAME], file_name)))

    return data


def extract_new_features(data, file_name, logger):
    """
    extract additional meteorological features from grib data
    """

    new_data = data.copy()
    missing_value = new_data['missingValue']

    # extract wind-speed for 10m, 80m, 850mb
    if _check_availability(new_data, ['ugrd10m', 'vgrd10m'], 'ws10m', file_name, logger) == True:
        new_data['ws10m'] = _calculate_wind_speed(data['ugrd10m'], data['vgrd10m'], missing_value)
    if _check_availability(new_data, ['ugrd80m', 'vgrd80m'], 'ws80m', file_name, logger) == True:
        new_data['ws80m'] = _calculate_wind_speed(data['ugrd80m'], data['vgrd80m'], missing_value)
    if _check_availability(new_data, ['ugrd850', 'vgrd850'], 'ws850', file_name, logger) == True:
        new_data['ws850'] = _calculate_wind_speed(data['ugrd850'], data['vgrd850'], missing_value)

    # extract heat index
    if _check_availability(new_data, ['rh2m', 'tmp2m'], 'heatIndex', file_name, logger) == True:
        new_data['heatIndex'] = _calculate_heat_index(data['rh2m'], data['tmp2m'], missing_value)
    # extract wind chill
    if _check_availability(new_data, ['ws10m', 'tmp2m'], 'windChill', file_name, logger) == True:
        new_data['windChill'] = _calculate_wind_chill(new_data['ws10m'], data['tmp2m'], missing_value)

    # extract K index
    if _check_availability(new_data, ['tmp500', 'tmp700', 'tmp850', 'rh700', 'rh850'],
                           'KI', file_name, logger) == True:
        new_data['KI'] = _calculate_K_index(data['tmp500'], data['tmp700'], \
                                            data['tmp850'], data['rh700'], data['rh850'], missing_value)
    """
    # extract HDD, CDD
    if _check_availability(new_data, ['tmin', 'tmax'], 'HDD', file_name, logger) == True:
        new_data['HDD'], new_data['CDD'] = _calculate_ch_degrees_day(data['tmin'], data['tmax'], missing_value)
    """

    # change features value type to list
    for k in new_data.keys():
        if type(new_data[k]) == np.ndarray:
            new_data[k] = np.ndarray.tolist(new_data[k])

    return new_data


def _check_availability(data, parameters, destination_field, file_name, logger):
    """
    check and warn on missing features required for new features generation
    """
    is_available = True
    for parameter in parameters:
        if parameter not in data.keys():
            logger.warning(('field %s is missing from grib file %s. unable to generate %s.') % (
                parameter, file_name, destination_field))
            is_available = False

    return is_available


####### NEW FEATURES EXTRACTOR #######

def _calculate_wind_speed(ugrd, vgrd, missing_value):
    """
    evaluate wind speed from u / v components
    """
    wind_speed = np.sqrt(np.power(ugrd, 2) + np.power(vgrd, 2))
    is_missing = np.logical_or(ugrd == missing_value, vgrd == missing_value)
    wind_speed[is_missing] = missing_value

    return wind_speed


def _calculate_heat_index(rh2m, tmp2m, missing_value):
    """
    evaluate heat index according to
    http://www.wpc.ncep.noaa.gov/html/heatindex_equation.shtml
    """
    # convert temperature from Kelvin to Fahrenheit
    t_fahrenheit = 9. / 5 * (tmp2m - 273.15) + 32

    # express heat index as a polynomial in t_fahrenheit, rh2m
    # set heat index polinomial coefficients
    polinomial_coefficients = np.array([-42.379, 2.04901523, 10.14333127,
                                        -0.22475541, -6.83783e-3, -5.481717e-2, 1.22874e-3, 8.5282e-4, -1.99e-6])
    # define powers of t_fahrenheit and rh2m in polinomial terms
    t_rh_power = [[0, 0], [1, 0], [0, 1], [1, 1], [2, 0], [0, 2], [2, 1], [1, 2], [2, 2]]
    # extract terms
    polinomial_values = [np.power(t_fahrenheit, trp[0]) * np.power(rh2m, trp[1]) for trp in t_rh_power]
    # evaluate heat index polinomial
    heat_index = sum([coef * val for coef, val in zip(polinomial_coefficients, polinomial_values)])

    # set correction terms according to temperature and humidity
    heat_index = _modify_low_rh_high_temperature(heat_index, rh2m, t_fahrenheit)
    heat_index = _modify_high_rh_mid_temperature(heat_index, rh2m, t_fahrenheit)
    heat_index = _modify_low_temperature(heat_index, rh2m, t_fahrenheit)
    heat_index = _modify_extreme_values(heat_index)

    # remove missing values
    is_missing = np.logical_or(rh2m == missing_value, tmp2m == missing_value)
    heat_index[is_missing] = missing_value

    return heat_index


def _modify_low_rh_high_temperature(heat_index, rh2m, t_fahrenheit):
    # find relevant indicies
    low_rh_indices = (rh2m < 13)
    high_temp_indices = np.logical_and(t_fahrenheit >= 80, t_fahrenheit <= 112)
    relevant_indices = np.logical_and(low_rh_indices, high_temp_indices)
    # set correction term (t_fahrenheit_mod = 112 for irrelevant indices to avoid sqrt of negative number)
    t_fahrenheit_mod = t_fahrenheit + 0
    t_fahrenheit_mod[relevant_indices == False] = 112.
    correction_term = -(13. - rh2m) / 4 * np.sqrt(1 - abs(t_fahrenheit_mod - 95.) / 17)
    # apply modification
    heat_index[relevant_indices] += correction_term[relevant_indices]

    return heat_index


def _modify_high_rh_mid_temperature(heat_index, rh2m, t_fahrenheit):
    # find relevant indicies
    high_rh_indices = (rh2m > 85)
    mid_temp_indices = np.logical_and(t_fahrenheit >= 80, t_fahrenheit <= 87)
    relevant_indices = np.logical_and(high_rh_indices, mid_temp_indices)
    # set correction term
    correction_term = (rh2m - 85.) / 10 * (87 - t_fahrenheit) / 5
    # apply modification
    heat_index[relevant_indices] += correction_term[relevant_indices]

    return heat_index


def _modify_low_temperature(heat_index, rh2m, t_fahrenheit):
    # low temperature - alternative formula
    low_temp_heat_index = 0.5 * (t_fahrenheit + 61 + 1.2 * (t_fahrenheit - 68) + 0.094 * rh2m)
    # low temperature - find relevant indicies
    low_temp_indices = (low_temp_heat_index < 83)
    # low temperature - apply modification
    heat_index[low_temp_indices] = low_temp_heat_index[low_temp_indices]

    return heat_index


def _modify_extreme_values(heat_index):
    # heat_index_mod = 0 for irrelevant indices to avoid power of negative number
    heat_index_mod = heat_index + 0
    heat_index_mod[heat_index_mod < 0] = 0
    # non-physical correction for extreme heat index values
    extreme_heat_index = np.power(140, 0.66) * np.power(heat_index_mod, 0.34)
    # extreme heat index - find relevant indicies
    extreme_indices = (heat_index > 140)
    # extreme heat index - apply modification
    heat_index[extreme_indices] = extreme_heat_index[extreme_indices]

    return heat_index


def _calculate_wind_chill(ws10m, tmp2m, missing_value):
    """
    evaluate wind_chill according to https://en.wikipedia.org/wiki/Wind_chill
    """
    # convert temperature from Kelvin to Celsius
    t_celsius = tmp2m - 273.15
    # convert wind speed from m/s to Km/h:
    ws_kmh = 3.6 * ws10m
    # Wind Chill formula - see https://en.wikipedia.org/wiki/Wind_chill:
    windChill = 13.12 + 0.6215 * t_celsius - 11.37 * np.power(ws_kmh, 0.16) + 0.3965 * t_celsius * np.power(ws_kmh,
                                                                                                            0.16)
    # ignore positive corrections
    windChill = np.minimum(windChill, t_celsius)
    # ignore correction for high temperatures
    windChill[t_celsius >= 10] = t_celsius[t_celsius >= 10]

    # remove missing values
    is_missing = np.logical_or(ws10m == missing_value, tmp2m == missing_value)
    windChill[is_missing] = missing_value

    return windChill


def _calculate_hdd(tmin, tmax, day_light_normalization=False, day_in_year=None, fahrenheit=True):
    """
    according to https://www.investopedia.com/terms/h/heatingdegreeday.asp
    :param tmax: type float: daily max (kelvin)
    :param tmin: type float: daily mean (kelvin)
    :param day_light_normalization: type bool: normalize by day length
    :param day_in_year: if normalize - give the day number
    :return:
    """
    celsius_min, celsius_max = tmin - 273.15, tmax - 273.15

    weights = [1, 1]
    if day_light_normalization:
        assert 1 <= day_in_year <= 366 and int(day_in_year) == day_in_year

        day_in_year = min(364, day_in_year)
        # in 40N We have \\ June: 15 light hours, Dec: 9 hours
        if day_in_year <= 182:
            min_weigth = ((182 - day_in_year) / 182.0 * 15) + (day_in_year / 182.0) * 9
            max_weight = 24 - min_weigth
        else:
            min_weigth = (abs(182 - day_in_year) / 182.0 * 15) + (364 - day_in_year / 182.0) * 9
            max_weight = 24 - min_weigth
        normalized_min_weight, normalized_max_weight = (min_weigth / 12.0, max_weight / 12.0)
        weights = [normalized_min_weight, normalized_max_weight]

    hdd = 18 - np.average([celsius_min, celsius_max], weights=weights)
    if fahrenheit:
        hdd = hdd * 1.8  # F = 1.8C + 32, 32 drops
    return max(hdd, 0)


def _calculate_cdd(tmin, tmax, day_light_normalization=False, day_in_year=None, fahrenheit=True):
    """
    according to https://www.investopedia.com/terms/h/heatingdegreeday.asp
    :param tmax: type float: daily max (kelvin)
    :param tmin: type float: daily mean (kelvin)
    :param day_light_normalization: type bool: normalize by day length,
        We are assuming 40N day lengths as representing all of the US
    :param day_in_year: if normalize - give the day number
    :return:
    """
    celsius_min, celsius_max = tmin - 273.15, tmax - 273.15

    weights = [1, 1]
    if day_light_normalization:
        assert 1 <= day_in_year <= 366 and int(day_in_year) == day_in_year
        day_in_year = min(364, day_in_year)
        # in 40N We have \\ June: 15 light hours, Dec: 9 hours
        if day_in_year <= 182:
            min_weigth = ((182 - day_in_year) / 182.0 * 15) + (day_in_year / 182.0) * 9
            max_weight = 24 - min_weigth
        else:
            min_weigth = (abs(182 - day_in_year) / 182.0 * 15) + (364 - day_in_year / 182.0) * 9
            max_weight = 24 - min_weigth
        normalized_min_weight, normalized_max_weight = (min_weigth / 12.0, max_weight / 12.0)
        weights = [normalized_min_weight, normalized_max_weight]

    cdd = np.average([celsius_min, celsius_max], weights=weights) - 18
    if fahrenheit:
        cdd = cdd * 1.8  # F = 1.8C + 32, 32 drops
    return max(cdd, 0)


def _calculate_K_index(tmp500, tmp700, tmp850, rh700, rh850, missing_value):
    """
    evaluate K index according to https://en.wikipedia.org/wiki/K-index_(meteorology)
    """
    # convert temperatures from Kelvin to Celsius
    tmp500_c = tmp500 - 273.15
    tmp700_c = tmp700 - 273.15
    tmp850_c = tmp850 - 273.15
    # evaluate dew point temperature for 700 and 850 mb
    t_dp_700 = _eval_dew_point_temperature(tmp700_c, rh700)
    t_dp_850 = _eval_dew_point_temperature(tmp850_c, rh850)

    # evaluate K index
    KI = (tmp850_c - tmp500_c) + t_dp_850 - (tmp700_c - t_dp_700)

    # remove missing values
    is_missing = np.logical_or(np.logical_or(np.logical_or(tmp500 == missing_value, tmp700 == missing_value),
                                             np.logical_or(tmp850 == missing_value, rh700 == missing_value)),
                               rh850 == missing_value)
    KI[is_missing] = missing_value

    return KI


def _eval_dew_point_temperature(tmp, rh):
    """
    evaluate dew point temperature according to https://en.wikipedia.org/wiki/Dew_point
    tmp: in Celsius, rh: in percent
    """
    b = 18.678
    c = 257.14
    d = 234.5
    # convert rh from percent to fraction, avoid zeros
    rh = (rh + .001) / 100
    gamma = np.log(rh * np.exp((b - tmp / d) * (tmp / (c + tmp))))
    dew_point_temperature = c * gamma / (b - gamma)

    return dew_point_temperature


def _calculate_ch_degrees_day(tmin, tmax, missing_value):
    """
    evaluate heating degrees day according to
    https://www.investopedia.com/terms/h/heatingdegreeday.asp
    https://www.investopedia.com/terms/c/colddegreeday.asp
    """
    # convert temperature from Kelvin to Fahrenheit
    t_mean = (tmin + tmax) / 2
    t_mean_fahrenheit = 9. / 5 * (t_mean - 273.15) + 32
    hdd = (65 - t_mean_fahrenheit)
    cdd = (t_mean_fahrenheit - 65)
    hdd[hdd < 0] = 0
    cdd[cdd < 0] = 0
    is_missing = np.logical_or(tmin == missing_value, tmax == missing_value)
    hdd[is_missing] = missing_value
    cdd[is_missing] = missing_value

    return hdd, cdd