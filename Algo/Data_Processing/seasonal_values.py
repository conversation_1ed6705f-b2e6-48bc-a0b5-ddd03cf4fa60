import sys
sys.path.append("...") # Adds higher directory to python modules path.

import pandas as pd
import numpy as np

from Algo.Viasualization.visualize_live_degdays import get_file_path
import os
from Algo.Utils.files_handle import HOME
from datetime import datetime as dtdt
from datetime import timedelta as td

seasonal_values_file = os.path.join(HOME,"degdays_archive","Live","Seasonal Values","","Seasonal_Values.csv")
# fullfiles_dir = os.path.join(HOME,"degdays_archive","FULL_FILES","Full")
fullfiles_dir = os.path.join(HOME,"degdays_archive","FULL_FILES","Last3M")


def _extend_seasonal_values_with_previous_years(seasonal_df,days_ahead=45,
                                                days_before=0,
                                                weight='population_US_weight'):
    # todo maybe do it offline one-time? might get heavy sometime in the future
    bad_rows_dates = seasonal_df.query('feature_type == "GDD" & Value == 0')['validation_day'].tolist()
    seasonal_df = seasonal_df[~seasonal_df['validation_day'].isin(bad_rows_dates)]
    max_date = seasonal_df['validation_day'].max()
    # generate a list of all days from max_date to tomorrow

    all_dates = pd.date_range(max_date - td(days=days_before), dtdt.now()+td(days=1+days_ahead), freq='D')
    extension_df = pd.concat([pd.DataFrame({'validation_day': all_dates,'feature_type':ft})
                              for ft in ['HDD','CDD','GDD']])
    extension_df['weight'] = weight
    extension_df['month'] = extension_df['validation_day'].dt.month
    extension_df['day'] = extension_df['validation_day'].dt.day

    seasonal_df_unique = seasonal_df.sort_values('validation_day')
    seasonal_df_unique['month'] = seasonal_df_unique['validation_day'].dt.month
    seasonal_df_unique['day'] = seasonal_df_unique['validation_day'].dt.day
    seasonal_df_unique = seasonal_df_unique.drop_duplicates(subset=['weight','feature_type','month', 'day'],keep='last')
    extension_df = extension_df.merge(seasonal_df_unique[['weight','feature_type','month','day','Value']],on=['weight','feature_type','month','day'],
                                      how='left')

    seasonal_df = seasonal_df.merge(extension_df.drop(['month','day'],axis=1),on=list(seasonal_df),how='outer')
    seasonal_df = seasonal_df.sort_values(['validation_day','feature_type'])
    return seasonal_df

def main_seasonal_calc(model='EPS45'):
    fullfile = r"%s\Full_degdays_Full_%s.csv"%(fullfiles_dir,model)
    for ft_type in ['HDD','CDD']:
        weight = 'population_US_weight'
        df = pd.read_csv(fullfile, parse_dates=['forecast_time', 'validation_day'], error_bad_lines=False)
        df = df[(df['feature_type']==ft_type)&(df['weight']=='population_US_weight')]
        df = df.drop_duplicates(subset=['validation_day'])
        df2 = df[['validation_day','%s 30Y Climo'%ft_type]]
        df2['feature_type'] = ft_type
        df2['weight'] = weight
        df2 = df2.rename(columns={'%s 30Y Climo'%ft_type:'Value'})

        #df2.to_csv(seasonal_values_file,index=False)
        seasonal_df = pd.read_csv(seasonal_values_file,parse_dates=['validation_day'])
        seasonal_df = seasonal_df.merge(df2,on=list(seasonal_df),how='outer').sort_values(['validation_day','feature_type']).drop_duplicates(subset=['validation_day','feature_type'])
        seasonal_df = _extend_seasonal_values_with_previous_years(seasonal_df,days_before=180,days_ahead=45,weight=weight)

        seasonal_df.to_csv(seasonal_values_file,index=False)


GDD_WEIGHTS = {'HDD': 1, 'CDD': 1}  ### must sum up to 2
def add_gdds(seasonal_file,weights=GDD_WEIGHTS):

    df = pd.read_csv(seasonal_file,parse_dates=['validation_day'])
    df = df[df['feature_type'].isin(['HDD','CDD'])]
    df['Value'][df['feature_type'] == 'HDD'] = (df['Value'][df['feature_type'] == 'HDD'] * weights['HDD']).fillna(0)
    df['Value'][df['feature_type'] == 'CDD'] = (df['Value'][df['feature_type'] == 'CDD'] * weights['CDD']).fillna(0)
    cols_to_group = [x for x in list(df) if sum([s in x for s in ['Value', 'feature_type']]) == 0]
    df_gdd = df[(df['feature_type'].isin(['HDD', 'CDD']))].groupby(cols_to_group).sum().reset_index()
    df_gdd['feature_type'] = 'GDD'

    df = df.merge(df_gdd,on=list(df),how='outer')
    df['Value'] = df['Value'].apply(lambda x: round(x,2))
    df = df.sort_values('validation_day')[['validation_day','Value','feature_type','weight']].drop_duplicates(subset=['validation_day','feature_type'])
    df.to_csv(seasonal_file,index=False)


def get_seasonal_gap_from_ec(suffix='v8'):
    final = pd.DataFrame()
    for ft in ['HDD','CDD','GDD']:
        a_df = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","XY_a_%s_%s.csv") % (ft, suffix),parse_dates=['date'])
        a_df['feature_type'] = ft
        for model in ['PARA','PARACO','GEFS','GFSCO','GEFSL','CFS']:
            a_df['%s_0-0_ECcorrection'%model] = np.round(a_df['Value_0Z_0-0_EC'] - a_df['Value_0Z_0-0_%s'%model],2)
            a_df['%s_0-0_ECcorrection' % model] = a_df['%s_0-0_ECcorrection'%model].fillna(method='ffill')
        if final.shape[0] == 0:
            final = a_df[['date','feature_type']+[x for x in list(a_df) if 'ECcorrection' in x]]
        else:
            common = [x for x in list(final) if x in list(a_df)]
            final = final.merge(a_df[['date','feature_type']+[x for x in list(a_df) if 'ECcorrection' in x]],on=common,how='outer')
    final = final.sort_values('date')
    final = final.rename(columns={'date':'validation_day'})
    final['validation_day'] = final['validation_day'].apply(lambda x: x.replace(hour=0))
    final.to_csv(os.path.join(HOME,"degdays_archive","Live","Seasonal Values","","Gaps_from_EC.csv"),index=False)


def main():
    main_seasonal_calc('EPS45')
    add_gdds(seasonal_values_file)
    get_seasonal_gap_from_ec(suffix='v8_12Z')

def add_gdd_to_ec_gaps():
    gaps_from_ec_file = os.path.join(HOME,"degdays_archive","Live","Seasonal Values","","Gaps_from_EC.csv")
    gaps_from_ec_file2 = os.path.join(HOME,"degdays_archive","Live","Seasonal Values","","Gaps_from_EC2.csv")
    df = pd.read_csv(gaps_from_ec_file,parse_dates=['validation_day'])
    df = df[df['feature_type'].isin(['HDD','CDD'])]
    gdd = df[[x for x in list(df) if x != 'feature_type']].groupby(['validation_day']).sum()
    gdd['feature_type'] = 'GDD'

    df = df.merge(gdd,on=list(df),how='outer').sort_values(['validation_day','feature_type']).fillna(0)
    df.to_csv(gaps_from_ec_file2)

if __name__ == '__main__':
    # main()
    main_seasonal_calc('EPS45')
