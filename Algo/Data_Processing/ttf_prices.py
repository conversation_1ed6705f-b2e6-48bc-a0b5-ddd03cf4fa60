import pandas as pd
import numpy as np
from datetime import timedelta as td

from Algo.Data_Retrieval import ttf_retrieval

def add_ttf_features(a,ticker='TTF=F'):
    candles_csv = {'TTF=F': ttf_retrieval.TTF_CANDLES_CSV,'MTF=F':ttf_retrieval.MTF_CANDLES_CSV}[ticker]
    live_scraped_prices_csv = {'TTF':ttf_retrieval.TTF_SCRAPED_PRICES_CSV,'MTF':ttf_retrieval.MTF_SCRAPED_PRICES_CSV}[ticker]

    candles_df = pd.read_csv(candles_csv).rename(columns={'index':'date'})
    candles_df['date'] = pd.to_datetime(candles_df['date'])+td(hours=8)
    live_scraping_df = pd.read_csv(live_scraped_prices_csv).rename(columns={'time_utc':'date'})

    prefix = {'TTF=F':'ttf','MTF=F':'mtf'}[ticker]
    candles_df['%s_close-open'%prefix] = candles_df['close'] - candles_df['open']
    candles_df['%s_close-open_d-1'%prefix] =  (candles_df['close'] - candles_df['open']).shift(1)
    candles_df['%s_open-close-1'%prefix] =  candles_df['open'] - candles_df['close'].shift(1)
    candles_df['%s_close-Prev1'%prefix] =  candles_df['close'].diff()
    candles_df['%s_open-Prev1'%prefix] =  candles_df['open'].diff()
    candles_df['%s_open-Prev2'%prefix] =  candles_df['open'].diff(2)
    candles_df['%s_open-Prev1_d-1'%prefix] =  candles_df['%s_open-Prev1'%prefix].shift(1)

    return a

if __name__ == '__main__':
    add_ttf_features(3)