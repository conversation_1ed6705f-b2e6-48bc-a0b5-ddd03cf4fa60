from tests.Rans_Draft import main as main_candles_handle
from Algo.Wrapper.wrap_process import wrap_full_asset_retrieval
from Algo.Xbox.generate_x_data import full_process_method2
from Algo.Trading.Rans_Draft_v2 import wrap_hisorical_data_ibridge, wrap_hisorical_data_ibapi

years = [2022]
months = [3,6,9,12][-1:]


from datetime import datetime as dtdt
wrap_full_asset_retrieval([], months, asset='NG',include_xy=True,skip_bollinger=True,
                          include_ys=False,
                          bollinger_start=dtdt(2017,11,1))
raise