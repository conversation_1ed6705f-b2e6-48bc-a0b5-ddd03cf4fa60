from selenium import webdriver
import os, shutil

import time
from bs4 import BeautifulSoup
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import os
from Algo.Utils.files_handle import HOME


GOLD_TRADER_FEATURES_CSV = os.path.join(HOME,"General_Stocks","GoldTrader_features.csv")
GOLD_TRADER_EXCEL_FEATURES_CSV = os.path.join(HOME,"General_Stocks","GoldTrader_Excel_features.csv")

GOLD_TRADER_LOGIN_URL = "https://bluegoldtrader.com/login.php"
GOLD_TRADER_MAIN_URL = "https://bluegoldtrader.com/dashboards?dash_id=299&preset=2"
USERNAME = "<EMAIL>"
PASSWORD = "uTuF64uvX8mBa"

CHROME_DRIVER = r"C:\Users\<USER>\drivers\chromedriver.exe" #r"C:\Users\<USER>\drivers\chromedriver.exe"

GOLD_TRADER_DROPBOX_URL = "https://www.dropbox.com/sh/msjunjhhqm78gj1/AAADu3KelcdZKY4yPthzmGGga?dl=0"
DROPBOX_PASSWORD = "VNSHTPLS!582301(xp"

def get_production_df(soup):
    # production recent days
    main_table = soup.find_all('table',{'class':'table table-thin table-bordered nomargin'})[0]
    production_table = main_table.find_all('tbody',{'id' :"tbody-sparkline"})[0]
    production_last_5_days = [int(x.get_text()) for x in production_table.find_all('tr')[0].find_all('td')[1:6]]
    supply_last_5_days = [int(x.get_text()) for x in production_table.find_all('tr')[5].find_all('td')[1:6]]
    demand_last_5_days = [int(x.get_text()) for x in production_table.find_all('tr')[6].find_all('td')[1:6]]
    total_balance = [int(x.get_text()) for x in production_table.find_all('tr')[7].find_all('td')[1:6]]
    dates = [dtdt.strptime(x.get_text().replace(' ',''),'%d-%b-%y') for x in main_table.find_all('tbody')[0].find_all('th')[1:6]]
    production_df = pd.DataFrame({'date':dates,'production':production_last_5_days,
                                  'supply':supply_last_5_days,'demand':demand_last_5_days,
                                  'balance':total_balance})
    d = {}
    for n, lst in zip(['production',
                       'total_supply', 'total_demand', 'total_balance'],
                      [production_last_5_days, supply_last_5_days, demand_last_5_days,
                       total_balance]):
        for days_before in range(5):
            key = n+'_-%sd'%days_before
            d[key] = lst[5-days_before-1]
    df = pd.DataFrame(d, index=[0])
    df['date'] = dates[-1]
    return df, dates

def get_sentiment(soup,dates):
    try:
        morning_update = [x for x in soup.find_all('div',{'class':'entry-content'})][-1]
        recommendation_line = [x for x in morning_update.get_text().split('\n') if len(x) and 'stance' in x][0]
        recommendation = 0
        if 'bearish' in recommendation_line:
            recommendation = -1
        elif 'bullish' in recommendation_line:
            recommendation = 1
        recommendation_df = pd.DataFrame({'date':dates[-1:],'bias':[recommendation]})
        return recommendation_df
    except:
        recommendation_df = pd.DataFrame({'date':dates[-1:],'bias':0})
    return recommendation_df

def get_historical_balances(soup):
    main_table2 = soup.find_all('table',{'class':'table table-thin table-bordered nomargin'})[1]
    dates = [dtdt.strptime(x.get_text().replace(' ',''),'%d-%b-%y') for x in main_table2.find_all('tr')[0].find_all('th')[1:]]
    production_forecast = [int(x.get_text()) for x in main_table2.find_all('tr')[1].find_all('td')[1:]]
    production_vs_1y_forecast = [int(x.get_text()) for x in main_table2.find_all('tr')[2].find_all('td')[1:]]
    production_vs_5y_forecast = [int(x.get_text()) for x in main_table2.find_all('tr')[3].find_all('td')[1:]]
    total_supply = [int(x.get_text()) for x in main_table2.find_all('tr')[16].find_all('td')[1:]]
    total_demand = [int(x.get_text()) for x in main_table2.find_all('tr')[19].find_all('td')[1:]]
    total_demand_vs_1y = [int(x.get_text()) for x in main_table2.find_all('tr')[20].find_all('td')[1:]]
    total_demand_vs_5y = [int(x.get_text()) for x in main_table2.find_all('tr')[21].find_all('td')[1:]]
    d = {}
    for n,lst in zip(['production', 'production_Vs_1y','production_Vs_5y',
                      'total_supply','total_demand','demand_Vs_1y','demand_Vs_5y'],
                     [production_forecast,production_vs_1y_forecast,production_vs_5y_forecast,
                      total_supply,total_demand,total_demand_vs_1y,total_demand_vs_5y]):
        for week_ahead in range(1,7):
            d[n+'_week%s'%week_ahead] = lst[week_ahead-1]
    df = pd.DataFrame(d,index=[0])
    return df

def get_injections_stats(soup):
    pass


def parse_fundamentals_excel(days_back = 0):
    if os.path.exists(GOLD_TRADER_EXCEL_FEATURES_CSV):
        original_df = pd.read_csv(GOLD_TRADER_EXCEL_FEATURES_CSV,parse_dates=['date'])
    else:
        original_df = pd.DataFrame()

    fundamentals_excel_file = os.path.join(HOME,"General_Stocks","Bluegold_Trader","Natural Gas Fundamentals.xlsx")
    balance_df = pd.read_excel(fundamentals_excel_file,sheet_name='BALANCE')
    prices_df = pd.read_excel(fundamentals_excel_file,sheet_name='PRICES')

    balance_df2 = balance_df[2:][list(balance_df)[1:]].set_index(list(balance_df)[1]).T.rename(columns={'Dates': 'Date'})
    balance_df2.iloc[11:14]['Date'] = ['forecast', 'growth', 'vs_1_year']
    balance_df_basic = balance_df2.set_index('Date').iloc[:16]
    balance_df_production = balance_df2.set_index('Date')[16:24]

    balance_df_basic.columns = [str(x).replace('*','') for x in balance_df_basic.columns]

    relevant_cols = ['Wind impact','Production','Net exports','Net supply',
                     'N-D-D Net impact','Total supply','Total demand','Total balance']
    new_names = ['wind_effect','Production','exports','net_supply','non_weather_impact',
                 'total_supply','total_demand','total_balance']

    final_dict = {}

    for col_name,new_name in zip(relevant_cols,new_names):
        c = balance_df_basic[col_name]
        assert 'd-o-d' == c.index.tolist()[6],'we assumed todays values in in index=5, but look like not'
        if days_back > 0:
            c2 = c - (c[5]-c[5-days_back])
        else:
            c2 = c
        date = c.index.tolist()[5-days_back]
        bb = 0
        dod = c2[6] if days_back == 0 else c[5-days_back] - c[5-days_back-1]
        val_today,val_vs_yest,val_vs_7d_avg,val_vs_30d_avg,val_vs_1y = c[5-days_back],dod,c2[7],c2[8],c2[13]
        d = {'%s_today'%new_name:val_today,'%s_vs_1d'%new_name:val_vs_yest,
             '%s_vs_7d'%new_name:val_vs_7d_avg,'%s_vs_30d'%new_name:val_vs_30d_avg,
             '%s_vs_1y'%new_name:val_vs_1y,'date':date}
        final_dict.update(d)

    df = pd.DataFrame(final_dict,index=[0])
    if original_df.shape[0]>0:
        original_df = original_df.merge(df,on=list(original_df),how='outer')
    else:
        original_df = df
    original_df = original_df[['date']+[x for x in list(original_df) if x != 'date']].sort_values('date')
    original_df.to_csv(GOLD_TRADER_EXCEL_FEATURES_CSV,index=False)
    aa = 1


def dropbox_excel_download():

    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')
    driver = webdriver.Chrome(executable_path=CHROME_DRIVER, chrome_options=chrome_options)
    driver.get(GOLD_TRADER_DROPBOX_URL)
    input_pass = driver.find_element_by_id("shared-link-password")
    input_pass.send_keys(DROPBOX_PASSWORD)
    tries = 0
    success = False
    while tries < 8 and not success:
        try:
            continue_button = driver.find_element_by_class_name('mc-button-content')
            time.sleep(0.1)
            continue_button.click()
            succes = True
        except:
            time.sleep(0.5)
        finally:
            time.sleep(1)
            tries += 1
    EXCEL_URL = "https://www.dropbox.com/sh/qslnpsggd8jl3k6/AADke1d1QQ-Ka5NAn2r9ea2ba/Natural%20Gas%20Fundamentals.xlsx?dl=0"
    driver.get(EXCEL_URL)
    time.sleep(4)
    substrings = ["dig-Button-content","dig-Menu-row-title"] #'download-file'
    tries = 0
    while tries < 10 and not success:
        substring = substrings[tries%2]
        try:
            download_button = driver.find_elements_by_xpath("//*[contains(@class, '%s')]" % substring)[0]
            download_button.click()
            succes = True
        except:
            time.sleep(1)
        finally:
            tries += 1
    download_path = r"C:\Users\<USER>\Downloads\Natural Gas Fundamentals.xlsx"
    dest_path = os.path.join(HOME,"General_Stocks","Bluegold_Trader","Natural Gas Fundamentals.xlsx")
    try:
        shutil.move(download_path,dest_path)
    except:
        print('didnt find Excel file in Downloads directory... skipping')
    driver.close()
    bb = 0



def main():
    try:
        original_df = pd.read_csv(GOLD_TRADER_FEATURES_CSV,parse_dates=['date'])
    except:
        original_df = pd.DataFrame()
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')
    driver = webdriver.Chrome(executable_path=CHROME_DRIVER, chrome_options=chrome_options)
    driver.get(GOLD_TRADER_LOGIN_URL)
    time.sleep(2)

    input_email = driver.find_element_by_name("email")
    input_email.send_keys(USERNAME)
    input_pass = driver.find_element_by_name("password")
    input_pass.send_keys(PASSWORD)
    submit_button = driver.find_element_by_name("submit")
    time.sleep(2)
    submit_button.click()

    driver.get(GOLD_TRADER_MAIN_URL)
    time.sleep(2)

    html = driver.page_source
    soup = BeautifulSoup(html)

    production_df,dates = get_production_df(soup)
    recommendation_df = get_sentiment(soup,dates)
    historical_df = get_historical_balances(soup)
    historical_df['date'] = production_df['date'].iloc[0]
    # injections_df = get_injections_stats(soup)

    final_df = production_df.merge(historical_df,on=['date']).merge(recommendation_df,on=['date'])
    if original_df.shape[0] == 0:
        original_df = final_df
    else:
        original_df = original_df.merge(final_df,on=list(original_df),how='outer').drop_duplicates(subset=['date'],keep='last')
    original_df[['date']+[x for x in list(original_df) if 'date' not in x]].to_csv(GOLD_TRADER_FEATURES_CSV,index=False)

def wrap_bluegold_excel_handling():
    if os.path.exists(GOLD_TRADER_EXCEL_FEATURES_CSV):
        original_df = pd.read_csv(GOLD_TRADER_EXCEL_FEATURES_CSV,parse_dates=['date'])
        today = dtdt.today().date()
        if original_df[original_df['date'].dt.date==today].shape[0] == 0:
            dropbox_excel_download()
            parse_fundamentals_excel()
        else:
            print('Already have data for: %s'%today)


if __name__ == '__main__':
    main()
    # wrap_bluegold_excel_handling()
    # parse_fundamentals_excel(3)