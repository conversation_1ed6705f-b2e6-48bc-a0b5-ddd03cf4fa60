####### IMPORTS #######
import os
from datetime import datetime
import subprocess
from argparse import ArgumentParser
import os.path
# internal packages:
from sys import path as sPath
from os import path as oPath

####### CONSTANTS #######
FIELDS = {'ugrd725': ':UGRD:725 mb:', 'tmp525': ':TMP:525 mb:', 'ugrd375': ':UGRD:375 mb:', 'vvel625': ':VVEL:625 mb:', 'vgrd550': ':VGRD:550 mb:', 'clmr925': ':CLWMR:925 mb:', 'rh100': ':RH:100 mb:', 'tmp350': ':TMP:350 mb:', 'tmp825': ':TMP:825 mb:', 'acpcp': ':ACPCP:surface:', 'vgrd250': ':VGRD:250 mb:', 'hgt625': ':HGT:625 mb:', 'cfrzr': ':CFRZR:', 'tmp725': ':TMP:725 mb:', 'vgrd60to90': ':VGRD:90-60 mb above ground:', 'ugrd120to150': ':UGRD:150-120 mb above ground:', 'tmp400': ':TMP:400 mb:', 'CIN90': ':CIN:90-0 mb above ground:', 'tmp200': ':TMP:200 mb:',
		  'vvel250': ':VVEL:250 mb:', 'hgt925': ':HGT:925 mb:', 'hlcy1000': ':HLCY:1000', 'hgt800': ':HGT:800 mb:', 'cicep': ':CICEP:', 'vvel425': ':VVEL:425 mb:', 'hlcy3000': ':HLCY:3000', 'vgrd30to60': ':VGRD:60-30 mb above ground:', 'clmr875': ':CLWMR:875 mb:', 'rh450': ':RH:450 mb:', 'clmr850': ':CLWMR:850 mb:', 'hgt325': ':HGT:325 mb:', 'ugrd60to90': ':UGRD:90-60 mb above ground:', 'vgrd625': ':VGRD:625 mb:', 'ugrd650': ':UGRD:650 mb:', 'ugrd300': ':UGRD:300 mb:', 'ncpcp': ':NCPCP:', 'vvel1000': ':VVEL:1000 mb:', 'hgt125': ':HGT:125 mb:', 'vgrd700': ':VGRD:700 mb:', 'tmp_surf': ':TMP:', 
		  'tmp750': ':TMP:750 mb:', 'clmr600': ':CLWMR:600 mb:', 'clmr350': ':CLWMR:350 mb:', 'hgt975': ':HGT:975 mb:', 'clmr175': ':CLWMR:175 mb:', 'ugrd275': ':UGRD:275 mb:', 'vgrd350': ':VGRD:350 mb:', 'vgrd0to30': ':VGRD:30-0 mb above ground:', 'tmp325': ':TMP:325 mb:', 'hgt225': ':HGT:225 mb:', 'ugrd475': ':UGRD:475 mb:', 'ugrd200': ':UGRD:200 mb:', 'rh225': ':RH:225 mb:', 'ugrd1000': ':UGRD:1000 mb:', 'rh875': ':RH:875 mb:', 'ugrd875': ':UGRD:875 mb:', 'rh0to30': ':RH:30-0 mb above ground:', 'vgrd_max': ':VGRD:max wind:', 'clmr675': ':CLWMR:675 mb:', 'vvel150': ':VVEL:150 mb:', 
		  'tmp125': ':TMP:125 mb:', 'vvel900': ':VVEL:900 mb:', 'vgrd825': ':VGRD:825 mb:', 'rh500': ':RH:500 mb:', 'tmp975': ':TMP:975 mb:', 'vis': ':VIS:', 'vvel550': ':VVEL:550 mb:', 
		  'hgt100': ':HGT:100 mb:', 'rh850': ':RH:850 mb:', 'hgt425': ':HGT:425 mb:', 'clmr450': ':CLWMR:450 mb:', 'tmp600': ':TMP:600 mb:', 'rh625': ':RH:625 mb:', 'pot2m': ':POT:2 m above ground:', 
		  'snod': ':SNOD:', 'ugrd700': ':UGRD:700 mb:', 'vvel60to90': ':VVEL:90-60 mb above ground:', 'hgt500': ':HGT:500 mb:', 'mcdc': ':MCDCHCDC:', 'ugrd175': ':UGRD:175 mb:', 'vgrd300': ':VGRD:300 mb:', 'rh950': ':RH:950 mb:', 'vgrd650': ':VGRD:650 mb:', 'vvel30to60': ':VVEL:60-30 mb above ground:', 'ugrd350': ':UGRD:350 mb:', 'vgrd475': ':VGRD:475 mb:', 'CIN255': ':CIN:255-0 mb above ground:', 'spfh2m': ':SPFH:2 m above ground:', 'vvel850': ':VVEL:850 mb:', 'tmp700': ':TMP:700 mb:', 'rh125': ':RH:125 mb:', 'clmr900': ':CLWMR:900 mb:', 'vgrd575': ':VGRD:575 mb:', 'ugrd850': ':UGRD:850 mb:', 'ugrd600': ':UGRD:600 mb:', 'vvel175': ':VVEL:175 mb:', 'vvel275': ':VVEL:275 mb:', 'tmp150to180': ':TMP:180-150 mb above ground:', 'pressure2m': ':PRES:2 m above ground:', 'prate': ':PRATE:', 'ssrun': ':SSRUN:', 'hgt1000': ':HGT:1000 mb:', 'tmp225': ':TMP:225 mb:', 'tmp425': ':TMP:425 mb:', 
		  'vvel375': ':VVEL:375 mb:', 'rh900': ':RH:900 mb:', 'rh1000': ':RH:1000 mb:', 'vvel925': ':VVEL:925 mb:', 'ugrd575': ':UGRD:575 mb:', 'clmr275': ':CLWMR:275 mb:', 'clmr1000': ':CLWMR:1000 mb:', 'vgrd950': ':VGRD:950 mb:', 'tmp0to30': ':TMP:30-0 mb above ground:', 'rh2m': ':RH:2 m above ground:', 'rh825': ':RH:825 mb:', 'clmr100': ':CLWMR:100 mb:', 'vvel400': ':VVEL:400 mb:', 'ugrd150to180': ':UGRD:180-150 mb above ground:', 'vgrd150': ':VGRD:150 mb:', 'vgrd400': ':VGRD:400 mb:', 'vgrd80m': ':VGRD:80 m above ground:', 'clmr500': ':CLWMR:500 mb:', 'vgrd375': ':VGRD:375 mb:', 'vvel150to180': ':VVEL:180-150 mb above ground:', 'ugrd_tropopause': ':UGRD:tropopause:', 'clmr375': ':CLWMR:375 mb:', 'hgt450': ':HGT:450 mb:', 'ugrd675': ':UGRD:675 mb:', 'clmr150': ':CLWMR:150 mb:', 'hgt850': ':HGT:850 mb:', 'hgt675': ':HGT:675 mb:', 'ugrd500': ':UGRD:500 mb:', 
		  'clmr650': ':CLWMR:650 mb:', 'rh650': ':RH:650 mb:', 'vgrd_tropopause': ':VGRD:tropopause:', 'vvel675': ':VVEL:675 mb:', 'rh150': ':RH:150 mb:', 'hgt775': ':HGT:775 mb:', 'vgrd500': ':VGRD:500 mb:', 'tmp300': ':TMP:300 mb:', 'tmp650': ':TMP:650 mb:', 
		  'rh200': ':RH:200 mb:', 'ltng': ':LTNG:', 'rh425': ':RH:425 mb:', 'tmp550': ':TMP:550 mb:', 'vvel575': ':VVEL:575 mb:', 'tmp90to120': ':TMP:120-90 mb above ground:', 'tmp250': ':TMP:250 mb:', 'clmr400': ':CLWMR:400 mb:', 'vvel775': ':VVEL:775 mb:', 'clmr750': ':CLWMR:750 mb:', 'tmp850': ':TMP:850 mb:', 'vgrd775': ':VGRD:775 mb:', 'vvel90to120': ':VVEL:120-90 mb above ground:', 'vgrd850': ':VGRD:850 mb:', 'rh120to150': ':RH:150-120 mb above ground:', 'vvel0to30': ':VVEL:30-0 mb above ground:', 'rh925': ':RH:925 mb:', 
		  'rh525': ':RH:525 mb:', 'vvel475': ':VVEL:475 mb:', 'ugrd0to30': ':UGRD:30-0 mb above ground:', 'vgrd275': ':VGRD:275 mb:', 'hgt350': ':HGT:350 mb:', 'bgrun': ':BGRUN:', 'hgt600': ':HGT:600 mb:', 'vgrd90to120': ':VGRD:120-90 mb above ground:', 'clmr575': ':CLWMR:575 mb:', 'tmp60to90': ':TMP:90-60 mb above ground:', 'tmp2m': ':TMP:2 m above ground:', 'rh350': ':RH:350 mb:', 'vgrd675': ':VGRD:675 mb:', 'depr2m': ':DEPR:2 m above ground:', 'tmp625': ':TMP:625 mb:', 'clmr475': ':CLWMR:475 mb:', 'tmp150': ':TMP:150 mb:', 'vvel350': ':VVEL:350 mb:', 'vvel100': ':VVEL:100 mb:', 'clmr700': ':CLWMR:700 mb:', 'hgt175': ':HGT:175 mb:', 'hgt700': ':HGT:700 mb:', 'ugrd950': ':UGRD:950 mb:', 'ugrd625': ':UGRD:625 mb:', 'hgt825': ':HGT:825 mb:', 'vvel700': ':VVEL:700 mb:', 'vgrd100': ':VGRD:100 mb:', 'ugrd_max': ':UGRD:max wind:', 'ugrd825': ':UGRD:825 mb:', 
		  'vgrd150to180': ':VGRD:180-150 mb above ground:', 'rh800': ':RH:800 mb:', 'ugrd900': ':UGRD:900 mb:', 'cloud_top': ':HGT:cloud top:', 'tmp775': ':TMP:775 mb:', 'clmr975': ':CLWMR:975 mb:', 'tmp900': ':TMP:900 mb:', 'csnow': ':CSNOW:', 'ugrd100': ':UGRD:100 mb:', 
		  'CIN180': ':CIN:180-0 mb above ground:', 'hgt575': ':HGT:575 mb:', 'weasd': ':WEASD:', 'vgrd120to150': ':VGRD:150-120 mb above ground:', 'vvel500': ':VVEL:500 mb:', 'rh90to120': ':RH:120-90 mb above ground:', 'clmr250': ':CLWMR:250 mb:', 'rh275': ':RH:275 mb:', 'rh60to90': ':RH:90-60 mb above ground:', 'ptend': ':PTEND:', 'vgrd875': ':VGRD:875 mb:', 'con_cloud_top': ':HGT:convective cloud top level:', 'ugrd225': ':UGRD:225 mb:', 'clmr825': ':CLWMR:825 mb:', 'vgrd1000': ':VGRD:1000 mb:', 'tmp925': ':TMP:925 mb:', 
		  'ugrd450': ':UGRD:450 mb:', 'hgt200': ':HGT:200 mb:', 'CAPEsurf': ':CAPE:surface:', 'hgt475': ':HGT:475 mb:', 'vgrd175': ':VGRD:175 mb:', 'hgt650': ':HGT:650 mb:', 'tmp275': ':TMP:275 mb:', 'clmr725': ':CLWMR:725 mb:', 'vgrd200': ':VGRD:200 mb:', 'tmp120to150': ':TMP:150-120 mb above ground:', 'tcdc': ':TCDC:', 'rh675': ':RH:675 mb:', 'ugrd525': ':UGRD:525 mb:', 'clmr425': ':CLWMR:425 mb:', 'ugrd325': ':UGRD:325 mb:', 'hgt300': ':HGT:300 mb:', 'vvel825': ':VVEL:825 mb:', 'vvel325': ':VVEL:325 mb:', 'tmp475': ':TMP:475 mb:', 'lcdc': ':LCDC:', 'vvel450': ':VVEL:450 mb:', 'rh30to60': ':RH:60-30 mb above ground:', 'vvel200': ':VVEL:200 mb:', 'clmr950': ':CLWMR:950 mb:', 'tmp575': ':TMP:575 mb:', 'rh725': ':RH:725 mb:', 'clmr300': ':CLWMR:300 mb:', 'gust': ':GUST:', 'vvel875': ':VVEL:875 mb:', 'vvel120to150': ':VVEL:150-120 mb above ground:', 
		  'vgrd900': ':VGRD:900 mb:', 'CAPE255': ':CAPE:255-0 mb above ground:', 'vgrd750': ':VGRD:750 mb:', 'vvel750': ':VVEL:750 mb:', 'rh550': ':RH:550 mb:', 'rh700': ':RH:700 mb:', 'rh250': ':RH:250 mb:', 'ugrd750': ':UGRD:750 mb:', 'ugrd425': ':UGRD:425 mb:', 'hgt275': ':HGT:275 mb:', 
		  'hgt900': ':HGT:900 mb:', 'clmr225': ':CLWMR:225 mb:', 'tmp800': ':TMP:800 mb:', 'CAPE90': ':CAPE:90-0 mb above ground:', 'clmr550': ':CLWMR:550 mb:', 'hgt375': ':HGT:375 mb:', 'ugrd775': ':UGRD:775 mb:', 'vgrd975': ':VGRD:975 mb:', 'rh775': ':RH:775 mb:', 'ugrd250': ':UGRD:250 mb:', 'vvel800': ':VVEL:800 mb:', 'ugrd925': ':UGRD:925 mb:', 'vvel125': ':VVEL:125 mb:', 'rh600': ':RH:600 mb:', 'ugrd550': ':UGRD:550 mb:', 'vgrd450': ':VGRD:450 mb:', 'clmr125': ':CLWMR:125 mb:', 'tmp175': ':TMP:175 mb:', 'ugrd800': ':UGRD:800 mb:', 'vvel650': ':VVEL:650 mb:', 'vgrd800': ':VGRD:800 mb:', 'rh325': ':RH:325 mb:', 'tmp375': ':TMP:375 mb:', 'tmp500': ':TMP:500 mb:', 'hgt950': ':HGT:950 mb:', 'vvel600': ':VVEL:600 mb:', 'hgt_surf': ':HGT:', 'hgt750': ':HGT:750 mb:', 'vgrd600': ':VGRD:600 mb:', 'vvel525': ':VVEL:525 mb:', 'rh475': ':RH:475 mb:', 
		  'CAPE180': ':CAPE:180-0 mb above ground:', 
		  'hgt550': ':HGT:550 mb:', 'epot': ':EPOT:', 'vvel725': ':VVEL:725 mb:', 'ugrd125': ':UGRD:125 mb:', 'vgrd525': ':VGRD:525 mb:', 'rh175': ':RH:175 mb:', 'rh300': ':RH:300 mb:', 'clmr625': ':CLWMR:625 mb:', 'hgt875': ':HGT:875 mb:', 'clmr800': ':CLWMR:800 mb:', 'CINsurf': ':CIN:surface:', 'vgrd725': ':VGRD:725 mb:', 'rh575': ':RH:575 mb:', 'tmp1000': ':TMP:1000 mb:', 'ugrd10m': ':UGRD:10 m above ground:', 'rh750': ':RH:750 mb:', 'vvel950': ':VVEL:950 mb:', 'hpbl': ':HPBL:', 'vvel225': ':VVEL:225 mb:', 'clmr525': ':CLWMR:525 mb:', 'rh975': ':RH:975 mb:', 'vgrd225': ':VGRD:225 mb:', 'ugrd80m': ':UGRD:80 m above ground:', 'vgrd425': ':VGRD:425 mb:', 'rh150to180': ':RH:180-150 mb above ground:', 'tmp675': ':TMP:675 mb:', 'tmp450': ':TMP:450 mb:', 'vvel300': ':VVEL:300 mb:', 'tmp875': ':TMP:875 mb:', 'crain': ':CRAIN:', 'tmp950': ':TMP:950 mb:', 'clmr775': ':CLWMR:775 mb:', 
		  'tmp30to60': ':TMP:60-30 mb above ground:', 'sea_level2m': ':MSLMA:2 m above ground:', 'tmp100': ':TMP:100 mb:', 'ugrd975': ':UGRD:975 mb:', 'hgt400': ':HGT:400 mb:', 'rh400': ':RH:400 mb:', 'vgrd925': ':VGRD:925 mb:', 'ugrd90to120': ':UGRD:120-90 mb above ground:', 'cloud_base': ':HGT:cloud base:', 'ugrd30to60': ':UGRD:60-30 mb above ground:', 'vgrd10m': ':VGRD:10 m above ground:', 'clmr325': ':CLWMR:325 mb:', 'hgt725': ':HGT:725 mb:', 'vvel975': ':VVEL:975 mb:', 'dpt2m': ':DPT:2 m above ground:', 'rh375': ':RH:375 mb:', 'vgrd325': ':VGRD:325 mb:', 'hgt150': ':HGT:150 mb:', 'mstav': ':MSTAV:', 'clmr200': ':CLWMR:200 mb:', 'vgrd125': ':VGRD:125 mb:', 'hgt525': ':HGT:525 mb:', 'hgt250': ':HGT:250 mb:', 'ugrd400': ':UGRD:400 mb:', 'ugrd150': ':UGRD:150 mb:',
		  'hgt10': ':HGT:10 mb:', 'tmp10':':TMP:10 mb:',
		  'tmax': ':TMAX:2 m above ground:', 'tmin':':TMIN:2 m above ground:',
		  'apcp':':APCP:', 'cprat': ':CPRAT:'
		  }
US_FIELDS = ['ltng', 'vgrd80m', 'hgt700', 'CAPEsurf', 'hlcy3000', 'rh2m', 'rh850', 'ugrd10m', 'tmp500', 'vgrd10m', 'tcdc', 'tmp2m', 'gust', 'vgrd850', 'ugrd80m', 'ugrd850', 'vis', 'rh700', 'tmp700', 'CAPE90', 'hgt500', 'hlcy1000', 'CAPE255', 'tmp850', 'CAPE180']
EU_FIELDS = ['CAPE180', 'hgt700', 'clmr700', 'hlcy3000', 'rh700', 'tmp700', 'hgt500', 'vgrd850', 'clmr500', 'CAPEsurf', 'gust', 'CAPE255', 'clmr850', 'ugrd80m', 'vgrd80m', 'ugrd10m', 'tmp2m', 'tmp850', 'ugrd850', 'vgrd10m', 'rh850', 'tmp500', 'rh2m']
US_GFS_FIELDS = EU_FIELDS +['hgt10', 'tmp10', 'hgt100', 'tmp100', 'tmax', 'tmin', 'apcp']
FULL_GFS_FIELDS = EU_FIELDS + ['hgt10', 'tmp10', 'hgt100', 'tmp100', 'tmax', 'tmin', 'cprat', 'prate', 'apcp', 'acpcp']

US_GFS_FIELDS_RAN = ['tmp10', 'tmax', 'tmin', 'apcp','hgt500', 'tmp500', 'tmp850']

US_LAT_LON = (22, 51.5, -127, -64.5)
EU_LAT_LON = (35, 60, 0, 45)
FULL_GFS_LAT_LON = (-70, 90, -179.5, 180)

CONFIGURATION = {
	'NG_USA': {
		'model': 'RAP', 'mg_resolution': 0.5, 'relevant_fields': US_FIELDS,
		'lat_min': US_LAT_LON[0], 'lat_max': US_LAT_LON[1], 'lon_min': US_LAT_LON[2], 'lon_max': US_LAT_LON[3],
		'grib_type': 'mg', 'zone': 'USA'},
		
	'NG_USA_GFS': {
		'model': 'GFS', 'mg_resolution': 0.5, 'relevant_fields': US_GFS_FIELDS,
		'lat_min': US_LAT_LON[0], 'lat_max': US_LAT_LON[1], 'lon_min': US_LAT_LON[2], 'lon_max': US_LAT_LON[3],
		'grib_type': 'mg', 'zone': 'USA'},
	'NG_USA_GFS_RAN': {
		'model': 'GFS', 'mg_resolution': 0.5, 'relevant_fields': US_GFS_FIELDS_RAN,
		'lat_min': US_LAT_LON[0], 'lat_max': US_LAT_LON[1], 'lon_min': US_LAT_LON[2], 'lon_max': US_LAT_LON[3],
		'grib_type': 'mg', 'zone': 'USA'},
	'EPEX': {
		'model': 'GFS', 'mg_resolution': 0.5, 'relevant_fields': EU_FIELDS, 
		'lat_min': EU_LAT_LON[0], 'lat_max': EU_LAT_LON[1], 'lon_min': EU_LAT_LON[2], 'lon_max': EU_LAT_LON[3],
		'grib_type': 'mg', 'zone': 'EU'},
	'FULL_GFS': {
		'model': 'GFS', 'mg_resolution': 0.5, 'relevant_fields': FULL_GFS_FIELDS, 
		'lat_min': FULL_GFS_LAT_LON[0], 'lat_max': FULL_GFS_LAT_LON[1], 'lon_min': FULL_GFS_LAT_LON[2], 'lon_max': FULL_GFS_LAT_LON[3],
		'grib_type': 'midi', 'zone': None}
			   }


####### CODE #######

def generate_mg_file(grib_filename, mg_filename, configuration):
	
	if isinstance(configuration, str):
		configuration = CONFIGURATION[configuration]
	
	fields_str = '"(%s)"' %(')|('.join([FIELDS[field] for field in configuration['relevant_fields']]))
	
	latlon_str = ''
	if configuration['lat_min']:
		lat_steps = int((configuration['lat_max']-configuration['lat_min']) \
						/ configuration['mg_resolution'] + 1)
		lat_str = '%s:%s:%s' %(configuration['lat_min'], lat_steps, configuration['mg_resolution'])
		lon_steps = int((configuration['lon_max']-configuration['lon_min']) \
						/ configuration['mg_resolution'] + 1)
		lon_str = '%s:%s:%s' %(configuration['lon_min'], lon_steps, configuration['mg_resolution'])
		#latlon_str = 'latlon %s %s' %(lat_str, lon_str)
		latlon_str = 'latlon %s %s' %(lon_str, lat_str)
	
	wgrib_cmd = ('wgrib2 %s -match %s -set_grib_type same -new_grid_winds earth'
				 ' -new_grid %s %s >/dev/null') %(grib_filename, fields_str, latlon_str, mg_filename)
	
	flag = subprocess.call(wgrib_cmd, shell=True)
	return flag


def parse_grb_folder(grib_folder, mg_folder, t_start, t_end, horizon, configuration, logger):
	
	grib_files = []
	for file_name in os.listdir(grib_folder):
		(is_valid, file_horizon, file_time) = is_valid_grib(file_name, 
							configuration['model'], 'grib', zone = None)
		if is_valid:
			if (file_time >= t_start) & (file_time <= t_end) & (file_horizon == horizon):
				grib_files.append(file_name)
	logger.info('found %s relevant grib files. starting parsing to mini-grib, %s' %(
			len(grib_files), datetime.now()))

	for i, grib_file in enumerate(grib_files):
		grib_filename = '%s%s' %(grib_folder, grib_file)
		if configuration['grib_type'] == 'mg':
			extension = '%s.small' %(configuration['zone'])
		elif configuration['grib_type'] == 'midi':
			extension = 'midi'
		mg_filename = '%s%s.%s' %(mg_folder, grib_file, extension)
		if os.path.isfile(mg_filename):
			logger.info('%s already exists. skipping..., %s out of %s, %s' %(mg_filename, i+1, len(grib_files), datetime.now()))
		else:
			logger.info('generating %s, %s out of %s, %s' %(mg_filename, i+1, len(grib_files), datetime.now()))
			flag = generate_mg_file(grib_filename, mg_filename, configuration)
			if flag != 0 :
				logger.warning('failed to parse %s. return code = %s' %(grib_filename, flag))
			elif os.path.getsize(mg_filename) == 0:
				logger.warning('an empty file was generated while parsing %s to %s. please check' %(grib_filename, mg_filename))

"""_________________________________________________________________________"""

if __name__ == "__main__":
	
	parser = ArgumentParser(
		description = 'parse grib file into an mg file')
	# set arguments
	parser.add_argument('grib_folder',  help='input folder',  type=str)
	parser.add_argument('mg_folder', help='output folder', type=str)
	parser.add_argument('configuration', help='contraction configuration', 
			choices = ['NG_USA', 'NG_USA_GFS', 'EPEX', 'FULL_GFS'], type=str)    
	parser.add_argument('horizon', help='forecast horizon', type = int)    
	parser.add_argument('--t_start', '-ts', help='start date (dd/mm/yyyy_HH:MM:SS).', 
						type=ml_time, default=datetime(1970, 1, 1))  
	parser.add_argument('--t_end', '-te', help='end date (dd/mm/yyyy_HH:MM:SS).', 
						type=ml_time, default=datetime(2030, 1, 1))
	# logging parameters
	parser.add_argument('--log_file', '-log', help='name of log file', type=str, default = '/tmp/log.log')
	parser.add_argument('--err_file', '-err', help='name of exceptions file', type=str, default = '/tmp/err.err')  
	
	# read arguments and set logger  
	args = parser.parse_args()
	logger = get_logger('grib2mg', args.log_file)
	logger.info('starting parsing of grib files to mini / midi gribs')
	
	if args.grib_folder[-1] != '/': 
		args.grib_folder = '%s/' %(args.grib_folder)
	if args.mg_folder[-1] != '/': 
		args.mg_folder = '%s/' %(args.mg_folder)
	
	if not os.path.isdir(args.mg_folder):
		logger.warning('requested mg folder %s does not exist and will be generated' %(args.folder_out))
		os.makedirs(args.mg_folder)
	configuration = CONFIGURATION[args.configuration]
	parse_grb_folder(args.grib_folder, args.mg_folder, args.t_start, args.t_end, 
					 args.horizon, configuration, logger)   