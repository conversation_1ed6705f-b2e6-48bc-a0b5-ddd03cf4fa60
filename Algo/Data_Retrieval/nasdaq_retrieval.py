# from tests.Rans_Draft import main as main_candles_handle
from Algo.Wrapper.wrap_process import wrap_full_asset_retrieval
from Algo.Xbox.generate_x_data import full_process_method2
from Algo.Trading.Rans_Draft_v2 import wrap_hisorical_data_ibridge, wrap_hisorical_data_ibapi
from Algo.Utils.yaml_handle import set_real_mode, set_paper_mode, set_real_mode_cmd,set_paper_mode_cmd

from Algo.Utils.files_handle import PROJECT_ROOT
from subprocess import check_call
from datetime import datetime as dtdt
from datetime import timedelta as td

DEBUG = False
# DEBUG = True


def retrieve_date(asset_name='NQ',
                  years = [2023],months = [3,6],app=None,
                  resolution=None,
                  skip_candles=False,
                  base_on_existing=True):
    if resolution is None:
        resolution = '30 mins' if asset_name == 'ES' else '1 Hour'

    if not skip_candles:
        # check_call(set_real_mode_cmd, shell=True)
        app = wrap_hisorical_data_ibridge(years[0],months,asset_name,resolution=resolution,
                                          app=app)
        # check_call(set_paper_mode_cmd, shell=True)
    try:
        wrap_full_asset_retrieval([], months, asset=asset_name,include_xy=False,skip_bollinger=True,
                              resolution=resolution,
                              base_on_existing=base_on_existing)
    except Exception as e:
        print(f'Failed to run wrap_full_asset_retrieval: {e}')
        if DEBUG:
            raise
    return app



if __name__ == '__main__':
    # ADD argparser to choose the asset and the year and month
    import argparse
    parser = argparse.ArgumentParser(description='Retrieve data for a specific asset')
    parser.add_argument('--asset', type=str, default='all', help='Asset name')
    parser.add_argument('--year', type=int, default=None, help='Year')
    parser.add_argument('--month', type=int, default=None, help='Month')
    parser.add_argument('--skip_candles', type=bool, default=False, help='Month')


    args = parser.parse_args()
    skip_candles = args.skip_candles
    if args.asset == 'all':
        assets = ['NQ','MBT','GC']
    else:
        assets = [args.asset]
    app = None
    for asset in assets:

        ms = [1,2,3,4,5,6,7,8,9,10,11,12]
        # ms = [3]
        if asset in ['NQ','ES']:
            # ms = [3,6,9,12]
            ms = [((dtdt.now().month + i - 1) % 12) + 1 for i in range(0, 8)]
            ms = [m for m in ms if m % 3 == 0] #and (m - dtdt.now().month) % 12 < 6]
            # pass
        elif asset == 'MBT':
            ms = [(dtdt.now()+td(days=12)).month, dtdt.now().month]
            pass
        elif asset == 'GC':
            ms = [11,12] # list(range(1,13))
        # ms.reverse()
        for month in ms:
            for year in [2025] if args.year is None else [args.year]:
                if asset in ['NQ','MBT']:
                    if (month - dtdt.now().month)%12 > 3:
                        continue
                print(f'Running on YEAR = {year} and MONTH = {month}')
                months_list = [month]
                # months_list = ms # TODO
                try:
                    app = retrieve_date(asset_name=asset,years = [year],
                                        months =months_list
                                        ,app=app,
                                    resolution='15 mins' if asset in ['NQ','ES','MBT'] else None,
                                    skip_candles=skip_candles,base_on_existing=True,
                                    # skip_candles=True,base_on_existing=False, #resolution='1 hour'
                                    )
                except:
                    print(f'Failed to run on {asset} YEAR = {year} and MONTH = {month}')
                    if DEBUG:
                        raise
                    app = None


