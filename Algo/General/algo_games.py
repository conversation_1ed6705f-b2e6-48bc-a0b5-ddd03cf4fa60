import warnings
warnings.simplefilter(action='ignore')

from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import Extra<PERSON>reesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from sklearn.tree import DecisionTreeRegressor

import numpy as np
import pandas as pd
from datetime import datetime as dtdt
from datetime import timedelta as td
from matplotlib import pyplot as plt
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
import os
from Algo.Utils.files_handle import HOME

# elastic net conf
ALPHA = 0.3

T_test = 0
T_train = 0



def run_model_config_slide_build(df,target_col,reg_engine,learning_start,
                                 max_learning_lines,slide_window,normalize_y_tags,normalize_Xs):

    X, y = df[list(df)[2:]], df[target_col]  # load_boston(return_X_y=True)
    if normalize_Xs:
        X = X / X.std()
    df['y_tag'] = np.nan

    if reg_engine == 'DTR':
        regressor = DecisionTreeRegressor(random_state=0)
    elif reg_engine == 'db':
        regressor = Deepbit(complexbits=5,depth=4,tested_bits=20,bits_per_feature=7,iterations=20)
    elif reg_engine == 'elastic':
        regressor = ElasticNet(random_state=0, alpha=ALPHA)
    elif reg_engine == 'elastic2':
        regressor = ElasticNetCV(eps=0.25, random_state=0)
    elif reg_engine == 'Xtrees':
        regressor = ExtraTreesRegressor(n_estimators=100, max_depth=1, bootstrap=False)
    elif reg_engine == 'Bag':
        regressor = BaggingRegressor()
    elif reg_engine == 'randomF':
        regressor = RandomForestRegressor()
    else:
        raise AssertionError("")

    coefficients_dict = {}
    pointer = learning_start
    while pointer < df['time'].iloc[-1]:
        next_pointer = (pointer + td(days=slide_window))
        if pointer.month != next_pointer.month:
            next_pointer = next_pointer.replace(day=1)
        train_inds = (df['time'] < pointer) & (~df[target_col].isna()) & (X.isna().mean(axis=1) == 0)  # dtdt(2019,10,1)
        time_cond = (pointer <= df['time']) & (df['time'] < next_pointer)
        test_inds = (X.isna().mean(axis=1) == 0) & time_cond
        X_train, y_train = X[train_inds][-max_learning_lines:], y[train_inds][-max_learning_lines:]
        X_test, y_test = X[test_inds], y[test_inds]
        if X_train.shape[0] == 0:
            aa = 1
        if T_train:
            y_train = y_train.apply(lambda x: min(x, T_train) if x > 0 else max(-T_train, x))
        try:
            if reg_engine == 'db':
                X_train = X_train.as_matrix()
                y_train = y_train.as_matrix()
                X_test = X_test.as_matrix()
            regressor.fit(X_train, y_train)
            print ('Regressor Coefficients: %s'%str(regressor.coef_))
            coefficients_dict[pointer] = regressor.coef_[0]
            if X_test.shape[0]:
                ytag = regressor.predict(X_test)
                df['y_tag'][test_inds] = ytag
        except Exception as e:
            print("Failed with pointer = %s | Error: %s " % (pointer,e))

        pointer = next_pointer

    if T_test:
        inds = ~df[target_col].isna()
        df[target_col][inds] = df[target_col][inds].apply(
            lambda x: min(x, T_test) if x > 0 else max(-T_test, x))
    if normalize_y_tags:
        df['y_tag'] /= df['y_tag'].std()
    return df, coefficients_dict

def load_candles(open_hour = '0600',action_window=('0800','1645'),
                 candles_resolution=15,margins_around_minmax=0.15):
    """
    :param open_hour: 'hhmm'
    :param action_window:  ('hhmm','hhmm')
    :param candles_resolution:  in minutes
    :return:
    """

    open_h,open_m = int(action_window[0][:2]),int(action_window[0][2:])
    close_h, close_m = int(action_window[1][:2]), int(action_window[1][2:])
    window_start = dtdt(1990,6,28,open_h,open_m)
    window_end = dtdt(1990, 6, 28, close_h,close_m)
    num_of_rows_between = int(((window_end - window_start).total_seconds() / (60*candles_resolution)))
    candles_df = pd.read_csv(os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_Live.csv"),
                             parse_dates=['date','time_utc'])
    candles_df['open_price'] = np.nan
    candles_df.loc[(candles_df['time_utc'].dt.hour == int(open_hour[:2]))&(candles_df['time_utc'].dt.minute == int(open_hour[2:])),'open_price'] = \
        candles_df.loc[(candles_df['time_utc'].dt.hour == int(open_hour[:2])) & (candles_df['time_utc'].dt.minute == int(open_hour[2:])), 'open']
    candles_df['rolling_max_%s'%num_of_rows_between] = candles_df['high'].rolling(num_of_rows_between,num_of_rows_between).max().shift(-num_of_rows_between+1)
    candles_df['rolling_min_%s' % num_of_rows_between] = candles_df['low'].rolling(num_of_rows_between,num_of_rows_between).min().shift(-num_of_rows_between+1)
    #candles_df = candles_df[['time_utc', 'open', 'close', 'high', 'low', 'rolling_max_%s'%num_of_rows_between,
     #                   'rolling_min_%s'%num_of_rows_between,'open_price']]
    candles_df['open_price'] = candles_df['open_price'].fillna(method='ffill')
    candles_df['close_price'] = candles_df['close'].shift(-num_of_rows_between+1)
    candles_df['y_%s-%s'%(action_window[0],action_window[1])] = candles_df['close_price'] - candles_df['open']
    candles_df['y_%s-%s' % (open_hour, action_window[1])] = candles_df['close_price'] - candles_df['open_price']
    candles_df['y_%s-%s' % (open_hour, action_window[0])] = candles_df['open'] - candles_df['open_price']
    candles_df = candles_df[(candles_df['time_utc'].dt.hour==open_h)&(candles_df['time_utc'].dt.minute==open_m)]
    candles_df['is_open_price_achieved_in_window_naive'] = (candles_df['open_price']>=candles_df['rolling_min_%s' % num_of_rows_between])&\
                                                     (candles_df['open_price']<=candles_df['rolling_max_%s' % num_of_rows_between])
    candles_df['is_open_price_achieved_in_window'] = (candles_df['open_price'] + margins_around_minmax >= candles_df['rolling_min_%s' % num_of_rows_between])&\
                                                     (candles_df['open_price'] - margins_around_minmax <= candles_df['rolling_max_%s' % num_of_rows_between])
    candles_df['sell_punishment'] = candles_df['open_price'] - candles_df['rolling_max_%s' % num_of_rows_between]
    candles_df.loc[(candles_df['sell_punishment']<0)|(~candles_df['is_open_price_achieved_in_window']), 'sell_punishment'] = 0
    candles_df['buy_punishment'] = candles_df['rolling_min_%s' % num_of_rows_between] - candles_df['open_price']
    candles_df.loc[(candles_df['buy_punishment'] < 0)|(~candles_df['is_open_price_achieved_in_window']), 'buy_punishment'] = 0
    bb = 0
    candles_df['date'] = candles_df['time_utc']
    candles_df = candles_df[[x for x in list(candles_df) if 'time' not in x]]
    candles_df['is_open_price_achieved_in_window'] = candles_df['is_open_price_achieved_in_window'].fillna(False)
    candles_df[[x for x in list(candles_df) if 'y_' in x]+['buy_punishment','sell_punishment']] = candles_df[[x for x in list(candles_df) if 'y_' in x]+['buy_punishment','sell_punishment']]*1000
    return candles_df

def check_overbought_or_sold_days_by_eps(a,y_col,x_col='diff_0Z_0-13_last-Prev1_EPS',
                                         ref_y_col=None):
    a = a[a['date'] >= dtdt(2019, 7, 1)]
    coefficients_df = pd.read_csv(r"C:\tmp\coefficients.csv", parse_dates=['date'])
    coefficients_df['date'] += td(hours=8, days=0)
    a = a.merge(coefficients_df, on=['date'], how='outer')
    a['is_open_price_achieved_in_window'] = a['is_open_price_achieved_in_window'].fillna(False)
    a['coeff_eps_0z'] = a['coeff_eps_0z'].fillna(method='ffill')
    a = a.sort_values('date')
    a['expected_concated'] = a[x_col] * a['coeff_eps_0z']
    a['profit'] = np.sign(a[x_col] * a[y_col]) * abs(a[y_col])
    switch_to_0600 = True
    if switch_to_0600:
        assert ref_y_col is not None
        a[y_col+'_switched'] = a[y_col]
        a.loc[a['is_open_price_achieved_in_window'], y_col+'_switched'] = a.loc[a['is_open_price_achieved_in_window'], ref_y_col]
        a['profit_switched'] = np.sign(a[x_col] * a[y_col+'_switched']) * abs(a[y_col+'_switched'])
        a['profit_switched'] -= (a['sell_punishment'] +a['buy_punishment'])
        bb = 0
        #a.loc[a['is_open_price_achieved_in_window'],'profit_switched'] = a.loc[a['is_open_price_achieved_in_window'],'profit_switched']+ \
         #                                                                a.loc[a['is_open_price_achieved_in_window'], 'profit']
        print('ratio of cases achived open price: %s'%a.loc[(~a['y_0800-1100'].isna()),'is_open_price_achieved_in_window'].mean())
    a.loc[abs(a[x_col]) < abs(a[x_col]).quantile(0.4), ['profit','profit_switched']] = 0

    print('Profit =  %s'%a['profit'].sum())
    try:
        print('Profit Switched =  %s' % a['profit_switched'].sum())
    except:
        pass
    overbought_morning = (a[x_col] > 0) & (
            a['expected_concated'] * 5 < a['y_0600-0800'])
    oversold_morning = (a[x_col] < 0) & (
            a['expected_concated'] * 111 > a['y_0600-0800'])
    profit_col = 'profit'
    if switch_to_0600:
        oversold_morning = oversold_morning&(~a['is_open_price_achieved_in_window'])
        overbought_morning = overbought_morning&(~a['is_open_price_achieved_in_window'])
        profit_col = 'profit_switched'
    print('profit on overbought: %s' % a.loc[overbought_morning, profit_col].sum())
    print('profit on oversold: %s' % a.loc[oversold_morning, profit_col].sum())
    print('profit on overbought 21: %s' % a[a['date'] >= dtdt(2020, 11, 1)].loc[
        overbought_morning, 'profit'].sum())
    print('profit on oversold 21: %s' % a[a['date'] >= dtdt(2020, 11, 1)].loc[oversold_morning, profit_col].sum())
    print('PPT original: %s' % a[profit_col].replace(0, np.nan).mean())
    print('HR original: %s' % (a[[profit_col]].replace(0, np.nan).dropna()>0).mean()[profit_col])
    calmar,max_dd = calc_calmar(a[profit_col])
    sharpe = calc_sharpe(a[profit_col])
    print ('Calmar: %s | Sharpe: %s | MaxDD: %s'%(calmar,sharpe,max_dd))
    a.set_index('date')[profit_col].replace(0, np.nan).rolling(30, 10).mean().fillna(method='ffill').plot(title='PPT rolling30')
    print(abs(a[y_col]).sum())
    #a.loc[overbought_morning | oversold_morning, profit_col] = -a.loc[overbought_morning | oversold_morning, profit_col]
    a['%s_cumsum'%profit_col] = a[profit_col].fillna(0).cumsum()
    a['Value_0Z_0-0_PARA_fake'] = (a['Value_0Z_0-0_PARA'] * 150).rolling(5, 5).mean()
    a.set_index('date')[['%s_cumsum'%profit_col]].plot()
    plt.show()


f = os.path.join(HOME,"XYs","Enriched_XYs","XY_a_GDD_v8_12Z.csv")
a = pd.read_csv(f,parse_dates=['date'])
a = a.rename(columns={'date':'time'})
a = a.loc[:,~a.columns.duplicated()]

x_cols = ['ECGEM_AM_pred1','EC_AM_Summer_pred1',
     'GEPS_AM_basic_pred1','diff_0Z_0-16_last-Prev1_GEPS',
     'diff_0Z_0-13_Prev1-Prev2_EPS','diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC','diff_0Z_0-8_last-Prev3_EC',
     'diff_0Z_8-16_last-Prev1_GEPS','diff_0Z_8-16_last-Prev3_GEPS','EPS_pred1','EPS_pred2','EPS_pred3']

x_cols2 = ['ECGEM_AM_pred1','EC_AM_Summer_pred1',
     'GEPS_AM_basic_pred1','diff_0Z_0-16_last-Prev1_GEPS',
     'diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC',
     'diff_0Z_8-16_last-Prev1_GEPS','EPS_pred1','EPS_pred2','EPS_pred3']

x_cols3 = ['EPS_pred1','EPS_pred2','EPS_pred3','diff_0Z_0-8_last-Prev1_EC']
x_cols3 = ['diff_0Z_0-8_last-Prev1_EPS','diff_0Z_0-8_last-Prev1_EC','diff_0Z_8-16_last-Prev1_GEPS']
x_cols = ['diff_0Z_0-13_last-Prev1_EPS']

strats_to_concat = ['y_0600-0800','y_0800-1200','y_1200-1300',
                            'y_1300-1315',
                            'y_1330-1400','y_1400-1415',
                                'y_1515-1615','y_1615-1645',
                                'y_1715-1745'
                            ]


concated_strat = 'y_0800-1645'#'y_0800-2000_ConcatedHack'
ref_y_col = 'y_0600-1645'
a[concated_strat] = a[strats_to_concat[1:]].sum(axis=1)
a[ref_y_col] = a[strats_to_concat].sum(axis=1)

y_col = concated_strat
engine = 'elastic' #'elastic2' #randomF engine = 'Xtrees'

candles_df = load_candles()

a2 = a.copy()
a = a[['time',y_col]+x_cols]
a = a[abs(a[y_col]) > 0.001]
def run_coeff():
    df,coeff = run_model_config_slide_build(a,y_col,engine,dtdt(2019,7,1),50,3,False,False)
    coeffieicnts_series = pd.Series(coeff)
    coeffieicnts_series.plot()
    plt.show()
    pd.DataFrame(coeffieicnts_series).reset_index().rename(columns={'index':'date',0:'coeff_eps_0z'}).to_csv(r"C:\tmp\coefficients.csv",index=False)

## Now try to find on how many days from the top 40% we got close to the opening of 0600

a2 = a2.rename(columns={'time':'date'})
a2 = a2.merge(candles_df,on=['date'],suffixes=('','_candles'))
a2['is_open_price_achieved_in_window'] = a2['is_open_price_achieved_in_window'].fillna(False)
check_overbought_or_sold_days_by_eps(a2,y_col,x_col='diff_0Z_0-13_last-Prev1_EPS',ref_y_col=ref_y_col)
raise


df = df[(df['time']>=dtdt(2019,7,1))&(df['time']<=dtdt(2021,7,1))]
if 'y_0700-0800' not in list(df):
    df = df.merge(a2[['time','y_0700-0800']],on=['time'])
if 'diff_0Z_0-13_last-Prev1_EPS' not in list(df):
    df = df.merge(a2[['time','diff_0Z_0-13_last-Prev1_EPS']],on=['time'])
if 'diff_0Z_0-8_last-Prev1_EPS' not in list(df):
    df = df.merge(a2[['time', 'diff_0Z_0-8_last-Prev1_EPS']], on=['time'])


predictor = 'y_tag'
#predictor = 'EPS_pred3'
predictor = 'diff_0Z_0-8_last-Prev1_EC'


df['profit'] = abs(df['y_0700-0800']) * np.sign(df['y_0700-0800']*df[predictor])
print ('Corr: %s'%df.corr()[y_col].loc['y_tag'])

df['is_trade'] = abs(df[predictor])>abs(df[predictor]).quantile(0.6)
df.loc[~df['is_trade'],'profit'] = 0
df.set_index('time')['profit'].cumsum().plot()
plt.show()
aa = 1