from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Learning.algo_wrapper2 import wrap_analysis
from Algo.Learning.feature_engineering import add_deltaYs_columns,add_coffee_deltaYs
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
import os
from Algo.Utils.files_handle import HOME

COFFEE_CHOSEN_WEIGHTS = ['BR_ArabicaTrunc_weight']
WIND_SUFFIXES = ['v14_WindUS','v15_WindTX']
COFFEE_SUFFIXES = ['v12_BArabica','v12_BArabica12Z']

COND_FOR_ALLOWING_NANS = lambda x: 'GFSCO' in x or 'GEFSL' in x

coffee_candles = os.path.join(HOME,"COFFEE","Market_Data","COFFEE_2018-19_frontMonth_tz_Live_withBollinger_V3d10.csv")
XY_0800 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_0800-1100_15Mins_utc_DailyDiffs_Full_lastModel=12_daysAhead=[1-15]_Months=[1-12]_take9_prev24_PrevYs+Bollinger_V3d10.csv")

basic_analysis = False
hours_analysis = False

suffix_suffix = '_d=33'
suffix_suffix = ''

MODELS_WITH_6Z = ['PARA','PARACO','GEFS','CFS']
MODELS_WITH_12Z = MODELS_WITH_6Z #'PARA']

MONTHS = list(range(1,12))
LOCAL_TZ = pytz.timezone("Asia/Jerusalem")


preds_1745to1845 = [#'diff_0Z_0-16_last-Prev1_PARA', 'diff_0Z_0-16_last-Prev4_PARA', 'diff_0Z_0-16_last-Prev2_PARA',
                    'diff_0Z_0-16_last-Prev3_PARA', 'diff_0Z_0-16_last-Prev4_PARACO', 'diff_6Z_0-16_last-Prev1D_PARACO',
                    #'diff_0Z_0-16_last-Prev3_PARACO', 'diff_0Z_0-16_last-Prev1_PARACO', 'diff_6Z_0-16_last-Prev3D_PARA',
                    #'diff_6Z_0-16_last-Prev2_PARA',
                    'diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev4_GEFS',
                    'diff_6Z_0-16_last-Prev4_PARACO']
preds_1415to1545 = ['diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev1D_GFSCO']

preds_8to11 = ['diff_0Z_0-10_last-Prev4D_PARACO', 'diff_6Z_0-10_last-Prev4D_PARACO', 'diff_0Z_0-15_last-Prev3_PARACO', 'diff_0Z_0-16_last-Prev3_PARACO',
               'diff_6Z_0-16_last-Prev4_PARACO', 'diff_6Z_0-15_last-Prev4_PARACO', 'diff_6Z_0-13_last-Prev4_PARACO', 'diff_6Z_0-16_last-Prev1D_PARACO', 'diff_0Z_0-16_last-Prev1_PARACO']

preds_11to1945 = ['diff_0Z_0-16_last-Prev4_PARACO',
                #  'diff_0Z_14-16_last-Prev3_GEFSL','diff_0Z_0-2_last-Prev1_GEFSL','diff_0Z_0-2_last-Prev3_GEFSL',
                #  'diff_0Z_8-16_last-Prev4D_PARACO','diff_6Z_0-16_last-Prev1D_PARACO',
                    #'diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-16_last-Prev2_PARACO',
                    #'diff_6Z_0-16_last-Prev3_PARACO','diff_6Z_0-16_last-Prev4_PARACO',
                  #'diff_0Z_0-16_last-Prev2_PARACO',
                    'diff_0Z_0-2_last-Prev4_PARA','diff_0Z_0-2_last-Prev1_GFSCO',
                    #'diff_0Z_0-16_last-Prev2_PARACO',
                  ]

preds_0800to1415 = ['diff_0Z_0-10_Prev1-Prev2_PARACO',
                    'diff_0Z_0-10_last-Prev2_PARACO']

preds_1100to1315 = ['diff_6Z_0-10_last-Prev4_PARA','diff_6Z_0-10_last-Prev2_PARA',
                    'diff_0Z_0-10_Prev1D-Prev3D_PARA']
preds_1100to1415 = ['diff_0Z_8-16_last-Prev2_GEFS','diff_0Z_14-16_last-Prev2_GEFS',
                    'diff_0Z_0-10_Prev1D-Prev3D_PARA','diff_0Z_0-16_Prev1D-Prev3D_PARACO',
                    'diff_0Z_0-10_last-Prev3D_PARACO']
preds_1100to1745 = preds_1100to1415+['diff_0Z_0-15_Prev1-Prev2_PARA',
                                     'diff_0Z_14-16_last-Prev3_GEFS',
                                     'diff_0Z_8-16_last-Prev2D_GEFS',
                                     'diff_6Z_0-2_last-Prev4_GEFS']
preds_1315to1715 = ['diff_6Z_0-10_last-Prev1_PARACO','diff_0Z_0-10_last-Prev4_PARA',
                    'diff_6Z_0-10_last-Prev1_PARA']

preds_new_usWind_0800to1845_original = ['diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_6Z_0-8_last-Prev3_PARACO', 'diff_0Z_0-13_Prev1-Prev2_PARACO',
                                        #'diff_0Z_0-13_Prev1-Prev2_PARA',
                                        'diff_6Z_0-10_last-Prev3_PARACO', 'diff_0Z_0-0_last-Prev4_PARA', 'diff_0Z_0-8_Prev1-Prev2_PARACO', 'diff_0Z_0-0_last-Prev2_PARA',
                                        #'diff_6Z_0-8_last-Prev4_PARACO',
                                        'diff_0Z_0-0_last-Prev2_PARACO', #'diff_0Z_0-16_Prev1-Prev2_PARA',
                                        'diff_0Z_8-16_Prev1-Prev2_PARA',
                                        'diff_0Z_0-0_last-Prev3_PARA',
                                        #'diff_0Z_0-15_Prev1-Prev2_PARACO',
                                        'diff_0Z_0-10_last-Prev2_PARACO',
                                        #'diff_6Z_0-10_last-Prev1_PARA',
                                        'diff_0Z_0-16_Prev1-Prev2_PARACO', #'diff_0Z_0-10_Prev1-Prev3_PARACO',
                                        #'diff_0Z_0-8_last-Prev2_PARACO'
                                        ]
preds_new_usWind_0800to1845_original_v2 = ['diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_6Z_0-8_last-Prev3_PARACO', 'diff_0Z_0-13_Prev1-Prev2_PARACO',
                                        #'diff_0Z_0-13_Prev1-Prev2_PARA',

                                        'diff_0Z_0-0_last-Prev4_PARA', 'diff_0Z_0-8_Prev1-Prev2_PARACO',

                                        #'diff_6Z_0-8_last-Prev4_PARACO',
                                        #'diff_0Z_0-16_Prev1-Prev2_PARA',
                                        'diff_0Z_8-16_Prev1-Prev2_PARA',
                                        'diff_0Z_0-0_last-Prev3_PARA',
                                        #'diff_0Z_0-15_Prev1-Prev2_PARACO',
                                        'diff_0Z_0-10_last-Prev2_PARACO',
                                        #'diff_6Z_0-10_last-Prev1_PARA',
                                        'diff_0Z_0-16_Prev1-Prev2_PARACO', #'diff_0Z_0-10_Prev1-Prev3_PARACO',
                                        #'diff_0Z_0-8_last-Prev2_PARACO'

                                        # 'diff_0Z_0-0_last-Prev2_PARA',
                                        # 'diff_6Z_0-10_last-Prev3_PARACO',
                                        # 'diff_0Z_0-0_last-Prev2_PARACO',
                                        ]

preds_new_TXWind_0800to1100 = ['diff_0Z_14-16_last-Prev1_GEFS','diff_0Z_14-14_last-Prev1_GEFS',
                            'diff_0Z_0-10_last-Prev3_PARACO','diff_0Z_0-10_Prev1-Prev3_PARA']
preds_new_TXWind_1100to1745 = ['diff_6Z_11-15_last-Prev2_GEFS','diff_6Z_0-13_last-Prev2_GEFS',
                               'diff_6Z_0-0_last-Prev2_GEFS','diff_6Z_11-15_last-Prev4_GEFS',
                               'diff_0Z_8-16_last-Prev2_PARACO']
preds_new_usWind_1100to1745 = ['diff_6Z_0-16_last-Prev3_PARACO', 'diff_6Z_8-16_last-Prev3_PARACO',
                               'diff_6Z_0-13_last-Prev3_PARACO','diff_6Z_0-10_last-Prev3_PARACO','diff_6Z_11-15_Prev1-Prev2_GEFS',
                               'diff_6Z_0-16_last-Prev1D_GEFS', 'diff_6Z_14-16_last-Prev1_PARA','diff_6Z_0-8_last-Prev3_PARACO', 'diff_6Z_0-16_last-Prev2_PARACO',
                                    'diff_6Z_0-16_Prev1-Prev3_PARACO', 'diff_6Z_8-16_Prev1-Prev3_PARACO','diff_0Z_0-13_last-Prev2_PARACO',
                               'diff_0Z_8-16_last-Prev1D_GEFS', 'diff_0Z_14-16_last-Prev1_GEFS', 'diff_0Z_14-14_last-Prev1_GEFS', 'diff_0Z_0-16_last-Prev1D_GEFS', 'diff_0Z_8-16_last-Prev4D_GEFS', 'diff_0Z_8-16_last-Prev3D_GEFS']
                                #['diff_6Z_0-8_last-Prev3_PARACO', 'diff_6Z_0-10_last-Prev3_PARACO', 'diff_6Z_0-8_Prev1-Prev3_PARACO', 'diff_6Z_0-16_last-Prev3_PARACO', 'diff_6Z_0-13_last-Prev3_PARACO', 'diff_6Z_0-15_last-Prev3_PARACO', 'diff_6Z_0-10_Prev1-Prev3_PARACO', 'diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_0Z_0-0_last-Prev1_PARA', 'diff_0Z_0-10_last-Prev2_PARACO', 'diff_6Z_8-16_last-Prev1D_GEFS', 'diff_0Z_0-13_Prev1-Prev2_PARACO', 'diff_6Z_11-15_Prev1-Prev2_GEFS', 'diff_0Z_0-8_last-Prev2_PARACO', 'diff_6Z_0-15_Prev1-Prev3_PARACO']

preds_new_TXWind_1515to1845 = ['diff_0Z_0-0_last-Prev1_PARACO', 'diff_0Z_0-0_last-Prev4_GEFS', 'diff_0Z_0-0_last-Prev2_GEFS', 'diff_0Z_0-0_last-Prev1_GEFS', 'diff_0Z_0-0_last-Prev4_PARACO', 'diff_0Z_8-16_Prev1D-Prev2D_PARA', 'diff_0Z_0-0_last-Prev3_GEFS', 'diff_0Z_0-16_Prev1D-Prev2D_PARA', 'diff_6Z_0-8_last-Prev1_GEFS', 'diff_0Z_0-2_last-Prev4_PARACO']

preds_new_usWind_0800to1845_original_v3 = ['diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_0Z_0-13_Prev1-Prev2_PARACO',
                                        'diff_0Z_0-10_last-Prev2_PARACO',
                                        ]

preds_new_usWind_0800to1845 = [#'diff_0Z_0-0_last-Prev4_PARA', 'diff_0Z_0-0_last-Prev2_PARA', 'diff_0Z_0-0_last-Prev3_PARA',
                               #'diff_6Z_0-10_last-Prev1_PARA', 'diff_0Z_0-13_Prev1-Prev2_PARA', 'diff_0Z_0-16_Prev1-Prev2_PARA', 'diff_0Z_8-16_Prev1-Prev2_PARA',
                               #'diff_0Z_0-0_last-Prev2_PARACO',
                               'diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_6Z_0-10_last-Prev3_PARACO', 'diff_0Z_0-10_last-Prev2_PARACO', 'diff_0Z_0-10_Prev1-Prev3_PARACO', 'diff_0Z_0-13_Prev1-Prev2_PARACO',
                               'diff_0Z_0-16_Prev1-Prev2_PARACO', 'diff_6Z_0-8_last-Prev3_PARACO', 'diff_0Z_0-8_Prev1-Prev2_PARACO',
                                    'diff_6Z_0-8_last-Prev4_PARACO', 'diff_0Z_0-8_last-Prev2_PARACO'
                               ]
preds_new_usWind_0800to1845_fridays = ['diff_6Z_0-8_last-Prev1_PARA', 'diff_6Z_0-10_last-Prev1_PARA', 'diff_0Z_0-0_last-Prev2_PARA', 'diff_0Z_0-2_last-Prev4_PARA', 'diff_0Z_0-13_last-Prev4_GEFS', 'diff_6Z_0-8_last-Prev3_PARACO', 'diff_6Z_8-16_last-Prev1D_GEFS', 'diff_0Z_8-16_last-Prev4_GEFS', 'diff_0Z_0-0_last-Prev4_PARA', 'diff_0Z_8-16_last-Prev1D_GEFS', 'diff_0Z_0-8_last-Prev4_GEFS', 'diff_0Z_0-0_Prev1-Prev2_PARA', 'diff_0Z_0-16_last-Prev4_GEFS', 'diff_0Z_0-8_last-Prev2_PARACO', 'diff_0Z_0-10_last-Prev4D_GEFS', 'diff_6Z_0-2_last-Prev1_PARA', 'diff_6Z_8-16_last-Prev4_GEFS', 'diff_0Z_0-10_last-Prev3D_GEFS', 'diff_6Z_0-2_last-Prev3_PARA', 'diff_6Z_0-8_Prev1-Prev3_PARACO']

preds_new_usWind_0800to1845_days24 = ['diff_0Z_15-15_Prev1-Prev2_GEFS', 'diff_6Z_14-16_Prev1-Prev3_PARACO', 'diff_6Z_0-8_last-Prev3_PARACO', 'diff_0Z_0-8_last-Prev2_PARACO', 'diff_6Z_0-10_last-Prev1_PARA', 'diff_0Z_0-10_Prev1-Prev2_PARACO', 'diff_6Z_0-10_last-Prev3_PARACO', 'diff_0Z_0-8_Prev1-Prev2_PARACO', 'diff_6Z_0-10_last-Prev4_PARA', 'diff_0Z_0-13_Prev1-Prev2_PARA', 'diff_0Z_14-16_last-Prev4_GEFS', 'diff_0Z_0-0_last-Prev4_PARA', 'diff_6Z_0-8_Prev1-Prev3_PARACO', 'diff_0Z_14-16_last-Prev1_PARACO', 'diff_6Z_14-16_last-Prev4_PARA', 'diff_0Z_0-16_Prev1-Prev2_PARA', 'diff_0Z_0-15_Prev1-Prev2_PARA', 'diff_6Z_0-8_last-Prev4_PARA', 'diff_6Z_14-16_last-Prev4_PARACO', 'diff_0Z_0-0_last-Prev2_PARA', 'diff_6Z_0-10_last-Prev3_PARA', 'diff_6Z_0-8_last-Prev1_PARA', 'diff_0Z_0-13_Prev1-Prev2_PARACO', 'diff_0Z_15-15_last-Prev1_GEFS', 'diff_6Z_0-13_last-Prev3_PARACO']

preds_1715to1745 = ['diff_0Z_0-0_last-Prev4_PARA']

preds_1745to1845b = ['diff_6Z_0-10_last-Prev4_PARA','diff_6Z_0-10_last-Prev3_PARA',
                    'diff_6Z_0-10_last-Prev1_PARA','diff_6Z_0-10_last-Prev4_PARACO']

PRODUCTION_PREDS_BY_WEEKDAY = {0:preds_new_usWind_0800to1845_original_v2,
                        1:preds_new_usWind_0800to1845_original_v3,
                        2: preds_new_usWind_0800to1845_original_v2,
                        3: preds_new_usWind_0800to1845_original_v2,
                        4:  preds_new_usWind_0800to1845_original_v2}

total_fts = list(set(preds_new_usWind_0800to1845_original_v2+preds_new_usWind_0800to1845_original_v3+\
                        preds_new_TXWind_0800to1100+preds_new_TXWind_1515to1845+preds_new_TXWind_1100to1745))

def basic_analysis():
    coffee_df = pd.read_csv(coffee_candles, parse_dates=['date', 'time_utc', 'time_chicago'])

    d2 = coffee_df.groupby(coffee_df.time_utc.dt.date).sum().reset_index()
    daily_df_max = coffee_df.groupby(coffee_df.time_utc.dt.date).max()['open']
    daily_df_min = coffee_df.groupby(coffee_df.time_utc.dt.date).min()['open']
    daily_df_avg = coffee_df.groupby(coffee_df.time_utc.dt.date).mean()['open']
    daily_df_minmax = ((daily_df_max - daily_df_min) / daily_df_avg).reset_index()

    d2['time_utc'] = pd.to_datetime(d2['time_utc'])
    daily_df_minmax['time_utc'] = pd.to_datetime(daily_df_minmax['time_utc'])

    d2.groupby(d2.time_utc.dt.month).mean()['vol'].plot(kind='bar',title='Coffee Avg Daily Vol | By Month')
    (daily_df_minmax.groupby(daily_df_minmax.time_utc.dt.month).mean()['open']*100).plot(kind='bar',title='Coffee Average Daily Range as % of price | By month')

    d2.groupby(d2.time_utc.dt.to_period('M')).mean()['vol'].plot(kind='bar',title='Coffee Avg Daily Vol | By Month-Year')
    (daily_df_minmax.groupby(daily_df_minmax.time_utc.dt.to_period('M')).mean()['open']*100).plot(kind='bar',title='Coffee Average Daily Range as % of price | By Month-Year')
    #  #.rolling(10,10).mean()
    plt.show()
    aa = 1

def hours_analysis():
    coffee_df = pd.read_csv(coffee_candles, parse_dates=['date', 'time_utc', 'time_chicago'])

    coffee_df['hour'] = coffee_df.time_utc.dt.hour
    coffee_df['day'] =  pd.to_datetime(coffee_df.time_utc.dt.date)
    hourly_df = coffee_df.groupby(['day','hour']).sum().reset_index()

    #hourly_df.groupby(['hour']).mean()['vol'].plot(kind='bar',title='Avg Vol | By Hour')
    #plt.show()
    print ('1')
    coffee_df = coffee_df[['day','hour','open']]
    dayhourly_df_min = coffee_df.groupby(['day','hour']).min()['open']
    print('2')
    dayhourly_df_max = coffee_df.groupby(['day', 'hour']).max()['open']
    print('3')
    dayhourly_df_avg = coffee_df.groupby(['day', 'hour']).mean()['open']
    print('4')
    dayhourly_df_minmax = ((dayhourly_df_max - dayhourly_df_min)/dayhourly_df_avg).reset_index()
    (dayhourly_df_minmax.groupby(['hour']).mean()*100).plot(kind='bar',title='Avg Max move inside each Hour in %')
    plt.show()

def load_diffs(model,last_hour=0,ft='CGDD',weights=COFFEE_CHOSEN_WEIGHTS,
               suffix='v12_BArabica',start=dtdt(2018,1,1)):
    if last_hour == 12:
        bb = 0
    if suffix in WIND_SUFFIXES:
        eps45 = pd.read_csv(os.path.join(HOME,"Xdata","X_file_DailyDiffs_%s%s_%s%s.csv") % (model, '', suffix,suffix_suffix),
                            parse_dates=['forecast_time'])
    elif suffix in COFFEE_SUFFIXES:
        eps45 = pd.read_csv(os.path.join(HOME,"COFFEE","Xdata","X_file_DailyDiffs_%s%s_%s%s.csv") % (
        model, '', suffix, suffix_suffix),
                            parse_dates=['forecast_time'])
    else:
        raise AssertionError('Invalud suffix: %s'%suffix)

    eps45 = eps45[(eps45['feature_type'] == ft) & (eps45['weight'].isin(weights))]
    eps45 = eps45.drop_duplicates(subset=['forecast_time'], keep='last')
    eps45 = eps45[eps45['forecast_time']>=start]

    if eps45.shape[0] == 0:
        return pd.DataFrame()

    if last_hour != 12 and not (model == 'CFS' and last_hour == 6):
        eps45 = eps45[eps45.isna().mean(axis=1) < 0.3]

    eps45['time'] = eps45['forecast_time'] + td(days=1 if (model in ['EPS45', 'GEFS35'] or last_hour in [12,18]) else 0)
    eps45 = eps45[(eps45['forecast_time'].dt.hour == last_hour) & (eps45['forecast_time'] < dtdt.today())]

    # rename the columns
    l = list(eps45)
    eps45 = eps45.rename(columns={x: x.replace('diff', 'diff_%sZ' % last_hour) +
                                     '_%s' % (model) for x in l if 'diff' in x and 'diff8' not in x})
    eps45 = eps45.rename(columns={x: x.replace('Value', 'Value_%sZ' % last_hour) +
                                     '_%s' % (model) for x in l if 'Value' in x})

    eps45['date'] = eps45.time.dt.date
    if eps45.shape[0] != eps45.drop_duplicates(subset=['date', 'feature_type', 'weight']).shape[0]:
        raise AssertionError('eps45 file has several weight which will cause Error when merging')

    eps45 = eps45[[x for x in list(eps45) if x not in ['time', 'forecast_time']]]
    return eps45

def add_diffs_to_xy(xy_df,ft,models=['PARA', 'GEFS', 'PARACO','GEFSL','GFSCO'],always_add_suffix=False,suffix='v12_BArabica',
                    start=dtdt(2018,1,1)):
    if ft == 'ws10m':
        weights_lst = ['US_Wind','ERCOT_with_Neighbors_Wind']
    elif ft == 'HDD':
        weights_lst = ['population_US_weight']
    elif suffix in ['v12_BArabica12Z','v12_BArabica']:
        weights_lst = COFFEE_CHOSEN_WEIGHTS
    for model in models:
        if model == 'GEFS':
            bb = 0
        eps45 = load_diffs(model, last_hour=0, ft=ft,suffix=suffix,weights=weights_lst,
                           start=start)
        if model in MODELS_WITH_6Z and eps45.shape[0] > 0:
            eps45 = eps45.merge(load_diffs(model, last_hour=6, ft=ft,suffix=suffix,weights=weights_lst,
                                           start=start),
                   on=['date', 'feature_type'],how='outer')
        if model in MODELS_WITH_12Z and '12Z' in suffix:
            eps45 = eps45.merge(load_diffs(model, last_hour=12, ft=ft,suffix=suffix,weights=weights_lst,start=start),
                                on=['date', 'feature_type'],how='outer')
        #eps45 = eps45[eps45.isna().sum(axis=1)<10]
        common = ['date'] #[x for x in list(eps45) if x in list(xy_df)]

        if eps45.shape[0] >0:
            if always_add_suffix:
                eps45 = eps45.rename(columns={x: x+'_%s'%ft for x in list(eps45) if model in x})
            xy_df = xy_df.merge(eps45,on=common,how='outer',suffixes=('','_%s'%ft)) #'inner' if model in ['PARA'] else 'outer')
    return xy_df

def modify_preds(xy_df,pred,ft):
    if ft in ['CGDD','PCDD','ws10m']:
        #xy_df[pred] *= xy_df['date'].apply(lambda x: (-1 if (x.month in [4, 5, 6, 7, 8, 9] or (x.month == 3 and x.day > 10) or (x.month == 10 and x.day < 20)) else 1))
        xy_df[pred] *= -1
    elif ft in ['PCDD','CGDD']:
        xy_df[pred] *= xy_df['date'].apply(lambda x: (-1 if (x.month in [4,5,6,7,8,9] or (x.month ==3 and x.day > 10) or (x.month ==10 and x.day < 20)) else 1))
        pass
    return xy_df


def plot_pnl(a,preds,y_col,thresh=0.6):
    for p in preds:
        if 'CGDD' in p:
            a = modify_preds(a,p,'CGDD')
        elif 'PCDD' in p:
            a = modify_preds(a, p, 'PCDD')
        a['profit_%s_%s'%(y_col,p)] = np.sign(a[y_col]*a[p])*abs(a[y_col]) #* 37.5
        a.loc[abs(a[p])<abs(a[p]).quantile(1-thresh) ,'profit_%s_%s' % (y_col, p)] = 0
    a = a.sort_values('date')
    a.set_index('date')[[x for x in list(a) if 'profit' in x]].fillna(0).cumsum().plot(style=['-']*5+['--']*(max(0,(len(preds)-5))))
    # a[['date']+preds_1415to1745[:1]+['profit_%s_%s'%(y_col,preds_1415to1745[0])]].sum()
    plt.show()
    return a


def plot_pnl_lexi(a,preds,y_col,thresh=0.6,add_mean=False,mean_ind=-1,is_prod=True,
                  ignore_contradictions=True,ft='ws10m',multiplier=1,
                  is_modify_preds=True,weekdays=[0,1,2,3,4]):
    now_naive = dtdt.now()
    local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
    utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

    older_trades = a['date'].apply(lambda x: False)

    if is_modify_preds:
        if ft != '':
            for p in preds:
                a = modify_preds(a, p, ft)
        else:
            for p in preds:
                tmp_ft = p.split('_')[-1]
                a = modify_preds(a, p, tmp_ft)

    a['MEAN'] = (a[preds] / a[preds].std()).mean(axis=1)
    a['profit_Combined'] = 0
    i = 0
    new_preds = preds+['MEAN']
    #a[['date', p]]
    if mean_ind != -1:
        new_preds = preds[:mean_ind]+['MEAN']+preds[mean_ind:]

    if isinstance(thresh,float) or isinstance(thresh,int):
        thresh_lst = [thresh]* len(new_preds)
    else:
        thresh_lst = thresh
    for p,thresh in zip(new_preds,thresh_lst):
        i+=1
        a['profit_%s_%s'%(y_col,p)] = (np.sign(a[y_col]*a[p])*abs(a[y_col])).fillna(0)
        a['profit_%s_%s'%(y_col,p)] *= multiplier
        a['action_%s_%s'%(y_col,p)] = 'I'
        current_ignore = (abs(a[p]) < abs(a[p]).quantile(1 - thresh))

        if i == 1:
            bb = 0
        a['ignore_tmp'] = current_ignore
        a['old_trades'] = older_trades
        test = a[['date','ignore_tmp','old_trades']+[x for x in list(a) if 'profit' in x]]
        a.loc[current_ignore,'profit_%s_%s' % (y_col, p)] = 0
        a['tmp'] = a['profit_%s_%s'%(y_col,p)]
        a.loc[older_trades ,'tmp'] = 0

        print('Pred: %s | final ignore ratio: %s | Added profit = %s' % (p,older_trades.mean(),a['tmp'].sum()))

        if p != 'MEAN' or add_mean:
            a['profit_Combined'] = a['profit_Combined']+a['tmp'].fillna(0)
        a.loc[(older_trades | (~current_ignore))&(~a[p].isna()),'action_%s_%s'%(y_col,p)] = a.loc[(older_trades | (~current_ignore)),p].apply(lambda x: 'B' if x>0 else 'S')
        older_trades = older_trades | (~current_ignore)

    a = a.sort_values('date')
    a = a[a['date'].dt.weekday.isin(weekdays)]
    actions = a[[x for x in list(a) if 'action' in x]].iloc[-1]
    final_action = 'I'
    if is_prod:
        nans_sum = a[[p for p in preds if not COND_FOR_ALLOWING_NANS(p)]].iloc[-1].isna().sum()
        if nans_sum>0:
            first_nan = a[[p for p in preds if not COND_FOR_ALLOWING_NANS(p)]].iloc[-1].isna()[a[[p for p in preds if not COND_FOR_ALLOWING_NANS(p)]].iloc[-1].isna()].index.tolist()[0]
            first_nan_index = preds.index(first_nan)
            cond_previous_action = a[['action_%s_%s'%(y_col,x) for x in preds[:first_nan_index]+['MEAN']]].iloc[-1].sum() != ('I'*(first_nan_index+1))
            if not (nans_sum == 1 and '6Z' in first_nan and cond_previous_action):
                print ('WARNING... some of the predictors for %s are NaNs'%a.iloc[-1]['date'])
                print(str(a[preds].iloc[-1].isna()))
                return a,'I'
        if a['date'].iloc[-1]<(utc_now_dt.replace(hour=0,minute=0)-td(minutes=1)):
            print('WARNING... prediction is old (%s < %s). Not using it' % (a.iloc[-1]['date'],
                                                                            (utc_now_dt.replace(hour=0)-td(minutes=1))))
            return a, 'I'
    for x in [x for x in list(a) if 'action' in x]:
        if 'diff' in x:
            pred = 'diff'+x.split('diff')[-1]
        elif 'MEAN' in x:
            pred = 'MEAN'
        elif 'Value' in x:
            pred = 'Value'+x.split('Value')[-1]

        s = '+' if a.iloc[-1][pred] > 0 else '-'
        print ('Date: %s | Action of %s = %s(%s)'%(a['date'].iloc[-1],x,a[x].iloc[-1],s))
        if final_action == 'I':
            final_action = a[x].iloc[-1]

    if 'diff_0Z_0-16_last-Prev4_PARACO' in list(a):
        naive_paraco_final_action = 'B' if a.iloc[-1]['diff_0Z_0-16_last-Prev4_PARACO'] > 0 else 'S'
        naive_mean_final_action = 'B' if a.iloc[-1]['MEAN'] > 0 else 'S'
        if (naive_paraco_final_action != final_action) and (naive_mean_final_action!=final_action) and ignore_contradictions:
            print('PARACO Naive is opposite to final action... We ignore')
            final_action = 'I'
    print ('Final Action: %s'%(final_action))
    if not is_prod:
        a['Naive_PARACO_action'] = a['diff_0Z_0-16_last-Prev4_PARACO'].apply(lambda x: 'S' if x < 0 else 'B')
        a['Final_action_is_buy'] = ((a['profit_Combined']>0)&(a[y_col]>0))|((a['profit_Combined']<0)&(a[y_col]<0))
        a['Final_action_is_sell'] = ((a['profit_Combined'] > 0) & (a[y_col] < 0)) | ((a['profit_Combined'] < 0) & (a[y_col] > 0))
        a['Final_action'] = 'I'
        a.loc[a['Final_action_is_buy'],'Final_action'] = 'B'
        a.loc[a['Final_action_is_sell'], 'Final_action'] = 'S'
        #a = a[a['Naive_PARACO_action']!=a['Final_action']]
        """
        a = a[(a['action_y_1100-1945_finalHack_diff_0Z_0-16_last-Prev4_PARACO'] != a['action_y_1100-1945_finalHack_MEAN'])&
              (a['action_y_1100-1945_finalHack_diff_0Z_0-16_last-Prev4_PARACO']!= 'I')&
                (a['action_y_1100-1945_finalHack_MEAN']!= 'I')]
        """
        a.set_index('date')[[x for x in list(a) if 'profit' in x]].fillna(0).cumsum().plot(title='Multi Preds + Comb',
                                                                       style=['-']*(4+2)+['--']*(max(0,(len(preds)-4-2))))
        a.groupby(a['date'].dt.weekday).mean()[[x for x in list(a) if 'profit' in x]].plot(title='Profit by weekday',kind='bar')
        # a[['date']+preds_1415to1745[:1]+['profit_%s_%s'%(y_col,preds_1415to1745[0])]].sum()

        plt.show()
    actual_trades = a['profit_Combined'] != 0
    calmar, max_dd = calc_calmar(a['profit_Combined'])
    sharpe = calc_sharpe(a['profit_Combined'])
    total_pnl, total_ppt = a['profit_Combined'].sum(), a[actual_trades]['profit_Combined'].mean()
    hr = (a['profit_Combined'][actual_trades] > 0).mean()
    print('Combined Strategy | PPT = %s | HR = %s | PNL = %s | calmar %s | Sharpe = %s | MaxDD %s' % (
    total_ppt, hr, total_pnl, calmar, sharpe, max_dd))
    a = a.rename(columns={'profit_Combined':'profit_Combined_%s'%y_col})
    return a,final_action


#cofee_degdays = pd.read_csv(os.path.join(HOME,"COFFEE","degdays_archive","Live","Live_from_ec2","live_degdays_PARA.csv"),
 #                           parse_dates=['forecast_time','validation_day'])


def prepare_xy(csv=XY_0800,fts = ['ws10m'], suffix = 'v14_WindUS',
               always_add_suffix=False,start=dtdt(2018,7,1)):
    xy_df = pd.read_csv(csv,parse_dates=['time_utc'])
    xy_df = xy_df[xy_df['time_utc']>=start]
    xy_df['date'] = xy_df['time_utc'].dt.date

    for ft in fts:
        xy_df = add_diffs_to_xy(xy_df,ft,always_add_suffix=always_add_suffix,suffix=suffix,start=start)

    xy_df['date'] = pd.to_datetime(xy_df['date'])
    xy_df = xy_df.sort_values('date')

    ### ADD Delta Ys

    if suffix in WIND_SUFFIXES:
        xy_df = add_deltaYs_columns(xy_df)
        xy_df['y_1415-1945_no1545to1615'] = xy_df['y_1415-1545'] + xy_df['y_1615-1945']
        xy_df['y_1200-1945_no1545to1615'] = xy_df['y_1200-1415'] + xy_df['y_1415-1545'] + xy_df['y_1615-1945']
        xy_df['y_1100-1945_no1545to1615'] = xy_df['y_1100-1200'] + xy_df['y_1200-1415'] + xy_df['y_1415-1545'] + xy_df[
            'y_1615-1945']
        xy_df['y_0800-1945_no1545to1615'] = xy_df['y_0800-1100'] + xy_df['y_1100-1200'] + xy_df['y_1200-1415'] + xy_df[
            'y_1415-1545'] + xy_df['y_1615-1945']
        xy_df['y_1100-1945_finalHack'] = xy_df[
            ['y_1100-1430', 'y_1500-1515', 'y_1515-1545', 'y_1615-1745', 'y_1815-1945']].fillna(0).sum(axis=1)
        xy_df['y_1400-1915_hackMar21'] = xy_df[['y_1400-1430', 'y_1615-1715', 'y_1745-1915']].fillna(0).sum(axis=1)
        xy_df = xy_df.sort_values(['date'])
        try:
            xy_df['diff_6Z_0-16_last-Prev123_PARA_%s' % ft] = xy_df['diff_6Z_0-16_last-Prev1_PARA_%s' % ft] + \
                                                              0.5 * xy_df['diff_6Z_0-16_last-Prev2_PARA_%s' % ft] + \
                                                              0.3 * xy_df['diff_6Z_0-16_last-Prev3_PARA_%s' % ft]
            xy_df['diff_0Z_0-16_last-Prev14D_PARA_%s' % ft] = xy_df['diff_0Z_0-16_last-Prev1D_PARA_%s' % ft] + xy_df[
                'diff_0Z_0-16_last-Prev4D_PARA_%s' % ft]
        except:
            pass

        ys_for_concat = ['y_0800-1100','y_1130-1200','y_1200-1430','y_1715-1845','y_1845-1900',
                         'y_1915-1945']
        ### production strats
        common_wind_trades = [] #'y_1400-1430']
        wind_trades_by_day = {'all': common_wind_trades,
                              0: ['y_1100-1300', 'y_1300-1330', 'y_1415-1845', 'y_1100-1245'],
                              1: ['y_1100-1415', 'y_1415-1845', 'y_1100-1245', 'y_1415-1515'],
                              2: ['y_1100-1315'],  # 'y_0800-1415' too? 16.08
                              3: ['y_0800-1845', 'y_1300-1430', 'y_1445-1515'],
                              4: ['y_0800-1100', 'y_1330-1400','y_1400-1445']
                              }  # 'y_1615-1715'
        xy_df['y_0800-2000_tmphack'] = xy_df[ys_for_concat].fillna(0).sum(axis=1)
        xy_df['y_0800-2000_production'] = 0
        for weekday in [0,1,2,3,4]:
            xy_df.loc[xy_df['date'].dt.weekday == weekday,'y_0800-2000_production'] = xy_df[common_wind_trades+wind_trades_by_day[weekday]].sum(axis=1)
        xy_df['y_0800-1200'] = xy_df['y_0800-1100']+xy_df['y_1100-1200']
        xy_df['y_1815-1945'] = xy_df['y_1815-1845']+xy_df['y_1845-1945']
    elif suffix in COFFEE_SUFFIXES:
        xy_df = add_coffee_deltaYs(xy_df)
        xy_df = enrich_coffee_df(xy_df)
    else:
        raise AssertionError('Invalud suffix')

    #### used to be here delta ys

    return xy_df

def enrich_coffee_df(xy_df,fts=['PCDD']):
    try:
        for ft in fts:
            xy_df['diff_6Z_0-16_last-Prev123_PARA_%s' % ft] = xy_df['diff_6Z_0-16_last-Prev1_PARA_%s' % ft] + \
                                                              0.5 * xy_df['diff_6Z_0-16_last-Prev2_PARA_%s' % ft] + \
                                                              0.3 * xy_df['diff_6Z_0-16_last-Prev3_PARA_%s' % ft]
            xy_df['diff_0Z_0-16_last-Prev14D_PARA_%s' % ft] = xy_df['diff_0Z_0-16_last-Prev1D_PARA_%s' % ft] + xy_df[
                'diff_0Z_0-16_last-Prev4D_PARA_%s' % ft]
        xy_df['Value_0Z_0-2_PARA_ma20_PCDD'] = xy_df['Value_0Z_0-2_PARA_PCDD'].rolling(20, 10, center=False).mean()
        xy_df['Value_0Z_0-2_PARA_ma30_PCDD'] = xy_df['Value_0Z_0-2_PARA_PCDD'].rolling(30, 10, center=False).mean()
        xy_df['Value_0Z_0-2_PARA_ma90_PCDD'] = xy_df['Value_0Z_0-2_PARA_PCDD'].rolling(90, 10, center=False).mean()
        # xy_df[['date','Value_0Z_0-2_PARA_ma20_PCDD','Value_0Z_0-2_PARA_ma90_PCDD','Value_0Z_0-16_PARA_PCDD']].sort_values('date').set_index('date').plot()
    except:
        pass
    finally:
        return xy_df


def get_wind_action(threshold=0.45,wind_suffix='v15_WindTX',day=0):

    preds = PRODUCTION_PREDS_BY_WEEKDAY[day]
    xy_df = prepare_xy(XY_0800,['ws10m'],wind_suffix)
    a,final_action = plot_pnl_lexi(xy_df[(xy_df['date']>=dtdt(2020,9,15))&(xy_df['date'].dt.weekday.isin([0,1,2,3,4]))],
                                   preds,
                 'y_1100-1945_finalHack', #'y_1100-1945_no1545to1615', #'y_1415-1545',
                  threshold, add_mean=True,mean_ind=1)
    return a,final_action


def compare_strats(xy_df,y_cols,weekdays=[0,1,2,3,4],start=dtdt(2020, 9, 15),
                   preds=preds_11to1945,ft='ws10m',threshold=0.55):
    a = xy_df[(xy_df['date'] >= start)]
    is_first = True
    for y_col in y_cols:
        a, final_action = plot_pnl_lexi(a,preds,y_col,
                                        threshold, add_mean=True, mean_ind=1, is_prod=True, ft=ft,
                                        is_modify_preds=is_first,weekdays=weekdays)
        a = a.rename(columns={'profit_Combined':'profit_Combined_%s'%y_col})
        is_first = False
    profit_combined_cols = [x for x in list(a) if 'profit_Combined' in x]
    a.set_index('date')[profit_combined_cols].cumsum().plot(style=['-']*int(len(profit_combined_cols)/2)+
                                                            ['--']*(len(profit_combined_cols)-int(len(profit_combined_cols)/2)))
    plt.show()

if __name__ == '__main__':
    ### CORRELATION PLAYS
    #correlations = xy_df[(pd.to_datetime(xy_df['date'])>=dtdt(2021,4,1))&(xy_df['date'].dt.weekday == 1)].corr()[sorted([x for x in list(xy_df) if x.startswith('diff_') and '0-' in x and '' in x and ('PARA' in x or 'GEFS' in x or 'GFS' in x)])].loc[sorted([x for x in list(xy_df) if x.startswith('y_')])].loc['y_1100-1745'].sort_values().index.tolist()[:12]
    #correlations.loc[['y_0900-1100','y_1100-1200','y_1145-1415','y_1415-1745','y_1615-1745','y_0900-1745']]
    #xy_df[pd.to_datetime(xy_df['date'])>=dtdt(2020,5,1)].corr()[['y_0900-1745','y_0900-1100','y_0900-1415','y_1130-1415','y_1130-1315','y_1415-1745','y_1645-1745']].loc[[x for x in list(xy_df) if 'DD' in x and ('0-13' in x or '0-16' in x) and 'diff' in x[:10]]].sort_values(['y_0900-1745'])
    # xy_df[xy_df['date']>=dtdt(2021,1,1)].corr()['y_1100-1945_finalHack'].loc[[x for x in list(xy_df) if 'diff' in x]].sort_values()

    ft = 'ws10m' #'ws10m'
    # xy_df = prepare_xy(XY_0800, [ft],'v14_WindUS')
    xy_df = prepare_xy(XY_0800, [ft],'v15_WindTX')
    xy_df['Value_0Z_0-0_PARA_vs_ma30'] = xy_df['Value_0Z_0-0_PARA'] - xy_df['Value_0Z_0-0_PARA'].rolling(30,30).mean()
    xy_df['Value_0Z_0-0_PARA_vs_ma60'] = xy_df['Value_0Z_0-0_PARA'] - xy_df['Value_0Z_0-0_PARA'].rolling(60,30).mean()
    xy_df['Value_0Z_last3d_vs_ma30'] = xy_df['Value_0Z_0-0_PARA'].rolling(3,3).mean() - xy_df['Value_0Z_0-0_PARA'].rolling(30,30).mean()
    xy_df['Value_0Z_last7d_vs_ma30'] = xy_df['Value_0Z_0-0_PARA'].rolling(7,7).mean() - xy_df['Value_0Z_0-0_PARA'].rolling(30,30).mean()
    xy_df['Value_0Z_yest_vs_ma30'] = xy_df['Value_0Z_0-0_PARA'].shift(1) - xy_df['Value_0Z_0-0_PARA'].rolling(30,30).mean()

    cols = ['Value_0Z_0-0_PARA_vs_ma30','Value_0Z_0-0_PARA_vs_ma60','Value_0Z_last3d_vs_ma30','Value_0Z_last7d_vs_ma30','Value_0Z_yest_vs_ma30']
    cols = ['diff_6Z_0-16_Prev1D-Prev3D_PARACO', 'diff_6Z_0-16_last-Prev2_GEFS', 'diff_6Z_0-13_Prev1-Prev3_PARACO', 'diff_6Z_0-16_Prev1-Prev3_PARACO', 'diff_6Z_0-13_last-Prev2_GEFS', 'diff_6Z_0-15_Prev1-Prev3_PARACO', 'diff_6Z_0-16_last-Prev3D_PARACO', 'diff_0Z_0-13_last-Prev2_PARACO', 'diff_0Z_0-0_Prev1-Prev3_GEFSL', 'diff_0Z_0-15_last-Prev2_PARACO', 'diff_6Z_0-8_last-Prev1_GEFS', 'diff_6Z_0-10_Prev1-Prev3_PARACO']
    para_0z_preds = ['diff_0Z_0-8_last-Prev1_PARA','diff_0Z_0-8_last-Prev2_PARA',
                     'diff_0Z_0-8_last-Prev3_PARA','diff_0Z_0-8_last-Prev4_PARA',
                     'diff_0Z_0-16_last-Prev1_PARA','diff_0Z_0-16_last-Prev2_PARA',
                     'diff_0Z_0-16_last-Prev3_PARA','diff_0Z_0-16_last-Prev4_PARA'][1:4]
    para_6z_preds = ['diff_6Z_0-8_last-Prev1_PARA', 'diff_6Z_0-8_last-Prev2_PARA',
                     'diff_6Z_0-8_last-Prev3_PARA', 'diff_6Z_0-8_last-Prev4_PARA',
                     'diff_6Z_0-16_last-Prev1_PARA', 'diff_6Z_0-16_last-Prev2_PARA',
                     'diff_6Z_0-16_last-Prev3_PARA', 'diff_6Z_0-16_last-Prev4_PARA']
    weekdays_for_corr = [0,1,2,3,4]#[2,4]#[0,1,2,3,4]
    # c = xy_df[(xy_df['date']>=dtdt(2021,6,1))&(xy_df['date'].dt.weekday.isin(weekdays_for_corr))][[x for x in list(xy_df) if 'diff_' in x
    #                                                                                      or x.startswith('y_')]].corr()
    # c[[x for x in list(c) if x in ['y_0000-0600', 'y_0600-0800', 'y_0800-1100', 'y_1100-1315', 'y_1200-1315', 'y_1315-1515', 'y_1515-1845',
    #          'y_1415-1745', 'y_1200-1745','y_1100-1745', 'y_0800-1745', 'y_0800-1845']]].loc[[x for x in list(xy_df) if
    #             x.startswith('diff_') and sum([s in x for s in ['GEFS', 'PARA']]) and sum([s in x for s in ['GEFSL','GFSCO']]) == 0]].sort_values('y_0800-1100')
    strats_to_compare = [#'y_0800-1100','y_1100-1200','y_1200-1300','y_1200-1230',
                         #'y_1300-1330',
                         # 'y_1300-1400','y_1400-1430','y_1430-1500',
                         'y_0800-1100',
                         'y_1100-1130','y_1130-1200','y_1200-1230','y_1715-1730','y_1900-1915','y_1915-1945',
                            'y_1845-1900',

                         #'y_1515-1545','y_1545-1615','y_1615-1645','y_1645-1715',
                         # 'y_1715-1745','y_1745-1815','y_1815-1845','y_1845-1915',
                          #'y_1915-1945',#'y_1100-1415','y_1415-1945',
                          'y_0800-2000_tmphack'
                            ]
    common_wind_trades = ['y_1400-1430']
    wind_trades_by_day = {'all': common_wind_trades,
                          0: ['y_1430-1500', 'y_1800-1900', 'y_1615-1745'],
                          1: ['y_1845-1915', 'y_1230-1300'],
                          2: ['y_1100-1230', 'y_1300-1330', 'y_1515-1745', 'y_1845-1915'],
                          3: ['y_0800-1200', 'y_1200-1400', 'y_1545-1645', 'y_1815-1945'],
                          4: ['y_0800-1100', 'y_1130-1400', 'y_1845-1900',
                              'y_1915-1945','y_1300-1330','y_1300-1430'
                              #'y_1715-1845',
                              ]}  # 'y_1615-1715'

    # mondays ['y_1100-1300','y_1300-1330','y_1415-1845']
    # tuesdays ['y_1100-1845']
    # [2] x ['y_0800-1415'] and especially ['y_1100-1315']
    # [3] x ['y_0800-1845']
    # [4] x ['y_0800-1100','y_1330-1415','y_1415-1445']
    strats_to_concat = ['y_0800-1845','y_1300-1430','y_1445-1515']
    xy_df['y_0800-2000_ConcatedHack'] = xy_df[strats_to_concat].sum(axis=1)
    strats_to_compare = common_wind_trades+wind_trades_by_day[4]
    strats_to_compare = ['y_0800-1100',
                         'y_1100-1300','y_1200-1315','y_1300-1330',
                         'y_1300-1430','y_1330-1430','y_1545-1745','y_1615-1845',
                         'y_0800-1845',
                         'y_0800-2000_ConcatedHack']
    # compare_strats(xy_df,strats_to_compare,weekdays=[0],threshold=0.4,
    #                preds=preds_new_usWind_0800to1845_original_v2)

    weekdays = [0,1,2,3,4]
    # weekdays = [1,2,3] #[0,1,3]

    # para_0z_preds x [0,1,2,3,4]x 0600-0900
    #    also [2] x 1315-1515
    #    also [0,4] x 1745-1945

    a, final_action = plot_pnl_lexi(xy_df[(xy_df['date'] >= dtdt(2020, 7, 15)) & (xy_df['date'].dt.weekday.isin([0,1,2,3,4,5,6]))],
                                    #preds_new_usWind_0800to1845_days24,
                                    # preds_new_usWind_0800to1845_original_v2, #preds_11to1945,
                                    para_6z_preds,#preds_new_usWind_0800to1100,
                                    'y_1745-1945',#'y_0800-2000_production',
        0.4,#[0.45]*6+[0.6]+[0.35]*6,
                                    add_mean=True, mean_ind=1,is_prod=False,ft=ft,weekdays=weekdays)
    diff_cols = [x for x in list(xy_df) if x.startswith('diff_')]
    val_cols = [x for x in list(xy_df) if x.startswith('Value_')]
    xy_df[diff_cols] = xy_df[diff_cols].fillna(0)
    xy_df[val_cols] = xy_df[val_cols].fillna(method='ffill')



    bb = 0
    #xy_df = xy_df[~xy_df[pred].isna()]

    #c = xy_df[(xy_df['date'].dt.weekday.isin([0,1,2,3,4]))&
     #         (xy_df['date'].dt.month.isin([10,11,12]))].corr()[sorted([x for x in list(xy_df) if x.startswith('y_')])]
    # c = xy_df[(xy_df['date'].dt.weekday.isin([0, 1, 2, 3, 4]))].corr()['y_1100-1415'].loc[
    #     sorted([x for x in list(xy_df) if x.startswith('diff_')])].sort_values()
    xy_df = xy_df[xy_df['date'].dt.month.isin(MONTHS)]
    small_df = wrap_analysis(xy_df,pred,y_col,weekdays=[0,1,2,3,4],
                  start=dtdt(2018,4,1),end=dtdt(2021,10,1),chosen_ratio=0.5)
    aa = 1
    # Why am I missing 201904-07


