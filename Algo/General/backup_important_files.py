import os
import sys
import pickle
import pandas as pd
import numpy as np
from datetime import datetime as dtdt
import argparse

from Algo.Utils.files_handle import get_daily_predictions_path

def backup_daily_predictions(suffix='_All'):
    path = get_daily_predictions_path(suffix)
    df = pd.read_csv(path,parse_dates=['date'])
    df['date'] = pd.to_datetime(df['date'],errors='coerce')
    current_year = dtdt.now().year
    df = df[df.date.dt.year == current_year]
    outpath = path.replace('.csv','_%s.csv'%current_year)
    if os.path.exists(outpath):
        existing_df = pd.read_csv(outpath,parse_dates=['date'])
    else:
        existing_df = pd.DataFrame()
    if existing_df.shape[0] > 0:
        df = existing_df.merge(df,on=list(df),how='outer')
        df = df.sort_values('date')
    df = df.drop_duplicates()
    df.to_csv(outpath,index=False)

# backup_daily_predictions(suffix='_All')
# raise

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--specific_function', type=str, default=None)
    args = parser.parse_args()

    # check if specific function is requested
    if args.specific_function is not None:
        eval(args.specific_function + '()')

    # backup_daily_predictions()