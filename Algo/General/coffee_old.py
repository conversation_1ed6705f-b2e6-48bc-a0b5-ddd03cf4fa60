from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Learning.algo_wrapper2 import wrap_analysis
import os
from Algo.Utils.files_handle import HOME

coffee_candles = os.path.join(HOME,"COFFEE","Market_Data","COFFEE_2018-19_frontMonth_tz_Live_withBollinger_V3d10.csv")
xy_csv_1130 = os.path.join(HOME,"COFFEE","XYs","Full2019_prev24","XY_file_1130-1415_15Mins_utc_DailyDiffs_Full_lastModel=12_daysAhead=[1-15]_Months=[1-12]_take9_prev24_PrevYs+Bollinger_V3d10.csv")
xy_csv_1415 = os.path.join(HOME,"COFFEE","XYs","Full2019_prev24","XY_file_1415-1715_15Mins_utc_DailyDiffs_Full_lastModel=12_daysAhead=[1-15]_Months=[1-12]_take9_prev24_PrevYs+Bollinger_V3d10.csv")


basic_analysis = False
hours_analysis = False

suffix_suffix = '_d=33'
suffix_suffix = ''

MODELS_WITH_6Z = ['PARA','PARACO','GEFS','CFS']
MODELS_WITH_12Z = MODELS_WITH_6Z #'PARA']

def basic_analysis():
    coffee_df = pd.read_csv(coffee_candles, parse_dates=['date', 'time_utc', 'time_chicago'])

    d2 = coffee_df.groupby(coffee_df.time_utc.dt.date).sum().reset_index()
    daily_df_max = coffee_df.groupby(coffee_df.time_utc.dt.date).max()['open']
    daily_df_min = coffee_df.groupby(coffee_df.time_utc.dt.date).min()['open']
    daily_df_avg = coffee_df.groupby(coffee_df.time_utc.dt.date).mean()['open']
    daily_df_minmax = ((daily_df_max - daily_df_min) / daily_df_avg).reset_index()

    d2['time_utc'] = pd.to_datetime(d2['time_utc'])
    daily_df_minmax['time_utc'] = pd.to_datetime(daily_df_minmax['time_utc'])

    d2.groupby(d2.time_utc.dt.month).mean()['vol'].plot(kind='bar',title='Coffee Avg Daily Vol | By Month')
    (daily_df_minmax.groupby(daily_df_minmax.time_utc.dt.month).mean()['open']*100).plot(kind='bar',title='Coffee Average Daily Range as % of price | By month')

    d2.groupby(d2.time_utc.dt.to_period('M')).mean()['vol'].plot(kind='bar',title='Coffee Avg Daily Vol | By Month-Year')
    (daily_df_minmax.groupby(daily_df_minmax.time_utc.dt.to_period('M')).mean()['open']*100).plot(kind='bar',title='Coffee Average Daily Range as % of price | By Month-Year')
    #  #.rolling(10,10).mean()
    plt.show()
    aa = 1

def hours_analysis():
    coffee_df = pd.read_csv(coffee_candles, parse_dates=['date', 'time_utc', 'time_chicago'])

    coffee_df['hour'] = coffee_df.time_utc.dt.hour
    coffee_df['day'] =  pd.to_datetime(coffee_df.time_utc.dt.date)
    hourly_df = coffee_df.groupby(['day','hour']).sum().reset_index()

    #hourly_df.groupby(['hour']).mean()['vol'].plot(kind='bar',title='Avg Vol | By Hour')
    #plt.show()
    print ('1')
    coffee_df = coffee_df[['day','hour','open']]
    dayhourly_df_min = coffee_df.groupby(['day','hour']).min()['open']
    print('2')
    dayhourly_df_max = coffee_df.groupby(['day', 'hour']).max()['open']
    print('3')
    dayhourly_df_avg = coffee_df.groupby(['day', 'hour']).mean()['open']
    print('4')
    dayhourly_df_minmax = ((dayhourly_df_max - dayhourly_df_min)/dayhourly_df_avg).reset_index()
    (dayhourly_df_minmax.groupby(['hour']).mean()*100).plot(kind='bar',title='Avg Max move inside each Hour in %')
    plt.show()

def load_diffs(model,last_hour=0,ft='CGDD',weights=['BR_ArabicaTrunc_weight'],
               suffix='v12_BArabica'):
    if last_hour == 12:
        bb = 0
    eps45 = pd.read_csv(os.path.join(HOME,"COFFEE","Xdata","X_file_DailyDiffs_%s%s_%s%s.csv") % (model, '', suffix,suffix_suffix),
                        parse_dates=['forecast_time'])
    eps45 = eps45[(eps45['feature_type'] == ft) & (eps45['weight'].isin(weights))]
    eps45 = eps45.drop_duplicates(subset=['forecast_time'], keep='last')

    if eps45.shape[0] == 0:
        return pd.DataFrame()

    if last_hour != 12 and not (model == 'CFS' and last_hour == 6):
        eps45 = eps45[eps45.isna().mean(axis=1) < 0.3]

    eps45['time'] = eps45['forecast_time'] + td(days=1 if (model in ['EPS45', 'GEFS35'] or last_hour == 18) else 0)
    eps45 = eps45[(eps45['forecast_time'].dt.hour == last_hour) & (eps45['forecast_time'] < dtdt.today())]

    # rename the columns
    l = list(eps45)
    eps45 = eps45.rename(columns={x: x.replace('diff', 'diff_%sZ' % last_hour) +
                                     '_%s' % (model) for x in l if 'diff' in x and 'diff8' not in x})
    eps45 = eps45.rename(columns={x: x.replace('Value', 'Value_%sZ' % last_hour) +
                                     '_%s' % (model) for x in l if 'Value' in x})

    eps45['date'] = eps45.time.dt.date
    if eps45.shape[0] != eps45.drop_duplicates(subset=['date', 'feature_type', 'weight']).shape[0]:
        raise AssertionError('eps45 file has several weight which will cause Error when merging')

    eps45 = eps45[[x for x in list(eps45) if x not in ['time', 'forecast_time']]]
    return eps45

def add_diffs_to_xy(xy_df,ft,models=['PARA', 'GEFS', 'PARACO','GEFSL','GFSCO'],always_add_suffix=False,suffix='v12_BArabica'):

    for model in models:
        eps45 = load_diffs(model, last_hour=0, ft=ft,suffix=suffix)
        if model in MODELS_WITH_6Z and eps45.shape[0] > 0:
            eps45 = eps45.merge(load_diffs(model, last_hour=6, ft=ft,suffix=suffix),
                   on=['date', 'feature_type'])
        if model in MODELS_WITH_12Z and '12Z' in suffix:
            eps45 = eps45.merge(load_diffs(model, last_hour=12, ft=ft,suffix=suffix),
                                on=['date', 'feature_type'])
        common = ['date'] #[x for x in list(eps45) if x in list(xy_df)]

        if eps45.shape[0] >0:
            if always_add_suffix:
                eps45 = eps45.rename(columns={x: x+'_%s'%ft for x in list(eps45) if model in x})
            xy_df = xy_df.merge(eps45,on=common,how='outer',suffixes=('','_%s'%ft)) #'inner' if model in ['PARA'] else 'outer')
    return xy_df

def modify_preds(xy_df,pred,ft):
    if ft in ['CGDD','PCDD']:
        #xy_df[pred] *= xy_df['date'].apply(lambda x: (-1 if (x.month in [4, 5, 6, 7, 8, 9] or (x.month == 3 and x.day > 10) or (x.month == 10 and x.day < 20)) else 1))
        xy_df[pred] *= -1
    elif ft in ['PCDD','CGDD']:
        xy_df[pred] *= xy_df['date'].apply(lambda x: (-1 if (x.month in [4,5,6,7,8,9] or (x.month ==3 and x.day > 10) or (x.month ==10 and x.day < 20)) else 1))
        pass
    return xy_df


def plot_pnl(a,preds,y_col,thresh=0.6):
    for p in preds:
        if 'CGDD' in p:
            a = modify_preds(a,p,'CGDD')
        elif 'PCDD' in p:
            a = modify_preds(a, p, 'PCDD')
        a['profit_%s_%s'%(y_col,p)] = np.sign(a[y_col]*a[p])*abs(a[y_col]) * 37.5
        a.loc[abs(a[p])<abs(a[p]).quantile(1-thresh) ,'profit_%s_%s' % (y_col, p)] = 0
    a = a.sort_values('date')
    a.set_index('date')[[x for x in list(a) if 'profit' in x]].fillna(0).cumsum().plot(style=['-']*5+['--']*(max(0,(len(preds)-5))))
    # a[['date']+preds_1415to1745[:1]+['profit_%s_%s'%(y_col,preds_1415to1745[0])]].sum()
    plt.show()
    return a


def plot_pnl_lexi(a,preds,y_col,thresh=0.6,add_mean=False):
    older_trades = a['date'].apply(lambda x: False)
    a['MEAN'] = (a[preds] / a[preds].std()).mean(axis=1)
    a['profit_Combined'] = 0
    i = 0
    for p in preds+['MEAN']:
        i+=1
        if 'CGDD' in p:
            a = modify_preds(a,p,'CGDD')
        elif 'PCDD' in p:
            a = modify_preds(a, p, 'PCDD')
        else:
            a = modify_preds(a, p, 'CGDD')
        a['profit_%s_%s'%(y_col,p)] = (np.sign(a[y_col]*a[p])*abs(a[y_col]) * 37.5).fillna(0)

        current_ignore = (abs(a[p]) < abs(a[p]).quantile(1 - thresh))

        if i == 1:
            bb = 0
        a['ignore_tmp'] = current_ignore
        a['old_trades'] = older_trades
        test = a[['date','ignore_tmp','old_trades']+[x for x in list(a) if 'profit' in x]]
        a.loc[current_ignore,'profit_%s_%s' % (y_col, p)] = 0
        a['tmp'] = a['profit_%s_%s'%(y_col,p)]
        a.loc[older_trades ,'tmp'] = 0

        print('final ignore ratio: %s | Added profit = %s' % (older_trades.mean(),a['tmp'].sum()))

        if p != 'MEAN' or add_mean:
            a['profit_Combined'] = a['profit_Combined']+a['tmp'].fillna(0)
        older_trades = older_trades | (~current_ignore)
    a = a.sort_values('date')
    a.set_index('date')[[x for x in list(a) if 'profit' in x]].fillna(0).cumsum().plot(title='Multi Preds + Comb',
                                                                   style=['-']*(4+2)+['--']*(max(0,(len(preds)-4-2))))
    # a[['date']+preds_1415to1745[:1]+['profit_%s_%s'%(y_col,preds_1415to1745[0])]].sum()
    plt.show()
    return a


#cofee_degdays = pd.read_csv(os.path.join(HOME,"COFFEE","degdays_archive","Live","Live_from_ec2","live_degdays_PARA.csv"),
 #                           parse_dates=['forecast_time','validation_day'])

MONTHS = list(range(1,12))
ft = 'PCDD'
suffix = 'v12_BArabica'

xy_df = pd.read_csv(xy_csv_1130,parse_dates=['time_utc'])
xy_df = xy_df[xy_df['time_utc']>=dtdt(2018,7,1)]
xy_df['date'] = xy_df['time_utc'].dt.date


#xy_df = add_diffs_to_xy(xy_df,ft)
xy_df = add_diffs_to_xy(xy_df,'PCDD',always_add_suffix=True,suffix=suffix)
xy_df = add_diffs_to_xy(xy_df,'CGDD',always_add_suffix=True,suffix=suffix)


xy_df['date'] = pd.to_datetime(xy_df['date'])
xy_df = xy_df.sort_values('date')
xy_df['y_1130-1715'] = xy_df['y_1130-1415'] + xy_df['y_1415-1715']
xy_df['y_1130-1745'] = xy_df['y_1130-1415'] + xy_df['y_1415-1745']
xy_df['y_0900-1100'] = xy_df['y_0900-1000'] + xy_df['y_1000-1100']
xy_df['y_1100-1200'] = xy_df['y_1100-1130'] + xy_df['y_1130-1200']
xy_df['y_0900-1745'] = xy_df['y_0900-1100']+xy_df['y_1100-1130']+xy_df['y_1130-1745']
xy_df['y_1545-1645'] = xy_df['y_1515-1645'] - xy_df['y_1515-1545']
xy_df['y_1415-1746_no12Z'] = xy_df['y_1415-1745'].fillna(0) + xy_df['y_1545-1645'].fillna(0)

correlations = xy_df[pd.to_datetime(xy_df['date'])>=dtdt(2020,4,15)].corr()[sorted([x for x in list(xy_df) if x.startswith('diff_') and '0-16' in x and 'last-Prev1D' in x])].loc[sorted([x for x in list(xy_df) if x.startswith('y_')])]
correlations.loc[['y_0900-1100','y_1100-1200','y_1145-1415','y_1415-1745','y_1615-1745','y_0900-1745']]
xy_df[pd.to_datetime(xy_df['date'])>=dtdt(2020,4,15)].corr()['y_1415-1745'].sort_values().loc[[x for x in list(xy_df) if 'CGDD' in x and ('0-13' in x or '0-16' in x) and 'diff' in x[:10]]].sort_values()

try:
    xy_df['diff_6Z_0-16_last-Prev123_PARA'] = xy_df['diff_6Z_0-16_last-Prev1_PARA'] +\
                                        0.5*xy_df['diff_6Z_0-16_last-Prev2_PARA']+\
                                        0.3* xy_df['diff_6Z_0-16_last-Prev3_PARA']
    xy_df['diff_0Z_0-16_last-Prev14D_PARA'] = xy_df['diff_0Z_0-16_last-Prev1D_PARA'] + xy_df['diff_0Z_0-16_last-Prev4D_PARA']
    xy_df['Value_0Z_0-2_PARA_ma20'] = xy_df['Value_0Z_0-2_PARA'].rolling(20, 10, center=False).mean()
    xy_df['Value_0Z_0-2_PARA_ma30'] = xy_df['Value_0Z_0-2_PARA'].rolling(30, 10, center=False).mean()
    xy_df['Value_0Z_0-2_PARA_ma90'] = xy_df['Value_0Z_0-2_PARA'].rolling(90, 10, center=False).mean()

except:
    pass


cfs_preds = ['diff_0Z_10-21_last-Prev3D_CFS','diff_0Z_28-42_last-Prev3D_CFS','diff_0Z_14-28_last-Prev1_CFS']
pred1 = 'diff_0Z_0-16_last-Prev4D_PARA_CGDD' # y_1415-1745
pred2 = 'diff_0Z_0-16_last-Prev1D_PARACO' # y_1415-1745 SELL | 1130-1415 BUY
pred3 = 'diff_0Z_0-16_last-Prev2D_GEFSL' #  y_1315-1615

pcdd_pred1 = 'diff_6Z_0-16_last-Prev2_PARA' # 1315-1745 with smart multiplier
pcdd_pred2 = 'diff_6Z_0-16_last-Prev2_PARACO' # 1315-1745 with smart multiplier - SUPER STRONG
pcdd_pred3 = 'diff_6Z_0-16_last-Prev1_GEFS' # 1315-1745 with smart multiplier
pcdd_gefsl_pred = 'diff_0Z_0-16_last-Prev1D_GEFSL_PCDD'

preds_1415to1745 = ['diff_6Z_0-13_last-Prev1_PARACO_PCDD','diff_6Z_0-16_last-Prev2_PARACO_PCDD',
                    'diff_0Z_0-16_last-Prev2D_GEFSL_PCDD','diff_6Z_0-16_last-Prev2D_GEFS_PCDD',
                    'diff_6Z_0-16_last-Prev4_GEFS_PCDD',
                    'diff_0Z_0-16_last-Prev1_PARACO_CGDD','diff_6Z_0-16_last-Prev2_PARACO_CGDD','diff_0Z_0-16_last-Prev1D_PARACO_CGDD',
                    'diff_0Z_0-16_last-Prev4D_PARA_CGDD','diff_6Z_0-16_last-Prev1_PARACO_CGDD']
preds_9to17 = ['diff_6Z_0-16_Prev1D-Prev3D_PARA_PCDD','diff_6Z_0-16_last-Prev2D_PARA_PCDD',
               'diff_6Z_0-16_last-Prev3D_PARA_PCDD','diff_0Z_0-16_last-Prev2D_GEFSL_PCDD',
               'diff_6Z_0-16_last-Prev2D_GEFS_PCDD','diff_0Z_0-16_last-Prev4D_GFSCO_PCDD',
                'diff_6Z_0-16_last-Prev2_PARACO_PCDD',
               'diff_0Z_0-16_last-Prev1_PARACO_CGDD','diff_6Z_0-16_last-Prev1_PARACO_CGDD'
               ]



plot_pnl_lexi(xy_df[xy_df['date']>=dtdt(2020,4,15)],preds_1415to1745[:4],'y_1415-1745',0.5,
              add_mean=True)

y_col = 'y_0900-1745' #-1745' #-1745'

xy_df['y_1515-1815'] = xy_df['y_1515-1715']+xy_df['y_1715-1815']
xy_df['y_1315-1645'] = xy_df['y_1315-1415']+xy_df['y_1415-1645']
xy_df['y_1315-1615'] = xy_df['y_1315-1515']+xy_df['y_1515-1615']
xy_df['y_1315-1745'] = xy_df['y_1315-1615']+xy_df['y_1615-1745']
xy_df['y_1415-1745'] = xy_df['y_1315-1615']+xy_df['y_1615-1745']-xy_df['y_1315-1415']
#pred = 'diff_6Z_0-16_last-Prev123_PARA'
#y_col = 'y_1615-1745'

## correlations
#xy_df[xy_df['date']>=dtdt(2020,4,1)].corr()[[x for x in list(xy_df) if ('PARA' in x or 'GEFS' in x) and 'last-' in x and '0-16' in x]].loc[sorted([x for x in list(xy_df) if x.startswith('y_')])]

#xy_df['Value_0Z_0-0_PARA'] /= 1.8
#xy_df['Value_0Z_0-16_PARA'] /= 1.8
#xy_df['Value_0Z_0-0_PARA'].hist(bins=100)
#xy_df.set_index('date')[['Value_0Z_0-0_PARA','Value_0Z_0-16_PARA']].rolling(5).mean().plot()
#plt.show()
#xy_df = xy_df[xy_df['Value_0Z_0-0_PARA']>12]


diff_cols = [x for x in list(xy_df) if x.startswith('diff_')]
val_cols = [x for x in list(xy_df) if x.startswith('Value_')]
xy_df[diff_cols] = xy_df[diff_cols].fillna(0)
xy_df[val_cols] = xy_df[val_cols].fillna(method='ffill')

def more_correlations():
    ref_y = 'y_1415-1745'
    c = xy_df[[ref_y,'diff_6Z_0-16_last-Prev2_PARA','diff_6Z_0-16_last-Prev4_PARA',
               'diff_6Z_0-16_last-Prev1D_PARA','diff_0Z_0-16_last-Prev4D_PARA',
               'Value_0Z_0-0_PARA','Value_0Z_0-8_PARA','Value_0Z_0-16_PARA']].groupby(xy_df.date.dt.month).corr().reset_index()
    c = c[c['level_1']==ref_y]

    d = xy_df[['diff_6Z_0-16_last-Prev2_PARA','y_1130-1315','y_1315-1515','y_1415-1645','y_1615-1745',
               'y_1315-1645','y_1315-1715','y_1130-1715','y_1315-1615']].groupby(xy_df.date.dt.month).corr().reset_index()
    d = d[d['level_1']=='diff_6Z_0-16_last-Prev2_PARA']


modify_preds(xy_df,pred,ft)
xy_df[y_col] *= 37.5

bb = 0
#xy_df = xy_df[~xy_df[pred].isna()]

#c = xy_df[(xy_df['date'].dt.weekday.isin([0,1,2,3,4]))&
 #         (xy_df['date'].dt.month.isin([10,11,12]))].corr()[sorted([x for x in list(xy_df) if x.startswith('y_')])]
xy_df = xy_df[xy_df['date'].dt.month.isin(MONTHS)]
small_df = wrap_analysis(xy_df,pred,y_col,weekdays=[1,2,3,4],
              start=dtdt(2018,4,1),end=dtdt(2021,10,1),chosen_ratio=0.5)
aa = 1
# Why am I missing 201904-07


