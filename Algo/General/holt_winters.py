import pandas as pd
from matplotlib import pyplot as plt
from statsmodels.tsa.holtwinters import ExponentialSmoothing as HWES
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
import os
from Algo.Utils.files_handle import HOME

TEST_SIZE = 2
MINIMAL_TRAIN_SIZE = 30 #366
FORECAST_COL = 'diff_0-13_last-Prev1_EPS' #'Value_0Z_0-13_EPS'  #'Retail_Sales'
TIME_COL = 'date'
PLOT_MIDDLE = False
SEASONALITY = 14 # # 365
SEASONALITY_TYPE = 'add'

#csv_file = r"C:\Users\<USER>\Documents\Ran\FlowPractice\Holt_Winters\retail_sales_used_car_dealers_us_1992_2020.csv"
csv_file = os.path.join(HOME,"XYs","Enriched_XYs","XY_a_GDD_v8_12Z.csv")


df0 = pd.read_csv(csv_file, header=0)
df0[TIME_COL] = pd.to_datetime(df0[TIME_COL],dayfirst=True)
df = df0.copy()


if FORECAST_COL == 'diff_0-13_last-Prev1_EPS':
    df1 = df[[TIME_COL,'diff_0Z_0-13_last-Prev1_EPS']]
    df2 = df[[TIME_COL,'diff_12Z_0-13_last-Prev1_EPS']]
    df1[FORECAST_COL] = df1['diff_0Z_0-13_last-Prev1_EPS']
    df2[FORECAST_COL] = df2['diff_12Z_0-13_last-Prev1_EPS']
    df2[TIME_COL] += td(hours=12)
    df = df1[[TIME_COL,FORECAST_COL]].merge(df2[[TIME_COL,FORECAST_COL]],on=[TIME_COL,FORECAST_COL],how='outer').sort_values(TIME_COL)
#df = df.set_index(TIME_COL)
df = df[[TIME_COL,FORECAST_COL]]
df = df.dropna(subset=[FORECAST_COL])
df = df.reset_index()

#df = df.asfreq(freq='D')
df[FORECAST_COL] += 100



#df.plot()
#plt.show()
for SEASONALITY in [2,4,5,8,10,16,21,28][:-3]:
    for SEASONALITY_TYPE in ['add','mul'][1:]:
        df['prediction'] = np.nan
        pointer = MINIMAL_TRAIN_SIZE
        while pointer < df.shape[0] - TEST_SIZE:
            df_train = df.iloc[pointer-MINIMAL_TRAIN_SIZE:pointer][[FORECAST_COL]]
            df_test = df.iloc[pointer:pointer + TEST_SIZE][[FORECAST_COL]]

            try:
                model = HWES(df_train, seasonal_periods=SEASONALITY, trend='add', seasonal=SEASONALITY_TYPE)
            except:
                bb = 0
                raise
            model._index_dates = True
            try:
                fitted = model.fit()
            except:
                bb = 0
                raise

            #print(fitted.summary())
            sales_forecast = fitted.forecast(steps=TEST_SIZE)
            df.loc[sales_forecast.index, 'prediction'] = sales_forecast
            # Plot test, train and forecast
            if PLOT_MIDDLE:
                fig = plt.figure()
                fig.suptitle('Retail Sales of Used Cars in the US (1992-2020)')
                past, = plt.plot(df_train.index, df_train, 'b.-', label='Sales History')
                future, = plt.plot(df_test.index, df_test, 'r.-', label='Actual Sales')
                predicted_future, = plt.plot(df_test.index, sales_forecast, 'g.-', label='Sales Forecast')
                plt.legend(handles=[past, future, predicted_future])
                #plt.show()
            pointer += TEST_SIZE
            #print ('Finished iteration.... Pointer = %s'%pointer)

        df_train = df.iloc[:MINIMAL_TRAIN_SIZE][[FORECAST_COL]]
        df_test = df.iloc[MINIMAL_TRAIN_SIZE:][[FORECAST_COL]]
        model = HWES(df_train, seasonal_periods=12, trend='add', seasonal='mul')
        fitted = model.fit()

        #print(fitted.summary())
        sales_forecast = fitted.forecast(steps=df.shape[0]-MINIMAL_TRAIN_SIZE)
        df['prediction_from_i=%s'%MINIMAL_TRAIN_SIZE] = sales_forecast

        total_corr = df.corr()[FORECAST_COL].loc['prediction']
        winter_corr = df[(df[TIME_COL].dt.month.isin([11,12,1,2,3]))&(df[TIME_COL]>=dtdt(2020,10,1))].corr()[FORECAST_COL].loc['prediction']
        winter_0z_corr = df[(df[TIME_COL].dt.month.isin([11,12,1,2,3]))&(df[TIME_COL]>=dtdt(2020,10,1))&(df[TIME_COL].dt.hour == 8)].corr()[FORECAST_COL].loc['prediction']
        winter_12z_corr = df[(df[TIME_COL].dt.month.isin([11, 12, 1, 2, 3])) & (df[TIME_COL].dt.hour == 20)].corr()[FORECAST_COL].loc['prediction']
        diff_corr = df.diff().corr()[FORECAST_COL].loc['prediction']
        txt = 'Forecast Col: %s ||| Seasonal Period = %s | Seasonality type =%s ||| Total Corr: %s | Winter Corr: %s (0Z = %s | 12Z = %s) '%(FORECAST_COL,SEASONALITY,SEASONALITY_TYPE, total_corr,winter_corr,
                                                                                                                        round(winter_0z_corr,3), round(winter_12z_corr,3))
        df['prediction_Slide=%s_Period=%s_Type=%s' % (TEST_SIZE, SEASONALITY, SEASONALITY_TYPE)] = df['prediction']
        print (txt)

"""
fig = plt.figure()
fig.suptitle('Retail Sales of Used Cars in the US (1992-2020)')
past, = plt.plot(df_train.index, df[FORECAST_COL].loc[df_train.index], 'b.-', label='Sales History')
future, = plt.plot(df_test.index, df[FORECAST_COL].loc[df_test.index], 'r.-', label='Actual Sales')
predicted_future, = plt.plot(df_test.index, df['prediction'].loc[df_test.index], 'g.-', label='Sales Forecast')
predicted_future_no_slide, = plt.plot(df_test.index, df['prediction_from_i=%s'%MINIMAL_TRAIN_SIZE].loc[df_test.index], 'y.-', label='Sales Forecast No Slide')

plt.legend(handles=[past, future, predicted_future]) #,predicted_future_no_slide])
plt.show()
"""

y_col = 'y_0600-0800'
df[[x for x in list(df) if x not in ['date','index']]] -= 100
df = df.dropna(subset=['prediction'])
(np.sign(df[FORECAST_COL]*df['prediction_Slide=1_Period=2_Type=add'])>0).mean()

df0 = df0.merge(df,on=['date'])
df0['profit_EPSpredHW'] = (np.sign(df0[y_col]*df0['prediction_Slide=1_Period=2_Type=add'])*df0[y_col])
df0.set_index('date')['profit_EPSpredHW'].cumsum()

c = df0.set_index('date')[['diff_0Z_0-13_last-Prev1_EPS','prediction_Slide=1_Period=2_Type=mul']].rolling(30,30).corr().reset_index()
c[c['level_1']=='diff_0Z_0-13_last-Prev1_EPS']

bb = 0