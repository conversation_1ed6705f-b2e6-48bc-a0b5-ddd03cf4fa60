import textract
from selenium import webdriver
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
import time
import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import urllib

from time import strptime
from Algo.Conf.Scraping import CHROME_DRIVER_LOCATION
import os
from Algo.Utils.files_handle import HOME

chrom_driver = CHROME_DRIVER_LOCATION




def main(max_days_back=7):
    driver = webdriver.Chrome(chrom_driver)
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')
    PROXY = "http://176.9.195.133:3128"
    # chrome_options.add_argument('--proxy-server=%s' % PROXY)
    # chrome_options.add_argument(
    #    '--no-sandbox')  # required when running as root user. otherwise you would get no sandbox errors.
    driver = webdriver.Chrome(executable_path=chrom_driver, chrome_options=chrome_options)
    # service_args=['--verbose', '--log-path=/tmp/chromedriver.log'])

    driver.minimize_window()
    driver.get("https://www.bespokeweather.com/users/sign_in")

    user = '<EMAIL>'
    password = '28BWAmuz90'

    input_email = driver.find_element_by_id("user_email")
    input_email.send_keys(user)
    time.sleep(1)
    input_pass = driver.find_element_by_id("user_password")
    input_pass.send_keys(password)
    time.sleep(1)
    remember_me_button = driver.find_element_by_id("user_remember_me")
    remember_me_button.click()
    time.sleep(1)
    signin_button = driver.find_element_by_name("commit")
    signin_button.click()
    time.sleep(1)
    driver.get("https://www.bespokeweather.com/contents/archive")

    driver.find_elements_by_class_name('a')

    html = driver.page_source
    soup = BeautifulSoup(html)
    # headline = soup.findAll("h1", {"class":"page-content-primary-title"})[0]
    a_objs = soup.findAll("a")
    updates_objs = [x for x in a_objs if 'Morning' in x.get_text()[:30]]
    updates_urls = ["https://www.bespokeweather.com%s"%x['href'] for x in updates_objs]
    titles = [x.get_text() for x in a_objs if 'Morning' in x.get_text()]

    stack = []
    csv = os.path.join(HOME,"Market_Data","Cash Prices","cash_prices.csv")
    for url,title in zip(updates_urls,titles):
        n = 0
        success = False

        while not success and n < 5:
            n += 1

            try:
                try:
                    current_df = pd.read_csv(csv,parse_dates=['report_date','date'])
                except:
                    current_df = pd.read_csv(csv, parse_dates=['report_date'])
                current_df['report_date'] = pd.to_datetime(current_df['report_date'])
                url_dld = url.replace('getpost','download')

                driver.get(url_dld)
                try:
                    date_str = driver.current_url.split('_Morning_Update')[0].split("/")[-1]
                    month_str = date_str.split("_")[0]
                    month = strptime(month_str, '%B').tm_mon
                    day = int(date_str.split("_")[1])
                    year = int(date_str.split("_")[2])
                    report_date = dtdt(year, month, day)
                except:
                    print('Couldnt parse name ... Skipping')
                    success = True
                    report_date = None
                    continue

                if current_df[current_df['report_date']==report_date].shape[0] > 0:
                    print ('Report: %s Was already in table... Skipping'%report_date)
                    success = True
                    continue

                web_file = urllib.request.urlopen(driver.current_url).read()
                local_file = r"C:\Users\<USER>\Downloads\tmp_PDF.pdf"
                urllib.request.urlretrieve(driver.current_url, local_file)
                s = 1

                txt = textract.process(local_file,'utf-8')
                try:
                    s = str(txt).find('Previous')
                    price = float(str(txt)[s:s+50].split("\\r")[0].split("$")[1])
                except:
                    s = str(txt).find('Recent Ca')
                    price = float(str(txt)[s:s + 50].split("\\r")[0].split("$")[1])
                aa = 1
                print ('Report Date: %s | Cash pric: %s'%(report_date, price))

                tmp_df = pd.DataFrame({'report_date':report_date,'price': price},index=[1])
                tmp_df['date'] = tmp_df['report_date'] - td(days=1)
                current_df = current_df.merge(tmp_df,on=list(current_df),how='outer')
                current_df.to_csv(csv,index=False)
                success = True
            except Exception as e:
                raise
                print ('Failes with Error: %s'%e)
                print ("Retrying (#%s"%n)
                time.sleep(10)
        if report_date is not None:
            if report_date < (dtdt.now() - td(days=max_days_back)):
                print ('reached report date = %s | Stopping !'%report_date)
                break
        else:
            print ('no report date was parsed... skipping')
if __name__ == '__main__':
    main(max_days_back=30)

a = 1