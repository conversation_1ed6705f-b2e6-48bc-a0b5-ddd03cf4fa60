2025-06-05 23:06:05,667 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,667 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,667 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,667 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,667 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,668 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,669 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,670 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,671 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,672 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,673 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,673 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,673 Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-05 23:06:05,674 Running candles retrieval
2025-06-05 23:06:05,682 Check call local is about to run cmd: C:\Users\<USER>\miniconda311\python.exe C:\Users\<USER>\PycharmProjects\TWS_API/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_candles_and_uniting_wrapper
