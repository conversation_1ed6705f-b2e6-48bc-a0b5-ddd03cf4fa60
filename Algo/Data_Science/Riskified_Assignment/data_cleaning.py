from mpl_toolkits.mplot3d import Axes3D
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt # plotting
import matplotlib.patches as mpatches

import numpy as np # linear algebra
import os # accessing directory structure
import time
import sys
import pandas as pd # data processing,
import seaborn as sns
from datetime import datetime as dtdt
from datetime import timedelta as td
import user_agents
from ua_parser import user_agent_parser

from constants import *
from riskified_utils import load_datasets



def initial_train_test_split():
    orders_df, events_df = load_datasets('original')
    orders_train_inds = orders_df['order_timestamp'].dt.tz_convert(None) < dtdt(2021,1,15)
    orders_test_inds = orders_df['order_timestamp'].dt.tz_convert(None) >= dtdt(2021,1,15)
    events_train_inds = events_df['event_at'].dt.tz_convert(None) < dtdt(2021,1,15)
    events_test_inds = events_df['event_at'].dt.tz_convert(None) >= dtdt(2021,1,15)

    orders_df.loc[orders_train_inds].to_csv(HOME_DIR+"\dataset\orders_Train.csv",index=False)
    orders_df.loc[orders_test_inds].to_csv(HOME_DIR+"\dataset\orders_Test.csv",index=False)
    events_df.loc[events_train_inds].to_csv(HOME_DIR+"\dataset\events_Train.csv",index=False)
    events_df.loc[events_test_inds].to_csv(HOME_DIR+"\dataset\events_Test.csv",index=False)


"""
Cleaning and Processing === >>>>
"""


def add_time_features(df,time_col='order_timestamp'):
    df['weekday'] = df[time_col].dt.weekday
    # we use UTC weekends as it doesnt really matter where to cut
    df['is_weekend'] = df[time_col].dt.weekday.isin([5,6]).astype(int)
    df['hour'] = df[time_col].dt.hour
    df['quarter_hour'] = (np.ceil((df[time_col].dt.minute / 15)).replace(0,4)).astype(int)
    # 3-11 UTC = night, 11-19 = morning, rest=evening
    df['is_night'] = df[time_col].dt.hour.isin(range(3,11)).astype(int)
    df['is_morning'] = df[time_col].dt.hour.isin(range(11,19)).astype(int)
    df['is_evening'] = (~df[time_col].dt.hour.isin(range(3,19))).astype(int)
    return df


def geo_features_extraction(latlon):
    """
    didnt have time for adding
    calc continent, country, state, population_density_of_radius_10km^2
    :param latlon:
    :return:
    """
    pass


def add_email_features(df,time_col='order_timestamp'):
    """
    email_age_riskified = (current_timestamp - time_first_appearance_riskified).hours
    email_age_data_sources = (current_timestamp - time_first_appearance_data_sources).hours
    :return:
    """
    df['riskified_email_age'] = np.clip(((df[time_col] - df['riskified_email_first_seen']).dt.total_seconds() / (3600*24)),0,1000)
    df['datasources_email_age'] = np.clip(((df[time_col] - df['datasource_email_first_seen']).dt.total_seconds() / (3600*24)),0,1000)
    df['email_age_difference'] = df['datasources_email_age'] - df['riskified_email_age']
    return df

def add_user_agent_features(df):
    unique_user_agents_df = pd.DataFrame({'user_agent':df['user_agent'].unique()},index=range(98))
    stack = []
    for i,user_agent_name in unique_user_agents_df.iterrows():
        try:
            user_agent_info = user_agents.parse(user_agent_name['user_agent'])
            user_agents_dict = {'ua_device_family':user_agent_info.device.family,
                          'ua_device_brand':user_agent_info.device.brand,
                          'ua_device_model':user_agent_info.device.model,
                          'ua_browser_family':user_agent_info.browser.family,
                          'ua_os_family':user_agent_info.os.family,
                          'ua_os':user_agent_info.os.family+user_agent_info.os.version_string,
                          }
        except:
            user_agents_dict = {'ua_device_family': np.nan,'ua_device_brand': np.nan,
                                'ua_device_model': np.nan,'ua_browser_family': np.nan,
                                'ua_os_family': np.nan,'ua_os': np.nan,
                                }
        # try additional features
        try:
            user_agents_dict.update({'ua_is_bot':int(user_agent_info.is_bot),
                                     #'ua_is_email_client': user_agent_info.is_email_client,
                                     })
        except:
            user_agents_dict.update({'ua_is_bot':np.nan,
                                     #'ua_is_email_client': np.nan,
                                     })
        stack.append(user_agents_dict)

    unique_user_agents_info_df = pd.DataFrame(stack,index=range(98))
    unique_user_agents_df = unique_user_agents_df.merge(unique_user_agents_info_df,left_index=True,right_index=True)
    # extract specific suspicious info
    unique_user_agents_df['ua_is_ipad'] = unique_user_agents_df['ua_device_family'].str.contains('iPad').astype(int)
    unique_user_agents_df['ua_is_old_windows'] = unique_user_agents_df['ua_os'].isin(['Windows7','Winsows8.1','Windows8']).astype(int)

    df = df.merge(unique_user_agents_df,on=['user_agent'])
    return df


def prepare_final_dataframe(file_type='train'):
    assert file_type in ['train','test']

    orders_df, events_df = load_datasets(file_type)
    # events data is kept aside, too partial and unclear if usable in production
    # (altough password events is indicative of frauds
    # events_df = events_df.loc[events_df['event_field'].str.contains('password')]

    # add the binary target, assuming declined trades
    orders_df['Is_fraud_balanced'] = (~orders_df['order_status'].isin(['approved'])).astype(int)
    orders_df['Is_fraud_strict'] = (~orders_df['order_status'].isin(['approved','declined'])).astype(int)

    # drop tz
    orders_df['order_timestamp'] = orders_df['order_timestamp'].dt.tz_convert(None)
    # convert boolean to numeric
    orders_df['has_apple_pay_support'] = orders_df['has_apple_pay_support'].astype(int)

    orders_df = add_time_features(orders_df,time_col='order_timestamp')
    orders_df = add_email_features(orders_df)
    orders_df = add_user_agent_features(orders_df)

    # shift longitudes to be positive
    orders_df['ip_longitude'] += 360
    orders_df['billing_longitude'] += 360

    # dropping high cardinality features that are either dependent of others or dont make much sense
    orders_df = orders_df[['order_id','order_timestamp','order_status','Is_fraud_strict','Is_fraud_balanced']+\
                                [x for x in orders_df.columns.tolist() if x not in ['order_id','order_timestamp','order_status','Is_fraud_strict','Is_fraud_balanced',
                                                                                    'ua_device_family', 'ua_device_brand', 'ua_device_model',
                                                                                    'ua_browser_family', 'ua_os','user_agent','riskified_email_first_seen','datasource_email_first_seen']]]


    outpath = FINAL_ORDERS_TRAIN_CSV if file_type == 'train' else FINAL_ORDERS_TEST_CSV

    orders_df.to_csv(outpath,index=False)
    aa = 1



if __name__ == '__main__':
    #initial_train_test_split()
    prepare_final_dataframe('train')
    prepare_final_dataframe('test')
    pass


