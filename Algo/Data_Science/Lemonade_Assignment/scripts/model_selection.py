import gc
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.model_selection import KFold,StratifiedKFold
from sklearn.metrics import roc_auc_score
from sklearn.metrics import classification_report

from sklearn.ensemble import RandomForestClassifier
from sklearn.ensemble import AdaBoostClassifier
from catboost import CatBoostClassifier
from sklearn.ensemble import GradientBoostingClassifier

from imblearn.under_sampling import RandomUnderSampler
from imblearn.over_sampling import RandomOverSampler
from imblearn.over_sampling import SMOTE

from sklearn.preprocessing import LabelEncoder,OrdinalEncoder,OneHotEncoder


from sklearn import svm
from lightgbm import LGBMClassifier
import lightgbm
import time
import xgboost as xgb
from sklearn.model_selection import cross_val_score
import numpy as np

import pandas as pd
pd.set_option('display.max_columns', 100)



RFC_METRIC = 'gini'  #metric used for RandomForrestClassifier
NUM_ESTIMATORS = 100 #number of estimators used for RandomForrestClassifier / lgbm
NO_JOBS = 1 #number of parallel jobs used for RandomForrestClassifier


#TRAIN/VALIDATION/TEST SPLIT
#VALIDATION
VALID_SIZE = 0.3 # simple validation using train_test_split
TEST_SIZE = 0.7 # test size using_train_test_split

#CROSS-VALIDATION
NUMBER_KFOLDS = 5 #number of KFolds for cross-validation


RANDOM_STATE = 2021

MAX_ROUNDS = 1000 #lgb iterations
EARLY_STOP = 50 #lgb early stop
OPT_ROUNDS = 1000  #To be adjusted based on best validation rounds
VERBOSE_EVAL = 50 #Print out metric result

TARGET_NAME = 'is_claim'



def _subsample_data(X,y,sampling_strategy='auto'):
    rus = RandomUnderSampler(random_state=0,sampling_strategy=sampling_strategy)
    X_resampled, y_resampled = rus.fit_resample(X, y)
    return X_resampled, y_resampled


def _oversample_data(X,y,sampling_strategy='auto'):
    ros = RandomOverSampler(random_state=0,sampling_strategy=sampling_strategy)
    X_resampled, y_resampled = ros.fit_resample(X, y)
    return X_resampled, y_resampled


def _smote_resample(X,y):
    sm = SMOTE(random_state=0)
    X_resampled, y_resampled = sm.fit_resample(X, y)
    return X_resampled, y_resampled


def modify_y_pred_by_approval_rate(y_pred,y_pred_class1_proba,desired_approval_rate=0.9):
    threshold = np.quantile(y_pred_class1_proba, desired_approval_rate)
    y_pred_new = np.zeros(y_pred.size)
    y_pred_new[y_pred_class1_proba > threshold] = 1
    return y_pred_new


def _drop_users_with_leakage_from_train(df, X_train_cv, y_train_cv, X_test_cv,
                                        train_idx, test_idx):
    predictors = list(X_train_cv)
    for c in ['user_id', 'previous_policies']:
        if c not in predictors:
            X_train_cv[c] = df[c].iloc[train_idx]
            X_test_cv[c] = df[c].iloc[test_idx]
    # let's take the common users and then filter the problematic - those wite future events in train
    common_users_df = X_train_cv[['user_id', 'previous_policies']].merge(X_test_cv[['user_id', 'previous_policies']],
                                                                         on=['user_id'], suffixes=('_train', '_test'))
    users_with_leakage_df = common_users_df[
        common_users_df['previous_policies_train'] >= common_users_df['previous_policies_test']]
    users_with_leakage_lst = users_with_leakage_df['user_id'].tolist()
    # keep only valid indices
    filtered_indices = ~X_train_cv['user_id'].isin(users_with_leakage_lst)
    X_train_cv, y_train_cv = X_train_cv[filtered_indices], y_train_cv[filtered_indices]

    # drop the unnecessary columns
    for c in ['user_id', 'previous_policies']:
        if c not in predictors:
            try:
                X_train_cv = X_train_cv.drop(c, axis=1)
                X_test_cv = X_test_cv.drop(c, axis=1)
            except:
                pass
    return X_train_cv, y_train_cv,X_test_cv


def fill_nans_generic(train_df,method='median',test_df=None):
    if method == 'median':
        ref = train_df.median()
    elif method == 'mean':
        ref = train_df.mean()
    else:
        raise AssertionError('Invalid fillnans method')
    train_df_filled = train_df.fillna(ref)
    if test_df is None:
        return train_df_filled
    test_df_filled = test_df.fillna(ref)
    return train_df_filled, test_df_filled


def one_hot_encoding(df,col_name,test_df=None,allow_override=False):

    encoder = OneHotEncoder(handle_unknown='ignore')
    # Assigning numerical values and storing in another column
    encoder.fit(df[[col_name]].dropna())
    encoded_df = pd.DataFrame(encoder.transform(df[[col_name]].dropna()).toarray(),
                                index=df.dropna(subset=[col_name]).index)
    names = encoder.get_feature_names()
    encoded_df = encoded_df.rename(columns={i: names[i].replace('x0','is')+'_enc' for i in range(encoded_df.shape[1])}).astype('int32')
    if allow_override:
        df = df.drop(list(encoded_df),axis=1)
    if not set(list(encoded_df)) <= set(list(df)):
        df = pd.concat([df, encoded_df], axis=1)
    if test_df is not None:
        encoded_test_df = pd.DataFrame(encoder.transform(test_df[[col_name]]).toarray(),
                                  index=test_df.index)
        encoded_test_df = encoded_test_df.rename(columns={i: names[i].replace('x0', 'is') + '_enc' for i in range(encoded_test_df.shape[1])}).astype('int32')
        if allow_override:
            test_df = test_df.drop(list(encoded_test_df),axis=1)
        if not set(list(encoded_df)) <= set(list(test_df)):
            test_df = pd.concat([test_df,encoded_test_df],axis=1)
        return df, test_df
    return df


def manual_kfold_CV(df, clf, predictors, target=TARGET_NAME, desired_approval_rate=0.9,
                    fillna_method='median', categorical_columns_for_one_hot_encoding=[],
                    is_stratified=False,balance_method=None):
    """
    We wrap manually to handle the users with past
    :return tuple (df, dict)
        df: datframe with predictions and probabilities
        dict: a dictionary with main KPIs based on approval rate
    """
    assert TARGET_NAME not in predictors, 'Target appeared in predictors list'

    # make sure index is new
    df = df.reset_index().drop('index', axis=1)
    # initialize predictions
    y_pred_colname = 'y_pred_%s' % desired_approval_rate
    df[y_pred_colname] = np.nan
    df[y_pred_colname + '_proba'] = np.nan

    if not is_stratified:
        kf = KFold(n_splits=NUMBER_KFOLDS, random_state=RANDOM_STATE, shuffle=True)
        cv_split = kf.split(df)
    else:
        kf = StratifiedKFold(n_splits=NUMBER_KFOLDS, random_state=RANDOM_STATE, shuffle=True)
        cv_split = kf.split(df, y=df[target])

    for train_idx, test_idx in cv_split:
        X_train_cv, y_train_cv = df.drop(target, axis=1).iloc[train_idx], df[target].iloc[train_idx]
        X_test_cv, y_test_cv = df.drop(target, axis=1).iloc[test_idx], df[target].iloc[test_idx]

        # drop invalid users from train
        X_train_cv, y_train_cv, X_test_cv = _drop_users_with_leakage_from_train(df, X_train_cv, y_train_cv,
                                                                                X_test_cv,
                                                                                train_idx, test_idx)

        # Fill Nans based on train
        X_train_cv, X_test_cv = fill_nans_generic(X_train_cv, test_df=X_test_cv)
        # Fill categorical nans with most frequent
        # Peform one hot encoding based on train
        for c in categorical_columns_for_one_hot_encoding:
            if X_train_cv[c].isna().sum() > 0:
                most_frequent_val = X_train_cv[c].value_counts().index[0]
                X_train_cv[c] = X_train_cv[c].fillna(most_frequent_val)
                X_test_cv[c] = X_test_cv[c].fillna(most_frequent_val)

            X_train_cv, X_test_cv = one_hot_encoding(X_train_cv, c, test_df=X_test_cv)
            X_train_cv = X_train_cv.drop(c, axis=1)
            X_test_cv = X_test_cv.drop(c, axis=1)

        X_train_cv = X_train_cv[predictors]
        X_test_cv = X_test_cv[predictors]

        if balance_method is not None:
            if balance_method == 'sub':
                X_train_cv, y_train_cv = _subsample_data(X_train_cv, y_train_cv)
            elif balance_method == 'over':
                X_train_cv, y_train_cv = _oversample_data(X_train_cv, y_train_cv)
            elif balance_method == 'smote':
                X_train_cv,y_train_cv = _smote_resample(X_train_cv,y_train_cv)

        clf.fit(X_train_cv, y_train_cv)
        y_pred = clf.predict(X_test_cv)
        y_pred_class1_proba = clf.predict_proba(X_test_cv)[:, 1]
        y_pred_approval_fix = modify_y_pred_by_approval_rate(y_pred, y_pred_class1_proba, desired_approval_rate)
        # turn to series with correct indices
        y_pred_approval_fix = pd.Series(y_pred_approval_fix, index=test_idx)
        y_pred_class1_proba = pd.Series(y_pred_class1_proba, index=test_idx)
        df[y_pred_colname] = df[y_pred_colname].fillna(y_pred_approval_fix)
        df[y_pred_colname + '_proba'] = df[y_pred_colname + '_proba'].fillna(y_pred_class1_proba)

    roc_auc_cv_score = roc_auc_score(df[target], df[y_pred_colname + '_proba'])
    recall_cv_score = recall_score(df[target], df[y_pred_colname])
    precision_cv_score = precision_score(df[target], df[y_pred_colname])
    f1_cv_score = f1_score(df[target], df[y_pred_colname])
    kpis_dict = {'f1': f1_cv_score, 'precision': precision_cv_score,
                 'recall': recall_cv_score, 'roc_auc': roc_auc_cv_score}

    return df[[y_pred_colname, y_pred_colname + '_proba']].copy(), kpis_dict












rf_dynamic_params = {'max_features': None, # 10,15
                     'bootstrap': True, # False
                    }
rf_clf = RandomForestClassifier(n_jobs=NO_JOBS,
                                random_state=RANDOM_STATE,
                                criterion=RFC_METRIC,
                                n_estimators=NUM_ESTIMATORS,
                                verbose=False,
                                max_features=rf_dynamic_params['max_features'],
                                bootstrap=rf_dynamic_params['bootstrap']
                               )

ada_clf = AdaBoostClassifier(random_state=RANDOM_STATE,
                         algorithm='SAMME.R',
                         learning_rate=0.8,
                             n_estimators=NUM_ESTIMATORS)

cb_clf = CatBoostClassifier(iterations=250, #500,
                             learning_rate=0.02,
                             depth=12,
                             eval_metric='AUC', # 'F1'
                             random_seed = RANDOM_STATE,
                             bagging_temperature = 0.2,
                             od_type='Iter',
                             metric_period = VERBOSE_EVAL,
                             od_wait=100)

lgbm_depth = 4
lgbm_dynamic_params = {'max_depth':lgbm_depth,
                      'num_leaves': 2**lgbm_depth-1,
                      'metric':'auc',  # / f1
                      }
lgbm_clf = lightgbm.LGBMClassifier(boosting_type='gbdt',
                              n_estimators=NUM_ESTIMATORS,
                              learning_rate=0.1,
                              objective='binary',
                              n_jobs=NO_JOBS, random_state=RANDOM_STATE,
                              subsample=0.9,  # Subsample rows to reduce variance
                              subsample_freq=int(NUM_ESTIMATORS/2),  # don't always subsample
                              colsample_bytree=0.9,  # Subsample columns to reduce variance
                              scale_pos_weight=150, # should help with extremely unbalanced
                              max_depth=lgbm_dynamic_params['max_depth'],
                                num_leaves=lgbm_dynamic_params['num_leaves'],
                                   metric=lgbm_dynamic_params['metric'])






depths = [2,4,6,8,10]
objectives = ['regression','binary','cross_entropy']  #'regression_l1', 'huber', 'fair', 'poisson', 'quantile', 'mape', 'gamma', 'tweedie', 'binary', 'multiclass', 'multiclassova', 'cross_entropy', 'cross_entropy_lambda', 'lambdarank']

lgbm_clf = lightgbm.LGBMClassifier(max_depth=6)  # should help with extremely unbalanced


from sklearn.model_selection import cross_val_score




x_columns_to_drop = ['id', 'user_id','state', 'square_ft', 'postal_code',
                     'product', 'fire_housing_proximity', 'card_type',
                     'high_risk_dog','coast',
                     ]
train_df2 = pd.read_csv \
    (r"/Algo/Data_Science/Lemonade_Assignment/dataset/train_df_TEMP.csv",
     )
train_df2 = train_df2.fillna(train_df2.median())

from sklearn.metrics import confusion_matrix,recall_score,precision_score,f1_score

predictors = [x for x in list(train_df2) if x not in x_columns_to_drop and x != TARGET_NAME]
results_df, kpis_dict = manual_kfold_CV(train_df2,lgbm_clf,predictors,TARGET_NAME)



# train_df2 = train_df2.drop(x_columns_to_drop, axis=1)  # .dropna(how='any',axis=0)
X = train_df2.drop(TARGET_NAME, axis=1)
y = train_df2[TARGET_NAME]

X = X.drop(['user_age', 'median_home_value', 'population_density', 'median_household_income', 'occupied_housing_units', 'housing_units'],
           axis=1)

importances_stack = {}
for objective in objectives:
    for depth in depths[:4]:
        try:
            print ('======= Objective: %s | Depth = %s ======='%(objective,depth))

            recalls = []
            for i in range(10):
                # print ('Iteration Number %s'%i)
                X_train,X_test,y_train,y_test = train_test_split(X,y,stratify=y,test_size=0.3)
                # X_train,y_train = _smote_resample(X_train,y_train)
                X_train,y_train = _subsample_data(X_train,y_train)
                X_train,y_train = _oversample_data(X_train,y_train)
                # todo
                #clf = ada_clf #lgbm_clf #lgbm_clf
                # clf = lightgbm.LGBMClassifier(max_depth=depth, objective=objective,num_class=1,
                #                               n_estimators=100)
                # clf = GradientBoostingClassifier(max_depth=depth)
                clf = rf_clf
                clf.fit(X_train,y_train)
                importances = pd.Series(clf.feature_importances_,index=list(X_train))
                importances_stack['Obj=%s_depth=%s'%(objective,depth)] = importances
                y_pred = clf.predict(X_test)
                y_pred_class1_proba = clf.predict_proba(X_test)[:,1]

                y_pred_new = modify_y_pred_by_approval_rate(y_pred,y_pred_class1_proba,0.9)
                # print ('Threshold: %s | We have %s predicted claims '%(threshold,y_pred_new.mean()))
                # print ('Precision: %s'%precision_score(y_test,y_pred_new,pos_label=0))
                # print ('Recall: %s'%recall_score(y_test,y_pred_new))
                # print ('F1: %s'%f1_score(y_test,y_pred_new))

                confusion_matrix_results = confusion_matrix(y_test, y_pred_new)
                # print(confusion_matrix_results)
                # print(classification_report(y_test, y_pred_new,output_dict=False))
                recalls.append(recall_score(y_test,y_pred_new))

            recalls = pd.Series(recalls)
            print('recall_mean = %s'%recalls.mean())
        except:
            pass

importances_df = pd.DataFrame(importances_stack)
bb = 0
#importances_df.mean(axis=1).sort_values(ascending=False).to_csv(r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Data_Science\Lemonade_Assignment\dataset\RandomForest_basic_importances.csv")
