import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from matplotlib import pyplot as plt
from datetime import datetime as dtdt

def handle_clustering(pivot,pivot_T,num_clusters = 7):
    # Create the KMeans model
    kmeans = KMeans(n_clusters=num_clusters, random_state=42)
    # Fit the model to the data
    kmeans.fit(pivot_T)
    # Add cluster labels to the DataFrame
    pivot_T['cluster'] = kmeans.labels_

    # Print the cluster centers (centroids)
    #print("Cluster Centers:")
    #print(kmeans.cluster_centers_)
    i = 1
    for centroid in kmeans.cluster_centers_:
        pivot[f'centroid_{i}'] = centroid
        i += 1

    cluster_weights = pivot_T['cluster'].value_counts().to_dict()
    cluster_weights = {(f'centroid_{k+1}',''):v for k,v in cluster_weights.items()}
    return pivot, cluster_weights,pivot_T

def plot_clusters(pivot_to_plot,
                  cluster_weights,starting_point=0,end_point=None,title='',width_multiplier=1,
                  current_week=None):
    plt.figure(figsize=(20, 14))
    # Loop through each numeric column and plot it with the specified linewidth
    for i, column in enumerate(pivot_to_plot.columns):
        # print(cluster_weights.get(column,1))
        # print(column)
        if end_point is None:
            pivot_to_plot2 = pivot_to_plot[starting_point:] - pivot_to_plot.iloc[starting_point]
        else:
            pivot_to_plot2 = pivot_to_plot[starting_point:end_point] - pivot_to_plot.iloc[starting_point]
        if column not in cluster_weights:
            print('column %s not in cluster weights'%column)
        # come up with 10 distinct Tableau Palette Colors
        #colors = ['tab:blue', 'tab:orange', 'tab:green', 'tab:red', 'tab:purple','tab:brown', 'tab:pink', 'tab:gray', 'tab:olive', 'tab:cyan']
        plt.plot(pivot_to_plot2[column], linewidth=cluster_weights.get(column,1)*width_multiplier if str(dtdt.now().year) not in str(column) else 3, label=column,linestyle='-' if i > 1 else '--',
                 # color=colors
                 )
    # make the first curve thicker
    c = list(pivot_to_plot)[0]
    if current_week is not None:
        # add vertical dashed red line to signal current week (x axis)
        plt.axvline(x=current_week, color='r', linestyle='--')
    #plt.plot(pivot_to_plot2[c], linewidth=3, label=c,linestyle='--')
    #raise

    # Add labels, title, and legend
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.title(title)
    plt.legend()

    # Show the plot
    plt.show()