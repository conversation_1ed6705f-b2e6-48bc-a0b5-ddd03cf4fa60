#%%

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf

# ---------------------------------------
# Strategy 1: Bollinger Bands Mean Reversion
# ---------------------------------------
def bollinger_bands_strategy(df, window=20, num_std=2):
    df = df.copy()
    df['rolling_mean'] = df['close'].rolling(window=window).mean()
    df['rolling_std'] = df['close'].rolling(window=window).std()
    # If the close is below its rolling mean, we expect an upward move.
    df['prediction_bb'] = (df['rolling_mean'] - df['close']) / (df['rolling_std'] + 1e-8)
    return df

# ---------------------------------------
# Strategy 2: EMA Crossover Strategy
# ---------------------------------------
def ema_crossover_strategy(df, short_window=10, long_window=30):
    df = df.copy()
    df['ema_short'] = df['close'].ewm(span=short_window, adjust=False).mean()
    df['ema_long'] = df['close'].ewm(span=long_window, adjust=False).mean()
    df['prediction_ema'] = df['ema_short'] - df['ema_long']
    return df

# ---------------------------------------
# Strategy 3: VWAP Strategy
# ---------------------------------------
def vwap_strategy(df, window=20):
    df = df.copy()
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
    df['rolling_vwap'] = (df['typical_price'] * df['volume']).rolling(window=window).sum() / (df['volume'].rolling(window=window).sum() + 1e-8)
    df['prediction_vwap'] = df['close'] - df['rolling_vwap']
    return df

# ---------------------------------------
# Strategy 4: High-Low Channel (Range Breakout) Strategy
# ---------------------------------------
def high_low_channel_strategy(df, window=20):
    df = df.copy()
    df['highest_high'] = df['high'].rolling(window=window).max()
    df['lowest_low'] = df['low'].rolling(window=window).min()
    df['channel_mid'] = (df['highest_high'] + df['lowest_low']) / 2
    df['prediction_channel'] = (df['close'] - df['channel_mid']) / ((df['highest_high'] - df['lowest_low']) + 1e-8)
    return df

# ---------------------------------------
# Strategy 5: Candlestick Body Strategy
# ---------------------------------------
def candlestick_body_strategy(df):
    df = df.copy()
    df['body_ratio'] = (df['close'] - df['open']) / ((df['high'] - df['low']) + 1e-8)
    df['prediction_candle'] = df['body_ratio']
    return df

def weekday_recent_avg_strategy(df, num_weeks=4):
    """
    For each row, compute the simple average of delta_y for the last `num_weeks`
    occurrences of the same weekday. This uses past performance on that weekday
    (shifted by one period to avoid lookahead bias) as the prediction.
    
    The resulting prediction is stored in the column 'prediction_weekday_avg'.
    """
    df = df.copy()
    # Ensure a weekday column exists (Monday=0, Sunday=6)
    df['weekday'] = df['date'].dt.weekday
    df['prediction_weekday_avg'] = df.groupby('weekday')['delta_y'] \
                                      .apply(lambda x: x.shift(1).rolling(window=num_weeks, min_periods=1).mean())
    return df


def weekday_recent_weighted_avg_strategy(df, num_weeks=4):
    """
    For each row, compute a weighted average of delta_y for the last `num_weeks`
    occurrences of the same weekday. More recent days receive higher weight.
    
    The weights are assigned linearly (1, 2, ..., N) so that the most recent
    observation gets the highest weight. The resulting prediction is stored in
    the column 'prediction_weekday_weighted'.
    """
    df = df.copy()
    df['weekday'] = df['date'].dt.weekday
    df['prediction_weekday_weighted'] = df.groupby('weekday')['delta_y'] \
        .apply(lambda x: x.shift(1).rolling(window=num_weeks, min_periods=1)
                           .apply(lambda s: np.dot(s, np.arange(1, len(s)+1)) / np.arange(1, len(s)+1).sum(), raw=True))
    return df


def weekday_recent_median_strategy(df, num_weeks=4):
    """
    For each row, compute the median of delta_y for the last `num_weeks`
    occurrences of the same weekday. This uses the robust central tendency (median)
    of past returns on that weekday (after shifting to avoid lookahead bias)
    as the prediction.
    
    The resulting prediction is stored in the column 'prediction_weekday_median'.
    """
    df = df.copy()
    df['weekday'] = df['date'].dt.weekday
    df['prediction_weekday_median'] = df.groupby('weekday')['delta_y'] \
                                        .apply(lambda x: x.shift(1).rolling(window=num_weeks, min_periods=1).median())
    return df

def meta_weekday_recent_avg_strategy(df, windows=[4, 8, 12, 16]):
    """
    For each day in the DataFrame, this meta strategy computes the average of the 
    predictions produced by the weekday_recent_avg_strategy for several window lengths.
    
    It runs the weekday_recent_avg_strategy (which computes the average delta_y for the 
    past num_weeks occurrences of the same weekday, shifted by 1 to avoid lookahead bias)
    for each window in the list and then averages the resulting predictions.
    
    The final meta prediction is stored in the column 'meta_prediction_weekday_avg'.
    """
    df = df.copy()
    prediction_list = []
    for w in windows:
        temp_df = weekday_recent_avg_strategy(df, num_weeks=w)
        prediction_list.append(temp_df['prediction_weekday_avg'])
    df['meta_prediction_weekday_avg'] = pd.concat(prediction_list, axis=1).mean(axis=1)
    return df


def meta_weekday_recent_weighted_strategy(df, windows=[4, 8, 12, 16]):
    """
    For each day in the DataFrame, this meta strategy computes the average of the 
    predictions produced by the weekday_recent_weighted_avg_strategy for several window lengths.
    
    The weekday_recent_weighted_avg_strategy computes a weighted average of delta_y for the 
    past num_weeks occurrences of the same weekday, where more recent days are given higher weight.
    
    The final meta prediction is stored in the column 'meta_prediction_weekday_weighted'.
    """
    df = df.copy()
    prediction_list = []
    for w in windows:
        temp_df = weekday_recent_weighted_avg_strategy(df, num_weeks=w)
        prediction_list.append(temp_df['prediction_weekday_weighted'])
    df['meta_prediction_weekday_weighted'] = pd.concat(prediction_list, axis=1).mean(axis=1)
    return df


def meta_weekday_recent_median_strategy(df, windows=[4, 8, 12, 16]):
    """
    For each day in the DataFrame, this meta strategy computes the average of the 
    predictions produced by the weekday_recent_median_strategy for several window lengths.
    
    The weekday_recent_median_strategy computes the median of delta_y for the past 
    num_weeks occurrences of the same weekday (shifted by 1 to avoid lookahead bias).
    
    The final meta prediction is stored in the column 'meta_prediction_weekday_median'.
    """
    df = df.copy()
    prediction_list = []
    for w in windows:
        temp_df = weekday_recent_median_strategy(df, num_weeks=w)
        prediction_list.append(temp_df['prediction_weekday_median'])
    df['meta_prediction_weekday_median'] = pd.concat(prediction_list, axis=1).mean(axis=1)
    return df

# ---------------------------------------
# Performance Analysis Function with Quantile-based Threshold and Profit as Cumulative Sum (%)
# ---------------------------------------
def analyze_strategy(df, prediction_col, threshold=0.0, avoid_shorts=False):
    """
    Backtests a strategy by:
      - Generating trading signals based on the strategy's prediction.
      - Days with absolute predictions below the quantile threshold (computed on absolute predictions)
        are assigned a neutral signal (0).
      - If avoid_shorts is True, only long positions (signal = 1) are allowed.
      - Computes daily profit in percentage terms, then takes the cumulative sum.
    
    Parameters:
      - df: DataFrame containing the columns 'close', 'delta_y', etc.
      - prediction_col: The column name of the prediction signal.
      - threshold: A quantile threshold (between 0 and 1). For instance, threshold=0.25 means that
                   days with an absolute prediction below the 25th percentile will be neutral.
      - avoid_shorts: Boolean flag. If True, only long positions (signal = 1) are allowed.
    """
    df = df.copy().dropna(subset=[prediction_col, 'delta_y', 'close'])
    
    # Compute the quantile threshold from the absolute prediction values.
    quantile_val = df[prediction_col].abs().quantile(threshold)
    
    # Initialize signals to 0.
    df['signal'] = 0
    if avoid_shorts:
        # Only long positions: signal = 1 when prediction is above the quantile threshold.
        df.loc[df[prediction_col] > quantile_val, 'signal'] = 1
    else:
        # Long positions where prediction > quantile threshold.
        df.loc[df[prediction_col] > quantile_val, 'signal'] = 1
        # Short positions where prediction < -quantile threshold.
        df.loc[df[prediction_col] < -quantile_val, 'signal'] = -1
    
    # Compute daily profit percentage. For a constant holding of 1 stock, the profit (%) is:
    # (delta_y / close) * 100, adjusted by the position signal.
    df['daily_profit_pct'] = df['signal'] * (df['delta_y'] / df['close']) * 100
    df['cumulative_profit_pct'] = df['daily_profit_pct'].cumsum()
    
    total_profit_pct = df['cumulative_profit_pct'].iloc[-1]
    daily_mean = df['daily_profit_pct'].mean()
    daily_std = df['daily_profit_pct'].std()
    sharpe_ratio = (daily_mean / daily_std) * np.sqrt(252) if daily_std != 0 else np.nan
    
    print(f"Performance for strategy '{prediction_col}':")
    print(f"  Total Profit (%): {total_profit_pct:.2f}%")
    print(f"  Annualized Sharpe Ratio: {sharpe_ratio:.2f}")
    
    plt.figure(figsize=(10, 6))
    plt.plot(df['date'], df['cumulative_profit_pct'], label=prediction_col)
    plt.xlabel("Date")
    plt.ylabel("Cumulative Profit (%)")
    plt.title(f"Cumulative Profit (%) for {prediction_col} Strategy")
    plt.legend()
    plt.grid(True)
    plt.show()
    
    return df[['date', 'open', 'high', 'low', 'close', 'volume', 'delta_y', prediction_col, 'signal', 'daily_profit_pct', 'cumulative_profit_pct']]

# ---------------------------------------
# Data Retrieval & Strategy Evaluation for a Given Ticker
# ---------------------------------------
def run_strategies_on_ticker(ticker, start_date="2020-01-01", end_date=None, threshold=0.0, avoid_shorts=False,num_weeks=4):
    """
    Downloads historical daily data for the given ticker using yfinance,
    flattens any MultiIndex columns, renames columns to a standard convention,
    computes delta_y as tomorrow's return (to avoid lookahead bias),
    applies the five strategies, and runs the performance analysis for each.
    
    Parameters:
      - ticker: string (e.g., "AAPL")
      - start_date: string (e.g., "2020-01-01")
      - end_date: string or None
      - threshold: Quantile threshold (between 0 and 1) for neutral signals.
      - avoid_shorts: Boolean; if True, short positions are avoided.
    """
    # Download data using yfinance.
    data = yf.download(ticker, start=start_date, end=end_date)
    data.reset_index(inplace=True)  # Ensure 'Date' becomes a column.
    
    # If columns are a MultiIndex (tuples), flatten them.
    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.get_level_values(0)
    
    # Rename columns to lower-case standard names.
    data = data.rename(columns={
        "Date": "date",
        "Open": "open",
        "High": "high",
        "Low": "low",
        "Close": "close",
        "Volume": "volume"
    })
    
    # Select only the required columns.
    data = data[['date', 'open', 'high', 'low', 'close', 'volume']]
    
    # Compute delta_y as tomorrow's close minus today's close (to avoid leakage).
    data["delta_y"] = data["close"].shift(-1) - data["close"]
    # Drop the last row which will have NaN for delta_y.
    data = data.dropna(subset=["delta_y"])
    
    print("Sample of downloaded data:")
    # print(data.head())
    
    # Apply each strategy.
    df_bb      = bollinger_bands_strategy(data)
    df_ema     = ema_crossover_strategy(data)
    df_vwap    = vwap_strategy(data)
    df_channel = high_low_channel_strategy(data)
    df_candle  = candlestick_body_strategy(data)
    df_weekday_avg = weekday_recent_avg_strategy(data,num_weeks)
    df_weekday_weighted = weekday_recent_weighted_avg_strategy(data,num_weeks)
    df_weekday_median = weekday_recent_median_strategy(data,num_weeks)
    df_meta_avg = meta_weekday_recent_avg_strategy(data, windows=[4, 8, 12, 16])
    df_meta_weighted = meta_weekday_recent_weighted_strategy(data, windows=[4, 8, 12, 16])
    df_meta_median = meta_weekday_recent_median_strategy(data, windows=[4, 8, 12, 16])
    
    
    # Run analysis for each strategy using the quantile threshold and avoid_shorts options.
    print("\nBollinger Bands Strategy Performance:")
    analyze_strategy(df_bb, 'prediction_bb', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nEMA Crossover Strategy Performance:")
    analyze_strategy(df_ema, 'prediction_ema', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nVWAP Strategy Performance:")
    analyze_strategy(df_vwap, 'prediction_vwap', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nHigh-Low Channel Strategy Performance:")
    analyze_strategy(df_channel, 'prediction_channel', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nCandlestick Body Strategy Performance:")
    analyze_strategy(df_candle, 'prediction_candle', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nWeekday Recent Average Strategy Performance:")
    analyze_strategy(df_weekday_avg, 'prediction_weekday_avg', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nWeekday Recent Weighted Average Strategy Performance:")
    analyze_strategy(df_weekday_weighted, 'prediction_weekday_weighted', threshold=threshold, avoid_shorts=avoid_shorts)
    
    # print("\nWeekday Recent Median Strategy Performance:")
    # analyze_strategy(df_weekday_median, 'prediction_weekday_median', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nMeta Weekday Recent Average Strategy Performance:")
    analyze_strategy(df_meta_avg, 'meta_prediction_weekday_avg', threshold=threshold, avoid_shorts=avoid_shorts)
    
    print("\nMeta Weekday Recent Weighted Average Strategy Performance:")
    analyze_strategy(df_meta_weighted, 'meta_prediction_weekday_weighted', threshold=threshold, avoid_shorts=avoid_shorts)
    
    # print("\nMeta Weekday Recent Median Strategy Performance:")
    # analyze_strategy(df_meta_median, 'meta_prediction_weekday_median', threshold=threshold, avoid_shorts=avoid_shorts)
    
    return data


#%%
# data = run_strategies_on_ticker("ROOT", start_date="2020-01-01",threshold=0.5)
data = run_strategies_on_ticker("^NDX", start_date="2020-01-01",threshold=0.4,
                                num_weeks=8)

