#%%
import requests
from bs4 import BeautifulSoup

url = "https://www.nasdaq.com/market-activity/stocks/screener?exchange=nasdaq"
response = requests.get(url)
html_content = response.content

soup = BeautifulSoup(html_content, "html.parser")

table = soup.find("table", {"class": "symbol-screener__table"})

for row in table.find_all("tr"):
    symbol = row.find_all("td")[0].text.strip()
    print(symbol)

#%%
1