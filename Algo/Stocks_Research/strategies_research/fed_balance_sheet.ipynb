#%%
import pandas as pd
import requests

# Define the URL of the FED balance sheet website
url = 'https://www.federalreserve.gov/releases/h41/current/'

# Send a GET request to the website and extract the HTML content
response = requests.get(url)
html_content = response.content


#%%
import pandas as pd
import requests

from bs4 import BeautifulSoup


def get_fed_balance(date='current',date_override=None):
    # Define the URL of the FED balance sheet website
    url = f'https://www.federalreserve.gov/releases/h41/{date}/'

    # Send a GET request to the website and extract the HTML content
    response = requests.get(url)
    html_content = response.content

    soup = BeautifulSoup(html_content)
    try:
        date = soup.find('span',{"style":"font-family:Arial; font-size:8pt; font-weight:bold"}).text
        date = pd.to_datetime([date])[0]
    except:
        if date_override is not None:
            date = date_override
        date = dtdt.strptime(date,'%Y%m%d')
    print(date)
    index = [i for i in range(10,20) if len(soup.find_all('table')[i].find_all('tr')) > 25][0]
    table = soup.find_all('table')[index]
    for body in table("tbody"):
        body.unwrap()
    df = pd.read_html(str(table), flavor="bs4")[0]
    df.columns = ['asset','eliminiation','current_balance','change_1w','change_1y']

    df['change_1w'] = pd.to_numeric(df['change_1w'].str.replace(' ','').str.replace('+','').str.replace(',',''))
    df['change_1y'] = pd.to_numeric(df['change_1y'].str.replace(' ','').str.replace('+','').str.replace(',',''))
    df['asset'] = df['asset'].replace({'Loans7':'Loans','Total assets': 'Total_Assets'})
    df = df.loc[df['asset'].isin(['Loans','Total_Assets'])]
    df['date'] = date
    return df


def get_fed_balance_old_format(date='current'):
    # Define the URL of the FED balance sheet website
    url = f'https://www.federalreserve.gov/releases/h41/{date}/'

    # Send a GET request to the website and extract the HTML content
    response = requests.get(url)
    html_content = response.content

    soup = BeautifulSoup(html_content)

    text = soup.find('pre').text

    line = [l for l in text.split('\n')
     if l.startswith('Total factors supplying reserve funds ')][0]

    line = line.replace('+ ','+').replace('+ ','+').replace('+ ','+').replace('+ ','+').replace(' +','+')
    line = line.replace('- ','-').replace('- ','-').replace('- ','-').replace('- ','-').replace(' -','-')
    parts = [x.replace(' ','').replace(',','') for x in line.split('  ') if x not in ['','\r']]
    try:
        parts = pd.to_numeric(parts[1:])
    except:
        print(f'Failed to convert to numeric: {parts}')
    df = pd.DataFrame(pd.Series({'asset': "Total_Assets", 'date':date,'current_balance': parts[0],'change_1w':parts[2],'change_1y':parts[3]}))
    df = df.T
    return df


# get_fed_balance_old_format('20200102')
#%%
from datetime import datetime as dtdt
from datetime import timedelta as td

BACKFILL_WEEKS = 3

thursdays_to_query = [x for x in [dtdt.now().replace(hour=0,minute=0,second=0,microsecond=0) + td(days=-7*i+(3-dtdt.now().weekday())) for i in range(BACKFILL_WEEKS)]
                      if x < dtdt.now()]
# thursdays_to_query.reverse()

stack = []
i = 0
for thursday in thursdays_to_query:
    i += 1
    print(f'Handling {thursday}')
    try:
        if i != 1:
            df = get_fed_balance(thursday.strftime('%Y%m%d'))
        else:
            df = get_fed_balance('current',date_override=thursday.strftime('%Y%m%d'))
        stack.append(df)
    except Exception as e:
        raise
        try:
            df = get_fed_balance_old_format(thursday.strftime('%Y%m%d'))
            stack.append(df)
        except:
            print('Skipping old format too')
            # raise


#%%
existing_df = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\Fed_balance_sheet_2020-2023.csv",parse_dates=['date'])
#%%
df = pd.concat(stack)
df['date'] = pd.to_datetime(df['date'])

for c in ['current_balance', 'change_1w', 'change_1y']:
    existing_df[c] = pd.to_numeric(existing_df[c],errors='coerce')

df = existing_df.merge(df,on=list(df),how='outer').sort_values('date')

df['current_balance'] = pd.to_numeric(df['current_balance'],errors='coerce')
df.query('asset == "Total_Assets"').set_index('date')["current_balance"].plot()


df.to_csv(r'C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\Fed_balance_sheet_2020-2023.csv',index=False)
#%%
