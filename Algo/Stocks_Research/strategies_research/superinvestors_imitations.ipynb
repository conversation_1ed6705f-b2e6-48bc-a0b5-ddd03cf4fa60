#%%
import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import re
import os
import time
from datetime import datetime as dtdt
from datetime import timedelta as td
#%%

#%%
# see also fund managers performance comparison here 
# https://www2.trustnet.com/Managers/ManagerPerf.aspx?MP_LeagueTable_sortedColumn=P60m,Name&MP_LeagueTable_sortedDirection=DESC 
#%%
superinvestors_portfolios_url = "https://www.dataroma.com/m/managers.php"
# result = requests.get(URL, headers = {'User-Agent': 'My User Agent 1.0'})
base_url = "https://www.dataroma.com"


source_html = requests.get(superinvestors_portfolios_url, headers = {'User-Agent': 'My User Agent 1.0'}).text
# print(source_html[:100])
soup = BeautifulSoup(source_html, 'lxml')
tables = soup.find_all('tbody')
table = tables[0]


stack = []
# parse the table
rows = table.find_all('tr')
for row in rows:
    cols = row.find_all('td')
    # take text of first cell
    manager_name = cols[0].find('a').text.strip()
    
    link = cols[0].find('a')['href']
    manager_code = link.split('m=')[1]
    stack.append({'manager_name': manager_name, 'link': link, 'manager_code': manager_code})
    #print(manager_name, link, manager_code)
#%%
final_stack = []


for manager in stack:
    print('Extracting data on manager: ' + manager['manager_name'])
    url = base_url + manager['link']
    source_html = requests.get(url, headers = {'User-Agent': 'My User Agent 1.0'}).text
    soup = BeautifulSoup(source_html, 'lxml')
    tables = soup.find_all('tbody')
    table = tables[0]
    rows = table.find_all('tr')
    for row in rows:
        cols = row.find_all('td')
        # take text of first cell
        stock_fullname = cols[1].find('a').text.strip().split(' - ')[-1]
        stock_ticker = cols[1].find('a').text.strip().split(' - ')[0]
        portfolio_share = cols[2].text.strip()
        recent_activity = cols[3].text.strip()
        num_shares = cols[4].text.strip()
        entry_price = cols[5].text.strip()
        current_price = cols[8].text.strip()
        pnl = cols[9].text.strip()
        final_stack.append({'manager_name': manager['manager_name'], 'manager_code': manager['manager_code'], 'stock_ticker': stock_ticker,'stock_fullname': stock_fullname,
                            'portfolio_share': portfolio_share, 'recent_activity': recent_activity, 'num_shares': num_shares, 'entry_price': entry_price, 'current_price': current_price, 'pnl': pnl,})
        

#%%
from Algo.Utils.files_handle import HOME

final_df = pd.DataFrame(final_stack)
DATE = '2023Q2'
final_df['date'] = DATE
final_df.to_csv(os.path.join(HOME,'Stocks_Research','super_investors',f'superinvestors_portfolio_{DATE}.csv'),index=False)
#%%
final_df['recent_activity_numeric'] = final_df['recent_activity'].str.replace('Reduce','-').str.replace('Add','').str.replace(' ','').str.replace('%','')
final_df['recent_activity_numeric'] = pd.to_numeric(final_df['recent_activity_numeric'],errors='coerce')

final_df['portfolio_share'] = pd.to_numeric(final_df['portfolio_share'],errors='coerce')

num_investors = final_df['manager_name'].nunique()

#%%
final_df['recent_activity_numeric_buckets'] = pd.qcut(final_df['recent_activity_numeric'],q=9,labels=[-1,-0.8,-0.4,-0.2,0,0.2,0.4,0.8,1])

MAX_MOMENTUM_EFFECT_ON_SHARE = 0.5
final_df['portfolio_share_adjusted'] = final_df['portfolio_share'].fillna(0) + final_df['portfolio_share'] * MAX_MOMENTUM_EFFECT_ON_SHARE * final_df['recent_activity_numeric_buckets'].fillna(0).astype(int)
#%%
num_chosen = 30
stocks_ranking = final_df.groupby(['stock_ticker','stock_fullname']).agg({'portfolio_share':'sum','portfolio_share_adjusted':'sum'}).sort_values('portfolio_share',ascending=False) #/ num_investors

top10_by_naive = [x[0] for x in stocks_ranking.sort_values('portfolio_share',ascending=False).index.tolist()[:num_chosen]]
top10_by_momentum = [x[0] for x in stocks_ranking.sort_values('portfolio_share_adjusted',ascending=False).index.tolist()[:num_chosen]]

print('Top 10 by naive approach:')
print(top10_by_naive)
print('Top 10 by momentum approach:')
print(top10_by_momentum)

stocks_ranking.sort_values('portfolio_share_adjusted',
                           ascending=False)[:num_chosen]
#%%
# Get historical Nasdaq 100 data from Yahoo Finance
from pandas_datareader import data as pdr
from datetime import datetime as dtdt
import pandas as pd
from datetime import timedelta as td

import yfinance as yfin
yfin.pdr_override()


TICKERS_LIST = ["^NDX","^GSPC"]+ top10_by_momentum
NUM_YEARS = 1
start_date = (dtdt.today() - td(days=365*NUM_YEARS)).strftime('%Y-%m-%d')
end_date = dtdt.today().strftime('%Y-%m-%d')

final_tickers_df = pd.DataFrame()
delta_ys_df = pd.DataFrame()

tickers_stack = []
for symbol in TICKERS_LIST:
    if symbol in ['BRK.A','BRK.B']:
        symbol = symbol.replace('.','-')
    print(symbol)
    ticker_df = pdr.get_data_yahoo(symbol, start=start_date, end=end_date)
    ticker_df['ticker'] = symbol
    
    # if final_tickers_df.empty:
    #     final_tickers_df = ticker_df
    # else:
    #     final_tickers_df = pd.concat([final_tickers_df,ticker_df],axis=0)

    last_month_change = ticker_df['Adj Close'].iloc[-1] / ticker_df['Adj Close'].iloc[-22] - 1
    last_2month_change = ticker_df['Adj Close'].iloc[-1] / ticker_df['Adj Close'].iloc[-43] - 1
    last_3month_change = ticker_df['Adj Close'].iloc[-1] / ticker_df['Adj Close'].iloc[-65] - 1
    ticker_df[f'y_{symbol}'] = ticker_df['Adj Close']/ticker_df['Adj Close'].shift(1) - 1
    if delta_ys_df.empty:
        delta_ys_df = ticker_df[[f'y_{symbol}']]
    else:
        delta_ys_df = delta_ys_df.merge(ticker_df[[f'y_{symbol}']],left_index=True,right_index=True,how='outer')
    
    tickers_stack.append({'ticker': symbol, 'last_month_change': last_month_change, 'last_2month_change': last_2month_change, 'last_3month_change': last_3month_change})
#%%
delta_ys_df['MEAN'] = delta_ys_df[list(delta_ys_df)[2:]].mean(axis=1)

last_day = -100
delta_ys_df[last_day:][list(delta_ys_df)[:2]+['MEAN']+list(delta_ys_df)[2:]].cumsum().plot(style=['-*']*3+['-']*7+['--']*7,
                                                                                        figsize=(10,10))
print(delta_ys_df[list(delta_ys_df)[:2]].sum())
for num_chosen in range(3,30):
    mean_return = delta_ys_df[list(delta_ys_df)[2:2+num_chosen]].mean(axis=1)
    print(f'for {num_chosen}: MEAN return = {mean_return[last_day:].sum()}')
#%%
period = 'last_3month_change'
performance_df = pd.DataFrame(tickers_stack)
average_3m_pnl_top15 = performance_df[2:].mean()[period]
nq_3m_pnl = performance_df[performance_df['ticker']=='^NDX'][period].values[0]
sp_3m_pnl = performance_df[performance_df['ticker']=='^GSPC'][period].values[0]
print(f'Average 3m pnl of top {num_chosen} stocks: {average_3m_pnl_top15}\nNasdaq 3m pnl: {nq_3m_pnl}\nS&P 3m pnl: {sp_3m_pnl}')
lift_vs_nq = average_3m_pnl_top15 / nq_3m_pnl
lift_vs_sp = average_3m_pnl_top15 / sp_3m_pnl
#%% md
# NUM_CHOSEN 15 
Average 3m pnl of top 15 stocks: 0.05174821147908857
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585

# NUM_CHOSEN 5
Average 3m pnl of top 5 stocks: 0.028163560103370112
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585

# NUM_CHOSEN 25
Average 3m pnl of top 25 stocks: 0.012941103124320898
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585

# NUM_CHOSEN 50
Average 3m pnl of top 50 stocks: 0.01409964466635853
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585

# 10 
Average 3m pnl of top 10 stocks: 0.024878563533903875
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585

# 18 
Average 3m pnl of top 18 stocks: 0.022383913877508058
Nasdaq 3m pnl: -0.015040877512965456
S&P 3m pnl: -0.024654069693507585