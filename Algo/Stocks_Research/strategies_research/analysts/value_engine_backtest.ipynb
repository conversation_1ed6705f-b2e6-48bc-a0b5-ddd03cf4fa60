#%%
import os
import time

import matplotlib.pyplot as plt
import pandas as pd 
from datetime import datetime as dtdt
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis

#%%
## This file was deleted as requested by VE
# csv = r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\MonthlyData_USA_202405.csv"
# see C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\drafts instead

df = pd.read_csv(csv,parse_dates=['Month'])
df.columns = [c.lower() for c in df.columns]

ranking_cols = [c for c in df.columns if 'ranking' in c]
rename_map = {c:c.replace(' ','').replace('_','').replace('ranking','_ranking') for c in ranking_cols}
df = df.rename(columns=rename_map)
# for all columns replace space with "_" 
df.columns = [c.replace(' ','_') for c in df.columns]


#%%

#%%
def get_kpis(profits,quantile,indicator,market_cap_bucket,conf_name_prefix=''):
        kpis = print_kpis(profits,indicator,skip_printing=True)
        kpis['quantile'] = quantile
        kpis['indicator'] = indicator
        kpis['market_cap_bucket'] = market_cap_bucket
        kpis['conf_name_prefix'] = conf_name_prefix
        kpis_last_6_years = print_kpis(profits[-6*12:],indicator,skip_printing=True)
        kpis['calmar_6Y'] = kpis_last_6_years['calmar']
        kpis['sharpe_6Y'] = kpis_last_6_years['sharpe']
        kpis_last_3_years = print_kpis(profits[-3*12:],indicator,skip_printing=True)
        kpis['calmar_3Y'] = kpis_last_3_years['calmar']
        kpis['sharpe_3Y'] = kpis_last_3_years['sharpe']
        kpis['pnl_6Y'] = kpis_last_6_years['pnl']
        kpis['pnl_3Y'] = kpis_last_3_years['pnl']
        return kpis

def analyze_quantile_conf(analyst_pivot,quantile,indicator,market_cap_bucket,
                          original_profits,kpis_stack=[],plot=False,conf_name_prefix='',
                          add_calmar_to_plot=False,num_of_months_to_plot="all",original_kpis=None,):
    
    # print(f'Handling {indicator} with quantile {quantile} and market_cap_bucket {market_cap_bucket}')
    conf_name = conf_name_prefix+f'{indicator}_q={quantile}_Mcap={market_cap_bucket}'
    print('conf_name:',conf_name)
    tickers_performance = tickers_with_diffs.copy().set_index('Date')
    tickers_performance = filter_tickers(analyst_pivot,tickers_performance,quantile)
    # tickers_performance.sum() - tickers_with_diffs.sum()
    tickers_performance_num_not_nans = tickers_performance.apply(lambda x: x.count(),axis=1)
    tickers_performance_num_zeros = tickers_performance.apply(lambda x: x[x==0].count(),axis=1)
    
    filtered_profits = ((tickers_performance.sort_values('Date').sum(axis=1))/(tickers_performance_num_not_nans - tickers_performance_num_zeros))
    filtered_profits = filtered_profits.fillna(0)
    plt.show()
    
    
    kpis = get_kpis(filtered_profits,quantile,indicator,market_cap_bucket,conf_name_prefix)
    kpis_stack.append(kpis)
    
    if plot:
        comparison_df = pd.DataFrame({'filtered_profits':filtered_profits,'original_profits':original_profits})
        comparison_df_to_plot = comparison_df.copy()
        if num_of_months_to_plot != "all":
            comparison_df_to_plot = comparison_df_to_plot[-num_of_months_to_plot:]
        comparison_df_to_plot.cumsum().plot(title=conf_name)
        if add_calmar_to_plot:
            calmar = round(kpis['calmar'],1)
            sharpe = round(kpis['sharpe'],1)
            calmar_6Y = round(kpis['calmar_6Y'],1)
            sharpe_6Y = round(kpis['sharpe_6Y'],1)
            calmar_3Y = round(kpis['calmar_3Y'],1)
            sharpe_3Y = round(kpis['sharpe_3Y'],1)
                                    
            # Add text for the overall calmar and sharpe, 6Y and 3Y metrics
            plt.text(comparison_df_to_plot.index[-1], comparison_df_to_plot['filtered_profits'].cumsum().iloc[-1], f'Calmar={calmar}, Sharpe={sharpe}', fontsize=12)
            plt.text(comparison_df_to_plot.index[-1], comparison_df_to_plot['filtered_profits'].cumsum().iloc[-1] * 0.9, f'Calmar_6Y={calmar_6Y}, Sharpe_6Y={sharpe_6Y}', fontsize=12)
            plt.text(comparison_df_to_plot.index[-1], comparison_df_to_plot['filtered_profits'].cumsum().iloc[-1] * 0.8, f'Calmar_3Y={calmar_3Y}, Sharpe_3Y={sharpe_3Y}', fontsize=12)
            # plot the original profits_kpis
            if original_kpis is not None:
                calmar = round(original_kpis['calmar'],1)
                sharpe = round(original_kpis['sharpe'],1)
                plt.text(comparison_df_to_plot.index[-1], comparison_df_to_plot['original_profits'].cumsum().iloc[-1] * 0.7, f'ORIGINAL Calmar={calmar}, Sharpe={sharpe}', fontsize=12)
    
        plt.show()
    return kpis_stack


def add_market_cap_buckets(df):
    df['market_cap'] = pd.to_numeric(df['market_cap'],errors='coerce')
    df['market_cap_q0.25'] = df.groupby(df['Date'].dt.year)['market_cap'].transform(lambda x: x.quantile(0.25))
    df['market_cap_q0.5'] = df.groupby(df['Date'].dt.year)['market_cap'].transform(lambda x: x.quantile(0.5))
    df['market_cap_q0.75'] = df.groupby(df['Date'].dt.year)['market_cap'].transform(lambda x: x.quantile(0.75))
    df['market_cap_q0.9'] = df.groupby(df['Date'].dt.year)['market_cap'].transform(lambda x: x.quantile(0.9))
    df['market_cap_q0.99'] = df.groupby(df['Date'].dt.year)['market_cap'].transform(lambda x: x.max())
    
    df['market_cap_bucket'] = None
    df.loc[df['market_cap'] < df['market_cap_q0.25'],'market_cap_bucket'] = 'B1'
    df.loc[(df['market_cap'] >= df['market_cap_q0.25']) & (df['market_cap'] < df['market_cap_q0.5']),'market_cap_bucket'] = 'B2'
    df.loc[(df['market_cap'] >= df['market_cap_q0.5']) & (df['market_cap'] < df['market_cap_q0.75']),'market_cap_bucket'] = 'B3'
    df.loc[(df['market_cap'] < df['market_cap_q0.5']),'market_cap_bucket'] = 'B12'
    
    df.loc[(df['market_cap'] >= df['market_cap_q0.75']) & (df['market_cap'] < df['market_cap_q0.9']),'market_cap_bucket'] = 'B4'
    df.loc[(df['market_cap'] >= df['market_cap_q0.9']) & (df['market_cap'] <= df['market_cap_q0.99']),'market_cap_bucket'] = 'B5'
    return df

def prepare_tickers_diffs(df,horizons_months=[1]):
    tickers_with_diffs = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\value_engine_backtest\tickers_historical_df.csv",parse_dates=['Date'])
    
    # create 1 month diffs 
    for c in list(tickers_with_diffs):
        if 'Date' in c:
            continue
        for horizon in horizons_months:
            tickers_with_diffs[c+f'_+{horizon}m'] = tickers_with_diffs[c].pct_change(horizon).shift(-horizon)    
    
    list_of_relevant_tickers = df['ticker'].unique().tolist()
    tickers_with_diffs = tickers_with_diffs[[c for c in tickers_with_diffs.columns if
                                             ( c.split('_')[0] in list_of_relevant_tickers and TARGET in c) 
                                             or c in ['Date']]]
    # remove tzinfo 
    tickers_with_diffs['Date'] = pd.to_datetime(tickers_with_diffs['Date'],utc=True)
    tickers_with_diffs['Date'] = pd.to_datetime(tickers_with_diffs['Date'].dt.tz_localize(None).dt.date)

    
    return tickers_with_diffs


def prepare_df(df,sector_name,other_conds_queries=None):
    df['sector_name'] = df['sector_name'].fillna('Unknown')
    query = 'country=="UNITED STATES" and sector_name==@sector_name'
    if other_conds_queries is not None:
        for other_cond in other_conds_queries:
            query += ' and ' + other_cond
    df = df.query(query)
    
    from datetime import timedelta as td
    df['Date'] = pd.to_datetime((df['month']+td(days=5)).dt.date)
    df['Date'] = df['Date'] - df['Date'].apply(lambda x: td(days=x.day-1))
    return df

def filter_out_bad_tickers(df,num_years = 6):
    df['month2'] = df['month']
    num_days = num_years*365
    
    period_by_ticker = df.groupby('ticker').agg({'month2':'max','month':'min'})
    period_by_ticker['length'] = period_by_ticker['month2'] - period_by_ticker['month']
    tickers_with_enough_history = period_by_ticker.query(f'length>"{num_days} days"').index
    df = df.query('ticker in @tickers_with_enough_history')
    return df

def filter_tickers(analyst_pivot,tickers_performance,quantile = 0.9):
    for month in analyst_pivot.index.tolist():
        # print('handling month:',month)
        
        ref_row = analyst_pivot.loc[month]
        ref_row_no_nans = ref_row.dropna()
        cols_that_pass = [x[1] for x in ref_row_no_nans[ref_row_no_nans > ref_row_no_nans.quantile(quantile)].index.tolist()]
        # cols_that_pass_in_tickers = [c for c in list(tickers_performance) if c.split('_')[0] in cols_that_pass]
        cols_that_dont_pass_in_tickers = [c for c in list(tickers_performance) if c.split('_')[0] not in cols_that_pass]
        # print('cols_that_pass',cols_that_pass)
        tickers_performance.loc[month,cols_that_dont_pass_in_tickers] = 0
    return tickers_performance

def prepare_analyst_pivot(df,indicator = 'one_month_forecast'): 
    
    df[indicator] = pd.to_numeric(df[indicator],errors='coerce')
    analyst_pivot = pd.pivot_table(df,index='Date',columns=['ticker'],values=[indicator])
    return analyst_pivot

#%% md
# filter to tickers with long enough history
#%%

TARGET = '+1m'


list_of_sectors = ['Computer and Technology',
 'Industrial Products',
 # 'Medical',
 'Transportation',
 # 'Consumer Discretionary',
 # 'Construction',
 'Retail-Wholesale',
 'Finance',
 # 'Business Services',
 # 'Consumer Staples',
 # 'Utilities',
 # 'Auto-Tires-Trucks',
 # 'Aerospace',
 # 'Basic Materials',
 # 'Oils-Energy',
 # 'Unknown',
 # 'Multi-Sector Conglomerates'
                   ]



#%%
from matplotlib import pyplot as plt


lst = ['forecast_ranking',
 'valuation_ranking',
 'e/p_ratio_rankign',
 'momentum_ranking',
 'size_ranking',
 'growth_ranking',
 'forwardp/e_ranking',
 'peg_ranking',
       'one_month_forecast',
           'one_month_forecast','one_year_forecast','two_year_forecast','three_year_forecast']


sector_name = 'Computer and Technology'
plot = True

kpis_stack = []
original_df = df.copy()

for sector_name in list_of_sectors:
    df = filter_out_bad_tickers(original_df)
    df = prepare_df(df,sector_name,other_conds_queries=None)
    df = add_market_cap_buckets(df)
    tickers_with_diffs = prepare_tickers_diffs(df,horizons_months=[1,3,6])
    
    # do the same to the original tickers_with_diffs 
    tickers_with_diffs_num_not_nans = (tickers_with_diffs.set_index('Date')).apply(lambda x: x.count(),axis=1)
    tickers_with_diffs_num_zeros = (tickers_with_diffs.set_index('Date')).apply(lambda x: x[x==0].count(),axis=1)

    conf_name_prefix = sector_name.lower().replace(' ','')+'_'
        
    
    for market_cap_bucket in ['B12','B1','B2','B3','B4','B5'][2:3]:
        # for indicator in lst[:1]: 
        for indicator in ['peg_ranking']: 
            df2 = df.query('market_cap_bucket == @market_cap_bucket').copy()
            original_profits = (((tickers_with_diffs.set_index('Date')).sort_values('Date').sum(axis=1))/(tickers_with_diffs_num_not_nans - tickers_with_diffs_num_zeros))
            original_profits = original_profits.fillna(0) 
            analyst_pivot = prepare_analyst_pivot(df2,indicator)
            # for quantile in [0.975,0.95,0.9,0.8,0.7,0.6,0.5][:1]+[0]:
            for quantile in [0.975,0.95,0.9,0.8,0.7,0.6,0.5][:1]:
                kpis_stack = analyze_quantile_conf(analyst_pivot,quantile,indicator,market_cap_bucket,original_profits,kpis_stack,conf_name_prefix=conf_name_prefix,plot=plot)
                
#%%
# raise
#%%
kpis_df = pd.DataFrame(kpis_stack)
kpis_df.to_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\value_engine_backtest\kpis_df.csv",index=False)


# kpis_df.sort_values('calmar',ascending=False)
kpis_df.groupby('market_cap_bucket').mean()

kpis_df.groupby('quantile').mean()

winning_confs = kpis_df.query('pnl > 4').sort_values('calmar',ascending=False)
winning_confs_indicators = winning_confs['indicator'].tolist()
winning_confs_quantiles = winning_confs['quantile'].tolist()
winning_confs_market_cap_buckets = winning_confs['market_cap_bucket'].tolist()

#%%
# raise
kpis_df['indicator'].unique()
#%%
# market_cap_bucket = 'B1'
# market_cap_bucket = 'B5'

quantile_override = None 
# quantile_override = 0.9

original_kpis = print_kpis(original_profits,'original_profits',skip_printing=True)

kpis_stack2 = []
for market_cap_bucket, indicator, quantile in zip(winning_confs_market_cap_buckets,winning_confs_indicators,winning_confs_quantiles):
    if quantile_override is not None:
        quantile = quantile_override
    df2 = df.query('market_cap_bucket == @market_cap_bucket').copy()
    analyst_pivot = prepare_analyst_pivot(df2,indicator)
    # for quantile in [0.975,0.95,0.9,0.8,0.7,0.6,0.5][3:4]:
    kpis_stack2 = analyze_quantile_conf(analyst_pivot,quantile,indicator,market_cap_bucket,original_profits,kpis_stack2,plot=True,add_calmar_to_plot=True,num_of_months_to_plot=6*12, original_kpis=original_kpis)
    
#%%

#sorted([x.lower() for x in list(full_kpis_df['sector'].unique().tolist())])
#%%
chosen_quantile = [0.975,0.95,0.9,0.8][:2]
# specific_indicator = None
specific_indicator = 'peg_ranking'
metric = 'calmar_6Y'
metric = 'sharpe'


cond = f'(quantile.isin({chosen_quantile}) or quantile == 0)'
if specific_indicator is not None:
    cond += f' and indicator == "{specific_indicator}"'

full_kpis_df = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\value_engine_backtest\kpis_df_v2.csv")

# kpis_df3 = kpis_df.copy()
# kpis_df3['indicator'] = specific_indicator
# full_kpis_df = pd.concat([full_kpis_df,kpis_df3])

full_kpis_df['sector'] = full_kpis_df['conf_name_prefix'].str.lower()

full_kpis_df2 = full_kpis_df.query(cond)

grouped_by_sector = full_kpis_df2.groupby(['sector','quantile']).mean().unstack()
grouped_by_bucket = full_kpis_df2.groupby(['market_cap_bucket','quantile']).mean().unstack()

grouped = full_kpis_df2.groupby(['sector','market_cap_bucket','quantile']).mean().unstack()

# order_by = grouped_by_sector.sort_values(metric,ascending=False).index.tolist()
grouped = grouped#.loc[order_by]
grouped[metric].plot(kind='bar',title=f'{metric} by sector and market cap bucket')
plt.show()
grouped_by_sector[metric].plot(kind='bar',title=f'{metric} by sector')
plt.show()
grouped_by_bucket[metric].plot(kind='bar',title=f'{metric} by market cap bucket')
plt.show()

grouped_by_predictor = full_kpis_df2.groupby(['indicator','quantile']).mean().unstack()
grouped_by_predictor[metric].plot(kind='bar',title=f'{metric} by predictor')
#%%
full_kpis_df.shape
full_kpis_df.query(cond).shape
cond
#%%
#grouped_by_sector['calmar']
full_kpis_df.groupby(['sector','quantile']).mean()['calmar'].unstack()
#%%
full_kpis_df.sort_values('calmar_3Y',ascending=False)
#%%

#%%
import pandas as pd 
import os 
#%%
csv = r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\MonthlyData_USA_202405.csv"

df = pd.read_csv(csv,parse_dates=['Month'])

outdir = r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\analysts\drafts"
all_tickers = df['Ticker'].unique()
n = len(all_tickers)
# print(list(df))
for i,ticker in  enumerate(all_tickers):
    if i % 10 == 0:
        print(f'handling {i}/{n}')
    df2 = df.query('Ticker == @ticker')
    df2.to_csv(os.path.join(outdir,f'historical_{ticker}.csv'),index=False)
    
