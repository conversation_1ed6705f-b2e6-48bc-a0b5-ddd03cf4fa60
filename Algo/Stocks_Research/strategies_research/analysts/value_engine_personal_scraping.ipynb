#%%
# we would like to scrape several lists of recommended stocks from the website
# use the screener tool 
# look by cap size, sector, industry, etc.
# go back to it 3/6/9/12 months later to see how the recommendations have performed


#%% md
# Filter 1 
#%%
# Your Screening Criteria:
# Market: US
# Engine Rating >= 4
# Expected gain of at least 20% in the next 1Y 
#%% md
# Filter 2 
#%% md

Your Screening Criteria:
Market: US
Engine Rating >= 2
Expected gain of at least 40% in the next 2Y
#%%
# Filter 3

# Your Screening Criteria:
# Market: US
# Engine Rating >= 2
# Expected gain of at least 10% in the next 6M
#%%

#%% md
# Filter 4
#%%
# Your Screening Criteria:
# Market: US
# Engine Rating >= 2
# Expected gain of at least 75% in the next 3Y