#%%
# using finchat images and running gpt4o on them based on the following instrucations

#%%
# Here is a summary of the updated instructions to interpret an EPS long-term revision trends chart and generate the corresponding dataframe with the revised "revision trend" metric:
# 
# ### Steps to Interpret the Chart and Generate the Dataframe:
# 
# 1. **Extract EPS Values:**
#    - Identify and extract the EPS estimates for each fiscal year (e.g., FY2022, FY2023, FY2024, FY2025) from the chart.
# 
# 2. **Calculate Revision Trend:**
#    - **Definition:** The "revision trend" score indicates the trend direction and magnitude in the preceding 2-3 quarters.
#    - **Scoring:**
#      - **-2:** Strongly bearish trend (significant downward revision).
#      - **-1:** Moderately bearish trend.
#      - **0:** Neutral or slightly changed trend.
#      - **1:** Moderately bullish trend.
#      - **2:** Strongly bullish trend (significant upward revision).
#    - **Calculation:**
#      - Look at the EPS estimates for the preceding 2-3 quarters.
#      - Compare the current quarter's EPS estimate with the average of the preceding quarters.
#      - Assign the appropriate score based on the above criteria.
# 
# 3. **Calculate Consistency Score:**
#    - **Definition:** The "consistency" score indicates how consistently the next year's EPS estimate is higher than the current year's (YoY expected growth).
#    - **Scoring:**
#      - **1 point** if FY2023 EPS is higher than FY2022.
#      - **1 point** if FY2024 EPS is higher than FY2023.
#      - **1 point** if FY2025 EPS is higher than FY2024.
#      - The score ranges from 1 to 3, reflecting the number of times the EPS estimates increase year-over-year.
# 
# 4. **Create the Dataframe:**
#    - Use pandas to create a dataframe with the following columns: "Date", "revision trend", and "consistency".
#    - Set the "Date" column as the index and sort the dataframe by date.
# 
# ### Example Dataframe Structure:
# 
# | Date       | revision trend | consistency |
# |------------|----------------|-------------|
# | 2020-07-01 | NaN            | 2           |
# | 2020-10-01 | NaN            | 2           |
# | 2021-01-01 | 1              | 2           |
# | 2021-04-01 | 1              | 2           |
# | 2024-05-01 | -2             | 3           |
# 
# ### Steps to Implement in Code:
# 
# ```python
# import pandas as pd
# import numpy as np
# 
# # Extract EPS values from the chart (example data)
# data = {
#     'Date': pd.date_range(start='2020-07-01', periods=16, freq='Q'),
#     'FY2022': [7, 7, 8, 8, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13, 13.5, 14, 14.5],
#     'FY2023': [8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13, 13.5, 14, 14.5, 15, 15.5],
#     'FY2024': [9, 9.5, 10.5, 11, 12, 12.5, 13.5, 14, 15, 15.5, 16, 16.5, 17, 17.5, 18, 18.5],
#     'FY2025': [None, None, None, None, None, None, None, None, 14, 14.5, 15, 15.5, 16, 16.5, 17, 17.5]
# }
# 
# # Create DataFrame
# df = pd.DataFrame(data)
# df['Date'] = pd.to_datetime(df['Date'])
# 
# # Calculate revision trend
# def calculate_revision_trend(row, df):
#     prev_two_quarters = df[(df['Date'] < row['Date']) & (df['Date'] >= row['Date'] - pd.DateOffset(months=6))]
#     if len(prev_two_quarters) < 2:
#         return None
#     avg_prev = prev_two_quarters[['FY2023', 'FY2024', 'FY2025']].mean()
#     trend = 0  # Default to neutral trend
#     if pd.notna(row['FY2024']) and row['FY2024'] < avg_prev['FY2024']:
#         trend = -1  # Moderately bearish trend
#         if row['FY2024'] < avg_prev['FY2024'] - (avg_prev['FY2024'] * 0.1):  # 10% threshold for strongly bearish
#             trend = -2  # Strongly bearish trend
#     elif pd.notna(row['FY2024']) and row['FY2024'] > avg_prev['FY2024']:
#         trend = 1  # Moderately bullish trend
#         if row['FY2024'] > avg_prev['FY2024'] + (avg_prev['FY2024'] * 0.1):  # 10% threshold for strongly bullish
#             trend = 2  # Strongly bullish trend
#     return trend
# 
# df['revision trend'] = df.apply(lambda row: calculate_revision_trend(row, df), axis=1)
# 
# # Calculate consistency score
# def calculate_consistency(row):
#     scores = []
#     if pd.notna(row['FY2022']) and pd.notna(row['FY2023']) and row['FY2023'] > row['FY2022']:
#         scores.append(1)
#     if pd.notna(row['FY2023']) and pd.notna(row['FY2024']) and row['FY2024'] > row['FY2023']:
#         scores.append(1)
#     if pd.notna(row['FY2024']) and pd.notna(row['FY2025']) and row['FY2025'] > row['FY2024']:
#         scores.append(1)
#     return len(scores)
# 
# df['consistency'] = df.apply(calculate_consistency, axis=1)
# 
# # Set the index to Date and keep the relevant columns
# df['Date'] = pd.to_datetime(df['Date'])
# df = df.set_index('Date')[['revision trend', 'consistency']]
# df.sort_index(inplace=True)
# 
# import ace_tools as tools; tools.display_dataframe_to_user(name="EPS Revision Analysis", dataframe=df)
# 
# df
# ```
# 
# ### Summary:
# 1. **Extract EPS values** from the chart for each fiscal year.
# 2. **Calculate "revision trend"** based on the change in EPS estimates compared to the previous 2-3 quarters.
# 3. **Calculate "consistency"** score reflecting YoY growth.
# 4. **Create and sort a pandas dataframe** with "Date", "revision trend", and "consistency" columns.