#%% md
Hammer reversal
#%%
# Calculate the range and body of each candle
candles_df['range'] = candles_df['high'] - candles_df['low']
candles_df['body'] = abs(candles_df['close'] - candles_df['open'])

# Identify hammer candlestick patterns
candles_df['hammer'] = ((candles_df['range'] > 3 * candles_df['body']) &
                        (candles_df['close'] > candles_df['open']) &
                        (candles_df['close'] == candles_df['high']) &
                        ((candles_df['close'] - candles_df['open']) / (.001 + candles_df['range']) > 0.6))

# Generate signals based on hammer patterns
candles_df['signal'] = 0
candles_df.loc[candles_df['hammer'], 'signal'] = 1
candles_df['positions'] = candles_df['signal'].diff()

# Plot the signals
import matplotlib.pyplot as plt

fig, ax = plt.subplots(figsize=(10, 6))
ax.plot(candles_df.index, candles_df['close'], label='Close')
ax.plot(candles_df[candles_df['signal'] == 1].index,
        candles_df['close'][candles_df['signal'] == 1],
        'o', markersize=8, color='green', label='Buy Signal')
ax.plot(candles_df[candles_df['positions'] == -1].index,
        candles_df['close'][candles_df['positions'] == -1],
        'o', markersize=8, color='red', label='Sell Signal')
plt.legend()
plt.show()

#%% md
Doji reversal
#%%
# Calculate the range and body of each candle
candles_df['range'] = candles_df['high'] - candles_df['low']
candles_df['body'] = abs(candles_df['close'] - candles_df['open'])

# Identify doji candlestick patterns
candles_df['doji'] = ((candles_df['range'] < 0.05 * candles_df['range'].rolling(5).mean()) &
                      (candles_df['body'] < 0.05 * candles_df['range'].rolling(5).mean()))

# Generate signals based on doji patterns
candles_df['signal'] = 0
candles_df.loc[candles_df['doji'], 'signal'] = 1
candles_df['positions'] = candles_df['signal'].diff()

# Plot the signals
import matplotlib.pyplot as plt

fig, ax = plt.subplots(figsize=(10, 6))
ax.plot(candles_df.index, candles_df['close'], label='Close')
ax.plot(candles_df[candles_df['signal'] == 1].index,
        candles_df['close'][candles_df['signal'] == 1],
        'o', markersize=8, color='green', label='Buy Signal')
ax.plot(candles_df[candles_df['positions'] == -1].index,
        candles_df['close'][candles_df['positions'] == -1],
        'o', markersize=8, color='red', label='Sell Signal')
plt.legend()
plt.show()

#%% md
WARNING !!! For all of the above we need to make sure shift is ok (no leakage)
#%%
import pandas as pd

def bullish_engulfing(candles_df):
    # Find bullish engulfing pattern
    for i in range(2, 4):
        if candles_df.iloc[-i]['Close'] < candles_df.iloc[-i]['Open'] and \
           candles_df.ilo   c[-i-1]['Close'] > candles_df.iloc[-i-1]['Open'] and \
           candles_df.iloc[-i-1]['Close'] > candles_df.iloc[-i]['Close'] and \
           candles_df.iloc[-i-1]['Open'] < candles_df.iloc[-i]['Open']:
            candles_df.loc[candles_df.index[-1], 'signal'] = "Buy"
            candles_df.loc[candles_df.index[-1], 'stop_loss'] = candles_df.iloc[-i]['Low']
            break
    else:
        candles_df.loc[candles_df.index[-1], 'signal'] = "Hold"
    return candles_df

def bearish_harami(candles_df):
    # Find bearish harami pattern
    for i in range(1, 3):
        if candles_df.iloc[-i]['Close'] > candles_df.iloc[-i]['Open'] and \
           candles_df.iloc[-i-1]['Close'] < candles_df.iloc[-i-1]['Open'] and \
           candles_df.iloc[-i-1]['Close'] < candles_df.iloc[-i]['Close'] and \
           candles_df.iloc[-i-1]['Open'] > candles_df.iloc[-i]['Open']:
            candles_df.loc[candles_df.index[-1], 'signal'] = "Sell"
            candles_df.loc[candles_df.index[-1], 'stop_loss'] = candles_df.iloc[-i]['High']
            break
    else:
        candles_df.loc[candles_df.index[-1], 'signal'] = "Hold"
    return candles_df

def three_white_soldiers(candles_df):
    # Find three white soldiers pattern
    for i in range(3, 6):
        if candles_df.iloc[-i]['Close'] > candles_df.iloc[-i-1]['Close'] and \
           candles_df.iloc[-i-1]['Close'] > candles_df.iloc[-i-2]['Close'] and \
           candles_df.iloc[-i-2]['Close'] > candles_df.iloc[-i-3]['Close']:
            candles_df.loc[candles_df.index[-1], 'signal'] = "Buy"
            candles_df.loc[candles_df.index[-1], 'stop_loss'] = candles_df.iloc[-3]['Low']
            break
    else:
        candles_df.loc[candles_df.index[-1], 'signal'] = "Hold"
    return candles_df

def three_black_crows(candles_df):
    # Find three black crows pattern
    for i in range(3, 6):
        if candles_df.iloc[-i]['Close'] < candles_df.iloc[-i-1]['Close'] and \
           candles_df.iloc[-i-1]['Close'] < candles_df.iloc[-i-2]['Close'] and \
           candles_df.iloc[-i-2]['Close'] < candles_df.iloc[-i-3]['Close']:
            candles_df.loc[candles_df.index[-1], 'signal'] = "Sell"
            candles_df.loc[candles_df.index[-1], 'stop_loss'] = candles_df.iloc[-3]['High']
            break
    else:
        candles_df.loc[candles_df.index[-1], 'signal'] = "Hold"
    return candles_df


def engulfing_pattern(candles_df):
    candles_df['signal'] = 'Hold'
    for i in range(1, len(candles_df)):
        if candles_df.loc[i, 'open'] > candles_df.loc[i, 'close']:
            if candles_df.loc[i-1, 'open'] < candles_df.loc[i-1, 'close'] and candles_df.loc[i, 'close'] > candles_df.loc[i-1, 'open']:
                candles_df.loc[i, 'signal'] = 'Buy'
            elif candles_df.loc[i-1, 'open'] > candles_df.loc[i-1, 'close'] and candles_df.loc[i, 'close'] < candles_df.loc[i-1, 'open']:
                candles_df.loc[i, 'signal'] = 'Sell'
    candles_df['stop_loss'] = candles_df['low'].shift()
    return candles_df


def piercing_line_pattern_strategy(candles_df):
    """
    Implements the Piercing Line Pattern Strategy.

    Parameters:
    candles_df (pd.DataFrame): a dataframe with columns ['open', 'high', 'low', 'close']

    Returns:
    pd.DataFrame: a copy of the input dataframe with an additional column 'signal' containing "Buy", "Sell", or "Hold" signals
    """
    signals = []
    for i in range(2, len(candles_df)):
        if candles_df.iloc[i-1]['close'] < candles_df.iloc[i-1]['open'] and candles_df.iloc[i]['open'] < candles_df.iloc[i-1]['low'] and candles_df.iloc[i]['close'] > candles_df.iloc[i-1]['open'] + (candles_df.iloc[i-1]['high'] - candles_df.iloc[i-1]['open'])/2:
            signals.append('Buy')
        elif candles_df.iloc[i-1]['close'] > candles_df.iloc[i-1]['open'] and candles_df.iloc[i]['open'] > candles_df.iloc[i-1]['high'] and candles_df.iloc[i]['close'] < candles_df.iloc[i-1]['open'] + (candles_df.iloc[i-1]['low'] - candles_df.iloc[i-1]['open'])/2:
            signals.append('Sell')
        else:
            signals.append('Hold')

    candles_df_with_signals = candles_df.copy()
    candles_df_with_signals['signal'] = signals
    return candles_df_with_signals



def inside_bar_strategy(candles_df):
    """
    Implements the Inside Bar Strategy.

    Parameters:
    candles_df (pd.DataFrame): a dataframe with columns ['open', 'high', 'low', 'close']

    Returns:
    pd.DataFrame: a copy of the input dataframe with an additional column 'signal' containing "Buy", "Sell", or "Hold" signals
    """
    signals = []
    for i in range(1, len(candles_df)):
        if candles_df.iloc[i]['high'] < candles_df.iloc[i-1]['high'] and candles_df.iloc[i]['low'] > candles_df.iloc[i-1]['low']:
            if candles_df.iloc[i]['close'] > candles_df.iloc[i-1]['close']:
                signals.append('Buy')
            elif candles_df.iloc[i]['close'] < candles_df.iloc[i-1]['close']:
                signals.append('Sell')
            else:
                signals.append('Hold')
        else:
            signals.append('Hold')

    candles_df_with_signals = candles_df.copy()
    candles_df_with_signals['signal'] = signals
    return candles_df_with_signals


def morning_evening_star_strategy(candles_df):
    candles_df['signal'] = 'Hold'

    for i in range(2, len(candles_df)):
        # Check for morning star pattern
        if candles_df['close'][i-2] > candles_df['open'][i-2] and \
           abs(candles_df['close'][i-1] - candles_df['open'][i-1]) / \
           min(candles_df['close'][i-1], candles_df['open'][i-1]) > 0.01 and \
           candles_df['open'][i] < candles_df['close'][i-1] and \
           candles_df['close'][i] > candles_df['open'][i-1] and \
           candles_df['close'][i] > candles_df['open'][i]:
            candles_df.at[i, 'signal'] = 'Buy'

        # Check for evening star pattern
        elif candles_df['close'][i-2] < candles_df['open'][i-2] and \
             abs(candles_df['close'][i-1] - candles_df['open'][i-1]) / \
             min(candles_df['close'][i-1], candles_df['open'][i-1]) > 0.01 and \
             candles_df['open'][i] > candles_df['close'][i-1] and \
             candles_df['close'][i] < candles_df['open'][i-1] and \
             candles_df['close'][i] < candles_df['open'][i]:
            candles_df.at[i, 'signal'] = 'Sell'

    return candles_df


def hammer_hanging_man_strategy(candles_df):
    # Calculate the difference between the open and close prices
    candles_df['oc_diff'] = candles_df['close'] - candles_df['open']

    # Determine the hammer or hanging man pattern
    candles_df['hammer'] = ((candles_df['high'] - candles_df[['open', 'close']].max(axis=1)) >= (candles_df['open'] - candles_df['close']) * 2) & (candles_df['oc_diff'] > 0)
    candles_df['hanging_man'] = ((candles_df[['open', 'close']].min(axis=1) - candles_df['low']) >= (candles_df['open'] - candles_df['close']) * 2) & (candles_df['oc_diff'] < 0)

    # Determine when to enter a trade
    candles_df['signal'] = 'Hold'
    candles_df.loc[candles_df['hammer'], 'signal'] = 'Buy'
    candles_df.loc[candles_df['hanging_man'], 'signal'] = 'Sell'

    # Drop intermediate columns
    candles_df = candles_df.drop(['oc_diff', 'hammer', 'hanging_man'], axis=1)

    return candles_df
#%%
