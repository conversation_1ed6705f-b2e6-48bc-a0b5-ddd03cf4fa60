import pandas as pd
import pandas_datareader.data as web
from datetime import datetime


def fetch_gdp_data():
    # Set the date range for data retrieval
    start = datetime(2000, 1, 1)  # adjust start date as needed
    end = datetime.now()  # current date to ensure all recent data is included

    # Fetch quarterly GDP data from FRED (U.S. GDP)
    gdp = web.DataReader('GDP', 'fred', start, end)

    # Resample the quarterly data to get it in a cleaner quarterly format if not already
    gdp = gdp.resample('Q').last()

    # Calculate rolling annual GDP
    gdp['rolling_annual_GDP'] = gdp['GDP'].rolling(window=4).sum()

    # Calculate last year's GDP by shifting the data by 4 quarters
    gdp['last_year_GDP'] = gdp['GDP'].shift(4)

    # Calculate last quarter's GDP
    gdp['last_quarter_GDP'] = gdp['GDP'].shift(1)

    # For current year forecasted GDP, you would normally need a forecasting model or an API that provides forecasts.
    # Here, we'll just demonstrate how to structure this in the DataFrame; this column will be filled with NaNs for now
    gdp['current_year_forecasted_GDP'] = pd.NA

    # Reset index to work with the date components
    gdp.reset_index(inplace=True)

    # Extract year and quarter
    gdp['year'] = gdp['DATE'].dt.year
    # gdp['quarter'] = gdp['DATE'].dt.quarter

    # Reorder and rename the DataFrame columns
    gdp = gdp[['DATE', 'year', 'rolling_annual_GDP', 'last_year_GDP', 'last_quarter_GDP',
               'current_year_forecasted_GDP']]
    gdp.rename(columns={'DATE': 'quarter'}, inplace=True)

    return gdp

