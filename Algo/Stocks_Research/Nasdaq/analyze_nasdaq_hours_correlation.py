

from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v4
from Algo.Performance_Analysis.clusters_auto_selection import check_drop_losing_hours,check_performance_by_hour,check_performance_by_mode
from Algo.Performance_Analysis.clusters_auto_selection import *


import copy

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT,DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR
from Algo.Learning.research_plays import rolling_calmar
from matplotlib import pyplot as plt

from Algo.Utils.kpis_calculation import calc_calmar, calc_sharpe
from Algo.Utils.calendar_handle import get_opex_week_monday
import requests
from bs4 import BeautifulSoup
import calendar
from datetime import datetime as dtdt

from datetime import timedelta as td
from Algo.Stocks_Research.Nasdaq.utils import *


fomc_dates_2021to2023 = [dtdt.strptime(x,'%Y-%m-%d %H:%M:%S') for x in [
    '2023-02-01 00:00:00', '2023-03-22 00:00:00', '2023-05-03 00:00:00', '2023-06-14 00:00:00', '2023-07-26 00:00:00', '2023-09-20 00:00:00', '2023-11-01 00:00:00', '2023-12-13 00:00:00', '2022-01-26 00:00:00', '2022-03-16 00:00:00', '2022-05-04 00:00:00', '2022-06-15 00:00:00', '2022-07-27 00:00:00', '2022-09-21 00:00:00', '2022-11-02 00:00:00', '2022-12-14 00:00:00', '2022-02-01 00:00:00', '2021-01-27 00:00:00', '2021-03-17 00:00:00', '2021-04-28 00:00:00', '2021-06-16 00:00:00', '2021-07-28 00:00:00', '2021-09-22 00:00:00', '2021-11-03 00:00:00', '2021-12-15 00:00:00'] + [
      "2024-12-08 00:00:00",
    "2024-01-31 00:00:00",
    "2024-03-20 00:00:00",
    "2024-05-01 00:00:00",
    "2024-06-12 00:00:00",
    "2024-07-31 00:00:00",
    "2024-09-18 00:00:00",
    "2024-11-07 00:00:00",
    "2024-12-18 00:00:00"
] + [
    '2025-01-29 00:00:00',  # January 28-29, 2025
    '2025-03-19 00:00:00',  # March 18-19, 2025
    '2025-05-07 00:00:00',  # May 6-7, 2025
    '2025-06-18 00:00:00',  # June 17-18, 2025
    '2025-07-30 00:00:00',  # July 29-30, 2025
    '2025-09-17 00:00:00',  # September 16-17, 2025
    '2025-10-29 00:00:00',  # October 28-29, 2025
    '2025-12-10 00:00:00'   # December 9-10, 2025
]
    ]

RETRIEVE_FOMCE_DATES_FROM_NEW = False
NASDAQ_CSV = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Nasdaq\NQ_frontMonth_tz_Live_15mins.csv"
BITCOIN_CSV = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\BitcoinMini\MBT_frontMonth_tz_Live_15mins.csv"


def converter(x):
    try:
        return dtdt.strptime(x, '%b. %d, %Y')
    except:
        return dtdt.strptime(x, '%b %d, %Y')


def get_pce_dates():
    # PCE dates
    # See https://fred.stlouisfed.org/releases/calendar?od=asc&rid=54&ve=2023-12-31&view=year&vs=2023-01-01  (currently only 2023 is updated)

    pce_dates_csv = r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\pce_dates.csv"
    pce_dates = pd.read_csv(pce_dates_csv, parse_dates=['date'])
    pce_dates_2024_list = pd.to_datetime(
        ['2023-01-26', '2024-02-29', '2024-03-29', '2024-04-26', '2024-05-31', '2024-06-28', '2024-07-26', '2024-08-30',
         '2024-09-27', '2024-10-31', '2024-11-27', '2024-12-20']+[
        '2025-01-31 00:00:00',  # December 2024 data
        '2025-02-28 00:00:00',  # January 2025 data
        '2025-03-28 00:00:00',  # February 2025 data
        '2025-04-30 00:00:00',  # March 2025 data
        '2025-05-30 00:00:00',  # April 2025 data
        '2025-06-27 00:00:00',  # May 2025 data
        '2025-07-31 00:00:00',  # June 2025 data
        '2025-08-29 00:00:00',  # July 2025 data
        '2025-09-26 00:00:00',  # August 2025 data
        '2025-10-31 00:00:00',  # September 2025 data
        '2025-11-26 00:00:00',  # October 2025 data
        '2025-12-19 00:00:00'  # November 2025 data
    ])

    pce_dates = pce_dates.merge(pd.DataFrame({'date': pce_dates_2024_list}), how='outer').sort_values(['date'])

    pce_dates['weekday'] = pce_dates['date'].dt.weekday
    pce_dates['week'] = pce_dates['date'] - pce_dates['weekday'].apply(lambda x: td(days=x))
    cpi_mondays = pce_dates['week'].tolist()
    return pce_dates, cpi_mondays

def get_cpi_dates():
    cpi_dates = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Stocks_Research\cpi_dates_2023.csv",
                             names=['month','date'])
    cpi_dates_2024 = pd.to_datetime(['2023-11-14', '2023-12-12',
                                     '2024-01-11', '2024-02-13', '2024-03-12', '2024-04-10', '2024-05-15', '2024-06-12', '2024-07-11', '2024-08-14', '2024-09-11', '2024-10-10', '2024-11-13', '2024-12-11',
                                    '2025-01-15 00:00:00',  # December 2024 data
                                    '2025-02-12 00:00:00',  # January 2025 data
                                    '2025-03-12 00:00:00',  # February 2025 data
                                    '2025-04-10 00:00:00',  # March 2025 data
                                    '2025-05-13 00:00:00',  # April 2025 data
                                    '2025-06-11 00:00:00',  # May 2025 data
                                    '2025-07-15 00:00:00',  # June 2025 data
                                    '2025-08-12 00:00:00',  # July 2025 data
                                    '2025-09-11 00:00:00',  # August 2025 data
                                    '2025-10-15 00:00:00',  # September 2025 data
                                    '2025-11-13 00:00:00',  # October 2025 data
                                    '2025-12-10 00:00:00'  # November 2025 data
                                ])
    cpi_dates['date'] = pd.to_datetime(cpi_dates['date'])
    cpi_dates = cpi_dates.merge(pd.DataFrame({'date':cpi_dates_2024}),on=['date'],how='outer').sort_values(['date'])


    try:
        cpi_dates['date2'] = cpi_dates['date'].apply(converter)
    except:
        cpi_dates['date2'] = cpi_dates['date']
    cpi_dates['weekday'] = cpi_dates['date2'].dt.weekday
    cpi_dates['week'] = cpi_dates['date2'] - cpi_dates['weekday'].apply(lambda x: td(days=x))
    cpi_mondays = cpi_dates['week'].tolist()

    return cpi_dates, cpi_mondays

def add_opex_week_features(df,base_on_mondays = True,
                           date_col='date'):
    if 'weekday' not in df.columns:
        df['weekday'] = df[date_col].dt.weekday
    df['week'] = (df[date_col] - df['weekday'].apply(lambda x: td(days=x))).dt.date
    df['is_opex_week'] = pd.to_datetime(df['week']) == df[date_col].dt.strftime('%m-%Y').apply(get_opex_week_monday)
    if base_on_mondays:
        df['is_opex_week_+1w'] = pd.to_datetime(df['week']) == (
            pd.to_datetime(df['week']).dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=7)))
        df['is_opex_week_-1w'] = pd.to_datetime(df['week']) == (
            pd.to_datetime(df['week']).dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=-7)))
        df['is_opex_week_-2w'] = pd.to_datetime(df['week']) == (
            pd.to_datetime(df['week']).dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=-14)))
        df['is_opex_week_+2w'] = pd.to_datetime(df['week']) == (
            pd.to_datetime(df['week']).dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=14)))
        df['is_opex_week_+3w'] = pd.to_datetime(df['week']) == (
            pd.to_datetime(df['week']).dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=21)))
    else:
        # This is the original method
        df['is_opex_week_+1w'] = pd.to_datetime(df['week']) == (
            df[date_col].dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=7)))
        df['is_opex_week_-1w'] = pd.to_datetime(df['week']) == (
            df[date_col].dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=-7)))
        df['is_opex_week_-2w'] = pd.to_datetime(df['week']) == (
            df[date_col].dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=-14)))
        df['is_opex_week_+2w'] = pd.to_datetime(df['week']) == (
            df[date_col].dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=14)))
        df['is_opex_week_+3w'] = pd.to_datetime(df['week']) == (
            df[date_col].dt.strftime('%m-%Y').apply(lambda x: get_opex_week_monday(x) + td(days=21)))
    df['is_opex_week_+-2w'] = df['is_opex_week_+2w'] | df['is_opex_week_-2w']
    df['is_opex_week_dummy'] = False
    df['is_not_opex_week_dummy'] = True
    df['which_opex_week'] = np.nan
    df.loc[df['is_opex_week'], 'which_opex_week'] = '0'
    df.loc[df['is_opex_week_-2w'], 'which_opex_week'] = '-2'
    df.loc[df['is_opex_week_+1w'], 'which_opex_week'] = '1'
    df.loc[df['is_opex_week_+2w'], 'which_opex_week'] = '2'
    df.loc[df['is_opex_week_+3w'], 'which_opex_week'] = df.loc[df['is_opex_week_+3w'], 'which_opex_week'].fillna(
        '3')
    df.loc[df['is_opex_week_-1w'], 'which_opex_week'] = df.loc[df['is_opex_week_-1w'], 'which_opex_week'].fillna(
        '-1')
    return df

def prepare_df(csv=NASDAQ_CSV,time_col='time_utc'):

    df = pd.read_csv(csv,parse_dates=['date','time_utc','time_chicago'])
    df['date'] = df[time_col]


    df['minute'] = df['date'].dt.minute
    df['15M'] = df['date'].dt.strftime('%H%M')
    df['30M'] = df['date'].dt.strftime('%H%M').str[-4:-2]+ df['date'].dt.strftime('%H%M').str[-2:].str.replace('15','00').str.replace('45','30')

    df['daily_minute'] = df['date'].dt.hour * 60 + df['date'].dt.minute
    df['hour'] = df['date'].dt.hour
    df['hour_+15'] = (df['date']-td(minutes=15)).dt.hour
    df['hour_+30'] = (df['date']-td(minutes=30)).dt.hour
    df['hour_+45'] = (df['date']-td(minutes=45)).dt.hour
    df['2H'] = df['date'].dt.hour//2 * 2
    df['4H'] = df['date'].dt.hour//4 * 4
    df['6H'] = df['date'].dt.hour//6 * 6

    df['weekday'] = df['date'].dt.weekday
    df['day'] = df['date'].dt.date

    df = add_opex_week_features(df)

    df['y'] = df['open'].diff().shift(-1)
    df.loc[df['date'].diff().dt.days > 1,'y'] = 0

    if RETRIEVE_FOMCE_DATES_FROM_NEW:
        # Get the HTML from the URL
        response = requests.get("https://marketnews.com/topics/fomc-meeting-calendar-market-news/")
        # Parse the HTML with BeautifulSoup
        soup = BeautifulSoup(response.content, "html.parser")

        # Find all the ul objects that contain dates
        date_uls = soup.find_all("ul")

        # Extract the dates from each ul object and store them in a list
        date_list = []
        for date_ul in date_uls:
            try:
                year = date_ul.find_all("h3")[0].text.split("schedule")[-1]
            except:
                try:
                    dates = date_ul.find_all("li")

                    for date in dates:
                        date_list.append(f"{year}"+date.text)
                except:
                    pass

        # Print the list of dates
        dates = date_list[8:-26]

        def parse_date(date_str):
            date_str = date_str.strip()
            # If the date range ends with an asterisk, remove it
            if date_str.endswith('*'):
                date_str = date_str[:-1]
            # Split the string into year and date range components
            year, date_range = date_str.split(':')
            # Split the date range into start and end dates
            start_date_str, end_date_str = date_range.split('-')
            # Parse the start and end dates
            start_date = dtdt.strptime(f"{year}:{start_date_str}", "%Y:%B %d")
            end_date = start_date + td(days=1)
            return end_date

        parsed_dates = []
        for date_str in dates:
            try:
                parsed_dates.append(parse_date(date_str))
            except:
                print(f'Failed on {date_str}')
                #raise

        fed_interest_rate_decision_dates = parsed_dates
    else:
        fed_interest_rate_decision_dates = fomc_dates_2021to2023
    #print(fed_interest_rate_decision_dates)
    df['is_fomc_week'] = pd.to_datetime(df['week']).isin([(x-td(days=2)).date() for x in fed_interest_rate_decision_dates])
    df['is_fomc_day'] = pd.to_datetime(df['day']).isin([x.date() for x in fed_interest_rate_decision_dates])

    df['is_fomc_and_opex_week'] = df['is_fomc_week']&df['is_opex_week']
    df['is_fomc_week_-1w'] = (pd.to_datetime(df['week'])+td(days=7)).isin([(x-td(days=2)).date() for x in fed_interest_rate_decision_dates])
    df['is_fomc_week_+1w'] = (pd.to_datetime(df['week'])-td(days=7)).isin([(x-td(days=2)).date() for x in fed_interest_rate_decision_dates])

    now = dtdt.now()
    last_monday = now - td(days=now.weekday())
    opex_week = get_opex_week_monday(now.strftime("%m-%Y"))
    next_opex_week = get_opex_week_monday((now+td(days=30)).strftime("%m-%Y"))

    days_since_this_month_opex = (last_monday-opex_week).days
    days_until_next_opex = (last_monday - next_opex_week).days

    if days_since_this_month_opex in [14]:
        ref_opex_col = 'is_opex_week_+2w'
    elif days_since_this_month_opex == -7:
        ref_opex_col = 'is_opex_week_-1w'
    elif days_since_this_month_opex == 0:
        ref_opex_col = 'is_opex_week'
    elif days_since_this_month_opex in [7]:
        ref_opex_col = 'is_opex_week_+1w'
    elif days_since_this_month_opex in [-14]:
        ref_opex_col = 'is_opex_week_-2w'
    else:
        raise ValueError(f'Unexpected days_since_this_month_opex = {days_since_this_month_opex}')
    print(f'ref_col = ',ref_opex_col)

    # if opex_week_reference is None:
    #     opex_week_reference = ref_opex_col

    pce_dates,cpi_mondays = get_pce_dates()
    df['is_pce_week'] = pd.to_datetime(df['week']).isin(cpi_mondays)
    df['is_pce_day'] = pd.to_datetime(df['day']).isin(pce_dates['date'].tolist())

    cpi_dates,cpi_mondays = get_cpi_dates()

    df['is_cpi_week'] = pd.to_datetime(df['week']).isin(cpi_mondays)
    df['is_cpi_week_-1w'] = (pd.to_datetime(df['week'])+td(days=7)).isin(cpi_mondays)
    df['is_cpi_week_+1w'] = (pd.to_datetime(df['week'])-td(days=7)).isin(cpi_mondays)
    df['is_cpi_day'] = pd.to_datetime(df['day']).isin(cpi_dates['date2'].tolist())

    return df,ref_opex_col


def add_delta_ys(pivot_opex_daily2,true_or_false_cond=False,
                 targets=["1000", "1100", "1200",
                          "1300", "1300", "1400", '1500', '1600'],
                 grouper='hour',max_periods=None
                 ):
    tuple_cols = [x for x in list(pivot_opex_daily2) if isinstance(x, tuple)
                  and x[2] == true_or_false_cond]

    targets = [t for t in targets if not t.endswith("30")]
    if grouper == 'hour':
        targets = [int(t[:2]) for t in targets]
    print(tuple_cols[:2])

    for hhmm in targets:
        columns_after_given_hhmm = [x for x in tuple_cols if int(x[1]) >= int(hhmm)]
        if max_periods is not None:
            columns_after_given_hhmm = columns_after_given_hhmm[:max_periods]
        # print(f'Creating y_{hhmm}_on. cols = {columns_after_given_hhmm[:10]}')
        pivot_opex_daily2[f'y_{hhmm}_on'] = pivot_opex_daily2[columns_after_given_hhmm].sum(axis=1)

    return pivot_opex_daily2, targets, tuple_cols


def get_pivot(df,opex_week_reference,
              weekdays_for_hourly = [2],grouper = 'hour',
              ):

    DROP_FALSE_DAYS = False

    hours_to_include = list(range(0,24))
    df_for_pivot = df[(df['date'].dt.weekday.isin(weekdays_for_hourly))&
                                            (df['date'].dt.hour.isin(hours_to_include))]
    if DROP_FALSE_DAYS:
        df_for_pivot = df_for_pivot[df_for_pivot[opex_week_reference]]

    pivot_opex_daily = pd.pivot_table(df_for_pivot,values=['y'],index=['day'],columns=[grouper,opex_week_reference],
                                aggfunc=np.sum).fillna(0)

    pivot_opex = pd.pivot_table(df[df['date'].dt.hour.isin(range(0,20))],values=['y'],index=['week'],columns=['weekday',opex_week_reference],
                                aggfunc=np.sum).fillna(0)
    return pivot_opex_daily

def calculate_profit(concated,target,
                     # weeks_back_for_calc = WEEKS_BACK_FOR_CALC,
                     quantile,num_top=5,
                     start_index=0,end_index=None,
                     plot=False,
                     ):
    if 'final_profit' not in concated.columns:
        concated['final_profit'] = 0
        concated['final_quantity'] = 0
    # concated_for_search = concated.loc[dtdt(2020,1,1).date():(dtdt.today()-td(days=1+7*weeks_back_for_calc)).date()].copy()
    concated_for_search = concated[start_index:end_index or concated.shape[0]+10]
    concated_for_search = concated_for_search[[x for x in list(concated_for_search)
                                               if 'profit' not in x[0]]]
    # print(concated.corr()[f'y_{target}_on'].sort_values(ascending=False)[:10])
    top3 = concated_for_search.corr()[f'y_{target}_on'].sort_values(ascending=False)[1:1+num_top].index.tolist()

    for top in top3:
        concated[f'profit_{target}_x_{top[1]}'] = np.sign(concated[top])*concated[f'y_{target}_on']
        concated[f'action_{target}_x_{top[1]}'] = np.sign(concated[top]).apply(lambda x: 'B' if x > 0 else 'S')
        cond = abs(concated[top]) < abs(concated[top].replace(0,np.nan)).quantile(quantile)

        # print('cond.sum()', cond.sum())
        concated.loc[cond,f'profit_{target}_x_{top[1]}'] = 0
        concated.loc[cond,f'action_{target}_x_{top[1]}'] = 'I'

        hit_rate = 0.5 + (concated[f'profit_{target}_x_{top[1]}'].replace(0,np.nan) > 0).mean() / 2
        pnl = concated[f'profit_{target}_x_{top[1]}'].sum()
        kpis = print_kpis(concated[f'profit_{target}_x_{top[1]}'][-WEEKS_FOR_KPIS:].replace(0,-1),f'profit_{target}_x_{top[1]}',
                          skip_printing=True)

        no_leakage_concated = concated[start_index:end_index or concated.shape[0]+10]
        hit_rate_no_leakage = 0.5 + (no_leakage_concated[f'profit_{target}_x_{top[1]}'].replace(0,np.nan) > 0).mean() / 2
        pnl_no_leakage = no_leakage_concated[f'profit_{target}_x_{top[1]}'].sum()
        kpis_no_leakage = print_kpis(no_leakage_concated[f'profit_{target}_x_{top[1]}'][-WEEKS_FOR_KPIS:].replace(0,-1),f'profit_{target}_x_{top[1]}',
                          skip_printing=True)

        calmar = kpis['calmar']
        calmar_no_leakage = kpis_no_leakage['calmar']
        inclusion_cond = calmar_no_leakage >= MINIMAL_CALMAR_TO_BE_PLOTTED and kpis_no_leakage['sharpe'] >= 1
        inclusion_cond2 = (no_leakage_concated[f'profit_{target}_x_{top[1]}'][-20:].replace(0,np.nan).dropna()[-3:] > 0).sum() == 3
        exclusion_cond1 = no_leakage_concated[f'profit_{target}_x_{top[1]}'][-20:].replace(0,np.nan).dropna()[-2:].min() < -30
        if (not (inclusion_cond or inclusion_cond2)) or exclusion_cond1:
            concated.drop(f'profit_{target}_x_{top[1]}' ,axis=1,inplace=True)
            concated.drop(f'action_{target}_x_{top[1]}' ,axis=1,inplace=True)
            continue
            # concated.loc[concated.index[end_index:], (f'profit_{target}_x_{top[1]}', '', '')] = 0
        print(f'TARGET = {target}, Pred = {top[1]} || Hit rate for {target} x {top[1]} = {hit_rate:.2f} || CALMAR: {round(calmar,2)} || PNL = {pnl:.2f} || ACTION = {concated[f"action_{target}_x_{top[1]}"].iloc[-1]}')

    profit_cols = [x for x in list(concated.columns) if 'profit' in x[0] and str(target) in x[0]
                   ]
    action_cols = [x for x in list(concated.columns) if 'action' in x[0] and str(target) in x[0]
                   ]
    print(f'We have #{len(profit_cols)} profit_cols')

    if end_index is not None:
        try:
            if len(profit_cols):
                concated.loc[concated.index[end_index:], ('final_profit','','')] = concated.loc[concated.index[end_index:], profit_cols].sum(axis=1)
                concated.loc[concated.index[end_index:], ('final_quantity','','')] = concated.loc[concated.index[end_index:], action_cols].replace({'S':-1,'I':0,'B':1}).sum(axis=1)
            else:
                concated.loc[concated.index[end_index:], 'final_profit'] = 0
                concated.loc[concated.index[end_index:], 'final_quantity'] = 0

        except:
            bb = 0
            raise

    if plot:
        if len(profit_cols):
            ax = concated[profit_cols].cumsum().plot(figsize=(10,10),style=['-']*len(profit_cols))
            # add the last action as text on the chart
            for i in range(len(profit_cols)):
                ax.text(concated.index[-1],concated[profit_cols[i]].sum(),action_cols[i][-1],fontsize=12)

            plt.show()
    print("======"*10)
    return concated

import argparse


DEFAULT_POSITION_HOURS = 3
MAX_TRAINING_WINDOW = 60
DEFAULT_QUANTILE = 0.4
EXTENDED_QUANTILES = [0.3,0.4,0.5]
WEEKS_FOR_KPIS = 40
WEEKS_BACK_FOR_CALC = 4
MINIMAL_CALMAR_TO_BE_PLOTTED = 2


def main(quantiles_list =EXTENDED_QUANTILES,
         position_lengths_list = [DEFAULT_POSITION_HOURS],
         max_training_window=MAX_TRAINING_WINDOW,
         weekdays_list=[0,1,2,3,4],
         which_week='all',
         include_is_cpi=False,
         base_on_existing=True,
         skip_existing_files=False,
         needed_weeks = 52*2):
    multiplier_by_grouper = {"30M": 2, "15M": 4, "2H": 1 / 2, "4H": 1 / 4, "hour": 1}

    targets_list = ["0900","1000", "1100", "1200",
                      "1300", "1300", "1400", '1500', '1600',
                     "1700","1800","1900","2000"]

    all_opex_weeks_to_process = ['is_opex_week_+-2w','is_opex_week_-1w',
                                'is_opex_week','is_opex_week_+1w',
                                 'is_cpi_week','is_fomc_week',
                                 'is_not_opex_week_dummy'][-1:]
    final_opex_weeks_to_process = all_opex_weeks_to_process if which_week == 'all' else (['last'] + ['is_cpi_week'] * include_is_cpi)

    df, opex_week_reference_last = prepare_df()
    for position_length in position_lengths_list:
        for opex_week_reference in final_opex_weeks_to_process:
            if opex_week_reference == 'last':
                opex_week_reference = opex_week_reference_last
            for weekday in weekdays_list:
                print(f'==== Handling weekday {weekday} ====')
                grouper = 'hour'
                TRUE_OR_FALSE_COND = True

                pivot_opex_daily = get_pivot(df,opex_week_reference,weekdays_for_hourly = [weekday],grouper = grouper)
                pivot_opex_daily2 = pivot_opex_daily.copy()

                max_periods = multiplier_by_grouper[grouper]*position_length

                pivot_opex_daily2, targets,tuple_cols = add_delta_ys(pivot_opex_daily2,TRUE_OR_FALSE_COND,
                                                                     targets=targets_list,
                                                                     grouper=grouper,
                                                                     max_periods=max_periods)

                for quantile in quantiles_list:
                    from Algo.Utils.files_handle import HOME
                    # if quantile == 0.4:
                    #     outdir = os.path.join(HOME,'Stocks_Research','Nasdaq','correlations_research')
                    # else:
                    outdir = os.path.join(HOME, 'Stocks_Research', 'Nasdaq', 'correlations_research','wide_scan_fixed')

                    if not os.path.exists(outdir):
                        os.makedirs(outdir)

                    # actions_stack = []
                    for target in targets:
                        outpath = os.path.join(outdir,f'correlations_predictions_weekday={weekday}_{opex_week_reference}_{target}_quantile={quantile}_position={position_length}H_window={max_training_window}.csv')
                        preceding_hhmms = [x for x in tuple_cols if int(x[1])<int(target)]
                        a = pivot_opex_daily2[[f'y_{target}_on']]
                        b = pivot_opex_daily2[preceding_hhmms]
                        concated = pd.concat([a,b],axis=1)

                        if skip_existing_files and os.path.exists(outpath):
                            print(f'Skipping {outpath}')
                            continue
                        if base_on_existing and os.path.exists(outpath):
                            try:
                                original_df = pd.read_csv(outpath,parse_dates=['day'])
                                last_week = original_df['day'].max()
                                previous_week = last_week - td(days=7)
                                needed_weeks = (dtdt.now() - last_week).days//7 + 1
                            except:
                                pass

                        for end_ind in sorted([-i for i in range(1,needed_weeks+1)]):
                            print(f'======== Handling Target {target} X END_index {end_ind} ========')
                            start_ind = min(0,end_ind - max_training_window)
                            concated = calculate_profit(concated,target,
                                                        quantile = quantile,num_top=5,
                                                        start_index=start_ind,end_index=end_ind,
                                                        plot=False)
                        concated = concated[-needed_weeks:]
                        for clip in [1,2,3,4,5,6]:
                            concated[f'final_profit_clip{clip}'] = concated['final_quantity'].clip(-clip,clip) * concated[f'y_{target}_on']
                        # turn the column names that are tuples to strings connected by "_"
                        concated.columns = ['_'.join([str(x) for x in col]) if isinstance(col, tuple) else col for col in concated.columns]
                        concated = concated.rename(columns={c: c.replace('__','') for c in list(concated)})
                        concated = concated.reset_index()
                        if base_on_existing and os.path.exists(outpath):
                            concated['day'] = pd.to_datetime(concated['day'])
                            original_df = original_df[~original_df['day'].isin(concated['day'])]
                            # assert list(original_df) == list(concated),f'columns not matching!, concated but not in original: {set(list(concated))-set(list(original_df))}, original but not in concated: {set(list(original_df))-set(list(concated))}'
                            common = [x for x in list(concated) if x in list(original_df)]
                            final_df = pd.concat([original_df[common],concated[common]])
                        else:
                            final_df = concated
                        final_df.to_csv(outpath,
                                        index=False)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Process some integers.')
    parser.add_argument('--which_week', type=str, default='all')
    parser.add_argument('--weekday',type=str, default='all')
    parser.add_argument('--include_is_cpi',type=bool, default=False)#,action='store_true')
    parser.add_argument('--base_on_existing',type=bool, default=True)#,action='store_true')

    args = parser.parse_args()

    weekdays_list = [0,1,2,3,4]
    if args.weekday != "all":
        if args.weekday == "today":
            weekdays_list = [dtdt.now().weekday()]
        else:
            raise AssertionError('Invalid weekday')

    for max_training_window in [15,30,45,60,75][1:2]:
        for position_length in [2,3,4][:1]: # 1
            main(weekdays_list=weekdays_list,which_week=args.which_week,
                 include_is_cpi=args.include_is_cpi,
                 base_on_existing=args.base_on_existing,
                 # base_on_existing=True,
                 skip_existing_files=True, # TODO
                 position_lengths_list=[position_length],
                 max_training_window=max_training_window,
                 )