from Stocks_Research.other_stocks.basic_candles_lgbm_on_growth_stocks import handle_ticker,enrich_dataset, generate_profit_col,get_data

from lightgbm import LGBMRegressor
from Algo.AI import slide_build


ticker_symbol = '^NDX'
interval = '1d'
resolution_override = '1w'

max_depth = 4
learning_rate = 0.01
max_window=300
step=7


algo = LGBMRegressor(max_depth=max_depth, #num_leaves=num_leaves,
                         learning_rate=learning_rate)

data = get_data(ticker_symbol,interval=interval,resolution_override=resolution_override)

data,fts = enrich_dataset(data)

target = 'y_pct'
time_col = 'Date'

pred_name = f'y_pred_{max_window}_step={step}_rate={learning_rate}_depth={max_depth}'
data = slide_build.general_slide_build(algo, data, target, fts, max_window=max_window,
                                                               step=step,
                                                               time_col=time_col, predict_proba=False,
                                                               pred_name=pred_name,
                                                               verbose=1,
                                       weekdays_for_train=[0,1,2,3,4,5,6,7])
aa = 0
# for ref_quantile in [0.2,0.3,0.4,0.5,0.6,]:
#     pred_name = f'y_pred_{max_window}_step={step}_nlvs={num_leaves}_rate={learning_rate}_depth={max_depth}'
#     data = generate_profit_col(data, ref_quantile,
#                            pred_col=pred_name,
#                            cost_per_trade_in_pct=0,
#                            target_col_for_profit=target)
#
