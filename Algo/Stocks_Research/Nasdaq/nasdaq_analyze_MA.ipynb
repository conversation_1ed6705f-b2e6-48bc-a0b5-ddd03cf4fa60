#%%
import yfinance as yf
from datetime import datetime as dtdt
from datetime import timedelta as td

# Ticker symbol for Bitcoin to USD
ticker_symbol = "NQ=F"

# Start and end dates for historical data
start_date = "2000-01-01"
end_date = "2025-12-31"

interval = '1d'
# interval = '1h'

resolution = '2H'
# resolution = '4H'
# resolution = '1H'
if interval == '1d':
    resolution = '1D'

if interval == '1h':
    start_date = (dtdt.now() - td(days=365*2-3)).strftime('%Y-%m-%d')
    
# Fetch historical data from Yahoo Finance
# data = yf.download(ticker_symbol, start=start_date, end=end_date,interval=interval)

# Display the first few rows of the data
#print(list(data))

#%%
#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
#import talib


# In[2]:


import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from pandas_datareader import data as pdr
import yfinance as yfin
from Algo.Stocks_Research.utils.general import analyze_profits, get_df_by_windows, update_json

COMPLETE_CANDLES_WITH_LOCAL = True 
NUM_YEARS = 33
NORMALIZE_PROFITS = True
PLOT = False
# Get historical yield curve data from FRED
start_date = (datetime.today() - timedelta(days=365*NUM_YEARS)).strftime('%Y-%m-%d')
end_date = datetime.today().strftime('%Y-%m-%d')

# resolution = '4H'

# Get historical Nasdaq 100 data from Yahoo Finance
# yfin.pdr_override()
# nasdaq100_original = pdr.get_data_yahoo("^NDX", start=start_date, end=end_date)


nq_historical_1h_csv = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Nasdaq\external_providers\NQ_1hour_continuous_adjusted.txt"
nq_historical_1h = pd.read_csv(nq_historical_1h_csv,parse_dates=['date'])


nq_candles = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Nasdaq\NQ_frontMonth_tz_Live_15mins.csv",parse_dates=['date','time_utc','time_chicago'])
nq_candles['time_eastern'] = nq_candles['time_chicago'] + td(hours=1)
nq_candles['date'] = nq_candles['time_eastern']
nq_candles_mini = nq_candles[list(nq_historical_1h)].set_index('date').resample('H').agg({'open': 'first', 'close': 'last','high':'max','low':'min','vol':'sum'}).reset_index()[list(nq_historical_1h)]
nq_candles_mini = nq_candles_mini.query('date >= @nq_historical_1h.date.max()')

if COMPLETE_CANDLES_WITH_LOCAL:
    print(f'before enrichment, shape was {nq_historical_1h.shape}')
    nq_historical_1h = nq_historical_1h.merge(nq_candles_mini,on=list(nq_historical_1h),how='outer').sort_values('date')
    print(f'after enrichment, shape was {nq_historical_1h.shape}')


nq_historical_1d = nq_historical_1h.set_index('date').resample('D').agg({'open': 'first', 'close': 'last','high':'max','low':'min'}).shift(-1).reset_index()
nq_historical_2h = nq_historical_1h.set_index('date').resample('2H').agg({'open': 'first', 'close': 'last','high':'max','low':'min'}).shift(-1).reset_index()
nq_historical_4h = nq_historical_1h.set_index('date').resample('4H').agg({'open': 'first', 'close': 'last','high':'max','low':'min'}).shift(-1).reset_index()
nq_historical_6h = nq_historical_1h.set_index('date').resample('6H').agg({'open': 'first', 'close': 'last','high':'max','low':'min'}).shift(-1).reset_index()

resolution_to_df_map = {'1D':nq_historical_1d,'2H':nq_historical_2h,'4H':nq_historical_4h,'6H':nq_historical_6h,'1H':nq_historical_1h}


#%%
# write data to csv
 # TODO - implement a symetric filter (equal chances of long short)
#%%
import numpy as np
import pandas as pd
from datetime import timedelta as td 

# resolution = '2H'

df = resolution_to_df_map[resolution]
df['y'] = df['open'].diff().shift(-1)
df['y_log'] = np.log(df['open']).diff().shift(-1)


data = df.copy()
list(data)
data.columns = [x.lower() for x in data.columns]
for c in ['open', 'high', 'low', 'close',]:
    data[c+'_log'] = np.log(data[c])
#%%
data['y'] = data['open'].diff().shift(-1)
data['log_y'] = data['open_log'].diff().shift(-1)


#%%
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector
from Algo.Stocks_Research.utils.general import analyze_profits, choose_winner,choose_winner2
from matplotlib import pyplot as plt
CALMAR_WINDOW = 15 #20


minimal_gap_for_signal = 30
minimal_gap_for_signal_pct = 0.005



for ma in [10,20,30,40,50,75,100,150,200,220,240,360,400,450,480]:
    data[f'MA{ma}'] = data['open'].rolling(ma,10).mean()
    data[f'MA{ma}_diff'] = data['open'] - data[f'MA{ma}']
    data[f'MA{ma}_diff_pct'] = data[f'MA{ma}_diff'] / data['open']
    
    data[f'MA{ma}_diff_sign'] = np.sign(data[f'MA{ma}_diff'])
    data.loc[abs(data[f'MA{ma}_diff'])<minimal_gap_for_signal,f'MA{ma}_diff_sign'] = 0
    data[f'MA{ma}_diff_pct_sign'] = np.sign(data[f'MA{ma}_diff_pct'])
    data.loc[abs(data[f'MA{ma}_diff_pct'])<minimal_gap_for_signal_pct,f'MA{ma}_diff_pct_sign'] = 0
    

    
    
#%%
data.set_index('date',inplace=True) 
#%%

cond_full_neg = (data['MA10_diff_sign'] == -1) & (data['MA20_diff_sign'] == -1) & (data['MA30_diff_sign'] == -1) & (data['MA40_diff_sign'] == -1) & (data['MA50_diff_sign'] == -1) & (data['MA75_diff_sign'] == -1) & (data['MA100_diff_sign'] == -1) & (data['MA150_diff_sign'] == -1) & (data['MA200_diff_sign'] == -1) & (data['MA240_diff_sign'] == -1) #& (data['MA360_diff_sign'] == -1)
cond_partial_neg = ((data['MA30_diff_sign'] == -1)&(data['MA40_diff_sign'] == -1)&(data['MA50_diff_sign'] == -1))&((data['MA100_diff_sign'] == -1)&(data['MA150_diff_sign'] == -1)&(data['MA200_diff_sign'] == -1))&(data['MA240_diff_sign'] == -1)

cond360 = (data['MA360_diff_sign'] <= 0)
# extract quarter-year (e.g. Q322)
data['quarter'] = data.index.to_period('M')
data['day'] = data.index.date
data['year'] = data.index.year

chosen_cond = cond360

for ma in [10,20,30,40,50,75,100,150,200,220,240,360,400,450,480]:
    
    chosen_cond = (data[f'MA{ma}_diff_sign'] <= 0)
    print(f'profits when cond {ma} is satisfied: {data.loc[chosen_cond].groupby("year").sum()["y"].sum()}')
    # data.loc[chosen_cond].groupby('quarter').sum()['y'].plot(kind='bar')
    # plt.show()
    # data.loc[chosen_cond].groupby('day').sum()['y'].cumsum().plot(kind='bar')
    # plt.show()
    # data.loc[chosen_cond].groupby('day').sum()['y'].rolling(10,10).sum().plot()
    
#%%
ma = 400 
chosen_cond = (data[f'MA{ma}_diff_sign'] <= 0)

# plot cumsum of y on all rows Vs on rows where condition is False
# groupy by week first 
data['week'] = data.index.to_period('W')
data['week'] = data['week'].dt.to_timestamp()

# add dAY 
data['day'] = data.index.date

data['y_filtered'] = data['y']
data.loc[chosen_cond,'y_filtered'] = 0

# take log on both 
data['y_log_filtered'] = data['y_log']
data['y_log_filtered_quantity'] = 1
data.loc[chosen_cond,'y_log_filtered'] = 0
data.loc[chosen_cond,'y_log_filtered_quantity'] = 0

# plot 
data.groupby('week').sum()[['y_log','y_log_filtered']].rolling(20,10).mean().plot()

data_weekly = data.groupby('week').sum()
data_daily = data.groupby('day').sum()
# data_weekly = choose_winner(data_weekly,'y_log_filtered','y_log','y_log_winner',window=20)
# data_weekly[['y_log','y_log_filtered','y_log_winner']].cumsum().plot()
# chosen_data = data_daily

final_resolution = 'daily'

i = 0
for window in [40,60,80,120,180,240,360]:
    print(f'window = {window}')
    
    chosen_data = {'daily':data_daily,'weekly':data_weekly}[final_resolution].copy()
    if i == 0:
        print_kpis(chosen_data['y_log'],'y_log')
        # last 4 years kpis
        print_kpis(chosen_data['y_log'].tail((365 if final_resolution == 'daily' else 52)*4),'y_log_last_4_years')
    i +=1
    chosen_data = choose_winner(chosen_data,'y_log_filtered','y_log','y_log_winner',window=window)
    chosen_data[['y_log','y_log_filtered','y_log_winner']] = chosen_data[['y_log','y_log_filtered','y_log_winner']]*100
    (chosen_data[['y_log','y_log_filtered','y_log_winner']]).cumsum().plot(title='winner window = '+str(window))
    for c in ['y_log','y_log_filtered','y_log_winner'][-1:]:
        print_kpis(chosen_data[c],c+'_W'+str(window))
        # last 4 years kpis
        print_kpis(chosen_data[c].tail((365 if final_resolution == 'daily' else 52)*4),c+'_W'+str(window)+'_last_4_years')
    plt.show()

#%%
chosen_data['is_using_y_log_filtered'][-100:].mean()
chosen_data['quantity_change_abs'] = abs(chosen_data['y_log_filtered_quantity'].diff())
chosen_data = chosen_data.reset_index()
chosen_data.groupby(pd.to_datetime(chosen_data['day']).dt.to_period('M')).sum()['quantity_change_abs'][-50:].plot(kind='bar')
plt.show()
#%%

#%% md
# Complex conditions (2 dimensional) 
#%%

cond_full_neg = (data['MA10_diff_sign'] == -1) & (data['MA20_diff_sign'] == -1) & (data['MA30_diff_sign'] == -1) & (data['MA40_diff_sign'] == -1) & (data['MA50_diff_sign'] == -1) & (data['MA75_diff_sign'] == -1) & (data['MA100_diff_sign'] == -1) & (data['MA150_diff_sign'] == -1) & (data['MA200_diff_sign'] == -1) & (data['MA240_diff_sign'] == -1) #& (data['MA360_diff_sign'] == -1)
cond_partial_neg = ((data['MA30_diff_sign'] == -1)&(data['MA40_diff_sign'] == -1)&(data['MA50_diff_sign'] == -1))&((data['MA100_diff_sign'] == -1)&(data['MA150_diff_sign'] == -1)&(data['MA200_diff_sign'] == -1))&(data['MA240_diff_sign'] == -1)

cond360 = (data['MA360_diff_sign'] <= 0)
# extract quarter-year (e.g. Q322)
data['quarter'] = data.index.to_period('M')
data['day'] = data.index.date
data['year'] = data.index.year

chosen_cond = cond360

double_cond_stack = []

for ma in [10,20,30,40,50,75,100,150,200,240,360,400,450,480]:
    for other_ma in [10,20,30,40,50,75,100,150,200,240,360,400,450,480]:
        if ma == other_ma:
            continue
        chosen_cond = (data[f'MA{ma}_diff_sign'] <= 0) & (data[f'MA{other_ma}_diff_sign'] <= 0)
        groups = data.groupby([f'MA{ma}_diff_sign',f'MA{other_ma}_diff_sign']).sum()['y_log']
        minimal_subset= groups.sort_values().index[0]
        second_lowest_subset = groups.sort_values().index[1]
        print(f'For conditions: {ma} and {other_ma}, Worst subset was = {minimal_subset}, with profit = {groups.min()}')
        # print(f'profits when cond {ma} and {other_ma} are satisfied: {data.loc[chosen_cond].groupby("year").sum()["y"].sum()}')
        
        double_cond_stack.append({'ma':ma,'other_ma':other_ma,'minimal_subset':minimal_subset,'minimal_profit':groups.min(),
                                  'second_lowest_subset':second_lowest_subset,'second_lowest_profit':groups.loc[second_lowest_subset]})
        # data.loc[chosen_cond].groupby('quarter').sum()['y'].plot(kind='bar')
        # plt.show()
        # data.loc[chosen_cond].groupby('day').sum()['y'].cumsum().plot(kind='bar')
        # plt.show()
        # data.loc[chosen_cond].groupby('day').sum()['y'].rolling(10,10).sum().plot()
    
    # chosen_cond = (data[f'MA{ma}_diff_sign'] <= 0)
    # print(f'profits when cond {ma} is satisfied: {data.loc[chosen_cond].groupby("year").sum()["y"].sum()}')
    # data.loc[chosen_cond].groupby('quarter').sum()['y'].plot(kind='bar')
    # plt.show()
    # data.loc[chosen_cond].groupby('day').sum()['y'].cumsum().plot(kind='bar')
    # plt.show()
    # data.loc[chosen_cond].groupby('day').sum()['y'].rolling(10,10).sum().plot()
results = pd.DataFrame(double_cond_stack)
# results.sort_values('minimal_profit',ascending=True)
results['sum_2_lowest'] = results['minimal_profit'] + results['second_lowest_profit']
results.sort_values('sum_2_lowest',ascending=True)
results.query('ma == 20').sort_values('sum_2_lowest',ascending=True)
#%%
#### OFFFICIAL CONF 
winning_ma = 100
winning_other_ma = 50

minimal_subset1 = (-1,1)
minimal_subset2 = (0,-1)

#### TEST CONF 
winning_ma = 20
winning_other_ma = 30
# 
minimal_subset1 = (-1,-1)
minimal_subset2 = (-1,-1)


cond_to_filter1 = (data[f'MA{winning_ma}_diff_sign'] == minimal_subset1[0]) & (data[f'MA{winning_other_ma}_diff_sign'] == minimal_subset1[1])
cond_to_filter2 = (data[f'MA{winning_ma}_diff_sign'] == minimal_subset2[0]) & (data[f'MA{winning_other_ma}_diff_sign'] == minimal_subset2[1])

data['y_log_filtered_double'] = data['y_log'].copy()
data.loc[cond_to_filter1,'y_log_filtered_double'] = 0
data.loc[cond_to_filter2,'y_log_filtered_double'] = 0

data['y_log_filtered_double_quantity'] = 1
data.loc[cond_to_filter1,'y_log_filtered_double_quantity'] = 0
data.loc[cond_to_filter2,'y_log_filtered_double_quantity'] = 0

print(data['y_log_filtered_double'].sum())

daily_data_double = data.groupby('day').sum()
print(daily_data_double['y_log_filtered_double'].sum())
# merge with chosen_data from above 
chosen_data2 = daily_data_double.merge(chosen_data,on='day',suffixes=('_1d','_2d'))
print(chosen_data2['y_log_filtered_double'].sum())
chosen_data2['y_log_filtered_double'] *= 100
chosen_data2['y_log_1d'] *= 100

# drop duplicated cols 
chosen_data2 = chosen_data2.loc[:,~chosen_data2.columns.duplicated()]
chosen_data2 = choose_winner(chosen_data2,'y_log_winner','y_log_filtered_double','y_log_filtered_double_winner',window=20)

chosen_data2[['y_log_winner','y_log_filtered_double','y_log_filtered_double_winner']].cumsum().plot()

#%%
# daily_data_double = data.groupby('day').agg({'y_log':'sum','y_log_filtered_double':'sum','y_log_filtered_double_quantity':'sum'})
# daily_data_double['y_log_filtered_double'].sum()
stack3 = []
# kpis_stack_final = []
for window in [#30,40,50,60,70,80,90,100,
                120,180,
               240,
                # 300,
               # 360,
               # 400,450,480,500,600,700,
               #  800,
                   900,#1000
               ][-2:]:
    print(f'window = {window}') # todo todo todo check it's equal to prod
    chosen_data2 = choose_winner(chosen_data2,'y_log_winner','y_log_filtered_double','y_log_filtered_double_winner',window=window,
                                 calmar_value_for_cond4=2)
    
    kpis = print_kpis(chosen_data2['y_log_filtered_double_winner'],'y_log_filtered_double_winner_W'+str(window))
    kpis['winner_window'] = window
    stack3.append(kpis)
    
    def analyze_results(first_ind_to_plot_weeks=0,weekly_or_daily='daily',
                        plot=True):        
        results = pd.DataFrame(stack3)
        results.sort_values('calmar',ascending=False)
        
        weekly = chosen_data2.groupby(pd.to_datetime(chosen_data2['day']).dt.to_period('W')).sum()[['y_log_1d','y_log_winner','y_log_filtered_double','y_log_filtered_double_winner']]
        # monthly aggrregation 
        monthly = chosen_data2.groupby(pd.to_datetime(chosen_data2['day']).dt.to_period('M')).sum()[['y_log_1d','y_log_winner','y_log_filtered_double','y_log_filtered_double_winner']]
        
        if plot:
            if weekly_or_daily == 'daily':
                chosen_data2.set_index('day')[['y_log_1d','y_log_winner','y_log_filtered_double','y_log_filtered_double_winner']][first_ind_to_plot_weeks*5:].cumsum().plot(figsize=(20,12))
            elif weekly_or_daily == 'weekly':
                weekly[first_ind_to_plot_weeks:].cumsum().plot(figsize=(20,12))
            
            monthly[first_ind_to_plot_weeks//4:].cumsum().plot(figsize=(20,12))
        
        print_kpis(monthly['y_log_filtered_double_winner'],'y_log_filtered_double_winner_monthly')
        kpis_10y_monthly_double_winner = print_kpis(monthly['y_log_filtered_double_winner'][-12*10:], 'y_log_filtered_double_winner_last_10_years_monthly')
        kpis_5y_monthly_double_winner = print_kpis(monthly['y_log_filtered_double_winner'][-12*5:], 'y_log_filtered_double_winner_last_5_years_monthly')
        kpis_2y_weekly_double_winner = print_kpis(weekly['y_log_filtered_double_winner'][-52*2:], 'y_log_filtered_double_winner_last_2_years_weekly')
        print_kpis(monthly['y_log_filtered_double'],'y_log_filtered_double_monthly')
        print_kpis(monthly['y_log_filtered_double'][-12*5:], 'y_log_filtered_double_last_5_years_monthly')
        print_kpis(monthly['y_log_1d'],'y_log_monthly')
        
        final_kpis_dict = {'calmar_5Y_monthly_double_winner':kpis_5y_monthly_double_winner['calmar'],
                           'sharpe_5Y_monthly_double_winner':kpis_5y_monthly_double_winner['sharpe'],
                           'calmar_2Y_weekly_double_winner':kpis_2y_weekly_double_winner['calmar'],
                            'sharpe_2Y_weekly_double_winner':kpis_2y_weekly_double_winner['sharpe'],
                            'calmar_10Y_monthly_double_winner':kpis_10y_monthly_double_winner['calmar'],
                            'sharpe_10Y_monthly_double_winner':kpis_10y_monthly_double_winner['sharpe'],
                           }
        return final_kpis_dict
    
    kpis = analyze_results(first_ind_to_plot_weeks=-15,weekly_or_daily='daily',
                           plot=True)
    kpis['window'] = window
    kpis_stack_final.append(kpis)
    
    
#%%
kpis_df = pd.DataFrame(kpis_stack_final)
#kpis_df.set_index('window').plot(kind='bar',figsize=(12,8))
kpis_df['combined_metric_calmar'] = 0.4*kpis_df['calmar_5Y_monthly_double_winner'] + 0.3 * kpis_df['calmar_2Y_weekly_double_winner'] + 0.3 * kpis_df['calmar_10Y_monthly_double_winner']
kpis_df['combined_metric_calmarxsharpe'] = 0.4*(kpis_df['calmar_5Y_monthly_double_winner']*kpis_df['sharpe_5Y_monthly_double_winner']) + 0.3 * (kpis_df['calmar_2Y_weekly_double_winner']*kpis_df['sharpe_2Y_weekly_double_winner']) + 0.3 * (kpis_df['calmar_10Y_monthly_double_winner']*kpis_df['sharpe_10Y_monthly_double_winner'])

kpis_df.set_index('window')[['combined_metric_calmar','combined_metric_calmarxsharpe']].plot(kind='bar',figsize=(12,8))
# add text on each bar with the value 
for i in range(kpis_df.shape[0]):
    plt.text(i-0.2,kpis_df['combined_metric_calmar'].iloc[i]+0.1,str(round(kpis_df['combined_metric_calmar'].iloc[i],2)))
    plt.text(i+0.1,kpis_df['combined_metric_calmarxsharpe'].iloc[i]+0.1,str(round(kpis_df['combined_metric_calmarxsharpe'].iloc[i],2)))

#%%
# To wrap it up 
## We first research filter strategies based one a single MA column
## We reached the y_log_winner 
### based on: 2H candles x MA400  x winner window = 42 (data[f'MA400_diff_sign'] <= 0)
    ### This was cool as it skipped the big drops 2008, 2020, 202, but we missed some serious PNL 
## then we researched a double filter strategy based on 2 MA columns
    # We reached the y_log_filtered_double_winner
    # winning_ma = 100, winning_other_ma = 50
    # found 2 subsets that are better to be out of market 
        # minimal_subset1 = (-1,1), minimal_subset2 = (0,-1)
    # this yielded y_log_filtered_double
## Lastly we ran winner choosing between y_log_filtered_double and y_log_winner
    # winner window = 900 days 
     
# THE RESULTS ARE ABSOLUTELY AMAZING
## FULL PERIOD 
### profit for window y_log_filtered_double_winner_monthly = 219.12 | calmar, max_dd = (15.3, -14.32) | sharpe = 4.64 | {0} Losses 1000$ 
### profit for window y_log_filtered_double_monthly = 250.78 | calmar, max_dd = (7.08, -35.42) | sharpe = 4.6 | {0} Losses 1000$ 
### profit for window y_log_winner_monthly = 161.41 | calmar, max_dd = (9.02, -17.9) | sharpe = 3.7 | {0} Losses 1000$ 

## LAST 5 YEARS
### profit for window y_log_filtered_double_winner_last_5_years_monthly = 106.74 | calmar, max_dd = (7.45, -14.32) | sharpe = 5.58 | {0} Losses 1000$ 
### profit for window y_log_filtered_double_last_5_years_monthly = 114.75 | calmar, max_dd = (5.64, -20.34) | sharpe = 5.44 | {0} Losses 1000$ 
### profit for window y_log_winner_last_5_years_monthly = 87.69 | calmar, max_dd = (5.78, -15.17) | sharpe = 5.06 | {0} Losses 1000$
#%%
#### NOW - PRODUCTIONIZE
#%%
