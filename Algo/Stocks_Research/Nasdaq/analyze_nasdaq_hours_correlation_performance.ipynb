#%% md
# Evaluate the slide build 
#%%

#%%
import os 
import pandas as pd 
from datetime import datetime as dtdt
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis
from Algo.Utils.files_handle import HOME,PROJECT_ROOT
outdir = os.path.join(HOME,'Stocks_Research','Nasdaq','correlations_research',
                      'wide_scan_fixed')
# outdir = os.path.join(HOME,'Stocks_Research','Nasdaq','correlations_research','0.66')
from matplotlib import pyplot as plt 
import numpy as np 

SKIP_MERGE = True
# SKIP_MERGE = False

final_dfs_by_weekday = {}
chosen_profit = 'final_profit_clip2'

kpis_stack = []
for weekday in [0,1,2,3,4]:
    final_df = pd.DataFrame()
    for opex_week in ['is_opex_week_+-2w','is_opex_week_-1w','is_opex_week','is_opex_week_+1w','is_cpi_week',#'is_fomc_week',
                            # 'is_cpi_week'
                        'is_not_opex_week_dummy'
                      ][-1:]:
        for h in [
                    # 6,7,8,9,
                  # 9,10,11,
                    12,13,14,15,16,
                  17,18,19,
                    # 20
                  ]:
            for quantile in [0.3,0.4,0.5]:
                for position in ['2H','3H','4H']:#[:1]:
                    for window in [15,30,45,60,75][1:2]:#[1:2]:
                        conf_name = f'weekday={weekday}_{opex_week}_{h}_quantile={quantile}_position={position}_window={window}'
                        f = f'correlations_predictions_{conf_name}.csv'
                        print(conf_name)
                        ff = os.path.join(outdir,f)
                        if not os.path.exists(ff):
                            print(f'File {f} did not exist...Skipping')
                            continue
                        df = pd.read_csv(ff,parse_dates=['day'])
                        # final_profit_cols = [x for x in list(df) if 'final_profit' in x]+['final_quantity']
                        delta_y_col = [x for x in list(df) if 'y_' in x and x.endswith('on')][0]
                        df['final_profit_0x1'] = df['final_quantity'].clip(0,1)*df[delta_y_col]
                        df['final_profit_0.25x1'] = df['final_quantity'].clip(-0.25,1)*df[delta_y_col]
                        df['final_profit_0.5x1'] = df['final_quantity'].clip(-0.5,1)*df[delta_y_col]
                        df['final_profit_0.75x1'] = df['final_quantity'].clip(-0.75,1)*df[delta_y_col]
                        df['final_quantity_strict'] = np.floor(df['final_quantity']//3)
                        df['final_profit_0.75x1_strict'] = df['final_quantity_strict'].clip(-0.75,1)*df[delta_y_col]
                        COST_PER_TRADE = 2 # 2$
                        df['final_profit_0.75x1_with_friction'] = df['final_profit_0.75x1'] - df['final_quantity'].clip(-1,1).abs() * 2*COST_PER_TRADE
                        df['final_profit_0.75x1_strict_with_friction'] = df['final_profit_0.75x1_strict'] - df['final_quantity_strict'].clip(-1,1).abs() * 2*COST_PER_TRADE
                        final_profit_cols = [chosen_profit,'final_profit_0x1',
                                             # 'final_profit_0.25x1','final_profit_0.5x1',
                                             'final_profit_0.75x1','final_profit_0.75x1_with_friction','final_profit_0.75x1_strict',
                                                'final_profit_0.75x1_strict_with_friction',
                                             'final_quantity','final_quantity_strict']
                        # conf_name = f'{opex_week}_{weekday}_{h}_q={quantile}_len={position}_W={window}'
                        tmp_df = df[['day']+final_profit_cols].rename(columns={c: c.replace('final_profit',f'final_profit_{conf_name}').replace('final_quantity',f'final_quantity_{conf_name}') for c in final_profit_cols})
                        kpis = {'profit':df['final_profit_0.75x1_strict_with_friction'].sum(),
                                'weekday':weekday,
                                'opex_week':opex_week,
                                'hour':h,
                                'quantile':quantile,
                                'position':position,
                                'window':window}
                        kpis_stack.append(kpis)
                        if not SKIP_MERGE:
                            if final_df.empty:
                                final_df = tmp_df
                            else:
                                final_df = final_df.merge(tmp_df,on=['day'],how='outer')
                                print('final_df.shape = ',final_df.shape)
                        # df.set_index('day')['final_profit'].cumsum().plot()
                        # plt.show()
                
    final_dfs_by_weekday[weekday] = final_df
#%%
#[x for x in list(final_=dfs_by_weekday[0]) if 'x1' not in x]
raise
#%%
nasdaq_candles = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\Nasdaq\NQ_frontMonth_tz_Live_15mins.csv",
                             parse_dates=['time_utc'])
nasdaq_candles = nasdaq_candles[~((nasdaq_candles['time_utc'].dt.weekday == 4)&(nasdaq_candles['time_utc'].dt.hour > 20))]

nasdaq_candles['y'] = nasdaq_candles['open'].diff().shift(-1)
nasdaq_candles['day'] = nasdaq_candles['time_utc'].dt.date
nasdaq_candles = nasdaq_candles.groupby('day').agg({'y':'sum'})#.reset_index()


#%%
quantity_cols = [x for x in list(final_dfs_by_weekday[0]) if 'quantity' in x and 'strict' not in x]
strict_quantity_cols = [x for x in list(final_dfs_by_weekday[0]) if 'quantity' in x and 'strict' in x]
final_dfs_by_weekday[0][quantity_cols].sum(axis=1).abs().sum(),final_dfs_by_weekday[0][strict_quantity_cols].abs().sum(axis=1).sum()
#%%

#%%
cond_on_cols = lambda x: 'x1' not in x or x == 'day'
cond_on_cols = lambda x: ('0.75x1' in x and 'cpi' not in x and 'friction' in x and 'strict' in x) or x == 'day'

full_df = pd.DataFrame()
# for weekday in [0,1,2,3,4]:
for weekday in [1,2,3,4]:
    final_df = final_dfs_by_weekday[weekday]
    final_df = final_df[[x for x in list(final_df) if cond_on_cols(x)]]
    final_df = final_df.rename(columns={c:c.replace(f'weekday={weekday}_','') for c in list(final_df)})
    if full_df.empty:
        full_df = final_df
    else:
        common = [x for x in list(full_df) if x in list(final_df)]
        full_df = full_df.merge(final_df,on=common,how='outer')
full_df = full_df.sort_values('day')
full_df = full_df.set_index('day').fillna(0)
full_df.cumsum().plot(figsize=(16,8),style=['-']*7+['--']*7+['-*']*7)

base_cols = []
for c in list(full_df):
    num_underscored_before_quantile = len(c[:c.find('quantile')].split('_'))-2
    base_col = '_'.join(c.split('_')[num_underscored_before_quantile:])
    if base_col not in base_cols:
        base_cols.append(base_col)
    
for base_col in base_cols:
    ref_cols = [c for c in list(full_df) if base_col in c]
    full_df[base_col] = full_df[ref_cols].sum(axis=1)

full_df[base_cols].cumsum().plot(figsize=(16,8),style=['-']*7+['--']*7+['-*']*7)
plt.tight_layout()

from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector

full_df['profit_all'] = full_df[base_cols].sum(axis=1)
filtered_df = full_df[base_cols]
for c in base_cols:
    performance_cond1 = filtered_df[c].rolling(12,1).mean().shift(1)>-10
    performance_cond2 = filtered_df[c].rolling(40,1).apply(lambda x: calc_calmar(x,False)[0]).shift(1)>3
    filtered_df.loc[~performance_cond1,c] *= 0
    filtered_df.loc[performance_cond2,c] *= 1.5
full_df['profit_all_filtered'] = filtered_df.sum(axis=1)

print_kpis(full_df['profit_all'],'profit_all')
print_kpis(full_df['profit_all_filtered'],'profit_all_filtered')
ax = full_df[['profit_all','profit_all_filtered']].cumsum().plot(figsize=(16,8))
nasdaq_candles['naive_long'] = nasdaq_candles['y']
nasdaq_candles['naive_long_2contracts'] = 2*nasdaq_candles['y']
print_kpis(nasdaq_candles['naive_long'],'naive_long')
(nasdaq_candles.merge(full_df,left_index=True,right_index=True,how='inner')[['naive_long','naive_long_2contracts']].cumsum()).plot(ax=ax)
    
#%%
# kpis_df = pd.DataFrame(kpis_stack)fprint_kpis
kpis_df['position'].value_counts()
#%%
kpis_df = pd.DataFrame(kpis_stack)
kpis_df.groupby('hour').mean()['profit'].plot(kind='bar',title='Hourly Performance')
plt.show()
kpis_df2 = kpis_df
kpis_df2 = kpis_df[(kpis_df['hour'].isin(range(12,20)))#&
                   (kpis_df['quantile']==0.4)#&
                   # (kpis_df['weekday'].isin([1,2,3,4]))&
                   #   (kpis_df['position']=='2H')
                    ]
kpis_df2.groupby('position').mean()['profit'].plot(kind='bar',title='Position Performance')
plt.show()

kpis_df2.groupby('weekday').mean()['profit'].plot(kind='bar',title='Weekday Performance')
plt.show()
kpis_df2.groupby('opex_week').mean()['profit'].plot(kind='bar',title='Opex Performance')
plt.show()
kpis_df2.groupby('quantile').mean()['profit'].plot(kind='bar',title='Quantile Performance')
plt.show()
kpis_df2.groupby('window').mean()['profit'].plot(kind='bar',title='Window Performance')
plt.show()

#%%
# Winning conf =  window30 x q=0.4 x 2H x 0.75x1 x [12 ---> 19]
# TODO try to build averages across different confs (ensemble /  voting) 
# unfornately, once we consider friction almost all strategies lose their edge
# not good enough
# 5/12/24
#%%
from Algo.Stocks_Research.utils.general import generate_candles_df

# def extract_hourly_positions_from_weekday_opex_df(ref_df,position_length=3,
#                                                   hours=[10,11,12,13,14,15,16,17,18,19]):
position_length=3
hours=[10,11,12,13,14,15,16,17,18,19]
    # generate
    
dfs_stack = {}
for weekday in [0,1,2,3,4]:
    final_candles_df = pd.DataFrame()
    ref_df = final_dfs_by_weekday[weekday]
    for hour in hours:
        hour_cols = [x for x in list(ref_df) if x.split('_')[-1] == str(hour) and 'final_quantity' in x]
        combined_quantity_col = f'combined_quantity_hour={hour}'
        ref_df[combined_quantity_col] = ref_df[hour_cols].fillna(0).sum(axis=1)
        # print(ref_df.set_index('day')[combined_quantity_col].iloc[-1:])
        # print(f'Weekday={weekday}|| min,max = {ref_df["day"].min()},{ref_df["day"].max()}')
        candles_df = generate_candles_df(ref_df['day'].min(),ref_df['day'].max(),'H',
                                         allowed_hours=range(min(hours),max(hours)+position_length))
        candles_df = candles_df[candles_df.index.weekday.isin([weekday])]
        candles_df['day'] = pd.to_datetime(candles_df.index.date)
        candles_df = candles_df.reset_index().rename(columns={'index':'datetime'})
        candles_df = candles_df.merge(ref_df[['day',f'combined_quantity_hour={hour}']],on=['day'])
        candles_df['hour'] = candles_df['datetime'].dt.hour
        candles_df.loc[~candles_df['hour'].isin(range(hour,hour+position_length)),combined_quantity_col] = 0
        if final_candles_df.empty:
            final_candles_df = candles_df
        else:
            common = [x for x in list(candles_df) if x in list(final_candles_df)]
            print(final_candles_df.shape[0])
            final_candles_df = final_candles_df.merge(candles_df,on=common,how='outer')
    dfs_stack[weekday] = final_candles_df



#%%
ref_df = final_dfs_by_weekday[weekday]

#%%

#%%

stack = []

this_weeks_opex = 'is_opex_week_-1w'
this_weeks_opex = 'is_opex_week'
this_weeks_opex = 'is_cpi_week'
todays_weekday = dtdt.now().weekday()
todays_weekday = 1

ONLY_TODAYS_WEEKDAY = True 
ONLY_THIS_WEEKS_OPEX = True 

opex_week_references = ['is_opex_week_+-2w','is_opex_week_-1w','is_opex_week','is_opex_week_+1w',
                            'is_cpi_week']

CHOSEN_CLIP = None
CHOSEN_CLIP = 2

clip_cond = (lambda x: 'clip' not in x) if CHOSEN_CLIP is None else (lambda x: f'clip{CHOSEN_CLIP}' in x)
for weekday in [0,1,2,3,4] if not ONLY_TODAYS_WEEKDAY else [todays_weekday]:
    final_df = final_dfs_by_weekday[weekday]
    df_to_plot = final_df.set_index('day').loc['2023-04-01':]
    
    # print([x for x in list(df_to_plot)])
    individual_cols_to_plot = [x for x in list(df_to_plot) if clip_cond(x)]
    # Full Plot 
    # try:
    #     df_to_plot[individual_cols_to_plot].cumsum().plot(style=['-']*7+['--']*7+['-*']*7,
    #                                         figsize=(16,8))
    #     plt.show()
    # except:
    #     pass
    grouped_by_opex = df_to_plot.reset_index()
    
    # Plot by Opex 
    cols_groups_list = []
    
    for opex_week_reference in opex_week_references:
        opex_cols = [x for x in individual_cols_to_plot if "_".join(x.split('_')[2:(-2 if CHOSEN_CLIP is None else -3)]) == opex_week_reference]
        # print(opex_cols)
        c = f'profit_{opex_week_reference}'
        df_to_plot[c] = df_to_plot[opex_cols].sum(axis=1)
        cols_groups_list.append(c)
    df_to_plot[cols_groups_list].cumsum().plot(figsize=(16,8))
    plt.show()
    
    # Plot by Clip 
    cols_groups_list = []
    for clip in [None,1,2,3,4,5,6]:
        cond = (lambda x: f'clip{clip}' in x) if clip else (lambda x: 'clip' not in x)
        cols = [x for x in list(df_to_plot) if cond(x) and 'profit' in x]
        c = f'profit_clip={clip or "None"}'
        df_to_plot[c] = df_to_plot[cols].sum(axis=1)
        cols_groups_list.append(c)
    df_to_plot[cols_groups_list].cumsum().plot(figsize=(16,8),title=f'Weekday= {weekday} ||BY CLIP')
    plt.show()
    
    # plot by hour - only this opex  
    cols_groups_list = []
    for h in hours:
        cond = lambda x: x.split('_')[-1] == str(h)
        cols = [x for x in list(df_to_plot) if cond(x) and 'profit' in x and 
                (this_weeks_opex in x or not ONLY_THIS_WEEKS_OPEX)
                 ]
        c = f'profit_hour={h}'
        df_to_plot[c] = df_to_plot[cols].sum(axis=1)
        cols_groups_list.append(c)
    df_to_plot['profit_all_hours_mean'] = df_to_plot[[x for x in list(df_to_plot) if 'profit' in x and (this_weeks_opex in x or not ONLY_THIS_WEEKS_OPEX)]].mean(axis=1)
    df_to_plot[cols_groups_list+['profit_all_hours_mean']].cumsum().plot(figsize=(16,8),title=f'Weekday= {weekday} BY HOUR'+"" if not ONLY_THIS_WEEKS_OPEX else f' || {this_weeks_opex}',
                                               style=['-']*7+['-*']*7)
    plt.show()
    
    # plot by Hour all weeks
    cols_groups_list = []
    for h in hours:
        cond = lambda x: x.split('_')[-1] == str(h)
        cols = [x for x in list(df_to_plot) if cond(x) and 'profit' in x and 'is_opex' in x]
        
        c = f'profit_hour={h}'
        df_to_plot[c] = df_to_plot[cols].sum(axis=1)
        cols_groups_list.append(c)
    df_to_plot['profit_all_hours_mean'] = df_to_plot[[x for x in list(df_to_plot) if 'profit' in x and (this_weeks_opex in x or not ONLY_THIS_WEEKS_OPEX)]].mean(axis=1)
    df_to_plot[cols_groups_list+['profit_all_hours_mean']].cumsum().plot(figsize=(16,8),title=f'Weekday= {weekday} BY HOUR (ALL WEEKS)',
                                               style=['-']*7+['-*']*7)
    plt.show()
    
    for c in list(df_to_plot):
        kpis = print_kpis(df_to_plot[c],c,skip_printing=True)
        kpis_6m = print_kpis(df_to_plot[c][-26:],c,skip_printing=True)
        for k in ['calmar','sharpe','pnl']:
            kpis[k+'_6M'] = kpis_6m[k]
        kpis['weekday'] = weekday
        kpis['name'] = c
        hour = c.split('_')[-1]
        opex = c.replace('final_profit_','').replace(f'_{weekday}_{hour}','')
        kpis['hour'] = hour
        kpis['opex'] = opex
        
        stack.append(kpis)
    
    try:
        df_to_plot[individual_cols_to_plot].sum(axis=1).cumsum().plot(title=f'TOTAL PROFIT DAY = {weekday}',figsize=(16,8))
    except:
        pass
    plt.show()
#%% md
# PLOT TODAYS POSITIONS 
#%%
#ref_df[['day',f'combined_quantity_hour={hour}']]
from datetime import datetime as dtdt 


final_df2 = pd.DataFrame()
for weekday in [0,1,2,3,4]:
    tmp_df = dfs_stack[weekday]
    if final_df2.empty:
        final_df2 = tmp_df
        # print(list(tmp_df))
    else:
        common = [x for x in list(final_df2) if x in list(tmp_df)]
        final_df2 = final_df2.merge(tmp_df,on=common,how='outer')

final_df2 = final_df2.sort_values('datetime')
final_df2_today = final_df2[final_df2['datetime']>=dtdt.today().replace(hour=0)]
dt = final_df2_today['datetime'].min().date()
quantity_cols = [c for c in list(final_df2_today) if 'combined' in c]
final_df2_today.set_index('hour')[quantity_cols].plot(kind='bar',stacked=True,figsize=(8,8),
                                                      title=f'Positions for {dt}')
#%%
kpis_df = pd.DataFrame(stack)
kpis_df.groupby('hour').mean()
# kpis_df.groupby('weekday').mean()
# kpis_df.groupby('opex').mean()

kpis_df.query('weekday.isin([4])').groupby('hour').mean()
kpis_df.groupby('opex').mean()
#%%
# NEXT: 
# combine the positions df with candles df to get performance by 15min window for each hour predictor

# ALSO: 
# try applying this method to my regulat hour analysis (i.e. instead of finding correlated hours, predict by historical movement of the same hour)
#%%
from Algo.Utils.files_handle import get_candles_outpath


candles_csv = get_candles_outpath('NQ',True)

candles_df = pd.read_csv(candles_csv,parse_dates=['time_utc'])[['time_utc','open']]
candles_df['datetime'] = candles_df['time_utc']
candles_df['y'] = candles_df['open'].diff().shift(-1)
candles_df_hourly = candles_df.set_index('datetime').resample('H').agg({'open':'first','y':'sum'}).reset_index()
candles_df_hourly.tail(5)


#%%
final_df3 = final_df2.merge(candles_df_hourly,on='datetime',how='left')


from Algo.Stocks_Research.Nasdaq.analyze_nasdaq_hours_correlation import add_opex_week_features,get_cpi_dates

final_df3 = add_opex_week_features(final_df3,date_col='datetime')

cpi_dates, cpi_mondays = get_cpi_dates()
cpi_mondays = [x.date() for x in cpi_mondays]
final_df3['is_cpi_week'] = final_df3['week'].isin(cpi_mondays)

#%%

#%%
weekday = 
opex_col = 'is_cpi_week'
profit_col = 'profit_hour=11'

filter = f'{opex_col} & weekday == {weekday}'

quantity_cols = [c for c in list(final_df3) if 'combined_quantity' in c]
for c in quantity_cols:
    final_df3[f'profit_{c.replace("combined_quantity_","")}'] = final_df3[c]*final_df3['y']
    # print(c,(final_df3[c]*final_df3['y'])[-100:].tolist())
    
profit_cols = [c for c in list(final_df3) if 'profit' in c]

final_df4 = final_df3.query(filter)

for profit_col in [10,11,12,13,14]:
    profit_col = 'profit_hour='+str(profit_col)
    final_df4_pivot = pd.pivot_table(final_df4,index=['week'],values=[profit_col],columns=['hour'])
    final_df4_pivot.columns = final_df4_pivot.columns.droplevel(0)
    final_df4_pivot.cumsum().plot(figsize=(16,8),title=f'{profit_col} || WEEK={opex_col} || WEEKDAY={weekday}')
    plt.show()