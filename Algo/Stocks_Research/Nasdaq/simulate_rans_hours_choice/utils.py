import pandas as pd

from Algo.Stocks_Research.Nasdaq.analyze_nasdaq_hours_correlation import get_pivot,add_delta_ys, prepare_df,NASDAQ_CSV,BITCOIN_CSV
from Algo.Learning.strategies_selector import rolling_calmar
from Viasualization.trading_kpis import calc_calmar,calc_sharpe
import numpy as np

from matplotlib import pyplot as plt
from Algo.Notebooks.Utils.nasdaq_strategies_encoder import periods_list_to_strings, unite_consecutive_hours

multiplier_by_grouper = {"30M": 2, "15M": 4, "2H": 1 / 2, "4H": 1 / 4, "hour": 1}

all_opex_weeks_to_process = ['is_opex_week_+-2w','is_opex_week_-1w',
                                'is_opex_week','is_opex_week_+1w',
                                 'is_cpi_week','is_fomc_week',
                             'is_not_opex_week_dummy'
                             ]



def wrap_pivot_generation(opex_week_reference='is_opex_week', weekday=0, grouper='2H', df_override=None):
    if df_override is None:
        df, opex_week_reference_last = prepare_df()
    else:
        df = df_override
    pivot_opex_daily = get_pivot(df, opex_week_reference, weekdays_for_hourly=[weekday], grouper=grouper)
    pivot_opex_daily2 = pivot_opex_daily.copy()
    return pivot_opex_daily2


def filter_pivot(pivot_opex_daily3, window_in_months=6):
    window_in_weeks = window_in_months * 4  # months

    filtered_pivot = pivot_opex_daily3.copy()
    profit_changes_stack = {}
    for col in pivot_opex_daily3.columns:
        print(col)
        rolling_mean = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(window_in_weeks, 1).mean()
        rolling_sum = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(window_in_weeks, 1).sum()
        rolling_sum_ext = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(2 * window_in_weeks, 1).sum()
        rolling_sum1 = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(5, 1).max() > 0

        losing_cond = rolling_mean < -5
        losing_cond_ext = rolling_sum_ext <= 0

        winning_last_one = rolling_sum < -5
        recent_calmar = pivot_opex_daily3[col].replace(0, -1).shift(1).rolling(window_in_weeks, 1).apply(
            lambda x: calc_calmar(x)[0])
        outperform_cond = recent_calmar > 3.5
        winning_strike_cond = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(window_in_weeks,
                                                                                         1).min() >= -10
        winning_strike_cond2 = pivot_opex_daily3[col].replace(0, np.nan).shift(1).rolling(window_in_weeks,
                                                                                          1).mean() >= 10
        # losing_cond_extended = losing_cond |
        profit1 = filtered_pivot[col].sum()
        # filtered_pivot.loc[losing_cond,col] = 0
        profit2 = filtered_pivot[col].sum()
        # filtered_pivot.loc[losing_cond_ext,col] = 0
        profit3 = filtered_pivot[col].sum()
        # filtered_pivot.loc[outperform_cond,col] = 2
        profit4 = filtered_pivot[col].sum()
        # filtered_pivot.loc[~winning_strike_cond,col] = 0
        filtered_pivot.loc[~winning_strike_cond2, col] = 0
        profit5 = filtered_pivot[col].sum()
        # print('filter #1 changed profit by',profit2-profit1)
        # print('filter #2 changed profit by',profit3-profit2)
        # print('filter #3 changed profit by',profit4-profit3)
        print('filter #4 changed profit by', profit5 - profit4)
        profit_changes_stack[col] = {'filter #1': profit2 - profit1,
                                     'filter #2': profit3 - profit2,
                                     'filter #3': profit4 - profit3,
                                     'filter #4': profit5 - profit4}
    return filtered_pivot, profit_changes_stack


def get_allowed_hours(pivot, resolution2='hour',
                      PLOT=False, last_index_to_use=0,
                      min_value_for_last3=5,
                        min_value_for_all=5,
                      base_calmar=1, extreme_calmar=4,
                      num_weeks=25,extended_num_weeks=None,
                      num_weeks_for_all=12,
                      all_or_false_for_reference='all'):
    allowed_cols_new = []
    if last_index_to_use < 0:
        pivot_for_calc = pivot[:last_index_to_use]
    elif last_index_to_use == 0:
        pivot_for_calc = pivot
    else:
        raise Exception('last_index_to_use should be negative')

    for c in list(pivot_for_calc):
        if c[2]:
            try:
                pivot_for_calc[(c[0], c[1], 'all')] = pivot_for_calc[c].fillna(0) + pivot_for_calc[(c[0], c[1], False)]
            except:
                pivot_for_calc[(c[0], c[1], 'all')] = pivot_for_calc[c].fillna(0)

            cond_last3 = pivot_for_calc[c].shift(0).replace(0, np.nan).dropna()[-3:].mean() > min_value_for_last3
            cond_last12_all = pivot_for_calc[(c[0], c[1], all_or_false_for_reference)].shift(0).replace(0, np.nan).dropna()[
                              -num_weeks_for_all:].mean() > min_value_for_all
            # cond_last12_all_above_0 = pivot_for_calc[(c[0], c[1], 'all')].shift(0).replace(0, np.nan).dropna()[
            #                   -num_weeks // 2:].mean() > min_value_for_last3
            cond_calmar_25 = \
            calc_calmar(pivot_for_calc[c].shift(0)[-num_weeks:], use_annual=False, epsilon_reduction=0.01)[
                0] > base_calmar
            cond_extreme_calmar = \
            calc_calmar(pivot_for_calc[c].shift(0)[-num_weeks:], use_annual=False, epsilon_reduction=0.01)[
                0] > extreme_calmar
            if extended_num_weeks is not None:
                cond_calmar_extended_period = calc_calmar(pivot_for_calc[c].shift(0)[-extended_num_weeks:], use_annual=False, epsilon_reduction=0.01)[
                    0] > extreme_calmar
            else:
                cond_calmar_extended_period = False

            is_allowed = (cond_last3 and cond_last12_all and cond_calmar_25) or (cond_extreme_calmar and cond_last3) \
                                or (cond_extreme_calmar and cond_last12_all) \
                                    or  (cond_calmar_extended_period and cond_last12_all)

            if is_allowed:
                allowed_cols_new.append(c)

    plt.tight_layout()

    allowed_hours = [x[1] for x in allowed_cols_new]
    if resolution2 == '2H':
        allowed_hours = sorted([x for x in allowed_hours] + [x + 1 for x in allowed_hours])
    elif resolution2 == '4H':
        allowed_hours = sorted(
            [x for x in allowed_hours] + [x + 1 for x in allowed_hours] + [x + 2 for x in allowed_hours]) + [x + 3 for x
                                                                                                             in
                                                                                                             allowed_hours]

    allowed_cols_completer = [(x[0], x[1], False) for x in allowed_cols_new if (x[0], x[1], False) in pivot_for_calc]
    print('ALLOWED HOURS ARE', allowed_hours)
    filtered_df = pivot[sorted(allowed_cols_new + allowed_cols_completer)]

    try:
        filtered_df[('y', 'all', False)] = filtered_df[[x for x in list(filtered_df) if not x[2]]].sum(axis=1)
    except:
        print('NO FALSE COLUMNS')
        filtered_df[('y', 'all', False)] = 0
    filtered_df[('y', 'all', True)] = filtered_df[[x for x in list(filtered_df) if x[2]]].sum(axis=1)

    if PLOT:
        filtered_df[sorted(allowed_cols_new + allowed_cols_completer) + [x for x in list(filtered_df) if
                                                                         'all' in str(x)]].cumsum().plot(
            style=['-', '--'] * 10, figsize=(20, 10),
            color=np.repeat(
                ['#d62728', '#1f77b4', '#7f7f7f', '#ff7f0e', '#2ca02c', '#9467bd', '#8c564b', '#e377c2', 'orange',
                 '#bcbd22'], 2))
    return allowed_hours


def slide_build_profits(full_df,resolution, weekday, opex_week,
                        num_weeks=25, min_value_for_last3=5,
                        cond_on_hours=lambda x: True,
                        extended_num_weeks=None,
                        num_weeks_for_all=12,
                        min_value_for_all=5,
                        all_or_false_for_reference='all'
                        ):
    pivot = wrap_pivot_generation(opex_week_reference=opex_week,
                                  weekday=weekday, grouper=resolution, df_override=full_df)
    pivot['filtered_profit'] = np.nan
    pivot['naive_profit'] = np.nan
    first_true_column = [x for x in list(pivot) if x[2]][0]

    for max_index in sorted([-i for i in range(1, 130)]):
        print(f'Handling Week: {opex_week}, Weekday = {weekday}, Resolution {resolution} with max_index {max_index}')

        if pivot.iloc[max_index][first_true_column] == 0:
            continue
        allowed_hours = get_allowed_hours(pivot, resolution2=resolution,
                                          PLOT=False, last_index_to_use=max_index,
                                          num_weeks=num_weeks,
                                          min_value_for_last3=min_value_for_last3,
                                            min_value_for_all=min_value_for_all,
                                            num_weeks_for_all=num_weeks_for_all,
                                            all_or_false_for_reference=all_or_false_for_reference,
                                          extended_num_weeks=extended_num_weeks,

                                          )
        allowed_hours = [x for x in allowed_hours if cond_on_hours(x)]
        allowed_hours_naive = [x for x in range(24)]
        allowed_cols = [x for x in list(pivot) if x[1] in allowed_hours]
        allowed_cols_naive = [x for x in list(pivot) if x[1] in allowed_hours_naive]
        # Calculate the value for the filtered profit column

        if max_index != -1:
            filtered_profit_value = pivot[allowed_cols].iloc[max_index:max_index + 1, :].sum(axis=1)
            full_profit = pivot[allowed_cols_naive].iloc[max_index:max_index + 1, :].sum(axis=1)
            # Assign the value using iloc
            pivot.iloc[max_index:max_index + 1, pivot.columns.get_loc('filtered_profit')] = filtered_profit_value
            pivot.iloc[max_index:max_index + 1, pivot.columns.get_loc('naive_profit')] = full_profit
        else:
            filtered_profit_value = pivot[allowed_cols].iloc[max_index:, :].sum(axis=1)
            full_profit = pivot[allowed_cols_naive].iloc[max_index:, :].sum(axis=1)
            # Assign the value using iloc
            pivot.iloc[max_index:, pivot.columns.get_loc('filtered_profit')] = filtered_profit_value
            pivot.iloc[max_index:, pivot.columns.get_loc('naive_profit')] = full_profit
    first_true_column = [x for x in list(pivot) if x[2]][0]
    pivot.loc[pivot[first_true_column] == 0, 'filtered_profit'] = 0
    pivot.loc[pivot[first_true_column] == 0, 'naive_profit'] = 0
    return pivot[['filtered_profit', 'naive_profit']]