#import graphlab as gl
from __future__ import division
from datetime import datetime
from datetime import timedelta as td
from yahoo_finance import Share
from yahoo_fin import stock_info as si
import pandas_ta as ta
import pandas as pd
import numpy as np
from matplotlib import pyplot as plt

from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe,get_drawdown_vector
import os
from Algo.Utils.files_handle import HOME

def add_indicators(df_xy):
    df_xy.ta.strategy(ta.CommonStrategy) #"Momentum") # Default values for all Momentum indicators
    return df_xy

def add_bollingers(df_xy,window_size,res,ref_col='close',stds=2):

    # Calculate the SMA
    df_xy.insert(0, 'MA_%s_%s'%(res,window_size), df_xy[ref_col].rolling(window_size).mean())

    # Calculate the upper and lower Bollinger Bands
    stds = df_xy[ref_col].rolling(window_size).std() * bol_size
    df_xy.insert(0, 'MA_%s_%s_upper'%(res,window_size), df_xy['MA_%s_%s'%(res,window_size)] + stds)
    df_xy.insert(0, 'MA_%s_%s_lower'%(res,window_size), df_xy['MA_%s_%s'%(res,window_size)] - stds)

    df_xy['location_%s_%s' % (res, window_size)] = (df_xy[ref_col] - df_xy['MA_%s_%s' % (res, window_size)]) / stds

    # Remove the NaNs -> consequence of using a non-centered moving average
    #df_xy.dropna(inplace=True)

    return df_xy


PPT_IN_PERCENT = True
stock_ticker = 'TTF=F' #'TTF=F' #'NG=F' #'^GSPC'
res = '1D'
intervals_dict = {'1D':'1d','4H':'4h','1Min':'1m','1W':'1wk'}
calc_xy = True

if __name__ == '__main__':
    if calc_xy:
        # download historical prices of S&P 500 index
        today = datetime.strftime(datetime.today(), "%Y-%m-%d")
        tommorrow = datetime.strftime(datetime.today() + td(days=1), "%Y-%m-%d")
        # we gather historical quotes from 2001-01-01 up to today
        if res == '1D':
            hist_quotes_df = si.get_data(stock_ticker, '2018-01-01', tommorrow, interval=intervals_dict[res])
        elif res == '1Min':
            hist_quotes_df = si.get_data(stock_ticker, (datetime.now()-td(days=5)).strftime('%Y-%m-%d'), tommorrow, interval=intervals_dict[res])
        # add the outcome variable, 1 if the trading session was positive (close>open), 0 otherwise
        hist_quotes_df['y'] = hist_quotes_df['close'] - hist_quotes_df['open']
        # we also need to add three new columns ‘ho’ ‘lo’ and ‘gain’
        # they will be useful to backtest the model, later
        hist_quotes_df['ho'] = hist_quotes_df['high'] - hist_quotes_df['open']  # distance between Highest and Opening price
        hist_quotes_df['lo'] = hist_quotes_df['low'] - hist_quotes_df['open']  # distance between Lowest and Opening price

        df_xy = hist_quotes_df[['open', 'close', 'high', 'low', 'ho', 'lo', 'y','volume']]
        df_xy = df_xy.reset_index().sort_values('index').set_index('index')
        # can take it to be binart 0-1 todo
        df_xy['ft1_close-Prev1'] = df_xy['close'].diff()
        df_xy['ft2_close-Prev2'] = df_xy['close'].diff(2)
        df_xy['ft3_close-Prev5'] = df_xy['close'].diff(5)
        df_xy['ft1_open-Prev1'] = df_xy['open'].diff()
        df_xy['ft2_open-Prev2'] = df_xy['open'].diff(2)
        df_xy['ft3_open-Prev5'] = df_xy['open'].diff(5)

        # write TTF
        df_xy.reset_index().to_csv(os.path.join(HOME,"General_Stocks","%s_Prices.csv")%stock_ticker.split('=')[0],index=False)

        # Define the parameters for the Bollinger Band calculation
        bol_size = 2
        ref_col = 'open'

        for window_size in [10, 20, 30, 50, 100, 200]:
            df_xy = add_bollingers(df_xy, window_size, res, ref_col=ref_col)

        # add macd
        df_xy = df_xy.merge(ta.macd(df_xy[ref_col]), left_index=True, right_index=True)
        # ['MACDh_12_26_9','MACDs_12_26_9','MACDh_12_26_9']
        # [real_macd,            signal_line,      gap
        df_xy['MACDh_12_26_9_diff1'] = df_xy['MACDh_12_26_9'].diff()
        df_xy['MACDh_12_26_9_diff2'] = df_xy['MACDh_12_26_9'].diff(2)
        df_xy['MACDh_12_26_9_diff4'] = df_xy['MACDh_12_26_9'].diff(4)
        df_xy['MACD_12_26_9_diff1'] = df_xy['MACD_12_26_9'].diff()
        df_xy['MACD_12_26_9_diff2'] = df_xy['MACD_12_26_9'].diff(2)
        df_xy['MACD_12_26_9_diff4'] = df_xy['MACD_12_26_9'].diff(4)

        macd_fts = [x for x in list(df_xy) if 'MACD' in x]
        bollingers_fts = [x for x in list(df_xy) if '1D' in x]  # 'open',
        dynamic_fts = [x for x in list(df_xy) if 'ft' in x and 'Prev' in x]
        dynamic_open_fts = [x for x in dynamic_fts if
                            'open' in x and not ('close' in x or 'high' in x or 'low' in x or 'ho' in x or 'lo' in x)]
        feature_columns_to_be_shifted = ['close', 'high', 'low', 'ho', 'lo','volume'] + [x for x in dynamic_fts if
                                                                                x not in dynamic_open_fts]
        if ref_col != 'open':
            feature_columns_to_be_shifted += bollingers_fts
            feature_columns_to_be_shifted += macd_fts
        for ft in feature_columns_to_be_shifted:
            df_xy[ft + '_-1d'] = df_xy[ft].shift(1)

        #df_xy.iloc[-1:]['close'] = np.nan
        cols_before_addition = [x for x in list(df_xy)]
        #df_xy[['open','close','high','low']] = df_xy[['open','close','high','low']].shift(1)#.fillna(method='ffill').fillna(method='bfill')
        #df_xy = df_xy.dropna(subset=['open','close','high','low'])
        df_xy = df_xy.dropna(subset=['open','close','high','low'],how='all')
        df_xy.ta.strategy(ta.AllStrategy)
        df_xy.to_csv(os.path.join(HOME,"General_Stocks","S&P500_df_xy.csv"))
    else:
        df_xy = pd.read_csv(os.path.join(HOME,"General_Stocks","S&P500_df_xy.csv"),parse_dates=['index']).set_index('index')
        macd_fts = [x for x in list(df_xy) if 'MACD' in x]
        bollingers_fts = [x for x in list(df_xy) if '1D' in x]  # 'open',
        dynamic_fts = [x for x in list(df_xy) if 'ft' in x and 'Prev' in x]
        dynamic_open_fts = [x for x in dynamic_fts if
                            'open' in x and not ('close' in x or 'high' in x or 'low' in x or 'ho' in x or 'lo' in x)]
        feature_columns_to_be_shifted = ['close', 'high', 'low', 'ho', 'lo', 'volume'] + [x for x in dynamic_fts if
                                                                                          x not in dynamic_open_fts]

        cols_before_addition = ['MA_1D_200_lower', 'MA_1D_200_upper', 'MA_1D_200', 'MA_1D_100_lower', 'MA_1D_100_upper', 'MA_1D_100', 'MA_1D_50_lower', 'MA_1D_50_upper', 'MA_1D_50', 'MA_1D_30_lower', 'MA_1D_30_upper', 'MA_1D_30', 'MA_1D_20_lower', 'MA_1D_20_upper', 'MA_1D_20', 'MA_1D_10_lower', 'MA_1D_10_upper', 'MA_1D_10', 'open', 'close', 'high', 'low', 'ho', 'lo', 'y', 'volume', 'ft1_close-Prev1', 'ft2_close-Prev2', 'ft3_close-Prev5', 'ft1_open-Prev1', 'ft2_open-Prev2', 'ft3_open-Prev5', 'location_1D_10', 'location_1D_20', 'location_1D_30', 'location_1D_50', 'location_1D_100', 'location_1D_200', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9_diff1', 'MACDh_12_26_9_diff2', 'MACDh_12_26_9_diff4', 'MACD_12_26_9_diff1', 'MACD_12_26_9_diff2', 'MACD_12_26_9_diff4', 'close_-1d', 'high_-1d', 'low_-1d', 'ho_-1d', 'lo_-1d', 'volume_-1d', 'ft1_close-Prev1_-1d', 'ft2_close-Prev2_-1d', 'ft3_close-Prev5_-1d']
        cols_before_addition += [x for x in list(df_xy) if 'MA_' in x]
    df_xy[[x for x in list(df_xy) if x not in cols_before_addition]] = df_xy[[x for x in list(df_xy) if x not in cols_before_addition]].shift(1)
    df_xy[[x for x in list(df_xy) if 'DPO_20' in x]] = df_xy[[x for x in list(df_xy) if 'DPO_20' in x]].shift(10)
    df_xy = df_xy[[x for x in list(df_xy) if 'profit' not in x and 'action' not in x]]
    corrs = df_xy[[x for x in list(df_xy) if x not in feature_columns_to_be_shifted]].corr()['y'].sort_values()

    most_negative = corrs.index.tolist()[:5]
    most_positive = corrs.index.tolist()[-6:-1]

    for pred in most_negative+most_positive:
        df_xy['tmp'] = df_xy[pred] #- df_xy[pred].mean()
        if pred in most_negative:
            df_xy['tmp'] *= -1

        df_xy['action_%s'%pred] = 'B'
        df_xy.loc[df_xy['tmp']<0,'action_%s'%pred] = 'S'

        df_xy['profit_%s'%pred] = np.sign(df_xy['y']*df_xy['tmp'])*abs(df_xy['y'])
        ignored_trades = abs(df_xy['tmp'])<abs(df_xy['tmp']).quantile(0.35)
        df_xy.loc[ignored_trades,'profit_%s'%pred] = 0
        df_xy.loc[ignored_trades,'action_%s'%pred] = 'I'
        if PPT_IN_PERCENT:
            df_xy['profit_%s'%pred] = df_xy['profit_%s'%pred]/df_xy['open']
        hr = (df_xy[~ignored_trades]['profit_%s'%pred] > 0).mean()
        ppt = (df_xy[~ignored_trades]['profit_%s'%pred]).mean()
        calmar, max_dd = calc_calmar(df_xy['profit_%s'%pred])
        sharpe = calc_sharpe(df_xy['profit_%s'%pred])
        print ('Pred: %s ||| PPT: %s  | HR: %s | calmar: %s | sharpe: %s | max_dd: %s'%(pred,ppt, hr, calmar,sharpe,max_dd))
    df_xy[[x for x in list(df_xy) if 'profit' in x]].cumsum().plot()
    plt.show()
    actions_df = df_xy[[x for x in list(df_xy) if 'action' in x]]
    last_actions = actions_df.iloc[-1]
    last_vals = df_xy[most_negative+most_positive].iloc[-1]
    df_xy['BIAS_SMA_26'][-20:].to_csv(os.path.join(HOME,"General_Stocks","BIAS_SMA_Vals_full.csv"))
    aaa = 1

