#!/usr/bin/env python
# coding: utf-8

# In[1]:


import yfinance as yf
from datetime import datetime as dtdt
from datetime import timedelta as td
import os
import pandas as pd
from Algo.Utils.files_handle import HOME
import numpy as np
import pandas as pd
from datetime import timedelta as td


from Algo.AI import slide_build
from lightgbm import LGBMRegressor
from Algo.Viasualization.trading_kpis import print_kpis


def get_data(ticker_symbol,start_date = "2012-01-01",end_date = "2024-12-31",interval = '1d',
             resolution_override=None):

    if interval == '1h':
        start_date = (dtdt.now() - td(days=365*2-3)).strftime('%Y-%m-%d')

    # Fetch historical data from Yahoo Finance
    data = yf.download(ticker_symbol, start=start_date, end=end_date,interval=interval)
    if isinstance(list(data)[0], tuple):
        data.columns = [x[0] for x in list(data)]
    if resolution_override is not None:
        assert resolution_override in ['1w','1M']
        # resample the data to the new resolution
        data = data.resample(resolution_override).agg({'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'})

    data.columns = [x.lower() for x in data.columns]
    for c in ['open', 'high', 'low', 'close', ]:
        data[c + '_log'] = np.log(data[c])

    data['y'] = data['open'].diff().shift(-1)
    data['y_open_to_close'] = data['close'] - data['open']
    data['y_close_to_open'] = data['open'].shift(-1) - data['close']
    data['y_close_to_close'] = data['close'].diff().shift(-1)

    data['log_y'] = data['open_log'].diff().shift(-1)

    data['y_pct'] = data['open'].pct_change().shift(-1)
    data['y_open_to_close_pct'] = data['y_open_to_close'] / data['open']
    data['y_close_to_open_pct'] = data['y_close_to_open'] / data['close']
    data['y_close_to_close_pct'] = data['y_close_to_close'] / data['close']

    data = data.reset_index()
    data['Date'] = pd.to_datetime(data['Date'])
    data['weekday'] = data['Date'].dt.weekday
    data['week_of_month'] = (data['Date'] - data['Date'].apply(lambda x: td(days=x.weekday()))).dt.day // 7 + 1

    data['year'] = data['Date'].dt.year
    data['y'] = data['open']

    return data


def enrich_dataset(data):
    data['high-open_pct'] = ((data['high'] - data['open']) / data['open']).shift(1)

    data['low-open_pct'] = ((data['low'] - data['open']) / data['open']).shift(1)

    data['close-open_pct'] = ((data['close'] - data['open']) / data['open']).shift(1)
    data['high-low_pct'] = ((data['high'] - data['low']) / data['low']).shift(1)
    data['close-low_pct'] = ((data['close'] - data['low']) / data['low']).shift(1)
    data['high-close_pct'] = ((data['high'] - data['close']) / data['close']).shift(1)
    data['open-open1d_pct'] = ((data['open'] - data['open'].shift(1)) / data['open'].shift(1))
    data['open-open3d_pct'] = ((data['open'] - data['open'].shift(3)) / data['open'].shift(3))
    data['open-open5d_pct'] = ((data['open'] - data['open'].shift(5)) / data['open'].shift(5))
    data['close-close1d_pct'] = ((data['close'] - data['close'].shift(1)) / data['close'].shift(1)).shift(1)

    # define what's a negative can

    fts = ['high-open_pct', 'low-open_pct', 'close-open_pct', 'high-low_pct', 'close-low_pct', 'high-close_pct',
           'open-open1d_pct', 'open-open3d_pct', 'open-open5d_pct', 'close-close1d_pct', 'weekday']

    return data, fts

def enrich_dataset_with_moving_avgs(data):
    windows= [10,15, 20,30,40, # 50,75, 100,120,200
              ]
    for ma in windows:
        print('creating MA col for :',ma)
        data['MA'+str(ma)] = data['open'].rolling(ma).mean().shift(1)
        data[f'open-MA{ma}_pct'] = (data['open'] - data['MA'+str(ma)]) / data['MA'+str(ma)]
    ma_cols = [f'MA{ma}' for ma in windows]
    for ma in windows:
        # check how many other MAs are below the current MA for each row
        print(f'creating rank for window={ma}')
        data[f'MA{ma}_rank'] = pd.concat([pd.DataFrame(data[f'MA{ma}'] >= data[f'MA{ma2}'])
                                        for ma2 in windows],
                                         axis=1).sum(axis=1)

    fts = [x for x in list(data) if 'rank' in x]+[f'open-{ma}_pct' for ma in ma_cols]+['weekday']
    return data, fts

def generate_profit_col(data, quantile, pred_col, cost_per_trade_in_pct=0,
                        target_col_for_profit='y_open_to_close_pct',
                        allow_negative_position=True):
    quantile_value = abs(data[pred_col]).quantile(quantile)
    data[pred_col + '_quantity'] = 0
    data.loc[abs(data[pred_col]) > quantile_value, pred_col + '_quantity'] = np.sign(
        data.loc[abs(data[pred_col]) > quantile_value, pred_col])
    if not allow_negative_position:
        data[pred_col + '_quantity'] = data[pred_col + '_quantity'].clip(lower=0)
    data[pred_col + f'_profit_{quantile}'] = data[pred_col + '_quantity'] * data[target_col_for_profit]
    if cost_per_trade_in_pct > 0:
        data[pred_col + f'_profit_{quantile}'] -= cost_per_trade_in_pct * abs(data[pred_col + '_quantity'].diff())
    return data


def handle_ticker(ticker_symbol,
                  max_depths=[3, 5, 7, 8, 10],
                  steps=[3],interval='1d',resolution_override=None,
                  which_features='MA'):
    data = get_data(ticker_symbol,interval=interval,resolution_override=resolution_override)
    if which_features == 'MA':
        data, fts = enrich_dataset_with_moving_avgs(data)
    elif which_features == 'candles':
        data, fts = enrich_dataset(data)
    else:
        raise
    for max_depth in max_depths:
        for num_leaves in num_leaves_values:
            for learning_rate in learning_rate_values:
                for max_window in max_windows_list:
                    for step in steps:
                        algo = LGBMRegressor(max_depth=max_depth, num_leaves=num_leaves,
                                             learning_rate=learning_rate)
                        if ONLY_WINNER_CONF:
                            if max_depth != winning_max_depth or num_leaves != winning_num_leaves or learning_rate != winning_learning_rate or max_window != winning_max_window:
                                continue
                        print(f'Handling window = {max_window}')
                        pred_name = f'y_pred_{max_window}_step={step}_nlvs={num_leaves}_rate={learning_rate}_depth={max_depth}'

                        fts_suffix = ''
                        if which_features == 'MA':
                            fts_suffix = '_fts=MA'
                        csv_outpath = os.path.join(HOME, 'Stocks_Research', 'other_stocks', 'lgbm_on_candle_fts',
                                                   f'slide_build_{which_cap}_{ticker_symbol}_window={max_window}{fts_suffix}.csv')
                        if os.path.exists(csv_outpath) or os.path.exists(csv_outpath.replace(which_cap + '_', '')):
                            continue

                        data = data[data[time_col].dt.tz_localize(None) >= START_DATE]
                        data = slide_build.general_slide_build(algo, data, target, fts, max_window=max_window,
                                                               step=step,
                                                               time_col=time_col, predict_proba=False,
                                                               pred_name=pred_name,
                                                               verbose=1)

                        data = generate_profit_col(data, ref_quantile,
                                                   f'y_pred_{max_window}_step={step}_nlvs={num_leaves}_rate={learning_rate}_depth={max_depth}',
                                                   cost_per_trade_in_pct=0,
                                                   target_col_for_profit=target)

                        profit_col = f'y_pred_{max_window}_step={step}_nlvs={num_leaves}_rate={learning_rate}_depth={max_depth}_profit_{ref_quantile}'

                        data[[time_col, target, pred_name, profit_col]].to_csv(csv_outpath,
                                                                               index=False)


if __name__ == '__main__':

    ONLY_WINNER_CONF = True

    target = 'y_open_to_close_pct'
    COST_PER_TRADE_BY_TICKER = {'ROOT': 0.003,  # 0.4$ spread for 62$ price divided by 2 is 0.0032, 0.0025is ok to assume
                                'TSLA': 0.0003}


    max_windows_list = [  # 100,250,
                           400, 420, 450,  # 500,540,
                           # 730,1000
                       ][:1]

    # Possible values for num_leaves
    num_leaves_values = [20, 30, 40, 50, 60]

    # Possible values for learning_rate
    learning_rate_values = [0.01, 0.05, 0.1, 0.2, 0.3]

    winning_max_depth = 5
    winning_learning_rate = 0.01
    winning_num_leaves = 30
    winning_max_window = 400
    winning_step = 3

    ref_quantile = 0.33
    time_col = 'Date'
    START_DATE = dtdt(2019,1,1)

    WHICH_FEATURES = 'candles'
    WHICH_FEATURES = 'MA'

    import time
    # time.sleep(60*60*5)

    # for which_cap in ['small_cap','mid_cap']:
    #     tickers_df = pd.read_csv(os.path.join(HOME,'Stocks_Research','other_stocks','stocks_list_htmls',f'{which_cap}_gainers_with_volume.csv'))
    #     tickers_df_filtered = tickers_df[(tickers_df['volume'] > tickers_df['volume'].quantile(0.3))&
    #                                         (tickers_df['avg_move'] > tickers_df['avg_move'].quantile(0.3))]
    #     potential_tickers = tickers_df_filtered['ticker'].tolist()
    #
    #     for ticker_symbol in ['ROOT','LMND','TSLA']+potential_tickers:
    #         print(ticker_symbol)
    #         handle_ticker(ticker_symbol,which_features=WHICH_FEATURES)

    handle_ticker('^NDX',interval='1d',resolution_override='1w')