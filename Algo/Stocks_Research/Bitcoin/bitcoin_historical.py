import os
import json
import numpy as np
import pandas as pd
import yfinance as yf
from datetime import datetime as dtdt, timedelta as td
from tabulate import tabulate
from Algo.Stocks_Research.utils.general import analyze_profits, get_df_by_windows, update_json
from Algo.Trading.general_utils import update_actual_positions
from Algo.Utils.send_email import send_email_main
from Algo.Utils.files_handle import HOME,PROJECT_ROOT

from Algo.Stocks_Research.Bitcoin.definitions import COST_PER_TRADE

WINNERS_TABLE = {'1D': (40, 120, 'profit_filtered_f4','weekly',42),
                     # '2H': (10, 200, 'profit_filtered_f3_dynamic_chosen','weekly',42),
                     '2H': (20, 120, 'profit_filtered_f4_dynamic_chosen','weekly',42), # updated on Aug 24
                     '1H': (20, 480, 'profit_filtered_f4_dynamic_chosen','weekly',42),
                     # '4H': (50, 120, 'profit_filtered_f2_dynamic_chosen','weekly',42) # updated on Aug 24
                     '4H': (40, 480, 'profit_filtered_f2_dynamic_chosen','weekly',42) # updated on Sep 24
                     }

def fetch_data(ticker_symbol,start_date = "2012-01-01",
            end_date = "2030-12-31",interval = '1h',resolution = '1H',
               num_days_back_override=None):

    two_years_gap = 365 * 2 - 3
    hourly_resolution_days = 90
    num_days_back = hourly_resolution_days if resolution != '1D' else 365 * 8
    num_days_back = num_days_back_override if num_days_back_override is not None else num_days_back
    num_days_back = min(num_days_back, two_years_gap)
    if resolution == '1D':
        interval = '1d'
    elif resolution == '4H':
        interval = '1h'
    if interval == '1h':
        start_date = (dtdt.now() - td(days=num_days_back)).strftime('%Y-%m-%d')

    data = yf.download(ticker_symbol, start=start_date, end=end_date, interval=interval)
    if isinstance(list(data)[0], tuple):
        data.columns = [x[0] for x in list(data)]
    return preprocess_data(data, interval, resolution)

def preprocess_data(data, interval, resolution):
    data.columns = [x.lower() for x in data.columns]
    for c in ['open', 'high', 'low', 'close']:
        data[c + '_log'] = np.log(data[c])
    data['y'] = data['open'].diff().shift(-1)
    data['log_y'] = data['open_log'].diff().shift(-1)

    if interval == '1h':
        if resolution != '1H':
            data = data.resample(resolution).agg({'open': 'first', 'close': 'last', 'high': 'max', 'low': 'min',
                                                  'open_log': 'first', 'close_log': 'last', 'high_log': 'max',
                                                  'low_log': 'min', 'y': 'sum', 'log_y': 'sum'})
    return data


def main(resolution = '1H',
         num_days_back_override=None,
         return_df_to_plot=False,
         plot=False,
         cost_per_trade=COST_PER_TRADE):
    ticker_symbol = "BTC-USD"
    data = fetch_data(ticker_symbol,resolution=resolution,
                      num_days_back_override=num_days_back_override)

    df = data.copy()
    df = df.reset_index().rename(columns={'Date': 'date', 'Datetime': 'date'})

    # winners_table = {'1D': (40, 120, 'profit_filtered_f2'),
    #                  '2H': (10, 240, 'profit_filtered_f3'),
    #                  '1H': (20, 240, 'profit_filtered_f4')}

    winner_w1, winner_w2, winner_col,winner_resolution, winner_choice_window = WINNERS_TABLE[resolution]

    num_days = 730 * (24 if resolution == '1H' else 12)
    df = get_df_by_windows(df, window1=winner_w1, window2=winner_w2,cost_per_trade=cost_per_trade)
    df_to_plot, results = analyze_profits(df, num_days, f'{winner_w1}_{winner_w2}', plot=plot,
                                          resolution=winner_resolution,
                                          rolling_performance_window=winner_choice_window)
    is_current_week_using_filtered = df_to_plot[f'is_using_{"_".join(winner_col.split("_")[1:3])}'].iloc[-1]
    PERIOD_TO_MONITOR = 72

    filter_type = winner_col.split('_')[2]
    df['final_quantity'] = df[f'quantity_{filter_type}']
    df[['date', 'final_quantity']].to_csv(os.path.join(HOME, 'Trading', "Bitcoin_positions", f"bitcoin_historical_{resolution}.csv"), index=False)
    mini_df = df[-PERIOD_TO_MONITOR:]

    last_position = mini_df[f'quantity_{filter_type}'].iloc[-1] if is_current_week_using_filtered else 1
    # mini_df['resolution'] = resolution
    update_actual_positions(mini_df[['date', f'quantity_{filter_type}']].rename(columns={f'quantity_{filter_type}': 'quantity'}),
                            suffix=resolution)

    live_positions_json_path = os.path.join(PROJECT_ROOT, 'Algo', 'Stocks_Research', 'Bitcoin', 'live_positions.json')
    is_update, position_change = update_json(mini_df,last_position,live_positions_json_path,resolution)

    if is_update:
        msg = f"We had Positions change in the last {PERIOD_TO_MONITOR} candles"
        msg += f'\nLast advised position ({mini_df["date"].iloc[-1]}) = {last_position}'
        print(msg)
        mini_df[['date', f'quantity_{filter_type}']].set_index('date')
        full_tabulated = tabulate(mini_df[['date', f'quantity_{filter_type}']].set_index('date'), headers='keys', tablefmt='psql')

        utc_now = dtdt.utcnow().strftime('%Y-%m-%d %H:%M')
        direction = 'BUY' if last_position > 0 else 'SELL'
        position_type = "Long" if last_position > 0 else ("Neutral" if last_position == 0 else "Short")
        subject = f'Bitcoin ({resolution} based) {direction} ALERT | {"+" if last_position >0 else ""}{last_position} {position_type} {utc_now} '
        send_email_main(subject, msg+'\n'*4+full_tabulated)
    if not return_df_to_plot:
        return df
    else:
        return df_to_plot

def wrap_main(num_days_back_override=None,
              resolutions_override=None):
    resolutions = ['1H', '4H', '1D']
    if resolutions_override is not None:
        resolutions = resolutions_override
    multi_resolution_df = pd.DataFrame()
    for resolution in resolutions:
        df = main(resolution,num_days_back_override=num_days_back_override,
                  return_df_to_plot=False,plot=False)
        df = df[['date']+[x for x in list(df) if 'profit' in x or 'quantity' in x]]
        df = df.rename(columns={x: f'{x}_{resolution}' for x in list(df) if 'profit' in x or 'quantity' in x})

        if multi_resolution_df.empty:
            multi_resolution_df = df
        else:
            multi_resolution_df = multi_resolution_df.merge(df,on='date',how='outer')
    multi_resolution_df = multi_resolution_df.sort_values('date').set_index('date')
    return multi_resolution_df


if __name__ == "__main__":
    resolutions = [ #'1D', # dropping as of Sep24 there seem to be no jusitfication. the daily double cond is better
                   '4H','2H']  # 1H/4H are not stable enough and performance concerns were raised in Apr 24. currenty can count on 1D only
    multi_df = wrap_main(num_days_back_override=365*5,
                         resolutions_override=resolutions)
    # from matplotlib import pyplot as plt
    # for which_f in ['f2','f3','f4']:
    #     profit_cols_f4 = [x for x in list(multi_df) if 'profit' in x and which_f in x or 'long_1H' in x]
    #     final_df = multi_df[profit_cols_f4].fillna(0)[-365 * 2 * 20:].reset_index()
    #     final_df['week'] = pd.to_datetime(final_df['date'].dt.date) - final_df['date'].dt.weekday.apply(lambda x: td(days=x))
    #     final_df = final_df.groupby('week')[profit_cols_f4].sum()
    #     final_df.cumsum().plot(title='Bitcoin Profits', figsize=(12, 8))
    #     plt.show()

    ### need to revisit to choost winner maybe between f2,f3,f4 and long or between resolutions
