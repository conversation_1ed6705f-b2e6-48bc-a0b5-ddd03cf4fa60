import pandas as pd
import numpy as np
from Algo.Viasualization.trading_strategies_summary import summarize_positions,get_ensemble_positions,prepare_final_positions
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe,get_drawdown_vector
from Algo.Learning.research_plays import rolling_calmar
from matplotlib import pyplot as plt
import os
from Algo.Utils.files_handle import HOME





METHOD = 'calmar'
THRESHOLD_VAL = 3 #0.2#-500
RESOLUTION = '%H'#%M'
IGNORED_QUANTITY_RATIO = 0.25 #0
MAX_CONTRACTS = 3
cost_per_trade = 0

CSV_PATH = os.path.join(HOME,"performance_analysis","dynamic_hours_selection","final_df.csv")
GROUP_FINAL_POSITIONS_PATH = os.path.join(HOME,"performance_analysis","dynamic_hours_selection","group_final_positions_$MODE$_Days=$DAYS$.csv")
CALC_FROM_NEW = True
USE_GROUP = True
PLOT = True

def add_stoploss_strategy(final_df,
                            activation_conds=[{'absolute':-2000,'hours':(11,11)}],ref_col='quantity',
                          stoploss_col_suffix=''):

    if ref_col == 'quantity':
        profit_col = 'profit'
    elif ref_col == 'quantity_selected_hours':
        profit_col = 'profit_selected_hours'

    final_df['quantity_stoploss%s'%stoploss_col_suffix] = final_df[ref_col]

    for activation_cond in activation_conds:
        ref_hours = activation_cond['hours']
        hours_cond = (final_df['t_open'].dt.hour >= ref_hours[0]) & (final_df['t_open'].dt.hour <= ref_hours[1])
        if 'absolute' in activation_cond.keys():
            bad_days = (final_df[hours_cond].groupby(final_df['t_open'].dt.date).sum()[profit_col] < activation_cond['absolute'])[(final_df[hours_cond].groupby(final_df['t_open'].dt.date).sum()[profit_col] < activation_cond['absolute'])
                    ].index.tolist()
            final_df.loc[(final_df['t_open'].dt.date.isin(bad_days)) & (final_df['t_open'].dt.hour > ref_hours[1]), 'quantity_stoploss%s' % stoploss_col_suffix] = 0
        elif 'takeprofit' in activation_cond.keys():
            good_days = (final_df[hours_cond].groupby(final_df['t_open'].dt.date).sum()[profit_col] > activation_cond['takeprofit'])[(
                        final_df[hours_cond].groupby(final_df['t_open'].dt.date).sum()[profit_col] > activation_cond['takeprofit'])].index.tolist()
            final_df.loc[(final_df['t_open'].dt.date.isin(good_days)) & (final_df['t_open'].dt.hour > ref_hours[1]), 'quantity_stoploss%s' % stoploss_col_suffix] = 0
        elif 'dynamic' in activation_cond.keys():
            # final_df.to_csv(os.path.join(HOME,"performance_analysis","drafts","ens_calmar0.25_S0.87_window4_profits.csv"),index=False)
            weekdays = [0]
            for window in [1,3,5,7,10,12,15]:
                (final_df[hours_cond&(final_df['t_open'].dt.weekday.isin(weekdays))].groupby(final_df['t_open'].dt.date).sum().replace(0,np.nan)['profit_selected_hours'] >= 0).replace(False, 0).rolling(
                    window, 1).mean().rolling(1,1).mean().plot()
            plt.legend(['W%s'%w for w in [1,3,5,7,10,12,15]])
            plt.show()
            final_df.loc[(final_df['t_open'].dt.date.isin(good_days)) & (final_df['t_open'].dt.hour > ref_hours[1]), 'quantity_stoploss%s' % stoploss_col_suffix] = 0

    final_df['profit_stoploss%s'%stoploss_col_suffix] = final_df['quantity_stoploss%s'%stoploss_col_suffix] * final_df['delta_y'] * 10 ** 4
    final_df['profit_stoploss%s'%stoploss_col_suffix] -= abs(final_df['quantity_stoploss%s'%stoploss_col_suffix].diff().fillna(0)) * cost_per_trade
    return final_df

def calc_final_df(days = 100,
                trades_file_suffix = '_All',
                mode = 'ens_calmar10_S0.5_window7',
                cond_on_strats = lambda x: True,
                weekdays = [0,1,2,3,4],weekdays_groups=[0,1,2,3,4],
                window = 10,
                epsilon = 0,hours_to_drop_from_original_profit=[],
                method=METHOD,threshold_val=THRESHOLD_VAL,
                  group_final_positions=None):

    if group_final_positions is None:
        group_final_positions = prepare_final_positions(days, mode, True, cond_on_strats, trades_file_suffix, weekdays,
                                                        min_score=0.5,debug=False)
    group_final_positions_ref = group_final_positions.copy()
    group_final_positions['weekday'] = group_final_positions.t_open.dt.weekday
    group_final_positions['quantity'] = np.clip(group_final_positions['quantity'], -MAX_CONTRACTS, MAX_CONTRACTS)

    group_final_positions['quantity_selected_hours'] = np.clip(group_final_positions['quantity'], -MAX_CONTRACTS, MAX_CONTRACTS)
    group_final_positions.loc[group_final_positions['t_open'].dt.hour.isin(hours_to_drop_from_original_profit),
                                                            'quantity_selected_hours'] = 0

    group_final_positions['profit'] = group_final_positions['quantity'] * group_final_positions['delta_y'] * 10 ** 4
    group_final_positions['profit'] -= abs(group_final_positions['quantity'].diff().fillna(0)) * cost_per_trade
    group_final_positions['profit_selected_hours'] = group_final_positions['quantity_selected_hours'] * group_final_positions['delta_y'] * 10 ** 4
    group_final_positions['profit_selected_hours'] -= abs(group_final_positions['quantity_selected_hours'].diff().fillna(0)) * cost_per_trade

    group_final_positions = add_stoploss_strategy(group_final_positions,  # 0,11 x -2000 x quantity - GOOD !!!
                                                  activation_cond={'absolute':-500,'hours':(11,11)},
                                                  ref_col='quantity') #_selected_hours')

    group_final_positions['quantity_filtered'] = group_final_positions['quantity']
    group_final_positions['profit_filtered_simple'] = group_final_positions['profit']

    final_df = pd.DataFrame()

    for i, d in enumerate(weekdays_groups):
        current_weekdays = [d]
        if isinstance(d,list):
            current_weekdays = d
        sub_df = group_final_positions[group_final_positions['t_open'].dt.weekday.isin(current_weekdays)]
        sub_df['date'] = sub_df['t_open'].dt.date
        sub_df['t_open_1H'] = sub_df['t_open'].apply(lambda x: x.replace(minute=0))
        sub_df['15min'] = sub_df['t_open'].dt.strftime(RESOLUTION)
        sub_df['quantity_change_abs'] = abs(sub_df['quantity'].diff())

        if RESOLUTION == '%H':
            sub_df = sub_df.groupby(['15min', 'date', 't_open_1H']).sum().reset_index()
            sub_df['t_open'] = sub_df['t_open_1H']
        pivot_df = pd.pivot_table(sub_df, values=['profit'], index=['date'], columns=['15min'],
                                  aggfunc=np.sum).fillna(0)
        pivot_df.columns = [c[0] + '_' + c[1] for c in list(pivot_df)]

        lossy_strats = pivot_df.sum()[pivot_df.sum() < -100].index.tolist()
        # pivot_df[lossy_strats].cumsum().plot()
        # plt.show()

        past_calmar_df = pivot_df.copy()
        for c in pivot_df.columns:
            if method == 'calmar':
                past_calmar_df[c] = np.clip(pivot_df[[c]].rolling(window, 3).apply(rolling_calmar).shift(1),
                                            -100, 100)
            elif method == 'sum':
                past_calmar_df[c] = pivot_df[[c]].rolling(window, 3).sum().shift(1)
            elif method == 'hr':
                past_calmar_df[c] = (pivot_df[[c]] > 0).rolling(window, 3).mean().shift(1)

            fake_sub_df = sub_df.loc[sub_df['15min'] == c.replace('profit_', '')]
            fake_sub_df_index = fake_sub_df.index.tolist()
            try:
                past_calmar_df['fake_index'] = fake_sub_df_index
            except:
                # print('failed on day %s | c=%s' % (d, c))
                pass
            past_calmar_df2 = past_calmar_df.reset_index().merge(fake_sub_df[['date']], on=['date'])

            past_calmar_df2_momentum = (past_calmar_df2[c].diff() + past_calmar_df2[c].diff(2) + past_calmar_df2[
                c].diff(4)) / 3
            past_calmar_df2_momentum = past_calmar_df2_momentum.fillna(0)
            past_calmar_df2[c] += epsilon * past_calmar_df2_momentum
            ### choosing the bad ones
            bad_indices = past_calmar_df2[(past_calmar_df2[c] < threshold_val)]['fake_index'].tolist()

            sub_df.loc[bad_indices, 'quantity_filtered'] = sub_df.loc[bad_indices, 'quantity_filtered']*IGNORED_QUANTITY_RATIO
            sub_df.loc[bad_indices, 'profit_filtered_simple'] = sub_df.loc[bad_indices,'profit'] * IGNORED_QUANTITY_RATIO
            sub_df['quantity_change_abs'] = abs(sub_df['quantity_filtered'].diff())
            # past_calmar_df.set_index('date')

        sub_df['profit_filtered'] = sub_df['delta_y'] * sub_df['quantity_filtered'] * 10000
        sub_df['profit_filtered'] -= (sub_df['quantity_change_abs'].fillna(0)) * cost_per_trade
        sub_df = sub_df.reset_index()
        # past_calmar_df[['profit_1000','profit_1100','profit_1130','profit_1215',
        #                 'profit_1345','profit_1430','profit_1500','profit_1600']].plot()
        # plt.show()
        if final_df.shape[0] == 0:
            final_df = sub_df
        else:
            final_df = final_df.merge(sub_df, on=list(sub_df), how='outer')
    return final_df, group_final_positions_ref


##### Scan #######
def scan_for_kpis_thresholds():
    positions = None
    weekdays_groups = [[0,1],[2,3,4]]
    weekdays_groups = [0,1,2,3,4]
    epsilon=0.75

    for method,val in [('calmar',calmar) for calmar in [-2,1,3,5,10,15]]+\
                        [('hr',hr) for hr in [0,0.2,0.4,0.6,0.7,0.8]]+\
                        [('sum',hr) for hr in [500,1000,2500]]:
        for window in [4,7,10,14,20]:
            if positions is None:
                final_df, positions = calc_final_df(method=method,threshold_val=val,window=window,weekdays_groups=weekdays_groups,
                                                    epsilon=epsilon)
            else:
                final_df, positions = calc_final_df(method=method, threshold_val=val, window=window,group_final_positions=positions,
                                                    weekdays_groups=weekdays_groups,
                                                    epsilon=epsilon)
            calmar,max_dd = calc_calmar(final_df['profit_filtered_simple'],use_annual=False)
            print ('Filtered Profit for (%s %s X %sD):\nPNL=%s | Calmar=%s | MaxDD=%s'%(method,val,window,
                                                                                        round(final_df['profit_filtered_simple'].sum()),
                                                                                        round(calmar,2),round(max_dd)))

def scan_stoploss(days = 100,
                trades_file_suffix = '_All',
                mode = 'ens_calmar10_S0.5_window7',
                cond_on_strats = lambda x: True,
                weekdays = [0,1,2,3,4],weekdays_groups=[0,1,2,3,4],
                hours_to_drop_from_original_profit=[],
                ref_col='quantity',
                  group_final_positions=None):

    if group_final_positions is None:
        group_final_positions = prepare_final_positions(days, mode, True, cond_on_strats, trades_file_suffix, weekdays,
                                                        min_score=0.5,debug=True)
        group_final_positions.to_csv(GROUP_FINAL_POSITIONS_PATH.replace('$MODE$', mode).replace('$DAYS$', str(days)),
                                     index=False)
    group_final_positions_ref = group_final_positions.copy()
    group_final_positions['weekday'] = group_final_positions.t_open.dt.weekday
    group_final_positions['quantity'] = np.clip(group_final_positions['quantity'], -MAX_CONTRACTS, MAX_CONTRACTS)

    group_final_positions['quantity_selected_hours'] = np.clip(group_final_positions['quantity'], -MAX_CONTRACTS, MAX_CONTRACTS)
    group_final_positions.loc[group_final_positions['t_open'].dt.hour.isin(hours_to_drop_from_original_profit),
                                                            'quantity_selected_hours'] = 0

    group_final_positions['profit'] = group_final_positions['quantity'] * group_final_positions['delta_y'] * 10 ** 4
    group_final_positions['profit'] -= abs(group_final_positions['quantity'].diff().fillna(0)) * cost_per_trade
    group_final_positions['profit_selected_hours'] = group_final_positions['quantity_selected_hours'] * group_final_positions['delta_y'] * 10 ** 4
    group_final_positions['profit_selected_hours'] -= abs(group_final_positions['quantity_selected_hours'].diff().fillna(0)) * cost_per_trade

    for hours in [(0,8),(6,8),(8,10),(10,11),(8,11),(8,12),(8,14),(12,13),(12,14),
                  (8,15),(8,16),(8,17),(0,12),(0,14)
                  ]:
        for activation_conds in [
                                 [{'takeprofit': 0}], [{'takeprofit': -300}], [{'takeprofit': -500}],[{'takeprofit': -750}], [{'takeprofit': -1000}], [{'takeprofit': -1500}],
                                 [{'takeprofit': -2500}], [{'takeprofit': -3300}], [{'takeprofit': 1000}],[{'takeprofit': 1500}], [{'takeprofit': 2000}], [{'takeprofit': 3000}],
                                 # [{'takeprofit': 4000}],[{'takeprofit': 5000}],
                                 # [{'takeprofit': 7500}],
                            [{'absolute': 0}], [{'absolute': -300}], [{'absolute': -500}], [{'absolute': -750}],
                            [{'absolute': -1000}], [{'absolute': -1500}],
                            # [{'absolute': -2500}], [{'absolute': -3300}], [{'absolute': 1000}], [{'absolute': 1500}],
                            # [{'absolute': 2000}], [{'absolute': 3000}],
                            # [{'absolute': 4000}], [{'absolute': 5000}],
                            # [{'absolute': 7500}],
                            # [{'absolute': 3000},{'takeprofit': 1500}],
                            [{'dynamic':0}],
                                 ][-1:]:
            try:
                suffix = '%s_x_'%(str(hours)) +"_".join([str(activation_cond['absolute']) for activation_cond in
                                           activation_conds])
            except:
                suffix = '%s_x_' % (str(hours)) + "_".join([str(activation_cond[list(activation_cond.keys())[0]]) for activation_cond in
                                                            activation_conds])
            for conf in activation_conds:
                conf['hours'] = hours
            group_final_positions = add_stoploss_strategy(group_final_positions,
                                                              activation_conds=activation_conds,
                                                              ref_col=ref_col,stoploss_col_suffix=suffix)
    for activation_conds in [
                    # [{'absolute': -500,'hours':(0,h)} for h in range(6,19)],
                    [{'absolute': -500,'hours':(10,11)},{'takeprofit': tp,#2400,
                                                         'hours':(8,14)#(h1,h2)
                                                         }]
                    for tp in [1200,2400,3600,4800]
                    # for h1 in [0,6,8,11]
                    # for h2 in [11,12,13,14,15,16,17,18]
                    ]:
        suffix = ''
        for activation_cond in activation_conds:
            suffix+='_%s'%str(activation_cond['hours'])
            if 'absolute' in activation_cond.keys():
                suffix += '_abs%s'%activation_cond['absolute']
            elif 'takeprofit' in activation_cond.keys():
                suffix += '_tp%s' % activation_cond['takeprofit']
        group_final_positions = add_stoploss_strategy(group_final_positions,
                                                      activation_conds=activation_conds,
                                                      ref_col=ref_col, stoploss_col_suffix=suffix)
    print(group_final_positions[[x for x in list(group_final_positions)
                                 if 'profit' in x]].sum())
    return group_final_positions


mode = 'ens_calmar3_S0.75_window4'
mode = 'ens_calmar0.25_S0.87_window4'
# mode = 'real'
days = 250
final_hours_to_drop = []  # [11,14,16,19] #[]
calc = False
if not calc:
    try:
        group_final_positions = pd.read_csv(GROUP_FINAL_POSITIONS_PATH.replace('$MODE$', mode).replace('$DAYS$', str(days)), parse_dates=['t_open'])
    except:
        group_final_positions = None
else:
    group_final_positions = None
df = scan_stoploss(days, group_final_positions=group_final_positions,trades_file_suffix='_All',
                   mode=mode)
# raise
df.to_csv(os.path.join(HOME,"performance_analysis","Stoploss_scan.csv"),index=False)
df = pd.read_csv(os.path.join(HOME,"performance_analysis","Stoploss_scan.csv"),parse_dates=['t_open'])
df.loc[df['t_open'].dt.hour.isin(final_hours_to_drop),[x for x in list(df) if 'profit' in x]] = 0
kpis_df = pd.DataFrame({'Profit':df[[x for x in list(df) if 'profit' in x]].sum(),
              'calmar':[calc_calmar(df[xx],use_annual=False)[0] for xx in [x for x in list(df) if 'profit' in x]],
              'Max_DD':[calc_calmar(df[xx],use_annual=False)[1] for xx in [x for x in list(df) if 'profit' in x]],
              'Sharpe':[calc_sharpe(df[['t_open',xx]].groupby(df['t_open'].dt.date).sum()) for xx in [x for x in list(df) if 'profit' in x]],
              'HR_Daily':[((df[['t_open',xx]].groupby(df['t_open'].dt.date).sum()[[xx]].replace(0,np.nan)).dropna() > 0).mean()[xx] for xx in [x for x in list(df) if 'profit' in x]],
                })
kpis_dfs_dict = {}
for d in [0,1,2,3,4]:
    tmp_df = df.loc[df['t_open'].dt.weekday == d]
    kpis_dfs_dict[d] = pd.DataFrame({'Profit':tmp_df[[x for x in list(tmp_df) if 'profit' in x]].sum(),
              'calmar':[calc_calmar(tmp_df[xx],use_annual=False)[0] for xx in [x for x in list(tmp_df) if 'profit' in x]],
              'Max_DD':[calc_calmar(tmp_df[xx],use_annual=False)[1] for xx in [x for x in list(tmp_df) if 'profit' in x]]})

### Daily rolling calmarr
#np.clip((df[['t_open']+[x for x in list(df) if 'profit' in x]].loc[df['t_open'].dt.weekday==0].groupby(df['t_open'].dt.date).sum()).rolling(15,3).apply(rolling_calmar),-50,50)
## Daily PNL by stoploss
# df[['t_open']+[x for x in list(df) if 'profit' in x and ('-500' in x or x == 'profit')]].loc[df['t_open'].dt.weekday.isin([4])
# ].groupby(df['t_open'].dt.date).sum().cumsum().plot(style=['-']*6+['-*']*6+['--']*6)
# plt.show()
profit_cols = ['profit_stoploss(10, 11)_x_-500','profit_stoploss(10, 11)_x_-1000',
                        'profit_stoploss_(10, 11)_abs-500_(8, 14)_tp2400',
                        'profit_stoploss_(10, 11)_abs-500_(8, 14)_tp3600',
                        'profit_stoploss_(10, 11)_abs-500_(8, 14)_tp4800',
                        'profit']
profit_cols = [x for x in list(df) if ('profit' in x and '-500' in x) or x=='profit']
df.set_index('t_open')[profit_cols].cumsum().rolling(20,1).mean().plot(style=['-']*7+['-*']+['--']*7)
# df.set_index('t_open')[profit_cols].rolling(4*24*30,4*24*30).sum().plot(style=['-']*7+['-*']+['--']*7)
plt.show()
# take profit 8-12 x 1500
# stoploos 10-11 x -3000
# try to combine

raise
bb= 0

######### original ######
if __name__ == '__main__':
    mode = 'ens_calmar3_S0.75_window4'
    days = 150
    if CALC_FROM_NEW:
        if USE_GROUP:
            try:
                group_final_positions = pd.read_csv(GROUP_FINAL_POSITIONS_PATH.replace('$MODE$', mode).replace('$DAYS$', str(days)), parse_dates=['t_open'])
            except:
                group_final_positions = None
        else:
            group_final_positions = None
        final_df, group_final_positions = calc_final_df(mode=mode,group_final_positions=group_final_positions,
                                                        days=days,
                                                        hours_to_drop_from_original_profit=[11,16,19])
    else:
        final_df = pd.read_csv(CSV_PATH,parse_dates=['t_open'])


    final_df = final_df.sort_values('t_open').set_index('t_open')
    final_df.reset_index().to_csv(CSV_PATH)
    group_final_positions.to_csv(GROUP_FINAL_POSITIONS_PATH.replace('$MODE$',mode).replace('$DAYS$',str(days)),index=False)

    if PLOT:
        final_df[[x for x in list(final_df) if 'profit' in x]].fillna(0).cumsum().plot(title='profits comparison')
        plt.show()

    final_df.reset_index(inplace=True)

    final_df.loc[final_df['t_open'].dt.hour.isin([]),[x for x in list(final_df) if 'profit' in x]] = 0
    final_df_1h = final_df.groupby(final_df['t_open'].dt.strftime('%Y%m%d%H')).sum()[[x for x in list(final_df) if 'profit' in x]]
    final_df_1d = final_df.groupby(final_df['t_open'].dt.date).sum()[[x for x in list(final_df) if 'profit' in x]]
    calmar_15m = [calc_calmar(final_df[xx],use_annual=False) for xx in [x for x in list(final_df) if 'profit' in x]]
    max_dd_15m = [calc_calmar(final_df[xx],use_annual=False) for xx in [x for x in list(final_df) if 'profit' in x]]
    calmar_1h = [calc_calmar(final_df_1h[xx],use_annual=False) for xx in [x for x in list(final_df) if 'profit' in x]]
    calmar_1d = [calc_calmar(final_df_1d[xx],use_annual=False) for xx in [x for x in list(final_df) if 'profit' in x]]

    calmar_df = pd.DataFrame({
                              'calmar_15m':[x[0] for x in calmar_15m],
                              'calmar_1h':[x[0] for x in calmar_1h],
                                  'calmar_1d':[x[0] for x in calmar_1d],
                                  'max_dd_15m':[x[1] for x in calmar_15m],
                                  'max_dd_1h':[x[1] for x in calmar_1h],
                                  'max_dd_1d':[x[1] for x in calmar_1d],
                                  },index=[x for x in list(final_df) if 'profit' in x])


    print('Original | Profit 15min ===>> PNL=%s | Calmar=%s | MaxDD=%s' % (round(final_df['profit'].sum()),
                                                                  round(calmar_df['calmar_15m'].loc['profit'], 2), round(calmar_df['max_dd_15m'].loc['profit'])))
    print('Selected_Hours | Profit 15min ===>> PNL=%s | Calmar=%s | MaxDD=%s' % (round(final_df['profit_selected_hours'].sum()),
                                                                  round(calmar_df['calmar_15m'].loc['profit_selected_hours'], 2), round(calmar_df['max_dd_15m'].loc['profit_selected_hours'])))
    print('StopLoss | Profit 15min ===>> PNL=%s | Calmar=%s | MaxDD=%s' % (round(final_df['profit_stoploss'].sum()),
                                                                  round(calmar_df['calmar_15m'].loc['profit_stoploss'], 2), round(calmar_df['max_dd_15m'].loc['profit_stoploss'])))

    if PLOT:
        calmar_df[['calmar_15m', 'calmar_1h', 'calmar_1d']].plot(kind='bar',title='Calmar')
        plt.tight_layout()
        calmar_df[['max_dd_15m', 'max_dd_1h', 'max_dd_1d']].plot(kind='bar',title='Max DD')
        plt.tight_layout()
        plt.show()