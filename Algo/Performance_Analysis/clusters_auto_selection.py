import copy

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT,DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR
from Algo.Learning.research_plays import rolling_calmar
from Algo.Utils.files_handle import get_clusters_performance_csv_by_mode
from matplotlib import pyplot as plt
from Algo.Performance_Analysis.cluster_selection_definitions import COST_PER_TRADE,CONFIG,CLIP_AFTER,INITIAL_CLIP
from Algo.Utils.files_handle import get_candles_outpath
import os
from Algo.Utils.files_handle import HOME


DEFAULT_DICT_FOR_DROP_LOSING_HOURS = dict(resolution = '%H',
                                        bigloss_window = 5,
                                        bigloss_threshold = 500,
                                        trend_filter=None
                                            )
NO_ACTION_DICT_FOR_DROP_LOSING_HOURS = dict(resolution = '%H',
                                        bigloss_window = None,
                                        bigloss_threshold = None,
                                        trend_filter=None
                                            )

DICT_FOR_DROP_LOSING_HOURS_W3 = dict(resolution = '%H',
                                        bigloss_window = 3,
                                        bigloss_threshold = 500,
                                        trend_filter=None
                                            )

DICT_FOR_DROP_LOSING_HOURS_W1 = dict(resolution = '%H',
                                        bigloss_window = 1,
                                        bigloss_threshold = 450,
                                        trend_filter=None
                                            )
DICT_FOR_DROP_LOSING_HOURS_W4 = dict(resolution = '%H',
                                        bigloss_window = 4,
                                        bigloss_threshold = 450,
                                        trend_filter=None
                                            )

DICT_FOR_DROP_LOSING_HOURS_W4_T350 = dict(resolution = '%H',
                                        bigloss_window = 4,
                                        bigloss_threshold = 350,
                                        trend_filter=None
                                            )

DICT_FOR_DROP_LOSING_HOURS_T350 = dict(resolution = '%H',
                                        bigloss_window = 5,
                                        bigloss_threshold = 350,
                                        trend_filter=None
                                            )
DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350 = dict(resolution = '%H',
                                        bigloss_window = 5,
                                        bigloss_threshold = 350,
                                        trend_filter=None,
                                        weekdays_subsets=[([[0,1,2]],600),
                                                          ([[0],[1],[2],[3],[4]],350),]
                                            )

DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER = dict(resolution = '%H',
                                        bigloss_window = 5,
                                        bigloss_threshold = 350,
                                        trend_filter=None,
                                        weekdays_subsets=[([[0,4]],400,3),([[1,2,3]],400,3),
                                                          ([[0,1,2,3,4]],300,5),
                                                          ([[0]],250,3),([[1]],250,3),]
                                            )

# todo !!!! this is supposed to be the fina final conf
# problem is we while DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350 matches the window5 of the charts
# when adding the trend_filter + dynamic bigloss we lose contact!
# must debug and find the bug
DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_FULL = dict(resolution = '%H',
                                        bigloss_window = ([3,4,5],18), # windows to check, on which rolling window
                                        bigloss_threshold = 350,
                                        weekdays_subsets=[([[0,1,2]],750),
                                                          ([[0],[1],[2],[3],[4]],350),],
                                        trend_filter={'w1':5,'w2':20,
                                                      'short_term_w1':3,'short_term_w2':6,
                                                      'bear_cond3_limit': -1000}
                                            )

V5_DICT_FOR_DROP_LOSING_HOURS = dict(resolution = '%H',
                                        bigloss_window = ([3,4,5],18), # windows to check, on which rolling window
                                        bigloss_threshold = 500,
                                        trend_filter={'w1':5,'w2':20,
                                                      'short_term_w1':3,'short_term_w2':6,
                                                      'bear_cond3_limit': -1000})
V5b_DICT_FOR_DROP_LOSING_HOURS = dict(resolution = '%H',
                                        bigloss_window = ([3,4,5],18), # windows to check, on which rolling window
                                        bigloss_threshold = 500,
                                        trend_filter=None
                                            )

DAYS_FOR_CLUSTER_PERFORMANCE = 365 # 250 # 365 todo
#DAYS_FOR_CLUSTER_PERFORMANCE = '365ExtEPS2' # 250 # 365 todo !

ROUND_HOURS_DIVISION_OF_DAY=[(0, 4),(4,6),(6,8), (8, 9),(9,10), (10, 11), (11, 12),
                                 (12, 13), (13, 14), (14, 15),
                                 (15, 16),(16,17),(17, 18), (18, 20),
                                 (8, 14), (14, 20)]
ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE =[(h,h+1) for h in range(0,20,1)]
DUMMY_DIVISION_OF_DAY_UNIQUE_0814 =[(8,14)]
DUMMY_DIVISION_OF_DAY_UNIQUE =[(0,24)]

ALL_15MIN_DT_SERIES = pd.Series(pd.date_range(dtdt(1990,6,28),dtdt(1990,6,28,23,45),freq='15min'))
ALL_15MIN_HHMM = ALL_15MIN_DT_SERIES.dt.strftime('%H%M').tolist()
AM_15MIN_HHMM = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(4,10))].dt.strftime('%H%M').tolist()
AM_15MIN_HHMM_SMALL = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(4,8))].dt.strftime('%H%M').tolist()
NOON_15MIN_HHMM_SMALL = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(10,15))].dt.strftime('%H%M').tolist()
NOON_15MIN_HHMM_EARLY_SMALL = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(8,12))].dt.strftime('%H%M').tolist()
NOON_15MIN_HHMM = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(8,15))].dt.strftime('%H%M').tolist()
DAILY_15MIN_HHMM = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(8,18))].dt.strftime('%H%M').tolist()
MIDDAY_15MIN_HHMM = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(14,18))].dt.strftime('%H%M').tolist()
EVENING_15MIN_HHMM = ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(16,20))].dt.strftime('%H%M').tolist()

AM_DIVISION_15MIN_OF_DAY=[AM_15MIN_HHMM_SMALL[i:i+1] for i in range(0,len(AM_15MIN_HHMM_SMALL))]
AM_DIVISION_30MIN_OF_DAY=[AM_15MIN_HHMM[i:i+2] for i in range(0,len(AM_15MIN_HHMM)-1,2)]
NOON_DIVISION_15MIN_OF_DAY_SMALL=[NOON_15MIN_HHMM_SMALL[i:i+1] for i in range(0,len(NOON_15MIN_HHMM_SMALL))]
NOON_DIVISION_15MIN_OF_DAY_EARLY_SMALL=[NOON_15MIN_HHMM_EARLY_SMALL[i:i+1] for i in range(0,len(NOON_15MIN_HHMM_EARLY_SMALL))]
NOON_DIVISION_15MIN_OF_DAY=[NOON_15MIN_HHMM[i:i+1] for i in range(0,len(NOON_15MIN_HHMM))]
NOON_DIVISION_30MIN_OF_DAY=[NOON_15MIN_HHMM[i:i+2] for i in range(0,len(NOON_15MIN_HHMM)-1,2)]

NOON_DIVISION_30MIN_OF_DAY_FULL=[NOON_15MIN_HHMM_SMALL[i:i+2] for i in range(0,len(NOON_15MIN_HHMM_SMALL)-1,1)]


NOON_DIVISION_30MIN_OF_DAY_0045=[[DAILY_15MIN_HHMM[i],DAILY_15MIN_HHMM[i+3]] for i in range(0,len(DAILY_15MIN_HHMM)-1,4)]
NOON_DIVISION_30MIN_OF_DAY_1530=[[DAILY_15MIN_HHMM[i],DAILY_15MIN_HHMM[i+1]] for i in range(1,len(DAILY_15MIN_HHMM)-1,4)]
NOON_DIVISION_30MIN_OF_DAY_0045_1530 = NOON_DIVISION_30MIN_OF_DAY_0045+NOON_DIVISION_30MIN_OF_DAY_1530


AM_DIVISION_45MIN_OF_DAY=[AM_15MIN_HHMM[i:i+3] for i in range(0,len(NOON_15MIN_HHMM)-1,2)]
MIDDAY_DIVISION_15MIN_OF_DAY=[MIDDAY_15MIN_HHMM[i:i+1] for i in range(0,len(MIDDAY_15MIN_HHMM))]
MIDDAY_DIVISION_30MIN_OF_DAY=[MIDDAY_15MIN_HHMM[i:i+2] for i in range(0,len(MIDDAY_15MIN_HHMM)-1,2)]
MIDDAY_DIVISION_30MIN_OF_DAY_HH15 = [MIDDAY_15MIN_HHMM[i+1:i+3] for i in range(0,len(MIDDAY_15MIN_HHMM)-1,2)]
MIDDAY_DIVISION_45MIN_OF_DAY = [MIDDAY_15MIN_HHMM[i:i+3] for i in range(0,len(MIDDAY_15MIN_HHMM)-1,2)]
EVENING_DIVISION_15MIN_OF_DAY=[EVENING_15MIN_HHMM[i:i+1] for i in range(0,len(EVENING_15MIN_HHMM),1)]
EVENING_DIVISION_30MIN_OF_DAY=[EVENING_15MIN_HHMM[i:i+2] for i in range(0,len(EVENING_15MIN_HHMM)-1,2)]
EVENING_DIVISION_30MIN_OF_DAY_FULL=[EVENING_15MIN_HHMM[i:i+2] for i in range(0,len(EVENING_15MIN_HHMM)-1,1)]
EVENING_DIVISION_45MIN_OF_DAY=[EVENING_15MIN_HHMM[i:i+3] for i in range(0,len(EVENING_15MIN_HHMM)-1,2)]

DIVISION_15MIN_OF_DAY=[ALL_15MIN_HHMM[i:i+1] for i in range(0,len(ALL_15MIN_HHMM)-1)]
DIVISION_30MIN_OF_DAY=[ALL_15MIN_HHMM[i:i+2] for i in range(0,len(ALL_15MIN_HHMM)-1,2)]
DIVISION_45MIN_OF_DAY=[ALL_15MIN_HHMM[i:i+3] for i in range(0,len(ALL_15MIN_HHMM)-1,3)]
DIVISION_60MIN_OF_DAY_HH00=[ALL_15MIN_HHMM[i:i+4] for i in range(0,len(ALL_15MIN_HHMM)-1,4)]
DIVISION_60MIN_OF_DAY_HH15=[ALL_15MIN_HHMM[i+1:i+5] for i in range(0,len(ALL_15MIN_HHMM)-1,4)]
DIVISION_60MIN_OF_DAY_HH30=[ALL_15MIN_HHMM[i+2:i+6] for i in range(0,len(ALL_15MIN_HHMM)-1,4)]
DIVISION_60MIN_OF_DAY_HH45=[ALL_15MIN_HHMM[i+3:i+7] for i in range(0,len(ALL_15MIN_HHMM)-1,4)]

DIVISION_30MIN_OF_DAY_HYBRID = sorted([x for x in DIVISION_30MIN_OF_DAY if
                                int(x[0][:2]) in [6,7,13,15,16,18]]+[
                                x for x in DIVISION_60MIN_OF_DAY_HH00
                                if int(x[0][:2]) not in [4,5,7,13,15,16,18]],
                                      key=lambda x: x[0])

FILTER_DICT_CONFS = {
                  'ens_cluster_dynamic_v4_S0.5_w9_hybrid_full': DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_FULL,
                  'ens_cluster_dynamic_v4_S0.5_w9_hybrid': DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350,
                  'ens_cluster_dynamic_v5_S0.5_dynamic': V5_DICT_FOR_DROP_LOSING_HOURS,
                  'ens_cluster_dynamic_v4_S0.5_w9': NO_ACTION_DICT_FOR_DROP_LOSING_HOURS,
                  'ens_cluster_dynamic_v3_S0.5_w9': NO_ACTION_DICT_FOR_DROP_LOSING_HOURS}


def handle_config(weekdays_tuple,window,epsilon):
    """
    :param weekdays_tuple:
    :param window:
    :param epsilon:
    :return:
    """
    final_profit = pd.DataFrame()
    weekdays_lst = weekdays_tuple[0]
    weekdays_name = weekdays_tuple[1]
    for weekdays_subset in weekdays_lst:
        weekday_profit = pd.DataFrame()
        hours_subset = [(0, 8), (8, 11), (11, 13), (13, 15), (15, 18), (18, 20)]
        # hours_subset = [(0, 8), (8, 11),(11,14),(14, 16), (16, 20)] # original!
        # hours_subset = [(0, 20), (20, 24)][:1]
        for i, (hour_start, hour_end) in enumerate(hours_subset):

            tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                            (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))].copy()
            daily_df = tmp_df.groupby(tmp_df['t_open'].dt.date).sum()
            profits_daily = daily_df[[x for x in list(daily_df) if 'profit' in x]]
            # profits_df = df.set_index('t_open')[[x for x in list(df) if 'profit' in x]]

            rolling_performance = [profits_daily[c].rolling(window, 3).apply(lambda x: rolling_calmar(x))
                                   for c in list(profits_daily)]

            rolling_performance = pd.DataFrame(rolling_performance, index=list(profits_daily)).T.shift(1)

            rolling_performance += epsilon * rolling_performance
            quantities_by_day = rolling_performance.copy().fillna(0) * 0
            quantities_by_day[(rolling_performance > 0) & (rolling_performance.diff() > 0)] = 1
            quantities_by_day[(rolling_performance > 0) & (rolling_performance.diff() <= 0)] = 0.5
            quantities_by_day[(rolling_performance < 0) & (rolling_performance.diff() <= 0)] = 0
            quantities_by_day[(rolling_performance < 0) & (rolling_performance.diff() > 0)] = 0.5

            quantities_by_day[(rolling_performance > 0) & (rolling_performance.diff() <= -2)] = 0
            quantities_by_day[(rolling_performance < -3) & (rolling_performance.diff() > 0)] = 0

            new_profits_daily = (profits_daily * quantities_by_day).reset_index()
            if weekday_profit.shape[0] == 0:
                weekday_profit = new_profits_daily
            else:
                weekday_profit = (
                            weekday_profit.set_index('t_open') + new_profits_daily.set_index('t_open')).reset_index()

        if final_profit.shape[0] == 0:
            final_profit = weekday_profit
        else:
            common = list(set(list(weekday_profit)).intersection(set(list(final_profit))))
            final_profit = final_profit.merge(weekday_profit, on=common, how='outer')
    final_profit = final_profit.sort_values('t_open').set_index('t_open')
    return final_profit

def handle_config_v2(mode,weekdays_tuple,window,epsilon,clip,multiplier=10**4,df=None,
                     kpi='sum',limit_for_split=0,days_back=0,
                     cond_on_hours=lambda x: True,
                     cancel_leakage_shift=False):
    """
    :param weekdays_tuple:
    :param window:
    :param epsilon:
    :return:
    """
    if df is None:
        clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=DAYS_FOR_CLUSTER_PERFORMANCE)
        df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])

    if days_back > 0:
        df = df[df['t_open']<= dtdt.now()-td(days=days_back)]
    df = df.fillna(0).drop_duplicates()
    df['profit_All_abs'] = abs(df['profit_All'])
    df = df.sort_values(['t_open', 'profit_All_abs']).drop_duplicates(subset=['t_open'], keep='last').drop(['profit_All_abs'],axis=1)
    # I thought we had to reduce here, but was wrong, we take care of in line 160-170!
    # profit_cols = [x for x in list(df) if 'profit' in x]
    # for profit_col in profit_cols:
    #     quantity_col = profit_col.replace('profit_','quantity_')
    #     df[profit_col] -= abs(quantity_col.diff()) * COST_PER_TRADE

    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 15), (15, 18), (18, 20)]
    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v2 = [(0, 8), (8, 10), (10, 12),(12,14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v3 = [(0, 8), (8, 11), (11, 12),(12,13), (13, 14), (14, 15), (15, 17),(17,18), (18, 20)]
    hours_subset_v4 = [(0, 8), (8, 10), (10,11), (11, 12),(12,13), (13, 14), (14, 15), (15, 17),(17,18), (18, 20)]
    hours_subset_v5 = [(0, 4),(4,6),(6,7),(7,8), (8, 9),(9,10),(10,11), (11, 12),(12,13), (13, 14), (14, 15), (15, 16),(16,17),(17,18), (18, 20)]
    hours_subset = hours_subset_v2

    final_profit = pd.DataFrame()
    weekdays_lst = weekdays_tuple[0]
    weekdays_name = weekdays_tuple[1]
    last_week_dict = {}
    overall_quantities_lst = []
    for weekdays_subset in weekdays_lst:
        weekday_profit = pd.DataFrame()
        if weekdays_subset[0] not in last_week_dict.keys():
            last_week_dict[weekdays_subset[0]] = {}

        quantities_by_hour_first_previous = None
        quantities_by_hour_last_previous = None

        hours_tuples_to_check = [(hour_start, hour_end)  for hour_start, hour_end in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                        (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                         )].shape[0] > 0
                                        and
                                         (ALL_15MIN_DT_SERIES[ALL_15MIN_DT_SERIES.dt.hour.isin(range(hour_start, hour_end))].apply(
                                             cond_on_hours).sum() > 0)
                                 ]

        for i, (hour_start, hour_end) in enumerate(hours_tuples_to_check):

            if (hour_start, hour_end) not in last_week_dict[weekdays_subset[0]].keys():
                last_week_dict[weekdays_subset[0]][(hour_start, hour_end)] = {}

            tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                            (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                            # &(df['t_open'].dt.hour.apply(cond_on_hours))
                            ].copy()

            cost_reduction_multiplier = 1
            if tmp_df.shape[0] == 0:
                continue
            elif tmp_df.groupby(tmp_df['t_open'].dt.date).size().mean() < 4:
                # we are inside specific hour and need to reduce position closing too
                cost_reduction_multiplier = 2

            daily_df = tmp_df.groupby(tmp_df['t_open'].dt.date).sum()
            profits_daily = daily_df[[x for x in list(daily_df) if 'profit' in x and 'All' not in x and 'Total' not in x]]

            if kpi == 'calmar':
                rolling_performance = [profits_daily[c].rolling(window, 3).apply(lambda x: rolling_calmar(x))
                                   for c in list(profits_daily)]
            elif kpi == 'sum':
                rolling_performance = [profits_daily[c].rolling(window, 3).sum()
                                       for c in list(profits_daily)]

            # we add next week artificially in order to have the chosen strats and not lose the current week performance due to shift
            rolling_performance = pd.DataFrame(rolling_performance, index=list(profits_daily)).T.reset_index().merge(pd.DataFrame({'t_open':[max(rolling_performance[0].index.tolist())+td(days=7)]}),how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)

            rolling_performance += epsilon * rolling_performance

            quantities_by_day = rolling_performance.copy().fillna(0) * 0
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() > limit_for_split)] = 1
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() <= limit_for_split)] = 0.5
            quantities_by_day[(rolling_performance < limit_for_split) & (rolling_performance.diff() <= limit_for_split)] = 0
            quantities_by_day[(rolling_performance < limit_for_split) & (rolling_performance.diff() > limit_for_split)] = 1 # used to be 0.5 until 04.07.2022
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() <= -2)] = 0

            # checked this, didnt improve....
            # quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() > 100)&(rolling_performance.diff(2) > 100)] = 2

            last_chosen_clusters = [(x.replace('profit_', ''), y) for (x, y) in list(zip(quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0].index,quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0]))]
            last_week_dict[weekdays_subset[0]][(hour_start,hour_end)] = last_chosen_clusters

            # now we can drop the future fake rows
            quantities_by_day = quantities_by_day[:-1]

            quantities_by_day_new = quantities_by_day.reset_index()
            quantities_by_day_new = quantities_by_day_new.rename(columns={c: c.replace('profit','quantity')
                                                                          for c in list(quantities_by_day_new)})
            quantities_by_day_new['t_open'] = pd.to_datetime(quantities_by_day_new['t_open'])
            quantities_by_hour = quantities_by_day_new.merge(tmp_df,on=['t_open'],suffixes=('_daily',''),how='outer')
            quantities_by_hour = quantities_by_hour.sort_values('t_open').fillna(method='ffill')

            daily_quantity_cols = [x for x in list(quantities_by_hour) if '_daily' in x]
            final_quantities_cols = [x.replace('_daily','') for x in list(quantities_by_hour) if '_daily' in x]
            for daily_q,final_q in zip(daily_quantity_cols,final_quantities_cols):
                quantities_by_hour[final_q] *= quantities_by_hour[daily_q]
            quantities_by_hour['total_quantity'] = np.clip(quantities_by_hour[final_quantities_cols].sum(axis=1),
                                                           -clip,clip)

            quantities_by_hour = quantities_by_hour[(quantities_by_hour['t_open'].apply(cond_on_hours))&(quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000')]

            quantities_by_hour_copy = quantities_by_hour.copy()
            quantities_by_hour_copy['date'] = quantities_by_hour_copy['t_open'].dt.date
            quantities_by_hour_first = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']][quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000'].drop_duplicates(subset=['date'],keep='first')
            quantities_by_hour_last = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']].drop_duplicates(subset=['date'], keep='last')

            quantities_by_hour['abs_change'] = abs(quantities_by_hour['total_quantity'].diff()).fillna(1)
            inner_quantity_changes_by_day = quantities_by_hour[['t_open', 'total_quantity', 'abs_change']]
            inner_quantity_changes_by_day.loc[inner_quantity_changes_by_day['t_open'].dt.strftime('%H%M') == '0000', 'total_quantity'] = 0
            inner_quantity_changes_by_day['abs_change'] = abs(inner_quantity_changes_by_day['total_quantity'].diff()).fillna(0)
            inner_quantity_changes_by_day = inner_quantity_changes_by_day[quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000'].groupby(quantities_by_hour['t_open'].dt.date).sum()['abs_change']

            total_quantity_changes_by_day = inner_quantity_changes_by_day
            if quantities_by_hour_first_previous is not None:
                outer_quantity_changes_by_day = abs(quantities_by_hour_first.set_index('date')['total_quantity'] - quantities_by_hour_last_previous.set_index('date')['total_quantity'])
                total_quantity_changes_by_day = inner_quantity_changes_by_day + outer_quantity_changes_by_day
            else:
                bb = 0
            # old method we reduce here
                # quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity'] - quantities_by_hour['abs_change'] * COST_PER_TRADE
            quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity']

            # old method is when cost per trade is reduces a few linew above
                # new_profits_daily = (quantities_by_hour[['t_open','total_profit']].groupby(quantities_by_hour['t_open'].dt.date).sum()['total_profit']).to_frame().reset_index()
            new_profits_daily = (quantities_by_hour[['t_open','total_profit']].groupby(quantities_by_hour['t_open'].dt.date).sum()['total_profit'] - (total_quantity_changes_by_day * COST_PER_TRADE * cost_reduction_multiplier)).to_frame().reset_index()
            new_profits_daily.columns = ['t_open','total_profit']
            if weekday_profit.shape[0] == 0:
                weekday_profit = new_profits_daily.fillna(0)
            else:
                weekday_profit = weekday_profit.merge(new_profits_daily,on=['t_open'],how='outer',suffixes=('','_new')).fillna(0)
                weekday_profit['total_profit'] = weekday_profit[['total_profit','total_profit_new']].sum(axis=1)
                weekday_profit = weekday_profit[['t_open','total_profit']]

            quantities_by_hour_first_previous = quantities_by_hour_first.copy()
            quantities_by_hour_last_previous = quantities_by_hour_last.copy()

        if final_profit.shape[0] == 0:
            final_profit = weekday_profit
        else:
            common = list(set(list(weekday_profit)).intersection(set(list(final_profit))))
            final_profit = final_profit.merge(weekday_profit, on=common, how='outer')
    final_profit = final_profit.sort_values('t_open').set_index('t_open')
    return final_profit, last_week_dict


def handle_config_v3(mode,weekdays_tuple,window,epsilon,clip,multiplier=10**4,df=None,
                     kpi='sum',limit_for_split=0,days_back=0,
                     cond_on_hours=lambda x: True,
                     cancel_leakage_shift=False,
                     hours_subset_override=None,
                     clip_after=CLIP_AFTER,initial_clip=INITIAL_CLIP,
                     days=DAYS_FOR_CLUSTER_PERFORMANCE,
                     big_loss_value=800,
                     block_14_utc=True,
                     hours_subset_conf='v4'):
    """
    :param weekdays_tuple:
    :param window:
    :param epsilon:
    :return:
    """
    if big_loss_value is None:
        big_loss_value = 1e6
    if df is None:
        clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=days)
        df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])

    if days_back > 0:
        df = df[df['t_open']<= dtdt.now()-td(days=days_back)]
    df = df.fillna(0).drop_duplicates()
    df['profit_All_abs'] = abs(df['profit_All'])
    df = df.sort_values(['t_open', 'profit_All_abs']).drop_duplicates(subset=['t_open'], keep='last').drop(['profit_All_abs'],axis=1)

    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 15), (15, 18), (18, 20)]
    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v2 = [(0, 8), (8, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v3 = [(0, 8), (8, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
    hours_subset_v4 = [(0, 8), (8, 10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
    hours_subset_v4b = [(0, 4),(4,6),(6,7),(7,8), (8, 9),(9,10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 16),(16,17), (17, 18), (18, 20)]

    # v4 with window 9 was even better, but v5 keps things more tight and stable for different windows
    # we go with v5
    hours_subset_v5 = [(0, 4),(4,6),(6,7),(7,8), (8, 9),(9,10),(10,11), (11, 12),(12,13), (13, 14), (14, 15), (15, 16),(16,17),(17,18), (18, 20)]
    hour_min_15_subset_v6 = ['%s%s'%(str(h).zfill(2),mm) for h in range(24)
                              for mm in ['00','15','30','45']]
    hours_subset = eval(f"hours_subset_{hours_subset_conf}")
    if hours_subset_override is not None:
        if hours_subset_override == 'v3':
            hours_subset = hours_subset_v3
        elif hours_subset_override == 'v4':
            hours_subset = hours_subset_v4
        elif hours_subset_override == 'v5':
            hours_subset = hours_subset_v5
        else:
            raise AssertionError('invalid hours_subset_override')

    hours_subset_type = 'hour'
    if isinstance(hours_subset[0],str):
        hours_subset_type = '15min'

    final_profit = pd.DataFrame()
    weekdays_lst = weekdays_tuple[0]

    last_week_dict = {}
    for weekdays_subset in weekdays_lst:
        #print('Handling weekday: %s'%weekdays_subset)
        weekday_profit = pd.DataFrame()
        if weekdays_subset[0] not in last_week_dict.keys():
            last_week_dict[weekdays_subset[0]] = {}

        quantities_by_hour_first_previous = None
        quantities_by_hour_last_previous = None

        if isinstance(hours_subset[0],tuple):
            hours_tuples_to_check = [(hour_start, hour_end) for hour_start, hour_end in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                         (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                         )].shape[0] > 0
                                 and
                                 (ALL_15MIN_DT_SERIES[
                                      ALL_15MIN_DT_SERIES.dt.hour.isin(range(hour_start, hour_end))].apply(
                                     cond_on_hours).sum() > 0)
                                 ]
        elif isinstance(hours_subset[0],str):
            hours_tuples_to_check = [(hour_start, '') for hour_start in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                         (df['t_open'].dt.strftime('%H%M') == hour_start)
                                         )].shape[0] > 0
                                 and
                                 (ALL_15MIN_DT_SERIES[
                                      ALL_15MIN_DT_SERIES.dt.strftime('%H%M') == hour_start].apply(
                                     cond_on_hours).sum() > 0)
                                 ]

        for i, item in enumerate(hours_tuples_to_check):
            hour_start, hour_end = item
            # print('handling weekday %s X hour %s'%(weekdays_subset,item))
            if hour_start == 12:
                bb = 0
            if hours_subset_type == 'hour':
                (hour_start, hour_end) = item
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                # &(df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            elif hours_subset_type == '15min':
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.strftime('%H%M')==hour_start)
                                # &(df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            else:
                raise AssertionError('invalid hours_subset_type: %s'%hours_subset_type)

            if item not in last_week_dict[weekdays_subset[0]].keys():
                last_week_dict[weekdays_subset[0]][item] = {}

            cost_reduction_multiplier = 1

            if tmp_df.shape[0] == 0:
                continue
            elif tmp_df.groupby(tmp_df['t_open'].dt.date).size().mean() < 4:
                # we are inside specific hour and need to reduce position closing too
                cost_reduction_multiplier = 2

            daily_df = tmp_df.groupby(tmp_df['t_open'].dt.date).sum()
            profits_daily = daily_df[[x for x in list(daily_df) if 'profit' in x and 'All' not in x and 'Total' not in x]]

            if kpi == 'calmar':
                rolling_performance = [profits_daily[c].rolling(window, window).apply(lambda x: rolling_calmar(x))
                                   for c in list(profits_daily)]
            elif kpi == 'sum':
                rolling_performance = [profits_daily[c].rolling(window, 1).sum()
                                       for c in list(profits_daily)]
            profits_w1_performance = [profits_daily[c]
                                       for c in list(profits_daily)]
            profits_w1_performance = pd.DataFrame(profits_w1_performance, index=list(profits_daily)).T.reset_index().merge(
                pd.DataFrame({'t_open': [max(profits_w1_performance[0].index.tolist()) + td(days=7)]}),
                how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)
            # we add next week artificially in order to have the chosen strats and not lose the current week performance due to shift
            rolling_performance = pd.DataFrame(rolling_performance, index=list(profits_daily)).T.reset_index().merge(pd.DataFrame({'t_open':[max(rolling_performance[0].index.tolist())+td(days=7)]}),how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)

            rolling_performance += epsilon * rolling_performance

            quantities_by_day = rolling_performance.copy().fillna(0) * 0
            if kpi == 'calmar':
                momentum = rolling_performance.diff() <= -2
            elif kpi == 'sum':
                momentum = (sum([rolling_performance.diff(), rolling_performance.diff(2), rolling_performance.diff(3)]) / 3)
            no_loss_last_5 = (profits_w1_performance<0).rolling(5,5).sum() == 0
            losing_total_last = profits_w1_performance.rolling(window,2).sum() < -100
            hr_less_than_8_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.08
            hr_less_than_20_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.2
            hr_less_than_35_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.35
            hr_less_than_50_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.5
            hr_less_than_100_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 1
            hr_more_than_80_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() >= 0.79
            hr_more_than_90_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() >= 0.89
            hour_start_repr = hour_start
            if isinstance(hour_start,str):
                hour_start_repr = int(hour_start[:2])
            if hour_start_repr < 14:
                hr_cond = hr_less_than_8_last
            elif hour_start_repr >= 15:
                hr_cond = hr_less_than_50_last
            else:
                if block_14_utc:
                    hr_cond = hr_less_than_100_last
                else:
                    hr_cond = hr_less_than_50_last
            losing_total_last3 = profits_w1_performance.rolling(3,2).sum() < -100
            losing_total_last5 = profits_w1_performance.rolling(5,2).sum() < -100
            losing_total_last10 = profits_w1_performance.rolling(10,2).sum() < -100
            losing_4of5_last5 = (profits_w1_performance<0).rolling(5,2).sum() >= 4
            at_least_3_active_last5 = (profits_w1_performance!=0).rolling(window,window).sum() >= window *3/5
            big_win_last1 = profits_w1_performance >= 500
            big_loss_last1 = profits_w1_performance <= -400
            big_loss_last2 = profits_w1_performance.rolling(2,2).sum() <= -800
            big_loss_by_window = profits_w1_performance.rolling(window,2).min() <= -big_loss_value

            at_least_3_wins_last5 = (profits_w1_performance>0).rolling(window,window).sum() >= window * 3/5
            all_wins_last5 = (profits_w1_performance>0).rolling(window,window).sum() == window
            no_more_than_3losses_last_5 = (profits_w1_performance<0).rolling(window,window).sum() < window * 3/5

            quantities_by_day[~hr_cond] = 1
            quantities_by_day[hr_more_than_90_last] = 1.5
            quantities_by_day[(~hr_cond)&(big_loss_last2|big_loss_last1)] = 0.5
            quantities_by_day[big_loss_by_window] = 0

            # checked this, didnt improve....
            # quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() > 100)&(rolling_performance.diff(2) > 100)] = 2

            last_chosen_clusters = [(x.replace('profit_', ''), y) for (x, y) in list(zip(quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0].index,quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0]))]
            last_week_dict[weekdays_subset[0]][item] = last_chosen_clusters

            # now we can drop the future fake rows
            quantities_by_day = quantities_by_day[:-1]

            quantities_by_day_new = quantities_by_day.reset_index()
            quantities_by_day_new = quantities_by_day_new.rename(columns={c: c.replace('profit','quantity')
                                                                          for c in list(quantities_by_day_new)})
            quantities_by_day_new['t_open'] = pd.to_datetime(quantities_by_day_new['t_open'])
            quantities_by_hour = quantities_by_day_new.merge(tmp_df,on=['t_open'],suffixes=('_daily',''),how='outer')
            quantities_by_hour = quantities_by_hour.sort_values('t_open').fillna(method='ffill')

            daily_quantity_cols = [x for x in list(quantities_by_hour) if '_daily' in x]
            final_quantities_cols = [x.replace('_daily','') for x in list(quantities_by_hour) if '_daily' in x]
            for daily_q,final_q in zip(daily_quantity_cols,final_quantities_cols):
                quantities_by_hour[final_q] *= quantities_by_hour[daily_q]
            if clip_after:
                quantities_by_hour['total_quantity'] = np.clip(quantities_by_hour[final_quantities_cols].sum(axis=1),
                                                           -clip,clip)
            else:
                # we allow non round clips, but then round to jumps of 0.5
                quantities_by_hour['total_quantity'] = np.clip(np.round(np.clip(quantities_by_hour[final_quantities_cols],-initial_clip,initial_clip).sum(axis=1)*2)/2,
                                                               -clip,clip)
                #assert (pd.Series(quantities_by_hour['total_quantity'] * 2).astype('int64') == pd.Series(quantities_by_hour['total_quantity'] * 2)).mean() == 1

            filter_cond = (quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000')&(quantities_by_hour['t_open'].apply(cond_on_hours))
            if hour_start == '0000':
                filter_cond = (quantities_by_hour['t_open'].dt.year > 1990)&(quantities_by_hour['t_open'].apply(cond_on_hours))

            quantities_by_hour_copy = quantities_by_hour.copy()
            quantities_by_hour_copy['date'] = quantities_by_hour_copy['t_open'].dt.date
            quantities_by_hour_first = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']][filter_cond].drop_duplicates(subset=['date'],keep='first')
            quantities_by_hour_last = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']].drop_duplicates(subset=['date'], keep='last')

            quantities_by_hour['abs_change'] = abs(quantities_by_hour['total_quantity'].diff()).fillna(1)
            inner_quantity_changes_by_day = quantities_by_hour[['t_open', 'total_quantity', 'abs_change']]
            inner_quantity_changes_by_day.loc[inner_quantity_changes_by_day['t_open'].dt.strftime('%H%M') == '0000', 'total_quantity'] = 0
            inner_quantity_changes_by_day['abs_change'] = abs(inner_quantity_changes_by_day['total_quantity'].diff()).fillna(0)
            inner_quantity_changes_by_day = inner_quantity_changes_by_day[filter_cond].groupby(quantities_by_hour['t_open'].dt.date).sum()['abs_change']

            total_quantity_changes_by_day = inner_quantity_changes_by_day
            if quantities_by_hour_first_previous is not None:
                outer_quantity_changes_by_day = abs(quantities_by_hour_first.set_index('date')['total_quantity'] - quantities_by_hour_last_previous.set_index('date')['total_quantity'])
                total_quantity_changes_by_day = inner_quantity_changes_by_day + outer_quantity_changes_by_day
                #print('outer_quantity_changes_by_day: %s'%outer_quantity_changes_by_day.sum())
            # print(total_quantity_changes_by_day.shape)
            # old method we reduce here
                # quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity'] - quantities_by_hour['abs_change'] * COST_PER_TRADE

            quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity']

            # old method is when cost per trade is reduces a few linew above
                # new_profits_daily = (quantities_by_hour[['t_open','total_profit']].groupby(quantities_by_hour['t_open'].dt.date).sum()['total_profit']).to_frame().reset_index()
            new_profits_daily = (quantities_by_hour[['t_open','total_profit']][filter_cond].groupby(quantities_by_hour['t_open'].dt.date).sum()['total_profit'] - (total_quantity_changes_by_day * COST_PER_TRADE * cost_reduction_multiplier)).to_frame().reset_index()
            new_profits_daily.columns = ['t_open','total_profit']
            if weekday_profit.shape[0] == 0:
                weekday_profit = new_profits_daily.fillna(0)
            else:
                weekday_profit = weekday_profit.merge(new_profits_daily,on=['t_open'],how='outer',suffixes=('','_new')).fillna(0)
                weekday_profit['total_profit'] = weekday_profit[['total_profit','total_profit_new']].sum(axis=1)
                weekday_profit = weekday_profit[['t_open','total_profit']]

            quantities_by_hour_first_previous = quantities_by_hour_first.copy()
            quantities_by_hour_last_previous = quantities_by_hour_last.copy()
            # print(weekday_profit['total_profit'].isna().mean())


        if final_profit.shape[0] == 0:
            final_profit = weekday_profit
        else:
            common = list(set(list(weekday_profit)).intersection(set(list(final_profit))))
            final_profit = final_profit.merge(weekday_profit, on=common, how='outer')

    final_profit = final_profit.sort_values('t_open').set_index('t_open')
    return final_profit, last_week_dict


def drop_losing_hours_from_clusters_dict(last_week_dict,hours_to_ignore_dict):
    """
    :param last_week_dict:
    :param hours_to_ignore_dict: {d: [h1,h2]}
    :return:
    """
    new_dict = {}
    if list(last_week_dict.keys()) == [0]:
        for d in [1,2,3,4]:
            last_week_dict[d] = last_week_dict[0].copy()
    for d in sorted(last_week_dict.keys()):
        new_dict[d] = {}
        for hours_tuple in last_week_dict[d].keys():
            is_refactor_needed = sum([str(h).zfill(2) in hours_to_ignore_dict[d] for h in range(hours_tuple[0],hours_tuple[1])])
            if is_refactor_needed:
                for h in range(hours_tuple[0],hours_tuple[1]):
                    new_dict[d][(h,h+1)] = [(cluster_tuple[0],cluster_tuple[1]) for cluster_tuple in last_week_dict[d][hours_tuple]
                                      if str(h) not in hours_to_ignore_dict[d]]
            else:
                new_dict[d][(hours_tuple[0], hours_tuple[1])] = last_week_dict[d][(hours_tuple[0], hours_tuple[1])]
    return new_dict

def handle_multiplication_by_trend(final_profit, config,last_week_dict):
    """
    Drop to 0.5 quantity when bear trend conditions are satisfied
    :param final_profit:
    :param config:
    :param last_week_dict:
    :return:
    """
    daily_profits = final_profit.reset_index()
    try:
        daily_profits_df = daily_profits.groupby(daily_profits['t_open'].dt.date).sum()
    except:
        daily_profits_df = daily_profits.groupby(pd.to_datetime(daily_profits['t_open']).dt.date).sum()
    daily_profits = daily_profits_df['total_profit']
    bear_cond1 = daily_profits.rolling(config['trend_filter']['w1'], config['trend_filter']['w1']).mean().shift(1) < daily_profits.rolling(config['trend_filter']['w2'],config['trend_filter']['w2']).mean().shift(
        1) - 100  # w=15 also good (see notebook)
    bear_cond2 = daily_profits.rolling(config['trend_filter']['short_term_w1'], config['trend_filter']['short_term_w1']).mean().shift(1) <= daily_profits.rolling(config['trend_filter']['short_term_w2'],config['trend_filter']['short_term_w2']).mean().shift(1)
    bear_cond3 = daily_profits.rolling(2, 2).min().shift(1) < config['trend_filter']['bear_cond3_limit']
    bear_cond = (bear_cond1 & bear_cond2) | bear_cond3
    if bear_cond.iloc[-1]:
        multiplier_by_weekday_dict = {k: 0.5 for k in [0,1,2,3,4]}
    else:
        multiplier_by_weekday_dict = {}
    last_week_dict = scale_quantities_in_clusters_dict(last_week_dict,multiplier_by_weekday_dict)

    bad_days = daily_profits.loc[bear_cond].index.tolist()
    cond = np.in1d(final_profit.index.get_level_values(0), pd.to_datetime(bad_days))
    if 'total_quantity' in list(final_profit):
        final_profit.loc[cond, 'total_quantity'] = final_profit.loc[cond, 'total_quantity'] * 0.5
    final_profit.loc[cond, 'total_profit'] = final_profit.loc[cond, 'total_profit'] * 0.5
    return final_profit, last_week_dict



def scale_quantities_in_clusters_dict(last_week_dict,multiplier_by_weekday_dict):
    """
    :param last_week_dict:
    :param hours_to_ignore_dict: {d: [h1,h2]}
    :return:
    """
    if list(last_week_dict.keys()) == [0]:
        for d in [1,2,3,4]:
            last_week_dict[d] = last_week_dict[0].copy()
    for d in sorted(multiplier_by_weekday_dict.keys()):
        multiplier = multiplier_by_weekday_dict[d]
        for hours_tuple in last_week_dict[d].keys():
            original_lst = last_week_dict[d][hours_tuple]
            new_lst = [(item[0],np.ceil(item[1]*multiplier*2)/2) for item in original_lst]
            last_week_dict[d][hours_tuple] = new_lst
    return last_week_dict


def _get_daily_choises_between_candidates(candidates_df, window_for_choosing,candidates_df_minutes=None):
    candidates_df = candidates_df.rolling(window_for_choosing, window_for_choosing).mean()
    winners_table = candidates_df.shift(1).idxmax(axis=1).fillna(method='ffill').fillna(method='bfill')
    if candidates_df_minutes is None:
        final_profit = pd.Series(candidates_df.lookup(winners_table.index, winners_table),
                             index=winners_table.index,name='total_profit')
    else:
        candidates_df_minutes = candidates_df_minutes.reset_index()
        candidates_df_minutes['date'] = candidates_df_minutes['t_open'].dt.date
        candidates_df_minutes = candidates_df_minutes.set_index('date')
        winners_table_df = pd.DataFrame(winners_table,columns=['winner'])
        candidates_df_minutes = candidates_df_minutes.merge(winners_table_df,left_index=True,
                                                            right_index=True,how='outer')
        candidates_df_minutes = candidates_df_minutes.reset_index().set_index('t_open')
        final_profit = pd.Series(candidates_df_minutes.lookup(candidates_df_minutes.index, candidates_df_minutes['winner']),
                                 index=candidates_df_minutes.index, name='total_profit')
    return pd.DataFrame(final_profit).fillna(0), winners_table


def filter_losing_hours_by_weekday(final_profit,last_week_dict,
                           #config=DEFAULT_DICT_FOR_DROP_LOSING_HOURS,
                           resolution=DEFAULT_DICT_FOR_DROP_LOSING_HOURS['resolution'],
                           bigloss_window=DEFAULT_DICT_FOR_DROP_LOSING_HOURS['bigloss_window'],
                           bigloss_threshold=DEFAULT_DICT_FOR_DROP_LOSING_HOURS['bigloss_threshold'],
                            weekdays_subsets=[[0],[1],[2],[3],[4]],
                           cost_per_trade=COST_PER_TRADE,
                            multiplier=1e4):

    final_profit['date'] = final_profit['t_open'].dt.date
    final_profit['weekday'] = final_profit['t_open'].dt.weekday
    final_profit['hour'] = final_profit['t_open'].dt.strftime(resolution)
    final_profit['total_quantity_old'] = final_profit['total_quantity']
    final_profit_by_hour = pd.pivot_table(final_profit, values=['total_profit'], index=['date', 'weekday', 'hour'],
                                          columns=[],aggfunc=np.sum).reset_index()
    cond = final_profit_by_hour['hour']!= '00'
    fake_final_profit_by_hour = pd.DataFrame({'date':np.array(final_profit_by_hour.loc[cond]['date'].unique().tolist()).repeat(20),
                                              'hour':list(range(0,20)) * len(final_profit_by_hour.loc[cond]['date'].unique().tolist())})
    fake_final_profit_by_hour['weekday'] = pd.to_datetime(fake_final_profit_by_hour['date']).dt.weekday
    fake_final_profit_by_hour['hour'] = fake_final_profit_by_hour['hour'].apply(lambda x: str(x).zfill(2))
    final_profit_by_hour = final_profit_by_hour.merge(fake_final_profit_by_hour,
                                                      on=['date','hour','weekday'],how='outer').fillna(0)
    final_profit_by_hour = final_profit_by_hour.sort_values(['date', 'hour'])
    final_profit = final_profit.set_index(['date', 'weekday', 'hour'])
    final_profit_indices = final_profit.index.tolist()

    hours_to_ignore_dict = {}
    for weekdays_subset in weekdays_subsets:
        for wd in weekdays_subset:
            hours_to_ignore_dict[wd] = []
        for hour in final_profit_by_hour['hour'].unique().tolist():
            if hour == '07':
                bb = 0
            profits_tmp = final_profit_by_hour[(final_profit_by_hour['weekday'].isin(weekdays_subset)) &
                                               (final_profit_by_hour['hour'] == hour)
                                               ].set_index(['date', 'weekday', 'hour'])

            main_losers_df = (profits_tmp < -bigloss_threshold).rolling(bigloss_window,
                                                                   min(2, bigloss_window)).max().shift(1) == 1
            #losers_df = (profits_tmp < -final_big_loss_threshold).rolling(w, min(2, w)).max().shift(1) == 1
            for wd in weekdays_subset:
                losers_df = main_losers_df[(main_losers_df['total_profit'])&(main_losers_df.index.get_level_values(1)==wd)]
                indices_to_ignore = losers_df.index.tolist()
                is_next_week_canceled = (profits_tmp < -bigloss_threshold).rolling(bigloss_window, min(2, bigloss_window)).max()[profits_tmp.index.get_level_values(1)==wd]['total_profit'].iloc[-1]

                if len(indices_to_ignore) > 0:
                    indices_to_ignore = list(set(indices_to_ignore).intersection(set(final_profit_indices)))
                    if len(indices_to_ignore)>0:
                        final_profit.loc[indices_to_ignore, 'total_quantity'] = 0
                        final_profit.loc[indices_to_ignore, 'total_profit'] = 0
                if is_next_week_canceled:
                    hours_to_ignore_dict[wd].append(hour)
    for wd in [0,1,2,3,4]:
        if wd not in hours_to_ignore_dict.keys():
            hours_to_ignore_dict[wd] = []
    last_week_dict_new = drop_losing_hours_from_clusters_dict(last_week_dict, hours_to_ignore_dict)

    final_profit = final_profit.reset_index()[['t_open','total_profit','total_quantity','delta_y','total_quantity_old']]
    final_profit['abs_change'] = abs(final_profit['total_quantity'].diff())
    final_profit['total_profit'] = final_profit['total_quantity'] * final_profit['delta_y'] * multiplier - final_profit['abs_change'] * cost_per_trade

    hours_to_plot = list(range(5,13))+[16,17]
    daily_profits = final_profit[['t_open','total_profit']][final_profit['t_open'].dt.hour.isin(hours_to_plot)].groupby(final_profit['t_open'].dt.date).sum()['total_profit']

    print_kpis_with_90d(daily_profits)
    return final_profit, last_week_dict_new


def handle_config_v4(mode,weekdays_tuple,window,epsilon,clip,multiplier=10**4,df=None,
                     kpi='sum',limit_for_split=0,days_back=0,
                     cond_on_hours=lambda x: True,
                     cancel_leakage_shift=False,
                     hours_subset_override=None,filter_split_weekdays_bigloss=False,
                     clip_after=CLIP_AFTER,initial_clip=INITIAL_CLIP,
                     drop_losing_hours_config=NO_ACTION_DICT_FOR_DROP_LOSING_HOURS,
                     block_14_utc=True,
                     big_loss_value=1800,bigloss1_value=400,bigloss2_value=800,
                     normalize_profits=False):
    """
    :param weekdays_tuple:
    :param window:
    :param epsilon:
    :return:
    """
    if df is None:
        clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=DAYS_FOR_CLUSTER_PERFORMANCE)
        df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])
    if days_back > 0:
        df = df[df['t_open'].dt.date <= (dtdt.now()-td(hours=3,days=days_back)).date()]
    if normalize_profits:
        candles_csv = get_candles_outpath('NG',True)
        candles_df = pd.read_csv(candles_csv, parse_dates=['time_utc','date'])
        # aggregate to daily
        candles_df['date'] = candles_df['time_utc'].dt.date
        candles_df = candles_df.groupby('date').agg({'open':'first','close':'last'}).reset_index()

    print(f'Last date in df is {df["t_open"].max()}')
    df = df.fillna(0).drop_duplicates()
    df['profit_All_abs'] = abs(df['profit_All'])
    df = df.sort_values(['t_open', 'profit_All_abs']).drop_duplicates(subset=['t_open'], keep='last').drop(['profit_All_abs'],axis=1)

    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 15), (15, 18), (18, 20)]
    hours_subset = [(0, 8), (8, 11), (11, 13), (13, 14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v2 = [(0, 8), (8, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 18), (18, 20)]
    hours_subset_v3 = [(0, 8), (8, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
    hours_subset_v4 = [(0, 8), (8, 10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
    hours_subset_v4b = [(0, 4),(4,5),(5,6),(6,7),(7,8), (8, 9),(9,10),(10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 16), (16,17), (17, 18), (18, 20)]

    # v4 with window 9 was even better, but v5 keps things more tight and stable for different windows
    # we go with v5
    hours_subset_v5 = [(0, 4),(4,6),(6,7),(7,8), (8, 9),(9,10),(10,11), (11, 12),(12,13), (13, 14), (14, 15), (15, 16),(16,17),(17,18), (18, 20)]
    hour_min_15_subset_v6 = ['%s%s'%(str(h).zfill(2),mm) for h in range(24)
                              for mm in ['00','15','30','45']]
    hours_subset_v6 = [(0,6),(6,8),(8,12),(12,14),(14,17),(17,20)]
    hours_subset = hours_subset_v4
    if hours_subset_override is not None:
        if hours_subset_override == 'v3':
            hours_subset = hours_subset_v3
        elif hours_subset_override == 'v4':
            hours_subset = hours_subset_v4
        elif hours_subset_override == 'v5':
            hours_subset = hours_subset_v5
        elif hours_subset_override == 'v6':
            hours_subset = hours_subset_v6
        else:
            raise AssertionError('invalid hours_subset_override')


    hours_subset_type = 'hour'
    if isinstance(hours_subset[0],str):
        hours_subset_type = '15min'

    final_profit = pd.DataFrame()
    weekdays_lst = weekdays_tuple[0]

    last_week_dict = {}
    for weekdays_subset in weekdays_lst:
        #print('Handling weekday: %s'%weekdays_subset)
        weekday_profit = pd.DataFrame()
        if weekdays_subset[0] not in last_week_dict.keys():
            for d in weekdays_subset:
                last_week_dict[d] = {}

        quantities_by_hour_first_previous = None
        quantities_by_hour_last_previous = None

        if isinstance(hours_subset[0],tuple):
            hours_tuples_to_check = [(hour_start, hour_end) for hour_start, hour_end in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                         (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                         )].shape[0] > 0
                                 and
                                 (ALL_15MIN_DT_SERIES[
                                      ALL_15MIN_DT_SERIES.dt.hour.isin(range(hour_start, hour_end))].apply(
                                     cond_on_hours).sum() > 0)
                                 ]
        elif isinstance(hours_subset[0],str):
            hours_tuples_to_check = [(hour_start, '') for hour_start in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                         (df['t_open'].dt.strftime('%H%M') == hour_start)
                                         )].shape[0] > 0
                                 and
                                 (ALL_15MIN_DT_SERIES[
                                      ALL_15MIN_DT_SERIES.dt.strftime('%H%M') == hour_start].apply(
                                     cond_on_hours).sum() > 0)
                                 ]

        for i, item in enumerate(hours_tuples_to_check):
            if item  == (15,17):
                bb = 0
            hour_start, hour_end = item

            if hours_subset_type == 'hour':
                (hour_start, hour_end) = item
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                # &(df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            elif hours_subset_type == '15min':
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.strftime('%H%M')==hour_start)
                                # &(df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            else:
                raise AssertionError('invalid hours_subset_type: %s'%hours_subset_type)

            if item not in last_week_dict[weekdays_subset[0]].keys():
                last_week_dict[weekdays_subset[0]][item] = {}

            cost_reduction_multiplier = 1

            if tmp_df.shape[0] == 0:
                continue
            elif tmp_df.groupby(tmp_df['t_open'].dt.date).size().mean() < 4:
                # we are inside specific hour and need to reduce position closing too
                cost_reduction_multiplier = 2

            daily_df = tmp_df.groupby(tmp_df['t_open'].dt.date).sum()
            profit_cols = [x for x in list(daily_df) if 'profit' in x and 'All' not in x and 'Total' not in x]
            if normalize_profits:
                daily_df = daily_df.reset_index().merge(candles_df.reset_index().rename(
                    columns={'date':'t_open'}),on=['t_open'],how='left').set_index('t_open')
                daily_df = daily_df.dropna(subset=profit_cols,how='all')
                for c in profit_cols:
                    daily_df[c] = (daily_df[c] / (daily_df['open'] * multiplier)) * 100 # to get percents

            profits_daily = daily_df[profit_cols]

            if kpi == 'calmar':
                rolling_performance = [profits_daily[c].rolling(window, window).apply(lambda x: rolling_calmar(x))
                                   for c in list(profits_daily)]
            elif kpi == 'sum':
                rolling_performance = [profits_daily[c].rolling(window, 1).sum()
                                       for c in list(profits_daily)]
            profits_w1_performance = [profits_daily[c]
                                       for c in list(profits_daily)]
            days_to_add = 1
            if max(profits_daily.index.tolist()).weekday() == 4:
                days_to_add = 3
            profits_w1_performance = pd.DataFrame(profits_w1_performance, index=list(profits_daily)).T.reset_index().merge(
                pd.DataFrame({'t_open': [max(profits_w1_performance[0].index.tolist()) + td(days=days_to_add)]}),
                how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)
            # we add next week artificially in order to have the chosen strats and not lose the current week performance due to shift
            rolling_performance = pd.DataFrame(rolling_performance, index=list(profits_daily)).T.reset_index().merge(pd.DataFrame({'t_open':[max(rolling_performance[0].index.tolist())+td(days=days_to_add)]}),how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)

            rolling_performance += epsilon * rolling_performance

            quantities_by_day = rolling_performance.copy().fillna(0) * 0

            hr_less_than_8_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.08
            hr_less_than_50_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 0.5
            hr_less_than_100_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() <= 1
            hr_more_than_90_last = (profits_w1_performance.replace(0,np.nan) >0).rolling(window,2).mean() >= 0.89
            hour_start_repr = hour_start
            if isinstance(hour_start,str):
                hour_start_repr = int(hour_start[:2])
            if hour_start_repr < 14:
                hr_cond = hr_less_than_8_last
            elif hour_start_repr >= 15:
                hr_cond = hr_less_than_50_last
            else:
                if block_14_utc:
                    hr_cond = hr_less_than_100_last
                else:
                    hr_cond = hr_less_than_50_last

            big_loss_last1 = profits_w1_performance <= -bigloss1_value
            big_loss_last2 = profits_w1_performance.rolling(2,2).sum() <= -bigloss2_value
            big_loss_by_window = profits_w1_performance.rolling(window, 2).min() <= -big_loss_value

            quantities_by_day[~hr_cond] = 1
            quantities_by_day[hr_more_than_90_last] = 1.5
            quantities_by_day[(~hr_cond)&(big_loss_last2|big_loss_last1)] = 0.5
            quantities_by_day[big_loss_by_window] = 0

            for day in weekdays_subset:
                ref_date = dtdt.now() + td(hours=24) # if working on sundays we refer to next week
                current_monday = pd.to_datetime((ref_date - td(days=ref_date.weekday())).date())

                tmp_quantities_by_day = quantities_by_day.copy().reset_index()
                tmp_quantities_by_day['t_open'] = pd.to_datetime(tmp_quantities_by_day['t_open'])
                tmp_quantities_by_day = tmp_quantities_by_day.set_index('t_open')
                tmp_quantities_by_day = tmp_quantities_by_day[tmp_quantities_by_day.index.get_level_values(0).weekday == day]
                if pd.to_datetime(tmp_quantities_by_day.reset_index()['t_open'].max().date()) >= current_monday:
                    last_chosen_clusters = [(x.replace('profit_', ''), y) for (x, y) in list(zip(tmp_quantities_by_day.iloc[-1][tmp_quantities_by_day.iloc[-1] > 0].index,tmp_quantities_by_day.iloc[-1][tmp_quantities_by_day.iloc[-1] > 0]))]
                else:
                    last_chosen_clusters = [(x.replace('profit_', ''), y) for (x, y) in list(zip(quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0].index,quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0]))]
                last_week_dict[day][item] = last_chosen_clusters

            # now we can drop the future fake rows
            quantities_by_day = quantities_by_day[:-1]
            quantities_by_day_new = quantities_by_day.reset_index()
            quantities_by_day_new = quantities_by_day_new.rename(columns={c: c.replace('profit','quantity')
                                                                          for c in list(quantities_by_day_new)})
            quantities_by_day_new['t_open'] = pd.to_datetime(quantities_by_day_new['t_open'])
            quantities_by_hour = quantities_by_day_new.merge(tmp_df,on=['t_open'],suffixes=('_daily',''),how='outer')
            quantities_by_hour = quantities_by_hour.sort_values('t_open').fillna(method='ffill')

            daily_quantity_cols = [x for x in list(quantities_by_hour) if '_daily' in x]
            final_quantities_cols = [x.replace('_daily','') for x in list(quantities_by_hour) if '_daily' in x]
            for daily_q,final_q in zip(daily_quantity_cols,final_quantities_cols):
                quantities_by_hour[final_q] *= quantities_by_hour[daily_q]
            if clip_after:
                quantities_by_hour['total_quantity'] = np.clip(quantities_by_hour[final_quantities_cols].sum(axis=1),
                                                           -clip,clip)
            else:
                # we allow non round clips, but then round to jumps of 0.5
                quantities_by_hour['total_quantity'] = np.clip(np.round(np.clip(quantities_by_hour[final_quantities_cols],-initial_clip,initial_clip).sum(axis=1)*2)/2,
                                                               -clip,clip)


            filter_cond = (quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000')&(quantities_by_hour['t_open'].apply(cond_on_hours))
            if hour_start == '0000':
                filter_cond = (quantities_by_hour['t_open'].dt.year > 1990)&(quantities_by_hour['t_open'].apply(cond_on_hours))

            quantities_by_hour_copy = quantities_by_hour.copy()
            quantities_by_hour_copy['date'] = quantities_by_hour_copy['t_open'].dt.date

            quantities_by_hour['abs_change'] = abs(quantities_by_hour['total_quantity'].diff()).fillna(1)
            quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity']

            new_profits_hourly = quantities_by_hour[['t_open','total_profit','total_quantity','abs_change']][filter_cond]
            if new_profits_hourly['t_open'].value_counts().max()>1:
                raise AssertionError('got duplicates in t_open')
            if weekday_profit.shape[0] == 0:
                weekday_profit = new_profits_hourly
            else:
                weekday_profit = weekday_profit.merge(new_profits_hourly,on=list(new_profits_hourly),how='outer')

        if final_profit.shape[0] == 0:
            final_profit = weekday_profit
        else:
            common = list(set(list(weekday_profit)).intersection(set(list(final_profit))))
            final_profit = final_profit.merge(weekday_profit, on=common, how='outer')

    final_profit = final_profit.sort_values('t_open')#.set_index('t_open')
    final_profit['abs_change'] = abs(final_profit['total_quantity'].diff(1))
    final_profit.loc[final_profit['t_open'].dt.strftime('%H%M').isin(['0000','0015','1945']),'abs_change'] = 0
    final_profit['total_profit'] -= COST_PER_TRADE * final_profit['abs_change']
    if 'delta_y' not in list(final_profit):
        final_profit = final_profit.merge(df[['t_open','delta_y']],on=['t_open'])
    #### BIGLOSS by weekday filter
    if isinstance(drop_losing_hours_config['bigloss_window'],int):
        if 'weekdays_subsets' in drop_losing_hours_config.keys():
            if len(drop_losing_hours_config['weekdays_subsets']) == 2:
                # assert len(drop_losing_hours_config['weekdays_subsets'][0]) ==2, 'in case of weekdays subsets specification we must have a tuple (<weekdays subset>, <threshold>)'
                # assert drop_losing_hours_config['weekdays_subsets'][1][0] == [[0],[1],[2],[3],[4]]
                # assert drop_losing_hours_config['weekdays_subsets'][1][1] == drop_losing_hours_config['bigloss_threshold']
                additional_subset = drop_losing_hours_config['weekdays_subsets'][0][0]
                additional_subset_threshold = drop_losing_hours_config['weekdays_subsets'][0][1]
                final_profit,last_week_dict = filter_losing_hours_by_weekday(final_profit,last_week_dict,drop_losing_hours_config['resolution'],drop_losing_hours_config['bigloss_window'],
                                                                             additional_subset_threshold,weekdays_subsets=additional_subset,multiplier=multiplier)
                final_profit, last_week_dict_new = filter_losing_hours_by_weekday(final_profit, last_week_dict,drop_losing_hours_config['resolution'],drop_losing_hours_config['bigloss_window'],
                                                                              drop_losing_hours_config['bigloss_threshold'],multiplier=multiplier)
            #### Multi-Layer
            elif len(drop_losing_hours_config['weekdays_subsets']) > 2:
                for sub_conf in drop_losing_hours_config['weekdays_subsets']:
                    additional_subset = sub_conf[0]
                    additional_subset_threshold = sub_conf[1]
                    try:
                        additional_subset_window = sub_conf[2]
                    except:
                        additional_subset_window = drop_losing_hours_config['bigloss_window']

                    final_profit, last_week_dict = filter_losing_hours_by_weekday(final_profit, last_week_dict,drop_losing_hours_config['resolution'],
                                                              additional_subset_window,additional_subset_threshold,weekdays_subsets=additional_subset,multiplier=multiplier)

                last_week_dict_new = last_week_dict
        else:
            final_profit,last_week_dict_new = filter_losing_hours_by_weekday(final_profit,last_week_dict,
                                                                         drop_losing_hours_config['resolution'],
                                                                         drop_losing_hours_config['bigloss_window'],
                                                                         drop_losing_hours_config['bigloss_threshold'],
                                                                         multiplier=multiplier)

    elif drop_losing_hours_config['bigloss_window'] is None:
        # no action needed
        final_profit,last_week_dict_new = final_profit,last_week_dict
    else:
        bigloss_windows_candidates = drop_losing_hours_config['bigloss_window'][0]
        window_for_choosing = drop_losing_hours_config['bigloss_window'][1]
        #windows_dfs_stack = []
        last_week_dicts_stack = {}
        candidates_df_minutes = pd.DataFrame()
        for bigloss_window in bigloss_windows_candidates:
            tmp_config = drop_losing_hours_config.copy()
            tmp_config['bigloss_window'] = bigloss_window
            tmp_final_profit, tmp_last_week_dict_new = filter_losing_hours_by_weekday(final_profit, last_week_dict,
                                                              tmp_config['resolution'],tmp_config['bigloss_window'],tmp_config['bigloss_threshold'],
                                                                  multiplier=multiplier)
            if candidates_df_minutes.shape[0] == 0:
                candidates_df_minutes = tmp_final_profit[['t_open','total_profit']].rename(columns={'total_profit':f'total_profit_w{bigloss_window}'}).set_index('t_open')
            else:
                candidates_df_minutes = candidates_df_minutes.merge(tmp_final_profit[['t_open','total_profit']].rename(columns={'total_profit':f'total_profit_w{bigloss_window}'}).set_index('t_open'),
                                                            left_index=True, right_index=True)
                #candidates_df = candidates_df_minutes.groupby(tmp_final_profit['t_open'].dt.date).sum()

            last_week_dicts_stack[bigloss_window] = tmp_last_week_dict_new

        candidates_df = candidates_df_minutes.reset_index().groupby(candidates_df_minutes.reset_index()['t_open'].dt.date).sum()
        final_profit, winning_conf_by_day = _get_daily_choises_between_candidates(candidates_df,window_for_choosing,
                                                                                  candidates_df_minutes=candidates_df_minutes)
        last_week_dict_new = last_week_dicts_stack[int(winning_conf_by_day.iloc[-1][-1])]
    if drop_losing_hours_config['trend_filter'] is not None:
        final_profit, last_week_dict_new = handle_multiplication_by_trend(final_profit, drop_losing_hours_config, last_week_dict_new)

    if 't_open' not in list(final_profit):
        try:
            final_profit = final_profit.reset_index()
        except:
            final_profit = pd.DataFrame(final_profit).reset_index()
    return final_profit, last_week_dict_new

def handle_config_v3_15mins(mode,weekdays_tuple,window,epsilon,clip,multiplier=10**4,df=None,
                     kpi='sum',limit_for_split=0,days_back=0,
                     cond_on_hours=lambda x: True,
                     cancel_leakage_shift=False):
    """
    :param weekdays_tuple:
    :param window:
    :param epsilon:
    :return:
    """
    if df is None:
        clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=DAYS_FOR_CLUSTER_PERFORMANCE)
        df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])

    if days_back > 0:
        df = df[df['t_open']<= dtdt.now()-td(days=days_back)]

    hours_subset_v5 = [(0, 4),(4,6),(6,7),(7,8), (8, 9),(9,10),(10,11), (11, 12),(12,13), (13, 14), (14, 15), (15, 16),(16,17),(17,18), (18, 20)]
    hour_min_15_subset_v6 = ['%s%s'%(str(h).zfill(2),mm) for h in range(24)
                              for mm in ['00','15','30','45']]
    hours_subset = hour_min_15_subset_v6

    hours_subset_type = 'hour'
    if isinstance(hours_subset[0],str):
        hours_subset_type = '15min'

    final_profit = pd.DataFrame()
    weekdays_lst = weekdays_tuple[0]

    last_week_dict = {}
    for weekdays_subset in weekdays_lst:
        weekday_profit = pd.DataFrame()
        if weekdays_subset[0] not in last_week_dict.keys():
            last_week_dict[weekdays_subset[0]] = {}

        quantities_by_hour_first_previous = None
        quantities_by_hour_last_previous = None

        hours_tuples_to_check = [(hour_start, hour_end) for hour_start, hour_end in hours_subset if
                                 df.loc[((df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                         (df['t_open'].dt.hour.isin(range(hour_start, hour_end)))
                                         )].shape[0] > 0
                                 and
                                 (ALL_15MIN_DT_SERIES[
                                      ALL_15MIN_DT_SERIES.dt.hour.isin(range(hour_start, hour_end))].apply(
                                     cond_on_hours).sum() > 0)
                                 ]
        for i, item in enumerate(hours_tuples_to_check):
            # print('handling weekday %s X hour %s'%(weekdays_subset,item))
            if hours_subset_type == 'hour':
                (hour_start, hour_end) = item
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.hour.isin(range(hour_start, hour_end))) &
                                (df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            elif hours_subset_type == '15min':
                hour_start = item
                hour_end = ''
                tmp_df = df.loc[(df['t_open'].dt.weekday.isin(weekdays_subset)) &
                                (df['t_open'].dt.strftime('%H%M')==hour_start) &
                                (df['t_open'].dt.hour.apply(cond_on_hours))
                                ].copy()
            else:
                raise AssertionError('invalid hours_subset_type: %s'%hours_subset_type)

            if item not in last_week_dict[weekdays_subset[0]].keys():
                last_week_dict[weekdays_subset[0]][item] = {}

            cost_reduction_multiplier = 1

            if tmp_df.shape[0] == 0:
                continue
            elif tmp_df.groupby(tmp_df['t_open'].dt.date).size().mean() < 4:
                # we are inside specific hour and need to reduce position closing too
                cost_reduction_multiplier = 2

            daily_df = tmp_df.groupby(tmp_df['t_open'].dt.date).sum()
            profits_daily = daily_df[[x for x in list(daily_df) if 'profit' in x and 'All' not in x and 'Total' not in x]]

            if kpi == 'calmar':
                rolling_performance = [profits_daily[c].rolling(window, 3).apply(lambda x: rolling_calmar(x))
                                   for c in list(profits_daily)]
            elif kpi == 'sum':
                rolling_performance = [profits_daily[c].rolling(window, 3).sum()
                                       for c in list(profits_daily)]

            # we add next week artificially in order to have the chosen strats and not lose the current week performance due to shift
            rolling_performance = pd.DataFrame(rolling_performance, index=list(profits_daily)).T.reset_index().merge(pd.DataFrame({'t_open':[max(rolling_performance[0].index.tolist())+td(days=7)]}),how='outer').set_index('t_open').shift(1 if not cancel_leakage_shift else 0)

            rolling_performance += epsilon * rolling_performance

            quantities_by_day = rolling_performance.copy().fillna(0) * 0
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() > limit_for_split)] = 1
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() <= limit_for_split)] = 0.5
            quantities_by_day[(rolling_performance < limit_for_split) & (rolling_performance.diff() <= limit_for_split)] = 0
            quantities_by_day[(rolling_performance < limit_for_split) & (rolling_performance.diff() > limit_for_split)] = 1 # used to be 0.5 until 04.07.2022
            quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() <= -2)] = 0

            # checked this, didnt improve....
            # quantities_by_day[(rolling_performance > limit_for_split) & (rolling_performance.diff() > 100)&(rolling_performance.diff(2) > 100)] = 2

            last_chosen_clusters = [(x.replace('profit_', ''), y) for (x, y) in list(zip(quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0].index,quantities_by_day.iloc[-1][quantities_by_day.iloc[-1] > 0]))]
            last_week_dict[weekdays_subset[0]][item] = last_chosen_clusters

            # now we can drop the future fake rows
            quantities_by_day = quantities_by_day[:-1]

            quantities_by_day_new = quantities_by_day.reset_index()
            quantities_by_day_new = quantities_by_day_new.rename(columns={c: c.replace('profit','quantity')
                                                                          for c in list(quantities_by_day_new)})
            quantities_by_day_new['t_open'] = pd.to_datetime(quantities_by_day_new['t_open'])
            quantities_by_hour = quantities_by_day_new.merge(tmp_df,on=['t_open'],suffixes=('_daily',''),how='outer')
            quantities_by_hour = quantities_by_hour.sort_values('t_open').fillna(method='ffill')

            daily_quantity_cols = [x for x in list(quantities_by_hour) if '_daily' in x]
            final_quantities_cols = [x.replace('_daily','') for x in list(quantities_by_hour) if '_daily' in x]
            for daily_q,final_q in zip(daily_quantity_cols,final_quantities_cols):
                quantities_by_hour[final_q] *= quantities_by_hour[daily_q]
            quantities_by_hour['total_quantity'] = np.clip(quantities_by_hour[final_quantities_cols].sum(axis=1),
                                                           -clip,clip) # todo, clip before sum too?!?!? check pefroemance first

            filter_cond = (quantities_by_hour['t_open'].dt.strftime('%H%M') != '0000')&(quantities_by_hour['t_open'].apply(cond_on_hours))
            if hour_start == '0000':
                filter_cond = (quantities_by_hour['t_open'].dt.year > 1990)&(quantities_by_hour['t_open'].apply(cond_on_hours))

            quantities_by_hour_copy = quantities_by_hour.copy()
            quantities_by_hour_copy['date'] = quantities_by_hour_copy['t_open'].dt.date
            quantities_by_hour_first = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']][filter_cond].drop_duplicates(subset=['date'],keep='first')
            quantities_by_hour_last = quantities_by_hour_copy[['t_open', 'date', 'total_quantity']].drop_duplicates(subset=['date'], keep='last')

            quantities_by_hour['abs_change'] = abs(quantities_by_hour['total_quantity'].diff()).fillna(1)
            inner_quantity_changes_by_day = quantities_by_hour[['t_open', 'total_quantity', 'abs_change']]
            inner_quantity_changes_by_day.loc[inner_quantity_changes_by_day['t_open'].dt.strftime('%H%M') == '0000', 'total_quantity'] = 0
            inner_quantity_changes_by_day['abs_change'] = abs(inner_quantity_changes_by_day['total_quantity'].diff()).fillna(0)
            inner_quantity_changes_by_day = inner_quantity_changes_by_day[filter_cond].groupby(quantities_by_hour['t_open'].dt.date).sum()['abs_change']

            total_quantity_changes_by_day = inner_quantity_changes_by_day
            if quantities_by_hour_first_previous is not None:
                outer_quantity_changes_by_day = abs(quantities_by_hour_first.set_index('date')['total_quantity'] - quantities_by_hour_last_previous.set_index('date')['total_quantity'])
                total_quantity_changes_by_day = inner_quantity_changes_by_day + outer_quantity_changes_by_day
                #print('outer_quantity_changes_by_day: %s'%outer_quantity_changes_by_day.sum())
            # print(total_quantity_changes_by_day.shape)
            # old method we reduce here
                # quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity'] - quantities_by_hour['abs_change'] * COST_PER_TRADE
            quantities_by_hour['total_profit'] = multiplier*quantities_by_hour['delta_y']*quantities_by_hour['total_quantity']

            # old method is when cost per trade is reduces a few linew above
                # new_profits_daily = (quantities_by_hour[['t_open','total_profit']].groupby(quantities_by_hour['t_open'].dt.date).sum()['total_profit']).to_frame().reset_index()
            new_profits_daily = (quantities_by_hour[['t_open', 'total_profit']][filter_cond].groupby(
                quantities_by_hour['t_open'].dt.date).sum()['total_profit'] - (total_quantity_changes_by_day * COST_PER_TRADE * cost_reduction_multiplier)).to_frame().reset_index()
            new_profits_daily.columns = ['t_open','total_profit']
            if weekday_profit.shape[0] == 0:
                weekday_profit = new_profits_daily.fillna(0)
            else:
                weekday_profit = weekday_profit.merge(new_profits_daily,on=['t_open'],how='outer',suffixes=('','_new')).fillna(0)
                weekday_profit['total_profit'] = weekday_profit[['total_profit','total_profit_new']].sum(axis=1)
                weekday_profit = weekday_profit[['t_open','total_profit']]

            quantities_by_hour_first_previous = quantities_by_hour_first.copy()
            quantities_by_hour_last_previous = quantities_by_hour_last.copy()
            # print(weekday_profit['total_profit'].isna().mean())


        if final_profit.shape[0] == 0:
            final_profit = weekday_profit
        else:
            common = list(set(list(weekday_profit)).intersection(set(list(final_profit))))
            final_profit = final_profit.merge(weekday_profit, on=common, how='outer')

    final_profit = final_profit.sort_values('t_open').set_index('t_open')
    return final_profit, last_week_dict



def scan_filterings(df,clip=3,multiplier=10**4,start_date=dtdt(2021,10,1),
                            windows=[9,10,11,12,13,14],
                            big_loss_values=[None],
                                print_last_chosen=False,kpi='calmar',
                    cond_on_hours=lambda x: True,weekdays_for_plot=[0,1,2,3,4],
                        hours_to_plot='all',
                    cancel_leakage_shift=False,
                    filter_hours_before_scan=True,
                    config=CONFIG,print_performance=True,
                    hours_subset_conf='v4'):
    # print(f'1.1.0: {dtdt.now()}')
    if hours_to_plot == 'all':
        hours_to_plot = list(range(0,24))
    if filter_hours_before_scan:
        if isinstance(hours_to_plot[0],int):
            df = df[(df['t_open']>=start_date)&(df['t_open'].dt.hour.isin(hours_to_plot))]
        elif isinstance(hours_to_plot[0],str):
            assert len(hours_to_plot[0])==4
            df = df[(df['t_open'] >= start_date) & (df['t_open'].dt.strftime('%H%M').isin(hours_to_plot))]
    # print(f'1.1.0b: {dtdt.now()}')
    original_profits = df[['t_open','delta_y']+[x for x in list(df) if 'quantity' in x and 'All' not in x and 'Total' not in x]]
    # print(f'1.1.0b2: {dtdt.now()}')
    original_quantities = pd.Series(np.clip(original_profits[[x for x in list(df) if 'quantity' in x and 'All' not in x and 'Total' not in x
                                                                    ]].sum(axis=1),-clip,clip),index=original_profits.index)
    # print(f'1.1.0b3: {dtdt.now()}')
    original_profits['original_profit'] = original_quantities * original_profits['delta_y'] * multiplier - (COST_PER_TRADE * abs(original_quantities.diff()))
    # print(f'1.1.0b4: {dtdt.now()}')
    original_profits_daily = original_profits[['t_open','original_profit']][original_profits['t_open'].apply(cond_on_hours)].groupby(original_profits['t_open'].dt.date).sum()
    # print(f'1.1.0c: {dtdt.now()}')
    old_profit = original_profits_daily.sum(axis=1).sum()
    old_calmar = calc_calmar(original_profits_daily.sum(axis=1),use_annual=False)
    old_sharpe = round(calc_sharpe(original_profits_daily.sum(axis=1)),2)
    all_profits = pd.DataFrame()
    # print(f'1.1.0d: {dtdt.now()}')
    for window in windows:
        for epsilon in [0,0.25,0.5,1,2][:1]:
            for weekdays_tuple in [([[0],[1],[2],[3],[4]],'split')] if 'split' in config else \
                                   [([[0,1,2,3,4]],'all')]:

                weekdays_name = weekdays_tuple[1]
                for big_loss_value in big_loss_values:
                    if config == 'v2':
                        final_profit,last_week_dict = handle_config_v2('',weekdays_tuple,window,epsilon,clip,multiplier,df,
                                                                   kpi=kpi,cond_on_hours=cond_on_hours,
                                                                   cancel_leakage_shift=cancel_leakage_shift)
                    elif config in ['v3','v3split','v3b']:
                        # print(f'1.1.1: {dtdt.now()}')
                        final_profit, last_week_dict = handle_config_v3('', weekdays_tuple, window, epsilon, clip,
                                                                        multiplier, df,
                                                                        kpi=kpi, cond_on_hours=cond_on_hours,
                                                                        cancel_leakage_shift=cancel_leakage_shift,
                                                                        big_loss_value=big_loss_value,
                                                                        block_14_utc=config != 'v3b',
                                                                        hours_subset_conf=hours_subset_conf)
                        # print(f'1.1.2: {dtdt.now()}')
                    elif config == 'v4':
                        final_profit, last_week_dict = handle_config_v3_15mins('', weekdays_tuple, window, epsilon, clip,
                        # final_profit, last_week_dict = handle_config_v4('', weekdays_tuple, window, epsilon, clip,
                                                                        multiplier, df,
                                                                        kpi=kpi, cond_on_hours=cond_on_hours,
                                                                        cancel_leakage_shift=cancel_leakage_shift)
                    final_profit = final_profit.reset_index()[pd.to_datetime(final_profit.reset_index()['t_open']).dt.weekday.isin(weekdays_for_plot)
                                                                ].set_index('t_open')
                    profit_series = final_profit.sum(axis=1)
                    new_profit = profit_series.sum()
                    new_calmar = calc_calmar(profit_series,use_annual=False)
                    new_sharpe = round(calc_sharpe(profit_series),2)
                    new_profit_df = pd.DataFrame(profit_series,columns=[
                                                                            f'profit_window={window}_epsilon={epsilon}_weekdays={weekdays_name}']).reset_index()
                    if new_profit > 0 and print_performance:
                        print(f'Window {window} x BigLoss {big_loss_value}| old profit: {old_profit} x Calmar {old_calmar} x Sharpe {old_sharpe}, new: {new_profit} x Calmar {new_calmar} x Sharpe {new_sharpe}')

                    if all_profits.shape[0] == 0:
                        all_profits = new_profit_df
                    else:
                        all_profits = all_profits.merge(new_profit_df,on=['t_open'],how='outer')
                    if print_last_chosen:
                        print(last_week_dict)
    all_profits = all_profits.merge(
        pd.DataFrame(original_profits_daily.sum(axis=1), columns=['profit_original']).reset_index(),
        on=['t_open'], how='outer').fillna(0).sort_values('t_open')
    all_profits.to_csv(os.path.join(HOME,"performance_analysis","cluster_filtering","clusters_tmp_clip=%s.csv")%clip,index=False)
    return all_profits


def main(mode='ens_calmar0.25_S0.5_window4',calc = True, plot = True,clip = 3,kpi='calmar',
         end_date=dtdt(2030,1,1),weekdays_for_plot = [0,1,2,3,4],hours_to_plot='all',
         cond_on_hours=lambda x: True,cond_on_cluster_names=lambda x: True,
         windows=[3,4,5,7,9,10,11,12,13,14],big_loss_values=[None],cancel_leakage_shift=False,
         config=CONFIG,print_last_chosen=False,
         print_performance=True,
         hours_subset_conf='v4'):
    if cancel_leakage_shift:
        print('WARNING, we run in a mode that allows leakage, using current week to optimize current week')

    clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=DAYS_FOR_CLUSTER_PERFORMANCE)
    df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])
    # df = df[[c for c in list(df) if 'quantity' not in c or cond_on_cluster_names(c)]]
    df =df[df['t_open']<=end_date]
    # df[df['t_open']<=dtdt(2022,2,28)].sort_values(['t_open'])
    # todo here is the problem. something going wrong!!!! the new performance_csv with 365 is having zeros instead of nans and this affects the choice?!
    ### check what happens, why hourly profits not showing before March 22
    if calc:
        # print(f'1.1: {dtdt.now()}')
        all_profits = scan_filterings(df,clip=clip,start_date=dtdt(2021,11,15),
                                      kpi=kpi,windows=windows,cond_on_hours=cond_on_hours,
                                      cancel_leakage_shift=cancel_leakage_shift,
                                      weekdays_for_plot=weekdays_for_plot,hours_to_plot=hours_to_plot,
                                      big_loss_values=big_loss_values,print_last_chosen=print_last_chosen,
                                      config=config,
                                      print_performance=print_performance,
                                      hours_subset_conf=hours_subset_conf)
        # print(f'1.2: {dtdt.now()}')
    else:
        all_profits = pd.read_csv(os.path.join(HOME,"performance_analysis","cluster_filtering","clusters_tmp_clip=%s.csv")%clip,
                                  parse_dates=['t_open'])
    if plot:
        all_profits[pd.to_datetime(all_profits['t_open']).dt.weekday.isin(weekdays_for_plot)].set_index('t_open').cumsum().plot(title='Cumsum of profits Vs original %s-%s'%(hours_to_plot[0],hours_to_plot[-1]),
                                                      style=['-']*6+['--']*6+['-*']*6)
        plt.show()
    return all_profits


def check_positions(mode,date,last_sunday,clip=5):
    sub_mode = {'ens_calmar3_S0.5_window4':'ens_cluster_dynamic_v3T5_S0.5_w9'}[mode] #'real_w9'
    conf_name = {'ens_calmar3_S0.5_window4':'ens_cluster_dynamic_v3_S0.5_w9'}[mode] #'real_w9'

    clusters_performance_csv = get_clusters_performance_csv_by_mode(mode,days=DAYS_FOR_CLUSTER_PERFORMANCE)
    df = pd.read_csv(clusters_performance_csv, parse_dates=['t_open'])
    df = df[df['t_open'].dt.date == date.date()]
    from Algo.Utils.files_handle import HOME, get_dynamic_clusters_json
    import json
    clusters_dict = json.load(open(get_dynamic_clusters_json()))
    final_final_to_final = pd.DataFrame()
    for hour in [11,12,13,14][-2:]:
        hour_quantities = df[df['t_open'].dt.hour == hour]
        clusters_weights = clusters_dict[last_sunday][conf_name]["0"][str((hour,hour+1))]
        clusters_weights = pd.DataFrame(clusters_weights,columns=['cluster','quantity'])
        clusters_weights['cluster'] = 'quantity_'+clusters_weights['cluster']
        clusters_weights = clusters_weights.set_index('cluster')['quantity']
        hour_quantities_filtered = hour_quantities.set_index('t_open')[clusters_weights.index.tolist()] *clusters_weights
        hour_quantities_filtered_total = np.clip(hour_quantities_filtered.sum(axis=1),-clip, clip)
        bb = 0

        blah, manual_positions = get_ensemble_positions(date,
                               ENSEMBLES_DICT[sub_mode],
                               takeback_hours=-8,debug=False)
        final_to_final = pd.DataFrame(
            (hour_quantities.set_index('t_open')[clusters_weights.index.tolist()] * clusters_weights).sum(axis=1)).merge(
                    manual_positions.set_index('t_open'), left_index=True, right_index=True)
        final_to_final.columns = ['quantity_algo','quantity_trading_strategy']

        try:
            assert (final_to_final['quantity_algo'] == final_to_final['quantity_trading_strategy']).mean() == 1, 'found inconsistency for hour: %s'%hour
        except Exception as e:
            print ('got Error while checking consistency of cluster positions\n%s'%e)
        final_to_final = final_to_final.reset_index()
        if final_final_to_final.shape[0] == 0:
            final_final_to_final = final_to_final
        else:
            final_final_to_final = final_final_to_final.merge(final_to_final,on=list(final_to_final),
                                                              how='outer')
    final_final_to_final.set_index('t_open').plot(kind='bar')
    plt.show()
    bb = 0

def check_performance_by_hour_v4(conf,resolution='1H',
                                 hour_start=8,hour_end=20,
                                 weekdays=[0,1,2,3,4]
                              ):
    assert resolution in ['15M','30M','1H']

    filter_dict = FILTER_DICT_CONFS[conf]
    final_profit, last_week_dict = handle_config_v4('ens_calmar3_S0.5_window4', ([[0, 1, 2, 3, 4]], 'all'),9, 0, 2, days_back=0, clip_after=False,initial_clip=1.25,
      # drop_losing_hours_config=V5_DICT_FOR_DROP_LOSING_HOURS
      drop_losing_hours_config=filter_dict, block_14_utc=False)

    final_profit = final_profit[final_profit['t_open'].dt.weekday.isin(weekdays)]
    final_profit = final_profit[final_profit['t_open'].dt.hour.isin(range(hour_start,hour_end))]

    final_profit['date'] = final_profit['t_open'].dt.date
    final_profit['1H'] = final_profit['t_open'].dt.hour
    final_profit['minute'] = final_profit['t_open'].dt.minute
    final_profit['HH30'] = final_profit['t_open'].dt.minute//30*30
    final_profit['HH15'] = final_profit['t_open'].dt.minute//15*15
    final_profit['30M'] = (final_profit['t_open'] - final_profit['t_open'].apply(lambda x: td(minutes=x.minute)) + \
                          final_profit['HH30'].apply(lambda x: td(minutes=x))).dt.strftime('%H%M')
    final_profit['15M'] = (final_profit['t_open'] - final_profit['t_open'].apply(lambda x: td(minutes=x.minute)) + \
                          final_profit['HH15'].apply(lambda x: td(minutes=x))).dt.strftime('%H%M')

    pivot = pd.pivot_table(final_profit,index=['date'],values=['total_profit'],
                           columns=[resolution],aggfunc=np.sum)
    pivot.columns = [str(x[1]).zfill(2) for x in list(pivot)]
    pivot.cumsum().plot(style=['-']*7+['-*']*7+['--']*7,
                        title=conf)
    plt.show()

def get_profits_v3(mode='ens_calmar3_S0.5_window4',
                      weekdays_for_plot=[0],
                      window=10, end_date=dtdt(2050, 7, 15),
                      big_loss_value=None,
                      hours=range(8,20),
                      use_original=False,
                      clip=2
                      ):
    profits = main(mode=mode, calc=True, plot=False, clip=clip,
                   kpi='sum', end_date=end_date,
                   weekdays_for_plot=weekdays_for_plot,
                   cond_on_hours=lambda x: x.hour in hours,
                   cond_on_cluster_names=lambda x: True,
                   windows=[window], cancel_leakage_shift=False,
                   big_loss_values=[big_loss_value],
                   config='v3'
                   )
    return profits


def get_profits_v4(conf,hours,weekdays=[0,1,2,3,4]):
    filter_dict = FILTER_DICT_CONFS[conf]
    profits, last_week_dict = handle_config_v4('ens_calmar3_S0.5_window4', ([[0, 1, 2, 3, 4]], 'all'), 9, 0, 2,
                                                    days_back=0, clip_after=False, initial_clip=1.25,
                                                    # drop_losing_hours_config=V5_DICT_FOR_DROP_LOSING_HOURS
                                                    drop_losing_hours_config=filter_dict, block_14_utc=False)
    profits = profits[profits['t_open'].dt.hour.isin(hours)]
    profits = profits[profits['t_open'].dt.weekday.isin(weekdays)]
    profits[['t_open','total_profit']].groupby(profits['t_open'].dt.date).sum().cumsum().plot(title=f'Profits for {conf} x {weekdays} x {hours[0]}-{hours[-1]}')
    plt.show()
def check_performance_by_hour(mode='ens_calmar3_S0.5_window4',
                              weekdays_for_plot=[0],
                                  window=10,end_date=dtdt(2050, 7, 15),
                                    big_loss_value=None,
                                    hours_to_plot_lst=ROUND_HOURS_DIVISION_OF_DAY,
                                        use_original=False,use_cond_on_hours=True,
                                    cond_on_cluster_names=lambda x: True,
                                            clip=2,config=CONFIG,kpi='sum',
                                            figsize=(10,10),print_performance=False,
                              hours_subset_conf='v4'
                              ):
    hourly_profits = pd.DataFrame()

    for hours_to_plot_item in hours_to_plot_lst:
        if isinstance(hours_to_plot_item,tuple):
            hour_start, hour_end = hours_to_plot_item
            hours_to_plot = list(range(hour_start, hour_end))
        elif isinstance(hours_to_plot_item,str):
            hours_to_plot = [hours_to_plot_item]
        elif isinstance(hours_to_plot_item,list):
            hours_to_plot = hours_to_plot_item

        if isinstance(hours_to_plot[0],str):
            cond_on_hours = lambda x: x.strftime('%H%M') in hours_to_plot
        elif isinstance(hours_to_plot[0],int):
            cond_on_hours = lambda x: x.hour in hours_to_plot
        try:
            if use_cond_on_hours:
                # print(f'1: {dtdt.now()}')
                profits = main(mode=mode, calc=True, plot=False, clip=clip,
                               kpi=kpi, end_date=end_date,
                               weekdays_for_plot=weekdays_for_plot,
                               cond_on_hours=lambda x: cond_on_hours(x),
                               cond_on_cluster_names=lambda x: cond_on_cluster_names(x),
                               windows=[window], cancel_leakage_shift=False,
                               big_loss_values=[big_loss_value],
                               config=config,print_performance=print_performance,
                               hours_subset_conf=hours_subset_conf
                               )
                # print(f'2: {dtdt.now()}')
            else:
                profits = main(mode=mode, calc=True, plot=False, clip=clip,
                               kpi='sum', end_date=end_date,
                               weekdays_for_plot=weekdays_for_plot,
                               hours_to_plot=hours_to_plot,
                               windows=[window], cancel_leakage_shift=False,
                               big_loss_values=[big_loss_value],
                               config=config,
                              hours_subset_conf=hours_subset_conf
                               )
        except:
            continue

        profits = profits[['t_open'] + [x for x in list(profits) if (('original' not in x) if not use_original else ('original' in x)) and x != 't_open']]
        if isinstance(hours_to_plot_item,tuple):
            profits = profits.rename(columns={x: x + '_%s-%s' % (hours_to_plot[0], hours_to_plot[-1]+1) for x in
                                          [c for c in list(profits) if (('original' not in c) if not use_original else ('original' in c)) and c != 't_open']})
        elif isinstance(hours_to_plot_item,str):
            profits = profits.rename(columns={x: x + '_%s' % (hours_to_plot[0]) for x in
                                              [c for c in list(profits) if (('original' not in c) if not use_original else ('original' in c)) and c != 't_open']})
        elif isinstance(hours_to_plot_item,list):
            profits = profits.rename(columns={x: x + '_%s-%s' % (hours_to_plot[0], hours_to_plot[-1]) for x in
                                              [c for c in list(profits) if (('original' not in c) if not use_original else ('original' in c)) and c != 't_open']})

        if hourly_profits.shape[0] == 0:
            hourly_profits = profits
        else:
            hourly_profits = hourly_profits.merge(profits, on=['t_open'],how='outer').fillna(0)
    hourly_profits = hourly_profits[pd.to_datetime(hourly_profits['t_open']).dt.weekday.isin(weekdays_for_plot)]
    hourly_profits.set_index('t_open').cumsum().plot(title='%s (%s) weekdays = %s' % (mode,f'W{window}' if not use_original else '', weekdays_for_plot),
                                                     style=['-'] * 7 + ['-*'] * 7+['--']*7,figsize=figsize)
    plt.show()
    return hourly_profits

def check_performance_by_mode(modes_lst,weekdays_for_plot=[0,1,2,3,4],hours_to_plot='all',
                              windows=[9],big_loss_values=[None],
                              clip=2,config=CONFIG,
                              months=list(range(1,13)),
                              print_performance=True,normalize_to_percentage=False):
    final_profits = pd.DataFrame()
    if hours_to_plot == 'all':
        hours_to_plot = range(24)
    for mode in modes_lst:
        mode_short_name = mode.replace('ens_','').replace('_window4','')
        i = 0
        for window in windows:
            for big_loss_value in big_loss_values:
                i+=1
                profits = main(mode=mode, calc=True, plot=False, clip=clip,
                       kpi='sum', end_date=dtdt(2050, 7, 15),
                       weekdays_for_plot=weekdays_for_plot,
                       hours_to_plot=hours_to_plot,
                       # cond_on_hours=lambda x: x.hour in hours_to_plot,
                        cond_on_hours=lambda x: x.month in months,
                       windows=[window], cancel_leakage_shift=False,
                        big_loss_values=[big_loss_value],
                        config=config
                       )
                profits.loc[~pd.to_datetime(profits['t_open']).dt.month.isin(months),[c for c in list(profits) if 'profit' in c]] = 0
                nn= 0
                bigloss_suffix = '' if big_loss_value is None else f'_{big_loss_value}'
                profits.columns = ['t_open','profit_%s_w%s%s'%(mode_short_name,window,bigloss_suffix),'profit_original_%s'%mode_short_name]
                profits = profits[pd.to_datetime(profits['t_open']).dt.weekday.isin(weekdays_for_plot)]
                if final_profits.shape[0] == 0:
                    final_profits = profits
                else:
                    if i != 1:
                        profits = profits[list(profits)[:2]]
                    final_profits = final_profits.merge(profits,on=['t_open'])
    final_profits = final_profits.set_index('t_open')
    if print_performance:
        for c in list(final_profits):
            print(f'For {c} We have Calmar, MaxDD = {calc_calmar(final_profits[c],use_annual=False)} | Last 60Days: Calmar, MaxDD = {calc_calmar(final_profits[c][-60:],use_annual=False)}')

    if normalize_to_percentage:
        from Algo.Utils.files_handle import get_candles_outpath
        candles_df = pd.read_csv(get_candles_outpath('NG',True),parse_dates=['date'])
        daily_open = candles_df.groupby(candles_df['date'].dt.date).first()[['open']]
        final_profits = final_profits.merge(daily_open,left_index=True,right_index=True)
        final_profits['margin'] = 2700 * final_profits['open']
        for profit_col in [x for x in list(final_profits) if 'profit' in x]:
            final_profits[profit_col] = final_profits[profit_col] / final_profits['margin'] * 100
        final_profits = final_profits[[x for x in list(final_profits) if 'profit' in x]]

    final_profits.cumsum().plot(title='profits by mode',style=['-']*7+['-*']*7+['--']*7)
    plt.show()
    final_profits = final_profits.reset_index()
    final_profits['weekday'] = pd.to_datetime(final_profits['t_open']).dt.weekday
    for weekday in [0,1,2,3,4]:
        group_df = final_profits.loc[final_profits['weekday']==weekday].set_index('t_open')
        group_df.cumsum().plot(title=f'profits by mode for weekday {weekday}',style=['-']*7+['-*']*7+['--']*7)
    plt.show()

def check_performance_by_hour_new(mode='ens_calmar0.25_S0.5_window4',
                              weekdays_for_plot=[0],
                              window=10,
              hours_to_plot_lst=ROUND_HOURS_DIVISION_OF_DAY
                              ):
    hourly_profits = pd.DataFrame()

    for hours_to_plot_item in hours_to_plot_lst:
        assert isinstance(hours_to_plot_item,tuple) or isinstance(hours_to_plot_item,list)
        if isinstance(hours_to_plot_item[0], int):
            hour_start, hour_end = hours_to_plot_item
            hours_to_plot = [hours_to_plot_item[0],hours_to_plot_item[-1]]
            cond_on_hours = lambda x: x.hour in range(hour_start,hour_end)
        elif isinstance(hours_to_plot_item[0],str) and len(hours_to_plot_item[0]) == 4:
            hours_to_plot = [hours_to_plot_item[0],hours_to_plot_item[-1]]
            cond_on_hours = lambda x: x.strftime('%H%M') in hours_to_plot_item

        profits = main(mode=mode, calc=True, plot=False, clip=2,
                       kpi='sum', end_date=dtdt(2050, 7, 15),
                       weekdays_for_plot=weekdays_for_plot,
                       #hours_to_plot=hours_to_plot,
                       cond_on_hours=cond_on_hours,
                       windows=[window], cancel_leakage_shift=False
                       )
        profits = profits[['t_open'] + [x for x in list(profits) if 'original' not in x and x != 't_open']]
        if isinstance(hours_to_plot_item,tuple):
            profits = profits.rename(columns={x: x + '_%s-%s' % (hours_to_plot[0], hours_to_plot[-1]+1) for x in
                                          [c for c in list(profits) if 'original' not in c and c != 't_open']})
        elif isinstance(hours_to_plot_item,list):
            profits = profits.rename(columns={x: x + '_%s-%s' % (hours_to_plot[0], '%s'%hours_to_plot[-1]) for x in
                                          [c for c in list(profits) if 'original' not in c and c != 't_open']})
        elif isinstance(hours_to_plot_item,str):
            profits = profits.rename(columns={x: x + '_%s' % (hours_to_plot[0]) for x in
                                              [c for c in list(profits) if 'original' not in c and c != 't_open']})

        if hourly_profits.shape[0] == 0:
            hourly_profits = profits
        else:
            hourly_profits = hourly_profits.merge(profits, on=['t_open'])
    hourly_profits = hourly_profits[pd.to_datetime(hourly_profits['t_open']).dt.weekday.isin(weekdays_for_plot)]
    hourly_profits.set_index('t_open').cumsum().plot(title='%s weekdays = %s' % (mode, weekdays_for_plot),
                                                     style=['-'] * 7 + ['-*'] * 7+['--']*7)
    plt.show()
    return hourly_profits


def check_stoploss(window=10, weekdays_for_plot=[0, 1, 2, 3, 4],
                            mode='ens_calmar0.25_S0.5_window4',
                            losing_ratio_required=1,
                            resolution='1H', weekdays_method='split'):
    if resolution == '1H':
        hours_to_plot_lst = ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE  # ROUND_HOURS_DIVISION_OF_DAY
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH00  # ROUND_HOURS_DIVISION_OF_DAY
    elif resolution == '30m':
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY
    elif resolution == '30m_hybrid':
        # used correlation calculation to discover the hours with variance that's worth seperating into 30 min
        # see Algo/Notebooks/analyze_profits_by_30min.py
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY_HYBRID
    elif resolution == '15m':
        hours_to_plot_lst = DIVISION_15MIN_OF_DAY
    profits = check_performance_by_hour(window=window, weekdays_for_plot=weekdays_for_plot,
                                        mode=mode, hours_to_plot_lst=hours_to_plot_lst,
                                        )
    # check_performance_by_hour(window=11,weekdays_for_plot=[3], hours_to_plot_lst=afternoon_15mins)

    profits2 = profits[list(profits)[:-2]]  # .set_index('t_open')
    # profits2['weekday'] = pd.to_datetime(profits2['t_open']).dt.weekday

    cond_on_c = lambda x: '' in x  # '15-' in x or '14-' in x or '17-' in x
    dfs = []
    weekdays_subsets = [weekdays_for_plot]
    if weekdays_method == 'split':
        weekdays_subsets = [[wd] for wd in weekdays_for_plot]
    # weekdays_subsets = [[0,1,2,3,4]]
    ref_hours_lst = [(0,0),(0,8),(8,11),(11,12),(12,13),(11,14),
                     (6,8),(8,10),(10,11),(11,12),(12,13),(13,14),
                     (14,15),(15,16)]
    for ref_hour_start, ref_hour_end in ref_hours_lst:
        final_daily_profits = pd.DataFrame()
        for weekday_subset in weekdays_subsets:
            profits_tmp = profits2[pd.to_datetime(profits2['t_open']).dt.weekday.isin(weekday_subset)].copy()
            if ref_hour_start == ref_hour_end == 0:
                pass
            else:
                ref_columns = [x for x in list(profits_tmp)[1:] if ref_hour_start <= int(x.split('all_')[-1].split('-')[0]) < ref_hour_end]
                post_ref_columns = [x for x in list(profits_tmp)[1:] if ref_hour_end <= int(x.split('all_')[-1].split('-')[0]) < 20]
                ref_profit = profits_tmp[ref_columns].sum(axis=1)
                #print(f'Weekday: {weekday_subset} x Hours = {ref_hour_start, ref_hour_end} | Corr between ref and post: {pd.DataFrame({"ref":ref_profit.sum(axis=1),"post":profits_tmp[post_ref_columns].sum(axis=1)}).corr().iloc[0][1]}')
                cond_for_stoploss = (ref_profit < 0)&(ref_profit.diff()<-100)&((ref_profit>0).shift(1).rolling(5,2).mean()>=0)
                profits_tmp.loc[cond_for_stoploss,post_ref_columns] = 0
            profits_tmp.set_index('t_open',inplace=True)
            daily_profits = pd.DataFrame(profits_tmp.sum(axis=1), columns=['profit']).reset_index()
            if final_daily_profits.shape[0] == 0:
                final_daily_profits = daily_profits
            else:
                final_daily_profits = final_daily_profits.merge(daily_profits, on=list(final_daily_profits),
                                                                how='outer')
        final_daily_profits = final_daily_profits.sort_values('t_open').set_index('t_open')['profit']
        calmar, max_dd = calc_calmar(final_daily_profits, use_annual=False)
        sharpe = calc_sharpe(final_daily_profits)
        num_of_gte_1000_losses = (final_daily_profits <= -1000).sum()
        print('profit for window %s = %s | calmar, max_dd = (%s, %s) | sharpe = %s | {%s} Losses 1000$ ' % (
        f'{ref_hour_start}-{ref_hour_end}', final_daily_profits.sum(), calmar, max_dd, sharpe, num_of_gte_1000_losses))
        calmars_lst = [np.round(calc_calmar(profits_tmp[c])[0], 2) for c in list(profits_tmp)]
        print(calmars_lst)
        dfs.append(final_daily_profits.copy())

    comparison_df = pd.DataFrame(dfs).T
    comparison_df.columns = ['profit_window%s' % window for window in [str(item) for item in ref_hours_lst]]
    comparison_df.cumsum().plot(style=['-']*7+['-*']*7)
    plt.show()

def calc_drop_losing_hours_specific_window(profits2,weekday_subset,w,kpi,cond_on_c,
                                           losing_ratio_required,
                                           final_big_loss_threshold=500,
                                           window_for_print_dropped_hours=None):
    profits2 = profits2.sort_values('t_open')
    # profits2.to_csv(os.path.join(HOME,"tmp","deep_drop_profits_by_hour_%s.csv")%weekday_subset,index=False)

    profits_tmp = profits2[pd.to_datetime(profits2['t_open']).dt.weekday.isin(weekday_subset)].copy().set_index(
        't_open')
    if w > 0:
        if kpi == 'hr':
            losers_df = ((np.sign(profits_tmp + 0.0001) < 0).rolling(w,
                                                                     min(2, w)).mean() >= losing_ratio_required).shift(
                1).fillna(False)
            for c in list(profits_tmp):
                if cond_on_c(c):
                    profits_tmp.loc[losers_df[c], c] = 0
        elif kpi == 'bigloss':
            losers_df = (profits_tmp < -final_big_loss_threshold).rolling(w, min(2, w)).max().shift(1) == 1
            for c in list(profits_tmp):
                if cond_on_c(c):
                    profits_tmp.loc[losers_df[c], c] = 0
        elif kpi == 'bigloss_v2':
            losers_df = (profits_tmp < -final_big_loss_threshold).rolling(w, min(2, w)).max().shift(1) == 1
            big_winners_recently = (profits_tmp > 300).rolling(w, min(w, 2)).min().shift(1) == 1
            for c in list(profits_tmp):
                if cond_on_c(c):
                    profits_tmp.loc[(losers_df[c]) & (~big_winners_recently[c]), c] = 0
        elif kpi == 'dynamic_bigloss_v1':
            dynamic_losses = profits_tmp.clip(-1e4,0).replace(0,np.nan).rolling(30,3).quantile(losing_ratio_required).shift(1)
            losers_df = (profits_tmp < dynamic_losses).rolling(w, min(2, w)).max().shift(1) == 1
            for c in list(profits_tmp):
                if cond_on_c(c):
                    profits_tmp.loc[losers_df[c], c] = 0
        elif kpi == 'dynamic_bigloss_v2':
            # stack = []
            # for weekday in [[0],[1],[2],[3],[4],
            #                 [0,1,2,3,4]]:
            #     profits_tmp = profits2[
            #         pd.to_datetime(profits2['t_open']).dt.weekday.isin(weekday)].copy().set_index(
            #         't_open')
            #     for w in [10,12,15,18,21,25]:
            #         for neg_quant, pos_quant in [(0.75,0.25),(0.5,0.5),(0.25,0.75),
            #                                      (0.25,0.25),(0.25,0.5),(0.3,0.6),
            #                                      (0.2,0.8),(0.15,0.85),]:
            #             performance_ratio = abs((profits_tmp.clip(0,1e4).replace(0,np.nan).rolling(w,3).quantile(pos_quant)/profits_tmp.clip(-1e4,0).replace(0,np.nan).rolling(w,3).quantile(neg_quant)).shift(1)).fillna(1)
            #             for val in [-99,0.5,0.75,1,1.25,1.5,2]:
            #                 calmar,max_dd = calc_calmar(profits_tmp[performance_ratio>val].sum(axis=1),use_annual=False)
            #                 profit = profits_tmp[performance_ratio > val].sum().sum()
            #                 print(f'Window={w} x quants={(neg_quant,pos_quant)} x Val={val} Calmar={(calmar,max_dd)}, Profit = {profit}')
            #                 stack.append({'window':w,'val':val,'calmar':calmar, 'max_dd':max_dd,'profit':profit,
            #                               'quants':str((neg_quant,pos_quant)),
            #                               'weekday':str(weekday)})
            # kpis = pd.DataFrame(stack)
            # kpis['weekday'] = kpis['weekday'].astype(str)
            # by_quant = kpis[(kpis['val']>=0)].groupby('quants').mean()
            # by_val = kpis[(kpis['val'] >= 0)].groupby('val').mean()
            # by_window = kpis[(kpis['val'] >= 0)].groupby('window').mean()

            neg_quant, pos_quant = 0.25,0.75
            # neg_quant, pos_quant = 0.15,0.85
            val = 1 #0.75 # /try 0.75,0.6
            window = w #18 # try 15,21
            performance_ratio = abs((profits_tmp.clip(0, 1e4).replace(0, np.nan).rolling(window, 1).quantile(pos_quant) / profits_tmp.clip(-1e4, 0).replace(0, np.nan).rolling(window, 1).quantile(neg_quant)).shift(1)).fillna(1)
            losers_df = performance_ratio < val
            for c in list(profits_tmp):
                if cond_on_c(c):
                    profits_tmp.loc[losers_df[c], c] = 0
        bb = 0
        if window_for_print_dropped_hours is not None:
            if w == window_for_print_dropped_hours:
                print(f'\tFor Window={w} x weekdays_subset={weekday_subset}: We have the following losers:\n\t\t{[x.split("_")[-1] for x in losers_df.iloc[-1][losers_df.iloc[-1]].index.tolist()]}')
    daily_profits = pd.DataFrame(profits_tmp.sum(axis=1), columns=['profit']).reset_index()
    return daily_profits, profits_tmp

def print_kpis_with_90d(daily_profits):
    (calmar, maxdd), sharpe = calc_calmar(daily_profits,use_annual=False), calc_sharpe(daily_profits)
    (calmar90, maxdd90), sharpe90 = calc_calmar(daily_profits[-90:],use_annual=False), calc_sharpe(daily_profits[-90:])
    print(f'profit {daily_profits.sum()} | calmar = {calmar}, MaxDD = {maxdd}, sharpe = {sharpe}')
    print(f'profit 90D: {daily_profits[-90:].sum()} | calmar 90D = {calmar90}, MaxDD 90D = {maxdd90}, sharpe 90D= {sharpe90}')
    return {'calmar':calmar,'maxdd':maxdd,'sharpe':sharpe,'calmar90':calmar90,'maxdd90':maxdd90,'sharpe90':sharpe90}

    
def print_kpis(final_daily_profits,window_name,round=2,skip_printing=False):
    calmar, max_dd = calc_calmar(final_daily_profits, use_annual=False)
    sharpe = calc_sharpe(final_daily_profits)
    # round
    calmar = np.round(calmar,round)
    max_dd = np.round(max_dd,round)
    sharpe = np.round(sharpe,round)
    num_of_gte_1000_losses = (final_daily_profits <= -1000).sum()
    pnl = np.round(final_daily_profits.sum(), 2)
    if not skip_printing:
        print('profit for window %s = %s | calmar, max_dd = (%s, %s) | sharpe = %s | {%s} Losses 1000$ ' % (
        window_name, pnl, calmar, max_dd, sharpe, num_of_gte_1000_losses))
    results_dict = {'window_name':window_name,'calmar':calmar, 'max_dd':max_dd,'sharpe':sharpe,'pnl':pnl}
    return results_dict

def load_candles_df():
    from Algo.Utils.files_handle import get_candles_outpath

    candles_csv = get_candles_outpath('NG', True)
    print(candles_csv)
    candles_df = pd.read_csv(candles_csv, parse_dates=['date', 'time_utc'])

    candles_df['t_open'] = candles_df['time_utc']

    # leave the first row in each day
    candles_df = candles_df[['t_open', 'open']].groupby(candles_df['t_open'].dt.date).first()[
        ['open']].reset_index()
    candles_df['date'] = candles_df['t_open']  # pd.to_datetime(candles_df['t_open']).dt.da
    candles_df = candles_df[['date', 'open']]
    return candles_df

def check_drop_losing_hours(window=10,weekdays_for_plot=[0,1,2,3,4],
                            big_loss_value=None,
                            mode='ens_calmar0.25_S0.5_window4',
                            losing_ratio_required=1,kpi='hr',
                            clip=2,
                            resolution='1H',weekdays_method='split',
                            use_original=False,
                            plot=True,
                            chosen_window=5,
                            windows_for_comb=[3,4,5],
                            config=CONFIG,
                            hours_lst=list(range(24)),
                            chosen_windows_for_hourly_plot=[(4, list(range(4, 20)))],
                            windows = [0,1,2,3,4,5,6,8,10,12,15],
                            figsize=(8,8),
                            cond_on_cluster_names=lambda x: True,
                            offline_mode=False,
                            normalize_to_pct=False,
                            hours_subset_conf='v4'):
    if resolution == '1H':
        hours_to_plot_lst = ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE
        # hours_to_plot_lst = [(0,1),(1,2),(2,4),(4,6),(6,7),(7,8)]
    elif resolution == '1H15':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH15
    elif resolution == '1H30':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH30
    elif resolution == '1H45':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH45
    elif resolution == 'hybrid':
        hours_to_plot_lst = [(0,6),(6,7),(7,8),(8,9),(9,10),(10,11),(11,12),(12,13),(13,14),(14,15),(15,16),
                             (16,17),(17, 18),(19,20)]
        hours_to_plot_lst = [(0, 8), (8, 10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
        # hours_to_plot_lst = [(0, 6),(6,8), (8, 10), (10, 12), (12, 14), (14, 17), (17, 19), (19, 20),]
        #ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE
    elif resolution == '30m':
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY
    elif resolution == '30m_hybrid':
        # used correlation calculation to discover the hours with variance that's worth seperating into 30 min
        # see Algo/Notebooks/analyze_profits_by_30min.py
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY_HYBRID
    elif resolution == '15m':
        hours_to_plot_lst = DIVISION_15MIN_OF_DAY
    hours_to_plot_lst = [x for x in hours_to_plot_lst if x[0] in hours_lst or int(str(x[0])[:2]) in hours_lst]
    profits = check_performance_by_hour(window=max(window,1),weekdays_for_plot=[0,1,2,3,4],
                                        mode=mode,hours_to_plot_lst=hours_to_plot_lst,
                                        cond_on_cluster_names=lambda x: cond_on_cluster_names(x),
                                        clip=clip,use_original=use_original,
                                        big_loss_value=big_loss_value,
                                        # kpi=kpi,
                                        config=config,
                                        hours_subset_conf=hours_subset_conf)

    if normalize_to_pct:
        candles_df = load_candles_df()

    profits2 = profits

    cond_on_c = lambda x: '' in x
    dfs = []
    if weekdays_method == 'all':
        weekdays_subsets = [weekdays_for_plot]
    elif weekdays_method in ['split','hybrid']:
        # hybrid -- > do both
        weekdays_subsets = [[wd] for wd in weekdays_for_plot]
    elif weekdays_method == 'w012':
        weekdays_subsets = [[0,1,2],[3,4]]
    else:
        raise NotImplementedError()
    chosen_df_for_hourly_plot_dict = {}
    base_df_for_hourly_plot = None
    for w in windows:
        final_daily_profits = pd.DataFrame()
        final_hourly_profits = pd.DataFrame()
        for weekday_subset in weekdays_subsets:
            profits_tmp = profits2.copy()
            max_depth=1
            for depth in range(1,max_depth+1):
                if weekdays_method == 'hybrid':
                    if weekday_subset in [[0],[1],[2]]:
                        profits3 = profits2.copy()
                        if 't_open' not in list(profits3):
                            profits3 = profits3.reset_index()
                        daily_profits, profits_tmp = calc_drop_losing_hours_specific_window(profits3, [0,1,2], w, kpi,cond_on_c,losing_ratio_required,
                                                                                            final_big_loss_threshold=600)

                elif weekdays_method == 'w012' and 0 not in weekday_subset:
                    daily_profits, profits_tmp = calc_drop_losing_hours_specific_window(
                        profits_tmp if 't_open' in list(profits_tmp) else profits_tmp.reset_index(), weekday_subset, w, kpi,
                        cond_on_c,losing_ratio_required, final_big_loss_threshold=350)
                else:
                    daily_profits, profits_tmp = calc_drop_losing_hours_specific_window(profits_tmp if 't_open' in list(profits_tmp) else profits_tmp.reset_index(), weekday_subset, w, kpi, cond_on_c,
                                                       losing_ratio_required,
                                                    final_big_loss_threshold=350 if weekdays_method not in ['w012'] else 500,
                                                        window_for_print_dropped_hours=chosen_windows_for_hourly_plot[0][0])

            profits_tmp_new = profits_tmp.reset_index()

            if final_daily_profits.shape[0] == 0:
                final_daily_profits = daily_profits
                final_hourly_profits = profits_tmp_new
            else:
                final_daily_profits = final_daily_profits.merge(daily_profits,on=list(final_daily_profits),
                                                                how='outer')
                final_hourly_profits = final_hourly_profits.merge(profits_tmp_new,on=list(final_hourly_profits),how='outer')
        final_daily_profits = final_daily_profits.sort_values('t_open').set_index('t_open')['profit']
        print_kpis(final_daily_profits,w)

        # normalize if needed
        if normalize_to_pct:
            final_daily_profits = final_daily_profits.reset_index()
            final_daily_profits['date'] = pd.to_datetime(final_daily_profits['t_open']).dt.date
            final_daily_profits = final_daily_profits.merge(candles_df, on=['date'], how='left')
            final_daily_profits['open'] = final_daily_profits['open'].ffill().bfill()
            # print(final_daily_profits[['total_profit','open']].iloc[0])
            final_daily_profits['profit'] = final_daily_profits['profit'] / (final_daily_profits['open'] * 100)
            final_daily_profits = final_daily_profits.set_index('t_open')['profit']
        calmars_lst = [np.round(calc_calmar(profits_tmp[c])[0],2) for c in list(profits_tmp)]
        print (calmars_lst)
        dfs.append(final_daily_profits.copy())

        if w == 0:
            base_df_for_hourly_plot = final_hourly_profits.sort_values('t_open').set_index('t_open')
        if w in [x[0] for x in chosen_windows_for_hourly_plot]:
            chosen_df_for_hourly_plot = final_hourly_profits.sort_values('t_open').set_index('t_open')
            chosen_df_for_hourly_plot_dict[w] = chosen_df_for_hourly_plot


    comparison_df = pd.DataFrame(dfs).T
    comparison_df.columns = ['profit_window%s'%window for window in [str(item) for item in windows]]
    if plot:
        #comparison_df.to_csv(os.path.join(HOME,"tmp","drop_losing_hours_comparison_df.csv"))
        rolling_profits = comparison_df.rolling(18, 18).mean()
        # rolling_profits.plot(style=['-']*7+['-*']*7,title='18-day rolling mean')
        winners_table = rolling_profits[[x for x in list(rolling_profits) if int(x[-1]) in windows_for_comb]].shift(1).idxmax(axis=1).fillna(method='bfill')
        comb_profit = pd.Series(comparison_df.lookup(winners_table.index, winners_table),
                                index=winners_table.index)
        bear_cond1 = comb_profit.rolling(5, 5).mean().shift(1) < comb_profit.rolling(20,20).mean().shift(1) - 100 # w=15 also good (see notebook)
        bear_cond2 = comb_profit.rolling(3, 3).mean().shift(1) <= comb_profit.rolling(6,6).mean().shift(1)
        bear_cond3 = comb_profit.rolling(2, 2).min().shift(1) < -1000
        bear_cond = (bear_cond1 & bear_cond2)|bear_cond3
        comparison_df[f'profit_rolling_winners({windows_for_comb})'] = comb_profit
        comparison_df['profit_rolling_Comb'] = comb_profit
        comparison_df.loc[bear_cond,'profit_rolling_Comb'] = comparison_df.loc[bear_cond,'profit_rolling_Comb'] * 0.5
        comparison_df.loc[bear_cond,'profit_rolling_Comb'] = comparison_df.loc[bear_cond,'profit_rolling_Comb'] * 0.5
        print_kpis(comparison_df['profit_rolling_Comb'], 'rolling_Comb')
        print_kpis(comparison_df[f'profit_rolling_winners({windows_for_comb})'], 'rolling_winners')
        comparison_df.cumsum().plot(style=['-'] * 7 + ['-*'] * 7,figsize=figsize)
        plt.show()

        chosen_df_for_hourly_plot_hybrid = None
        for w,hours in chosen_windows_for_hourly_plot:
            tmp = chosen_df_for_hourly_plot_dict[w]
            tmp = tmp[[x for x in list(tmp) if int(x.split('_')[-1].split('-')[0]) in hours]]
            if chosen_df_for_hourly_plot_hybrid is None:
                chosen_df_for_hourly_plot_hybrid = tmp
            else:
                chosen_df_for_hourly_plot_hybrid = chosen_df_for_hourly_plot_hybrid.merge(tmp,left_index=True,right_index=True,how='outer')
        chosen_df_for_hourly_plot_hybrid = chosen_df_for_hourly_plot_hybrid.reset_index().sort_values('t_open').set_index('t_open')

        chosen_windows_for_hourly_plot_short = [(x[0], [x[1][0], x[1][-1]]) for x in chosen_windows_for_hourly_plot]
        chosen_df_for_hourly_plot_hybrid.sum(axis=1).cumsum().plot(title='Total Profit',figsize=figsize)
        print_kpis(chosen_df_for_hourly_plot_hybrid.sum(axis=1),str(chosen_windows_for_hourly_plot_short))
        chosen_df_for_hourly_plot_hybrid.cumsum().plot(style=['-']*7+['--']*7+['-*']*7,title=f'Hourly performance for window {chosen_windows_for_hourly_plot_short}',
                                                       figsize=figsize)

        base_df_for_hourly_plot.cumsum().plot(style=['-']*7+['--']*7+['-*']*7,title=f'Original performance for window {chosen_windows_for_hourly_plot_short}',
                                              figsize=figsize)
        def _compare_each_hour(base_df_for_hourly_plot,chosen_df_for_hourly_plot_hybrid):
            comparison_df2 = base_df_for_hourly_plot.merge(chosen_df_for_hourly_plot_hybrid,on=['t_open'],suffixes=('_base','_chosen'))
            hours_until8 = [c for c in list(base_df_for_hourly_plot) if 'profit' in c and int(c.split('_')[-1].split('-')[0])<8]
            hours_until16 = [c for c in list(base_df_for_hourly_plot) if 'profit' in c and 8<= int(c.split('_')[-1].split('-')[0])<16]
            hours_from16 = [c for c in list(base_df_for_hourly_plot) if 'profit' in c and 16 <= int(c.split('_')[-1].split('-')[0])<24]

            comparison_df2[[x for x in list(comparison_df2)
                                               if '_'.join(x.split('_')[:-1]) in hours_until8]].cumsum().plot(style=['--','-']*8,
                                                                                                      color=[str(x) for x in list(np.repeat(['b', 'g', 'r', 'c', 'm', 'y', 'k','magenta'],2))])
            comparison_df2[[x for x in sorted(list(comparison_df2))
                                               if '_'.join(x.split('_')[:-1]) in hours_until16]].cumsum().plot(style=['--','-']*8,
                                                                                                      color=[str(x) for x in list(np.repeat(['b', 'g', 'r', 'c', 'm', 'y', 'k','magenta'],2))])
            comparison_df2[[x for x in sorted(list(comparison_df2))
                                               if '_'.join(x.split('_')[:-1]) in hours_from16]].cumsum().plot(style=['--','-']*8,
                                                                                                      color=[str(x) for x in list(np.repeat(['b', 'g', 'r', 'c', 'm', 'y', 'k','magenta'],2))])
            chosen_df_for_hourly_plot_hybrid = chosen_df_for_hourly_plot_hybrid.sum(axis=1).reset_index()
            chosen_df_for_hourly_plot_hybrid['weekday'] = pd.to_datetime(chosen_df_for_hourly_plot_hybrid['t_open']).dt.weekday
            plt.show()
            return comparison_df2

    def _analyze_smart_hours_choice_for_second_filter(comparison_df):
        # todo
        # add dynamic filtering, rather than all time !!!
        df2 = pd.DataFrame(
            {c: calc_calmar(comparison_df[c], use_annual=False) for c in list(comparison_df)}).T.reset_index().sort_values(
            'index')  # .set_index('index')
        df2['hour'] = df2['index'].apply(lambda x: x.replace('_base', '').replace('_chosen', ''))
        df2['mode'] = df2['index'].apply(lambda x: x.split('_')[-1])
        p = pd.pivot_table(df2, index=['hour'], values=[0, 1], columns=['mode'])
        comparison_df['total_base'] = comparison_df[[x for x in list(comparison_df) if 'base' in x]].sum(axis=1)
        comparison_df['total_chosen'] = comparison_df[[x for x in list(comparison_df) if 'chosen' in x]].sum(axis=1)
        for ww in [3,5,7,10,15]:
        #ww = 5
            for base_col in [x for x in list(comparison_df) if 'base' in x]:
                chosen_col = base_col.replace('_base','_chosen')
                final_col = base_col.replace('_base','_final')
                comparison_df[final_col] = comparison_df[base_col].copy()
                comparison_df.loc[comparison_df[base_col].rolling(ww,1).mean().shift(1) < comparison_df[chosen_col].rolling(ww,1).mean().shift(1),final_col] = comparison_df[chosen_col]
            comparison_df[f'total_final_W{ww}'] = comparison_df[[x for x in list(comparison_df) if 'final' in x]].sum(axis=1)
        #final_profit = comparison_df[[x for x in list(comparison_df) if 'final' in x]].sum(axis=1)
        # base_profit = comparison_df[[x for x in list(comparison_df) if 'base' in x]].sum(axis=1)
        # chosen_profit = comparison_df[[x for x in list(comparison_df) if 'chosen' in x]].sum(axis=1)
        # df_to_plot = pd.DataFrame({'base':base_profit,'chosen':chosen_profit,'final':final_profit},index=comparison_df.index.tolist())
        comparison_df[[x for x in list(comparison_df) if 'total' in x]].cumsum().plot(figsize=(15,10))
        # for t in [0.5,0.6,0.7,0.8,0.9,0.95,1,1.5,2,3]:
        #     desired_hours_for_filtering = p[abs(p[(1,'chosen')])<abs(p[(1,'base')])*t].index.tolist()
        #     comparison_df[f'total_smart_hours_choice_{t}'] = comparison_df[[x for x in list(comparison_df) if 'chosen' in x and x.replace('_chosen','') in desired_hours_for_filtering]].sum(axis=1) + \
        #                                             comparison_df[[x for x in list(comparison_df) if 'base' in x and x.replace('_base','') not in desired_hours_for_filtering]].sum(axis=1)+(0*t)
        #     print_kpis(comparison_df[f'total_smart_hours_choice_{t}'],f'total_smart_hours_choice_{t}')
        # comparison_df[[x for x in list(comparison_df) if 'total' in x]].cumsum().plot(style=['-']*7+['--']*7)
        plt.show()

    if offline_mode:
        comparison_df2 = _compare_each_hour(base_df_for_hourly_plot,chosen_df_for_hourly_plot_hybrid)
        _analyze_smart_hours_choice_for_second_filter(comparison_df2)
    return dfs[windows.index(chosen_window)]

def _get_hours_lst_by_resolution(resolution,hours_lst):
    if resolution == '1H':
        hours_to_plot_lst = ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE
        # hours_to_plot_lst = [(0,1),(1,2),(2,4),(4,6),(6,7),(7,8)]
    elif resolution == '1H15':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH15
    elif resolution == '1H30':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH30
    elif resolution == '1H45':
        hours_to_plot_lst = DIVISION_60MIN_OF_DAY_HH45
    elif resolution == 'hybrid':
        hours_to_plot_lst = [(0,6),(6,7),(7,8),(8,9),(9,10),(10,11),(11,12),(12,13),(13,14),(14,15),(15,16),
                             (16,17),(17, 18),(19,20)]
        hours_to_plot_lst = [(0, 8), (8, 10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (15, 17), (17, 18), (18, 20)]
        # hours_to_plot_lst = [(0, 6),(6,8), (8, 10), (10, 12), (12, 14), (14, 17), (17, 19), (19, 20),]
        #ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE
    elif resolution == '30m':
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY
    elif resolution == '30m_hybrid':
        # used correlation calculation to discover the hours with variance that's worth seperating into 30 min
        # see Algo/Notebooks/analyze_profits_by_30min.py
        hours_to_plot_lst = DIVISION_30MIN_OF_DAY_HYBRID
    elif resolution == '15m':
        hours_to_plot_lst = DIVISION_15MIN_OF_DAY
    hours_to_plot_lst = [x for x in hours_to_plot_lst if x[0] in hours_lst or int(str(x[0])[:2]) in hours_lst]
    return hours_to_plot_lst

def wrap_deep_drop_losing_hours(confs_lst,window=10,
                            big_loss_value=None,
                            mode='ens_calmar0.25_S0.5_window4',
                            clip=2,resolution='1H',hours_lst=list(range(0,24)),
                            use_original=False,
                            config=CONFIG,
                            profits_dfs_by_resolution={},
                                plot=False
                            ):
    hours_to_plot_lst = _get_hours_lst_by_resolution(resolution,hours_lst)
    if resolution in profits_dfs_by_resolution:
        profits = profits_dfs_by_resolution[resolution]
    else:
        profits = check_performance_by_hour(window=max(window,1),weekdays_for_plot=[0,1,2,3,4],
                                        mode=mode,hours_to_plot_lst=hours_to_plot_lst,
                                        clip=clip,use_original=use_original,
                                        big_loss_value=big_loss_value,
                                        config=config)
    profits2 = profits
    cond_on_c = lambda x: '' in x

    profits_tmp = profits2.copy() #
    dfs = []
    for conf in confs_lst:
        dfs.append(profits_tmp.set_index('t_open').sum(axis=1))
        if conf['window'] > 0:
            daily_profits, profits_tmp2 = calc_drop_losing_hours_specific_window(profits_tmp, conf['weekdays_subset'], conf['window'], conf['kpi'],
                cond_on_c,conf['losing_ratio'],final_big_loss_threshold=conf['bigloss_threshold'])
            profits_tmp2 = profits_tmp2.reset_index()
            if conf['weekdays_subset'] != [0, 1, 2, 3, 4]:
                profits_tmp = profits_tmp[~pd.to_datetime(profits_tmp['t_open']).dt.weekday.isin(conf['weekdays_subset'])]
                profits_tmp = profits_tmp.merge(profits_tmp2, on=list(profits_tmp), how='outer')
            else:
                profits_tmp = profits_tmp2


    dfs.append(profits_tmp.set_index('t_open').sum(axis=1))
    df = pd.concat(dfs,axis=1).rename(columns={i:name for i,name in enumerate(['conf%s'%i for i in range(len(confs_lst)+1)])})
    #profits_tmp.set_index('t_open').sum(axis=1).cumsum().plot()
    if plot:
        df.cumsum().plot()
        plt.show()
    return profits_tmp,df

def check_15min_partition(window=10):
    original_profits = check_performance_by_hour(window=window,weekdays_for_plot=[0,1,2,3,4])
    original_profits2 = original_profits[list(original_profits)[:-2]].set_index('t_open')
    daily_profits_by_hour = original_profits2.sum(axis=1)
    # 15 mins
    profits_15mins = check_performance_by_hour(window=window,weekdays_for_plot=[0,1,2,3,4], hours_to_plot_lst=all_15mins)
    profits_15mins2 = profits_15mins.set_index('t_open')
    daily_profits_15mins = profits_15mins2.sum(axis=1)
    calmar, max_dd = calc_calmar(daily_profits_15mins, use_annual=False)
    print('profit for 15Min separation, window %s = %s | calmar, max_dd = (%s, %s)' % (window, daily_profits_15mins.sum(), calmar, max_dd))
    pd.DataFrame(daily_profits_by_hour).merge(pd.DataFrame(daily_profits_15mins), left_index=True, right_index=True).cumsum().plot()
    plt.show()

def check_bollinger_positions():
    bollinger = pd.read_csv(os.path.join(HOME,"performance_analysis","bollinger_positions","dynamic_bollinger_30x150_1H.csv"),
                parse_dates=['date']).rename(columns={'date':'t_open'})
    calmar3 = pd.read_csv(os.path.join(HOME,"performance_analysis","Real_Trades","Clusters_Performance_mode=ens_calmar3_S0.5_window4_weekdays=[0, 1, 2, 3, 4]_Alldays=250_hours=[0,20].csv"),
                          parse_dates=['t_open'])[['t_open','delta_y','quantity_All']].rename(columns={'quantity_All':'quantity'})
    mrg = calmar3.merge(bollinger[['t_open','Side']],on=['t_open'],
                        how='outer').fillna(method='ffill')
    mrg = mrg[mrg['t_open']>=calmar3['t_open'].min()]
    mrg['profit_algo'] = mrg['quantity']*mrg['delta_y'] * 10000 - abs(mrg['quantity'].diff()).fillna(0)*7.5
    mrg['profit_bollinger'] = mrg['Side']*mrg['delta_y'] * 10000 - abs(mrg['Side'].diff()).fillna(0)*7.5
    mrg = mrg[mrg['t_open'].dt.weekday.isin([0,1,2,4])&(mrg['t_open'].dt.hour.isin(range(1,21)))]#[1,2,3]+list(range(7,14))+[16,17,18]))]
    bad_sum = mrg[np.sign(mrg['Side']*mrg['quantity'])==-1].sum()['profit_algo']
    good_sum = mrg[np.sign(mrg['Side']*mrg['quantity'])==1].sum()['profit_algo']
    bb = 0
    mrg = mrg[#(np.sign(mrg['Side']*mrg['quantity'])==1)&
              # (abs(mrg['Side'])>1
              (mrg['t_open'].dt.minute.isin([15,45])
               )]
    mrg.set_index('t_open')[['profit_bollinger','profit_algo']].cumsum().plot()
    plt.show()


if __name__ == '__main__':

    night_15mins = ['%s%s'%(str(h).zfill(2),mm) for h in range(7) for mm in ['00','15','30','45']]
    morning_15mins = ['%s%s'%(str(h).zfill(2),mm) for h in range(7,11) for mm in ['00','15','30','45']]

    noon_15mins = ['%s%s'%(str(h).zfill(2),mm) for h in range(11,15) for mm in ['00','15','30','45']]
    afternoon_15mins = ['%s%s'%(str(h).zfill(2),mm) for h in range(15,20) for mm in ['00','15','30','45']]
    all_15mins = ['%s%s'%(str(h).zfill(2),mm) for h in range(0,20) for mm in ['00','15','30','45']]

    # check_bollinger_positions()

    # deep_drop_losing_hour wrap see in notebooks

    losing_hours = True
    losing_hours = False
    if losing_hours:
        # todo - revisit other hours_subset_conf (not v4!)
        hours_subset_conf = 'v4'
        chosen_window_profits = check_drop_losing_hours(
        # check_stoploss(
                                9,
                                # mode='ens_calmar0.25_S0.5_window4',
        #                         9,mode='real',
                                mode='ens_calmar0.25_S0.5_window4',
        #                         3,mode='real',
                                use_original=True,
                                # use_original=True,
                                big_loss_value=None,

                                # weekdays_for_plot=[1],
                                # weekdays_for_plot=[0],
                                #windows=[0,12,15,18,21,24],
                                weekdays_for_plot=[0,1,2,3,4],
                                # weekdays_for_plot=[0],
                                clip=2, # 3
                                losing_ratio_required=0.6,

                                # for [8-13] +[16,17]
                                # kpi='dynamic_bigloss_v1',resolution='1H',  # not bad for 8-13 + 16,17 too !!!! especially during recent oct-dec
                                kpi='bigloss',resolution='1H',  # not bad for 8-13 + 16,17 too !!!! especially during recent oct-dec
                                # kpi='hr',resolution='30m',  # for 4,5,6,7,13,

                                weekdays_method='split',
                                # weekdays_method='w012',
                                # weekdays_method='hybrid',
                                # weekdays_method='all',
                                windows_for_comb=[3,4,5], #[4,5]
                                config='v3b',
                                hours_subset_conf=hours_subset_conf,

                                hours_lst=[x for x in range(6,20)],
                                # hours_lst=[x for x in range(6,20) if x not in [13,15]],
                                # hours_lst=[x for x in range(6,14)]+[15,16,17,18,3],
                                #hours_lst=[x for x in range(0,6)]+[14,15,16,19],
                                # hours_lst=list(range(5,8))+list(range(9,13))+list(range(16,18))+[3,14],
                                # hours_lst=list(range(0,8))+list(range(9,13))+list(range(16,17)),
                                #hours_lst=[x for x in range(24) if x not in list(range(0,8))+list(range(9,13))+list(range(16,18))+[19]],
                                # hours_lst=[4,5,6,7,13],

                                # chosen_windows_for_hourly_plot=[(4, list(range(8,13))),(12,list(range(13,20)))],
                                chosen_windows_for_hourly_plot=[#(4, list(range(8,11))),
                                                                # (5, list(range(11, 14))),
                                                                (0, list(range(3, 20))),
                                                                #(2, list(range(16, 18))),
                                                                ],
                                offline_mode=True
                                ) # split
        bb = 0

    check_by_mode = False
    check_by_mode = True
    ## Check peformance by opex week! todo
    if check_by_mode:
        check_performance_by_mode(['ens_calmar0.25_S0.5_window4',
                                   'ens_calmar3_S0.5_window4',
                                   # 'ens_calmar3_S0.75_window4',
                               # 'ens_calmar0.25_S0.87_window4',
                               'real'
                               ],
                            # weekdays_for_plot=[0,1,2,3,4],
                            weekdays_for_plot=[
                                               0,1,2,3,4
                                               # 1,2,3
                                                ],
                              # hours_to_plot=list(range(0,24)),
                            #   hours_to_plot=list(range(5,13))+[16,17],
                              hours_to_plot=list(range(0,24)),
                              # hours_to_plot=list(range(0,8))+list(range(9,13))+list(range(14,19)),
                              # hours_to_plot=[13],
                              windows=[3,5,
                                       9
                                       # 7,
                                       # 9, #,20
                                       # 12,15,20
                                       ],
                                months=[1,2,3,4,5,6,7,8,9,10,11,12],
                                big_loss_values=[None,
                                                 #2500,1500,1000,800,600,
                                                 ],
                                    config='v3b', #b'
                                  clip=2,
                                  normalize_to_percentage=False
                                  # normalize_to_percentage=True
                                  )
    # check_positions('ens_calmar3_S0.5_window4', dtdt(2022, 11, 16), '20221113')

    # profits_v3 = get_profits_v3(window=9,weekdays_for_plot=[0,1,2,3,4],mode='ens_calmar3_S0.5_window4')
    # profits_v4 = get_profits_v4('ens_cluster_dynamic_v4_S0.5_w9',weekdays=[0,1,2,3,4],
    #                             hours=range(0,22))
    # profits_v4 = get_profits_v4('ens_cluster_dynamic_v4_S0.5_w9_hybrid_full',weekdays=[0,1,2,3,4],
    #                             hours=range(0,23))
    # check_performance_by_hour_v4('ens_cluster_dynamic_v3_S0.5_w9',weekdays=[4],
    #                              hour_start=8,hour_end=20,resolution='1H')
    # check_performance_by_hour_v4('ens_cluster_dynamic_v5_S0.5_dynamic',weekdays=[3])
    # check_performance_by_hour_v4('ens_cluster_dynamic_v4_S0.5_w9_hybrid',weekdays=[3])
    #check_performance_by_hour_v4('ens_cluster_dynamic_v3_S0.5_w9')

    check_performance_by_hour(

                              window=9,weekdays_for_plot=[2],mode='ens_calmar0.25_S0.5_window4',
                              #window=9,weekdays_for_plot=[0],mode='ens_calmar0.25_S0.5_window4',
                              #  end_date=dtdt(2030,1,1),
                              #  hours_to_plot_lst=AM_DIVISION_15MIN_OF_DAY,
                              #  hours_to_plot_lst=AM_DIVISION_30MIN_OF_DAY,
                              #  hours_to_plot_lst=NOON_DIVISION_15MIN_OF_DAY_SMALL,
                              #  hours_to_plot_lst=NOON_DIVISION_15MIN_OF_DAY_EARLY_SMALL,
                              # hours_to_plot_lst=NOON_DIVISION_30MIN_OF_DAY,
                              # hours_to_plot_lst=NOON_DIVISION_30MIN_OF_DAY_FULL,
                              # hours_to_plot_lst=NOON_DIVISION_30MIN_OF_DAY_0045_1530,

                                 hours_to_plot_lst=[x for x in ROUND_1HOUR_DIVISION_OF_DAY_UNIQUE[6:-1] if x[0] == 13],
                             # hours_to_plot_lst=MIDDAY_DIVISION_30MIN_OF_DAY,
                             #   hours_to_plot_lst=EVENING_DIVISION_30MIN_OF_DAY_FULL,
                             #   hours_to_plot_lst=EVENING_DIVISION_15MIN_OF_DAY,
                              #   hours_to_plot_lst=AM_DIVISION_15MIN_OF_DAY,
                              #   hours_to_plot_lst=NOON_DIVISION_15MIN_OF_DAY_SMALL,
                              #  use_original=False,
                              use_original=False,
                              use_cond_on_hours=True,
                              config='v3b',
                              #config='v4',
                                clip=2,

                              )

    # main(mode='ens_calmar3_S0.5_window4', calc=True, plot=True, clip=2,kpi='sum', #kpi='sum',
    #      weekdays_for_plot=[0,1,2,3,4],windows=[2,3,5,7,8,9,10,12,14,20],
    #      # cond_on_hours=lambda x: x.hour in range(14,17),
    #      #hours_to_plot=list(range(0,13))+list(range(15,22)),
    #      end_date=dtdt(2025,8,24)
    #      ) #x.strftime('%H%M') in ['1200','1215','1230','1245'])


