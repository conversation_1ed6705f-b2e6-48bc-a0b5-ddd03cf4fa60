WEEKLY_ADDITIONS = ['y_1100-1400_GEFS18Zday15Strict', 'y_1315-1745_EPS4528to423DNeg', 'y_1100-1745_PARAws10mNeg', 'y_1200-1945_epsFcst12ZbCorW15', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_1245-1745_SeasonalComb2', 'y_0800-1845_WTIopenStrict', 'y_0800-1845_ECEPSPrev1D', 'y_1215-1315_cashEC', 'y_0800-1415_GEFSCombWed', 'y_1245-1745_SeasonalComb2$b', 'y_0600-0800_GEFS0zComb', 'y_1130-1200_GFSv16z$0', 'y_1315-1415_GEFS6zp48to16Comb', 'y_1715-1915_GEPSComb$1', 'y_1130-1200_GFSv16z$9', 'y_1415-1615_GFS6Zrolling2$2', 'y_1315-1415_americanSeasonalb$1', 'y_1415-1745_monComb', 'y_0800-1845_SeasonalCombWed', 'y_1300-1400_EPS12zp12', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_1715-1845_PARACO12z0to8$2', 'y_1215-1300_PARA6zp10to8', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_0600-0800_eps12Z', 'y_1300-1545_PARA018Z0to8$3']
#
WEEKLY_ADDITIONS_STRICT = ['y_1215-1315_cashEC', 'y_1130-1200_GFSv16z$9', 'y_1300-1400_EPS12zp12', 'y_0800-1845_SeasonalCombWed', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_1315-1415_GEFS6zp48to16Comb', 'y_1315-1745_EPS4528to423DNeg', 'y_0600-0800_GEFS0zComb', 'y_0800-1845_ECEPSPrev1D', 'y_1100-1400_GEFS18Zday15Strict', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_1245-1745_SeasonalComb2', 'y_1300-1545_PARA018Z0to8$3', 'y_1215-1300_PARA6zp10to8']

CHOSEN_OPTIONAL_BY_DAY = {
                    0:WEEKLY_ADDITIONS,
                    1:WEEKLY_ADDITIONS,
                    2:WEEKLY_ADDITIONS,
                    3:WEEKLY_ADDITIONS+['y_1745-1815_GEPS12z0to8', 'y_1100-1200_GEPS12zp1loose$z'],
                    4: WEEKLY_ADDITIONS
                         }

#[x for x in CHOSEN_OPTIONAL_BY_DAY[0] if x not in ['y_0800-1845_RelStrengthSell', 'y_0800-1945_PARAVs1Yb', 'y_0800-1945_Seasonal', 'y_1815-1915_PARACO12zPrev1D']]
# 'y_0400-0700_CFSCOPrev1D','y_0400-0700_CFSCOPrev1D$4' x [4] ADD

ALL_CONFS_COMBINED_BY_DAY = {
                            # chosen from original mode + manual pick + daily safe mode 2308
                            # days [2,3,4] require check of non prod best 10
                            0: CHOSEN_OPTIONAL_BY_DAY[0]+ \
                                    ['y_1100-1745_epsFcstPcomb06Z', 'y_1100-1400_GFSv1612zp4', 'y_0000-0800_GEPFS4D', 'y_1300-1430_EC0dChange$b', 'y_0800-1945_PARACO24D', 'y_0800-1845_weeklyMomentumd0', 'y_0800-1945_GEPSCO', 'y_0800-1945_PARACOhybrid1', 'y_1415-1745_TuesdaysMomentum', 'y_1000-1200_eps12Z9to13', 'y_0800-1845_0Dtrend', 'y_1100-1945_RSIS', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1200-2030_epsFcstGEFS6Z', 'y_1245-1515_d', 'y_1100-1515_momentumMonNegative', 'y_1100-1245_cashGFS0d', 'y_1415-1615_PARAPrev1D', 'y_0800-1845_GEFS18Zday15p3', 'y_1515-1615_PARA6Zp24Strict', 'y_0730-0800_CFSCO0z0to16p24', 'y_1100-1300_cashCFS', 'y_1100-1415_GEMCOPrev1D', 'y_0800-1745_PARA1D0to10', 'y_0800-1845_CFSCO14to28p2', 'y_1200-1315_ICONCash0dp2', 'y_1100-1415_GEMCOPrev1DSummerMean', 'y_1145-1230_CFSCOcomb', 'y_1300-1430_EC0dChangeStrict', 'y_1130-1200_PARACO12z23DComb', 'y_1415-1845_cashVsettlediff', 'y_0800-1400_EPShybrid1b', 'y_1200-1415_epsFcst12ZbCor', 'y_1215-1300_PARA6zp10to8', 'y_1100-1230_GFSv16z0to8Strict', 'y_0800-1845_GEFS18Zday15p3$0', 'y_1115-1145_GEFS6z0to8', 'y_1100-1300_EPSSeasonalday15Strict', 'y_1130-1200_GFSv16z$0', 'y_0600-0800_epsFcstStrict', 'y_1400-1445_cfs6Z14to21D', 'y_1100-1230_EChybrid1', 'y_1200-1415_CFSvsEPS0Z', 'y_0800-1415_WindVs30dTXStrict', 'y_1615-1715_cash', 'y_1200-1945_epsFcst12ZbCorW15', 'y_0800-1200_GEM0Z', 'y_1100-1230_PARA6Zp30to8', 'y_1100-1400_GEFS18Zday15', 'y_0800-1845_PARACO0zCombStrict', 'y_0800-1845_WTIopenStrict', 'y_1200-1315_PARA6zws10mTX', 'y_1715-1845_cashGFS0z2d', 'y_1100-1745_MonComb1021Strict', 'y_1100-1315_EPSVsGEPStrict', 'y_1200-2030_epsFcstGEFS6Z$0', 'y_1130-1300_GEFS6zp1$0', 'y_1100-1745_MonComb1021', 'y_1700-1745_PARA12z0to8p34', 'y_0630-0800_MorningComb', 'y_1400-1745_GFSv1614D', 'y_1200-1215_EPSpred2b', 'y_1545-1645_PARA12z14to16p1', 'y_1315-1415_PARACO18zPrev1D8to16$1', 'y_0800-1845_NGFmidday2$0', 'y_1315-1615_PARA6Zp1only$b', 'y_1130-1200_PARACO6z0to2prev1', 'y_0800-1400_CFSCOPrev1D0to16'],
                            1: CHOSEN_OPTIONAL_BY_DAY[1]+\
                                    ['y_1100-1615_GFSv16Zp2', 'y_1615-1945_PARA1D18Z', 'y_1745-1945_GEFS12z0to8', 'y_0800-1845_WTIopen', 'y_1300-1400_EPS12zp12', 'y_0600-0815_GEMCO', 'y_1745-2045_12Zb124b', 'y_1245-1415_american4D$2', 'y_0800-1400_GEFSFcst12zAM', 'y_1300-1430_EC0dChange$5', 'y_0600-0900_PARA0z0to16WindTX', 'y_1200-1745_CFSCOp234', 'y_1300-1545_PARA018Z8to16', 'y_0800-1845_PARACO0zp38to16W', 'y_0800-1415_PARACO0zp3', 'y_1000-1100_GFSv160z', 'y_1715-1915_GEPSComb$1', 'y_0800-1945_PARACOhybrid1', 'y_1200-1300_PARA1D$1', 'y_0600-0800_GEFS0zComb', 'y_1100-1400_GFSv1612zp4', 'y_1100-1745_epsFcstPcomb06Z', 'y_1515-1645_CFSCO12zp24', 'y_0800-1415_WindVs30dTXStrict', 'y_1130-1300_GEFS6zp1Strict', 'y_0600-1415_epsFcstPgeps0Z$1', 'y_1230-1315_GEFS0zComb2', 'y_1530-1615_GEFS6zp18to16', 'y_0800-1200_PARARACO0to8Comb', 'y_1430-1715_COALopenYestd', 'y_0000-0600_cfs12Z28to42$2', 'y_0800-1200_f234b', 'y_0800-1845_0Dtrend', 'y_0800-1745_yearly', 'y_1100-1230_EChybrid1', 'y_1815-1900_GEPS12Zp1', 'y_0800-1745_cfs14to211D$1', 'y_1100-1815_cfsDaily0Z0to16Summer'],
                            2: CHOSEN_OPTIONAL_BY_DAY[2]+ \
                                    ['y_0800-1315_TTFStrict', 'y_1100-1400_ECFcst12zb', 'y_0800-1845_NGFmidday2', 'y_1300-1315_CFSCO6Z21to353D', 'y_1715-1915_GEFSComb', 'y_0800-1315_TTFcloseYestStrict', 'y_0800-1415_PARACOCombWed', 'y_0800-1945_PARAVs1Y14to16', 'y_1200-1415_epsFcst12ZbCor', 'y_1430-1500_GFSv16Zp40to10', 'y_1100-1400_GEFS18Zday15Strict', 'y_1815-2030_epsFcst12Z$Z', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1100-1745_momentumBollinger2Ht8', 'y_1415-1545_PARACO6zp1', 'y_1415-1715_PARA6Zp14comb', 'y_0800-1845_NGFopen2', 'y_1100-1315_ECGEM', 'y_1315-1415_GEFS6zp48to16Comb', 'y_0800-1100_NGFopenVscloseStrict', 'y_1100-1745_EPS4514to28rolling2', 'y_1300-1545_PARA018Z0to8$3', 'y_0800-1100_NGFopenVsclose$b', 'y_0800-1945_PARACO24D', 'y_1115-1245_PARA6Zp30to16', 'y_1200-1300_EPS4514to423D', 'y_1530-1615_GEFS6zp18to16', 'y_0800-1845_CFSCombDaily', 'y_0800-1745_cfsDaily06Z', 'y_1330-1415_GEPS12z14to16', 'y_0800-1400_GEFSFcst12zAM', 'y_0800-1845_COALopenclose', 'y_1300-1330_eps9to13p4Strict', 'y_0800-1400_cash2', 'y_0800-1845_EPSsouth', 'y_0800-1000_eps9to13p2', 'y_1100-1745_EPS9to13comb', 'y_0800-1845_CFS0Z14to28Yest', 'y_1245-1745_SeasonalComb2', 'y_0800-1000_eps9to13MEAN', 'y_1715-1845_cashGFS0z2d', 'y_1400-1430_PARARACOam', 'y_1200-1230_EC0zbasicYest', 'y_0800-1100_NGFopenVsclose', 'y_0800-1315_TTFStrict$9', 'y_0700-0800_EC0zbasic', 'y_0000-0600_cfs12Z28to42', 'y_1745-1845_fritest', 'y_1815-1945_CFSCO14to21Comb', 'y_1815-1845_PARACO12z0to10p1', 'y_1300-1545_PARA018Z8to16$2', 'y_1430-1500_PARApre6z$2', 'y_0800-1100_NGFopen', 'y_0600-0800_PARACO0AMpred3', 'y_0800-1845_GEFS18Zday15p3', 'y_1745-1945_PARA12Zp4$2', 'y_1515-1745_GEFS0Zp20to16', 'y_1000-1300_GEFS12zComb', 'y_0800-1845_GEFS0zp28to16W', 'y_0630-0800_MorningComb', 'y_1100-1400_GEFS18Zday15Strict$b', 'y_0000-0800_CFSvsEPS12Z', 'y_1745-2045_GEFS12Z', 'y_1115-1215_PARACO6z0to8WindTX', 'y_1615-1715_cash', 'y_0600-0700_GEPS0Zp2', 'y_0800-1315_momentumWed1d', 'y_0600-0800_GEPFS1D', 'y_1715-1915_PARACO12Z1D8to16', 'y_1300-1400_EPS0zp12Strict', 'y_1745-1945_PARA12Zp1Strict', 'y_1200-1330_paracoSeasonal8to16D', 'y_1100-1715_epsFcstPgeps0Z$b', 'y_1130-1200_PARA12zp1Neg', 'y_0600-1415_epsFcstPgeps0Zc2$b', 'y_1100-1300_EPSSeasonalday15', 'y_1315-1515_EPS9to13rsStrict', 'y_1215-1515_PARACO6z14to16', 'y_0800-1845_SeasonalCombWed', 'y_1715-1915_PARACO6z0to8WindTXNeg', 'y_1315-1415_EPS450to13Comb', 'y_1515-1915_PARA6Z3D0to10', 'y_0800-1200_EPSCFS', 'y_1230-1315_GEFS0zComb2', 'y_1445-1545_PARA0z14to16p4$b', 'y_1215-1515_PARACO6z14to16$W', 'y_0800-1100_NGFopenStrict', 'y_0800-1415_GEFSCombWed', 'y_0800-1400_EPSlast', 'y_1200-1245_6Zclean', 'y_1530-1615_GEFS6zp18to16Strict', 'y_1315-1645_GEFS6zp48to16Strict', 'y_1215-1300_PARA6zp10to8', 'y_1200-1300_EPS4514to421D', 'y_0630-0730_GEPS0Zp1', 'y_0800-1845_GEMCO12zComb', 'y_1230-1330_PARACO6zp4', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_1230-1315_GEFSCO18z0dCash', 'y_0800-1845_PARA0zp12W', 'y_1515-1645_CFSCO0Z14to2812D', 'y_1845-1915_GFSv160zp4', 'y_1215-1315_cashEC', 'y_1200-1315_PARA012Z0to8', 'y_0800-1845_AmericanDaily0zW'],
                            3: CHOSEN_OPTIONAL_BY_DAY[3]+\
                                    ['y_0800-1845_GEPS12zPrev1D0to10', 'y_1200-1300_EPS4514to28', 'y_1315-1745_EPS4528to423DNeg', 'y_0800-1945_cfs6z10to21D', 'y_1515-1645_CFSCO0Z14to2812D', 'y_1100-1400_GEFS18Zday15Strict', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_0800-1845_WTIcloseYest', 'y_0000-0400_GEM12Zc$b', 'y_0800-1845_PARA0zp12W', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_0800-1745_cfs12Z0to16p3', 'y_1215-1315_PARA6z1D0to10', 'y_0800-1845_GEFS0zp28to16W', 'y_0630-0730_cfs12z10to211D', 'y_0800-1500_CFSPrev1D14to21', 'y_0800-1500_CFSPrev1D14to21Strict', 'y_1545-1715_PARA0Zp2', 'y_0800-1845_PARA0zp1W', 'y_0800-1400_GEFSFcst12zAM', 'y_1115-1245_PARA6Zp30to16', 'y_1515-1615_GEFS6z0to8p1', 'y_1315-1745_cfs28to42', 'y_1515-1745_GEFSCO3514to21Prev1D', 'y_0800-1845_AmericanDaily0zW', 'y_0800-1745_cfsDaily0Z', 'y_0800-1415_ThursdaysMomentum', 'y_0800-1100_NGFopenStrict', 'y_1300-1430_EC0dChange$b', 'y_1130-1200_GFSv16z', 'y_1100-1230_PARA6Zp30to8', 'y_1300-1430_EC0dChange', 'y_0800-1845_WindUS', 'y_1200-1415_epsFcst12ZbCor', 'y_1515-1745_GEFS0Zp20to16', 'y_1945-2345_d3', 'y_0600-0700_cfs1218ZMix', 'y_1815-1945_GEFS0to8$3', 'y_1745-1815_GEPS12z0to8', 'y_1100-1315_ECGEM', 'y_1330-1415_GEPS12z14to16', 'y_1515-1545_EIA2', 'y_0800-1845_COALopenclose1d', 'y_1715-1915_CFSMComb', 'y_0800-1845_0Dtrend', 'y_0800-1845_0Dtrend$b', 'y_1145-1745_GEFS1D', 'y_1715-1845_cashGFS0z2d$3', 'y_1300-1315_PARA18z0to2p3$3', 'y_0800-1745_GEFSFcst12zStrict', 'y_1115-1245_PARA6Zp30to16Strict'],
                            4: CHOSEN_OPTIONAL_BY_DAY[4]+ \
                                    ['y_1245-1415_PARA6Zp18to16','y_1100-1200_PARA6Zp28to16','y_1100-1200_PARA6Zp28to16$4','y_1100-1200$b_PARA6Zp28to16','y_1200-1245_PARA6Zp40to16Neg','y_0800-1400_GEFSFcst12zAM', 'y_1715-1845_PARACO018z0to10', 'y_1815-1915_GEMCO0zPrev1D0to10', 'y_1415-1845_PARACO18zp10to10', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_0800-1845_ECEPSPrev1D', 'y_1200-1415_GEMCO12zPrev1Doto10', 'y_1445-1545_paracoSeasonal14to16', 'y_1415-1845_PARACO18zp10to10Strict', 'y_0800-1745_GEFSFcst12z', 'y_0800-1845_GEPSCOCombD', 'y_1100-1400_CFSCOcomb21to42', 'y_0800-1945_EPSPrev1DStrict', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_0800-1945_EPSPrev1D', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_0800-1845_cfsDaily0Z0to162Dclean', 'y_0800-1845_GDDVs30d', 'y_1415-1615_GFS6Zrolling2', 'y_1000-1100_GFSv160z2', 'y_1300-1315_CFSFSCO21to351Dcomb', 'y_1315-1745_CFSFSCOComb6Z0to16', 'y_1815-2030_epsFcst12Z', 'y_0800-1315_PARA0zPrev1D8to16', 'y_1200-1415_CFSvsEPS0Z', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1100-1200_GEPS12zp1loose', 'y_1100-1615_GFSv16Zp2$3', 'y_1100-1130_GFSv164D', 'y_1315-1715_CFS6z0to16p34', 'y_1745-1945_PARA12Zp1Strict', 'y_0800-1945_GEPSCO', 'y_1700-1745_PARA12Zp34', 'y_1130-1200_GFSv16z$9', 'y_1245-1545_EPS45', 'y_1330-1415_GEPSam', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_1715-1915_CFSMComb', 'y_1545-1845_PARACO0z14to16p4', 'y_0630-0800_GEPSamloose', 'y_1200-1300_EPS4514to423D', 'y_1100-1315_PARA12z14to16$Z', 'y_1415-1845_GEFSCO3521to28p3', 'y_1400-1530_CFSCOPrev12D$b', 'y_1400-1530_CFSCOPrev12D', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_0800-1000_eps9to13', 'y_1545-1715_PARA0Zp2', 'y_1100-1230_EChybrid1', 'y_1215-1315_eps12Zp40to8', 'y_0800-1845_EPSsouthStrict', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1415-1545_PARACO6zp1', 'y_0800-1100_NGFopen', 'y_0800-1945_PARACOhybrid1$4', 'y_1100-1300_PARA18Z3D8to16', 'y_0800-1400_EPShybrid1b$2', 'y_1400-1500_cfs0Z', 'y_1200-1430_WindTXPARACO0zStrict', 'y_0800-1845_0Dtrend', 'y_0630-0800_GEMCOPrev1D0z8to16', 'y_1200-1430_WindTXPARACO0z', 'y_1715-1915_GEFSCO3514to35p3', 'y_1330-1415_cashPARA6Z0to2', 'y_1100-1230_PARA6Zp30to8Strict', 'y_1845-1945_GEFSPrev1dp4']
                            }


ALL_CONFS_COMBINED_BY_DAY_STRICT = {
                                    0:WEEKLY_ADDITIONS_STRICT+\
                                        ['y_1300-1430_EC0dChange$b', 'y_0800-1845_weeklyMomentumd0', 'y_1415-1745_TuesdaysMomentum', 'y_0800-1845_0Dtrend', 'y_0800-1945_GEPSCO', 'y_1100-1945_RSIS', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1100-1745_epsFcstPcomb06Z', 'y_1200-2030_epsFcstGEFS6Z', 'y_1100-1400_GFSv1612zp4', 'y_1245-1515_d', 'y_1100-1515_momentumMonNegative', 'y_1100-1245_cashGFS0d', 'y_1415-1615_PARAPrev1D', 'y_0800-1845_GEFS18Zday15p3', 'y_1100-1300_cashCFS', 'y_1145-1230_CFSCOcomb', 'y_1300-1430_EC0dChangeStrict', 'y_1130-1200_PARACO12z23DComb', 'y_1415-1845_cashVsettlediff', 'y_0000-0800_GEPFS4D', 'y_0800-1945_PARACO24D', 'y_0800-1945_PARACOhybrid1', 'y_1000-1200_eps12Z9to13', 'y_1200-1945_epsFcst12ZbCorW15', 'y_1100-1415_GEMCOPrev1D', 'y_0800-1200_GEM0Z', 'y_1215-1300_PARA6zp10to8', 'y_1100-1230_PARA6Zp30to8', 'y_1100-1400_GEFS18Zday15', 'y_0800-1845_PARACO0zCombStrict', 'y_0800-1845_WTIopenStrict', 'y_1100-1230_GFSv16z0to8Strict', 'y_1200-1315_PARA6zws10mTX', 'y_1100-1230_EChybrid1', 'y_1715-1845_cashGFS0z2d', 'y_1100-1745_MonComb1021Strict', 'y_1100-1315_EPSVsGEPStrict'],
                                    1:WEEKLY_ADDITIONS_STRICT+\
                                        ['y_1100-1615_GFSv16Zp2', 'y_1615-1945_PARA1D18Z', 'y_1745-1945_GEFS12z0to8', 'y_0800-1845_WTIopen', 'y_1300-1400_EPS12zp12', 'y_0600-0815_GEMCO', 'y_1745-2045_12Zb124b', 'y_1245-1415_american4D$2', 'y_0800-1400_GEFSFcst12zAM', 'y_1300-1430_EC0dChange$5', 'y_0600-0900_PARA0z0to16WindTX', 'y_1200-1745_CFSCOp234', 'y_1300-1545_PARA018Z8to16', 'y_0800-1845_PARACO0zp38to16W', 'y_0800-1415_PARACO0zp3', 'y_1000-1100_GFSv160z', 'y_1100-1745_epsFcstPcomb06Z', 'y_1200-1300_PARA1D$1', 'y_0800-1945_PARACOhybrid1', 'y_0800-1845_WTIopen$1', 'y_1100-1715_epsFcstPgeps0Z', 'y_1515-1645_CFSCO12zp24', 'y_0000-0600_cfs12Z28to42$2', 'y_0800-1200_f234b', 'y_0800-1845_0Dtrend', 'y_1715-1915_GEPSComb$1', 'y_0600-1415_epsFcstPgeps0Z$1', 'y_0800-1745_yearly', 'y_1100-1230_EChybrid1', 'y_1130-1300_GEFS6zp1Strict', 'y_1815-1900_GEPS12Zp1', 'y_0800-1745_cfs14to211D$1', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1200-1245_6Zclean$1', 'y_0800-1845_WTIopenStrict', 'y_0800-1200_PARARACO0to8Comb', 'y_0800-1845_GDDVs30d', 'y_0800-1415_WindVs30dTX', 'y_1845-1945_PARA12Zp40to10', 'y_1230-1315_GEFS0zComb2', 'y_1815-1945_epsFcstGEPS0Z'],
                                    2:WEEKLY_ADDITIONS_STRICT+\
                                        ['y_1215-1300_PARA6zp10to8', 'y_1315-1415_EPS450to13Comb', 'y_1200-1300_EPS4514to421D', 'y_1745-1945_PARA12Zp1Strict', 'y_0630-0730_GEPS0Zp1', 'y_0800-1200_EPSCFS', 'y_1330-1415_GEPS12z14to16', 'y_0800-1845_GEMCO12zComb', 'y_0800-1100_NGFopenVscloseStrict', 'y_1300-1330_eps9to13p4Strict', 'y_1215-1515_PARACO6z14to16', 'y_0800-1845_NGFopen2', 'y_1515-1915_PARA6Z3D0to10', 'y_0800-1400_GEFSFcst12zAM', 'y_0800-1315_momentumWed1d', 'y_1130-1200_PARA12zp1Neg', 'y_0800-1315_TTFStrict', 'y_1315-1415_GEFS6zp48to16Comb', 'y_1100-1745_EPS4514to28rolling2', 'y_1300-1545_PARA018Z0to8$3', 'y_0800-1100_NGFopenVsclose$b', 'y_0800-1945_PARACO24D', 'y_1115-1245_PARA6Zp30to16', 'y_1200-1300_EPS4514to423D', 'y_1530-1615_GEFS6zp18to16', 'y_0800-1845_CFSCombDaily', 'y_1415-1545_PARACO6zp1', 'y_0800-1745_cfsDaily06Z', 'y_0800-1845_COALopenclose', 'y_0800-1400_cash2'],
                                    3:WEEKLY_ADDITIONS_STRICT+\
                                        ['y_0800-1845_PARA0zp12W', 'y_1545-1715_PARA0Zp2', 'y_0800-1845_PARA0zp1W', 'y_0800-1400_GEFSFcst12zAM', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1115-1245_PARA6Zp30to16', 'y_1515-1615_GEFS6z0to8p1', 'y_1315-1745_cfs28to42', 'y_1515-1745_GEFSCO3514to21Prev1D', 'y_0800-1745_cfsDaily0Z', 'y_1300-1430_EC0dChange$b', 'y_0800-1845_AmericanDaily0zW', 'y_0800-1415_ThursdaysMomentum', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0800-1845_WindUS', 'y_0800-1845_WTIcloseYest', 'y_1200-1415_epsFcst12ZbCor', 'y_1515-1745_GEFS0Zp20to16', 'y_1945-2345_d3', 'y_0600-0700_cfs1218ZMix', 'y_1815-1945_GEFS0to8$3', 'y_1215-1315_PARA6z1D0to10', 'y_1745-1815_GEPS12z0to8', 'y_1100-1315_ECGEM'],
                                    4:WEEKLY_ADDITIONS_STRICT+\
                                        ['y_1100-1815_cfsDaily0Z0to16Winter', 'y_1100-1200_GEPS12zp1loose', 'y_1100-1615_GFSv16Zp2$3', 'y_1100-1130_GFSv164D', 'y_1315-1715_CFS6z0to16p34', 'y_1745-1945_PARA12Zp1Strict', 'y_0800-1945_GEPSCO', 'y_1700-1745_PARA12Zp34', 'y_1130-1200_GFSv16z$9', 'y_1245-1545_EPS45', 'y_1330-1415_GEPSam', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_0800-1315_PARA0zPrev1D8to16', 'y_1715-1915_CFSMComb', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_1545-1845_PARACO0z14to16p4', 'y_0630-0800_GEPSamloose', 'y_1200-1300_EPS4514to423D', 'y_1200-1415_CFSvsEPS0Z', 'y_0800-1945_EPSPrev1D', 'y_1100-1315_PARA12z14to16$Z', 'y_0800-1945_EPSPrev1DStrict', 'y_1415-1845_GEFSCO3521to28p3', 'y_0800-1845_EPSsouthStrict', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1100-1230_EChybrid1', 'y_1415-1545_PARACO6zp1', 'y_0800-1100_NGFopen', 'y_0800-1945_PARACOhybrid1$4', 'y_1100-1300_PARA18Z3D8to16', 'y_0800-1845_GEPSCOCombD', 'y_0800-1400_EPShybrid1b$2', 'y_1215-1315_eps12Zp40to8', 'y_1815-1915_GEMCO0zPrev1D0to10']
                                    }

