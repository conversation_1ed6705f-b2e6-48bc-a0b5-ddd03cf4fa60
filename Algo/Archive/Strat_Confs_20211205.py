import copy

WEEKLY_ADDITIONS = ['y_1230-1330_PARACO6zp4','y_0715-0745_EC0Zp12','y_0715-0745_EC0Zp34','y_1100-1400_GEFS18Zday15Strict', 'y_1515-1845_GEM0zp20to10', 'y_1200-1500_EPStrend15', 'y_1315-1745_eps9to13basic', 'y_1300-1330_fri', 'y_0800-1845_MTFComb$4', 'y_1100-1715_epsFcstPgeps0Z', 'y_1100-1515_mon', 'y_1130-1200_PARA12z13DComb$0', 'y_1200-1300_EPS4514to423D$b', 'y_1130-1200_PARACO12z23DComb$7', 'y_1515-1745_GEFSCO3514to28Prev1D', 'y_1200-1415_FridaysMomentum', 'y_0000-0800_GEFS352D$0', 'y_1315-1745_d$2', 'y_0800-1845_PARA0zp1W', 'y_0800-1415_eps12Z', 'y_1100-1200_EPS4528to42b', 'y_1300-1315_CFSFSCO21to351Dcomb$b', 'y_0800-1945_Seasonal$4', 'y_0800-1945_EPShybrid2.2Strict', 'y_0800-1000_eps9to13MEAN', 'y_0800-1945_PARACOhybrid1$4', 'y_1400-1500_CFS12Z4D21to35', 'y_1745-1945_PARA12Zp4$2', 'y_0800-1845_CFS0Z14to28Prev1D']
WEEKLY_ADDITIONS_STRICT = ['y_1400-1500_CFS12Z4D21to35', 'y_0800-1945_PARACOhybrid1$4', 'y_0800-1000_eps9to13MEAN',
                           'y_1315-1745_eps9to13basic', 'y_1300-1330_fri', 'y_1100-1515_mon',
                           'y_0800-1845_PARA0zp1W', 'y_0800-1845_MTFComb$4', 'y_1130-1200_PARA12z13DComb$0',
                           'y_1745-1945_PARA12Zp4$2', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_1200-1500_EPStrend15', 'y_1100-1400_GEFS18Zday15Strict']

CHOSEN_OPTIONAL_BY_DAY = {
                    0:WEEKLY_ADDITIONS+['y_0715-0745_EC0Zp34'],
                    1:WEEKLY_ADDITIONS+['y_0715-0745_EC0Zp34','y_1745-1845_fritest','y_1745-1845_cashPARACO12z0to2','y_1645-1730_PARA12Z'],
                    2:WEEKLY_ADDITIONS+['y_0715-0745_EC0Zp34','y_1100-1745_EPS9to13comb','y_1100-1200_PARACO18zp1$z',
                                        'y_0800-1100_CFSM12M','y_0800-1100_PARA0ZSeasonal',
                                        'y_0800-1100_GEFS0Z14to16Seasonal','y_1245-1745_SeasonalComb2','y_1245-1745_SeasonalComb2$b',
                                                'y_1315-1515_EPS9to13rsStrict'],
                        3:WEEKLY_ADDITIONS+['y_1745-1815_GEPS12z0to8', 'y_1745-1945_GEFS12z0to8','y_1230-1330_PARACO6zp4$b','y_1230-1330_PARACO6zp4','y_1515-1745_GEFS0Zp20to16','y_0715-0745_EC0Zp34','y_0600-0900_PARA0z0to16WindTX','y_1845-1945_GEPS12z0to8p1','y_1100-1200_GEPS12zp1loose$z',
                                            'y_1115-1245_PARA6Zp30to16','y_1115-1245_PARA6Zp20to16'],
                    4:WEEKLY_ADDITIONS+['y_1200-1300_EPS4528to422D','y_1315-1745_EPS4528to423DStrict','y_1315-1745_EPS4528to423DStrict',
                                            'y_1200-1300_EPS4528to422D$4','y_1315-1745_EPS4528to423DStrict$4','y_1315-1745_EPS4528to423DStrict$4',
                                            'y_0715-0745_EC0Zp34','y_0800-1000_eps9to13MEAN','y_0800-1000_eps9to13MEANStrict']
                         }

#[x for x in CHOSEN_OPTIONAL_BY_DAY[0] if x not in ['y_0800-1845_RelStrengthSell', 'y_0800-1945_PARAVs1Yb', 'y_0800-1945_Seasonal', 'y_1815-1915_PARACO12zPrev1D']]
# 'y_0400-0700_CFSCOPrev1D','y_0400-0700_CFSCOPrev1D$4' x [4] ADD

ALL_CONFS_COMBINED_BY_DAY = {
                            # chosen from original mode + manual pick + daily safe mode 2308
                            # days [2,3,4] require check of non prod best 10
                            0: CHOSEN_OPTIONAL_BY_DAY[0]+\
                                    ['y_1145-1230_CFSCOcomb', 'y_1330-1415_GEPS12z14to16', 'y_1100-1515_mon', 'y_1100-1400_CFSCOcomb21to42', 'y_0800-1845_weeklyMomentumd0', 'y_1615-1745_cfsDaily0Z0to162D', 'y_0800-1845_PARA0zp1W', 'y_1245-1515_d', 'y_1100-1415_CFSCO21to351D', 'y_1315-1745_american024', 'y_0800-1315_GEFSCO354D', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_0800-1315_TTFcloseYestStrict', 'y_1415-1845_cashVsettlediff', 'y_0800-1845_NGFopen2', 'y_0800-1845_PARACO0zp38to16W', 'y_1815-1945_CFSCO14to21Comb', 'y_1100-1230_GFSv16z0to8', 'y_0800-1845_NGFopenStrict2', 'y_0800-1415_PARACO0zp3', 'y_0800-1845_MTFComb', 'y_1300-1315_CFSCO6Z21to353D', 'y_1300-1430_EC0dChange$b', 'y_0800-1945_PARACOhybrid1', 'y_1100-1230_GFSv16z0to8$b', 'y_1100-1745_MonComb1021', 'y_1000-1400_PARACO18z$0', 'y_1000-1200_MonCombStrict', 'y_1000-1200_MonComb', 'y_1000-1400_PARACO18z', 'y_0800-1200_EPSCFS', 'y_0800-1415_EPS0zp2', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1000-1200_PARACO0zp1$2', 'y_0000-0400_GEM12Zc', 'y_1100-1415_GEMCOPrev1DSummerMean', 'y_1300-1315_CFSFSCO21to351Dcomb$b', 'y_1300-1315_CFSFSCO21to351Dcomb', 'y_1515-1845_PARAPrev1DWind', 'y_1900-1915_epsFcstGEFS12Z', 'y_1130-1300_GEFS6zp1$0', 'y_0800-1945_EPShybrid2.2Strict', 'y_1515-1745_GEFS18z14d', 'y_1545-1715_PARA0Zp2', 'y_0000-0600_18Z', 'y_0000-0800_CFSvsEPS12Z', 'y_1300-1330_PARACO6zMean', 'y_0800-1845_WindUS', 'y_0400-0700_GEPS12Zp2', 'y_0800-1945_Seasonal$4', 'y_0800-1845_NGFmidday2$0', 'y_1200-1715_GEFS353D', 'y_1200-1515_momentumBollinger1Ht8'],
                            1: CHOSEN_OPTIONAL_BY_DAY[1]+\
                                    ['y_1100-1200_EPS4528to42b', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_0800-1845_GEFS0zp28to16W', 'y_1300-1545_PARA018Z0to8', 'y_0800-1845_RelStrengthSell', 'y_1300-1430_EC0dChange$b', 'y_0800-1845_CFSCombDaily', 'y_1300-1545_PARA018Z8to16', 'y_0800-1000_eps9to13MEAN', 'y_0800-1845_weeklyMomentumd1', 'y_1100-1200_gefsFcst6Z', 'y_0800-1845_NGFmidday2', 'y_1100-1745_ThuMomentumEIAWed', 'y_1100-1515_momentumThu1dNegative', 'y_1300-1315_PARA18z0to2p3', 'y_1130-1300_GEFS6zp1', 'y_1845-1945_CFS0z14to21p3', 'y_1100-1745_PARACO12Zp2', 'y_0800-1845_WTIopen', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1400_EPShybrid1b', 'y_1130-1200_PARACO0z0to8WindTXNeg', 'y_1200-1745_GEFS6zfcst', 'y_0600-0800_CFSPrev1D21to35', 'y_1000-1100_GFSv160z', 'y_1615-1715_cash', 'y_1545-1845_PARACO0z14to16p4', 'y_1400-1500_GEFSCO12z14to16Prev1$b', 'y_1745-1845_fritest', 'y_1315-1745_eps9to13basicStrict', 'y_1315-1745_eps9to13basic', 'y_1845-1945_CFS0z14to21p3Strict', 'y_1100-1145_PARA6Zp4', 'y_1845-1945_CFS0z14to21p3$1', 'y_1300-1400_cfs0Z0to21', 'y_1315-1415_PARACO18zPrev1D8to16', 'y_0800-1100_CFSM12M', 'y_1200-1500_EPStrend15', 'y_1315-1745_eps9to13basic$6', 'y_0600-0900_GFSCO0zp23Strict', 'y_1300-1330_eps9to13p4', 'y_0000-0400_GEM12Zc$b', 'y_1100-1200_EPS4528to42b$1', 'y_0800-1315_GEFSCO354D', 'y_0800-1945_EPSPrev1D$6', 'y_1300-1545_PARA018Z0to8$z', 'y_0800-1845_CFSCombDaily$1', 'y_1330-1415_cashPARA6Z0to2$1'],
                            2: CHOSEN_OPTIONAL_BY_DAY[2]+ \
                                    ['y_1200-1745_GEFSCO35Prev1D', 'y_0600-0900_PARA0z0to16WindTX', 'y_0800-1845_PARA0zp12W', 'y_0800-1845_AmericanDaily0zW', 'y_0800-1845_PARACO0zp38to16W', 'y_1200-1745_WindTX', 'y_0800-1845_PARACO0zComb', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1745_PARA18z4D', 'y_0800-1100_WindTX$2', 'y_0800-1315_TTFStrict', 'y_0800-1845_PARACO0zp4W', 'y_1315-1745_eps9to13basic', 'y_0800-1745_yearly', 'y_0800-1415_eps12Z', 'y_0700-0800_EC0zbasic', 'y_1645-1730_GEM12Zp2', 'y_0800-1000_eps9to13MEAN', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1845_CFSCOCombWed', 'y_0800-1845_CFSCO14to28p2', 'y_1100-1745_EPS4514to28rolling2', 'y_1315-1415_americanSeasonal', 'y_0800-1845_CFSCombDaily', 'y_1100-1745_momentumBollinger2Ht8', 'y_0400-0800_Seasonal', 'y_1415-1615_americanSeasonal', 'y_1430-1500_GFSv16Zp40to10', 'y_1130-1200_PARACO12z23DComb$7', 'y_1200-1330_paracoSeasonal8to16D', 'y_0800-1845_WindUS', 'y_1315-1415_americanSeasonal$1', 'y_1315-1415_GEFS6zp48to16Comb', 'y_0800-1945_Seasonal', 'y_0800-1845_SeasonalCombWed', 'y_0000-0800_GEFS352D', 'y_1315-1745_CFSCO6Zp314to21Comb', 'y_1315-1515_PARACO18zp30to8', 'y_1500-1545_GEMPrev1D', 'y_1730-1830_eps12Z9to13p1', 'y_0800-1100_WedComb2MEAN', 'y_1515-1745_GEFS0Zp20to16', 'y_1100-1315_ECGEM', 'y_0630-0730_GEPS0Zp1', 'y_1100-1615_GFSv16Zp2$4', 'y_1200-1315_PARA012Z0to8', 'y_1100-1515_momentumThFrNegative$7', 'y_1100-1300_EPSSeasonalday15', 'y_0800-1845_GEFS18Zday15p3$2', 'y_0800-1845_CFSM3M', 'y_1545-1715_PARA0Zp2', 'y_1715-1845_PARACO12z0to8', 'y_1100-1400_GEFS18Zday15Strict$b', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_1745-1945_PARA12Zp4$2', 'y_1745-1845_fritest', 'y_1430-1500_PARApre6z', 'y_1815-1945_12ECGEM', 'y_1130-1200_PARA12zp1Neg', 'y_0800-1945_PARAVs1Y14to16', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_1100-1245_cashGFS0d', 'y_0800-1845_PARA0zp1W', 'y_0800-1315_momentumWed1d', 'y_1000-1400_PARACO18zStrict', 'y_1130-1200_PARACO12z23DComb'],
                            3: CHOSEN_OPTIONAL_BY_DAY[3]+\
                                    ['y_1515-1745_GEFSCO3514to28Prev1D', 'y_1400-1500_GEFSCO12z14to16Prev1', 'y_0800-1315_TTFcloseYestStrict', 'y_1515-1745_GEFSCO3514to21Prev1D', 'y_0800-1315_TTFclose', 'y_1100-1400_GEFS18Zday15Strict', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_0800-1845_NGFopen2', 'y_0800-1315_PARA18zPrev1D8to16', 'y_1100-1615_GFSv16z0to8p2', 'y_0600-0900_PARA0z0to16WindTX', 'y_0800-1100_NGFopenloose', 'y_1445-1545_PARA0z14to16p4', 'y_1530-1615_GEFS6zp18to16', 'y_0800-1845_PARA0zp1W', 'y_1100-1230_PARA6Zp30to8', 'y_0800-1845_PARA0zp12W', 'y_1000-1400_PARACO18z$3', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_0800-1845_PARACO18zp20to13', 'y_0800-1030_PARApre6Z', 'y_0800-1845_MTFComb', 'y_0800-1845_weeklyMomentumd3', 'y_1900-1915_epsFcstGEFS12Z', 'y_0000-0600_PARA18zp2', 'y_1115-1245_PARA6Zp20to16', 'y_1200-1500_EC12Z0to4', 'y_1100-1230_PARA6Zp30to8$c', 'y_1300-1315_PARA18z0to2p3', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_1545-1715_PARA0Zp2', 'y_1845-1945_GEPS12z0to8p1', 'y_1100-1230_PARA6Zp30to8Strict', 'y_1530-1615_GEFS6zp18to16Strict'],
                            4: CHOSEN_OPTIONAL_BY_DAY[4]+ \
                                ['y_1815-1915_GEMCO0zPrev1D0to10', 'y_0800-1845_MTFCombStrict', 'y_1815-1945_CFSCO14to21Comb', 'y_0800-1845_MTFComb', 'y_1000-1200_MonComb', 'y_0800-1945_PARACOhybrid1$4', 'y_1000-1200_MonCombStrict', 'y_0800-1845_MTFComb$4', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1845_AmericanDaily0zW', 'y_1100-1230_PARA6Zp30to8', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1615-1645_PARA6Z', 'y_1300-1400_cfs0Z0to21', 'y_0800-1845_PARA0zp12W', 'y_0800-1845_CFS0Z14to284D', 'y_0600-0900_PARA0z0to8WindTX', 'y_1300-1330_fri', 'y_1545-1715_PARA0Zp2', 'y_1100-1130_GFSv164D$4', 'y_1215-1315_PARA6z1D0to10', 'y_1515-1745_PARACO0Zp20to16', 'y_0800-1845_PARACO0zp38to16W', 'y_1315-1715_CFS6z0to16p34', 'y_0800-1200_GEM0Z', 'y_1200-1745_WindTX', 'y_0800-1945_EPShybrid2.2Strict', 'y_0800-1845_PARA0zp1W', 'y_1200-1300_EPS4514to423D$b', 'y_1100-1230_PARA6Zp30to8Strict', 'y_1845-1915_GFSv160zp4', 'y_0800-1000_eps9to13MEAN', 'y_0800-1000_eps9to13MEANStrict', 'y_1100-1945_CFSCO6z0to16Yest', 'y_1715-1915_GEFSCO3514to35p3', 'y_0800-1845_PARACO0zCombStrict', 'y_1845-1915_GEFS1D', 'y_1515-1715_GFSEFSCO0Zp20to16', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_1200-1415_FridaysMomentum', 'y_1645-1745_GEFSPM', 'y_1400-1430_PARARACOam', 'y_1645-1745_TTFclosevs1to3$2', 'y_1845-1945_GEFSPrev1dp4', 'y_1845-1945_cfs12z12D', 'y_1845-2030_GEFS12Z', 'y_1100-1315_COALopen', 'y_0800-1400_EPShybrid1b$2', 'y_0600-0800_GEFS0zCombFull', 'y_1745-1845_fritest', 'y_1715-1915_CFSMComb', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_1215-1515_PARACO6z14to16', 'y_1300-1400_cfs0Z0to21Strict$b', 'y_0800-1100_WindTX']
                            }


ALL_CONFS_COMBINED_BY_DAY_STRICT = {
                                    0:WEEKLY_ADDITIONS_STRICT+['y_0800-1315_GEFSCO354D', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_1145-1230_CFSCOcomb', 'y_0800-1315_TTFcloseYestStrict', 'y_1415-1845_cashVsettlediff', 'y_0800-1845_NGFopen2', 'y_0800-1845_PARACO0zp38to16W', 'y_1330-1415_GEPS12z14to16', 'y_1815-1945_CFSCO14to21Comb', 'y_1100-1400_CFSCOcomb21to42', 'y_1100-1515_mon', 'y_0800-1845_weeklyMomentumd0', 'y_1615-1745_cfsDaily0Z0to162D', 'y_0800-1845_PARA0zp1W', 'y_1245-1515_d', 'y_1100-1415_CFSCO21to351D', 'y_1315-1745_american024', 'y_1300-1315_CFSFSCO21to351Dcomb', 'y_0800-1845_NGFmidday2$0', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_0400-0700_GEPS12Zp2', 'y_1200-1715_GEFS353D', 'y_0800-1200_EPSCFS', 'y_0800-1415_EPS0zp2', 'y_1545-1715_PARA0Zp2'],
                                    1:WEEKLY_ADDITIONS_STRICT+['y_1100-1200_EPS4528to42b', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_0800-1845_GEFS0zp28to16W', 'y_1300-1545_PARA018Z0to8', 'y_0800-1845_RelStrengthSell', 'y_1300-1430_EC0dChange$b', 'y_0800-1845_CFSCombDaily', 'y_1300-1545_PARA018Z8to16', 'y_0800-1000_eps9to13MEAN', 'y_0800-1845_weeklyMomentumd1', 'y_1100-1200_gefsFcst6Z', 'y_0800-1845_NGFmidday2', 'y_1100-1745_ThuMomentumEIAWed', 'y_1100-1515_momentumThu1dNegative', 'y_1300-1315_PARA18z0to2p3', 'y_1130-1300_GEFS6zp1', 'y_1845-1945_CFS0z14to21p3', 'y_1100-1745_PARACO12Zp2', 'y_0800-1845_WTIopen', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1400_EPShybrid1b', 'y_1200-1500_EPStrend15', 'y_1315-1745_eps9to13basic$6', 'y_0600-0900_GFSCO0zp23Strict', 'y_1300-1330_eps9to13p4', 'y_0000-0400_GEM12Zc$b', 'y_1100-1200_EPS4528to42b$1', 'y_0800-1315_GEFSCO354D', 'y_0800-1945_EPSPrev1D$6', 'y_1300-1545_PARA018Z0to8$z', 'y_1200-1745_GEFS6zfcst', 'y_1315-1745_eps9to13basic', 'y_0800-1845_CFSCombDaily$1', 'y_1845-1945_CFS0z14to21p3Strict', 'y_1315-1745_eps9to13basicStrict', 'y_0600-0800_CFSPrev1D21to35', 'y_1130-1200_PARACO0z0to8WindTXNeg', 'y_1100-1145_PARA6Zp4', 'y_1845-1945_CFS0z14to21p3$1'],
                                    2:WEEKLY_ADDITIONS_STRICT+['y_1315-1415_americanSeasonal', 'y_0400-0800_Seasonal', 'y_1415-1615_americanSeasonal', 'y_1315-1745_eps9to13basic', 'y_0800-1845_PARACO0zp38to16W', 'y_1515-1745_GEFS0Zp20to16', 'y_0800-1000_eps9to13MEAN', 'y_0800-1845_GEFS0zp28to16W', 'y_1545-1715_PARA0Zp2', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_0630-0730_GEPS0Zp1', 'y_0800-1415_eps12Z', 'y_1645-1730_GEM12Zp2', 'y_0800-1315_momentumWed1d', 'y_1745-1945_PARA12Zp4$2', 'y_0800-1845_AmericanDaily0zW', 'y_0800-1845_PARACO0zp4W', 'y_1715-1845_PARACO12z0to8', 'y_0800-1845_CFSM3M', 'y_0800-1845_PARA0zp12W', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_1100-1245_cashGFS0d', 'y_0800-1845_GEFS18Zday15p3$2', 'y_0800-1845_PARA0zp1W', 'y_1815-1945_12ECGEM', 'y_1745-1845_fritest', 'y_1100-1315_ECGEM', 'y_1430-1500_PARApre6z', 'y_1100-1400_GEFS18Zday15Strict$b'],
                                    3:WEEKLY_ADDITIONS_STRICT+['y_1115-1245_PARA6Zp20to16', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_0800-1315_TTFclose', 'y_0000-0600_PARA18zp2', 'y_0800-1845_PARA0zp1W', 'y_1100-1230_PARA6Zp30to8', 'y_1100-1400_GEFS18Zday15Strict', 'y_1545-1715_PARA0Zp2', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_1300-1315_PARA18z0to2p3'],
                                    4:WEEKLY_ADDITIONS_STRICT+['y_1815-1915_GEMCO0zPrev1D0to10', 'y_0800-1845_MTFCombStrict', 'y_1815-1945_CFSCO14to21Comb', 'y_0800-1845_MTFComb', 'y_1000-1200_MonComb', 'y_0800-1945_PARACOhybrid1$4', 'y_1000-1200_MonCombStrict', 'y_0800-1845_MTFComb$4', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1845_AmericanDaily0zW', 'y_1200-1300_EPS4514to423D$b', 'y_1300-1330_fri', 'y_1100-1230_PARA6Zp30to8Strict', 'y_0800-1845_PARA0zp1W', 'y_0800-1845_PARACO0zCombStrict', 'y_1545-1715_PARA0Zp2', 'y_1215-1315_PARA6z1D0to10', 'y_1845-1915_GEFS1D', 'y_0800-1000_eps9to13MEAN', 'y_0800-1945_EPShybrid2.2Strict', 'y_1100-1130_GFSv164D$4', 'y_1100-1230_PARA6Zp30to8', 'y_0800-1000_eps9to13MEANStrict'],
                                    }

# this will include manual additions
LOOSE_ADDITIONS_BY_DAY = {
                        0: ['y_1400-1500_cfs0Z', 'y_1545-1645_PARACashComb6zp3Negative', 'y_1415-1545_Comb', 'y_1400-1745_GFSv1614D', 'y_1515-1645_CFSCO0Z14to281D', 'y_0800-1315_TTFclose'],
                        1:['y_1100-1745_PARACO12Zp2'],
                        2:['y_1315-1645_GEFS6zp48to16Strict', 'y_0800-1315_TTFStrict$9', 'y_1845-2030_GEFS12Z'],
                        3:['y_1745-1945_GEFS12z0to8', 'y_0800-1100_NGFopenStrict'],
                        4:[]
                    }
ALL_CONFS_COMBINED_BY_DAY_LOOSE = copy.deepcopy(ALL_CONFS_COMBINED_BY_DAY)
for d in [0,1,2,3,4]:
    ALL_CONFS_COMBINED_BY_DAY_LOOSE[d] += LOOSE_ADDITIONS_BY_DAY[d]
