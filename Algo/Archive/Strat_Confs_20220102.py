WEEKLY_ADDITIONS = ['y_0800-1845_cfsDaily0Z0to162Dclean','y_0800-1845_cfsDaily12Z14to284Dclean','y_1200-1230_EC0zbasicYestStrict','y_1200-1230_EC0zbasicYest','y_1200-1300_EPS4514to423D', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1115-1815_GEPSpm', 'y_0800-1845_CFSCO14to28p2', 'y_0000-0400_GEM12Zc$b', 'y_0800-1745_GEFSFcst12zStrict', 'y_1415-1845_PARACO6z1D0to10', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0000-0800_GEPFS4D', 'y_1245-1415_american4D', 'y_0800-1400_EPShybrid1b', 'y_1100-1315_cfsMon', 'y_1145-1230_CFSCOcomb', 'y_1615-1845_GEM12zp20to2', 'y_0800-1845_WindUS', 'y_0400-0700_GEPS12Zp2', 'y_1215-1415_GEFS6z8to16Comb', 'y_0800-1945_cfs6z10to21D', 'y_1645-1745_GEFSPM', 'y_1100-1615_GFSv16z0to8p34', 'y_0800-1945_GEPSCO', 'y_0800-1945_Seasonal$4', 'y_1100-1945_CFS14to28Comb', 'y_1300-1430_EC0dChange$b', 'y_1530-1615_GEFS6zp18to16Strict', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_1415-1845_cashVsettlediff', 'y_1815-1945_CFSCO14to21Comb', 'y_0800-1315_TTFcloseYestStrict', 'y_0800-1845_NGFopen2', 'y_0800-1315_GEFSCO354D$0', 'y_0715-0745_EC0Zp12', 'y_1100-1745_MonComb1021', 'y_0800-1315_TTFcloseYest', 'y_1100-1400_CFSCOcomb21to42', 'y_0800-1415_PARACO0zp3', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_0800-1845_NGFopenStrict2', 'y_1300-1315_CFSCO6Z21to353D', 'y_1430-1500_GFSv16Zp40to10', 'y_1100-1400_GEPSCO12Zp30to8$2', 'y_0800-1945_EPShybrid2.2Strict', 'y_1000-1200_PARACO0zp1$2', 'y_0700-0800_EC0zbasic$2', 'y_1315-1615_PARA6Zp1only$b', 'y_1900-1915_epsFcstGEFS12Z', 'y_1515-1845_PARAPrev1DWind', 'y_1945-2145_EC12ZComb', 'y_0600-0800_GEFS0zCombFull', 'y_1745-1845_fritest', 'y_1315-1615_PARA6Zp1only', 'y_1815-2030_epsFcst12ZCor', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1845_GEFS18Zday15p3$0', 'y_1515-1745_PARACO6zp3', 'y_1615-1645_PARAGEM', 'y_1445-1545_paracoSeasonal14to16', 'y_2300-2345_EPS12Z', 'y_1315-1415_americanSeasonalb']
WEEKLY_ADDITIONS_STRICT = ['y_0800-1845_cfsDaily0Z0to162Dclean','y_0800-1845_cfsDaily12Z14to284Dclean','y_1200-1230_EC0zbasicYestStrict','y_1200-1230_EC0zbasicYest','y_1115-1245_PARA6Zp30to16', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_0800-1415_TueComb$2', 'y_1200-1300_EPS4514to423D$b', 'y_1100-1400_epsFcstGFSv16', 'y_1300-1330_fri$4', 'y_0800-1845_americanV4', 'y_1130-1200_GFSv16z$9', 'y_0800-1845_weeklyMomentumd3', 'y_0800-1415_TueComb', 'y_1100-1400_GEFS18Zday15$2', 'y_1745-1815_GEPS12z0to8', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0800-1945_PARAVs1Y']

CHOSEN_OPTIONAL_BY_DAY = {
                    0:WEEKLY_ADDITIONS+['y_1315-1415_GEFS6zp48to16Comb', 'y_1315-1415_americanSeasonalb$1','y_1100-1230_GFSv16z0to8$b'],
                    1:WEEKLY_ADDITIONS+[ 'y_0600-0800_GEFS0zComb','y_1300-1330_eps9to13p4Strict','y_1300-1330_eps9to13p4'],
                    2:WEEKLY_ADDITIONS+[],
                    3:WEEKLY_ADDITIONS+['y_1300-1400_EPS0zp12Strict','y_1745-1815_GEPS12z0to8', 'y_1745-1845_cashPARACO12z0to2', 'y_1715-1915_PARACO12Z1D8to16', 'y_1745-1945_GEFS12z0to8Strict', 'y_1100-1200_GEPS12zp1loose$z', 'y_1815-1845_PARACO12z0to10p14'],
                    4:['y_1300-1400_EPS0zp12Strict'] # WEEKLY_ADDITIONS
                         }

#[x for x in CHOSEN_OPTIONAL_BY_DAY[0] if x not in ['y_0800-1845_RelStrengthSell', 'y_0800-1945_PARAVs1Yb', 'y_0800-1945_Seasonal', 'y_1815-1915_PARACO12zPrev1D']]
# 'y_0400-0700_CFSCOPrev1D','y_0400-0700_CFSCOPrev1D$4' x [4] ADD

ALL_CONFS_COMBINED_BY_DAY = {
                            # chosen from original mode + manual pick + daily safe mode 2308
                            # days [2,3,4] require check of non prod best 10
                            0: CHOSEN_OPTIONAL_BY_DAY[0]+ \
                               ['y_1200-1300_EPS4514to423D', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1115-1815_GEPSpm','y_0800-1845_CFSCO14to28p2', 'y_0000-0400_GEM12Zc$b', 'y_0800-1745_GEFSFcst12zStrict','y_1415-1845_PARACO6z1D0to10', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0000-0800_GEPFS4D',
                                'y_1245-1415_american4D', 'y_0800-1400_EPShybrid1b', 'y_1100-1315_cfsMon','y_1145-1230_CFSCOcomb', 'y_1615-1845_GEM12zp20to2', 'y_0800-1845_WindUS',
                                'y_0400-0700_GEPS12Zp2', 'y_1215-1415_GEFS6z8to16Comb', 'y_0800-1945_cfs6z10to21D',
                                'y_1645-1745_GEFSPM', 'y_1100-1615_GFSv16z0to8p34', 'y_0800-1945_GEPSCO','y_0800-1945_Seasonal$4', 'y_1300-1430_EC0dChange$b', 'y_1530-1615_GEFS6zp18to16Strict',
                                'y_0600-1415_epsFcstPgeps0Zc2', 'y_1415-1845_cashVsettlediff', 'y_0800-1845_NGFopen2','y_0800-1315_GEFSCO354D$0', 'y_0715-0745_EC0Zp12', 'y_0800-1315_TTFcloseYest',
                                'y_0800-1415_PARACO0zp3', 'y_0800-1845_NGFopenStrict2', 'y_1300-1315_CFSCO6Z21to353D','y_1100-1400_GEPSCO12Zp30to8$2', 'y_0800-1945_EPShybrid2.2Strict','y_1000-1200_PARACO0zp1$2', 'y_0700-0800_EC0zbasic$2', 'y_1315-1615_PARA6Zp1only$b',
                                'y_1900-1915_epsFcstGEFS12Z', 'y_1515-1845_PARAPrev1DWind', 'y_1945-2145_EC12ZComb',
                                'y_0600-0800_GEFS0zCombFull', 'y_1745-1845_fritest', 'y_1315-1615_PARA6Zp1only','y_1815-2030_epsFcst12ZCor', 'y_0800-1845_GEFS0zp28to16W','y_0800-1845_GEFS18Zday15p3$0', 'y_1515-1745_PARACO6zp3', 'y_1615-1645_PARAGEM','y_1445-1545_paracoSeasonal14to16', 'y_2300-2345_EPS12Z','y_1315-1415_americanSeasonalb'],
                            1: CHOSEN_OPTIONAL_BY_DAY[1]+\
                                    ['y_1000-1400_PARACO18z$1', 'y_0800-1945_PARAVs1Y', 'y_1415-1545_Comb$2', 'y_1200-1300_PARA1D$1', 'y_0800-1845_RelStrengthSellloose', 'y_1400-1500_cfs0ZMean', 'y_1200-1415_CFSvsEPS0Z', 'y_0800-1845_CFSFSCOcomb28to4214D', 'y_0800-1845_RelStrengthSell', 'y_1100-1245_cashGFS0d', 'y_0800-1745_GEFSFcst12z', 'y_0800-1415_TueComb$2', 'y_0800-1745_PARA1D0to10', 'y_1100-1300_EPSSeasonalday15Strict', 'y_1100-1300_EPSSeasonalday15', 'y_0800-1415_TueComb', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1845_weeklyMomentumd1', 'y_1615-1645_PARA6Z', 'y_1300-1430_EC0dChange$b', 'y_1200-1500_EPStrend15', 'y_0800-1745_GEFSFcst12zStrict', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1400-1500_GEFSCO12z14to16Prev1', 'y_1330-1415_cashPARA6Z0to2$1', 'y_1300-1545_PARA018Z0to8', 'y_0800-1945_EPSPrev1D$b', 'y_0800-1315_GEFSCO354D', 'y_1300-1400_cfs0Z0to21', 'y_0630-0800_GEMCOPrev1D0z8to16', 'y_1315-1745_eps9to13basic$6', 'y_1315-1415_PARACO18zPrev1D8to16$6', 'y_1530-1645_GEFStrend15', 'y_0800-1845_WTIcloseYest', 'y_1100-1145_PARA6Zp4$1', 'y_1300-1330_PARA018Z', 'y_1230-1330_PARACO6zp4', 'y_1300-1400_EPS12zp12', 'y_1300-1330_eps9to13p4Strict', 'y_1000-1400_PARACO18z$z', 'y_1315-1745_eps9to13basic', 'y_1615-1715_cash', 'y_1545-1845_PARACO0z14to16p4', 'y_0930-1030_GEFS18z$z', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_0000-0600_PARA18zp2', 'y_1300-1545_PARA018Z0to8$z', 'y_0800-1000_eps9to13MEAN', 'y_1300-1315_CFSFSCO21to351Dcomb$1', 'y_0800-1100_NGFopenStrict$6', 'y_0800-1100_NGFopen$6', 'y_1300-1430_EC0dChange$5', 'y_1400-1430_PARARACOam', 'y_1645-1715_GEM12Z', 'y_0800-1945_PARACOhybrid1', 'y_1200-1315_CFS0z0to16p34', 'y_1100-1515_momentumThu1dNegative', 'y_1100-1315_COALopen', 'y_1200-2030_epsFcstGEFS6Z', 'y_1100-1300_cashCFStrict'],
                            2: CHOSEN_OPTIONAL_BY_DAY[2]+ \
                                    ['y_0600-0700_EPS45week3Mean', 'y_1100-1745_EPS4514to28rolling2', 'y_0800-1400_GEFSFcst12zAM', 'y_1845-1945_GEFSPrev1dp4', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1200-1300_EPS4514to421D', 'y_1515-1645_CFSCO12zp24', 'y_0000-0800_GEFS352D', 'y_0800-1745_cfsDaily06Z', 'y_0600-0800_epsFcstStrict', 'y_1645-1715_PARACO6z', 'y_1330-1415_GEPS12z14to16', 'y_1100-1300_NGFmidday', 'y_1315-1415_americanSeasonal$6', 'y_1300-1545_PARA018Z0to8$3', 'y_0800-1315_TTFcloseYestStrict', 'y_0600-0900_PARA0z0to8WindTX', 'y_0800-1315_TTFStrict', 'y_0800-1745_GEFSFcst12z', 'y_1100-1745_EPS9to13comb', 'y_0800-1745_GEFSFcst12zStrict', 'y_0800-1745_yearly', 'y_0800-1000_eps9to13MEAN', 'y_0800-1845_PARA0zp12W', 'y_1230-1330_PARACO6zp4', 'y_0800-1845_AmericanDaily0zW', 'y_1000-1100_GFSv160z', 'y_0800-1945_EPShybrid2.2', 'y_0700-0800_EC0zbasic', 'y_0800-1845_GEFS0zp28to16W', 'y_0600-1200_GEFSCOam', 'y_0800-1415_eps12Z', 'y_1100-1400_epsFcstGFSv16', 'y_1245-1745_SeasonalComb2$b', 'y_0800-1845_PARACO0zp38to16W', 'y_1245-1745_SeasonalComb2', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1945_PARAVs1Y14to16', 'y_1130-1200_PARACO12z23DComb$7', 'y_0800-1845_CFSCO0z14to28p1', 'y_1200-1330_CFSpre6z', 'y_1515-1745_GEFS0Zp20to16', 'y_1300-1315_CFSFSCO21to351Dcomb$2', 'y_1300-1345_cfs28to42', 'y_1515-1915_PARA6Z3D0to10', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_1245-1300_PARA6z', 'y_1200-1415_CFSvsEPS0Z', 'y_1430-1500_PARApre6z$7', 'y_1300-1545_PARA018Z8to16', 'y_1315-1415_cfs6Z28to42', 'y_1200-1500_EPStrend15', 'y_1815-1945_12ECGEM', 'y_1400-1745_GFSv1614D', 'y_1515-1615_GEFS6z0to8p1', 'y_1845-1945_CFS0z14to21p3', 'y_1730-1830_eps12Z9to13p1', 'y_1315-1415_cfs6Z14to42on12', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_0800-1845_NGFopen2', 'y_0630-0730_GEPS0Zp1', 'y_1845-1915_GFSv160zp4', 'y_1315-1745_EPS4528to423DPrev1', 'y_1545-1715_PARA0Zp2', 'y_1715-1915_EPS450to13', 'y_0800-1100_WedComb2MEAN', 'y_0800-1200_fMean', 'y_0600-0800_eps12Z', 'y_1515-1715_GFSEFSCO0Zp20to16'],
                            3: CHOSEN_OPTIONAL_BY_DAY[3]+\
                                    ['y_0600-0900_PARA0z0to16WindTX', 'y_0800-1845_CFS0Z14to284D', 'y_0800-1845_weeklyMomentumd3', 'y_0800-1945_Seasonal', 'y_0800-1745_cfsDaily0Z', 'y_1115-1245_PARA6Zp30to16', 'y_1100-1300_EPSSeasonalday15', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_1300-1430_EC0dChange$b', 'y_0630-0730_cfs12z10to211D', 'y_0800-1200_PARA18Zp3', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1315-1745_eps9to13basic', 'y_1130-1200_GFSv16z', 'y_0000-0400_GEM12Zc$b', 'y_0800-1315_PARA18zPrev1D8to16', 'y_1100-1615_GFSv16z0to8p2', 'y_1545-1715_PARA0Zp2', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_1100-1230_PARA6Zp30to8', 'y_0800-1500_CFSPrev1D14to21', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_1315-1415_americanSeasonal$6', 'y_0400-0600_american', 'y_1315-1615_PARA6Zp1only', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_0800-1945_PARAVs1YbStrict', 'y_1200-1415_epsFcst12ZbCor', 'y_0900-1200_MTFopenPrev13', 'y_1745-1945_GEFS12z0to8Strict', 'y_1100-1230_PARA6Zp30to8Strict', 'y_1300-1315_PARA18z0to2p3', 'y_1845-1945_GEPS12z0to8p1', 'y_1400-1530_Comb', 'y_1415-1745_PMComb', 'y_1515-1615_GEFS6z0to8p1', 'y_1400-1500_GEFSCO12z14to16Prev1', 'y_1100-1200_GEPS12zp1loose$z'],
                            4: CHOSEN_OPTIONAL_BY_DAY[4]+ \
                                    ['y_0800-1845_americanV4', 'y_1715-1845_PARACO018z0to10', 'y_0800-1945_cfs6z10to21D', 'y_1915-2030_GEFSCFSCOcomb', 'y_1200-1300_PARACO1D', 'y_0800-1845_NGFmidday2', 'y_1745-1815_GEPS12z0to8', 'y_1300-1400_cfs0Z0to21', 'y_1745-1845_fritest$P', 'y_0800-1200_GEM0Z$2', 'y_1815-1915_GEMCO0zPrev1D0to10', 'y_0800-1400_cash2', 'y_0800-1400_GEFSFcst12zAM', 'y_1100-1230_PARA6Zp30to8', 'y_1100-1245_cashGFS0d', 'y_1615-1645_PARA6Z', 'y_0600-0900_PARA0z0to8WindTX', 'y_1200-1300_EPS4514to423D$b', 'y_0800-1100_WindTX', 'y_1400-1500_cfs0Z', 'y_0630-0800_GEPSamloose', 'y_1615-1745_PARACO18zp1clean', 'y_0700-0830_EPSPrev1D8to13', 'y_1645-1745_TTFclosevs1to3', 'y_0800-1945_EPSPrev1D', 'y_0600-0700_GEPS0Zp2', 'y_1315-1745_momentumWed', 'y_1100-1515_momentumThFr', 'y_0800-1945_EPSPrev1DStrict', 'y_1645-1745_GEFSPM', 'y_1700-1745_PARA12Zp34', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_0800-1315_GEFS0z0to8Prev1d', 'y_0600-0800_PARACO0AMpred3', 'y_0800-1945_PARAVs1Yb', 'y_1300-1330_fri$4', 'y_1845-1915_GEFS1D', 'y_0600-0800_GEFS0zCombFull', 'y_2300-2345_EPS12Z', 'y_0400-0700_cfs18Z14to28', 'y_0800-1945_PARACO24D', 'y_1130-1200_GFSv16z$9', 'y_1100-1315_PARA12z14to16', 'y_0600-0900_GFSCO0zp23Strict', 'y_1845-1945_cfs12z12D', 'y_1000-1400_PARACO18zStrict', 'y_1100-1715_epsFcstPcomb06Z$4', 'y_0600-0800_epsFcst', 'y_0800-1845_MTFComb', 'y_0800-1845_RelStrengthSell', 'y_1215-1315_eps12Zp40to8', 'y_1100-1315_COALopen$2', 'y_0800-1845_MTFCombStrict', 'y_1430-1715_COALopenYestd', 'y_1315-1415_GEPSComb3', 'y_1300-1430_EC0dChange', 'y_1200-1245_6Zclean', 'y_0800-1000_eps9to13', 'y_1530-1645_GEFStrend15$2', 'y_1300-1400_cfs0Z0to21Strict', 'y_1515-1615_GEFS6z0to8p1$4', 'y_0630-0800_MorningComb', 'y_0600-0800_GEPFS1D', 'y_0700-0730_EC', 'y_0800-1745_yearly', 'y_1200-1430_WindTXPARACO0z', 'y_1200-1430_WindTXPARACO0zStrict', 'y_1745-1945_GEFS12zVsEPS0zgap', 'y_1300-1330_PARA018Z', 'y_1415-1845_PARACO18zp10to10', 'y_0800-1100_NGFopen', 'y_1415-1845_PARACO18zp10to10Strict', 'y_1230-1330_PARACO6zp4', 'y_1200-1300_EPS4528to422D$4', 'y_1330-1415_cashPARA6Z0to2', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_1215-1515_PARACO6z14to16', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1745_GEFSFcst12z', 'y_1115-1245_PARACO6zp10to8']
                            }


ALL_CONFS_COMBINED_BY_DAY_STRICT = {
                                    0:WEEKLY_ADDITIONS_STRICT+['y_0800-1400_EPShybrid1b', 'y_1100-1315_cfsMon', 'y_1145-1230_CFSCOcomb', 'y_1615-1845_GEM12zp20to2', 'y_0800-1845_WindUS', 'y_0400-0700_GEPS12Zp2', 'y_1245-1415_american4D', 'y_1215-1415_GEFS6z8to16Comb', 'y_0800-1945_cfs6z10to21D', 'y_1645-1745_GEFSPM', 'y_1100-1615_GFSv16z0to8p34', 'y_0800-1945_GEPSCO', 'y_0800-1945_Seasonal$4', 'y_1100-1945_CFS14to28Comb', 'y_1300-1430_EC0dChange$b', 'y_0800-1845_NGFopen2', 'y_0715-0745_EC0Zp12', 'y_0800-1315_TTFcloseYestStrict', 'y_1100-1745_MonComb1021', 'y_0800-1315_TTFcloseYest', 'y_1815-1945_CFSCO14to21Comb', 'y_1100-1400_CFSCOcomb21to42', 'y_0800-1415_PARACO0zp3', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_0800-1845_NGFopenStrict2', 'y_1200-1300_EPS4514to423D', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1115-1815_GEPSpm', 'y_0800-1845_CFSCO14to28p2', 'y_0000-0400_GEM12Zc$b', 'y_0800-1745_GEFSFcst12zStrict', 'y_1415-1845_PARACO6z1D0to10', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0000-0800_GEPFS4D', 'y_1315-1615_PARA6Zp1only', 'y_1515-1845_PARAPrev1DWind', 'y_1815-2030_epsFcst12ZCor', 'y_0800-1845_GEFS0zp28to16W', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_0800-1845_GEFS18Zday15p3$0', 'y_1100-1400_GEPSCO12Zp30to8$2', 'y_1515-1745_PARACO6zp3', 'y_1615-1645_PARAGEM', 'y_1315-1615_PARA6Zp1only$b', 'y_1445-1545_paracoSeasonal14to16', 'y_2300-2345_EPS12Z', 'y_1900-1915_epsFcstGEFS12Z', 'y_1315-1415_americanSeasonalb'],
                                    1:WEEKLY_ADDITIONS_STRICT+['y_1000-1400_PARACO18z$1', 'y_0800-1945_PARAVs1Y', 'y_1415-1545_Comb$2', 'y_1200-1300_PARA1D$1', 'y_0800-1845_RelStrengthSellloose', 'y_1400-1500_cfs0ZMean', 'y_1200-1415_CFSvsEPS0Z', 'y_0800-1845_CFSFSCOcomb28to4214D', 'y_0800-1845_RelStrengthSell', 'y_1100-1245_cashGFS0d', 'y_0800-1745_GEFSFcst12z', 'y_0800-1415_TueComb$2', 'y_0800-1745_PARA1D0to10', 'y_1100-1300_EPSSeasonalday15Strict', 'y_1100-1300_EPSSeasonalday15', 'y_0800-1415_TueComb', 'y_0800-1845_GEFS0zp28to16W', 'y_0800-1845_weeklyMomentumd1', 'y_1615-1645_PARA6Z', 'y_1300-1430_EC0dChange$b', 'y_1200-1500_EPStrend15', 'y_0800-1745_GEFSFcst12zStrict', 'y_1300-1315_CFSFSCO21to351Dcomb$1', 'y_0800-1100_NGFopenStrict$6', 'y_0800-1100_NGFopen$6', 'y_1300-1430_EC0dChange$5', 'y_1400-1430_PARARACOam', 'y_1300-1400_EPS12zp12', 'y_0630-0800_GEMCOPrev1D0z8to16', 'y_1645-1715_GEM12Z', 'y_1100-1145_PARA6Zp4$1', 'y_0800-1945_PARACOhybrid1', 'y_1315-1745_eps9to13basic$6', 'y_1200-1315_CFS0z0to16p34', 'y_1300-1330_eps9to13p4Strict', 'y_1300-1400_cfs0Z0to21', 'y_0800-1845_WTIcloseYest', 'y_1000-1400_PARACO18z$z', 'y_1315-1745_eps9to13basic', 'y_1615-1715_cash', 'y_1545-1845_PARACO0z14to16p4', 'y_1330-1415_cashPARA6Z0to2$1', 'y_0930-1030_GEFS18z$z'],
                                    # 2:WEEKLY_ADDITIONS_STRICT+['y_0800-1845_PARACO0zp38to16W', 'y_1315-1745_eps9to13basic', 'y_1515-1745_GEFS0Zp20to16', 'y_1100-1745_EPS9to13comb', 'y_0800-1845_GEFS0zp28to16W', 'y_1545-1715_PARA0Zp2', 'y_1230-1330_PARACO6zp4', 'y_0800-1000_eps9to13MEAN', 'y_1745-1845_fritest', 'y_1245-1745_SeasonalComb2', 'y_1815-1945_12ECGEM', 'y_0630-0730_GEPS0Zp1', 'y_1415-1615_americanSeasonal', 'y_1100-1945_CFSCO6z0to16Yest', 'y_1100-1745_EPS4514to28rolling2', 'y_0800-1845_CFSCO14to28p2', 'y_1315-1415_americanSeasonal', 'y_0800-1845_CFSCOCombWed', 'y_0800-1845_WindUS', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1945_Seasonal', 'y_1315-1415_americanSeasonal$1', 'y_0600-0900_PARA0z0to16WindTX', 'y_0800-1845_SeasonalCombWed', 'y_1100-1745_momentumBollinger2Ht8', 'y_0000-0800_GEFS352D', 'y_0800-1845_CFSCombDaily', 'y_1200-1745_WindTX'],
                                    # This Strict is taken by looking at BEST=15 x LAST=1/2/3 x DAYS=6 NO WEEKLY!
                                    2: ['y_0800-1400_GEFSFcst12zAM', 'y_0800-1845_PARACO0zp38to16W', 'y_1315-1415_cfs6Z14to42on12', 'y_0800-1000_eps9to13MEAN', 'y_1245-1745_SeasonalComb2$b', 'y_1815-1945_12ECGEM', 'y_1230-1330_PARACO6zp4', 'y_1515-1715_GFSEFSCO0Zp20to16', 'y_1430-1500_PARApre6z$7', 'y_0800-1745_GEFSFcst12z', 'y_1730-1830_eps12Z9to13p1', 'y_0630-0730_GEPS0Zp1', 'y_1845-1915_GFSv160zp4', 'y_0800-1945_PARAVs1Y14to16', 'y_1245-1300_PARA6z', 'y_1245-1745_SeasonalComb2', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1845_AmericanDaily0zW', 'y_1100-1745_EPS9to13comb', 'y_0800-1845_GEFS0zp28to16W', 'y_1130-1200_PARACO12z23DComb$7', 'y_0800-1845_CFSCO0z14to28p1', 'y_1200-1330_CFSpre6z', 'y_0000-0800_GEFS352D', 'y_1100-1745_EPS4514to28rolling2', 'y_0800-1745_GEFSFcst12zStrict', 'y_1515-1745_GEFS0Zp20to16'],
                                    3:WEEKLY_ADDITIONS_STRICT+['y_0600-0900_PARA0z0to16WindTX', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1100-1300_EPSSeasonalday15', 'y_1130-1200_GFSv16z', 'y_0800-1845_weeklyMomentumd3', 'y_0800-1745_cfsDaily0Z', 'y_1100-1615_GFSv16z0to8p2', 'y_1545-1715_PARA0Zp2', 'y_1300-1430_EC0dChange$b', 'y_0800-1845_CFS0Z14to284D', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_1115-1245_PARA6Zp30to16', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_1100-1230_PARA6Zp30to8', 'y_0800-1500_CFSPrev1D14to21', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_0800-1200_PARA18Zp3', 'y_1315-1415_americanSeasonal$6', 'y_0400-0600_american', 'y_1315-1615_PARA6Zp1only', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_0800-1945_PARAVs1YbStrict', 'y_1200-1415_epsFcst12ZbCor', 'y_0900-1200_MTFopenPrev13'],
                                    4:WEEKLY_ADDITIONS_STRICT+['y_1315-1745_momentumWed', 'y_1100-1515_momentumThFr', 'y_0800-1945_EPSPrev1DStrict', 'y_1645-1745_GEFSPM', 'y_1700-1745_PARA12Zp34', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_0800-1315_GEFS0z0to8Prev1d', 'y_0600-0800_PARACO0AMpred3', 'y_0800-1945_PARAVs1Yb', 'y_1300-1330_fri$4', 'y_1845-1915_GEFS1D', 'y_0600-0800_GEFS0zCombFull', 'y_1915-2030_GEFSCFSCOcomb', 'y_2300-2345_EPS12Z', 'y_0400-0700_cfs18Z14to28', 'y_0800-1945_PARACO24D', 'y_1130-1200_GFSv16z$9', 'y_1100-1315_PARA12z14to16', 'y_0600-0900_GFSCO0zp23Strict', 'y_1200-1300_EPS4514to423D$b', 'y_0800-1400_GEFSFcst12zAM', 'y_1745-1845_fritest$P', 'y_1845-1945_cfs12z12D', 'y_1000-1400_PARACO18zStrict', 'y_1100-1715_epsFcstPcomb06Z$4', 'y_0600-0800_epsFcst', 'y_1430-1715_COALopenYestd', 'y_1200-1430_WindTXPARACO0z', 'y_1200-1430_WindTXPARACO0zStrict', 'y_1745-1945_GEFS12zVsEPS0zgap', 'y_1300-1330_PARA018Z', 'y_1415-1845_PARACO18zp10to10', 'y_1200-1245_6Zclean', 'y_0800-1100_NGFopen', 'y_0630-0800_MorningComb', 'y_0800-1400_cash2', 'y_0600-0800_GEPFS1D', 'y_1745-1815_GEPS12z0to8', 'y_0700-0730_EC', 'y_0800-1745_yearly']
                                    }
