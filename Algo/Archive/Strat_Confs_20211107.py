
WEEKLY_ADDITIONS = ['y_1200-1330_paracoSeasonal8to16D', 'y_1200-1300_EPS4514to421D', 'y_1315-1745_eps9to13basic', 'y_0800-1845_CFSCO0z14to28p1', 'y_1130-1200_PARACO12z23DComb', 'y_0800-1100_NGFopenloose', 'y_0800-1030_PARApre6Z', 'y_1100-1745_PARACO12Zp2', 'y_1100-1230_GFSv16z0to8', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1315_momentumWed1d', 'y_1000-1200_MonComb', 'y_0800-1845_weeklyMomentumd3', 'y_1130-1200_PARACO12z23DComb$7', 'y_0800-1845_CFS0Z14to284D', 'y_1430-1500_GFSv16Zp40to10', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_0800-1315_GEFSCO354D', 'y_1200-1415_FridaysMomentum', 'y_0800-1315_TTFcloseYest', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1445-1545_PARA0z14to16p4', 'y_0800-1745_cfs14to211D']
WEEKLY_ADDITIONS_STRICT = ['y_0800-1845_CFSCO0z14to28p1', 'y_0800-1845_weeklyMomentumd3', 'y_1100-1745_PARACO12Zp2', 'y_0800-1315_GEFSCO354D', 'y_0800-1745_cfs14to211D', 'y_1430-1500_GFSv16Zp40to10', 'y_0800-1845_CFS0Z14to284D', 'y_1445-1545_PARA0z14to16p4', 'y_1130-1200_PARACO12z23DComb', 'y_1100-1945_CFSCO6z0to16Yest', 'y_1200-1415_FridaysMomentum', 'y_1315-1745_eps9to13basic']

CHOSEN_OPTIONAL_BY_DAY = {
                    0:WEEKLY_ADDITIONS+['y_1200-1315_PARA6zws10mTX'],
                    1:WEEKLY_ADDITIONS+['y_0800-1100_CFSM12M','y_1100-1145_PARA6Zp4','y_0400-0600_PARA18Zp23', 'y_0700-0730_PARA18Zp14','y_0800-1200_PARA18Zp3','y_1300-1430_PARA18Zp2','y_1300-1315_CFSCO6Z21to353D','y_1300-1315_PARA12zp28to16','y_1300-1315_CFSFSCO21to351Dcomb','y_1300-1315_CFSFSCO21to351Dcomb$1'],
                    2:WEEKLY_ADDITIONS+['y_1415-1715_PARA6Zp14comb','y_1300-1315_CFSCO6Z21to353D','y_1300-1315_PARA18z0to2p3','y_1300-1315_CFSFSCO21to351Dcomb'],
                        3:WEEKLY_ADDITIONS+['y_1200-1745_GEFSCO35Prev1DNeg','y_1200-1745_GEFSCO35Prev1D','y_1300-1315_CFSCO6Z21to353D','y_1300-1315_PARA18z0to2p3','y_1300-1315_CFSFSCO21to351Dcomb'],
                    4:WEEKLY_ADDITIONS+[],
                         }

#[x for x in CHOSEN_OPTIONAL_BY_DAY[0] if x not in ['y_0800-1845_RelStrengthSell', 'y_0800-1945_PARAVs1Yb', 'y_0800-1945_Seasonal', 'y_1815-1915_PARACO12zPrev1D']]
# 'y_0400-0700_CFSCOPrev1D','y_0400-0700_CFSCOPrev1D$4' x [4] ADD

ALL_CONFS_COMBINED_BY_DAY = {
                            # chosen from original mode + manual pick + daily safe mode 2308
                            # days [2,3,4] require check of non prod best 10
                            0: CHOSEN_OPTIONAL_BY_DAY[0]+\
                                    ['y_0800-1315_GEFSCO354D', 'y_0800-1315_TTFcloseYest', 'y_0800-1315_TTFcloseYestStrict', 'y_1100-1745_MonComb1021', 'y_1145-1230_CFSCOcomb', 'y_1815-1945_CFSCO14to21Comb', 'y_0800-1945_PARACOhybrid1', 'y_0800-1845_MTFComb', 'y_1130-1200_PARA12z13DComb$0', 'y_1300-1430_EC0dChange$b', 'y_1130-1200_PARACO12z23DComb', 'y_1000-1400_PARACO18z$0', 'y_0800-1415_PARACO0zp3', 'y_1430-1500_GFSv16Zp40to10', 'y_0600-0900_PARA0z0to8WindTX', 'y_1000-1200_MonCombStrict', 'y_0800-1845_NGFopenStrict2', 'y_0800-1845_NGFopen2', 'y_1100-1745_MonComb1021Strict', 'y_0800-1845_CFSCombDaily', 'y_1415-1845_cashVsettlediff', 'y_1100-1515_momentumMon', 'y_0800-1315_TTFclose', 'y_1000-1200_MonComb', 'y_0800-1845_GEPSCOCombD', 'y_1400-1530_Comb', 'y_0800-1845_PARACO0zComb', 'y_1130-1200_PARACO6z0to2prev1', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0000-0400_GEM12Zc', 'y_0800-1845_RelStrengthSell$0', 'y_1000-1200_PARACO0zp1', 'y_1000-1200_PARACO0zp1$2', 'y_1000-1400_PARACO18z', 'y_0730-0800_cfs12Z28to42', 'y_0530-0630_cashGEPSEPS', 'y_1100-1415_GEMCOPrev1DSummerMean', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_1100-1745_PARAws10mNeg', 'y_0000-0800_GEPFS4D$0', 'y_1100-1515_momentumMon$0', 'y_0800-1945_EPSPrev1DStrict', 'y_1330-1430_PARAPrev1Dx2D8to16', 'y_1100-1315_EPSVsGEPStrict', 'y_0000-0800_GEPFS4D', 'y_0800-1415_EPS0zp2', 'y_0800-1315_GEFS0z0to8Prev1d', 'y_1430-1500_cashGEM0zp30to2', 'y_1100-1400_ECFcst12zb', 'y_1300-1330_PARACO6zMean', 'y_0800-1945_EPShybrid2.2Strict', 'y_1100-1715_epsFcstPgeps0Z', 'y_0800-1845_GEFS18Zday15p3', 'y_1200-1315_ICONCash0dp2', 'y_1715-1915_CFSMComb', 'y_1515-1645_CFSCO12zp24', 'y_0000-0800_GEFS352D', 'y_1430-1715_COALopenYestd', 'y_1100-1400_GFSv1612zp4Strict', 'y_0000-0800_CFSvsEPS12Z', 'y_1400-1530_CFSCOPrev12DStrict', 'y_1745-1945_PARA12Zp1', 'y_1645-1730_PARA12Z', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_0800-1215_cashGFSEPS0d', 'y_1330-1415_GEPS12z14to16'],
                            1: CHOSEN_OPTIONAL_BY_DAY[1]+\
                                    ['y_1200-1315_PARA6z14to16p1','y_1100-1745_PARACO12Zp2', 'y_0800-1845_NGFmidday2', 'y_0800-1845_WTIopen', 'y_0800-1400_EPShybrid1b', 'y_1130-1300_GEFS6zp1', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_0800-1315_GEFSCO354D', 'y_1200-1745_GEFSCO35Prev1D', 'y_1100-1200_gefsFcst6Z', 'y_1130-1200_PARACO0z0to8WindTXNeg', 'y_1330-1415_cashPARA6Z0to2$1', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_0800-1945_EPSPrev1D$6', 'y_0800-1845_CFSCombDaily', 'y_0800-1845_CFSM3M', 'y_1100-1745_MonComb1021', 'y_0800-1945_GEFS0z0to8', 'y_0800-1745_yearly', 'y_1845-1945_CFS0z14to21p3', 'y_1400-1500_cfs0ZMean', 'y_0800-1845_CFSCOCombWed', 'y_0800-1845_PARACO0zComb', 'y_0800-1945_EPShybrid2.2', 'y_0800-1945_cfs6z10to21D', 'y_0800-1945_EPSPrev1D$1', 'y_1300-1545_CFS6ZPrev1D28to42$1', 'y_0800-1100_NGFopen$1', 'y_0800-1100_NGFopenStrict$1', 'y_1645-1745_TTFclosevs1to3', 'y_0800-1845_WTIcloseYest', 'y_0800-1945_EPSPrev1D', 'y_1330-1415_cashPARA6Z0to2', 'y_1845-1945_CFS0z14to21p3$1', 'y_1000-1400_PARACO18z$1', 'y_1100-1745_MonComb1021$1'],
                            2: CHOSEN_OPTIONAL_BY_DAY[2]+ \
                                    ['y_0630-0730_GEPS0Zp1', 'y_1715-1845_PARACO12z0to8', 'y_0800-1315_momentumWed1d', 'y_1130-1200_PARACO12z23DComb$7', 'y_1130-1300_ICONCash', 'y_1315-1415_americanSeasonal$1', 'y_1530-1645_pre12Z', 'y_1715-1845_PARACO12z0to8$2', 'y_0800-1315_PARA0zPrev1D8to16', 'y_0730-0800_CFSCO0z0to16p24', 'y_1100-1515_momentumThFrNegative', 'y_1130-1200_PARACO0z0to8WindTXNeg$2', 'y_1130-1200_PARA12z13DComb$z', 'y_1215-1415_GEFS6z8to16Comb', 'y_1445-1545_paracoSeasonal14to16', 'y_1515-1645_CFSCO0Z14to2812D', 'y_0600-0930_cfs12Z', 'y_1515-1645_CFSCO12zp24', 'y_1130-1200_PARACO12z23DComb', 'y_0800-1845_WindUS$2', 'y_1315-1515_PARACO18zp30to8', 'y_1100-1745_EPS4514to28rolling2', 'y_1715-1915_EPS450to13', 'y_1315-1515_EPS9to13rs$2', 'y_1315-1415_GEFS6zp48to16Comb', 'y_1130-1200_PARACO6z0to2prev1', 'y_1100-1515_momentumThFrNegative$7', 'y_1715-1915_CFSCO0z21to28', 'y_1815-1945_12ECGEM', 'y_1515-1615_PARA6Zp24Strict', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1130-1200_PARA12zp1Neg', 'y_1315-1745_eps9to13basic', 'y_1230-1315_PARACO1D0zStrict$2', 'y_1200-1415_CFSvsEPS0Z', 'y_1200-1300_EPS4514to421D', 'y_0800-1845_CFSCOCombWed', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1845_CFSCombDaily', 'y_0800-1745_PARA18z4D', 'y_1100-1745_momentumBollinger2Ht8', 'y_0800-1845_CFSCO0z14to28p1', 'y_1200-1330_CFSpre6z', 'y_0800-1945_Seasonal', 'y_0800-1845_cfs0z10to21CombD', 'y_0800-1945_PARAVs1Yb$2', 'y_0800-1845_SeasonalCombWed', 'y_0800-1845_WindUS', 'y_1200-1745_GEFSCO35Prev1D', 'y_1200-1330_paracoSeasonal8to16D', 'y_1315-1745_CFSCO6Zp314to21Comb', 'y_1315-1745_CFSFSCOComb6Z0to16', 'y_0800-1300_PARACO12zPrev1p2', 'y_0800-1745_cfsDaily0Z', 'y_1200-1330_PARACO18zp4'],
                            3: CHOSEN_OPTIONAL_BY_DAY[3]+\
                                    ['y_1200-1315_PARA6z14to16p1','y_0800-1845_CFSCOCombWed', 'y_0800-1845_CFS0Z14to28Yest', 'y_0800-1845_CFSM3M', 'y_0800-1845_CFSCombDaily', 'y_0800-1100_NGFopenloose', 'y_0800-1030_PARApre6Z', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_1445-1545_PARA0z14to16p4', 'y_0800-1845_WTIcloseYest', 'y_1100-1745_MonComb1021', 'y_0800-1845_CFS0Z14to284D', 'y_0800-1845_MTFComb', 'y_1515-1915_PARA6Z3D0to10', 'y_1415-1845_PARACO6z1D0to10', 'y_0800-1315_TTFopenBasic', 'y_0800-1200_PARACO12Zp1', 'y_0900-1200_momentumBollinger4H', 'y_0800-1315_TTFclose', 'y_0800-1745_cfs12Z0to16p3', 'y_1400-1745_GFSv1614D', 'y_1815-2030_epsFcst12ZbCor', 'y_0700-0730_GEM0Zp4', 'y_0800-1415_PARACO0zp3', 'y_0800-1315_TTFcloseYestStrict$3', 'y_1000-1100_GFSv160z2$3', 'y_0800-1315_TTFStrict$3', 'y_1100-1300_NGFmidday', 'y_1100-1300_NGFmidday$b', 'y_1545-1645_GFSv16z', 'y_1100-1400_GEFS18Zday15', 'y_1100-1400_GEFS18Zday15$3', 'y_0900-1200_MTFopenPrev13', 'y_0800-1845_MTFCombStrict', 'y_1330-1415_CFSCO6Zp18to16', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_1315-1745_3c', 'y_1815-1945_GEFS0to8$3', 'y_1445-1645_cfs18Z0to21', 'y_1515-1745_PARACO6zp3', 'y_0800-1400_EPSlastclean$b', 'y_0800-1400_EPSlastclean', 'y_0800-1315_TTFcloseStrict', 'y_0800-1845_PARACO18zp20to13', 'y_0800-1845_weeklyMomentumd3', 'y_1000-1400_PARACO18z$3', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_0800-1845_CFS1D0to16', 'y_1430-1715_COALopenYestd', 'y_1200-1315_PARA012Z0to8', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_1315-1745_eps9to13basic', 'y_0800-1500_GEFS350to13p2', 'y_0800-1945_PARAVs1YbStrict', 'y_1100-1215_cfs0z10to214D', 'y_0000-0600_18Z'],
                            4: CHOSEN_OPTIONAL_BY_DAY[4]+ \
                                    ['y_1100-1130_GFSv164D', 'y_0600-0800_PARACO0AMpred3', 'y_0800-1000_PARACO12zPrev1$2', 'y_0800-1100_WindTX', 'y_1200-1745_GEFSCO35Prev1D', 'y_0800-1845_PARACO0zComb', 'y_1715-1915_GEFSComb', 'y_1200-1300_PARACO1D', 'y_1100-1945_CFSCO6z0to16Yest', 'y_0800-1845_PARACO0zCombStrict', 'y_1400-1500_cfs0Z', 'y_0400-0700_CFSCOPrev1D', 'y_0630-0730_GEPS0Zp1', 'y_1615-1645_EC12Zb2', 'y_1715-1915_PARACO6z0to8WindTXNeg', 'y_1300-1330_EC12Zb1', 'y_1715-1845_cashGFS0z2d', 'y_1130-1300_ICONCashStrict', 'y_1815-1915_GEMCO0zPrev1D0to10', 'y_1100-1230_GFSv16z0to8', 'y_1430-1500_cashGEM0zp30to2', 'y_0700-0800_EC0zbasic', 'y_1230-1315_PARACO1D0zStrict', 'y_1300-1400_cfs0Z0to21', 'y_1615-1645_PARA6Z', 'y_1700-1745_cashCFSCO0d', 'y_1145-1315_fri', 'y_0800-1845_CFSCombDaily', 'y_1200-1415_FridaysMomentum', 'y_0600-0900_PARA0z0to8WindTX', 'y_0800-1400_EPSlastclean', 'y_1100-1745_MonComb1021', 'y_0800-1945_PARAVs1Yb', 'y_1130-1300_ICONCash', 'y_0800-1945_Seasonal', 'y_1515-1615_GEFS6z0to8p1$4', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1615-1745_PARACO18zp1clean$2', 'y_1200-1500_EC12Z0to4', 'y_0800-1945_PARACO24D', 'y_0600-0700_EPS45week3', 'y_0800-1745_yearly', 'y_1100-1315_ECGEM', 'y_1200-1300_EPS4514to423D', 'y_1245-1330_PARACO6zp10to10', 'y_1745-1845_fritest', 'y_1615-1745_PARACO18zp1clean', 'y_1530-1645_GEFStrend15$2', 'y_1130-1200_GFSv16z', 'y_0800-1200_f234b', 'y_1300-1430_EC0dChange$b', 'y_0700-0830_EPSPrev1D8to13', 'y_0800-1845_CFS0Z14to284D', 'y_1845-1915_GFSv160zp4', 'y_1000-1200_MonComb', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_1100-1230_EChybrid1', 'y_1330-1415_CFSCO6Zp18to16', 'y_1300-1330_fri', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1100-1515_momentumThFr']
                            }
