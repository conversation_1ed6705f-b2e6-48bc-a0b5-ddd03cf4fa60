import pandas as pd
import numpy as np
from matplotlib import pyplot as plt
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Utils.files_handle import get_degdays_path
from Algo.Xbox.generate_x_data import get_multimodel_df




model = 'PARA'
weight = 'ERCOT_with_Neighbors_Wind'
weight = 'US_Wind'

df = get_multimodel_df([model],special_file_name='wind')
# df = df[(df['feature_type']=='ws10m')&(df['weight']=='ERCOT_with_Neighbors_Wind')]

df = df[(df['feature_type']=='ws10m')&(df['weight']==weight)]

df_next7d = df[(df['validation_day']-df['forecast_time']).dt.days <=7]
df_next7d = df_next7d.groupby('forecast_time').mean()[['Value']].reset_index()
df_next7d['validation_day'] = df_next7d['forecast_time'] - df_next7d['forecast_time'].apply(lambda x: td(hours=x.hour))
df_next11to15d = df[((df['validation_day']-df['forecast_time']).dt.days >= 11)&((df['validation_day']-df['forecast_time']).dt.days <= 15)]
df_next11to15d = df_next11to15d.groupby('forecast_time').mean()[['Value']].reset_index()
df_next11to15d['validation_day'] = df_next11to15d['forecast_time'] - df_next11to15d['forecast_time'].apply(lambda x: td(hours=x.hour))
df_next15d = df[(df['validation_day']-df['forecast_time']).dt.days <=15]
df_next15d = df_next15d.groupby('forecast_time').mean()[['Value']].reset_index()
df_next15d['validation_day'] = df_next15d['forecast_time'] - df_next15d['forecast_time'].apply(lambda x: td(hours=x.hour))
df = df.merge(df_next7d,on=['forecast_time','validation_day'],suffixes=('','_+(1-7)D'))
df = df.merge(df_next15d,on=['forecast_time','validation_day'],suffixes=('','_+(1-15)D'))
df = df.merge(df_next11to15d,on=['forecast_time','validation_day'],suffixes=('','_+(11-15)D'))

df = df[(df['forecast_time'].dt.hour==0)&(df['validation_day']==df['forecast_time'])]
df_1y = df.copy()
df_2y = df.copy()
df_1y['forecast_time'] += td(days=365)
df_2y['forecast_time'] += td(days=365*2)
df_1y = df_1y[df_1y['forecast_time']<=dtdt.now()+td(days=1)]
df_2y = df_2y[df_2y['forecast_time']<=dtdt.now()+td(days=1)]

df = df.merge(df_1y[['forecast_time','Value']],on=['forecast_time'],suffixes=('','_1Y'),how='outer')
df = df.merge(df_2y[['forecast_time','Value']],on=['forecast_time'],suffixes=('','_2Y'),how='outer')
# df.set_index('forecast_time')['Value'].rolling(10,10).min().plot()
# df.set_index('forecast_time')['Value'].rolling(10,10).max().plot()
df.set_index('forecast_time')[['Value','Value_+(1-15)D','Value_1Y','Value_2Y']].rolling(14,7).mean().fillna(method='ffill').plot()

df['Value_30D'] = df['Value'].rolling(30,15).mean()
df['Value_15D'] = df['Value'].rolling(15,15).mean()
df['Value_60D'] = df['Value'].rolling(60,15).mean()
df.set_index('forecast_time')[['Value','Value_60D','Value_30D','Value_15D',
                               'Value_+(1-7)D','Value_+(1-15)D','Value_+(11-15)D']
                ].plot(style=['-']*4+['--']*4)



plt.show()
aa = 1