# -*- coding: utf-8 -*-
import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td
import pandas as pd
import numpy as np
import pytz
import time
from Algo.Data_Processing.gdd_handle import *
from Algo.Viasualization import visualize_live_degdays
import os
from Algo.Utils.files_handle import HOME

MAX_Z = 384
midi_above_240_zs = [12*i for i in range(20,33)]
midi_below_240 = [3*i for i in range(80)]
ALL_HORIZONS =  midi_below_240 +  midi_above_240_zs
GFS_4_HORIZONS = [z for z in ALL_HORIZONS if z <= MAX_Z]
GEFS_HORIZONS = range(0,390,6)
PARA_HORIZONS = range(0,385,3)
GEM_HORIZONS = range(0,241,3)
GEPS_HORIZONS = list(range(0,193,3))+list(range(198,385,6))

MODELS_LATENCIES = visualize_live_degdays.MODELS_LATENCIES

LOCAL_TZ = pytz.timezone("Asia/Jerusalem")
CET_TZ = pytz.timezone("CET")
MODELS_RUNS = visualize_live_degdays.MODELS_RUNS
MODEL_GAPS = visualize_live_degdays.MODEL_GAPS
MODELS_HORIZONS = visualize_live_degdays.MODELS_HORIZONS


interpolation_methods = [None, 'none', 'nearest', 'bilinear', 'bicubic', 'spline16',
           'spline36', 'hanning', 'hamming', 'hermite', 'kaiser', 'quadric',
           'catrom', 'gaussian', 'bessel', 'mitchell', 'sinc', 'lanczos']

def _last_available_model(x_dt, model):
    latency_hours, latency_minutes = MODELS_LATENCIES[model][0], MODELS_LATENCIES[model][1]
    latency_hours += LATENCY_DELAY
    model_runs = MODELS_RUNS[model]
    x_dt2 = x_dt - td(hours=latency_hours, minutes=latency_minutes)
    if x_dt2.hour in model_runs and x_dt2.minute == 0:
        return x_dt2
    else:
        while x_dt2.hour not in model_runs:
            x_dt2 -= td(hours=1)
        x_dt3 = dtdt(x_dt2.year, x_dt2.month, x_dt2.day, x_dt2.hour)

        return x_dt3


live_degdays_dir = os.path.join(HOME,"degdays_archive","FULL_FILES","Full")

#models = ['CFS']

def get_file_path(model):
    """
    :param model:
    :return:
    if model != 'GEPS':
        return live_degdays_dir+'\\live_degdays_%s.csv'% model
    else:
        return live_degdays_dir + '\\live_degdays_%s_Pmean.csv' % model
    """
    return live_degdays_dir + '\\Full_degdays_Full_%s.csv' % model

models_full = ['PARA','GEFS','PARACO','GEFSL','GFSCO',
               'GFSv16','GEM','GEMCO','GEPS','EC','EPS','CFS','CFSCO','GEFS35','GEFSCO35'] #'GEMCO',
models_ens = ['GEFS','GEFSL', 'GEPS', 'EPS']  #'GEFSL' 'GEMCO',
models_op = ['PARA','PARACO','GFSCO','GEM','GEMCO','EC'] # EPS45
models_co = ['GEFS','PARACO','PARA','GEFSL','GFSCO','GEPS','GEMCO','GEM','EC','EPS'] # EPS45
american = ['PARA','PARACO','GEFS','GFSCO','GEFSL']
chosen_models = models_full #+['CFS','EPS','EC','GEPS','GEM'] #models_ens +['PARA','PARACO','GFSCO','CFS'] #+['EPS45'] #models_ens+['PARA','GEM','EC'] #models_op
chosen_models = chosen_models #['ENS','ENS_top3','ENS_top4']
MODELS = models_full #['EPS','GEPS','GEFS']# ['EPS','GEPS','EC','GEM','GEMCO']#['GEFS','PARA','PARACO'] #['EC','EPS','GFSCO','GEFSL','PARA','GEFS','GEM','GEPS']  ### for ensemble

ft = 'GDD'
model_hours = [0,6,12,18]#[0,6,12,18] #+[6,18] #[6,18] #,6,12,18]
weight = 'population_US_weight' # 'basic_NG_weight'#


GRAPH_KIND = 'bar'
MAX_DAYS_AHEAD = 40
SHOW_MSE_HIST = False
REDUCE_SEASONALITY = True

measure = 'cor' # #'cor' # cor
normalize_mse = True
ADD_STDS = False#True #True
t_start = dtdt(2019,7,1)
t_end = dtdt.now() + td(days=5)
month_analysis = False

STDS_CHECK = True #False # check stds of different model to evaluate consistency

PLOT_ROLLING_CORS = True

multimodels_df = pd.DataFrame()
gaps_dict = {'PARA': 0.4905691056910566, 'GEFS': -0.43987804878048764, 'PARACO': -0.43813008130081266, 'GEFSL': -0.5624390243902438, 'GFSCO': -0.6373983739837397, 'GEM': -1.3852845528455289, 'GEMCO': -1.6361788617886177, 'GEPS': -1.6712601626016261,
                'GFSv16': 0.49,
            'EC': 0.27711382113821137, 'EPS': 0.03292682926829249,'EPS45': 0.03292682926829249, 'CFS': -1.8589800995024877, 'ENS': -0.6927371273712738,
             }
gaps_dict['ENS'] = pd.Series(gaps_dict).mean()
gaps_dict['ENS_top4'] = pd.Series({model: gaps_dict[model] for model in gaps_dict.keys() if model in ['GEFS','GEFSL','GEPS','EPS']}).mean()
gaps_dict['ENS_top3'] = pd.Series({model: gaps_dict[model] for model in gaps_dict.keys() if model in ['GEFS','GEFSL','EPS']}).mean()
gaps_dict['CFSCO'] = gaps_dict['CFS']
gaps_dict['GEFS35'] = gaps_dict['GEFS']
gaps_dict['GEFSCO35'] = gaps_dict['PARACO']


ensemble_df = pd.DataFrame()
models_mse_bias_df = pd.DataFrame()

def get_model_df(model, weight=weight,ft=ft):
    model_df = pd.read_csv(get_file_path(model), parse_dates=['forecast_time', 'validation_day'])
    print('model: %s had %s forecast times'%(model,model_df.drop_duplicates(subset=['forecast_time']).shape[0]))
    if model == 'PARA':
        aa = 1
    if ft == 'GDD':
        model_df = create_gdds(model_df)
    model_df = model_df[(model_df['feature_type'] == ft) & (model_df['weight'] == weight)]
    model_df = model_df.drop_duplicates(subset=['forecast_time','validation_day','feature_type','weight'])

    model_df = model_df[model_df['forecast_time'].dt.hour.isin(model_hours)]
    model_df['days_ahead'] = (model_df['validation_day'] - model_df['forecast_time']).dt.days
    model_df = model_df[(model_df['days_ahead'] >= 1) & (model_df['days_ahead'] <= MAX_DAYS_AHEAD)]

    model_df['real_Value'] = np.nan
    model_df['real_Value'][model_df['days_ahead'] == 1] = model_df['Value'][model_df['days_ahead'] == 1]
    model_df['real_Value'][model_df['days_ahead'] == 1] = model_df['Value'][model_df['days_ahead'] == 1]
    model_df.loc[(model_df['validation_day'] >= dtdt.today()) & (model_df['days_ahead'] <= 5) &
                 (model_df['forecast_time'] >= model_df['forecast_time'].max()), 'real_Value'] = \
        model_df.loc[(model_df['validation_day'] >= dtdt.today()) & (model_df['days_ahead'] <= 5) &
                     (model_df['forecast_time'] >= model_df['forecast_time'].max()), 'Value']

    real_vals_dict = model_df.dropna()[['validation_day', 'Value']].set_index('validation_day')['Value'].to_dict()

    model_df = model_df.set_index('validation_day')
    model_df['real_Value'] = model_df['real_Value'].fillna(real_vals_dict)
    model_df = model_df.reset_index()

    daily_forecasts = model_df.groupby(['validation_day', 'days_ahead']).mean().reset_index()
    return daily_forecasts


def get_ensemble_df(models=MODELS,ens_name='ENS',weight=weight,ft=ft):
    ensemble_df = pd.DataFrame()
    actual_models = models
    if ens_name == 'ENS_top3':
        actual_models = ['EPS','GEFS','GEFSL']
    elif ens_name == 'ENS_top4':
        actual_models = ['EPS','GEFS','GEFSL','GEPS']
    for model in actual_models:
        try:
            daily_forecasts = get_model_df(model,weight,ft)[['validation_day','days_ahead','Value','real_Value']]
            daily_forecasts['model'] = model
            if ensemble_df.shape[0] == 0:
                ensemble_df = daily_forecasts
            else:
                ensemble_df = ensemble_df.merge(daily_forecasts,on=list(daily_forecasts),how='outer')
        except:
            pass
    #multipliers_df = pd.Series({model:1.0/len(models) for model in models})

    ensemble_df = ensemble_df.groupby(['days_ahead','validation_day']).mean().reset_index()
    return ensemble_df

if __name__ == '__main__':

    multimodels_daily_forecasts = pd.DataFrame()
    rolling_cors_df = pd.DataFrame()
    accuracy_ppt_df = pd.DataFrame()
    for model in chosen_models: #+['ENS']: #['PARA','PARACO','GFSCO','GEM','GEMCO','GEFS','GEFSL','GEPS','CFS','EC','EPS','ENS']:
        if 'ENS' not in model:
            try:
                daily_forecasts = get_model_df(model)
            except KeyError:
                continue
        else:
            daily_forecasts = get_ensemble_df(models=MODELS,ens_name=model)
        daily_forecasts = daily_forecasts[(daily_forecasts['validation_day']>=t_start)&(daily_forecasts['validation_day']<=t_end)]
        if daily_forecasts.shape[0] == 0:
            print ('Skipping model %s x Hours %s | It was empty'% (model,model_hours))
            continue

        if STDS_CHECK:
            daily_forecasts2 = daily_forecasts.copy()
            daily_forecasts2['model'] = model
            if multimodels_daily_forecasts.shape[0] == 0:
                multimodels_daily_forecasts = daily_forecasts2
            else:
                multimodels_daily_forecasts = multimodels_daily_forecasts.merge(daily_forecasts2,on=list(set(list(multimodels_daily_forecasts)).intersection(set(list(daily_forecasts2)))),how="outer")

        if REDUCE_SEASONALITY:
            a = daily_forecasts.groupby("validation_day").mean()[['real_Value']]

            seasonal_vals_file = os.path.join(HOME,"degdays_archive","Live","Seasonal Values","","Seasonal_Values.csv")
            seasonal_vals = pd.read_csv(seasonal_vals_file, parse_dates=['validation_day'])
            seasonal_vals = seasonal_vals[(seasonal_vals['feature_type'] == ft) & (seasonal_vals['weight'] == weight)]
            seasonal_vals = seasonal_vals.rename(columns={'Value':'seasonal_Value'})
            #a.rolling(20,10,center=True).mean().reset_index()
            #daily_forecasts['seasonal_Value'] = daily_forecasts['validation_day'].apply(lambda x: seasonal_vals[seasonal_vals['validation_day']==x].iloc[0]['real_Value'])

            daily_forecasts = daily_forecasts.merge(seasonal_vals[['validation_day', 'seasonal_Value']], on=['validation_day'])
            daily_forecasts['Value'] = daily_forecasts['Value'] - daily_forecasts['seasonal_Value'] + gaps_dict[model]
            daily_forecasts['real_Value'] = daily_forecasts['real_Value'] - daily_forecasts['seasonal_Value'] + gaps_dict[model]
        if measure == 'cor':
            cr = daily_forecasts.groupby('days_ahead').corr().reset_index()
            window = 20 # 30 20
            days = range(12,16)

            b = daily_forecasts[daily_forecasts['days_ahead'].isin(days)].groupby(['validation_day']).mean()
            c = b.fillna(b.mean()).rolling(window, window).corr()
            c = c.reset_index()#
            c = c[c['level_1'] == 'Value'][['validation_day', 'real_Value']]
            if model == 'PARA':
                aaa = 1

            c = c.rename(columns={'real_Value':'Corr_%s_%s'%(20,model)})
            #todo hack taking rolling mean of the rolling corr
            c = c.set_index('validation_day').rolling(10,1).mean().reset_index()
            daily_forecasts['real_Error'] = daily_forecasts['Value'] - daily_forecasts['real_Value']
            d = daily_forecasts[daily_forecasts['days_ahead'].isin(days)].groupby(
                ['validation_day']).mean().reset_index()
            d = d[['validation_day', 'real_Error']].rename(columns={'real_Error': 'real_Error_%s_%s' % (window, model)})
            if rolling_cors_df.shape[0] == 0:
                rolling_cors_df = c
                accuracy_ppt_df = d
            else:
                rolling_cors_df = rolling_cors_df.merge(c,on=['validation_day'],how='outer')
                accuracy_ppt_df = accuracy_ppt_df.merge(d,on=['validation_day'],how='outer')
            print ('rolling_Corrs Shape: %s'%rolling_cors_df.shape[1])
            try:
                cr = cr[cr['level_1']=='real_Value']
            except Exception as e:
                aa = 1
            current = cr[['days_ahead','Value']].rename(columns={'Value':'Value_%s'%model})

        elif measure == 'mse':
            daily_forecasts['month'] = daily_forecasts['validation_day'].dt.month
            daily_forecasts['real_Error'] = daily_forecasts['Value']-daily_forecasts['real_Value']
            daily_forecasts['Value'] = abs(daily_forecasts['Value']-daily_forecasts['real_Value'])
            if normalize_mse:
                #daily_forecasts['Value'] /= daily_forecasts['real_Value']
                daily_forecasts['Value'] /= abs(daily_forecasts['seasonal_Value' if REDUCE_SEASONALITY else 'real_Value']) #.std() #(2*daily_forecasts['real_Value'].std())
                if SHOW_MSE_HIST:
                    if measure == 'mse':
                        daily_forecasts2 = daily_forecasts.copy()
                        mn = daily_forecasts2.groupby('days_ahead').mean().reset_index()[['days_ahead','real_Error']]
                        if models_mse_bias_df.shape[0] == 0:
                            models_mse_bias_df = mn
                        else:
                            models_mse_bias_df = models_mse_bias_df.merge(mn,on=['days_ahead'],suffixes=("","_%s"%model),how="outer")
                        #print ("Model: %s |  Avg bias was: %s"%(model,mn))
                        #daily_forecasts2 = daily_forecasts2[daily_forecasts2['days_ahead']==10]
                        #daily_forecasts2[['days_ahead','Value']].groupby(['days_ahead']).hist(bins=40)
                        #plt.title(model)
                        #plt.show()
                if REDUCE_SEASONALITY:
                    daily_forecasts['Value'] = (1 - daily_forecasts['Value'])
                else:
                    daily_forecasts['Value'] = (1 - daily_forecasts['Value'])
            if month_analysis:
                current = daily_forecasts.groupby(['days_ahead','month']).mean().reset_index()[['days_ahead','month','Value']].rename(columns={'Value':'Value_%s'%model})
                current_std = daily_forecasts.groupby(['days_ahead','month']).std().reset_index()[['days_ahead','month', 'Value']].rename(columns={'Value': 'Value_%s' % model})
            else:
                current = daily_forecasts.groupby(['days_ahead']).mean().reset_index()[['days_ahead', 'Value']].rename(columns={'Value': 'Value_%s' % model})
                current_std = daily_forecasts.groupby(['days_ahead']).std().reset_index()[['days_ahead', 'Value']].rename(
                    columns={'Value': 'Value_%s' % model})

        real = -1.5
        current_model_rs = daily_forecasts[daily_forecasts['days_ahead']==1]['Value'].mean()
        gap_to_real = real - current_model_rs
        #gaps_dict[model] = gap_to_real
        print ('Model: %s  | Avg rs was: %s'%(model, current_model_rs))
        if multimodels_df.shape[0] == 0:
            multimodels_df = current  # .plot(kind='bar',x='days_ahead',title='Correlations of Model: %s | Summer CDD'%model)
            if measure == 'mse':
                multimodels_df_std = current_std
        else:
            multimodels_df = multimodels_df.merge(current, on=['days_ahead','month'] if month_analysis else ['days_ahead'], how='outer')
            if measure == 'mse':
                multimodels_df_std = multimodels_df_std.merge(current_std,on=['days_ahead', 'month'] if month_analysis else ['days_ahead'],
                                                      how='outer')

    if PLOT_ROLLING_CORS:
        strats_to_concat = ['y_0000-0600','y_0600-0800','y_0800-1745','y_1745-1845'][-2:] #'y_0800-1745'
        strat = 'y_0800-2000_concated'
        trades_ratio = 0.5
        trades_start = dtdt(2019,7,1)
        trades_end = dtdt(2050, 9, 1)
        weekdays = [0,1,2,3,4]

        accuracy_ppt_df = accuracy_ppt_df[accuracy_ppt_df['validation_day']>dtdt(2019,3,1)]
        for c in [x for x in list(accuracy_ppt_df) if 'day' not in x]:
            accuracy_ppt_df[c][accuracy_ppt_df[c]<0] -= 1
            accuracy_ppt_df[c][accuracy_ppt_df[c] > 0] += 1
        # check error sign
        real_error_window = 20
        accuracy_ppt_df.set_index('validation_day').rolling(real_error_window, real_error_window).mean().plot(title='%s Moving Average or the Real Error (Bias) for US'%real_error_window)
        #s = np.square(accuracy_ppt_df.set_index('validation_day'))
        s = abs(accuracy_ppt_df.set_index('validation_day'))
        s.cumsum().plot(title='cummulative Error by model | Days = %s'%days,style=['-']*6+['--']*max(0,(s.shape[1]-6)))
        s.rolling(50,10).mean().plot(title='cummulative Error by model | Days = %s' % days,style=['-'] * 6 + ['--'] * max(0, (s.shape[1] - 6)))

        a_df = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","XY_a_GDD_v8_0Zb.csv"), parse_dates=['date'])
        a_df['y_0800-1945'] = a_df['y_0800-1745']+a_df['y_1745-1945']
        a_df['y_0800-2000_concated'] = a_df[strats_to_concat].sum(axis=1)
        a_df = a_df.dropna(subset=[strat])

        D = '1D'
        D2 = '2D'
        preds = ['EPSpost_pred2b','EPSpost_pred2.2','EPS_daily_hybrid_pred1','EPS_daily_hybrid_pred2','EPSL_daily_hybrid_pred4','PARA_daily_hybrid_pred2',
                 'diff_0Z_0-13_last-Prev1D_EPS',#'PARACO_daily_hybrid_pred2','diff_0Z_0-16_last-Prev%s_GEPS'%D,'diff_0Z_0-16_last-Prev%s_GEMCO'%D,
                 #'diff_0Z_0-13_last-Prev%s_EPS'%D,'diff_0Z_0-8_last-Prev%s_EC'%D,
                 #'diff_0Z_0-13_last-Prev2D_EPS','diff_0Z_0-13_last-Prev3D_EPS',
                 'diff_0Z_0-16_last-Prev%s_GEFSL'%D,'diff_0Z_0-16_last-Prev%s_GFSCO'%D,
                 'GEFSL_daily_hybrid_pred2','diff_0Z_0-16_last-Prev1D_GEFSL','diff_0Z_0-16_last-Prev2D_GEFSL',
                 'diff_0Z_0-16_last-Prev3D_GEFSL'
                 #'diff_0Z_0-16_last-Prev%s_GEFS'%D,'diff_0Z_0-16_last-Prev%s_PARA'%D,
                 #'diff_0Z_0-16_last-Prev%s_PARACO'%D,
                 ]#'diff_0Z_0-16_last-Prev%s_CFS'%D,'diff_0Z_10-21_last-Prev%s_CFS'%D]
        preds = ['diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-16_last-Prev%s_PARA'%D,'diff_0Z_0-16_last-Prev%s_PARA'%D2,
                 'diff_0Z_0-16_last-Prev%s_PARACO'%D,'diff_0Z_0-16_last-Prev%s_PARACO'%D2,'diff_0Z_0-16_last-Prev%s_GFSv16'%D,
                 'diff_0Z_0-16_last-Prev%s_GEFS'%D,'diff_0Z_0-16_last-Prev%s_GEFS'%D2,
                 'diff_0Z_0-16_last-Prev%s_GEPS'%D,'diff_0Z_0-16_last-Prev%s_GEPS'%D2,
                 'diff_0Z_0-16_last-Prev%s_GEFSL'%D,'diff_0Z_0-16_last-Prev%s_GEFSL'%D2,
                 'diff_0Z_0-16_last-Prev%s_GFSCO' % D, 'diff_0Z_0-16_last-Prev%s_GFSCO' % D2,
                 'diff_0Z_0-13_last-Prev%s_EPS'%D,'diff_0Z_0-13_last-Prev%s_EPS'%D2]+ \
                ['GEFS_daily_hybrid_pred3','EPSpost_pred2b', 'EPSpost_pred2.2', 'EPS_daily_hybrid_pred1','EPS_daily_hybrid_pred2.2', 'EPS_daily_hybrid_pred2', 'EPSL_daily_hybrid_pred4',
                 'PARA_daily_hybrid_pred2'][:1]
        preds = ['diff_0Z_0-13_last-Prev1D_EPS','diff_0Z_0-16_last-Prev1D_GEFS',
                 'diff_0Z_0-16_last-Prev4_PARA','diff_0Z_0-16_last-Prev4_PARACO','diff_0Z_0-16_last-Prev4_GEFS',
                 'diff_0Z_0-13_last-Prev2_EPS',
                 'diff_0Z_0-16_last-Prev2_PARA','diff_0Z_0-16_last-Prev2_PARACO','diff_0Z_0-16_last-Prev2_GEFS',
                 'diff_0Z_0-13_last-Prev1_EPS',

                 'diff_0Z_8-16_last-Prev4_PARA', 'diff_0Z_8-16_last-Prev4_PARACO', 'diff_0Z_8-16_last-Prev4_GEFS',
                 'diff_0Z_9-13_last-Prev2_EPS',
                 'diff_0Z_8-16_last-Prev2_PARA', 'diff_0Z_8-16_last-Prev2_PARACO', 'diff_0Z_8-16_last-Prev2_GEFS',
                 'diff_0Z_9-13_last-Prev1_EPS',]
        #preds = [p for p in preds if '4D' not in p]
        a_df = a_df[['date',strat]+preds]
        for c in preds:
            try:
                is_hit = np.sign(a_df[c]*a_df[strat])
            except:
                a_df[c] = pd.to_numeric(a_df[c])
                is_hit = np.sign(a_df[c] * a_df[strat])
            a_df['profit_%s'%c] = abs(a_df[strat])*is_hit
            thresh_val = abs(a_df[c]).quantile(1-trades_ratio)
            a_df.loc[abs(a_df[c]) < thresh_val, 'profit_%s'%c] = 0
        a_df = a_df.fillna(0)

        signal_corr = a_df.set_index('date').rolling(20, 1).corr().reset_index()
        signal_corr = signal_corr[signal_corr['level_1'] == strat]
        signal_corr['validation_day'] = signal_corr['date'] - td(hours=8)
        a_df['validation_day'] = a_df['date'] - td(hours=8)
        signal_corr = signal_corr[[x for x in list(signal_corr) if x not in ['date','level_1']]]
        signal_corr = signal_corr.set_index('validation_day').rolling(10,1).mean().reset_index()

        cors_fts = [x for x in ['Corr_20_EC','Corr_20_GEM','Corr_20_GEMCO','Corr_20_GEPS', 'Corr_20_EPS','Corr_20_GEFS', 'Corr_20_PARA',
                                'Corr_20_PARACO','Corr_20_GFSv16','Corr_20_CFS','Corr_20_CFSCO',
                                 # 'Corr_20_GEFSL', 'Corr_20_GFSCO',
                                'Corr_20_GEFS35','Corr_20_GEFSCO35'] if x.split('_')[2] in chosen_models]
        for cor_ft in cors_fts:
            rolling_cors_df[cor_ft+'_diff7'] = rolling_cors_df[cor_ft].diff(4).rolling(2,2).mean() # 7 x 3,3
            rolling_cors_df[cor_ft+'_+0.5diff7'] = rolling_cors_df[cor_ft] + 0.5 * rolling_cors_df[cor_ft+'_diff7']

        #todo
        #rolling_cors_df[['validation_day', 'Corr_20_GEPS', 'Corr_20_GEPS_diff7']].plot(x='validation_day')
        #rolling_cors_df.to_csv(os.path.join(HOME,"Accuract Weights","rolling_cors_window=%s_days=%s") % (window, days))

        final = signal_corr.merge(rolling_cors_df,on=['validation_day'],how='outer')
        final = final[[x for x in list(final) if 'profit' not in x]].merge(a_df[[x for x in list(a_df) if 'profit' in x or x == 'validation_day']],
                                                                           how='outer')

        tmp_model = 'EPS'
        smart_filtering_check = True ## todo
        days_for_accuracy = {'GEFS': 20, 'PARA': 45, 'GEPS':20,'PARACO': 30,'EPS':20}#30}
        multi_good = pd.DataFrame()
        if smart_filtering_check:
            prds = []
            for model in ['EPS','GEFS']:
                d = '1' if model not in ['PARA','PARACO'] else '4'
               #d = '4'
                #d =1
                #model = 'EPS'
                prd = 'diff_0Z_0-16_last-Prev%sD_%s'%(d,model)
                if model == 'EPS':
                    prd = 'diff_0Z_0-13_last-Prev%sD_%s' % (d, model)
                #prd = 'EPSL_daily_hybrid_pred4' if model == 'EPS' else 'GEFS_daily_hybrid_pred3'

                prds.append(prd)
                s = final[['validation_day', prd, 'Corr_20_%s'%model, 'profit_%s'%prd]].sort_values('validation_day')
                s['Corr_20_%s'%model] = s['Corr_20_%s'%model].tolist()[5:]+[np.nan]*5

                good_trades = s['Corr_20_%s'%model] > s['Corr_20_%s'%model].rolling(days_for_accuracy[model],1).mean()
                #good_trades = s['Corr_20_%s' % model] >= s['Corr_20_%s' % model].rolling(days_for_accuracy[model], 1).quantile(0.3)
                good_trades = s['Corr_20_%s'%model] > s['Corr_20_%s'%model].quantile(0.3) #.mean()

                good = s[good_trades]
                bad = s[~good_trades]
                s.loc[~good_trades,'profit_%s'%prd] = 0
                #good = s[s['Corr_20_%s' % model] > s['Corr_20_%s' % model].mean()]
                #bad = s[s['Corr_20_%s' % model] < s['Corr_20_%s' % model].mean()]
                good_profit = good['profit_%s'%prd].mean()
                bad_profit = bad['profit_%s'%prd].mean()
                print ('Pred = %s'%prd)
                print('Good PPT: %s'%good_profit)
                print('Bad PPT: %s' % bad_profit)
                #good.set_index('validation_day')['profit_%s'%prd].fillna(0).cumsum().plot()
                #bad.set_index('validation_day')['profit_%s' % prd].fillna(0).cumsum().plot()
                #plt.show()
                if multi_good.shape[0] == 0:
                    multi_good = s[['validation_day','profit_%s'%prd]]
                else:
                    multi_good = multi_good.merge(s[['validation_day','profit_%s'%prd]],on='validation_day')
            multi_good.set_index('validation_day')[['profit_%s'%prds[0],'profit_%s'%prds[1]]].fillna(0).cumsum().plot()
            multi_good.loc[multi_good['profit_%s'%prds[0]] ==0,'profit_%s'%prds[0]] = multi_good.loc[multi_good['profit_%s'%prds[0]] ==0,'profit_%s'%prds[1]]
            multi_good.set_index('validation_day')['profit_%s'%prds[0]].cumsum().plot()
            plt.show()
        #a1 = final[((final['Corr_20_%s'%tmp_model] > final['Corr_20_%s'%tmp_model].median())&(final['Corr_20_GEFSL_diff7']<1110))][['profit_diff_0Z_0-13_last-Prev1D_%s'%tmp_model]].cumsum()
        # final['Corr_20_%s' % tmp_model].median()
        try:
            final = final.dropna(subset=['Corr_20_%s' % tmp_model])
            good_corr = (final['Corr_20_%s' % tmp_model] > final['Corr_20_%s' % tmp_model].median())
            bad_corr = ~(good_corr)
            bad_diff = (final['Corr_20_%s_diff7'%tmp_model] < final['Corr_20_%s_diff7'%tmp_model].median()) #0)
            good_diff = ~(bad_diff)
            good_corr2 = (final['Corr_20_%s_+0.5diff7' % tmp_model] > final['Corr_20_%s_+0.5diff7' % tmp_model].median())
            bad_corr2 = ~good_corr2

            a1 = final[good_corr][['profit_diff_0Z_0-13_last-Prev1D_%s' % tmp_model]] # # (good_corr&bad_diff)|(bad_corr&good_diff)
            a2 = final[good_diff][['profit_diff_0Z_0-13_last-Prev1D_%s' % tmp_model]] # (good_corr & good_diff) | (bad_corr & good_diff)
            is_hit_a1 = a1['profit_diff_0Z_0-13_last-Prev1D_%s' % tmp_model] > 0
            is_hit_a2 = a2['profit_diff_0Z_0-13_last-Prev1D_%s' % tmp_model] > 0
            a1.cumsum().plot(title='A1')
            a2.cumsum().plot(title='A2')
            print ('Hit rate Good&Bad = %s'%is_hit_a1.mean())
            print('Hit rate Good&Good = %s' % is_hit_a2.mean())
            plt.show()
            final[['validation_day', 'Corr_20_GEFSL', 'diff_0Z_0-16_last-Prev1D_GEFSL']].plot(x='validation_day')
        except:
            pass

        final[['validation_day']+cors_fts].plot(
            x='validation_day',style=['-']*7+['--']*(len(cors_fts)-7))
        final.set_index('validation_day')[preds].dropna().plot(style=['-']*7+['--']*(len(preds)-7))

        a_df = a_df[(a_df['date']<=trades_end)&(a_df['date']>=trades_start)&(a_df['date'].dt.weekday.isin(weekdays))]
        a_df.set_index('date').fillna(0)[['profit_%s' % prd for prd in preds]].cumsum().plot(style=['-']*7+['--']*(len(preds)-7))
        plt.show()
        # final[['validation_day']+[x for x in list(final) if 'Corr' in x]]
        rolling_cors_df.to_csv(
            os.path.join(HOME,"performance_analysis","rolling_Corr_fts","","rolling_cors_hours=%s_window=%s.csv") % (model_hours,window),index=False)
        rolling_cors_df.plot(x='validation_day',style=['-']*(rolling_cors_df.shape[1]-1-7)+['--']*7)
        d_012 = pd.read_csv(os.path.join(HOME,"performance_analysis","rolling_Corr_fts","","rolling_cors_hours=[0,12]_window=%s.csv")%window)
        d_618 = pd.read_csv(os.path.join(HOME,"performance_analysis","rolling_Corr_fts","","rolling_cors_hours=[0,6,12,18]_window=%s.csv") % window)
        d_6 = pd.read_csv(os.path.join(HOME,"performance_analysis","rolling_Corr_fts","","rolling_cors_hours=[6]_window=%s.csv") % window)
        d_18 = pd.read_csv(os.path.join(HOME,"performance_analysis","rolling_Corr_fts","","rolling_cors_hours=[18]_window=%s.csv") % window)
        d = d_012.merge(d_618,on=['validation_day'],suffixes=("_012","_0618"))
        d2 = d_6.merge(d_18,on=['validation_day'],suffixes=("_6","_18"))
        d3 = d.merge(d2,on=['validation_day'])

        d = d.set_index('validation_day')
        #d3[[x for x in list(d3) if sum([s in x for s in ['_012', '_0618', 'CFS_6','CFS_18']]) and 'diff7' not in x and 'diff' not in x and 'CFS' in x]].rolling(10, 1).mean().plot()
        for c in list(d_618):
            if c == 'validation_day':
                continue
            d[c+'_diff_012-618'] = d[c+"_012"] - d[c+'_0618']
        d[[x for x in list(d) if 'diff_0' in x and 'diff7' not in x]].plot()
        plt.show()
        aaa = 1


    ## Stds check for models conssitency
    """
    multimodels_daily_forecasts['forecast_time'] = multimodels_daily_forecasts['validation_day'] - multimodels_daily_forecasts['days_ahead'].apply(lambda x: td(days=x))
    mean_val = multimodels_daily_forecasts['Value'].mean()
    stds1 = multimodels_daily_forecasts.groupby(['validation_day','days_ahead']).std().reset_index() #.groupby('days_ahead').mean()['Value']
    means1 = multimodels_daily_forecasts.groupby(['validation_day','days_ahead']).mean().reset_index() #.groupby('days_ahead').mean()['Value']
    stds1['Value_normed'] = stds1['Value']*(mean_val/means1['Value'])
    stds1[stds1['days_ahead']==14][['Value','Value_normed']].plot()
    mean1 = stds1.groupby('days_ahead').mean()['Value']
    upper = mean1 + stds1.groupby('days_ahead').std()['Value'] #_normed']
    lower = mean1 - stds1.groupby('days_ahead').std()['Value'] #_normed']
    a = pd.DataFrame(upper);a=a.rename(columns={'Value': 'upper'});a['lower'] = lower;a['mean'] = mean1
    a.to_csv(os.path.join(HOME,"degdays_archive","Live","","stds_between_models_by_daysAhead_HDD_Oct19_NotNormed.csv"),index=False)
    final_stds = stds1.groupby('days_ahead').mean()
    """
    analyze_seasonals = False

    if analyze_seasonals:
        multimodels_daily_forecasts2 = multimodels_daily_forecasts[multimodels_daily_forecasts['days_ahead']==3]
        multimodels_daily_forecasts2['ones'] = 1
        m2 = multimodels_daily_forecasts2.groupby(['validation_day']).sum().reset_index()
        good_days = m2[m2['ones']>=2]['validation_day'].tolist()
        m3 = multimodels_daily_forecasts2[multimodels_daily_forecasts2['validation_day'].isin(good_days)] #multimodels_daily_forecasts2.groupby('c')
        avg = m3.groupby('validation_day').mean()[['Value']].reset_index()
        final = pd.DataFrame()
        for model,model_group in m3.groupby('model'):
            model_group = model_group[['validation_day','Value']]
            model_group = model_group.rename(columns={'Value':'Value_%s'%model})
            if final.shape[0] == 0:
                final = model_group
            else:
                final = final.merge(model_group, on='validation_day')
        final = final.merge(avg,on=['validation_day'])
        for val_col in ['Value_%s'%model for model in chosen_models]:
            final[val_col+'_-avg'] = final[val_col] - final['Value_EC']
        final[['validation_day']+['Value_%s_-avg'%model for model in chosen_models]].plot(x='validation_day')
        final.set_index('validation_day')[['Value_%s_-avg'%model for model in chosen_models]].rolling(3).mean().plot()
        plt.show()

    dates_str = "%s ---> %s | Model Runs: %s " % (t_start.strftime("%d/%m/%Y"),t_end.strftime("%d/%m/%Y"),model_hours)
    if month_analysis:
        for month,gp in multimodels_df.groupby('month'):
            gp[[c for c in list(gp) if c != 'month']].plot(kind=GRAPH_KIND,x='days_ahead',title='Month: %s | correlation to real %s by model and Forecast horizon'%(month,ft))
    else:
        if measure == 'mse':
            if ADD_STDS:
                multimodels_df_std /= 2
                multimodels_df.plot(kind=GRAPH_KIND, x='days_ahead',yerr=multimodels_df_std,
                            title='MSE+- std to real %s by model and Forecast horizon | %s'%(ft, dates_str),ylim=(0.6,1))
            else:
                multimodels_df.plot(kind=GRAPH_KIND, x='days_ahead',
                                    title='MSE to real %s by model and Forecast horizon | %s'%(ft, dates_str))
                multimodels_df_std.plot(kind=GRAPH_KIND, x='days_ahead',
                                    title='Stds of the MSE to real %s by model and Forecast horizon | %s' %(ft,dates_str))

        else:
            # kind='bar'
            multimodels_df.plot(kind=GRAPH_KIND,x='days_ahead', title='correlation to real %s by model and Forecast horizon | %s '%(ft,dates_str))
    plt.show()

    if models_mse_bias_df.shape[0]:
        models_mse_bias_df = models_mse_bias_df.rename(columns={"real_Error": "real_Error_%s"% chosen_models[0]})
        models_mse_bias_df.plot(kind='bar',title="Avg MSE by day Ahead | %s" % dates_str, x='days_ahead')
        plt.show()
    a = 1
    """
    """
    dates_str2 = dates_str.replace(" ---> ","-").replace("/",".").split(" |")[0]
    for rolling_window in [3]:
        if rolling_window == 0:
            multimodels_df['days_group'] = ((multimodels_df['days_ahead'] / 3) - 0.01).apply(lambda x: int(x))
        elif rolling_window > 0:
            multimodels_df['days_group'] = multimodels_df['days_ahead']
        #multimodels_df = multimodels_df.groupby('days_group').mean().reset_index()
        m2 = multimodels_df.copy()

        for col in [x for x in list(m2) if 'days' not in x]:
            m2[col][multimodels_df[col].isna()] = np.nan
        m2[[x for x in list(m2) if x not in ['days_ahead','days_group']]] = m2[[x for x in list(m2) if x not in ['days_ahead','days_group']]].rolling(rolling_window,1,center=False).mean()
        #m2.to_csv(os.path.join(HOME,"degdays_archive","Live","Models_accuracy_%s_%sday_rollingMean_%s.csv")%(ft,rolling_window,dates_str2),index=False)
        m2[[x for x in list(m2) if 'days_group' not in x]].plot(kind='bar', x='days_ahead')
        plt.show()

    aa = 1