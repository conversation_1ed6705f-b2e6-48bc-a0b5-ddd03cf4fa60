import numpy as np
import pandas as pd

NUM_OF_TRADES_PER_YEAR = 252


def calc_sharpe(returns, risk_free=0,resolution='daily'):
    """

    :param returns:
    :param risk_free: daily return of risk free asset
    :param is_monthly:
    :return:
    """
    adj_returns = returns - risk_free
    num_trades = {'daily':252,'weekly':52,'monthly':12,'yearly':1}[resolution]
    factor = np.sqrt(num_trades)
    return (np.nanmean(adj_returns) * factor) \
        / np.nanstd(adj_returns, ddof=1)


def sortino_ratio(returns_array, risk_free_rate=0, resolution='daily'):
    """
    Calculates the Sortino Ratio for a given array of returns.

    Args:
    - returns_array (np.ndarray): Array of returns
    - risk_free_rate (float): Risk-free rate of return. Default is 0.
    - periods_per_year (int): Number of return periods per year. Default is 12 (monthly returns).

    Returns:
    - sortino_ratio (float): The Sortino Ratio of the returns_array.
    """
    num_trades = {'daily': 252, 'weekly': 52, 'monthly': 12, 'yearly': 1}[resolution]
    factor = np.sqrt(num_trades)

    downside_returns = np.where(returns_array < risk_free_rate, returns_array - risk_free_rate, 0)
    downside_volatility = np.std(downside_returns, ddof=1) * factor
    sortino_ratio = np.mean(returns_array - risk_free_rate) / downside_volatility if downside_volatility != 0 else np.nan
    return sortino_ratio

def sortino_ratio_v2(series, risk_free=0,resolution='daily'):
    num_trades = {'daily': 252, 'weekly': 52, 'monthly': 12, 'yearly': 1}[resolution]
    factor = np.sqrt(num_trades)
    mean = (series -risk_free).mean() * factor
    std_neg = series[series<0].std()
    return (mean/std_neg)



def calc_sharpe_v2(returns, risk_free=0,is_monthly=False):
    """
    dropping the risk free after mean
    :param returns:
    :param risk_free:
    :param is_monthly:
    :return:
    """
    adj_returns = returns
    return ((np.nanmean(adj_returns)-risk_free) * np.sqrt(NUM_OF_TRADES_PER_YEAR if not is_monthly else 1)) \
        / np.nanstd(adj_returns, ddof=1)



def annual_returns(returns,weights=None):
    if weights is None:
        return returns.mean()*NUM_OF_TRADES_PER_YEAR
    else:
        if isinstance(weights,list):
            weights = pd.Series(weights)
        return (returns.reset_index()[0] * weights).mean() * NUM_OF_TRADES_PER_YEAR

def max_drawdown(returns):
    returns = returns.cumsum()
    drawdown = returns - returns.cummax()
    max_dd = drawdown.min() #.iloc[0]
    return max_dd

def get_drawdown_vector(returns):
    returns = returns.cumsum()
    drawdown = returns - returns.cummax()
    return drawdown


def calc_calmar(returns, use_annual=True,epsilon_reduction=False,weights=None):

    max_dd = max_drawdown(returns)
    if epsilon_reduction:
        if max_dd == 0:
            max_dd -= 0.01
    if max_dd < 0:
        if weights is None:
            pnl = annual_returns(returns) if use_annual else returns.sum()
        else:
            pnl = annual_returns(returns,weights) if use_annual else np.sum(returns*weights)
        return round(pnl / abs(max_dd),2), round(max_dd,2)

    return np.nan,np.nan

def calc_calmar_on_candles(returns,candle_worst_profit, use_annual=True,epsilon_reduction=False,weights=None):

    max_dd = max_drawdown(returns)
    if epsilon_reduction:
        if max_dd == 0:
            max_dd -= 0.01
    if max_dd < 0:
        if weights is None:
            pnl = annual_returns(returns) if use_annual else returns.sum()
        else:
            pnl = annual_returns(returns,weights) if use_annual else np.sum(returns*weights)
        return round(pnl / abs(max_dd),2), round(max_dd,2)

    return np.nan,np.nan

def get_all_kpis(returns,drop_ignored_from_ppt=True,use_annual_returns=True,name_suffix=''):
    ignored_mask = (returns == 0)
    if drop_ignored_from_ppt:
        ppt = returns[~ignored_mask].mean()
        hr = (returns[~ignored_mask]>0).mean()
    else:
        ppt = returns.mean()
        hr = (returns > 0).mean()
    pnl = returns.sum()
    calmar,max_dd = calc_calmar(returns,use_annual_returns)
    sharpe = calc_sharpe(returns)

    return {'PPT%s'%name_suffix:ppt,'HR%s'%name_suffix:hr,'PNL%s'%name_suffix:pnl,
            'Calmar%s'%name_suffix:calmar,'MaxD%s'%name_suffix:max_dd,'Sharpe%s'%name_suffix:sharpe}

def print_kpis(final_daily_profits,window_name,round=2):
    calmar, max_dd = calc_calmar(final_daily_profits, use_annual=False)
    sharpe = calc_sharpe(final_daily_profits)
    # round
    calmar = np.round(calmar,round)
    max_dd = np.round(max_dd,round)
    sharpe = np.round(sharpe,round)
    num_of_gte_1000_losses = (final_daily_profits <= -1000).sum()
    pnl = np.round(final_daily_profits.sum(), 2)
    print('profit for window %s = %s | calmar, max_dd = (%s, %s) | sharpe = %s | {%s} Losses 1000$ ' % (
        window_name, pnl, calmar, max_dd, sharpe, num_of_gte_1000_losses))
    results_dict = {'window_name':window_name,'calmar':calmar, 'max_dd':max_dd,'sharpe':sharpe,'pnl':pnl}
    return results_dict
