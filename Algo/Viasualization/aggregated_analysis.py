import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
import time
# from scipy.interpolate import spline, interp1d

from visualize_live_degdays import *
from visualize_live_degdays import _last_available_model, get_file_path
import os
from Algo.Utils.files_handle import HOME


# weekly analysis
SHIFT_BY_MARKET_HOURS = True
CFS_WEEKLY = True

# normalization options
UNITE_VALS = True   # make Values start from same point
NORMALIZE_BY_ACCURACY = 3 # 3
FILL_NANS_METHOD = 2
NUM_OF_FILLNA_SAMPLES = 5
PLOT = True
# main options
MODELS = ['PARA','GFS','GEFS','GEFSL','GEM','GEPS','CFS','EC','EPS']
WEIGHT = 'population_US_weight' #'NG16_midwest_weight' #
FEATURE = 'HDD'
VAL_OR_DIFF = 'Diff' # #'Value'

takeback_hours = 0 #12
hours_back = 96
LATENCY_DELAY = 0 # considers time it takes me to move the models to analyis

# reference times
now_naive = dtdt.now()
local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

ACCURACY_CSV = os.path.join(HOME,"degdays_archive","Live","Models_accuracy_%sday_group_Jul-Sep19.csv")%NORMALIZE_BY_ACCURACY

assert os.path.isfile(ACCURACY_CSV) or NORMALIZE_BY_ACCURACY == 0
assert FILL_NANS_METHOD in [0,1,2]

"""_____________________________________________________________________________________"""

"""_______ Normalization______"""
def get_multiplier(weighting_df, model, days_group, days_per_group=5):
    if not days_group < days_per_group:
        raise AssertionError('ddd')
    row = weighting_df[weighting_df['days_group']==days_group].iloc[0]
    val = row['Value_%s'%model]
    return val


def normalize_by_accuracy(multi_models_df, accuracy_csv, model=None):
    models_weighting = pd.read_csv(accuracy_csv)
    multi_models_df['days_group'] = ((multi_models_df['validation_day'] - multi_models_df['validation_day'].iloc[0]).dt.days / 3 -0.01).apply(lambda x: int(x))
    multi_models_df = multi_models_df[multi_models_df['days_group'] < 5]
    lst = [c for c in list(multi_models_df) if 'Diff' in c or 'Value' in c]
    for col in lst:
        if model is None:
            model = col.split('_')[0]
        tmp_df = multi_models_df[['days_group', col]]
        multipliers = tmp_df.apply(lambda x: get_multiplier(models_weighting,model,x['days_group']), axis=1)
        multi_models_df['tmp_multi'] = multipliers
        if VAL_OR_DIFF == 'Diff':
            multi_models_df[col] *= multi_models_df['tmp_multi']
        else:
            multi_models_df[col] = (multi_models_df[col]*multi_models_df['tmp_multi']) + multi_models_df[col].mean()*(1-multi_models_df['tmp_multi'])
        aa = 1
    multi_models_df = multi_models_df[[c for c in list(multi_models_df) if c not in ['tmp_multi','days_group']]]
    return multi_models_df


def normalize_values(multi_models_df):
    """
    make all models start with same value
    :param multi_models_df:
    :return:
    """
    numeric_cols = [c for c in list(multi_models_df) if 'Val' in c]
    d0_mean = multi_models_df[numeric_cols].iloc[0].mean()
    gaps = (multi_models_df[numeric_cols].iloc[0] - d0_mean).to_dict()
    for col in numeric_cols:
        multi_models_df[col] -= gaps[col]
    return multi_models_df


"""____________ NANs___________"""


def fill_nans(final,method=2):
    for c in list(final):
        if final[c].isna().mean() > 0:
            if method == 1:
                final[c] = final[c].fillna(method='ffill')
            elif method == 2:
                try:
                    nans_number = final[c].isna().sum()
                    val_for_fill = final[c][-NUM_OF_FILLNA_SAMPLES-nans_number:].mean()
                    #print ("INFO | Column: %s | #Days: %s | #Nans = %s| Value for fill = %s" % (c,NUM_OF_FILLNA_SAMPLES, nans_number,val_for_fill))
                    final[c] = final[c].fillna(val_for_fill)
                except TypeError:
                    #print ('Warning FillNans on Column: %s Failed' %c)
                    final[c] = final[c].fillna(method='ffill')
    final = final.dropna()
    return final

"""____________Weekly Analysis_____________"""

def weekly_analysis(df,model,ft,weight,show_diff=True, norm_acc=True):
    if SHIFT_BY_MARKET_HOURS:
        df['forecast_time'] += td(hours=12 if model in ['EPS', 'CFS'] else 6)
    df['fcst_date'] = df['forecast_time'].dt.date

    new = df.groupby(['fcst_date', 'validation_day']).mean().reset_index()[['fcst_date', 'validation_day', 'Value']]
    final = pd.DataFrame()
    for name, group in new.groupby('fcst_date'):
        if final.shape[0] == 0:
            final = group
            name0 = name
        else:
            final = final.merge(group[['validation_day', 'Value']], on=["validation_day"],
                                suffixes=('', '_%s' % name), how="outer")
    final = final.rename(columns={'Value': 'Value_%s' % name0})
    if FILL_NANS_METHOD:
        final = fill_nans(final,FILL_NANS_METHOD)
    names = [c for c in sorted(list(final)) if c not in ['validation_day','fcst_date']]
    if show_diff:
        new_names = []
        last_val = final[names[-1]]
        for other in names[:-1]:
            final[other.replace('Value','Diff_last-->')] = last_val - final[other]
            new_names.append(other.replace('Value','Diff_last-->'))
        names = new_names
    if norm_acc:
        final = normalize_by_accuracy(final,ACCURACY_CSV,model)
    daily_vectors = [final[c].cumsum().apply(lambda x: round(x,2)).tolist() for c in names]
    print('--------------  Model: %s -----------------\n Weekly Anaylsis for Feature: %s | Weight: %s:\n%s'%(model,ft,weight, "\n".join(["%s  | %s "%(str(name), str(vec)) for name,vec in zip(names,daily_vectors)])))
    return final


if __name__ == "__main__":
    multi_models_df = pd.DataFrame()
    multi_models_weekly_df = pd.DataFrame()
    models = MODELS
    ft = FEATURE
    for weight in [WEIGHT]: #,'NG16_midwest_weight']:
        for model in models: # models: #['GEM','GEPS']: #['GFS','PARA','GEFS']: #['GEM','GEPS','GFS','PARA','GEFS']: #,'GFS','GEM','GEPS']: # 'GEM','GEPS'
            try:
                mini_cols = ['forecast_time', 'validation_day','Value']
                df = pd.read_csv(get_file_path(model), parse_dates=['forecast_time','validation_day'], error_bad_lines=False)
                last_model = _last_available_model(utc_now_dt-td(hours=takeback_hours),model)
                previous_24h_model = _last_available_model(utc_now_dt-td(hours=hours_back),model)
                previous_24h_hour_str = 'Prev(-24h)_%s'%previous_24h_model.hour

                df = df[(df['weight'] == weight)&(df['feature_type'] == FEATURE)]
                df = df[df['forecast_time'] >= previous_24h_model]

                df_last = df[df['forecast_time'] == last_model][mini_cols]
                df_previous24 = df[df['forecast_time'] == previous_24h_model][mini_cols]
                df_new = df_last.merge(df_previous24, on=['validation_day'],suffixes=('','_%sZ'% previous_24h_hour_str),
                                          how='outer')
                if CFS_WEEKLY:
                    weekly_analysis(df,model,ft,weight)
                    continue
                if FILL_NANS_METHOD:
                    df_new = df_new.sort_values('validation_day')
                    df_new = fill_nans(df_new,FILL_NANS_METHOD)

                df_new = df_new.rename(columns={'Value': 'Value_%sZ'% last_model.hour if 'Value_%sZ'% last_model.hour not in list(df_new) else 'Value_%sZ(2)'% last_model.hour})
                final_cols = ['validation_day'] + [l for l in list(df_new) if 'Value' in l]
                final_cols_only_last = ['validation_day'] + [l for l in list(df_new) if 'Value_%s'%last_model.hour in l]
                df_new = df_new[final_cols]

                try:
                    if PLOT:
                        df_new.plot(x='validation_day',kind='line', title='Model: %s'%model)
                        plt.tight_layout()
                except:
                    aa= 1
                if VAL_OR_DIFF != 'Value':
                    df_new['Diff_%sZ-%sZ' % (last_model.hour, previous_24h_hour_str)] = df_new['Value_%sZ' % last_model.hour] - df_new['Value_%sZ' % previous_24h_hour_str]
                    df_new['Diff_%sZ-%sZ_cumsum' %(last_model.hour, previous_24h_hour_str)] = df_new['Diff_%sZ-%sZ' % (last_model.hour, previous_24h_hour_str)].cumsum()

                # handle multi model charts
                cols_for_multimodel = ['validation_day'] + [l for l in list(df_new) if multimodel_cols_cond(l, last_model, VAL_OR_DIFF)]
                df_to_merge = df_new[cols_for_multimodel]
                df_to_merge = df_to_merge.rename(columns={c : '%s_%s' % (model,c) for c in list(df_to_merge) if 'Value' in c or 'Diff' in c})
                if weight in ['basic_NG_weight','population_US_weight']:
                    if multi_models_df.shape[0] == 0:
                        multi_models_df = df_to_merge
                    else:
                        multi_models_df = multi_models_df.merge(df_to_merge,on=[l for l in list(df_to_merge) if 'Value' not in l and 'Diff' not in l],
                                                                how='outer')
                if VAL_OR_DIFF != 'Value':
                    # plot normal by model
                    if PLOT:
                        df_new[['validation_day']+[l for l in list(df_new) if 'cumsum' in l]].plot(x='validation_day',title='Model: %s'%model)
            except:
                aa = 1
                print(model)
                #raise
        plt.show()

    if multi_models_df.shape[0] > 0:
        multi_models_df = multi_models_df.drop_duplicates(subset=[c for c in list(multi_models_df) if 'GFS' in c or 'PARA' in c or 'CFS' in c])
        if UNITE_VALS and VAL_OR_DIFF == 'Value':
            multi_models_df = normalize_values(multi_models_df)

        multi_models_df = multi_models_df.sort_values('validation_day')

        if NORMALIZE_BY_ACCURACY and VAL_OR_DIFF == 'Diff':
            multi_models_df = normalize_by_accuracy(multi_models_df,ACCURACY_CSV)

        if PLOT:
            multi_models_df.plot(x='validation_day')
        multi_models_df2 = multi_models_df.copy()

        multi_models_df2[[l for l in list(multi_models_df2) if l != 'validation_day']] = multi_models_df2[[l for l in list(multi_models_df2) if l != 'validation_day']].cumsum()
        if PLOT:
            multi_models_df2.plot(x='validation_day', title="Cummulative Graph")
            plt.show()

        multi_models_df_mean = multi_models_df.set_index('validation_day').mean(axis=1)
        multi_models_df_cumsum = multi_models_df_mean.cumsum()
        txt = "%s:\n"% ("Absolute Values of last Model (%s)"% last_model if VAL_OR_DIFF == "Value" else "Diff %s ---> %s" % (last_model, previous_24h_model))
        txt+= "Reaching date: %s\n----------------------------\n" % multi_models_df_mean.index.tolist()[-1]
        txt += "Daily Values:\n %s\n" % multi_models_df_mean.apply(lambda x: round(x,2)).tolist()
        txt += "Daily Cumsum:\n %s\n" % multi_models_df_cumsum.apply(lambda x: round(x, 2)).tolist()
        print('--------------  Model: MultiModels %s -----------------\nMultiModel Analysis for Feature: %s | Weight: %s:\n%s'%(models,ft,weight,txt))
        if PLOT:
            multi_models_df_mean.plot(title='Average of all models',kind='bar')
            plt.tight_layout()
            plt.show()
            multi_models_df_cumsum.plot()
            plt.tight_layout()
            plt.show()
