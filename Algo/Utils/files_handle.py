import sys
import os
from pathlib import Path

DEVICE_NAME = 'P14s'
# PYTHON_INTERPRETER = r"C:\Users\<USER>\Miniconda3\python.exe"
# PYTHON_INTERPRETER = r"C:\Users\<USER>\venvs\Algo_env\Scripts\python.exe" # X1 Carbon
PYTHON_INTERPRETER = r"C:\Users\<USER>\miniconda311\python.exe"

WINDOWS_HOME_DIR = r"C:\Users\<USER>\Documents\Work\Amazon"
MAC_HOME_DIR = '/Users/<USER>/Private/Amazon'

platform = {'darwin': 'OS X',
        'win32': 'Windows'
    }.get(sys.platform,'Windows')

if platform == 'Windows':
    HOME_DIR = WINDOWS_HOME_DIR
else:
    HOME_DIR = MAC_HOME_DIR
HOME = HOME_DIR


COMPACT_FILTERING_DIR = os.path.join(HOME,'performance_analysis','compact_filtering_method')
PROJECT_ROOT_WINDWOS = r"C:\Users\<USER>\PycharmProjects\TWS_API"
PROJECT_ROOT = PROJECT_ROOT_WINDWOS

DOWNLOADS_DIR_WINDOWS = r"C:\Users\<USER>\Downloads"
DOWNLOADS_DIR_MAC = ""
DOWNLOADS_DIR = DOWNLOADS_DIR_WINDOWS

CHARTS_DIRECTORY = os.path.join(HOME, 'Charts')

# for the MA_1H_240 strategies
BOLLINGER_PREDICTIONS_CSV = os.path.join(HOME, 'Trading','bollinger_predictions.csv')

ACTUAL_TRADES_CSV = os.path.join(HOME, 'Trading','actual_positions.csv')
ACTUAL_TRADES_NG_CSV = os.path.join(HOME, 'Trading','actual_positions_NG.csv')
NASDAQ_ACTUAL_POSITIONS_CSV = os.path.join(HOME, 'Trading','actual_positions_NQ.csv')
ORDER_IDS_CSV = os.path.join(HOME, 'Trading','orders_ids.csv')
# for new assets we create a directory
ACTUAL_POSITIONS_DIR = os.path.join(HOME,'Trading','Actual_Positions')

LOOK_FOR_NEW_STRATS_POSITIONS_FILE_PATH = os.path.join(HOME,'Trading','look_for_new_strategies_positions_NG_v4.csv')
LOOK_FOR_NEW_STRATS_NO_SEGMENTERS_POSITIONS_FILE_PATH = os.path.join(HOME,'Trading','look_for_new_strategies_positions_NG_v4_no_segmenters.csv')

IBRIDGEPY_TRADER_OBJECT_PICKLE = os.path.join(HOME, 'Config','ibridgepy_trader.pkl')

XDATA_DIR_NAME = os.path.join(HOME,'Xdata')

MAIN_XY_CSV = os.path.join(HOME,'XYs','Enriched_XYs','XY_a_GDD_v8_0Zb.csv')
MAIN_HDD_XY_CSV = os.path.join(HOME,'XYs','Enriched_XYs','XY_a_HDD_v8_0Zb.csv')
MAIN_CDD_XY_CSV = os.path.join(HOME,'XYs','Enriched_XYs','XY_a_CDD_v8_0Zb.csv')

WRAPPERS_LOCK_YAML = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'wrappers_lock.yaml')

MARKET_DATA_DIRECTORIES_BY_ASSET = {
                                    'NG': os.path.join(HOME,'Market_Data'),
                                    'QG': os.path.join(HOME,'Market_Data'),
                                    'COFFEE': os.path.join(HOME,'COFFEE','Market_Data'),
                                    'CORN': os.path.join(HOME,'CORN','Market_Data'),
                                    'NCF': os.path.join(HOME,'Market_Data_General','Coal'),
                                    'NGF': os.path.join(HOME,'Market_Data_General','NGF'),
                                    'WTI': os.path.join(HOME,'Market_Data_General','WTI'),
                                    'NQ': os.path.join(HOME,'Market_Data_General','Nasdaq'),
                                    'BRR': os.path.join(HOME,'Market_Data_General','Bitcoin'),
                                    'MBT': os.path.join(HOME,'Market_Data_General','BitcoinMini'),
                                    'GC': os.path.join(HOME,'Market_Data_General','Gold'),
                                    'ES': os.path.join(HOME,'Market_Data_General','SP500'),
                                    }

TELECONNECTIONS_RAW_DATA_CSV = os.path.join(HOME,'degdays_archive','teleconnections','teleconnections_from_weathermodels.csv')
TELECONNECTIONS_XS_CSV = os.path.join(HOME,'degdays_archive','teleconnections','teleconnections_diffs_Xs.csv')

def get_degdays_path(model,subdir='live',asset='NG'):
    suffix = ''
    if model in ['EPS2','EPSCO','EC2']:
        suffix = '_grib'
        model_for_path = model.replace('2','')
    elif model == 'GEPS':
        model_for_path = model
        suffix = '_Pmean'
    else:
        model_for_path = model
    if asset == 'NG':
        if subdir == 'live':
            file_path = os.path.join(HOME,'degdays_archive','Live','Live_from_ec2','live_degdays_%s%s.csv'%(model_for_path,suffix))
        elif subdir == 'full':
            file_path = os.path.join(HOME, 'degdays_archive', 'Full_Files','Last3M','Full_degdays_Full_%s.csv' % (model))
        else: # monthly dir
            file_path = os.path.join(HOME, 'degdays_archive', 'Live', subdir,
                                     'live_degdays_%s%s.csv'%(model_for_path,suffix))
    elif asset == 'coffee':
        if subdir == 'live':
            file_path = os.path.join(HOME,'COFFEE','degdays_archive','Live','Live_from_ec2','live_degdays_%s%s.csv'%(model_for_path,suffix))
        elif subdir == 'full':
            file_path = os.path.join(HOME,'COFFEE', 'degdays_archive', 'Full_Files','Last3M','Full_degdays_Full_%s.csv' % (model))
    elif asset == 'wind':
        if subdir == 'live':
            file_path = os.path.join(HOME,'degdays_archive','Live','Live_Wind','live_wind_%s%s.csv'%(model_for_path,suffix))
        elif subdir == 'full':
            file_path = os.path.join(HOME, 'degdays_archive', 'Live', 'Live_Wind','live_wind_%s%s.csv' % (model_for_path, suffix))
    return file_path

def get_wind_degdays_path(model,subdir='live'):
    """
    :param model:
    :param subdir: live / monthYY (e.g. Jul21)
    :return:
    """
    # for year in range(2020,2040):
    #     for month in range(1,13)
    return os.path.join(HOME,'degdays_archive','Live','Live_Wind' if subdir == 'live' else 'Wind_'+subdir,'live_wind_%s.csv'%model)

def get_daily_predictions_path(suffix=''):
    """
    :param suffix: ('_All', '', '_1W')
    :return:
    """
    return os.path.join(HOME,'Trading','daily_predictions%s.csv'%suffix)


def get_x_diffs_path(model,suffix='v8_0Zb'):
    if suffix in ['v8_0Zb','v8_12Z']:
        return os.path.join(HOME,'Xdata','X_file_DailyDiffs_%s_%s.csv'%(model,suffix))
    elif suffix in ['v2.0_0Z','v3.0_0Z']:
        return os.path.join(HOME, 'Xdata', 'X_file_DailyDiffs_%s_%s.csv' % (model, suffix))


def get_candles_path(asset='NG'):
    if asset == 'NG':
        return os.path.join(HOME,'Market_Data','NG_2018-19_frontMonth_tz_Live.csv')
    else:
        raise KeyError('Asset %s not supported'%asset)


def get_clusters_json_path():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf', 'clusters_division.json')
    return json_path

def get_compact_filter_json_path():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf','compact_filter.json')
    return json_path

def get_ALL_PREDS_json_path():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf', 'ALL_PREDS_list.json')
    return json_path

def get_chosen_strats_json():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf', 'chosen_strats.json')
    return json_path

def get_dynamic_clusters_json():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf', 'dynamic_chosen_clusters.json')
    return json_path
def get_5min_filteroing_json():
    json_path = os.path.join(Path(__file__).parent.absolute().parent, 'Conf', 'dynamic_5min_filter.json')
    return json_path

def get_yaml_config_path():
    return os.path.join(PROJECT_ROOT,'Algo', 'Trading', 'trading_config.yaml')


""" WEATHER MODELS CLUSTERS PATHS  """

def get_forecast_clusters_features_path(max_hours_back,present_to_past_ratio,models_version='v1'):
    return os.path.join(HOME,'degdays_archive','Clusters',f'Cluster_models_Diffs_MaxHours={max_hours_back}_Ratio={float(present_to_past_ratio)}{"" if models_version == "v1" else "_"+models_version}.csv')

def get_forecast_clusters_raw_file_path(max_hours_back,present_to_past_ratio,models_version='v1'):
    if models_version == 'v1':
        last_part = "models=dict_keys(['PARA', 'PARACO', 'GEMCO', 'CFSCO', 'CFS', 'GEFS', 'EPS', 'GEPS'])"
    else:
        last_part = f"models={models_version}"
    path = os.path.join(HOME,'degdays_archive','Clusters',f"Cluster_models_MaxHours={max_hours_back}_RatioFL={present_to_past_ratio}_{last_part}.csv")
    return path


def get_clusters_performance_csv_by_mode(mode,days=365):
    return r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\Real_Trades\Clusters_Performance_mode=%s_weekdays=[0, 1, 2, 3, 4]_Alldays=%s_hours=[0,20].csv"%(mode,days)


def get_past_positions_features_csv():
    return os.path.join(HOME,'General_Stocks','past_positions_features.csv')

def get_regression_predictions_enrichment_features_csv(weekdays_for_train,suffix='_start=2021_100D'):
    train_days_suffix = f'_TrainDays={weekdays_for_train}' if set(weekdays_for_train) < {0,1,2,3,4} else ''
    return os.path.join(HOME,'General_Stocks',f'daily_regression_features{train_days_suffix}{suffix}.csv')


def get_delta_ys_full_df_path(asset_name):
    market_data_dir = MARKET_DATA_DIRECTORIES_BY_ASSET[asset_name]
    sub_dir = os.path.join(market_data_dir,'Delta_Ys_Full')
    try:
        if not os.path.exists(sub_dir):
            os.mkdir(sub_dir)
    except:
        pass
    return os.path.join(market_data_dir,'Delta_Ys_Full','delta_ys_full.csv')


def get_candles_outpath(asset_name,add_tzs=False):
    market_data_dir = MARKET_DATA_DIRECTORIES_BY_ASSET[asset_name]
    if asset_name in ['QG','NG','MHNG']:
        candles_outpath = os.path.join(market_data_dir,'NG_2018-19_frontMonth_%sLive.csv' % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'CORN':
        candles_outpath = os.path.join(market_data_dir,"CORN_2018-19_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'COFFEE':
        candles_outpath = os.path.join(market_data_dir, "COFFEE_2018-19_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'NCF':
        candles_outpath = os.path.join(market_data_dir,"COAL_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'NQ':
        candles_outpath = os.path.join(market_data_dir,"NQ_frontMonth_%sLive_15mins.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'ES':
        candles_outpath = os.path.join(market_data_dir,"ES_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'BRR':
        candles_outpath = os.path.join(market_data_dir,"BTC_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'MBT':
        candles_outpath = os.path.join(market_data_dir,"MBT_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    elif asset_name == 'GC':
        candles_outpath = os.path.join(market_data_dir,"GC_frontMonth_%sLive.csv" % (
            "" if not add_tzs else "tz_"))
    else:
        raise AssertionError('Invalid asset')
    return candles_outpath


##### UTILS #####

import os

def rename_files_in_dir(directory_path,substring_to_drop,replacement=''):

    # loop through all files in the directory
    for filename in os.listdir(directory_path):
        # check if the file contains the substring to drop
        if substring_to_drop in filename:
            # generate the new filename by dropping the substring
            new_filename = filename.replace(substring_to_drop, replacement)
            # rename the file
            os.rename(os.path.join(directory_path, filename), os.path.join(directory_path, new_filename))
            print(f'Renamed {filename} to {new_filename}')
