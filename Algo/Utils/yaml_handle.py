import yaml
import os

from Algo.Utils.files_handle import HOME, PROJECT_ROOT, get_yaml_config_path

set_real_mode_cmd = f'python {PROJECT_ROOT}/Algo/Utils/yaml_handle.py --function set_real_mode'
set_paper_mode_cmd = f'python {PROJECT_ROOT}/Algo/Utils/yaml_handle.py --function set_paper_mode'


def load_trading_conf_yaml():
    # Open the YAML file
    with open(get_yaml_config_path(), 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    return config

def load_general_yaml(path):
    # Open the YAML file
    with open(path, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    return config

def dump_general_yaml(path,config):
    with open(path, 'w') as f:
        config = yaml.dump(config,f)
    return config

def update_general_yaml(path,update_dict):
    config = load_general_yaml(path)
    try:
        config.update(update_dict)
        return dump_general_yaml(path,config)
    except:
        print('Error, failed to update general yaml')
        # return config


def dump_trading_conf_to_yaml(selected_mode):
    # Open the YAML file
    with open(get_yaml_config_path(), 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    config['trading_mode'] = selected_mode
    with open(get_yaml_config_path(), 'w') as f:
        config = yaml.dump(config,f)
    return config


def set_paper_mode():
    dump_trading_conf_to_yaml('paper')
def set_real_mode():
    try:
        dump_trading_conf_to_yaml('real')
    except:
        print('Error, failed to set real mode')


if __name__ == '__main__':
    # dump_trading_conf_to_yaml('paper')
    # print(load_trading_conf_yaml())

    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--function', type=str)
    eval(parser.parse_args().function+'()')