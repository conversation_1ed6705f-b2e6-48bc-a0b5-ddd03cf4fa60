import smtplib
from datetime import datetime as dtdt
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
import smtplib
import subprocess
from os.path import basename
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email import encoders
import os

from Algo.Utils.files_handle import HOME, CHARTS_DIRECTORY, DEVICE_NAME

gmail_user = '<EMAIL>'
gmail_password = 'SendGDD@2022'
gmail_password = 'SendGDD@2022'
gmail_password = 'gtahrbclytbznawz' # app password 08/06/22


CHART_FILES_TO_SEND = [os.path.join(HOME,'Charts','Avg_Forecasts.pdf'),
                      os.path.join(HOME,'Charts','Forecast_By_Model.pdf'),
                       os.path.join(HOME,'Charts','clusters_charts_days=1-15_0Z.png'),
                       os.path.join(HOME,'Charts','clusters_charts_days=1-15_6Z.png'),
                       os.path.join(HOME,'Charts','clusters_charts_days=1-15_12Z.png')
                       ]

NOTEBOOKS_DIR = os.path.join(CHARTS_DIRECTORY, 'Notebooks')

def send_email_main(subject, body, sent_from=gmail_user, to=[gmail_user],
                    add_device_signature=True):
    msg = MIMEMultipart()
    msg['From'] = sent_from
    msg['To'] = ",".join(to)
    msg['Subject'] = subject

    if add_device_signature:
        body += '\n Device: %s' % DEVICE_NAME

    msg.attach(MIMEText(body, 'plain'))
    try:
        server = smtplib.SMTP_SSL('smtp.gmail.com', 465)
        server.ehlo()
        server.login(gmail_user, gmail_password)
        server.sendmail(sent_from, to, msg.as_string())
        ### old method
        # server.sendmail(sent_from, to, email_text)
        server.close()
        print('Email sent!')
    except Exception as e:
        print('couldnt send email due to Error: ', e)



def send_email_with_attachments(subject, body, sent_from=gmail_user, to=[gmail_user],
                                files=CHART_FILES_TO_SEND,
                                add_device_signature=True):
    msg = MIMEMultipart()
    msg['From'] = sent_from
    msg['To'] = ",".join(to)
    msg['Subject'] = subject
    if add_device_signature:
        body += '\n Device: %s' % DEVICE_NAME

    msg.attach(MIMEText(body, 'plain'))

    for f in files or []:
        if os.path.exists(f):
            with open(f, "rb") as fil:
                part = MIMEApplication(
                    fil.read(),
                    Name=basename(f)
                )
            # After the file is closed
            part['Content-Disposition'] = 'attachment; filename="%s"' % basename(f)
            msg.attach(part)

    try:
        server = smtplib.SMTP_SSL('smtp.gmail.com', 465)
        server.ehlo()
        server.login(gmail_user, gmail_password)
        server.sendmail(sent_from, to, msg.as_string())
        ### old method
        # server.sendmail(sent_from, to, email_text)
        server.close()
        print('Email sent!')
    except:
        print('Something went wrong...')



def send_update(model, model_time, ft, diffs_dic, normalization_method=0):
    normalization_str = "" if not normalization_method else "| Normalized 3day"
    subject = '%s Update | Model: %s | %s %s' % (ft, model, model_time.strftime("%d/%m/%Y %Hz"), normalization_str)
    body_parts = ['Model: %s' % model, 'Last_model: %s' % model_time.strftime("%d/%m/%Y %Hz")] + \
                 ['====================>>>>>>>>>>'] + \
                 ['%s%s \n -------------\n' % (k, diffs_dic[k]) for k in diffs_dic.keys()]
    body = "\n".join(body_parts)
    body = body.replace(": [", ": \n [")

    # new method
    send_email_main(subject, body)


def _convert_ipynb_to_html(ipynb_file, html_file,execute_notebook=True):
    command_lst = ["jupyter", "nbconvert", "--to", "html", ipynb_file, "--output", html_file]+(["--no-input", "--execute"] if execute_notebook else [])
    print('Calling command: ', ' '.join(command_lst), '...')
    subprocess.run(command_lst, check=True)
    # subprocess.run(["jupyter", "nbconvert", "--to", "html",  ipynb_file, "--output", html_file])


def send_notebooks_by_email(ipynb_files,html_outdir=NOTEBOOKS_DIR,execute_notebook=False,subject='Notebooks htmls Charts'):
    files_to_send = []
    for ipynb_file in ipynb_files:
        ipynb_file_name = os.path.basename(ipynb_file)
        html_output = os.path.join(html_outdir,ipynb_file_name.replace('.ipynb','.html'))
        _convert_ipynb_to_html(ipynb_file, html_output,execute_notebook=execute_notebook)
        files_to_send.append(html_output)

    send_email_with_attachments(subject,'Enjoy!',files=files_to_send)

def wrap_notebooks_daily_mail(execute_notebook=True):
    send_notebooks_by_email(
        [r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\Performance_Analysis\check_drop_losing_hours_v4.ipynb",
         r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\Performance_Analysis\cluster_auto_selection_charts.ipynb"
         ], execute_notebook=execute_notebook)

def wrap_degdays_report_mail(execute_notebook=True):
    from Algo.Utils.general import get_utc_now_dt
    from Algo.Viasualization.visualize_live_degdays import _last_available_model, get_file_path
    utc_now = get_utc_now_dt()
    last_run = _last_available_model(utc_now, 'EPS')
    last_run_str = last_run.strftime('%Y-%m-%d %H')+'Z'
    send_notebooks_by_email(
        [r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\degdays_visualizations\degdays_daily_report.ipynb"], execute_notebook=execute_notebook,
                    subject=f'Degdays Daily Report {last_run_str}')

def wrap_look_for_new_strats_report_mail(execute_notebook=True):
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour')
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_no_segmenters')
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_binEndDates')
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_hybrid')


def wrap_nasdaq_correlations_notebook_mail_heavy(execute_notebook=True):
    send_notebooks_by_email(
        [r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Stocks_Research\Nasdaq\analyze_nasdaq_hours_correlation_performance_production.ipynb"
         ], execute_notebook=execute_notebook,subject='Nasdaq Correlations Predictions')
def wrap_nasdaq_correlations_notebook_mail():
    html_outdir = NOTEBOOKS_DIR
    ipynb_file = r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Stocks_Research\Nasdaq\analyze_nasdaq_hours_correlation_performance_production.ipynb"
    ipynb_file_name = "analyze_nasdaq_hours_correlation_performance_production.ipynb"
    html_output = os.path.join(html_outdir,ipynb_file_name.replace('.ipynb','.html'))
    _convert_ipynb_to_html(ipynb_file, html_output,execute_notebook=True)
    files_to_send = [r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Stocks_Research\Nasdaq\charts\historical_performance.png",
                     r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Stocks_Research\Nasdaq\charts\positions_today.png"]
    current_datetime_utc = dtdt.utcnow().strftime('%Y-%m-%d %H:%M')
    current_weekday = dtdt.utcnow().weekday()
    send_email_with_attachments(f'Nasdaq Correlation Positions Charts \\ Weekday = {current_weekday} | {current_datetime_utc}', 'Enjoy!', files=files_to_send)

def wrap_look_for_new_strategies_notebook_mail(which_notebook='todays_positions'):

    html_outdir = NOTEBOOKS_DIR
    directory = 'production' if 'todays' in which_notebook else 'analysis'
    ipynb_file = fr"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\new_strategies_finder\{directory}\{which_notebook}.ipynb"
    ipynb_file_name = f"{which_notebook}.ipynb"
    if not os.path.exists(ipynb_file):
        print(f'File {ipynb_file} does not exist')
        return
    html_output = os.path.join(html_outdir,ipynb_file_name.replace('.ipynb','.html'))
    _convert_ipynb_to_html(ipynb_file, html_output,execute_notebook=True)
    current_datetime_utc = dtdt.utcnow().strftime('%Y-%m-%d %H:%M')
    current_weekday = dtdt.utcnow().weekday()
    files_to_send = [html_output]

    if which_notebook == 'todays_positions':
        subject = f'New Strategies Predictions | Weekday = {current_weekday} | {current_datetime_utc}'
    elif which_notebook == 'analyze_performance_by_hour':
        subject = f'New Strategies Performance | ALL WEEK'
    elif which_notebook == 'analyze_performance_by_hour_binEndDates':
        subject = f'New Strategies Performance | ALL WEEK - Dynamic Bin EndDates'
    elif which_notebook == 'analyze_performance_by_hour_no_segmenters':
        subject = f'New Strategies Performance | ALL WEEK | NO SEGMENTERS'
    elif which_notebook == 'analyze_performance_by_hour_semi234':
        subject = f'New Strategies Performance | SEMI234'
    elif which_notebook == 'analyze_performance_by_hour_semi234_no_segmenters':
        subject = f'New Strategies Performance | SEMI234 | NO SEGMENTERS'
    elif which_notebook == 'analyze_performance_by_hour_hybrid':
        subject = f'New Strategies Performance | HYBRID'
    else:
        raise ValueError(f'Invalid which_notebook: {which_notebook}')
    send_email_with_attachments(subject,'Enjoy!',
                                files=files_to_send
                                )

def run_look_for_new_strategies_todays_df():
    wrap_look_for_new_strategies_notebook_mail('todays_positions')

def run_look_for_new_strategies_analysis_by_hour():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour')

def run_look_for_new_strategies_analysis_by_hour_bin_end_dates():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_binEndDates')

def run_look_for_new_strategies_analysis_by_hour_no_segmenters():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_no_segmenters')

def run_look_for_new_strategies_analysis_by_hour_semi234():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_semi234')

def run_look_for_new_strategies_analysis_by_hour_semi234_no_segmenters():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_semi234_no_segmenters')

def run_look_for_new_strategies_analysis_by_hour_hybrid():
    wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_hybrid')

def wrap_ng_rolling_predictions_notebook():
    html_outdir = NOTEBOOKS_DIR
    ipynb_file = r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\new_strategies_finder\look_for_new_strategies_daily_preds.ipynb"
    ipynb_file_name = "look_for_new_strategies_daily_preds.ipynb"
    html_output = os.path.join(html_outdir,ipynb_file_name.replace('.ipynb','.html'))
    _convert_ipynb_to_html(ipynb_file, html_output,execute_notebook=True)
    current_datetime_utc = dtdt.utcnow().strftime('%Y-%m-%d %H:%M')
    current_weekday = dtdt.utcnow().weekday()
    files_to_send = [html_output]
    send_email_with_attachments(f'NG Rolling Predictors SUMMARY | Weekday = {current_weekday} | {current_datetime_utc}', 'Enjoy!',
                                files=files_to_send
                                )

if __name__ == '__main__':
    # wrap_nasdaq_correlations_notebook_mail()
    # run_look_for_new_strategies_todays_df()
    # run_look_for_new_strategies_analysis_by_hour_hybrid()
    # run_look_for_new_strategies_analysis_by_hour_hybrid()
    # raise

    # wrap_ng_rolling_predictions_notebook()
    # wrap_look_for_new_strategies_notebook_mail()
    # wrap_degdays_report_mail()
    # wrap_notebooks_daily_mail()
    # raise
    # run_look_for_new_strategies_analysis_by_hour_no_segments()
    # run_look_for_new_strategies_analysis_by_hour()
    # wrap_look_for_new_strats_report_mail()
    # raise
    # wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour')
    # wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_no_segmenters')
    # wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_semi234')
    # wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_semi234_no_segmenters')
    # wrap_look_for_new_strategies_notebook_mail('analyze_performance_by_hour_hybrid')
    # raise
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--specific_function', type=str, default='wrap_notebooks_daily_mail')
    args = parser.parse_args()

    if args.specific_function is not None:
        eval(args.specific_function+'()')
    # wrap_notebooks_daily_mail(execute_notebook=True)
