from Algo.Utils.files_handle import get_chosen_strats_json, get_dynamic_clusters_json
from Algo.Utils.json_utils import MyEncoder as MyJsonEncoder
from Algo.Utils.general import deep_update, convert_dict_keys_to_string
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Trading.constants import wrap_all_preds_update
from Algo.Wrapper import wrap_5min_candles
from Algo.Utils.yaml_handle import set_paper_mode, set_real_mode
from Algo.Notebooks.new_strategies_finder.production import wrap_winners_choice
from Algo.Xbox import retrieve_from_ec2
import json
import os

from Algo.Utils.files_handle import HOME
import pandas as pd
try:
    from Algo.Wrapper.outer_full_wrapper import main as main_outer_wrapper
    from Algo.Learning.algo_wrapper2 import main_wrapper as main_algo_wrapper
    from Algo.Learning import research_plays
    from Algo.Learning import strategies_selector
    from Algo.Learning import decision_maker
    from Algo.Learning.research_plays import compact_performance_summary
    from Algo.Learning.compact_filtering import main as main_compact_filtering
    from Algo.Viasualization.analysis_backfill import backfill_cluster_positions
    from Algo.Wrapper.cluster_auto_selection_utils import wrap_clusters_auto_selection, _get_last_sunday
except:
    print('Failed to import inside weekend_preparation_wrapper')
    raise

STRATEGIES_BACKTEST_START_DATE = dtdt(2021,7,1)

def write_decision_maker_to_archive(sunday_date):
    pass


def wrap_compact_filtering():
    production_mode = True
    add_date_to_filename = False
    performance_csv_start = dtdt(2021, 7, 1)
    compact_performance_summary('v8_12Z', 'GDD', performance_csv_start, dtdt(2050, 1, 1),
                                '', lambda x: '' in x,
                                weekdays=[0, 1, 2, 3, 4],
                                strings_to_include=['', ''],
                                strings_to_exclude=[],
                                plot_individuals=True,
                                allow_or_in_condition=False, calculate_calmars=True,
                                separate_weekdays=False,
                                add_date_to_filename=add_date_to_filename,
                                production_mode=production_mode)
    print('\t starting main_compact_filtering')
    main_compact_filtering(suffix='' if not add_date_to_filename else None,  # taking the
                           window=40,
                           )

def wrap_chosen_strats_to_json(fts=['GDD', 'CDD', 'HDD'],print_info=False):
    last_sunday = _get_last_sunday()

    all_confs_dict = {ft: {} for ft in fts}
    for ft in fts:
        hybrid_confs = strategies_selector.get_chosen_strats_by_confs(ft, 'hybrid', print_info=print_info)
        all_confs_dict[ft]['hybrid'] = hybrid_confs
        if ft == 'GDD':
            p20_confs = strategies_selector.get_chosen_strats_by_confs(ft, 'hybridP20', print_info=print_info)
            strict_confs = strategies_selector.get_chosen_strats_by_confs(ft, 'strict', print_info=print_info)
            all_confs_dict[ft]['strict'] = strict_confs
            all_confs_dict[ft]['hybridP20'] = p20_confs
    try:
        wrap_compact_filtering()
    except Exception as e:
        print(f'Failed to run compact_performance_summary {e}')

    if os.path.exists(get_chosen_strats_json()):
        with open(get_chosen_strats_json()) as f:
            final_dict = json.load(f)
    else:
        final_dict = {}

    # update nested dict
    print('before update: %s'%final_dict.keys())
    final_dict = deep_update(final_dict,{last_sunday: all_confs_dict})
    print('after update: %s' % final_dict.keys())
    # write to JSON
    with open(get_chosen_strats_json(), "w") as f:
        json.dump(final_dict, f,indent=True)

def clean_look_for_new_strats_archive():
    directory = os.path.join(HOME,'Trading','look_for_new_strats_archive')
    for filename in os.listdir(directory):
        if filename.endswith('.csv'):
            date_str = filename.split('NG_')[1].split('_')[0]
            date = dtdt.strptime(date_str, '%Y%m%d')
            if date < dtdt.now() - td(days=10):
                file_path = os.path.join(directory, filename)
                os.remove(file_path)


def wrap_backtest_analysis(fts=['GDD', 'HDD', 'CDD'],skip_xy=False,skip_algo_wrapper=False,
                           bring_5min_candles=False,skip_degdays_retrieval=True,
                           start_from_strat_backtest=False):
    if not start_from_strat_backtest:
        if not skip_degdays_retrieval:
            retrieve_from_ec2.main('full')
        set_real_mode()
        if not skip_xy:
            main_outer_wrapper(mode='xy',skip_xs=False,models_group_override='full',trade=False,suff_override='v8_0Zb')
        if bring_5min_candles:
            wrap_5min_candles.main()
        set_paper_mode()
        for ft in fts:
            try:
                for suffix in ['v8_0Zb','v8_12Z']:
                    if not skip_algo_wrapper:
                        try:
                            a = main_algo_wrapper(conf_name='test', last_model=12, strategy='0800-1100',
                                calc_As=1, print_predies=False, suffix=suffix, ft=ft,
                                 send_email=False,start=dtdt(2021,4,1),end=dtdt(2030,1,1),
                                     big_model_hour=0 if '12Z' in suffix else None,
                                     asset='NG',
                                     base_on_existing=True,calc_start_override=None)
                        except KeyError as e:
                            a = main_algo_wrapper(conf_name='test', last_model=12, strategy='0800-1100',
                                                  calc_As=1, print_predies=False, suffix=suffix, ft=ft,
                                                  send_email=False, start=dtdt(2021, 4, 1), end=dtdt(2030, 1, 1),
                                                  big_model_hour=0 if '12Z' in suffix else None,
                                                  asset='NG',
                                                  base_on_existing=False, calc_start_override=None)

            except:
                print(f'Failed to run Algo_Wrapper on Feature={ft} and Suffix={suffix}')
            ### todo - make it base on existing!!!!
    for ft in fts:
        research_plays.performance_sumary('v8_12Z', ft, cond_on_strats=lambda x:True,
                                          weekdays=[0,1,2,3,4],
                                            calc_profits=True,cond_str='', run_plots=False,
                                            days_replacement_dict={},
                                            additioanl_csv_suffix='',
                                            start=STRATEGIES_BACKTEST_START_DATE,end=dtdt(2050,3,31))

def wrap_weekend(fts,skip_xy=False,skip_algo_wrapper=False,bring_5min_candles=False,
                 skip_degdays_retrieval=False,
                 start_from_strat_backtest=False):

    wrap_backtest_analysis(fts,skip_xy,skip_algo_wrapper,bring_5min_candles,
                           skip_degdays_retrieval=skip_degdays_retrieval,
                           start_from_strat_backtest=start_from_strat_backtest)
    wrap_chosen_strats_to_json(fts)
    if 'GDD' in fts:
        # make sure clusters are up-to-date
        wrap_all_preds_update()
        strategies_selector.calc_chosen_strats_performance()
        wrap_clusters_auto_selection(clip=2)

    clean_look_for_new_strats_archive()
    clean_look_for_new_strats_pred_history()

def short_wrap_weekend(fts=['GDD'],skip_strats_to_json=False):
    if not skip_strats_to_json:
        wrap_chosen_strats_to_json(fts)
    if 'GDD' in fts:
        # make sure clusters are up-to-date
        # wrap_all_preds_update()
        strategies_selector.calc_chosen_strats_performance()
        wrap_clusters_auto_selection(clip=2)


def manually_update_chosen_strats_conf(conf='hybridP20',ft='GDD',last_sunday_str='20220710'):
    with open(get_chosen_strats_json()) as f:
        final_dict = json.load(f)
    p20_confs = strategies_selector.get_chosen_strats_by_confs(ft,conf, print_info=True)
    all_confs_dict = {ft:{}}
    all_confs_dict[ft][conf] = p20_confs

    # update nested dict
    print('before update: %s'%final_dict.keys())
    final_dict = deep_update(final_dict,{last_sunday_str: all_confs_dict})
    print('after update: %s' % final_dict.keys())
    # write to JSON
    with open(get_chosen_strats_json(), "w") as f:
        json.dump(final_dict, f,indent=True)

def clean_look_for_new_strats_pred_history(path=r'C:\Users\<USER>\Documents\Work\Amazon\performance_analysis'):

    for subdir in os.listdir(path):
        for sub_subdir in os.listdir(os.path.join(path, subdir)):
            if sub_subdir == 'chosen_preds_history':
                files = os.listdir(os.path.join(path, subdir, sub_subdir))
                for file in files:
                    timestamp = pd.to_datetime(file.split('_')[-1].split('.')[0])
                    if timestamp < dtdt.now() - td(days=14):
                        file_path = os.path.join(path, subdir, sub_subdir, file)
                        os.remove(file_path)



if __name__ == '__main__':
    # wrap_winners_choice.impute_profits_backfill() # fill the "look for new strategies" profits

    ###### PROD Code #######
    wrap_weekend(fts=['GDD'],
                 # skip_xy=True,
                 skip_xy=False,
                 skip_algo_wrapper=False,bring_5min_candles=False,
                 skip_degdays_retrieval=False,
                 start_from_strat_backtest=False)
    print('starting wrap_compact_filtering')
    wrap_compact_filtering()
    ##################################################

    #### from after the backfill of inidividual preds (if Error occured later)
    # wrap_chosen_strats_to_json(fts=['GDD'])
    # make sure clusters are up-to-date
    # wrap_all_preds_update()
    # strategies_selector.calc_chosen_strats_performance()
    # wrap_clusters_auto_selection(clip=2)
    # clean_look_for_new_strats_archive()
    # wrap_compact_filtering()
    # add_date_to_filename = False
    # main_compact_filtering(suffix='' if not add_date_to_filename else None,  # taking the
    #                        window=40,
    #                        )
    # wrap_chosen_strats_to_json(['GDD'])
    # if 'GDD' in fts:
    # make sure clusters are up-to-date
    # wrap_all_preds_update()
    # strategies_selector.calc_chosen_strats_performance()
    # wrap_clusters_auto_selection(clip=2)

    # wrap_compact_filtering()


    # wrap_chosen_strats_to_json(['GDD'])
    # make sure clusters are up-to-date
    # wrap_all_preds_update()
    # strategies_selector.calc_chosen_strats_performance()
    # wrap_clusters_auto_selection(clip=2)

    # wrap_clusters_auto_selection(backfill_mode='None', days_back=3 + 7 + 7)
    # wrap_clusters_auto_selection(backfill_mode='None', days_back=3 + 7)
    # wrap_clusters_auto_selection(backfill_mode='None',days_back=3)
    #

