from subprocess import check_call
import schedule
import time
from datetime import datetime as dtdt
from datetime import time as dt_time
from datetime import timedelta as td
import pytz
from Algo.Utils.files_handle import PROJECT_ROOT



# cmd = f'python {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --second_mode {mode2} --asset {asset}'
cmd = f'python {PROJECT_ROOT}/IBridge/v18/trading_utils.py'

def run_batch_trader():
    check_call(cmd, shell=True)

def set_schedule():
    now = dtdt.now()
    times = [now+td(minutes=2*i+1) for i in range(1, 7)]
    for t in times:
        schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader)

set_schedule()

# check_call(cmd, shell=True)

while True:
    utc_now = dtdt.utcnow()
    try:
        schedule.run_pending()
    except Exception as e:
        print(f'Scheduler faced an Error at {utc_now}: {e}')
        print('Skipping this iteration')

    print(f'utc_now = {utc_now}...going to sleep for 5 minutes')
    if utc_now.minute % 5 != 0:
        time.sleep(30)
    else:
        time.sleep(30)
    # time.sleep(30)