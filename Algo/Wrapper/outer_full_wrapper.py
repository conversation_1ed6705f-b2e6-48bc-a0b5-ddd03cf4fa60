from Algo.Wrapper.wrap_process import main as main_wrap_process
from Algo.Wrapper.wrap_process import wrap_retrieve_candles,wrap_candles_and_uniting
from Algo.Xbox.retrieve_from_ec2 import main as main_get_ec2
from Algo.Xbox import generate_x_data
from Algo.Xbox import other_x_features
from Algo.Xbox import retrieve_from_ec2

from Algo.Utils.files_handle import PYTHON_INTERPRETER,HOME
from Algo.Learning.algo_wrapper2 import main_wrapper as algo_main_wrapper
from Algo.Learning.decision_maker import main as main_decision_maker
from Algo.Learning.decision_maker import main_wind_wrapper,main_coffee_wrapper
from Algo.General.pdf_extraction import main as pdf_extract_main
from Algo.Utils import send_email
from Algo.Data_Retrieval.ttf_retrieval import main_ttf_scraper, main_ttf_candles
from Algo.Data_Retrieval.JKM_retrieval import main_jkm_scraper
from Algo.Data_Retrieval.coal_retrieval import main_coal_candles_ib
from Algo.Data_Retrieval.coal_retrieval import main_coal_scraper
from Algo.Data_Retrieval.gold_trader_retrieval import main as main_gold_trader_retrieval,wrap_bluegold_excel_handling
from Algo.Data_Retrieval.ttf_retrieval import main_ngf_candles_ib,main_ngf_candles_ib_v2
from Algo.Data_Processing.wind_features_generator import main_v2 as main_wind_features_extraction
from Algo.Data_Processing import generate_past_positions_features
from Algo.Viasualization.trading_strategies_summary import prepare_rsi_features
from Algo.AI.create_forecast_clusters import wrap_clusters_features_generation
from Algo.Data_Processing.CFS_Monthly_features_generator import main as main_CFSM_features_generator
from Algo.Data_Retrieval import retrieve_weathermodels
from Algo.Utils.send_email import send_email_with_attachments
from Algo.Tests.test_Xs import test_degdays_csvs2

from Algo.Viasualization.aggregated_analysis_linux import main as main_charts_analysis
from Algo.AI.create_forecast_clusters import main_wrapper as main_clusters_charts_wrapper
from Algo.Viasualization.trading_strategies_summary import plot_multi_modes
from Algo.Wrapper.wrap_bollinger_positions import single_iteration as single_iteration_bollinger_positions
from Algo.Wrapper.cluster_auto_selection_utils import wrap_clusters_auto_selection
from subprocess import Popen

from datetime import datetime as dtdt
from datetime import timedelta as td
import pytz
import time
import os
import pandas as pd
import os
from Algo.Utils.files_handle import HOME

LOCAL_TZ = pytz.timezone("Asia/Jerusalem")

LIVE_PREDS_CSV = os.path.join(HOME,"Trading","daily_predictions.csv")
LIVE_FROM_EC2_DIR = os.path.join(HOME,"Xdata","","")

ADDITIONAL_MODES = [#'realG',
                    #'realStrict',
                    # 'real2',
                   # 'realP20',
                   # 'real2b',
                    # 'paperCDD',
                    # 'realHDD'
                    #'realF0.3', 'realF',
                    # 'realPrev1w', #'real0.9',
                    ]
WIND_TRADING_ACTIVE = False
COFFEE_TRADING_ACTIVE = False

CHOSEN_WIND_SUFFIX = 'v15_WindTX'


def wrap_wind_decision_maker(tws_mode):
    main_wind_wrapper(tws_mode)

def wrap_coffee_decision_maker(tws_mode):
    main_coffee_wrapper(tws_mode)

def call_bollinger_position_wrapper():
    d = os.getcwd()
    procs = [Popen("%s %s"%(PYTHON_INTERPRETER,os.path.join(d,'wrap_bollinger_positions.py')), shell=True)]

    for i, proc in enumerate(procs):
        proc.wait()


def wrap_decision_maker(suff,time_mode_override):
    main_decision_maker(tws_mode=MODE, suff=suff, ft='GDD', time_mode_override=time_mode_override)
    if MODE == 'real':
        for additional_mode in ADDITIONAL_MODES:
            print('sleeping 5 seconds before another trading')
            time.sleep(5)
            ft = 'GDD'
            if 'HDD' in additional_mode:
                ft = 'HDD'
            elif 'CDD' in additional_mode:
                ft = 'CDD'
            main_decision_maker(tws_mode=additional_mode, ft=ft, suff=suff, time_mode_override=time_mode_override)



def main(mode='full',skip_xs=False,models_group_override=None,suff_override = None,trade=True,
         skip_file_download=False,time_mode_override=None,include_algo=True,
         special_ft=None):
    strat_code = 8 #3 #8 #10

    ys_mode = 'live' if mode == 'xy_instant' else 'research'
    suff = 'v8'

    now_naive = dtdt.now()
    local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
    utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

    asset = 'NG'
    if special_ft == 'wind':
        models_group = 'full'
        suff = CHOSEN_WIND_SUFFIX
    elif special_ft == 'coffee':
        models_group = 'SA'
        suff = 'v12_BArabica12Z'
        asset = 'COFFEE'
    elif utc_now_dt > dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,23,0) or \
            utc_now_dt < dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 1, 0):
        #### 18Z !!!!
        models_group = 'full'
        suff = 'v8_0Zb'
    elif utc_now_dt > dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,1,0) and \
            utc_now_dt < dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 4, 0):
        #### 18Z_cfs !!!!
        models_group = '6Z'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,6,50):
        models_group = 'full'
        suff = 'v8_0Zb'
    elif dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,6,50) <= utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,7,44):
        models_group = '12Z_b'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,8,10):
        models_group = 'full'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,10,55):
        models_group = 'legacy'
        suff = 'v8_0Zb'
        #models_group = 'full'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,11,5):
        models_group = 'PARA' # We need full because in the previous ones we had different suffix
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,12,5):
        models_group = '6Z' # We need full because in the previous ones we had different suffix
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,12,45):
        # We want to check if morning was working, if so we build only 6Z models
        eps_degdays = LIVE_FROM_EC2_DIR+"X_file_DailyDiffs_EPS_v8_12Z.csv"
        last_modification_of_eps = os.path.getmtime(eps_degdays)
        last_modification_of_eps = time.localtime(last_modification_of_eps)
        last_modification_of_eps_local = LOCAL_TZ.localize(dtdt.strptime(time.strftime('%Y%m%d %H:%M:%S',last_modification_of_eps),
                                                     '%Y%m%d %H:%M:%S'))
        last_modification_of_eps_utc = last_modification_of_eps_local.astimezone(pytz.utc).replace(tzinfo=None)
        if -((utc_now_dt - last_modification_of_eps_utc).total_seconds() / 3600) > 6:
            models_group = 'full' # We need full because in the previous ones we had different suffix
        else:
            models_group = '6Z'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,0):
        models_group = '6Z'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,30):
        models_group = 'cfs'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,14,15):
        models_group = '6Z'
        suff = 'v8_0Zb'
    elif utc_now_dt < dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 15,45):
        models_group = '12Z_a0'
        suff = 'v8_12Z'
    elif utc_now_dt < dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 17,50):
        models_group = '12Z'
        suff = 'v8_12Z'
    elif utc_now_dt > dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,17,30):
        if utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,18,30):
            models_group = 'full'
        else:
            models_group = '12Z_b'
        suff = 'v8_12Z'
    else:
        models_group = 'full'
    if models_group_override is not None:
        models_group = models_group_override
    if suff_override is not None:
        suff = suff_override

    bring_all = True

    try:
        suff2 = suff
    except:
        suff = suff_override
        suff2 = suff
    if suff == 'v8_0Zb':
        suff2 = 'v8_12Z'
    suffix_lst = [suff2]
    if special_ft == 'wind':
        suffix_lst = ['v14_WindUS','v15_WindTX']
    if models_group == 'full':
        if mode == 'full':
            if not skip_xs:
                if not skip_file_download:
                    main_get_ec2(models_group,bring_all=bring_all,special_ft=special_ft)
                try:
                    generate_x_data.full_process_method2(models_group,suffix=suffix_lst,from_new=False,asset=asset)
                except:
                    time.sleep(10)
                    generate_x_data.full_process_method2(models_group, suffix=suffix_lst, from_new=False,
                                                         asset=asset)
                # handle light Xs
                other_x_features.create_x_values_new(calc_start=dtdt(2022,1,1),model='EPSCO',use_full=True,
                                  base_on_existing=True,ft_type='GDD')
        elif mode in ['xy','xy_instant','xy_candles']:
            if mode in ['xy','xy_instant']:
                base_on_existing = True
                build_xs = False
                build_ys = True
                skip_uniting = False
                if mode in ['xy','xy_instant']:
                    skip_bollinger = False
                else:
                    skip_bollinger = True
                if not build_ys:
                    skip_bollinger = True
                    skip_uniting = True
            elif mode in ['xy_candles']:
                base_on_existing = True
                build_xs = False
                build_ys = False
                skip_bollinger = True
                skip_uniting = False

            try:
                main_wrap_process(strat_code=strat_code, mode=ys_mode, build_data=True, skip_bollinger=skip_bollinger,
                          skip_uniting=skip_uniting,build_xs=build_xs,
                          build_ys=build_ys,tws_mode='real',base_on_existing=base_on_existing) # True
            except:
                print('Failed to run %s... skipping'%mode)
    elif models_group in ['6Z','PARA','cfs','legacy','cmc','12Z_a0','12Z','12Z_b','eps',
                          'SA']:
        if not skip_xs:
            if not skip_file_download:
                main_get_ec2(models_group, bring_all=bring_all,special_ft=special_ft)
            print('about to generate X data')
            generate_x_data.full_process_method2(models_group, suffix=suffix_lst, from_new=False,asset=asset)
            # handle light Xs
            other_x_features.create_x_values_new(calc_start=dtdt(2022, 1, 1), model='EPSCO', use_full=True,
                                                 base_on_existing=True,ft_type='GDD')
    if special_ft is None:
        if include_algo:
            strategy = 'y_1245-1845'
            #assert USE_WINTER_MAPPING, 'In Winter, production mode has to be in Winter mapping mode... '
            start = dtdt(2020, 3, 1)
            end = dtdt(2050, 11, 14)
            #if not skip_xs:
            a = algo_main_wrapper(conf_name='test', last_model=12, strategy='0800-1100', calc_As=1, print_predies=0,
                                  suffix=suff, ft=FEATURE, send_email=True, start=start, end=end)
            if ('paperCDD' in ADDITIONAL_MODES) and FEATURE != 'CDD':
                a = algo_main_wrapper(conf_name='test', last_model=12, strategy='0800-1100', calc_As=1, print_predies=0,
                                      suffix=suff, ft='CDD', send_email=True, start=start, end=end)
            if ('paperHDD' in ADDITIONAL_MODES or 'realHDD' in ADDITIONAL_MODES) and FEATURE != 'HDD':
                a = algo_main_wrapper(conf_name='test', last_model=12, strategy='0800-1100', calc_As=1, print_predies=0,
                                      suffix=suff, ft='HDD', send_email=True, start=start, end=end)

            if trade:
                wrap_decision_maker(suff,time_mode_override)
                # repeating for trades that were missed
                time.sleep(1)
                wrap_decision_maker(suff, time_mode_override)

                HARD_BACKFILL = False
                if HARD_BACKFILL:
                    if time_mode_override == '0Z_a':
                        wrap_decision_maker('v8_0Zb', '18Z_cfs')
                        pass
                    elif time_mode_override == '0Z':
                        wrap_decision_maker('v8_0Zb', '0Z_a')
                        pass
                    elif time_mode_override == '0Z_b':
                        wrap_decision_maker('v8_0Zb', '0Z')
                        wrap_decision_maker('v8_0Zb', '0Z_a')

                    elif time_mode_override == '6Z_a2':
                        # re-run on morning to handle possible ignores trades that now have opposite
                        wrap_decision_maker('v8_0Zb', '6Z_a')
                    elif time_mode_override == '6Z':
                        # re-run on morning to handle possible ignores trades that now have opposite
                        wrap_decision_maker('v8_0Zb', '0Z')
                        wrap_decision_maker('v8_0Zb', '0Z_b')
                    elif time_mode_override == '6Z_cfs':
                        wrap_decision_maker('v8_0Zb', '6Z_cfs16d')
                    elif time_mode_override in ['12Z_a2','12Z_a3']:
                        wrap_decision_maker('v8_12Z', '12Z_a')
                        wrap_decision_maker('v8_12Z', '12Z_a2')
    elif special_ft == 'wind':
        main_wind_features_extraction()
        if trade and WIND_TRADING_ACTIVE:
            try:
                wrap_wind_decision_maker('real')
                for additional_mode in ADDITIONAL_MODES:
                    wrap_wind_decision_maker(additional_mode)
            except:
                print ('Failed on Wind...')
    elif special_ft == 'coffee' and COFFEE_TRADING_ACTIVE:
        if trade:
            try:
                wrap_coffee_decision_maker('real')
                for additional_mode in ADDITIONAL_MODES:
                    wrap_coffee_decision_maker(additional_mode)
            except:
                print ('Failed on Coffee...')
                raise

def wrap_candles_and_scraping():
    now_naive = dtdt.now()
    local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
    utc_now_dt = local_dt.astimezone(pytz.utc). \
        replace(tzinfo=None)

    try:
        # main_ttf_scraper(asset='MTF')
        prepare_rsi_features('real')
        generate_past_positions_features.main(start_time=utc_now_dt - td(days=10))
        main_coal_candles_ib('real', calc_start=utc_now_dt - td(days=30))
        main_coal_candles_ib('real', calc_start=utc_now_dt - td(days=30),resolution='1 day')
        main_ngf_candles_ib_v2('real', calc_start=utc_now_dt - td(days=30))
        main_ngf_candles_ib_v2('real', calc_start=utc_now_dt - td(days=30),asset='WTI')
        #wrap_clusters_features_generation(10)

        main_gold_trader_retrieval()
        main_ttf_candles()
        # retrieve_weathermodels.main()
    except Exception as e:
        print('Failed on: %s'%e)
    try:
        main_ttf_scraper()
        main_coal_scraper()
        main_jkm_scraper()
    except:
        print('Failed to retrieve TTF prices from scraping')

FEATURE = 'GDD'
MODE = 'paper'
MODE = 'real'
SKIP_0Zb_XS = True
LAST_MINUTE_OF_0Zb = 25

start = dtdt(2020, 3, 1)
end = dtdt(2022, 1, 1)

SKIP_EXTERNAL = False
# SKIP_EXTERNAL = True



#### Full morning package
def morning_wrap_full(skip_wind=False):
    # main(mode='xy_instant',skip_xs=True,models_group_override='full',trade=False,suff_override='v8_0Zb')
    # wrap_candles_and_scraping()
    main(time_mode_override='0Z_b', skip_xs=False, skip_file_download=False, models_group_override='full',
         suff_override='v8_0Zb', trade=True)
    if not skip_wind:
        main(special_ft='wind',skip_file_download=False,trade=False) #,trade=False,skip_file_download=True)
        main_wind_features_extraction()
    wrap_clusters_features_generation(30,base_on_existing=True)
    # main_CFSM_features_generator(start=utc_now_dt - td(days=30))
    prepare_rsi_features('real')
    main(time_mode_override='0Z_b',skip_xs=True,skip_file_download=True,models_group_override='full', suff_override='v8_0Zb',trade=True)

def wind_wrap():
    main(special_ft='wind',skip_file_download=False,trade=False) #,trade=False,skip_file_download=True)
    main_wind_features_extraction()
    main(time_mode_override='0Z_b', skip_xs=True, skip_file_download=True, models_group_override='full',
         suff_override='v8_0Zb', trade=True)

def morning_wrap_small(skip_download=False):
    main(special_ft='wind',skip_file_download=False,trade=False) #,trade=False,skip_file_download=True)
    main_wind_features_extraction()
    main(time_mode_override='0Z_b',skip_xs=True,skip_file_download=skip_download,models_group_override='full', suff_override='v8_0Zb',trade=True)

def wrap_0Z_predictors():
    main(time_mode_override='0Z_b',skip_xs=True,skip_file_download=True,models_group_override='full', suff_override='v8_0Zb',trade=True)


def morning_wrap_short(time_mode='0Z_b',models_group='full'):
    # main(mode='xy_instant',skip_xs=True,models_group_override='full',trade=False,suff_override='v8_0Zb')
    # wrap_candles_and_scraping()
    #main_CFSM_features_generator(start=utc_now_dt - td(days=30))
    retrieve_from_ec2.main('full')
    wrap_clusters_features_generation(30,base_on_existing=True)
    prepare_rsi_features('real')
    main(time_mode_override=time_mode,skip_xs=False,skip_file_download=True,models_group_override=models_group, suff_override='v8_0Zb',trade=True)

def wrap_charts_handling():
    main_charts_analysis(plot_charts=False)
    main_clusters_charts_wrapper(hours_back=1, hours_gap=6, days_back=4, cluster_for_plot='Big',
                                 base_on_original=True,
                                 plot=False, write_file=True)
    plot_multi_modes(show_plot=False,takeback_hours=3)
    send_email_with_attachments("Daily Charts %s" % dtdt.now().strftime('%d-%m-%Y %H'),'')


def quick_cluster_handling():
    # main(mode='xy_instant', skip_xs=False, models_group_override='full', trade=False)
    wrap_candles_and_uniting('real')
    wrap_clusters_auto_selection(backfill_mode='quick', days_back=0)

# main(mode='xy_instant',skip_xs=True,models_group_override='full',trade=False,suff_override='v8_0Zb')
# ran!
# quick_cluster_handling()
# send_email.wrap_notebooks_daily_mail()


# time.sleep(60*60*5)
# main(time_mode_override='0Z_a', skip_xs=False, skip_file_download=False, models_group_override='full',
#          suff_override='v8_0Zb', trade=True)
# wrap_clusters_auto_selection(backfill_mode='None', days_back=0)
# time.sleep(60*60)
# main(time_mode_override='18Z',skip_xs=False,skip_file_download=False,
#      models_group_override='full', suff_override='v8_0Zb',trade=True)

# prepare_rsi_features('real')
# wrap_candles_and_scraping()


# time.sleep(60*60*6)

### clusters handling
# wrap_candles_and_uniting('real')

# wrap_clusters_auto_selection(backfill_mode='quick', days_back=0)
# raise
### full day run
# morning_wrap_full(skip_wind=False)
# main(time_mode_override='6Z',skip_xs=False,skip_file_download=False,models_group_override='full', suff_override='v8_0Zb',trade=True)
# main(special_ft='wind',skip_file_download=False,trade=True) #,trade=False,skip_file_download=True)
### Coffee
# main(special_ft='coffee',skip_file_download=False,skip_xs=False)
# main(time_mode_override='full',models_group_override='18Z',skip_file_download=False,
#      skip_xs=False,suff_override='v8_0Zb')
# wrap_candles_and_scraping()

# main(time_mode_override='12Z',skip_xs=False,skip_file_download=False,models_group_override='full', suff_override='v8_12Z',trade=True)
# generate_past_positions_features.main(start_time=dtdt.now() - td(days=10))
# time.sleep(60*60*6)

# generate_past_positions_features.main(start_time=dtdt.now() - td(days=10))

now_naive = dtdt.now()
local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

# main_coal_candles_ib('real', calc_start=utc_now_dt - td(days=30))
# raise


if __name__ == '__main__':
    last_hour_bollinger_positions = 0

    while True:
        now_naive = dtdt.now()
        local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
        utc_now_dt = local_dt.astimezone(pytz.utc).\
            replace(tzinfo=None)

        cond_emergency = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 0, 0) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,0, 0, 0)

        cond_18z = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 0, 12) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,
                                                                                                     utc_now_dt.day, 0, 19)
        cond_18z_cfs16d = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 1, 25) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 1,30)
        cond_18z_b = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 3, 45) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 3,52)
        cond_earlyam = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 5, 40) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 5, 45)
        cond_am0a = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 6, 12) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 6, 18)
        cond_am0a2 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 6, 57) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 7, 2)
        cond_am0a3 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 7, 28) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 7,33, 10)
        cond_am1 = dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,7,53) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,8,8)
        cond_am2 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 8, 15) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 8, LAST_MINUTE_OF_0Zb)
        cond_am2b = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 8, 30) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 8, 34)
        cond_am3 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 11, 1) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 11, 6)
        cond_6za2 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 11, 22) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 11,30)
        cond_6za3 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 11, 57) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 12,2)

        cond_7utc = utc_now_dt.hour == 7
        cond_8utc = utc_now_dt.hour == 8
        cond_11utc = utc_now_dt.hour == 11
        cond_12utc = utc_now_dt.hour == 12
        #cond_noon = dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,11,45) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,11,52)
        cond_noon = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 12, 10) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 12,22)
        cond_noon_backup = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 12, 35) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 12,40)
        cond_cfs6z_early = dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,13) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,16)
        cond_afternoon = dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,38) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day,13,48)

        cond_12Z_a00 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 16, 15) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 16,20)
        cond_12Z_a0 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 16, 38) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 16,44)
        cond_12Z_a = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 16,55) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 17,5)
        cond_12Z_a2 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 17, 17) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 17,22)
        cond_12Z_a3 = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 17, 35) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 17,42)
        cond_12Z = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 18, 8) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 18,15)
        cond_12Z_b = dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, 19, 50) < utc_now_dt < dtdt(utc_now_dt.year,utc_now_dt.month,utc_now_dt.day, 19,55)

        if cond_cfs6z_early or cond_noon:
            try:
                pdf_extract_main()
            except:
                print('couldnt extract BWS pdf...')
        if sum ([cond_18z,cond_am0a,cond_am0a2,cond_am0a3, cond_am1, cond_am2,cond_am2b, cond_am3,cond_6za2,cond_6za3,
                 cond_noon,cond_noon_backup,
                 cond_afternoon,cond_cfs6z_early,cond_emergency,
                 cond_12Z_a0,cond_12Z_a00,cond_12Z_a,cond_12Z_a2,cond_12Z_a3, cond_12Z, cond_12Z_b,
                 cond_18z_b,cond_18z_cfs16d]):
            if cond_am1 or cond_am2 or cond_am3 or cond_6za2 or cond_noon or cond_afternoon or \
                cond_12Z_a or cond_12Z or cond_6za3 or cond_emergency:
                if (cond_am1 or cond_am2 or cond_am3 or cond_6za2 or cond_6za3 or cond_12Z_a or cond_noon or cond_afternoon or cond_emergency) and \
                        not SKIP_EXTERNAL:
                    main()
                    try:
                        try:
                            main_ttf_candles(start=(utc_now_dt-td(days=3)).strftime('%Y-%m-%d'))
                            main_ttf_candles(start=(utc_now_dt-td(days=3)).strftime('%Y-%m-%d'),stock_ticker='MTF=F')
                        except Exception as e:
                            print ('Faieled on TTF/MTF from yahoo with Error:\n %s'%e)
                        if cond_am1 or cond_am2 or cond_am3 or cond_6za3:
                            try:
                                main_coal_candles_ib('real', calc_start=utc_now_dt - td(days=30))
                                main_coal_candles_ib('real', calc_start=utc_now_dt - td(days=30), resolution='1 day')
                                main_ngf_candles_ib_v2('real', calc_start=utc_now_dt - td(days=30))
                                main_ngf_candles_ib_v2('real', calc_start=utc_now_dt - td(days=30), asset='WTI')
                            except:
                                print('Failed on candles')
                            prepare_rsi_features('real')
                            generate_past_positions_features.main(start_time=utc_now_dt - td(days=10))
                            retrieve_from_ec2.main('full')
                            wrap_clusters_features_generation(30,base_on_existing=True)
                            main_CFSM_features_generator(start=utc_now_dt - td(days=30))
                            main_gold_trader_retrieval()
                            wrap_bluegold_excel_handling()
                            retrieve_weathermodels.main()
                            main_ttf_candles()
                            # main_ttf_scraper(asset='MTF')
                    except Exception as e:
                        print ('Failed to retrieve TTF candles / Coal')
                        print ('Error was: %s'%e)
                try:
                    main_ttf_candles('MTF=F')
                except:
                    print('Failed to retrieve MTF from Yahoo finance')
                if cond_am2b or cond_am2:
                    test_degdays_csvs2(10)
                if not SKIP_EXTERNAL:
                    try:
                        main_ttf_scraper()
                        main_coal_scraper()
                        main_jkm_scraper()
                    except:
                        print ('Failed to retrieve TTF prices from scraping')
            if cond_18z:
                try:
                    retrieve_weathermodels.main()
                    pass
                except Exception as e:
                    print('Faile to retrieve weather models due to Error: %s'%e)
            if (cond_am0a or cond_am1 or cond_am3 or cond_noon or cond_noon_backup):
                try:
                    main(special_ft='wind',trade=True,include_algo=False)
                except:
                    main(special_ft='wind', trade=True, include_algo=False)
            try:
                if (cond_am2 and SKIP_0Zb_XS) or cond_am2b:
                    main(skip_file_download=True,skip_xs=True)
                else:
                    main()
            except Exception as e:
                print ('Encountered Error: %s, will rerun'%e)
                main(skip_file_download=True,skip_xs=True)

            # Charts handling
            if sum([cond_7utc,cond_8utc, cond_11utc,cond_12utc]) and os.path.getmtime(os.path.join(HOME,'Charts','Avg_Forecasts.pdf')) >60*60:
                try:
                    wrap_charts_handling()
                except:
                    print('failed to send charts')

            if False: #cond_12Z_b or cond_18z:
                try:
                    retrieve_weathermodels.main()
                except:
                    print ('Couldnt retrieve weather models... Skipping')
            if sum([cond_am1,cond_12Z_b,cond_am0a]):
                xy_mode = 'xy_instant' if utc_now_dt.weekday() != 4 else 'xy'
                if cond_am1:
                    # main(mode=xy_mode, skip_xs=False, models_group_override='full', trade=False)
                    pass # we don't want to bring it as of Jan 23, since it causes problems for the paper trading
                elif sum([cond_12Z_b,#cond_am0a
                          ]):
                    quick_cluster_handling()
                    main(mode=xy_mode, skip_xs=True, models_group_override='full', trade=False)
                    send_email.wrap_notebooks_daily_mail()
            if last_hour_bollinger_positions != dtdt.now().hour and utc_now_dt.hour not in list(range(0,6))+list(range(20,24))\
                    and utc_now_dt.weekday() in []: #[0,1,2,3,4]:
                print('Calling bollinger positions')
                single_iteration_bollinger_positions()
                last_hour_bollinger_positions = dtdt.now().hour
            print('Now its %s .... going to sleep for anothet 5 minutes' % utc_now_dt)
            time.sleep(60*2)

        if last_hour_bollinger_positions != dtdt.now().hour and utc_now_dt.hour not in list(range(0,6))+list(range(20,24))\
                and utc_now_dt.weekday() in []: #[0,1,2]:
            print('Calling bollinger positions')
            single_iteration_bollinger_positions()
            last_hour_bollinger_positions = dtdt.now().hour
        print ('Now its %s .... going to sleep for anothet 5 minutes'% utc_now_dt)
        time.sleep(60*2)