import os.path
from subprocess import check_call
import subprocess
import time
from datetime import datetime as dtdt
from datetime import time as dt_time
import pytz
import sys
import warnings
import logging

from Algo.Utils.files_handle import PROJECT_ROOT, PYTHON_INTERPRETER
from Algo.Utils.yaml_handle import dump_trading_conf_to_yaml, set_real_mode, set_paper_mode
from Algo.Utils.general import get_utc_now_dt
from Algo.Utils.timezone_definitions import LOCAL_TZ

warnings.filterwarnings("ignore")

platform = {'darwin': 'OSX',
            'win32': 'Windows'
           }.get(sys.platform, 'Windows')

# ================ Logging ==================
LOG_FILE_PATH = f'{PROJECT_ROOT}/Algo/Logs/mega_wrapper.log'
# Clean the log file from previous sessions
with open(LOG_FILE_PATH, 'w') as f:
    f.write('')
logging.basicConfig(filename=LOG_FILE_PATH, level=logging.INFO, format='%(asctime)s %(message)s')

# ================ Main CMDs ==================
outer_full_wrapper_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py'
outer_full_wrapper_evening_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function evening_preparation'
outer_full_wrapper_candles_retrieval_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_candles_and_uniting_wrapper'
outer_full_wrapper_short_evening_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function short_evening_preparation'
outer_full_wrapper_morning_backfill_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function morning_wrap_full'
outer_full_wrapper_morning_wrap_small_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_0Zb_process'
outer_full_wrapper_early_morning_wrap_small_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function early_morning_wrap'
outer_full_wrapper_early_noon_wrap_small_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function early_noon_wrap'
outer_full_wrapper_6za_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function early_noon_wrap'
outer_full_wrapper_6z_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_6Z_process'
outer_full_wrapper_6z_cfs_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_6Z_cfs_process'
outer_full_wrapper_0z_cfs_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_0Z_cfs_process'
outer_full_wrapper_12z_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_12Z_process'
outer_full_wrapper_unite_x_diffs_wrap_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_unite_x_diffs'
retrieve_weathermodels_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Data_Retrieval/retrieve_weathermodels.py'
teleconnections_x_extraction_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Data_Processing/teleconnections_xs_generator.py'
backup_daily_preds_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/General/backup_important_files.py --specific_function backup_daily_predictions'
rig_count_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Data_Retrieval/retrieve_rig_count_weekly.py'
zacks_retrieval_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/Zacks/download_zacks_ranking.py'
nasdaq_retrieval_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Data_Retrieval/nasdaq_retrieval.py --asset NQ'
btc_retrieval_cmd = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Data_Retrieval/nasdaq_retrieval.py --asset MBT'

# ================ Utility functions ==================
def kill_script(script_name):
    if platform == 'OSX':
        check_call(f'pkill -f {script_name}', shell=True)
    elif platform == 'Windows':
        check_call(f'taskkill /F /IM {script_name}', shell=True)
    else:
        raise Exception('Unsupported platform')

def kill_script_with_powershell(substring):
    import os
    tading_utils_killer_script = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'utils', 'stop_python_trading_utils.ps1')
    batch_trader_killer_script = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'utils', 'stop_python_batch_trader.ps1')
    full_wrapper_v2_killer_script = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'utils', 'stop_python_outer_wrapper_v2.ps1')
    new_strats_wrapper_killer_script = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'utils', 'stop_python_new_strats_final_wrapper.ps1')
    for script in [tading_utils_killer_script,
                   # batch_trader_killer_script,
                   full_wrapper_v2_killer_script,
                   new_strats_wrapper_killer_script]:
        if substring in script:
            try:
                args = ["powershell", script]
                print(args)
                subprocess.run(args, shell=True)
            except Exception as e:
                print(f'Failed to kill the script: {script}')
                print(e)

def wrap_killing_of_subscripts(substring=''):
    kill_script_with_powershell(substring)

def custom_check_call(cmd, shell=False, timeout=60*60,
                      kill_all_trader_scripts=True):
    process = subprocess.Popen(cmd, shell=shell)
    logging.info(f'Check call local is about to run cmd: {cmd}')
    try:
        process.communicate(timeout=timeout)
    except subprocess.TimeoutExpired:
        process.kill()
        stdout, stderr = process.communicate()
        raise subprocess.TimeoutExpired(cmd, timeout, output=stdout, stderr=stderr)
    finally:
        script_name = cmd.split(' ')[1]
        try:
            if kill_all_trader_scripts:
                kill_script_with_powershell('')
        except:
            print(f'Failed to kill the script: {script_name}')
    return_code = process.returncode
    if return_code != 0:
        raise subprocess.CalledProcessError(return_code, cmd)
    if kill_all_trader_scripts:
        wrap_killing_of_subscripts('')

def check_call_local(cmd, shell, timeout=60*60):
    logging.info(f'Check call local is about to run cmd: {cmd}')
    check_call(cmd, shell=shell, timeout=timeout)

def run_cmd():
    try:
        custom_check_call(outer_full_wrapper_cmd, shell=True)
    except:
        print('Error, failed to run the main cmd')

def run_data_backfill():
    cmd_new_data_backfill = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_additional_data_backfill'
    cmd_candles_backfill = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Wrapper/outer_full_wrapper_v2.py --specific_function wrap_candles_and_scraping'
    for name,cmd in zip(['additional data backfill', 'candles backfill'],[cmd_new_data_backfill,cmd_candles_backfill]):
        try:
            custom_check_call(cmd, shell=True)
        except Exception as e:
            print(f'Error, failed to run {name}')
            print(e)

def run_evening_cmd():
    logging.info('Running evening cmd')
    try:
        custom_check_call(outer_full_wrapper_evening_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run evening cmd')
        print(e)

def run_candles_retrieval():
    set_real_mode()
    logging.info('Running candles retrieval')
    try:
        custom_check_call(outer_full_wrapper_candles_retrieval_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run candles retrieval')
        print(e)

def run_short_evening_cmd():
    logging.info('Running short evening cmd')
    try:
        custom_check_call(outer_full_wrapper_short_evening_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run short evening cmd')
        print(e)

def run_morning_backfill():
    logging.info('Running morning backfill')
    try:
        custom_check_call(outer_full_wrapper_morning_backfill_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run morning backfill')
        print(e)

def run_backup_daily_preds():
    logging.info('Running backup daily preds')
    try:
        custom_check_call(backup_daily_preds_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run backup daily preds')
        print(e)

def retrieve_rig_count():
    logging.info('Running rig count retrieval')
    try:
        custom_check_call(rig_count_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run rig count retrieval')
        print(e)

def run_morning_wrap_small():
    logging.info('Running morning wrap small')
    try:
        custom_check_call(outer_full_wrapper_morning_wrap_small_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run morning wrap small')
        print(e)

def run_early_morning_wrap_small():
    logging.info('Running early morning wrap small')
    try:
        custom_check_call(outer_full_wrapper_early_morning_wrap_small_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run early morning wrap small')
        print(e)

def run_early_noon_wrap_small():
    logging.info('Running early noon wrap small')
    try:
        custom_check_call(outer_full_wrapper_early_noon_wrap_small_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run early noon wrap small')
        print(e)

def run_6z_process():
    logging.info('Running 6z process')
    try:
        custom_check_call(outer_full_wrapper_6z_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run 6z process')
        print(e)

def run_0z_cfs_process():
    logging.info('Running 0z CFS process')
    try:
        custom_check_call(outer_full_wrapper_0z_cfs_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run 0z_cfs process')
        print(e)

def run_6z_cfs_process():
    logging.info('Running 6z CFS process')
    try:
        custom_check_call(outer_full_wrapper_6z_cfs_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run 6z process')
        print(e)

def run_6za_process():
    logging.info('Running 6z process')
    try:
        custom_check_call(outer_full_wrapper_6za_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run 6z_a process')
        print(e)

def run_12z_process():
    logging.info('Running 12z process')
    try:
        custom_check_call(outer_full_wrapper_12z_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run 12z process')
        print(e)

def run_bitcoin_positions():
    logging.info('Running bitcoin positions')
    for script_name in ['bitcoin_historical', 'run_bitcoin_ma_analysis']:
        try:
            custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/Bitcoin/{script_name}.py', shell=True)
        except Exception as e:
            print('Error, failed to run bitcoin positions')
            print(e)

def send_positions_json():
    logging.info('Sending positions json')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/utils/general.py --specific_function send_positions_json_to_email', shell=True)
    except Exception as e:
        print('Error, failed to send positions json')
        print(e)
    try:
        custom_check_call(
            f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/utils/general.py --specific_function send_positions_json_to_email --input MBT',
            shell=True)
    except Exception as e:
        print('Error, failed to send positions json')
        print(e)

def run_nasdaq_positions():
    logging.info('Running nasdaq positions')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/Nasdaq/bollinger_plays_long_term.py', shell=True)
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/Nasdaq/nasdaq_analyze_MA.py', shell=True)
    except Exception as e:
        print('Error, failed to run Nasdaq positions')
        print(e)

def run_nasdaq_correlations_email():
    logging.info('Running nasdaq correlation notebook')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function wrap_nasdaq_correlations_notebook_mail', shell=True)
    except Exception as e:
        print('Error, failed to run Nasdaq positions')
        print(e)

def run_notebooks_daily_email():
    logging.info('Running nasdaq correlation notebook')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function wrap_notebooks_daily_mail', shell=True)
    except Exception as e:
        print('Error, failed to run Nasdaq positions')
        print(e)

def run_degdays_report_email():
    logging.info('Running degdays report notebook')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function wrap_degdays_report_mail', shell=True)
    except Exception as e:
        print('Error, failed to run degdays report')
        print(e)

def run_ng_rolling_predictors_email():
    logging.info('Running NG rolling predictors notebook')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function wrap_ng_rolling_predictions_notebook', shell=True)
    except Exception as e:
        print('Error, failed to run NG rolling preds positions')
        print(e)

def run_nasdaq_correlations():
    logging.info('Running nasdaq correlation predictions')
    weekday = dtdt.now().weekday()
    add_cpi = True
    suffix = ' --include_is_cpi True' if add_cpi else ''
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Stocks_Research/Nasdaq/analyze_nasdaq_hours_correlation.py', shell=True)
    except Exception as e:
        print('Error, failed to run Nasdaq correlations')
        print(e)

def run_unite_x_diffs():
    logging.info('Running unite x diffs')
    try:
        custom_check_call(outer_full_wrapper_unite_x_diffs_wrap_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run unite x diffs')
        print(e)

def send_look_for_new_strats_peformance_report():
    custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour', shell=True)
    custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour_bin_end_dates', shell=True)
    custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour_no_segmenters', shell=True)
    custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour_hybrid', shell=True)
    # custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour_semi234', shell=True)
    # custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function run_look_for_new_strategies_analysis_by_hour_semi234_no_segmenters', shell=True)

def run_retrieve_teleconnections(generate_xs=False):
    logging.info('Running retrieve teleconnections')
    try:
        custom_check_call(retrieve_weathermodels_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run retrieve teleconnections')
        print(e)
    if generate_xs:
        custom_check_call(teleconnections_x_extraction_cmd, shell=True)

def run_main_tester():
    logging.info('Running main tester')
    custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Tests/main_tester.py', shell=True)

def handle_evening_preparation(short=True):
    logging.info('Handling evening preparation')
    try:
        set_real_mode()
        time.sleep(2)
        if short:
            run_short_evening_cmd()
        else:
            run_evening_cmd()
        set_paper_mode()
    except Exception as e:
        print('Error, failed to handle evening preparation')
        print(e)

def run_zacks_retrieval():
    logging.info('Running zacks retrieval')
    try:
        custom_check_call(zacks_retrieval_cmd, shell=True)
    except Exception as e:
        print('Error, failed to run zacks retrieval')
        print(e)

def run_nasdaq_retrieval():
    logging.info('Running nasdaq retrieval')
    try:
        set_real_mode()
        print('running CMD: ', nasdaq_retrieval_cmd)
        custom_check_call(nasdaq_retrieval_cmd, shell=True)
        print('running CMD: ', btc_retrieval_cmd)
        custom_check_call(btc_retrieval_cmd, shell=True)
        set_paper_mode()
    except Exception as e:
        print('Error, failed to run nasdaq retrieval')
        print(e)

def bitcoin_retrieval():
    custom_check_call(btc_retrieval_cmd, shell=True)

def run_look_for_new_strategies():
    logging.info('Running look for new strategies')
    try:
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Notebooks/new_strategies_finder/look_for_new_strategies_daily_preds.py', shell=True)
    except Exception as e:
        print('Error, failed to run look for new strategies')
        print(e)

def run_look_for_new_strategies_production(only_today=False, strat_conf='all', only_yesterday=False, config='dynamic'):
    logging.info('Running look for new strategies production')
    if config == 'dynamic':
        if dtdt.now().weekday() in [0,1,2,3,4]:
            config = 'weekly'
        else:
            config = 'semi234'
    try:
        cmds_list = []
        for windows_group in ['group1', 'group2']:
            cmd_weekly = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Notebooks/new_strategies_finder/production/final_wrapper.py'
            cmd_semi_weekly_234 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Notebooks/new_strategies_finder/production/final_wrapper.py --config semi234'
            cmd_semi_weekly_01 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Notebooks/new_strategies_finder/production/final_wrapper.py --config semi01'
            cmd = {'weekly': cmd_weekly, 'semi234': cmd_semi_weekly_234, 'semi01': cmd_semi_weekly_01}[config]
            cmd += f' --strat_conf {strat_conf}'
            cmd += f' --windows_group {windows_group}'
            cmds_list.append(cmd)
        processes = []
        for cmd in cmds_list:
            process = subprocess.Popen(cmd, shell=True)
            print('Adding CMD to Popen: ', cmd)
            processes.append(process)
        for process in processes:
            process.wait()
        utc_now = get_utc_now_dt()
        custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Utils/send_email.py --specific_function wrap_look_for_new_strategies_notebook_mail', shell=True)
        if dtdt.now().weekday() == 0 and utc_now.hour <= 12:
            send_look_for_new_strats_peformance_report()
    except Exception as e:
        print('Error, failed to run look for new strategies production')
        print(e)

def run_backfill_trades(today_only=False):
    logging.info('Running backfill trades')
    try:
        if not today_only:
            custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Learning/decision_maker.py --specific_function wrap_backfill_trades', shell=True)
        else:
            custom_check_call(f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/Learning/decision_maker.py --specific_function wrap_backfill_trades_today_only', shell=True)
    except Exception as e:
        print('Error, failed to run backfill trades')
        print(e)

# ================ APScheduler Setup ==================
from apscheduler.schedulers.blocking import BlockingScheduler

def _get_local_hour(hour,minute=0):
    # Convert a given UTC hour to local hour using LOCAL_TZ
    dtdt_now_utc = dtdt.utcnow()
    return LOCAL_TZ.fromutc(dtdt_now_utc.replace(hour=hour, minute=minute, second=0, microsecond=0)).hour

# Create the scheduler with the local timezone
scheduler = BlockingScheduler(timezone=LOCAL_TZ)

# Define misfire_grace_time in seconds (30 minutes)
MISFIRE_GRACE = 1800

# ---------- Main calls for X generation (Weekdays only) ----------
scheduler.add_job(run_early_morning_wrap_small,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(6),
                  minute=2,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_morning_backfill,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(6),
                  minute=40,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_morning_backfill,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(7),
                  minute=40,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_0z_cfs_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(8),
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_6za_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(11),
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_6z_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(12),
                  minute=15,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_6z_cfs_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(13),
                  minute=15,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_12z_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(17),
                  minute=50,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_morning_backfill,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(9),
                  minute=48,
                  misfire_grace_time=MISFIRE_GRACE)

# ---------- Data Backfill tasks ----------
scheduler.add_job(run_data_backfill,
                    trigger='cron',
                    day_of_week='mon-fri',
                    hour=_get_local_hour(9),
                    minute=55,
                    misfire_grace_time=MISFIRE_GRACE)

# ---------- Night tasks ----------
h = _get_local_hour(19)
scheduler.add_job(run_candles_retrieval,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=h,
                  minute=10,
                  misfire_grace_time=MISFIRE_GRACE)

h = _get_local_hour(20)
scheduler.add_job(run_candles_retrieval,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=h,
                  minute=15,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(handle_evening_preparation,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=h,
                  minute=20,
                  misfire_grace_time=MISFIRE_GRACE)

# ---------- Additional strategy and backfill tasks ----------
scheduler.add_job(run_morning_wrap_small,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(6),
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="0z"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(6),
                  minute=35,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_morning_wrap_small,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(7),
                  minute=25,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="0z+6z_early"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(7),
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="0z+6z_early"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(8),
                  minute=15,
                  misfire_grace_time=MISFIRE_GRACE)


scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="6z_early"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(11),
                  minute=1,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="6z_mid"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(11),
                  minute=50,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="0z+6z"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(12),
                  minute=25,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="all"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(13),
                  minute=40,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="12z"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(18),
                  minute=5,
                  misfire_grace_time=MISFIRE_GRACE)

# Daily backfill (two jobs scheduled at the same time)
scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="all"),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(19),
                  minute=5,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_look_for_new_strategies_production(strat_conf="all", config='semi234'),
                  trigger='cron',
                  day_of_week='fri',
                  hour=_get_local_hour(19),
                  minute=5,
                  misfire_grace_time=MISFIRE_GRACE)

# ---------- Teleconnections retrieval ----------

for hour,minute in [(6,17),(7,15),(7,45),(8,15),(8,45),(9,30),
                    (19,30),(20,0),(20,30),(21,30)]:
    scheduler.add_job(run_retrieve_teleconnections,
                      trigger='cron',
                      day_of_week='mon-sun',
                      hour=_get_local_hour(hour),
                      minute=minute,
                      misfire_grace_time=MISFIRE_GRACE)


scheduler.add_job(run_notebooks_daily_email,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=7,
                  minute=5,
                  misfire_grace_time=MISFIRE_GRACE)

# ---------- Other tasks ----------
scheduler.add_job(run_unite_x_diffs,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=12,
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_backup_daily_preds,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=12,
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(retrieve_rig_count,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=12,
                  minute=45,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_main_tester,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=11,
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

# Nasdaq retrieval only on Fridays
scheduler.add_job(run_nasdaq_retrieval,
                  trigger='cron',
                  day_of_week='fri',
                  hour=16,
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_6z_process,
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(12),
                  minute=50,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_backfill_trades(today_only=True),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=17,
                  minute=30,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_backfill_trades(today_only=True),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=21,
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_degdays_report_email(),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(8),
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(lambda: run_degdays_report_email(),
                  trigger='cron',
                  day_of_week='mon-fri',
                  hour=_get_local_hour(20),
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

scheduler.add_job(run_backfill_trades,
                  trigger='cron',
                  day_of_week='fri',
                  hour=16,
                  minute=0,
                  misfire_grace_time=MISFIRE_GRACE)

##### multithreading preparation ######
# from apscheduler.schedulers.background import BackgroundScheduler
# from apscheduler.executors.pool import ThreadPoolExecutor
#
# executors = {
#     'default': ThreadPoolExecutor(max_workers=10)
# }
# scheduler = BackgroundScheduler(executors=executors, timezone='your_timezone')
# scheduler.start()

# ================ Main Execution ==================
if __name__ == '__main__':
    # run_0z_cfs_process()
    # run_look_for_new_strategies_production(strat_conf="0z")
    for i in range(1):
        run_candles_retrieval()
    # send_look_for_new_strats_peformance_report()
    handle_evening_preparation(short=True)
    # raise
    # run_degdays_report_email()
    # run_retrieve_teleconnections()#generate_xs=True)
    # raise
    # run_morning_backfill()
    # run_look_for_new_strategies_production(strat_conf="0z+6z_early",config='weekly')
    # run_6z_process()
    # time.sleep(60*10)

    # Start APScheduler – this call will block and run your scheduled jobs
    try:
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        pass
