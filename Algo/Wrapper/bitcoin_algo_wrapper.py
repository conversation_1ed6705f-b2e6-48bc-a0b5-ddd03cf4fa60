import logging
import time

import schedule
from datetime import datetime as dtdt
from Algo.Utils.timezone_definitions import LOCAL_TZ
from datetime import timedelta as td


from mega_wrapper import run_bitcoin_positions, run_nasdaq_positions,run_nasdaq_retrieval,run_nasdaq_correlations, run_nasdaq_correlations_email,send_positions_json,bitcoin_retrieval
def set_schedule():
    logging.info('Setting schedule')
    # schedule.every(2).hours.at(":01").do(run_bitcoin_positions)
    schedule.every(4).hours.at(":05").do(run_nasdaq_retrieval)
    schedule.every(4).hours.at(":20").do(run_nasdaq_positions)
    schedule.every(4).hours.at(":25").do(run_bitcoin_positions)
    schedule.every(4).hours.at(":30").do(send_positions_json)

    # Schedule the job to run every round hour between 9 and 17 UTC
    dtdt_now_utc = dtdt.utcnow()
    local_hour_9 = LOCAL_TZ.fromutc(dtdt_now_utc.replace(hour=9, minute=0, second=0, microsecond=0))
    hours = [x.hour for x in [local_hour_9+td(hours=i) for i in range(7)]]
    for hour in hours:
        schedule.every().day.at(f"{hour:02d}:05").do(run_nasdaq_correlations)

    # schedule email sending
    schedule.every().day.at(f"{hours[2]:02d}:05").do(run_nasdaq_correlations_email)
    schedule.every().day.at(f"{hours[3]:02d}:05").do(run_nasdaq_correlations_email)
    schedule.every().day.at(f"{hours[4]:02d}:05").do(run_nasdaq_correlations_email)
    # schedule.every().day.at(f"{hours[5]:02d}:05").do(run_nasdaq_correlations_email)


if __name__ == '__main__':
    set_schedule()


    bitcoin_retrieval()
    run_nasdaq_retrieval()
    run_bitcoin_positions()
    send_positions_json()
    # raise
    #
    # run_nasdaq_correlations()
    # run_nasdaq_correlations_email()

    # run_nasdaq_positions()
    # send_positions_json()
    # raise
    while True:
        utc_now = dtdt.utcnow()
        try:
            schedule.run_pending()
        except Exception as e:
            print(f'Scheduler faced an Error at {utc_now}: {e}')
            print('Skipping this iteration')
        # time.sleep(30)

