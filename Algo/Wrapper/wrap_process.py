import sys
import time

sys.path.append("...") # Adds higher directory to python modules path.

from Algo.Xbox import generate_x_data
from Algo.Ybox import Analyze_Ys, generate_XY
from tests.Rans_Draft import main as main_candles_handle
from Algo.Trading.Rans_Draft_v2 import wrap_hisorical_data_ibridge

from Algo.Learning import algo_wrapper,algo_wrapper2, old_configuration
from Algo.Trading.Rans_Draft_v2 import wrap_hisorical_data_ibridge, wrap_hisorical_data_ibapi
from Algo.Conf.assets import Asset, SPECIAL_RESOLUTION_ASSETS
# outer packages
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
from calendar import monthrange
import os
import os
from Algo.Utils.files_handle import HOME
from Algo.Ybox.definitions import DELTA_YS_METHOD
from Algo.Ybox import delta_ys_with_candles

LOAD_FROM_NEW = False # False #todo hack

#### X Generation
def wrap_Xs(last_model,x_suffix='take7',days_ahead=list(range(1,16)),write=True,base_on_existing=True):
    models_str = 'GFS+PARA+GEFS'
    if x_suffix in ['take8','take9','take9_Weighted']:
        models_str = 'Full'

    weights_csv = os.path.join(HOME,"degdays_archive","Live","Models_accuracy_HDD_3day_rollingMean_01.11.2019-20.01.2020.csv")
    if 'Weighted' not in x_suffix:
        weights_csv = None
    prev = True
    load_from_new = not base_on_existing
    try:
        generate_x_data.main(load_from_new=load_from_new,last_model=last_model,include_prev24=prev,start=(dtdt.now()-td(days=4)),end=dtdt(2022,2,1),
         suffix0=x_suffix,complete_missing_from_new=True,days_ahead=days_ahead,write=write,models_str=models_str,
                             weights_csv=weights_csv)
    except:

        generate_x_data.main(load_from_new=True, last_model=last_model, include_prev24=prev, start=dtdt(2018, 10, 1),
                             end=dtdt(2022, 2, 1),suffix0=x_suffix, complete_missing_from_new=False, days_ahead=days_ahead, write=write,
                             models_str=models_str,weights_csv=weights_csv)

### Prices retrieval
def wrap_retrieve_candles(tws_mode='paper',max_tries=3):
    days_in_month = lambda dt: monthrange(dt.year, dt.month)[1]
    today = dtdt.today() - td(days=2)
    first_day1 = today + td(days_in_month(today)-3)
    first_day2 = (first_day1.replace(day=1) + td(days_in_month(first_day1)+10)).replace(day=1)
    app = None
    i = 0
    # stupid hack to TWS problem first month using direct API, second month using Ibridgepy #todo
    ref_dates = [first_day1,first_day2]
    print('ref_dates = ',ref_dates)
    # shuffle
    current_second = dtdt.now().second # round to make more stable
    if current_second % 2 == 0:
        ref_dates = ref_dates[::-1]

    for ref_date in ref_dates:
        i += 1
        print ('INFO: retrieving candles for: %s'% ref_date)
        tries = 0
        while tries < max_tries:
            try:
                if i == 1111:
                    app = main_candles_handle(ref_date.year,[ref_date.month],mode=tws_mode,app=app)
                    time.sleep(1.5)
                    # app = None
                else:
                    app = wrap_hisorical_data_ibridge(ref_date.year,[ref_date.month],asset_name='NG',app=app)
            except Exception as e:
                print('ERROR: failed to retrieve candles due to Error: %s'%e)
                print(f'trying again...Try #{tries}')
                app = None
            finally:
                tries += 1
                time.sleep(1)

def wrap_candles_and_uniting(tws_mode='paper',asset='NG',create_delta_ys=True,
                             skip_candles=False,
                             max_candles_tries=3):
    add_tzs = True
    base_on_existing = True
    start = dtdt.now() - td(days=45)

    if not skip_candles:
        try:
            wrap_retrieve_candles(tws_mode,max_tries=max_candles_tries)
        except Exception as e:
            print('Error: couldnt retrieve candles...due to Error: %s'%e)
            print('moving on')
    if asset == 'NG':
        candles_outpath = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_%sLive.csv")% ("" if not add_tzs else "tz_")
    elif asset == 'CORN':
        candles_outpath = os.path.join(HOME,"CORN","Market_Data","CORN_2018-19_frontMonth_%sLive.csv") % ("" if not add_tzs else "tz_")
    elif asset == 'COFFEE':
        candles_outpath = os.path.join(HOME,"COFFEE","Market_Data","COFFEE_2018-19_frontMonth_%sLive.csv") % ("" if not add_tzs else "tz_")
    elif asset == 'NCF':
        candles_outpath = os.path.join(HOME,"Market_Data_General","Coal","COAL_frontMonth_%sLive.csv") % ("" if not add_tzs else "tz_")
    elif asset == 'NQ':
        candles_outpath = os.path.join(HOME,"Market_Data_General","Nasdaq","NQ_frontMonth_%sLive.csv") % ("" if not add_tzs else "tz_")
    else:
        raise AssertionError('Invalid asset')

    Analyze_Ys.unite_monthly_contracts(candles_outpath,add_tzs,base_on_existing=base_on_existing,
                                       start=start,
                                       asset=asset)
    if create_delta_ys:
        delta_ys_with_candles.main(asset, base_on_existing=base_on_existing)


### Ys generation
def wrap_Ys(asset='NG',mode='live',bollinger_start=dtdt(2019,12,1),skip_uniting=False,skip_bollinger=False,bollinger_price_col="open",
            base_on_existing=False,
            resolution=None,
            delta_y_method=DELTA_YS_METHOD):

    asset_obj = Asset(asset)

    assert mode in ['live','research']
    strategies = Analyze_Ys.production_strategies if mode == 'live' else \
        Analyze_Ys.research_strategies
    days_before_exp = asset_obj.days_to_exp
    # unite months + Bollinger
    if bollinger_price_col == "open":
        bollinger_suffix = "_V3d10" #"_OpenPriceV2b"
    else:
        bollinger_suffix = ""
    add_tzs = True

    candles_outpath = asset_obj.candles_outpath

    resolutions_fullname_inverse_mapping = {v:k for k,v in {'15m': '15 mins', '1H': '1 Hour', '1D': '1 day',
                                    '30m': '30 mins', '5m': '5 mins', '1m': '1 min'}.items()}
    resolutions_fullname_inverse_mapping.update({'1 hour': '1H'})
    resolutions_shortname = resolutions_fullname_inverse_mapping.get(resolution,'15m')

    if not skip_uniting:
        Analyze_Ys.unite_monthly_contracts(candles_outpath,add_tzs,base_on_existing=base_on_existing,start=bollinger_start.replace(day=1,hour=0),
                                           asset=asset,resolution=resolutions_shortname)
    if not skip_bollinger:
        #bollinger_start = dtdt(2017,10,1) # hack
        #print ('hack with bollinger start')
        Analyze_Ys.wrap_bollingers_generation(candles_outpath,True,base_on_existing=base_on_existing,
                                              start=bollinger_start,suffix=bollinger_suffix,price_col='open')
        print ('Warning hack bollinger from 0')
    # build Ys by strategies
    months = [10,11, 12, 1, 2, 3,4]  # [11,12,1]#[11,12,1,2]
    outdir = asset_obj.delta_ys_dir

    if asset_obj.days_to_exp !=9:
        outdir += '_D%s'%days_before_exp
    if not os.path.exists(outdir):
        os.makedirs(outdir)
    if delta_y_method == 'v1':
        nn = 0
        try:
            strats_df = Analyze_Ys.wrap_delta_Ys_generation(candles_outpath,outdir,strategies,
                                                        True, 'utc',base_on_existing=base_on_existing)
        except:
            print('Error: couldnt generate delta Ys... skipping')
    elif delta_y_method == 'v2':
        nn = 0
        delta_ys_with_candles.main(asset=asset,base_on_existing=base_on_existing)


def wrap_XYs(last_model,days_ahead='1-15',strategy='1415-1715',mode="open",
             x_suffix="take7",asset='NG',start=dtdt(2016,1,1)):
    cond_on_xs = lambda x: 'd=[%s]'%days_ahead in x

    if last_model == 12:
        cond_on_ys = lambda x: sum([st in x for st in [strategy]])  # ,'1430','1445','1515','1530']])
    else:
        cond_on_ys = lambda x: sum([st in x for st in ['0000-']]) #['0345-', '0445-']])

    if asset == 'NG':
        bollinger_close = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_Live_withBollinger_OpenPrice.csv")
        bollinger_open = os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_Live_withBollinger_V3d10.csv")
    elif asset == 'CORN':
        bollinger_close = os.path.join(HOME,"CORN","Market_Data","CORN_2018-19_frontMonth_tz_Live_withBollinger_OpenPrice.csv")
        bollinger_open = os.path.join(HOME,"CORN","Market_Data","CORN_2018-19_frontMonth_tz_Live_withBollinger_V3d10.csv")
    elif asset == 'COFFEE':
        bollinger_close = os.path.join(HOME,"COFFEE","Market_Data","COFFEE_2018-19_frontMonth_tz_Live_withBollinger_OpenPrice.csv")
        bollinger_open = os.path.join(HOME,"COFFEE","Market_Data","COFFEE_2018-19_frontMonth_tz_Live_withBollinger_V3d10.csv")
    bollinger_file = bollinger_open if mode == 'open' else bollinger_close

    days_before_exp = Analyze_Ys.DAYS_BEFORE_EXPIRY[asset]
    ys_dir = 'Ys3'
    xy_suffix = "_PrevYs+Bollinger_V3d10" if mode == 'open' else "_PrevYs+Bollinger_v3"
    if days_before_exp != 9:
        assert days_before_exp in [30,2,5]
        ys_dir = 'Ys3_D%s'%days_before_exp
        #x_suffix+='_Y=D%s'%days_before_exp
        bollinger_file = bollinger_file.replace('tz_Live','tz_Live_D2Exp=%s'%days_before_exp)
        xy_suffix+='_D2Exp=%s'%days_before_exp
    generate_XY.main_generator(last_model, cond_on_xs, cond_on_ys,
                               bollinger_file=bollinger_file,xy_suffix=xy_suffix,x_suffix=x_suffix,
                               bollinger_resolutions=['15M','30M','1H','2H','4H','1D'],asset=asset,ys_dir=ys_dir,
                               start=start)

def wrap_algo(last_model=12, strategy='1415-1715', days_ahead_str='1-15',x_suffix="take9",engine = 'elastic'):
    drop_partial_predictions = False
    market_features = True
    months_to_drop = [4]  # [11,12,1,2] # [10]

    test_truncation = 200
    normalize_Xs = True

    final_results_df = algo_wrapper.handle_strategy(strategy, last_model, engine, days_ahead_str,x_suffix, drop_partial_predictions,
                                       market_features,
                                       months_to_drop, normalize_Xs, test_truncation)

    last_pred, last_prob = (final_results_df.iloc[-1]['y_tag'], final_results_df.iloc[-1]['probability'])
    last_date = final_results_df.iloc[-1]['time']
    pred_action = "Sell" if last_pred < 0 else "Buy"
    print("last Prediction: %s |  %s (%s) | Prob: %s" % (last_date, pred_action, round(last_pred, 2), last_prob))
    return final_results_df, last_date,pred_action,last_pred,last_prob

def wrap_algo2(last_model=12, strategy='1415-1715', days_ahead_str='1-15',x_suffix="take9",engine = 'elastic'):
    drop_partial_predictions = False
    market_features = True
    months_to_drop = [4]  # [11,12,1,2] # [10]

    test_truncation = 200
    normalize_Xs = True

    models_configs = configuration.get_configs(strategy,"basic",{})

    final_results_df = algo_wrapper2.handle_strategy(strategy, last_model,models_configs, days_ahead_str,x_suffix, drop_partial_predictions,
                                       months_to_drop, normalize_Xs, test_truncation)

    last_pred, last_prob = (final_results_df.iloc[-1]['y_tag'], final_results_df.iloc[-1]['probability'])
    last_date = final_results_df.iloc[-1]['time']
    pred_action = "Sell" if last_pred < 0 else "Buy"
    print("last Prediction: %s |  %s (%s) | Prob: %s" % (last_date, pred_action, round(last_pred, 2), last_prob))
    return final_results_df, last_date,pred_action,last_pred,last_prob



strats_dict = {0:"0345-0545",
               1: "1130-1415",
               1.1: "1130-1315",
               1.2: "1315-1415",
                1.3: '1215-1345',
               2:"1415-1715",
               2.1: '1415-1530',
               2.3: '1415-1645',
               2.2: '1445-1530',
                2.4: '1415-1715',
               3:"1415-1745",
               4: "1530-1730",
               4.1: "1530-1645",
               4.2: '1515-1700',
               5: "1445-1745",
               6:'1130-1715',
               7:'1645-1745',
               8: '0800-1100',
               9: '0900-1200',
               10:'0000-0400'
               }


def main(strat_code=8,mode='live',build_data=True,
         skip_uniting=False,skip_bollinger=False,build_xs=True,build_ys=True,
         tws_mode='paper',base_on_existing=True):

    engine = "elastic"
    last_model = 12 # 12
    if strat_code == 10:
        last_model = 0
    strategy = strats_dict[strat_code]
    price_col = "open"
    days_ahead_list = list(range(1,16)) # 1
    days_ahead_str = "%s-%s"%(days_ahead_list[0], days_ahead_list[-1])
    x_suffix = 'take9' #_Weighted' # take7

    # Run
    if build_data:
        if not skip_uniting:
            wrap_retrieve_candles(tws_mode=tws_mode)
            pass
        bollinger_start = dtdt(2017,10,1) #
        if base_on_existing:
            bollinger_start = dtdt.now() - td(days=60)
        if build_ys:
            wrap_Ys(mode=mode,skip_uniting=skip_uniting,skip_bollinger=skip_bollinger,bollinger_start=bollinger_start,
                    bollinger_price_col=price_col,base_on_existing=base_on_existing) # False
        if build_xs:
            wrap_Xs(last_model,days_ahead=days_ahead_list,x_suffix=x_suffix,base_on_existing=base_on_existing)
        if mode != 'xy_candles':
            wrap_XYs(last_model=last_model,days_ahead=days_ahead_str,strategy=strategy,mode=price_col,x_suffix=x_suffix,
                 start=bollinger_start) # '1130-1415
        pass

def wrap_full_asset_retrieval(years,months,asset='COFFEE',
                              include_ys=True,include_xy=True,
                              skip_bollinger=False,
                              bollinger_start = dtdt(2021, 3, 1),
                              base_on_existing=True,
                              resolution=None):

    asset_obj = Asset(asset)
    asset2 = asset_obj.short_name
    if resolution is None:
        resolution = '15 mins'
        if asset in SPECIAL_RESOLUTION_ASSETS.keys():
            resolution = SPECIAL_RESOLUTION_ASSETS[asset]

    for year in years:
        allowed_months = asset_obj.allowed_months
        for month in months:
            if month in allowed_months:
                main_candles_handle(year, [month], mode='real', asset=asset2,resolution=resolution)

    if include_ys:
        wrap_Ys(asset=asset, mode='research', skip_uniting=False, skip_bollinger=skip_bollinger, bollinger_start=bollinger_start,
                bollinger_price_col='open', base_on_existing=base_on_existing,
                resolution=resolution)  # hack True

    strategy = '1130-1415' if asset == 'COFFEE' else '0800-1100'
    price_col = "open"

    days_ahead_list = list(range(1, 16))  # 1
    days_ahead_str = "%s-%s" % (days_ahead_list[0], days_ahead_list[-1])
    x_suffix = 'take9'  # _Weighted' # take7
    if include_xy:
        wrap_XYs(last_model=12, days_ahead=days_ahead_str, strategy=strategy, mode=price_col,
             x_suffix=x_suffix, asset=asset, start=bollinger_start)  # '1130-1415


if __name__ == '__main__':

    wrap_retrieve_candles(tws_mode='real', max_tries=3)
    raise

    code = 8 # 3 10
    mode = 'research'
    short_mode = False
    if short_mode:
        build_data = True
        skip_uniting = True
        skip_bollinger = True
        build_xs =False
    else:
        build_data = True
        skip_uniting = False
        skip_bollinger = False
        build_xs = True

    build_data = False
    build_xs = False
    main(strat_code=code,mode=mode,build_data=True,build_xs=False,build_ys=False,
         skip_uniting=True,skip_bollinger=True,base_on_existing=False
         )
