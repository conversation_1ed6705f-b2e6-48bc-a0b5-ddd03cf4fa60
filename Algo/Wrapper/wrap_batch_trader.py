from subprocess import check_call
import schedule
import time
from datetime import datetime as dtdt
from datetime import time as dt_time
import pytz
from Algo.Utils.files_handle import PROJECT_ROOT,PYTHON_INTERPRETER
from Algo.Utils.yaml_handle import dump_trading_conf_to_yaml,set_real_mode,set_paper_mode
from Algo.Utils.general import convert_timezone
from Algo.Trading.constants import LOCAL_TZ_NAME
from Algo.Wrapper.mega_wrapper_v2 import custom_check_call,kill_script,kill_script_with_powershell

mode2 = 'ens_calmar0.25_S0.5_window4'
# mode2 = 'ens_cluster_dynamic_v4_S0.5_w9x3d_loose'
# mode2 = 'ens_cluster_dynamic_v4_S0.5_w9x4d_loose'
# mode1 = 'ens_cluster_dynamic_v3b_S0.5_w9'
# mode1 = 'ens_cluster_dynamic_v4_S0.5_w9x3d_loose'

# mode1 = 'dynamic_search_ens_clip2_v2_no_segment'
mode1 = 'dynamic_search_ens_clip2_v2'

DEBUG = True

DEFAULT_ASSET = 'NG'
# QUANTITY_MULTIPLIER = 1
QUANTITY_MULTIPLIER = 1
TRADE_ALSO_MODE2 = True
TRADE_ALSO_MODE2 = False

KILL_BATCH_TRADER_BEFORE_CALL = True

# cmd = f'python {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --second_mode {mode2} --asset {DEFAULT_ASSET}'
cmd_original = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_both_strats = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --second_mode {mode2} --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER}'

cmd_thursday = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode2} --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_thursday_both_strats = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode2} --second_mode {mode1} --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER}'

cmd_look_for_new_strats = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode dynamic_search_ens_clip2_v2_no_segment --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_look_for_new_strats_executer = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode dynamic_search_ens_clip2_v2_no_segment --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER} --execute_strategy_only True'
cmd_look_for_new_strats_executer_v1_dynamic = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode dynamic_search_ens_clip2_v1_dynamic --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER} --execute_strategy_only True'
cmd_look_for_new_strats_executer_v1 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode dynamic_search_ens_clip2_v1 --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER} --execute_strategy_only True'
cmd_look_for_new_strats_executer_v2 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode dynamic_search_ens_clip2_v2_no_segment --asset {DEFAULT_ASSET} --quantity_multiplier {QUANTITY_MULTIPLIER} --execute_strategy_only True'

############### DECIDE WHICH CMD ##################
# chosen_cmd = cmd_both_strats if TRADE_ALSO_MODE2 else cmd_original
chosen_cmd = cmd_look_for_new_strats
chosen_cmd_executer = cmd_look_for_new_strats_executer
chosen_cmd_executer = cmd_look_for_new_strats_executer_v1_dynamic
##################################################

cmd_cancel_contradictions = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --asset {DEFAULT_ASSET} --only_cancel_contradictions True'
cmd_position_fix_mode1 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --asset {DEFAULT_ASSET} --only_fix True --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_position_fix_mode2 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode2} --asset {DEFAULT_ASSET} --only_fix True --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_update_actual_positions_csv = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode1} --asset {DEFAULT_ASSET} --only_actual_positions_update True --quantity_multiplier {QUANTITY_MULTIPLIER}'
cmd_update_actual_positions_csv_mode2 = f'{PYTHON_INTERPRETER} {PROJECT_ROOT}/Algo/trading/batch_trader.py --mode {mode2} --asset {DEFAULT_ASSET} --only_actual_positions_update True --quantity_multiplier {QUANTITY_MULTIPLIER}'

import os
batch_trader_killer_script = os.path.join(PROJECT_ROOT, 'Algo', 'Wrapper', 'utils', 'stop_python_batch_trader_regex.ps1')
cmd_kill_batch_trader = f'powershell {batch_trader_killer_script}'

batch_trader_utc_run_times = [
                                # night
                                dtdt(1990,6,28,0,30),dtdt(1990,6,28,2,20),dtdt(1990,6,28,2,45),
                                # early morning
                                dtdt(1990,6,28,5,45),dtdt(1990,6,28,6,15),
                                dtdt(1990,6,28,6,25),dtdt(1990,6,28,6,35),
                                dtdt(1990,6,28,6,45),dtdt(1990,6,28,6,55),
                                dtdt(1990,6,28,7,5),dtdt(1990,6,28,7,10),
                                dtdt(1990,6,28,7,25),dtdt(1990,6,28,7,35),

                                # mid morning
                                dtdt(1990,6,28,8,5),dtdt(1990,6,28,8,22),
                                dtdt(1990,6,28,8,30),dtdt(1990,6,28,8,35),
                                dtdt(1990,6,28,8,40),dtdt(1990,6,28,8,50),
                                dtdt(1990,6,28,9,0),dtdt(1990,6,28,9,15),
                                dtdt(1990,6,28,9,45),dtdt(1990,6,28,10,15),

                                # early noon
                                dtdt(1990,6,28,11,5),dtdt(1990,6,28,11,15),
                                dtdt(1990,6,28,11,30),dtdt(1990,6,28,11,45),
                                # noon
                                dtdt(1990,6,28,12,5),dtdt(1990,6,28,12,15),
                                dtdt(1990,6,28,12,25),dtdt(1990,6,28,12,40),
                                dtdt(1990,6,28,13,15),dtdt(1990,6,28,13,40),
                                dtdt(1990,6,28,16,50),dtdt(1990,6,28,17,10),
                                dtdt(1990,6,28,17,50),dtdt(1990,6,28,18,22),
                                  ]

position_fix_utc_run_times = [
                                dtdt(1990,6,28,2,50),dtdt(1990,6,28,6,30),dtdt(1990,6,28,7,15),
                                dtdt(1990,6,28,8,7),dtdt(1990,6,28,8,25),dtdt(1990,6,28,8,55),
                                dtdt(1990,6,28,11,10),dtdt(1990,6,28,11,30),
                                dtdt(1990,6,28,12,0),
                                dtdt(1990,6,28,12,15),dtdt(1990,6,28,12,25),dtdt(1990,6,28,12,40),
                                dtdt(1990,6,28,13,10),dtdt(1990,6,28,13,20),
                                dtdt(1990,6,28,13,40),dtdt(1990,6,28,17,50),dtdt(1990,6,28,18,22),
                                  ]

batch_trader_local_run_times = [convert_timezone(x,'UTC',LOCAL_TZ_NAME) for x in batch_trader_utc_run_times]
position_fix_local_run_times = [convert_timezone(x,'UTC',LOCAL_TZ_NAME) for x in position_fix_utc_run_times]

def wrap_check_call(cmd,shell=True,
                    kill_batch_trader_before=KILL_BATCH_TRADER_BEFORE_CALL):
    try:
        print(f'Running: {cmd}')
        if kill_batch_trader_before:
            print('about to kill batch_trader: ',cmd_kill_batch_trader)
            check_call(cmd_kill_batch_trader, shell=shell)
            time.sleep(1)
        custom_check_call(cmd,shell,timeout=60*60,
                          kill_all_trader_scripts=False)
    except Exception as e:
        print(f'Error in wrap_check_call: {e}... SKIPPING')

def run_batch_trader(run_positions_fix_after=True):
    try:
        set_paper_mode()
        try:
            # kill_script_with_powershell('batch_trader')
            pass
            # kill_script_with_powershell('trading_utils')
        except Exception as e:
            print('couldnt kill batch_trader:',e)
        wrap_check_call(chosen_cmd, shell=True)
        if run_positions_fix_after:
            run_batch_trader_positions_fixer()
        # kill_script_with_powershell('batch_trader')
    except Exception as e:
        print(f'Error in run_batch_trader: {e}... SKIPPING')
    print(f'Finished run_batch_trader')


def run_batch_trader_executer(chosen_cmd_override=None):
    try:
        set_paper_mode()
        cmd_to_run = chosen_cmd_override or chosen_cmd_executer
        wrap_check_call(cmd_to_run, shell=True)
    except Exception as e:
        print(f'Error in run_batch_trader: {e}... SKIPPING')
    print(f'Finished run_batch_trader')

def run_batch_trader_executer_double():
    try:
        set_paper_mode()
        wrap_check_call(cmd_look_for_new_strats_executer, shell=True)
        wrap_check_call(cmd_look_for_new_strats_executer_v1_dynamic, shell=True)
    except Exception as e:
        print(f'Error in run_batch_trader: {e}... SKIPPING')
    print(f'Finished run_batch_trader')

def run_batch_trader_executer_triple():
    try:
        set_paper_mode()
        wrap_check_call(cmd_look_for_new_strats_executer, shell=True)
        wrap_check_call(cmd_look_for_new_strats_executer_v1_dynamic, shell=True)
        wrap_check_call(cmd_look_for_new_strats_executer_v1, shell=True)
    except Exception as e:
        print(f'Error in run_batch_trader: {e}... SKIPPING')
    print(f'Finished run_batch_trader')


def run_batch_trader_C025():
    set_paper_mode()
    wrap_check_call(cmd_thursday, shell=True)

def run_batch_trader_positions_fixer(asset_override=None,num_of_repeats=2):
    try:
        set_paper_mode()
        # run_batch_trader_actual_positions_update(asset_override) # todo
        # set_paper_mode()
        cmds = [cmd_position_fix_mode1]
        if TRADE_ALSO_MODE2:
            cmds.append(cmd_position_fix_mode2)
        cmds += [cmd_cancel_contradictions]
        cmds = cmds*num_of_repeats
        for cmd in cmds:
            if asset_override is not None:
                cmd = cmd.replace(f'--asset {DEFAULT_ASSET}','--asset '+asset_override)
            if DEBUG:
                print('inside run_batch_trader_positions_fixer calling:',cmd)
            wrap_check_call(cmd, shell=True)
    except Exception as e:
        print(f'Error in run_batch_trader_positions_fixer: {e}... SKIPPING')
    print(f'Finished run_batch_trader_positions_fixer')

def run_batch_trader_actual_positions_update(asset_override=None):
    set_paper_mode()
    cmds = [cmd_update_actual_positions_csv]
    if TRADE_ALSO_MODE2:
        cmds.append(cmd_update_actual_positions_csv_mode2)
    for cmd in cmds:
        if asset_override is not None:
            cmd = chosen_cmd.replace(f'--asset {DEFAULT_ASSET}','--asset '+asset_override)
        if DEBUG:
            print('calling:',cmd)
        wrap_check_call(cmd, shell=True)
    print(f'Finished run_batch_trader_actual_positions_update')

def set_schedule():
    for t in batch_trader_local_run_times:
        schedule.every(1).day.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v2))
        # schedule.every(1).day.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1))
        # schedule.every(1).day.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1_dynamic))
        # schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader_executer_double)
        # schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader)
        # schedule.every(1).day.at(t.strftime('%H:%M:10')).do(run_batch_trader_positions_fixer)

    # for t in position_fix_local_run_times: # TODO postponed until the order cancelling is solved  Nov 24
    #     schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader_positions_fixer)

def set_schedule_hybrid():
    for t in batch_trader_local_run_times:
        # no_segments (v2) evey monday / thursday
        schedule.every().monday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v2))
        schedule.every().monday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1))
        schedule.every().thursday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v2))
        schedule.every().thursday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1))

        # with segments (v1) every tuesday / friday
        schedule.every().tuesday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1))
        schedule.every().friday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1))

        # v1_dynamic (endDate) every wednesday
        schedule.every().wednesday.at(t.strftime('%H:%M')).do(lambda: run_batch_trader_executer(cmd_look_for_new_strats_executer_v1_dynamic))

def set_schedule_only_update():
    # for t in batch_trader_local_run_times:
    #     schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader)
    for t in position_fix_local_run_times:
        schedule.every(1).day.at(t.strftime('%H:%M')).do(run_batch_trader_actual_positions_update)

#
# run_batch_trader()
# Days = [1,4]
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v1)
# Days = [0,3]
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v2)
# Days = [2]
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v1_dynamic)
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v1)
# raise
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v1_dynamic)
# run_batch_trader_executer(cmd_look_for_new_strats_executer_v1)
#######################################################################3
# set_schedule_only_update()
set_schedule_hybrid()
#######################################################################3
while True:
    utc_now = dtdt.utcnow()
    try:
        schedule.run_pending()
    except Exception as e:
        print(f'Scheduler faced an Error at {utc_now}: {e}')
        print('Skipping this iteration')

    print(f'utc_now = {utc_now}...going to sleep for 5 minutes')
    if utc_now.minute % 5 != 0:
        time.sleep(60*(5-utc_now.minute % 5))
    else:
        time.sleep(2*60)
    # time.sleep(30)