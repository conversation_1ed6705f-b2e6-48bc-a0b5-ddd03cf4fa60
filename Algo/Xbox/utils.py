import os
import pandas as pd
from datetime import datetime as dtdt
from datetime import timedelta as td

def generate_last_3_months_full_files():
    home_d = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\Full_Files"
    d = os.path.join(home_d,"Full")
    last3m_d = os.path.join(home_d,"Last3M")

    for f in os.listdir(d):
        csv = os.path.join(d,f)
        df = pd.read_csv(csv,parse_dates=['forecast_time'])
        df = df.loc[df['forecast_time']>=dtdt.now()-td(days=90)]
        df.to_csv(os.path.join(last3m_d,f) ,index=False)


if __name__ == '__main__':
    generate_last_3_months_full_files()