import sys
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time
import os

from Algo.Utils.files_handle import get_degdays_path,get_x_diffs_path
from Algo.Viasualization.visualize_live_degdays import MODEL_GAPS


def create_x_values_new(calc_start,model='EPS',ft_type='GDD',weights=['population_US_weight'],
                       use_full=False,base_on_existing=False):
    csv = get_degdays_path(model,'live' if not use_full else 'full')
    outpath = get_x_diffs_path(model,'v2.0_0Z')

    original_df = pd.DataFrame()
    if base_on_existing:
        try:
            original_df = pd.read_csv(outpath,parse_dates=['date']).dropna(how='any')
            last_fcst_t = min(original_df['date'].max(), original_df.loc[original_df['date'].diff().shift(-1).dt.total_seconds()/(3600*24) > 3]['date'].max())
            calc_start = max(calc_start,last_fcst_t-td(days=5))
        except:
            print ('No existing file found, building from new ')

    df = pd.read_csv(csv,parse_dates=['forecast_time','validation_day'])
    df = df[(df['forecast_time']>=calc_start)&(df['weight'].isin(weights))]

    df['days_ahead'] = (df['validation_day'] - df['forecast_time']).dt.days.astype('int16')
    results_df = pd.DataFrame()
    for min_day,max_day in [(0,15),(0,7),(7,15),(11,15)]:
        df2 = df[(df['days_ahead']>=min_day)&(df['days_ahead']<=max_day)]
        if (max_day - df2.groupby('forecast_time').max()['days_ahead']).max() > 3:
            good_forecast_times = df2.groupby('forecast_time').max()['days_ahead'][(max_day- df2.groupby('forecast_time').max()['days_ahead']) <= 3].index.tolist()
            df2 = df2[df2['forecast_time'].isin(good_forecast_times)]
        df2 = df2.groupby(['forecast_time','weight','feature_type']).mean().reset_index()

        if ft_type == 'GDD':
            columns_for_pivot = ['weight']
        else:
            columns_for_pivot = ['weight','feature_type']

        pivot_df = pd.pivot_table(df2,values='Value',index=['forecast_time'],columns=columns_for_pivot,aggfunc=np.mean)
        pivot_df = pivot_df[[x for x in list(pivot_df) if (ft_type if ft_type != 'GDD' else '') in x]]
        if ft_type != 'GDD':
            pivot_df.columns = [str(k[0])+'_'+str(k[1]) for k in pivot_df.columns]
        else:
            pivot_df *= 2
        pivot_df_diff1 = pivot_df.diff(1).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev1_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
        pivot_df_diff2 = pivot_df.diff(2).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev2_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
        pivot_df_diff3 = pivot_df.diff(3).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev3_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
        pivot_df_diff4 = pivot_df.diff(4).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev4_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})

        pivot_df_diff1prev1 = pivot_df.shift(1).diff(1).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_Prev1-Prev2_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
        pivot_df_diff2prev1 = pivot_df.shift(1).diff(2).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_Prev1-Prev3_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
        final_df = pivot_df.rename(columns={k:'Value_0Z_%s_%s'%(k,model) for k in list(pivot_df)})
        for other in [pivot_df_diff1,pivot_df_diff2,pivot_df_diff3,pivot_df_diff4,
                      pivot_df_diff1prev1,pivot_df_diff2prev1]:
            final_df = final_df.merge(other,left_index=True,right_index=True)

        final_df = final_df.drop([x for x in list(final_df) if 'Prev3' in x or 'Prev4' in x],axis=1)

        final_df = final_df.reset_index()
        final_df['date'] = pd.to_datetime((final_df['forecast_time'] + td(hours=12)).dt.date)+td(hours=8)
        final_df_0z = final_df[final_df['forecast_time'].dt.hour == 0].drop(['forecast_time'],axis=1)
        final_df_12z = final_df[final_df['forecast_time'].dt.hour == 12].rename(columns={k:k.replace('0Z','12Z') for k in list(final_df)}).drop(['forecast_time'],axis=1)
        final_df = final_df_0z.merge(final_df_12z,on=['date'],how='outer')
        if results_df.shape[0] == 0:
            results_df = final_df
        else:
            results_df = final_df.merge(results_df,on=['date'],how='outer')
    results_df = results_df[['date']+[x for x in list(results_df) if 'Value_' not in x and x != 'date']].sort_values('date')
    if base_on_existing and original_df.shape[0] > 0:
        results_df = original_df.merge(results_df,on=list(original_df),how='outer').sort_values('date').drop_duplicates(subset=['date'])
    results_df = results_df.round(2)
    results_df.to_csv(outpath,index=False)
    return results_df




def create_x_values_v3(calc_start,model='EPS',ft_type='GDD',weights=['population_US_weight'],
                       use_full=False,base_on_existing=False):
    csv = get_degdays_path(model,'live' if not use_full else 'full')
    outpath = get_x_diffs_path(model,'v3.0_0Z')

    original_df = pd.DataFrame()
    if base_on_existing:
        try:
            original_df = pd.read_csv(outpath,parse_dates=['date']).dropna(how='any')
            last_fcst_t = original_df['date'].max()
            calc_start = max(calc_start,last_fcst_t-td(days=5))
        except:
            print ('No existing file found, building from new ')

    df = pd.read_csv(csv,parse_dates=['forecast_time','validation_day'])
    df = df[(df['forecast_time']>=calc_start)&(df['weight'].isin(weights))]

    df['days_ahead'] = (df['validation_day'] - df['forecast_time']).dt.days.astype('int16')

    validation_days_to_calc_map = {'EPSCO': [(0, 15), (0, 7), (7, 15), (11, 15)],
                            'EC': [(0, 15), (5, 12),(7,15),(11, 15),(13,15)]}
    # assert model in validation_days_to_calc_map.keys(), 'Model not supported'
    validation_days_to_calc = validation_days_to_calc_map.get(model, validation_days_to_calc_map['EPSCO'])
    results_df = pd.DataFrame()
    for aggregation_method in ['normal','daily']:
        for min_day,max_day in validation_days_to_calc:
            df2 = df[(df['days_ahead']>=min_day)&(df['days_ahead']<=max_day)]
            if (max_day - df2.groupby('forecast_time').max()['days_ahead']).max() > 3:
                good_forecast_times = df2.groupby('forecast_time').max()['days_ahead'][(max_day- df2.groupby('forecast_time').max()['days_ahead']) <= 3].index.tolist()
                df2 = df2[df2['forecast_time'].isin(good_forecast_times)]
            df2 = df2.groupby(['forecast_time','weight','feature_type']).mean().reset_index()

            if ft_type == 'GDD':
                columns_for_pivot = ['weight']
            else:
                columns_for_pivot = ['weight','feature_type']

            pivot_df = pd.pivot_table(df2,values='Value',index=['forecast_time'],columns=columns_for_pivot,aggfunc=np.mean)
            pivot_df = pivot_df[[x for x in list(pivot_df) if (ft_type if ft_type != 'GDD' else '') in x]]
            if ft_type != 'GDD':
                pivot_df.columns = [str(k[0])+'_'+str(k[1]) for k in pivot_df.columns]
            else:
                pivot_df *= 2
            if aggregation_method == 'daily':
                models_per_day = int(24 / MODEL_GAPS[model])
                pivot_df_diff1 = pivot_df.rolling(models_per_day,models_per_day).mean().diff(models_per_day).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev1D_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff2 = pivot_df.rolling(2*models_per_day,2*models_per_day).mean().diff(2*models_per_day).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev2D_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff3 = pivot_df.rolling(3*models_per_day,3*models_per_day).mean().diff(3*models_per_day).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev3D_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff4 = pivot_df.rolling(4*models_per_day,4*models_per_day).mean().diff(4*models_per_day).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev4D_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
            else:
                pivot_df_diff1 = pivot_df.diff(1).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev1_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff2 = pivot_df.diff(2).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev2_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff3 = pivot_df.diff(3).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev3_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff4 = pivot_df.diff(4).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_last-Prev4_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})

                pivot_df_diff1prev1 = pivot_df.shift(1).diff(1).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_Prev1-Prev2_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
                pivot_df_diff2prev1 = pivot_df.shift(1).diff(2).rename(columns={k: 'diff_0Z_%s-%s'%(min_day,max_day)+'_Prev1-Prev3_%s'%model+'_%s'%k.replace('_weight','') for k in list(pivot_df)})
            if aggregation_method == 'daily':
                final_df = pivot_df.rolling(models_per_day,models_per_day).mean().rename(columns={k: 'Value_0Z_%s-%sD_%s_%s' % (min_day,max_day,k, model) for k in list(pivot_df)})
            else:
                final_df = pivot_df.rename(columns={k:'Value_0Z_%s-%s_%s_%s'%(min_day,max_day,k,model) for k in list(pivot_df)})
            for other in [pivot_df_diff1,pivot_df_diff2,pivot_df_diff3,pivot_df_diff4]+\
                          [pivot_df_diff1prev1,pivot_df_diff2prev1]*(aggregation_method=='normal'):
                final_df = final_df.merge(other,left_index=True,right_index=True)

            final_df = final_df.reset_index()
            final_df['date'] = pd.to_datetime((final_df['forecast_time'] + td(hours=12)).dt.date)+td(hours=8)
            final_df_0z = final_df[final_df['forecast_time'].dt.hour == 0].drop(['forecast_time'],axis=1)
            final_df_12z = final_df[final_df['forecast_time'].dt.hour == 12].rename(columns={k:k.replace('0Z','12Z') for k in list(final_df)}).drop(['forecast_time'],axis=1)
            final_df = final_df_0z.merge(final_df_12z,on=['date'],how='outer')
            if results_df.shape[0] == 0:
                results_df = final_df
            else:
                results_df = final_df.merge(results_df,on=['date'],how='outer')
    results_df = results_df[['date']+[x for x in list(results_df) if 'Value_' not in x and x != 'date']].sort_values('date')
    if base_on_existing and original_df.shape[0] > 0:
        results_df = original_df.merge(results_df,on=list(original_df),how='outer').sort_values('date').drop_duplicates(subset=['date'])
    results_df = results_df.round(2)
    results_df.to_csv(outpath,index=False)
    return results_df

def main_x_values_wrapper():
    create_x_values_new(calc_start=dtdt(2022, 1, 1), model='EPSCO', use_full=True,
                        base_on_existing=True, ft_type='GDD')
    # for EC we want to have daily values too which requires v3
    create_x_values_v3(dtdt(2024, 11, 15), model='EC',
                       ft_type='GDD', weights=['population_US_weight'],
                       use_full=True, base_on_existing=True)


if __name__ == '__main__':
    # csv = get_degdays_path('EPSCO')
    # df = pd.read_csv(csv)
    # results = create_x_values_v3(calc_start=dtdt(2022,1,1),model='EPSCO',use_full=False,
    #                               base_on_existing=True)
    create_x_values_v3(dtdt(2024,11,15), model='EC', ft_type='GDD', weights=['population_US_weight'],
                        use_full=True, base_on_existing=False)
    nn = 1
    # todo - try to run it with wind features? all models!