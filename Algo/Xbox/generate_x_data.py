### RUNNING MODE
MODE = 'local'
assert MOD<PERSON> in ['local','linux']

import sys
if "..." not in sys.path:
    sys.path.append("...") # Adds higher directory to python modules path.
if MODE == 'local':
    sys.path.append(r"C:\TWS API\Algo")
else:
    sys.path.append("/mnt/volume_100G/home/<USER>/TWS/DellAlgo5.5_Linux/Algo")
    sys.path.append("/mnt/volume_100G/home/<USER>/TWS/DellAlgo5.5_Linux")

from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import os
from Algo.Utils.files_handle import HOME
from Algo.Utils.general import safe_write_to_csv
from Algo.General.split_degdays import get_last_date_of_xdata_file,split_xdata_df_to_subdirs,unite_monthly_xdata_files

try:
    from Algo.Viasualization.visualize_live_degdays import _last_available_model, _model_availability_time, _prev_last_model,MODELS_HORIZONS,MODEL_GAPS
    from Algo.Data_Processing.gdd_handle import *
except:
    from Viasualization.visualize_live_degdays import _last_available_model, _model_availability_time, _prev_last_model,MODELS_HORIZONS


END_DATE = dtdt(2030,6,28)
GAP = 6 # for GFS PARA GEFS ... EC needs adjustments
LAST_MODEL_HOUR = 0
MODELS_WITH_6Z = ['PARA','PARACO','GEFS','GFSv16','CFS','CFSCO','ICON']
MODELS_WITHOUT_12Z = ['GFSCO','GEFSL','EPS45']
prev_model_hour = 18
DAYS_AHEAD_DEFAULT = list(range(0,46))
MONTHS = range(1,13) #[10, 11, 12, 1, 2, 3, 4]
WEIGHTS = ['population_US_weight','basic_NG_weight']
UNITE_ONLY_LAST_MONTHS = True



d1_march = os.path.join(HOME,"degdays_archive","Live","March19")
d2_may = os.path.join(HOME,"degdays_archive","Live","May19_Before_CDD")
d3_summer19 = os.path.join(HOME,"degdays_archive","Live","until_11.2019")
d4_last_week = os.path.join(HOME,"degdays_archive","Live","last_week_archive")
d5_jan20 = os.path.join(HOME,"degdays_archive","Live","Jan20")
d6_until_Nov19 = os.path.join(HOME,"degdays_archive","Live","until_11.2019")
d7_feb20_cfs = os.path.join(HOME,"degdays_archive","Live","Feb20")
d8_march20 = os.path.join(HOME,"degdays_archive","Live","March20")
d9_apr20 = os.path.join(HOME,"degdays_archive","Live","Apr20")
d10_jul20 = os.path.join(HOME,"degdays_archive","Live","Jul20")
d11_sep20 = os.path.join(HOME,"degdays_archive","Live","Sep20")
d12_oct20 = os.path.join(HOME,"degdays_archive","Live","Oct20")
d13_nov20 = os.path.join(HOME,"degdays_archive","Live","Nov20")
d14_dec20 = os.path.join(HOME,"degdays_archive","Live","Dec20")
d15_jan21 = os.path.join(HOME,"degdays_archive","Live","Jan21")
d16_feb21 = os.path.join(HOME,"degdays_archive","Live","Feb21")
d17_mar21 = os.path.join(HOME,"degdays_archive","Live","Mar21")
d18_apr21= os.path.join(HOME,"degdays_archive","Live","Apr21")
d19_may21 = os.path.join(HOME,"degdays_archive","Live","May21")
d20_jun21 = os.path.join(HOME,"degdays_archive","Live","Jun21")

historic_completion = os.path.join(HOME,"degdays_archive","Live","historic_completion")
live_dir = os.path.join(HOME,"degdays_archive","Live")
live_dir_ec2 = os.path.join(HOME,"degdays_archive","Live","Live_from_ec2")
live_dir_wind = os.path.join(HOME,"degdays_archive","Live","Live_Wind")

DEGDAYS_FULL_OUTDIR = os.path.join(HOME,"degdays_archive","Full_Files","Full")
DEGDAYS_LAST3_OUTDIR = os.path.join(HOME,"degdays_archive","Full_Files","Last3M")

WIND_DEGDAYS_FULL_OUTDIR = os.path.join(HOME,"degdays_archive","Wind_Full_Files")
WIND_DEGDAYS_LAST3_OUTDIR = os.path.join(HOME,"degdays_archive","Wind_Full_Files")

DEFAULT_FULL_FILES_OUTDIR = DEGDAYS_FULL_OUTDIR if not UNITE_ONLY_LAST_MONTHS else DEGDAYS_LAST3_OUTDIR

coffee_out_d = os.path.join(HOME,"COFFEE","degdays_archive","Full_Files")
wind_out_d = os.path.join(HOME,"degdays_archive","Wind_Full_Files")


if MODE == 'linux':
    out_d = "/mnt/volume_100G/home/<USER>/degdays_csvs/Full_Files/"

MODELS_TO_ALLOW_NANS = ['GFSv16','CFS','CFSCO','GEFS']
EXTENDED_MODELS = ['GFS','PARA','GEFS','PARACO']
NANS_REPLACING_DICT = {"PARA":"GFS","GFS":"PARA","PARACO":"PARA","GFSCO":"GFS"}
COFFEE_SUFFIXES = ['v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof']
WIND_SUFFIXES = ['v14_WindUS', 'v15_WindTX']
SUFFIXES_12Z = ['v8_12Z','v12_BArabica12Z','v14_WindUS', 'v15_WindTX']

# this substrings will not cause dropping of rows in the daily calc
SUBSTRINGS_TO_ALLOW_NANS = ['Value_0-0','diff_0-0','5-8','28-','35-','12-13','14-16','-12','12-12','9-13','13-13',
                            '10-13','11-15','14-14','15-15','16-16','0-13','0-16','8-16']

GDD_WEIGHTS = {'CDD':1,'HDD':1}

outfile_template = "Full_degdays_Full"

#GFS,
FULL_MODELS = ['PARA','GEFS','GEFSL','GEPS','GEM','PARACO','GFSCO','GFSv16',
               'EC','EPS','GEMCO','CFS','CFSCO','ICON','GEFS35','GEFSCO35','EPS45',
               'EPSCO']


LAPTOP_MODEL_DIR_TEMPLATE = os.path.join(HOME,"degdays_archive","Live","$MMYY$")

LAPTOP_MODEL_DIR_TEMPLATE_WIND = os.path.join(HOME,"degdays_archive","","Live","Wind_$MMYY$")
LAPTOP_MODEL_DIR_TEMPLATE_COFFEE = os.path.join(HOME,"COFFEE","degdays_archive","","Live","$MMYY$")
LAPTOP_MODELS_DIRS = [d1_march,d2_may,d3_summer19,d4_last_week,d6_until_Nov19,
                        historic_completion,
                        ]+\
                        [d for d in [LAPTOP_MODEL_DIR_TEMPLATE.replace("$MMYY$",t.strftime('%b%y'))
                                     for t in pd.date_range('2020-01-01','2030-01-01' , freq='1M')-pd.offsets.MonthBegin(1)
                                     ] if os.path.exists(d)]+[live_dir_ec2]

LINUX_MODELS_DIR = ["/mnt/volume_100G/home/<USER>/degdays_csvs/Live_archive_Mar19/",
                    "/mnt/volume_100G/home/<USER>/degdays_csvs/Live_archive_Oct19/",
                    "/mnt/volume_100G/home/<USER>/degdays_csvs/Live_archive_Jan20/",
                    "/mnt/volume_100G/home/<USER>/degdays_csvs/Live_archive_Apr20/",
                    "/mnt/volume_100G/home/<USER>/degdays_csvs/Live_archive_July20/",
                    "/mnt/volume_100G/home/<USER>/degdays_csvs/Live/"][-1:]

COFFEE_DIRS = [d for d in [LAPTOP_MODEL_DIR_TEMPLATE_COFFEE.replace("$MMYY$",t.strftime('%b%y'))
                                     for t in pd.date_range('2020-01-01','2030-01-01' , freq='1M')-pd.offsets.MonthBegin(1)
                                     ] if os.path.exists(d)] + [os.path.join(HOME,"COFFEE","degdays_archive","Live","Live_from_ec2")]

WIND_DIRS = [d for d in [LAPTOP_MODEL_DIR_TEMPLATE_WIND.replace("$MMYY$",t.strftime('%b%y'))
                                     for t in pd.date_range('2020-01-01','2030-01-01' , freq='1M')-pd.offsets.MonthBegin(1)
                                     ] if os.path.exists(d)] + [os.path.join(HOME,"degdays_archive","Live","Live_Wind")]


if MODE == 'local':
    MODELS_DIRS = LAPTOP_MODELS_DIRS
elif MODE == 'linux':
    MODELS_DIRS = LINUX_MODELS_DIR
else:
    raise AssertionError()

def unite_model_degdays(model, paths_lst,outpath,t_start=dtdt(2017,10,1),t_end=dtdt(2030,1,1)):
    """
    unite the csvs in the paths into full degdays file
    :param model:
    :param paths_lst:
    :param t_start:
    :param t_end:
    :return:
    """
    final_df = pd.DataFrame()
    for csv in paths_lst:
        if os.path.exists(csv):
            try:
                df = pd.read_csv(csv) #,parse_dates=['forecast_time','validation_day'])
                df['forecast_time'] = pd.to_datetime(df['forecast_time'],errors='coerce')
                df['validation_day'] = pd.to_datetime(df['validation_day'], errors='coerce')
            except Exception as e:
                print ('couldnt load file %s due to Error: \n%s'%(csv,e))
            if 'Z' in list(df):
                df['Z'] = pd.to_numeric(df['Z'], errors='coerce')

            if final_df.shape[0] == 0:
                final_df = df
            else:
                try:
                    final_df = final_df.merge(df, on=list(set(list(final_df)).intersection(set(list(df)))),how="outer")
                except Exception as e:
                    print ('couldnt merge old with new, Error = %s'%e)
                    aa = 1
        else:
            continue
    try:
        final_df = final_df[(final_df['forecast_time'] >= t_start) & (final_df['forecast_time'] <= t_end)]
    except:
        bb = 0
        raise
    final_df['model'] = model
    final_df = final_df.sort_values('forecast_time')
    final_df['feature_type'] = final_df['feature_type'].fillna('HDD')
    if os.path.exists(outpath) and UNITE_ONLY_LAST_MONTHS:
        pass
        # we used to try merge it but it's still heavy.
        # original_df = pd.read_csv(outpath,parse_dates=['forecast_time','validation_day'])
        # final_df = final_df[final_df['forecast_time']>(original_df['forecast_time'].max()-td(hours=24))]
        # original_df = original_df[original_df['forecast_time'] <= (original_df['forecast_time'].max()-td(hours=24))]
        # final_df = pd.concat([original_df,final_df],axis=0)
    final_df.to_csv(outpath, index=False)
    return final_df

def get_multimodel_df(models=FULL_MODELS,
                      dirs=MODELS_DIRS,out_d= DEFAULT_FULL_FILES_OUTDIR,special_file_name=None,
                      unite_only_last_months=UNITE_ONLY_LAST_MONTHS,
                      suffix='v8_12Z'):
    out_d = DEGDAYS_FULL_OUTDIR if not unite_only_last_months else DEGDAYS_LAST3_OUTDIR

    ft_type = 'degdays'
    if special_file_name == 'wind':
        ft_type = 'wind'
        out_d = WIND_DEGDAYS_FULL_OUTDIR if not unite_only_last_months else WIND_DEGDAYS_LAST3_OUTDIR
    if not os.path.exists(out_d):
        os.makedirs(out_d)

    if special_file_name == 'wind' and dirs == MODELS_DIRS:
        dirs = WIND_DIRS

    multimodel_df = pd.DataFrame()
    for model in models:
        try:
            if model == 'GEPS':
                paths = [os.path.abspath(os.path.join(d, f"live_{ft_type}_{model}_Pmean.csv")) for d in dirs]
            elif model in ['EPS2','EC2'] or ('v8' in suffix and model == 'EPSCO'):
                suffix = '_grib'
                model_for_path = model.replace('2', '')
                paths = [os.path.abspath(os.path.join(d, f"live_{ft_type}_{model_for_path}{suffix}.csv")) for d in dirs]
            else:
                paths = [os.path.abspath(os.path.join(d, f"live_{ft_type}_{model}.csv")) for d in dirs]
            outpath = os.path.abspath(os.path.join(out_d, f"{outfile_template}_{model}.csv"))
            if special_file_name is not None:
                if special_file_name == 'wind':
                    paths = [x.replace('live_degdays','live_%s'%special_file_name) for x in paths]
            print ('About to unite from the following files: \n %s \n writing to: \n %s'%(paths,outpath))
            if unite_only_last_months and special_file_name is None:
                paths = paths[-3:]
            model_df = unite_model_degdays(model,paths,outpath)
            if multimodel_df.shape[0] == 0:
                multimodel_df = model_df
            else:
                common = list(set(list(model_df)).intersection(set(list(multimodel_df))))
                multimodel_df = multimodel_df.merge(model_df,on=common,how="outer")
        except:
            print(f'WARNING: Failed to unite degdays for model {model}...skipping it')
    multimodel_df['feature_type'] = multimodel_df['feature_type'].fillna('HDD')
    multimodel_df = multimodel_df.sort_values(['forecast_time','validation_day','weight'])
    return multimodel_df


def get_daily_multimodel(multimodel_df,last_model_hour=LAST_MODEL_HOUR,start_date=dtdt(2018,1,1),
                    end_date=dtdt(2020,1,1), ft_type='HDD', include_prev24=True,normalization_df=None):

    multimodel_df = multimodel_df[(multimodel_df['forecast_time'].dt.month.isin(MONTHS)) &
                                  (multimodel_df['feature_type'] == ft_type) & (
                                              multimodel_df['weight'].isin(WEIGHTS))]

    # make sure weights are as expected because we move duplicates
    assert WEIGHTS in [['population_US_weight','basic_NG_weight'],['population_US_weight'],['basic_NG_weight'],
                       ['population_US_weight','basic_NG_weight'], ['NG16_midwest_weight']]

    multimodel_df['fake_forecast_time'] = multimodel_df['forecast_time'] - td(hours=last_model_hour) +\
                                          (td(minutes=1) if include_prev24 else -td(minutes=1))
    multimodel_df['fake_day'] = multimodel_df['fake_forecast_time'].dt.date

    daily_multimodel_df = pd.DataFrame()
    multimodel_df = multimodel_df[(multimodel_df['forecast_time'] >= start_date)&(multimodel_df['forecast_time'] <= end_date)]

    # compress duplicates
    # multimodel_df["ones"] = 1
    # multimodel_df.groupby(["forecast_time", "model"]).sum()["ones"].sort_values()
    common = ["forecast_time","validation_day","feature_type","model"]
    multimodel_df_old = multimodel_df.copy()
    multimodel_df = multimodel_df.drop_duplicates(subset=common,keep='last')
    multimodel_df = multimodel_df.merge(multimodel_df_old.groupby(common).mean()[["Value"]].reset_index(),on=common,suffixes=("_first","_mean"))
    multimodel_df['Value'] = multimodel_df['Value_mean']
    multimodel_df = multimodel_df[[x for x in list(multimodel_df) if '_first' not in x and 'mean' not in x]]

    for fake_day, fake_day_group in multimodel_df.groupby(["fake_day","model","weight","feature_type"]):
        print ("Handling fake_day: %s"%fake_day[0])
        fake_day_df = pd.DataFrame()
        forecast_times = []
        on = ["fake_day", "model", "weight", "feature_type", 'validation_day'] #,'days_ahead']
        for forecast_time,fcst_group in fake_day_group.groupby("forecast_time"):
            fcst_group = fcst_group.rename(columns={"Value":"Value_%sZ"%forecast_time.hour})
            fcst_group = fcst_group[[x for x in list(fcst_group) if x in on or 'Value' in x]]
            if fake_day_df.shape[0] == 0:
                fake_day_df = fcst_group
            else:
                fake_day_df = fake_day_df.merge(fcst_group,on=on,how="outer")
                if fake_day_df.shape[0] > 17:
                    aa = 1
            forecast_times.append(forecast_time)
            aa = 1
        fake_day_df['forecast_time'] = max(forecast_times)+td(hours=GAP if include_prev24 else 0)
        if daily_multimodel_df.shape[0] == 0:
            daily_multimodel_df = fake_day_df
        else:
            common = list(set(list(daily_multimodel_df)).intersection(set(list(fake_day_df))))
            daily_multimodel_df = daily_multimodel_df.merge(fake_day_df,on=common,how="outer")

        aa = 1

    """
    daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['model'] == 'GFS']
    #daily_multimodel_df.to_csv(os.path.join(HOME,"Xdata"," tests","","daily_multi_prev=%s_onlyGFS.csv")%include_prev24,index=False)
    #prev_daily_multimodel_df = pd.read_csv(os.path.join(HOME,"Xdata"," tests","daily_multi_prev=True_onlyGFS.csv"),parse_dates=['forecast_time','validation_day'])
    daily_multimodel_df['forecast_time'] = daily_multimodel_df['forecast_time'].apply(lambda x: x.replace(hour=12))
    prev_daily_multimodel_df['forecast_time'] = prev_daily_multimodel_df['forecast_time'].apply(lambda x: x.replace(hour=12))
    d = prev_daily_multimodel_df.merge(daily_multimodel_df, on=['forecast_time', 'validation_day'],
                                   suffixes=("_prev", "_normal"), how='outer')
    d2 = d[['forecast_time','validation_day']+[x for x in sorted(list(d)) if x not in ['forecast_time','validation_day']]]
    d2 = d2.sort_values(['forecast_time', 'validation_day'])
    """
    #print("INFO :  (Before Hour Filter) daily_multimodel.shape = %s" % str(daily_multimodel_df.shape))

    daily_multimodel_df['forecast_time'] = daily_multimodel_df['forecast_time'].apply(lambda x: x.replace(hour=last_model_hour))
    daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['forecast_time'].dt.hour == last_model_hour]

    # drop first day that is always partial
    daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['forecast_time']>=(start_date+td(days=1))]

    return daily_multimodel_df

def get_expected_days(model,last_model_hour,days_ahead=[1,15]):
    expected_days = (days_ahead[-1] - days_ahead[0])+1  # if model in ['GFS','PARA','GEFS','GEFSL','GEPS']
    if model == 'GEM':
        expected_days = 9
    if model == 'GFS':
        expected_days -=1
    if model in ['GEFS','GEFSL','GEPS','GFS']:
        if last_model_hour == 12:
            expected_days -= 1
    elif model == 'EC':
        expected_days = 8
    elif model == 'EPS':
        expected_days = 13


    return expected_days


def analyze_daily_models(model,daily_multimodel_df,last_model_hour=LAST_MODEL_HOUR,method='sum',
                         days_ahead=DAYS_AHEAD_DEFAULT,prev24=False,weights_csv=None):

    daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['model'] == model]

    # prepare for dropping
    daily_multimodel_df['ones'] = 1
    check_entries = daily_multimodel_df.groupby(["forecast_time","model"]).sum().reset_index()

    # load weights
    if weights_csv is not None:
        weights_df = pd.read_csv(weights_csv)
        if 0 not in list(weights_df['days_ahead']):
            weights_df['days_ahead'] -= 1
        try:
            daily_multimodel_df= daily_multimodel_df.merge(weights_df[['days_ahead', 'Value_%s' % model]], on=['days_ahead'])
        except KeyError:
            weights_df['Value_%s'%model] = 1
            daily_multimodel_df = daily_multimodel_df.merge(weights_df[['days_ahead', 'Value_%s' % model]], on=['days_ahead'])
    else:
        daily_multimodel_df= daily_multimodel_df
        daily_multimodel_df['Value_%s'%model] = 1

    # sort
    daily_multimodel_df = daily_multimodel_df.sort_values(['forecast_time','validation_day'])

    # Add the diffs !
    if model not in EXTENDED_MODELS:
        if last_model_hour in [6,18]:
            last_model_hour -= 6
    if last_model_hour == 0:
        if not prev24:
            daily_multimodel_df['diff_0Z-12Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_12Z']
        else:
            daily_multimodel_df['diff_12Z-0Z'] = daily_multimodel_df['Value_12Z'] - daily_multimodel_df['Value_0Z']
            daily_multimodel_df['diff_18Z-0Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_0Z']

        if model in EXTENDED_MODELS:
            daily_multimodel_df['diff_18Z-12Z'] = daily_multimodel_df['Value_18Z'] - daily_multimodel_df['Value_12Z']
            daily_multimodel_df['diff_18Z-6Z'] = daily_multimodel_df['Value_18Z'] - daily_multimodel_df['Value_6Z']
            daily_multimodel_df['diff_12Z-6Z'] = daily_multimodel_df['Value_12Z'] - daily_multimodel_df['Value_6Z']
            daily_multimodel_df['diff_18Z-3Prev'] = daily_multimodel_df['Value_18Z'] - (daily_multimodel_df[['Value_6Z','Value_12Z']].mean(axis=1))
    elif last_model_hour == 12:
        if not prev24:
            daily_multimodel_df['diff_12Z-0Z'] = daily_multimodel_df['Value_12Z'] - daily_multimodel_df['Value_0Z']
            if model in EXTENDED_MODELS:
                daily_multimodel_df['diff_6Z-18Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_18Z']
                daily_multimodel_df['diff_6Z-0Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_0Z']
                daily_multimodel_df['diff_0Z-18Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_18Z']
                daily_multimodel_df['diff_6Z-3Prev'] = daily_multimodel_df['Value_6Z'] - (daily_multimodel_df[['Value_0Z', 'Value_18Z']].mean(axis=1))
        else:
            daily_multimodel_df['diff_0Z-12Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_12Z']
            if model in EXTENDED_MODELS:
                daily_multimodel_df['diff_6Z-12Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_12Z']
                daily_multimodel_df['diff_18Z-12Z'] = daily_multimodel_df['Value_18Z'] - daily_multimodel_df['Value_12Z']
                daily_multimodel_df['diff_6Z-18Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_18Z']
                daily_multimodel_df['diff_6Z-0Z'] = daily_multimodel_df['Value_6Z'] - daily_multimodel_df['Value_0Z']
                daily_multimodel_df['diff_0Z-18Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_18Z']
                daily_multimodel_df['diff_6Z-3Prev'] = daily_multimodel_df['Value_6Z'] - (daily_multimodel_df[['Value_0Z', 'Value_18Z','Value_12Z']].mean(axis=1))
    elif last_model == 6:
        daily_multimodel_df['diff_0Z-18Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_18Z']
        daily_multimodel_df['diff_0Z-12Z'] = daily_multimodel_df['Value_0Z'] - daily_multimodel_df['Value_12Z']
        daily_multimodel_df['diff_18Z-12Z'] = daily_multimodel_df['Value_18Z'] - daily_multimodel_df['Value_12Z']
        daily_multimodel_df['diff_18Z-6Z'] = daily_multimodel_df['Value_18Z'] - daily_multimodel_df['Value_6Z']
        daily_multimodel_df['diff_0Z-3Prev'] = daily_multimodel_df['Value_0Z'] - (daily_multimodel_df[['Value_12Z', 'Value_18Z']].mean(axis=1))

    # normalize by the given weights / multiply by 1
    diff_cols = [x for x in list(daily_multimodel_df) if 'diff' in x]
    for diff_col in diff_cols:
        daily_multimodel_df[diff_col] *= daily_multimodel_df['Value_%s'%model]

    # Sum the validation days
    if method == 'sum':
        good_forecasts_models_tuples = check_entries[
            check_entries.apply(lambda x: x['ones'] == get_expected_days(x['model'], last_model_hour), axis=1)][
            ['forecast_time', 'model']].apply(lambda x:
                                              (x['model'], x['forecast_time']), axis=1).tolist()
        # filter out bad lines
        daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['days_ahead'].isin(days_ahead)]
        daily_multimodel_df = daily_multimodel_df[daily_multimodel_df.apply(lambda x: (x['model'], x['forecast_time']), axis=1).isin(
                good_forecasts_models_tuples)]
        # take sum
        daily_multimodel_df = daily_multimodel_df.groupby(["forecast_time", "model", "weight", "feature_type"]).sum().reset_index()
    elif method == 'mean':
        good_forecasts_models_tuples = check_entries[check_entries.apply(lambda x: x['ones'] >= get_expected_days(x['model'], last_model_hour), axis=1)][
            ['forecast_time', 'model']].apply(lambda x: (x['model'], x['forecast_time']), axis=1).tolist()
        # filter out bad lines
        daily_multimodel_df = daily_multimodel_df[daily_multimodel_df['days_ahead'].isin(days_ahead)]
        daily_multimodel_df = daily_multimodel_df[daily_multimodel_df.apply(lambda x: (x['model'], x['forecast_time']), axis=1).isin(
                good_forecasts_models_tuples)]
        # take mean
        daily_multimodel_df = daily_multimodel_df.groupby(["forecast_time", "model", "weight", "feature_type"]).mean().reset_index()
    else:
        raise AssertionError("bad method")

    # analyze HR
    d3 = daily_multimodel_df[daily_multimodel_df['model'] == model]
    if not prev24:
        if last_model_hour == 0:
            if model in EXTENDED_MODELS:
                d3['is_hit_0-12'] = np.sign(d3['diff_0Z-12Z']*d3['diff_18Z-12Z']).apply(lambda x: 0 if x < 0 else 1)
                d3['is_hit_0-3Prev'] = np.sign(d3['diff_0Z-12Z']*d3['diff_18Z-3Prev']).apply(lambda x: 0 if x < 0 else 1)
        elif last_model_hour == 12:
            if model in EXTENDED_MODELS:
                d3['is_hit'] = np.sign(d3['diff_12Z-0Z'] * d3['diff_6Z-0Z']).apply(lambda x: 0 if x < 0 else 1)
                d3['is_hit_0-3Prev'] = np.sign(d3['diff_12Z-0Z'] * d3['diff_6Z-3Prev']).apply(lambda x: 0 if x < 0 else 1)
    return d3



def get_hr_of_cols(df,predictor,target,thresh=1):
    quantile = abs(df[predictor]).quantile(1-thresh)
    filter_ind = abs(df[predictor]) > quantile
    df = df[filter_ind]
    is_hit = np.sign(df[predictor] * df[target]).apply(lambda x: 0 if x <0 else 1)
    is_hit = np.sign(df[predictor] * df[target]).apply(lambda x: 0 if x <0 else 1)
    return is_hit.mean(),is_hit,quantile

def _merge_models_xs(models_tuples_for_xfile,add_month_feature=True):
    multimodel_final_xs = pd.DataFrame()
    for model_name, model_df in models_tuples_for_xfile:
        value_cols = [x for x in list(model_df) if 'Value' in x or 'diff' in x]
        renaming_dict = {k: k+"_%s"%model_name for k in value_cols}
        model_df = model_df.rename(columns=renaming_dict)[['forecast_time']+[v for k,v in renaming_dict.items()]]
        if multimodel_final_xs.shape[0] == 0:
            multimodel_final_xs = model_df
        else:
            multimodel_final_xs = multimodel_final_xs.merge(model_df,on=['forecast_time'],how="outer")
    multimodel_final_xs = multimodel_final_xs.sort_values("forecast_time")

    if add_month_feature:
        multimodel_final_xs['month'] = multimodel_final_xs['forecast_time'].dt.month
    return multimodel_final_xs

def fillnans_by_other_runs(daily_multimodel):
    all_val_cols = ['Value_%sZ'%z for z in [0,6,12,18]]
    for col in all_val_cols:
        other3 = daily_multimodel[[x for x in all_val_cols if x != col]]
        daily_multimodel[col][:-100] = daily_multimodel[col].fillna(other3.mean(axis=1))[:-100]
    return daily_multimodel

def fillnans_multimodel(final_multimodel,replacing_dic=NANS_REPLACING_DICT):
    for bad_model, replacing_model in replacing_dic.items():
        cols_to_replace = [x for x in list(final_multimodel) if bad_model in x]
        for col in cols_to_replace:
            replacing_col = col.replace(bad_model,replacing_model)
            if replacing_col in list(final_multimodel):
                final_multimodel[col] = final_multimodel[col].fillna(final_multimodel[replacing_col])
    val_cols = [x for x in list(final_multimodel) if 'Value' in x or 'diff' in x]
    # complete the others with rolling mean
    final_multimodel[val_cols] = final_multimodel[val_cols].fillna(final_multimodel[val_cols].rolling(15,1).mean())
    # now as last resort, fill with mean
    final_multimodel[val_cols] = final_multimodel[val_cols].fillna(final_multimodel[val_cols].mean())
    return final_multimodel

def extract_advances_diffs(multimodel_final_xs,last_model):
    prev1_hour = (last_model - 6)%24
    prev2_hour = (prev1_hour - 6)%24

    try:
        assert 'Value_%sZ_PARACO'%prev1_hour in list(multimodel_final_xs) and 'Value_%sZ_PARA'%prev1_hour in list(multimodel_final_xs), "We dont have PARA or PARACO"
    except:
        a = 1
        raise
    # use 6z / 18z
    multimodel_final_xs['diff_%sZ_PARACO-GEFS'%prev1_hour] = multimodel_final_xs['Value_%sZ_PARACO'%prev1_hour] - multimodel_final_xs[
        'Value_%sZ_GEFS'%prev2_hour]
    multimodel_final_xs['diff_%sZ_PARA-GEFS'%prev1_hour] = multimodel_final_xs['Value_%sZ_PARA'%prev1_hour] - multimodel_final_xs[
        'Value_%sZ_GEFS'%prev2_hour]
    # use 0z / 12z
    multimodel_final_xs['diff_%sZ_PARACO-GEFS'%prev2_hour] = multimodel_final_xs['Value_%sZ_PARACO'%prev2_hour] - multimodel_final_xs[
        'Value_%sZ_GEFS'%prev2_hour]
    multimodel_final_xs['diff_%sZ_PARA-GEFS'%prev2_hour] = multimodel_final_xs['Value_%sZ_PARA'%prev2_hour] - multimodel_final_xs[
        'Value_%sZ_GEFS'%prev2_hour]

    # unite 2 prev models
    multimodel_final_xs['diff_%sZ+%sZ_PARACO-GEFS'%(prev1_hour,prev2_hour)] = multimodel_final_xs[
        ['diff_%sZ_PARACO-GEFS'%prev1_hour, 'diff_%sZ_PARACO-GEFS'%prev2_hour]].mean(axis=1)
    multimodel_final_xs['diff_%sZ+%sZ_PARA-GEFS' % (prev1_hour, prev2_hour)] = multimodel_final_xs[
        ['diff_%sZ_PARA-GEFS' % prev1_hour, 'diff_%sZ_PARA-GEFS' % prev2_hour]].mean(axis=1)
    # unite PARA and PARACO
    multimodel_final_xs['diff_%sZ_PARAACO-GEFS'%prev1_hour] = multimodel_final_xs[
        ['diff_%sZ_PARA-GEFS'%prev1_hour, 'diff_%sZ_PARACO-GEFS'%prev1_hour]].mean(axis=1)
    multimodel_final_xs['diff_%sZ_PARAACO-GEFS'%prev2_hour] = multimodel_final_xs[
        ['diff_%sZ_PARA-GEFS'%prev2_hour, 'diff_%sZ_PARACO-GEFS'%prev2_hour]].mean(axis=1)
    return multimodel_final_xs

def main(last_model = 12,load_from_new=True,models_str = 'GFS+PARA+GEFS',
            method = 'mean',include_prev24 = True,suffix0='take5',ft_type='HDD',
         start=dtdt(2018,1,1),end=dtdt(2020,12,31),write=True, complete_missing_from_new=True,
         days_ahead=None,weights_csv=None,add_month_feature=True,months=MONTHS):

    # File for reference
    backup_file = os.path.join(HOME,"Xdata","daily_multimodel_df_allMonths_partialDates_Full_last=%s_%s.csv")%(last_model,suffix0)

    if load_from_new:
        start = dtdt(2018,10,1)
        multimodel_df = get_multimodel_df()
        daily_multimodel = get_daily_multimodel(multimodel_df,last_model_hour=last_model,include_prev24=include_prev24,
                                                start_date=start,end_date=end,ft_type=ft_type)
        daily_multimodel.to_csv(backup_file,index=False)
    else:
        daily_multimodel = pd.read_csv(backup_file, parse_dates=['forecast_time', 'validation_day', 'fake_day'])
        if complete_missing_from_new:
            multimodel_df = get_multimodel_df()
            new_daily_multimodel = get_daily_multimodel(multimodel_df,last_model_hour=last_model,include_prev24=include_prev24,
                                                start_date=start,end_date=end,ft_type=ft_type)
            daily_multimodel = pd.concat([daily_multimodel,new_daily_multimodel]) #,on=list(daily_multimodel),how="outer")
            daily_multimodel.to_csv(backup_file, index=False)
            a = 1
    # drop dups
    daily_multimodel = daily_multimodel.drop_duplicates(subset=['forecast_time','validation_day','weight','model','feature_type']+\
                                                               ['Value_%sZ'%i for i in range(0,24,6)])
    # fix days_ahead
    #if daily_multimodel['days_ahead'].isna().mean() > 0:
    daily_multimodel['days_ahead'] = (daily_multimodel['validation_day'].dt.date - daily_multimodel['forecast_time'].dt.date).dt.days
    daily_multimodel['days_ahead'][daily_multimodel['days_ahead'] < 0] = 0

    # filter and fill nans
    daily_multimodel = daily_multimodel[daily_multimodel['forecast_time'].dt.month.isin(months)]  # 10,11,4
    daily_multimodel = fillnans_by_other_runs(daily_multimodel)

    if days_ahead is None:
        days_ahead = list(range(1,16)) #range(16) #range(8,16) #[10,11,12,13,14,15,16]
    else:
        assert isinstance(days_ahead, list)

    # loop on models to generate the diffs
    models_tuples_for_xfile = []
    for model in ['PARA','GFS','GEFS','PARACO','EC','EPS','GEM','GEPS','CFS','CFSCO']:
        if model == 'GFS':
            aa = 1
        try:
            d3_model = analyze_daily_models(model,daily_multimodel,last_model_hour=last_model,
                                     method=method,days_ahead=days_ahead,prev24=include_prev24,weights_csv=weights_csv)
        except:
            d3_model = pd.DataFrame()
            continue
        models_tuples_for_xfile.append((model, d3_model))

    if models_str == 'GFS+PARA+GEFS':
        relevant_models = ['GFS','PARA','GEFS']
        models_tuples_for_xfile = [x for x in models_tuples_for_xfile if x[0] in relevant_models]
    elif models_str == 'Full':
        models_tuples_for_xfile = models_tuples_for_xfile
    else:
        raise AssertionError("Bad models_str")
    multimodel_final_xs = _merge_models_xs(models_tuples_for_xfile,add_month_feature)

    # add inter-model features
    multimodel_final_xs = extract_advances_diffs(multimodel_final_xs,last_model)

    multimodel_final_xs = fillnans_multimodel(multimodel_final_xs)

    not_fully_nan_columns = multimodel_final_xs.isna().mean()[multimodel_final_xs.isna().mean()!=1].index.tolist()
    multimodel_final_xs = multimodel_final_xs[not_fully_nan_columns]
    multimodel_final_xs = multimodel_final_xs.dropna()
    multimodel_final_xs_for_write = multimodel_final_xs[[x for x in list(multimodel_final_xs) if x not in ['weight','days_ahead']]]

    suffix = suffix0
    suffix2 = ""
    if include_prev24:
        suffix += '_prev24'
        suffix2 += '_prev24'
    if write:
        xfile = os.path.join(HOME,"Xdata","X_files_GFS_%sZ%s","X_file_DailyDiffs_%s_lastModel=%s_daysAhead=[%s-%s]_Months=[%s-%s]_%s.csv")%(last_model,suffix2,models_str,last_model,days_ahead[0],days_ahead[-1],months[0],months[-1],suffix)
        multimodel_final_xs_for_write.to_csv(xfile,index=False)

    # 'diff_6Z+0Z_PARACO-GEFS' | 'diff_6Z_PARA-GEFS'
    multimodel_final_xs = multimodel_final_xs[multimodel_final_xs['forecast_time'] >= dtdt(2017, 11, 7)]

    if not include_prev24:
        multimodel_final_xs['diff_12Z-6Z_GEFS'] = multimodel_final_xs['Value_12Z_GEFS'] - multimodel_final_xs[
            'Value_6Z_GEFS']
        ref_df = multimodel_final_xs
        for thresh in [0.1 * i for i in range(1, 11)]:
            if last_model == 0:
                hr, is_hit, quantile = get_hr_of_cols(ref_df, 'diff_18Z-12Z_PARACO', 'diff_0Z-12Z_GEFS', thresh)
            elif last_model == 12:
                hr, is_hit, quantile = get_hr_of_cols(ref_df, 'diff_6Z+0Z_PARA-GEFS', 'diff_12Z-0Z_GEFS', thresh)
            print("Threshold: %s (> %s) (%s Trades)  | HR: %s" % (thresh, quantile, is_hit.shape[0], hr))


"""____________________________ DAILY AGGREGATION__________________________"""


def daily_aggregation_diffs(df,model,ft_types=['HDD','CDD','GDD'],last_run=6):
    a = 1
    MODELS_WITH_14_28 = ['CFSCO','CFS','EPS45']
    assert last_run in [0,6]
    df['fake_forecast'] = df['forecast_time'] + td(hours=18 - last_run)
    df['forecast_day'] = df['fake_forecast'].dt.date

    if 'GDD' in ft_types:
        assert {'HDD', 'CDD'} <= set(df['feature_type'].tolist())
        df_gdd = create_gdds(df)
        df = df.merge(df_gdd, on=list(df), how='outer').sort_values(['forecast_time', 'validation_day'])

    df2 = df.groupby(['forecast_day','validation_day','weight','feature_type']).mean()
    df2 = df2.reset_index()
    df5 = pd.DataFrame()

    for ft in ft_types:
        df3 = df2[df2['feature_type']==ft]
        df3 = df3.sort_values(['validation_day', 'forecast_day'])
        df3['diff1'] = df3['Value'].diff(1)
        df3['diff2'] = df3['Value'].diff(2)
        df3['diff3'] = df3['Value'].diff(3)
        df3['diff4'] = df3['Value'].diff(4)
        df3['days_diff'] = df3['validation_day'].diff(1).dt.days

        shift_by_model = {'EPS': -2,'EC': -7,'EPS45':29,
                          'GEM':-6,'ICON':-7,'CFS':29,'CFSCO':29}
        if model in shift_by_model.keys():
            shift = shift_by_model[model]
        else:
            shift = 0
        nans_cond = {'diff1': lambda x: x>14+shift, 'diff2': lambda x: x > 13+shift,
                     'diff3':lambda x: x > 12+shift, 'diff4' : lambda x: x > 11+shift}
#                     'diff1_14-28': lambda x: lambda x: 14< x <28,
 #                    'diff2_14-28': lambda x: lambda x: 14< x <28,
  #                   'diff3_14-28': lambda x: lambda x: 14< x <28,
   #                  'diff4_14-28': lambda x: lambda x: 14< x <28}

        for col in ['diff1','diff2','diff3','diff4']:
            if model in MODELS_WITH_14_28:
                col_b = col + '_14-28'
                if col_b in nans_cond.keys():
                    df3[col_b] = df3[col]
                    df3[col_b][df3['days_ahead'].apply(lambda x: nans_cond[col_b](x))] = np.nan
            df3[col][df3['days_ahead'].apply(lambda x: nans_cond[col](x))] = np.nan

        df4 = df3.sort_values(['forecast_day', 'validation_day'])
        if model == 'EC':
            aa = 1
        df4['test'] = df4['diff2'].diff(1)[1:].tolist() + [np.nan]
        if df5.shape[0] == 0:
            df5 = df4
        else:
            df5 = df5.merge(df4,on=list(df4),how='outer')
    df5 = df5.groupby(['forecast_day', 'feature_type','weight']).mean().reset_index()
    df5 = df5.rename(columns={'diff1':'diff_last-D1', 'diff2': 'diff_last-D2',
                              'diff3':'diff_last-D3','diff4':'diff_last-D4',
                              'diff1_14-28': 'diff_last-D1_14-28', 'diff2_14-28': 'diff_last-D2_14-28',
                              'diff3_14-28': 'diff_last-D3', 'diff4_14-28': 'diff_last-D4_14-28',
                              })
    df5['forecast_time'] = df5['forecast_day'].apply(lambda x: dtdt(x.year,x.month,x.day,last_run))
    return df5[['forecast_time','feature_type','weight'] + [x for x in list(df5) if 'last-D' in x]]


def aggregate_diffs(df,model,ft_types=['HDD','CDD','GDD'],last_run=6,mode='normal',
                    resolutions=['0-0','0-13'],fillnans=False,base_on_values=False,add_stds=False,gdd_weights=GDD_WEIGHTS):
    """
    :param df:
    :param model:
    :param ft_types:
    :param last_run:
    :param mode: normal / daily
    :param resolution
    :return:
    """
    if fillnans:
        assert base_on_values, 'We allow filling nans only when using Values columns'

    models_max_days = int(MODELS_HORIZONS[model][-1] / 24)
    gaps_divisor = (24/MODEL_GAPS[model])
    if model in ['EC','EPS','EPS45']: # ['EC','EPS','EPS45']:
        models_max_days -= 1
    assert last_run in [0,6,12,18]

    if 'GDD' in ft_types:
        print ('generating GDDs... ')
        df_gdd = create_gdds(df,weights=gdd_weights)
        df_gdd['Zs'] = df_gdd['Zs'].fillna('"[]"')
        df = df.merge(df_gdd, on=list(df), how='outer').sort_values(['forecast_time', 'validation_day'])

    # df['models_ahead'] = pd.to_numeric((df['validation_day'] - df['forecast_time']).dt.total_seconds() / ((24/gaps_divisor) * 3600),downcast='integer')
    # df['days_ahead'] = pd.to_numeric((df['validation_day'] - df['forecast_time']).dt.total_seconds() / (24 * 3600),downcast='integer')
    def f(x):
        try:
            return int(x)
        except:
            return np.nan
    df['models_ahead'] = ((df['validation_day'] - df['forecast_time']).dt.total_seconds() / ((24 / gaps_divisor) * 3600)).apply(f)#lambda x: int(x))  # .days
    df['days_ahead'] = ((df['validation_day'] - df['forecast_time']).dt.total_seconds() / (24 * 3600)).apply(f) #lambda x: int(x))  # .days

    if mode == 'daily':
        df['fake_forecast'] = df['forecast_time'] + td(hours=18-last_run)
        df['forecast_day'] = df['fake_forecast'].dt.date
        df2 = df.groupby(['forecast_day','validation_day','weight','feature_type']).mean()
        df2 = df2.reset_index()
        time_col = 'forecast_day'
    else:
        df2 = df
        time_col = 'forecast_time'

    df5 = pd.DataFrame()
    for ft in ft_types:
        df3 = df2[df2['feature_type']==ft]
        if df3.shape[0] == 0:
            a = 1
            continue

        df3 = df3.sort_values(['validation_day', time_col])
        df3['diff1'] = df3['Value'].diff(1)
        df3['diff2'] = df3['Value'].diff(2)
        df3['diff3'] = df3['Value'].diff(3)
        df3['diff4'] = df3['Value'].diff(4)
        df3['Value_Prev1'] = [np.nan] + df3['Value'][:-1].tolist()
        df3['Value_Prev2'] = [np.nan]*2 + df3['Value'][:-2].tolist()
        df3['Value_Prev3'] = [np.nan] * 3 + df3['Value'][:-3].tolist()
        df3['Value_Prev4'] = [np.nan] * 4 + df3['Value'][:-4].tolist()

        df3['days_diff'] = df3['validation_day'].diff(1).dt.days

        cols = ['Value', 'Value_Prev1', 'Value_Prev2', 'Value_Prev3','Value_Prev4', 'diff1', 'diff2', 'diff3', 'diff4']
        if mode == 'daily' and ft == 'CDD' and last_run == 0:
             aa = 1
        for resolution in resolutions:
            if '14-16' in resolution:
                aaa = 1
            print('Handling Model: %s | Feature: %s | Resolution: %s' % (model,ft, resolution))
            for col in cols:
                lower = int(resolution.split('-')[0])
                upper = int(resolution.split('-')[1])

                if 'Prev' not in col:
                    col_b = col + '_%s'%resolution
                else:
                    col_b = "_".join([col.split('_')[0],resolution,col.split('_')[1]])

                # do the real Calculation
                if 'diff' in col and base_on_values:
                    c = int(col.split('diff')[-1])
                    assert {'Value','Value_Prev1','Value_Prev2','Value_Prev3','Value_Prev4'} <= set(cols), 'We dont have Values to base on'
                    df3[col_b] = df3['Value_%s'%resolution] - df3['Value_%s_Prev%s'%(resolution,c)]
                else:
                    df3[col_b] = df3[col].copy()
                    try:
                        if 'Value_Prev2 ' in col and resolution == '0-15':
                            a = 1
                        if 'diff' in col:
                            c = int(col.split('diff')[-1])
                        elif 'Prev' in col:
                            c = int(col.split('Prev')[-1])
                        else:
                            c = 0
                    except:
                        c = 1
                    #epsilon = 0 # if model not in ['EC','EPS','EPS45'] else 0.5
                    #ref_int = int(upper + float(1/gaps_divisor) + float(c/gaps_divisor)) #-epsilon)
                    #upper0 = upper
                    #if ref_int > models_max_days:
                    #    upper -= (ref_int - models_max_days)
                    required_days_cond = lambda x:  x < lower or x > upper
                    def required_days_cond2(x,col):
                        return (int(x +0.5) < lower) or (int(x +0.5) >= (upper - int(col.split('Prev')[-1])))
                    if mode == 'normal':
                        boundary_cond_mask = df3['models_ahead'].diff(c) > 0
                    elif mode == 'daily':
                        boundary_cond_mask = df3['days_ahead'].diff(c) > 0
                    else:
                        raise AssertionError()
                    if mode == 'normal' or 'Prev' not in col_b:
                        df3[col_b][(df3.apply(lambda x: required_days_cond(x['days_ahead']),axis=1))|boundary_cond_mask] = np.nan
                    else:
                        df3[col_b][(df3.apply(lambda x: required_days_cond2(x['days_ahead'],col_b),axis=1))] = np.nan
                    if resolution == '0-16' and col == 'Value_Prev2' and ft == 'GDD' and mode == 'daily':
                        aa = 1
                    # df3.sort_values(['forecast_time','validation_day'])[['forecast_time','validation_day','days_ahead','models_ahead']+[x for x in list(df3) if 'Value_0-13' in x]]
                    # fill nans only when the upper is reaching the edge
                    if fillnans and 'Prev' in col_b and base_on_values:
                        if upper == 13:
                            aa = 1
                        if upper >= models_max_days:
                            print ('Model: %s | Upper (%s) > max_days (%s) | Filling Nans'%(model,upper,models_max_days))

                            def _fillna_func(forecast_time_df,col_b,lower,method='mean'):
                                #forecast_time_df = forecast_time_df.sort_values(['validation_day'])
                                try:
                                    if 'forecast_day' in list(forecast_time_df):
                                        if forecast_time_df['forecast_day'].iloc[-1] == dtdt(2020,6,17).date():
                                            aaa = 1
                                except:
                                    aaa = 1

                                if method == 'mean':
                                    forecast_time_df[col_b][(forecast_time_df[col_b].isna())&(forecast_time_df['days_ahead'] >= lower)] = forecast_time_df[col_b].mean()
                                elif method == 'last':
                                    forecast_time_df[col_b] = forecast_time_df[col_b].fillna(method='ffill')
                                return forecast_time_df
                            df3 = df3.groupby([time_col]).apply(lambda x: _fillna_func(x,col_b,lower,'mean'))
        df4 = df3.sort_values([time_col, 'validation_day'])
        df4['test'] = df4['diff2'].diff(1)[1:].tolist() + [np.nan]
        if df5.shape[0] == 0:
            df5 = df4
        else:
            df5 = df5.merge(df4,on=list(df4),how='outer')
    df5 = df5.groupby([time_col, 'feature_type','weight']).mean().reset_index()
    df6 = pd.DataFrame()
    if add_stds and mode == 'normal':
        for ft_type in ft_types:
            d = df5[df5['feature_type']==ft_type]
            for resolution in resolutions:
                if '0-' in resolution and resolution != '0-0':
                    d['Value_%s_std4_normed' % resolution] = d['Value_%s' % resolution].rolling(4, 4).std() / d['Value_%s' % resolution].rolling(4, 4).mean()
                    d['Value_%s_diff8_normed' % resolution] = abs(d['Value_%s' % resolution].diff(1)).rolling(8, 4).std() / d['Value_%s' % resolution].rolling(8, 4).mean()
                    d['Value_%s_diff8' % resolution] = abs(d['Value_%s' % resolution].diff(1)).rolling(8,4).std()
            if df6.shape[0] == 0:
                df6 = d
            else:
                df6 = df6.merge(d, on=list(d),how='outer')
    else:
        df6 = df5
    df5 = None
    if mode == 'daily':
        df6['forecast_time'] = df6['forecast_day'].apply(lambda x: dtdt(x.year,x.month,x.day,last_run))

    if mode == 'normal':
        rename_dict = {'diff%s_%s'%(prev,res): 'diff_%s_last-Prev%s'%(res,prev) for res in resolutions for prev in [1,2,3,4]}
    else:
        rename_dict = {'diff%s_%s' % (prev, res): 'diff_%s_last-Prev%sD' % (res, prev) for res in resolutions for prev in
                       [1, 2, 3, 4]}
        rename_dict.update({x: x+'D' for x in list(df6) if 'Value_' in x})
        rename_dict.update({'Value':'Value_lastD'})


    df6 = df6.rename(columns=rename_dict)
    for res in resolutions:
        if mode == 'normal':
            df6['diff_%s_Prev1-Prev2'%(res)] = df6[rename_dict['diff%s_%s'%(2,res)]] - df6[rename_dict['diff%s_%s'%(1,res)]]
            df6['diff_%s_Prev1-Prev3'%(res)] = df6[rename_dict['diff%s_%s'%(3,res)]] - df6[rename_dict['diff%s_%s'%(1,res)]]
        else:
            df6['diff_%s_Prev1D-Prev2D' % (res)] = df6[rename_dict['diff%s_%s' % (2, res)]] - df6[rename_dict['diff%s_%s' % (1, res)]]
            df6['diff_%s_Prev1D-Prev3D' % (res)] = df6[rename_dict['diff%s_%s' % (3, res)]] - df6[rename_dict['diff%s_%s' % (1, res)]]

    return df6[['forecast_time','feature_type','weight'] + sorted([x for x in list(df6) if ('last-' in x or 'Prev' in x) or 'Value' in x])]


def wrap_daily_aggregation(eps45_file,model,start=dtdt(2018,11,1),ft_types=['HDD','CDD'],last_run=0,
                            weights=['population_US_weight','basic_NG_weight'],model_runs=[0,6,12,18]):
    models_max_days = int(MODELS_HORIZONS[model][-1] / 24)
    df = pd.read_csv(eps45_file, parse_dates=['forecast_time', 'validation_day'])
    df['validation_day'] = pd.to_datetime(df['validation_day'], errors='coerce')
    df['forecast_time'] = pd.to_datetime(df['forecast_time'], errors='coerce')
    print('File: %s | dtypes: %s'%(eps45_file,df.dtypes))
    df = df[(df['feature_type'].isin(ft_types)) & (df['weight'].isin(weights))]
    assert model_runs in [[0,6,12,18],[0,12]], 'Invalid models_runs: %s'%model_runs
    df = df[df['forecast_time'].dt.hour.isin(model_runs)]

    if weights == ['population_US_weight','basic_NG_weight']:
        df = df.sort_values([x for x in ['forecast_time','validation_day','feature_type','model','weight'] if x in list(df)])
        df = df.drop_duplicates(keep='last',subset=[c for c in list(df) if c not in ['weight','Value']])
    df = df.drop_duplicates(keep='last')

    df['days_ahead'] = ((df['validation_day'] - df['forecast_time']).dt.total_seconds() / (24 * 3600)).apply(
        lambda x: int(x))  # .days
    start = _prev_last_model(_model_availability_time(start, model), model, 4, 1)

    df = df[df['forecast_time'] >= start]
    daily_aggregation_df = daily_aggregation_diffs(df,model,last_run=last_run)
    return daily_aggregation_df

def wrap_aggregation(eps45_file,start, model, ft_types=['HDD','CDD'],mode='normal',last_run=6,
                            weights=['population_US_weight','basic_NG_weight'],resolutions=['0-0','0-13'],
                     fillnans=False,base_on_values=False,add_stds=False,gdd_weights=GDD_WEIGHTS,model_runs=[0,6,12,18],
                     end=END_DATE):
    df = pd.read_csv(eps45_file, parse_dates=['forecast_time', 'validation_day'],error_bad_lines=False)
    df['validation_day'] = pd.to_datetime(df['validation_day'], errors='coerce')
    df['forecast_time'] = pd.to_datetime(df['forecast_time'], errors='coerce')
    #df = df[df['forecast_time']<=dtdt(2019,5,1)]
    df = df.dropna(subset=['Value'])
    df = df[(df['feature_type'].isin(ft_types)) & (df['weight'].isin(weights))]
    assert model_runs == [0,6,12,18] or (mode == 'daily' and last_run in model_runs), 'subset of model runs is allowed only with daily mode.. got: %s'%model_runs
    assert model_runs in [[0, 6, 12, 18], [0, 12]], 'Invalid models_runs: %s' % model_runs

    df = df[df['forecast_time'].dt.hour.isin(model_runs)]

    # re-assure conversion due to possible deprecated rows
    df['forecast_time'] = pd.to_datetime((df['forecast_time']))
    df['validation_day'] = pd.to_datetime(df['validation_day'])

    if weights == ['population_US_weight','basic_NG_weight']:
        df = df.sort_values([x for x in ['forecast_time','validation_day','feature_type','model','weight'] if x in list(df)])
        df = df.drop_duplicates(keep='last',subset=[c for c in list(df) if c not in ['weight','Value']])

    cols_to_group = [x for x in list(df) if sum([s in x for s in ['Value', 'feature_type']]) == 0]

    df = df.drop_duplicates(keep='last')

    models_back = 4 if model not in ['EPS45','GFSv16'] else 16
    start = _prev_last_model(_model_availability_time(start, model), model, models_back, 1)

    df = df[(df['forecast_time'] >= start)&(df['forecast_time']<=end)]
    if df.shape[0] == 0:
        raise AssertionError('FullFile of model %s didnt have data >= %s | PLEASE CHECK FULL FILE'%(model,start))

    if df.shape[0] == 0:
        return pd.DataFrame(columns='forecast_time,feature_type,weight'.split(','))
    else:
        daily_aggregation_df = aggregate_diffs(df,model,mode=mode,resolutions=resolutions,last_run=last_run,
                                               fillnans=fillnans,base_on_values=base_on_values,add_stds=add_stds,gdd_weights=gdd_weights,
                                               ft_types=ft_types)
        if mode == 'daily':
            s = 1
        columns_with_nans = np.array(list(daily_aggregation_df))[daily_aggregation_df.isna().mean() > 0]
        columns_with_nans_that_cause_dropping = [x for x in columns_with_nans if sum([s in x for s in SUBSTRINGS_TO_ALLOW_NANS])==0]

        original_shape = daily_aggregation_df.shape[0]
        daily_aggregation_df = daily_aggregation_df.dropna(subset=[x for x in list(daily_aggregation_df) if not sum([s in x for s in SUBSTRINGS_TO_ALLOW_NANS])])
        new_shape = daily_aggregation_df.shape[0]
        if len(columns_with_nans_that_cause_dropping):
            print ('Warning! We had columns with nans that caused dropping of daily rows:\n%s\nShape changed from: %s ----> %s'%(columns_with_nans_that_cause_dropping,original_shape,new_shape))
        if model_runs == [0,12]:
            def _replacement_func(col):
                d = col.split('Prev')[-1].split('D')[0]
                return col.replace('Prev%sD'%d,'Prev%sD_012'%d)
            daily_aggregation_df = daily_aggregation_df.rename(columns={c: _replacement_func(c) for
                                                                        c in list(daily_aggregation_df) if 'Prev' in c})
        return daily_aggregation_df


""" ________________________ DIFFS NEW MWTHOD _____________________________________"""

def get_EPS45_Xs(eps45_file,start=dtdt(2018,10,1),
                 model='EPS45',ft_types=['HDD','CDD','GDD'],resolutions=['7-42','14-42','14-21','21-28','28-35','35-42','14-28'],
                 weights=['population_US_weight']):

    df = pd.read_csv(eps45_file, parse_dates=['forecast_time', 'validation_day'])
    #df['feature_type'] = df['feature_type'].fillna('HDD')

    df = df[(df['feature_type'].isin(ft_types)) & (df['weight'].isin(weights))]
    df = df.drop_duplicates(keep='last')
    df['days_ahead'] = ((df['validation_day'] - df['forecast_time']).dt.total_seconds() / (24 * 3600)).apply(
        lambda x: int(x))  # .days

    if 'GDD' in ft_types:
        assert {'HDD','CDD'} <= set(df['feature_type'].tolist())
        df_gdd = create_gdds(df)
        df = df.merge(df_gdd,on=list(df),how='outer').sort_values(['forecast_time','validation_day'])

    start = _prev_last_model(_model_availability_time(start, model), model, 4, 1)
    df = df[df['forecast_time'] >= start-td(days=4)]
    stack = []
    for (forecast_time, weight,feature_type),forecast_time_group in df.groupby(['forecast_time','weight','feature_type']):
        if forecast_time < start:
            continue
        if forecast_time.day == 1:
            print ('Model: %s | forecast_time: %s'%(model, forecast_time))
        forecast_time_record = {'forecast_time':forecast_time, 'weight':weight,'feature_type': feature_type}
        if model != 'EPS45':
            last1 = _prev_last_model(_model_availability_time(forecast_time,model),model,1,1)
            last2 = _prev_last_model(_model_availability_time(forecast_time,model),model,2,1)
            last4 = _prev_last_model(_model_availability_time(forecast_time,model),model,4,1)#
            last6 = _prev_last_model(_model_availability_time(forecast_time, model), model, 6, 1)  #
        else:
            last1 = _last_available_model(_model_availability_time(forecast_time, model), model, 1)
            last2 = _last_available_model(_model_availability_time(last1, model), model, 1)
            last3 = _last_available_model(_model_availability_time(last2, model), model, 1)
            last4 = _last_available_model(_model_availability_time(last3, model), model, 1)
        days41 = (forecast_time - last4).days
        days21 = (forecast_time - last2).days
        days11 = (forecast_time - last1).days
        prevs_strs_lst = ['Prev1', 'Prev2', 'Prev4']
        prevs_fcst_ts_lst = [last1, last2, last4]
        if model in ['PARA','GEFS','PARACO']:
            prevs_strs_lst += ['Prev6']
            prevs_fcst_ts_lst += [last6]
        for last, last_str in zip(prevs_fcst_ts_lst, prevs_strs_lst):
            for resolution in resolutions:
                if resolution == '0-8' and model == 'EC' and last_str == 'Prev4':
                    a = 1
                lower = int(resolution.split('-')[0])
                upper = int(resolution.split('-')[1])

                relevant_indices = (forecast_time_group['days_ahead'] >= lower) & (
                            forecast_time_group['days_ahead'] <= upper)
                relevant_val_days = forecast_time_group[relevant_indices]['validation_day'].tolist()

                forecast_time_record['Value_%s' % resolution] = forecast_time_group[relevant_indices].mean()['Value']
                last_df = df[(df['forecast_time']==last)&(df['feature_type']==feature_type)&(df['weight']==weight)]
                forecast_time_record['Value_%s_%s'%(resolution,last_str)] = last_df[last_df['validation_day'].isin(relevant_val_days)].mean()['Value']

        stack.append(forecast_time_record)
    final_df = pd.DataFrame(stack)
    for resolution in resolutions:
        final_df['diff_%s_last-Prev1' % resolution] = final_df['Value_%s' % resolution] - \
                                                                  final_df['Value_%s_Prev1' % (resolution)]
        final_df['diff_%s_last-Prev2'%resolution] = final_df['Value_%s'%resolution] - final_df['Value_%s_Prev2'%resolution]
        final_df['diff_%s_last-Prev4' % resolution] = final_df['Value_%s' % resolution] - final_df['Value_%s_Prev4' % resolution]
        final_df['diff_%s_Prev1-Prev2' % resolution] = final_df['Value_%s_Prev1' % resolution] - final_df['Value_%s_Prev2' % resolution]

    return final_df

def get_specific_days(model, suffix):
    if suffix in ['v7Flna', 'v8', 'v8_12Z', 'v9_wGDD', 'v10_wMidwest',
                  'v10_East', 'v10_SouthCentral', 'v11', 'v14_WindUS',
                  'v15_WindTX']:
        specific_days_dict = {'GEPS': ['0-8', '8-16', '0-15', '0-16', '14-16'],
                              'ICON': ['0-4', '0-8'],
                              'GEM': ['0-8', '0-10'],
                              'GEMCO': ['0-8', '8-16', '0-16', '14-16'],
                              'GEFSL': ['0-8', '8-16', '0-16', '14-16'],
                              'GFSCO': ['0-8', '8-16', '0-16', '14-16'],
                              'GEFS': ['0-8', '8-16', '0-16', '14-16', '11-15', '14-14', '15-15'],
                              'PARA': ['0-8', '0-10', '8-16', '0-16', '14-16', '0-15'],
                              'GFSv16': ['0-8', '0-10', '8-16', '0-16', '14-16'],
                              'GFS': ['0-8', '8-16', '0-16', '14-16'],
                              'PARACO': ['0-8', '0-10', '8-16', '0-16', '14-16', '0-15'],
                              'EPS': ['0-8', '0-14', '9-13', '12-13', '13-13'],
                              'EPSCO': ['0-8', '0-14', '9-13', '12-13', '13-13'],
                              'EC': ['0-4', '0-8', '5-8'],
                              'EPS45': ['14-21', '14-28', '21-28', '28-35', '28-42'],
                              'CFS': ['8-16', '0-16', '0-21', '14-21', '14-18', '14-28', '21-28', '28-35', '28-42'],
                              'CFSCO': ['8-16', '0-16', '0-21', '14-21', '14-18', '14-28', '21-28', '28-35',
                                        '28-42'],
                              'GEFS35': ['14-21', '0-21', '14-28', '21-28', '14-35'],
                              'GEFSCO35': ['14-21', '0-21', '14-28', '21-28', '14-35']
                              }
        specific_daily_days_dict = {
            'GEPS': ['0-10', '0-16', '8-16'],
            'ICON': ['0-8'],
            'GEM': ['0-10'],
            'GEMCO': ['0-10', '0-16', '8-16'],
            'GEFSL': ['0-10', '0-16', '8-16'],
            'GFSCO': ['0-10', '0-16', '8-16'],
            'GEFS': ['0-10', '0-16', '8-16'],  # ,'11-15','15-15'],
            'PARA': ['0-10', '0-16', '8-16'],
            'GFSv16': ['0-10', '0-16', '8-16'],
            'GFS': ['0-16'],
            'PARACO': ['0-10', '0-16', '8-16'],
            'EPS': ['0-13', '8-13'],
            'EPSCO': ['0-13', '8-13'],
            'EC': ['0-8', '5-8'],  # ,'4-8'
            'EPS45': ['14-21', '14-28', '14-35'],
            'CFS': ['0-16', '14-21', '14-28', '14-35', '28-42', '10-21', '21-35', '21-42'],
            'CFSCO': ['0-16', '14-21', '14-28', '14-35', '28-42', '10-21', '21-35', '21-42'],
            'GEFS35': ['14-21', '0-21', '14-28', '21-28', '14-35'],
            'GEFSCO35': ['14-21', '0-21', '14-28', '21-28', '14-35']
        }
    elif suffix in ['v12_BArabica', 'v12_BArabica12Z', 'v13_BTotalCof']:
        specific_days_dict = {'GEPS': ['0-8', '8-16', '0-15', '0-16', '14-16'],
                              'ICON': ['0-4', '0-8'],
                              'GEM': ['0-8', '0-10'],
                              'GEMCO': ['0-8', '8-16', '0-16', '14-16'],
                              'GEFSL': ['0-8', '8-16', '0-16', '14-16'],
                              'GFSCO': ['0-8', '8-16', '0-16', '14-16'],
                              'GEFS': ['0-8', '8-16', '0-16', '14-16', '13-15', '11-15', '14-14', '15-15'],
                              'PARA': ['0-8', '0-10', '8-16', '0-16', '11-15', '13-15', '14-16', '15-15'],
                              'GFSv16': ['0-8', '0-10', '8-16', '0-16', '14-16'],
                              'GFS': ['0-8', '8-16', '0-16', '14-16'],
                              'PARACO': ['0-8', '0-10', '8-16', '0-16', '11-15', '13-15', '14-16', '15-15'],
                              'EPS': ['0-8', '0-14', '9-13', '12-13', '13-13'],
                              'EPSCO': ['0-8', '0-14', '9-13', '12-13', '13-13'],
                              'EC': ['0-4', '0-8', '5-8'],
                              'EPS45': ['14-21', '14-28', '21-28', '28-35', '28-42'],
                              'CFS': ['8-16', '0-16', '0-21', '14-21', '14-18', '14-28', '21-28', '28-35', '28-42'],
                              'CFSCO': ['8-16', '0-16', '0-21', '14-21', '14-18', '14-28', '21-28', '28-35',
                                        '28-42'],
                              'GEFS35': ['14-21', '0-21', '14-28', '21-28', '14-35'],
                              'GEFSCO35': ['14-21', '0-21', '14-28', '21-28', '14-35']
                              }
        specific_daily_days_dict = {
            'GEPS': ['0-10', '0-16', '8-16'],
            'ICON': ['0-8'],
            'GEM': ['0-10'],
            'GEMCO': ['0-10', '0-16', '8-16'],
            'GEFSL': ['0-10', '0-16', '8-16'],
            'GFSCO': ['0-10', '0-16', '8-16'],
            'GEFS': ['0-10', '0-16', '8-16'],  # ,'11-15','15-15'],
            'PARA': ['0-10', '0-16', '8-16'],
            'GFSv16': ['0-10', '0-16', '8-16'],
            'GFS': ['0-16'],
            'PARACO': ['0-10', '0-16', '8-16'],
            'EPS': ['0-13', '8-13'],
            'EPSCO': ['0-13', '8-13'],
            'EC': ['0-8', '5-8'],  # ,'4-8'
            'EPS45': ['14-21', '14-28', '14-35'],
            'CFS': ['0-16', '14-21', '14-28', '14-35', '28-42', '10-21', '21-35', '21-42'],
            'CFSCO': ['0-16', '14-21', '14-28', '14-35', '28-42', '10-21', '21-35', '21-42'],
            'GEFS35': ['14-21', '0-21', '14-28', '21-28', '14-35'],
            'GEFSCO35': ['14-21', '0-21', '14-28', '21-28', '14-35']
        }
    else:
        specific_days_dict = {'GEPS': ['0-8', '8-16', '0-16'],
                              'GEFSL': ['0-8', '8-16', '0-16'],
                              'GFSCO': ['0-8', '8-16', '0-16'],
                              'GEFS': ['0-8', '8-16', '0-15', '0-16'],
                              'PARA': ['0-8', '8-16', '0-16'],
                              'GFS': ['0-8', '8-16', '0-16'],
                              'PARACO': ['0-8', '8-16', '0-16'],
                              'EPS': ['0-8'],
                              'EC': ['0-8', '5-8'],
                              'EPS45': ['14-21', '14-28', '21-28', '28-35', '28-42'],
                              'CFS': ['14-21', '14-28', '21-28', '28-35', '28-42'],
                              'CFSCO': ['14-21', '14-28', '21-28', '28-35', '28-42']}

    return specific_days_dict[model], specific_daily_days_dict[model]

def get_weights_lst(suffix):
    weights = ['population_US_weight', 'basic_NG_weight']
    ft_types = ['HDD', 'CDD', 'GDD']
    if 'Midwest' in suffix:
        weights = ['NG16_midwest_weight']
    elif 'East' in suffix:
        weights = ['NG16_east_weight']
    elif 'SouthCentral' in suffix:
        weights = ['NG16_SouthCentral_weight']
    elif 'Arabica' in suffix:
        weights = ['BR_ArabicaTrunc_weight']
    elif 'BTotalCof' in suffix:
        weights = ['BR_TotalTrunc_weight']
    elif 'WindUS' in suffix:
        weights = ['US_Wind']
        ft_types = ['ws10m']
    elif 'WindTX' in suffix:
        weights = ['ERCOT_with_Neighbors_Wind']
        ft_types = ['ws10m']

    if weights[0] in ['BR_ArabicaTrunc_weight', 'BR_TotalTrunc_weight']:
        ft_types = ['CKDD', 'CGDD', 'PCDD']
    return weights, ft_types

def generate_xs_method2(model='GEPS',start=dtdt(2018,10,1),end=END_DATE,load_new=False,
                        suffix='v4gdd',asset='NG',unite_only_last_months=True):
    out_d = DEGDAYS_FULL_OUTDIR if not unite_only_last_months else DEGDAYS_LAST3_OUTDIR
    print ('Handling Xs for model: %s'%model)
    specific_days,specific_daily_days = get_specific_days(model,suffix)

    if asset == 'NG':
        outfile = r'C:\Users\<USER>\Documents\Work\Amazon\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (model,suffix)
        file = out_d+r"\Full_degdays_Full_%s.csv" % model
        if suffix in WIND_SUFFIXES:
            outfile = r'C:\Users\<USER>\Documents\Work\Amazon\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (model, suffix)
            file = os.path.join(HOME,"degdays_archive","Wind_Full_Files","Full_degdays_Full_%s.csv") % model
    elif asset == 'COFFEE':
        outfile = r'C:\Users\<USER>\Documents\Work\Amazon\%s\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (asset,model, suffix)
        file = os.path.join(HOME,"%s","degdays_archive","Full_Files","Full_degdays_Full_%s.csv") % (asset,model)
    if MODE == 'linux':
        outfile = "/mnt/volume_100G/home/<USER>/Data_Home/Xdata/X_file_DailyDiffs_%s_%s.csv" % (model,suffix)
        file = "/mnt/volume_100G/home/<USER>/degdays_csvs/Full_Files/Full_degdays_Full_%s.csv" % model

    if not load_new:
        current_df = pd.read_csv(outfile,parse_dates=['forecast_time'])
        initial_size_of_current_df = current_df.shape[0]

        rename_dict = {'Value_x':'Value','Value_y':'Value_lastD'}
        current_df = current_df.rename(columns=rename_dict)
        if suffix in ['v8','v9_wGDD','v11','v8_12Z', 'v12_BArabica12Z']:
            current_df = current_df[(~(current_df['Value_0-0'].isna())|(current_df['forecast_time'] <= (dtdt.now()-td(days=120))))] # todo
        try:
            start = max(start, current_df['forecast_time'].sort_values().iloc[-1]) - td(days=2)
        except:
            pass
    else:
        current_df = pd.DataFrame()
        initial_size_of_current_df = current_df.shape[0]
    print('About to run on Model: %s | Start = %s' % (model, start))

    add_stds = True if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                                  'v10_East','v10_SouthCentral','v11',
                                  'v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX'] else False
    # Choose GDD weighting
    gdd_weights = GDD_WEIGHTS
    if suffix == 'v9_wGDD':
        gdd_weights = {'HDD': 1.3, 'CDD': 0.7}

    last_run = 6 if model in MODELS_WITH_6Z else 0

    weights, ft_types = get_weights_lst(suffix)

    if suffix in ['v5alt','v6Flna','v7Flna','v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        eps45_df = wrap_aggregation(file, start=start, model=model, resolutions=['0-0','0-2', '0-13'] + specific_days,mode='normal',
                                    fillnans=True,base_on_values=True,last_run=last_run,add_stds=add_stds,gdd_weights=gdd_weights,weights=weights,
                                    ft_types=ft_types,end=end)
    else:
        eps45_df = get_EPS45_Xs(file, start=start, model=model,ft_types=ft_types,
                                resolutions=['0-0', '0-13'] + specific_days)
    if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']: # updated daily Features
        startD = start - td(days=4 if model != 'EPS45' else 14)
        daily_aggregation = wrap_aggregation(file, start=startD, model=model,last_run=last_run,
                                             resolutions=specific_daily_days,
                                             ft_types=ft_types,
                                             mode='daily', fillnans=True,
                                             base_on_values=True,gdd_weights=gdd_weights,weights=weights,
                                             end=end)
        if last_run == 6:
            # We add daily from 0Z too
            daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model,last_run=0,
                                             resolutions=specific_daily_days,ft_types=ft_types,
                                             mode='daily', fillnans=True, base_on_values=True,gdd_weights=gdd_weights,
                                                    weights=weights,end=end)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
            if suffix == 'v11':
                daily_aggregation_0z_012 = wrap_aggregation(file, start=startD, model=model, last_run=0,
                                                            resolutions=specific_daily_days,
                                                            mode='daily', fillnans=True, base_on_values=True,
                                                            gdd_weights=gdd_weights, weights=weights,
                                                            model_runs=[0, 12],end=end)
                relevant_cols_012 = ['forecast_time', 'feature_type', 'weight']+ [x for x in list(daily_aggregation_0z_012) if '012' in x and 'diff' in x]
                daily_aggregation_0z_012 = daily_aggregation_0z_012[relevant_cols_012]
                daily_aggregation = daily_aggregation.merge(daily_aggregation_0z_012, on=['forecast_time', 'feature_type', 'weight'], how='outer')
        if suffix in SUFFIXES_12Z and model not in MODELS_WITHOUT_12Z:

            daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model,last_run=12,
                                             resolutions=specific_daily_days,ft_types=ft_types,
                                             mode='daily', fillnans=True, base_on_values=True,
                                                    gdd_weights=gdd_weights,weights=weights,end=end)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
            if model in MODELS_WITH_6Z and asset != 'COFFEE':
                daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model, last_run=18,
                                                    resolutions=specific_daily_days,ft_types=ft_types,
                                                    mode='daily', fillnans=True, base_on_values=True,
                                                    gdd_weights=gdd_weights, weights=weights,end=end)
                daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
    else:
        daily_aggregation = wrap_daily_aggregation(file,model=model,ft_types=['HDD','CDD','GDD'],last_run=last_run)
        if last_run == 6:
            # We add daily from 0Z too
            daily_aggregation_0z = wrap_daily_aggregation(file, model=model, ft_types=['HDD', 'CDD', 'GDD'],
                                                       last_run=0)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z,on=list(daily_aggregation),how='outer')

    current_df = current_df[[x for x in list(current_df) if 'last-D' not in x]]
    if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        if suffix not in SUFFIXES_12Z:
            if model != 'CFS':
                how = 'inner'
                eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how=how)
            else: ## This hack if for us to be able to trade based on quasi 6Z dailies
                how = 'outer'
                first_day_of_eps45 = eps45_df['forecast_time'].min()
                try:
                    daily_aggregation = daily_aggregation[daily_aggregation['forecast_time'].dt.hour.isin([0,6])]
                except:
                    aa = 1
                eps45_df = eps45_df[eps45_df['forecast_time'].dt.hour.isin([0, 6])]
                eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how=how)
                eps45_df = eps45_df[(eps45_df['forecast_time']>=first_day_of_eps45)&(eps45_df['forecast_time']>=first_day_of_eps45)]
        else: # suffix in SUFFIXES_12Z:
            how = 'outer'
            eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'],how=how)

    if current_df.shape[0] > 0:
        common = list(set(list(eps45_df)).intersection(set(list(current_df))))
        try:
            eps45_df = eps45_df.sort_values('forecast_time')
            current_df = current_df.dropna(subset=[x for x in list(current_df) if '_0-0' in x])
            last_date_of_current_df = current_df['forecast_time'].iloc[-1]
            #eps45_df = eps45_df[(eps45_df>=last_date_of_current_df)|].loc[eps45_df['forecast_time']<=last_date_of_current_df] = eps45_df.loc[eps45_df['forecast_time']<=last_date_of_current_df].dropna()
            last_date_allowed = last_date_of_current_df - td(hours=12)
            eps45_df = eps45_df[(eps45_df['forecast_time'] > last_date_allowed)]#|((eps45_df.isna().sum(axis=1)==0))]
            first_date_of_new_df = eps45_df['forecast_time'].iloc[0]
            current_df = current_df[current_df['forecast_time']<first_date_of_new_df]
        except:
            pass
        #current_df = current_df[current_df['forecast_time']<=start]
        eps45_df = current_df.merge(eps45_df,on=common,how='outer')
    if suffix not in ['v8','v8_12Z','v9_wGDD','v10_wMidwest','v10_East',
                      'v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how='outer')
    eps45_df = eps45_df[['forecast_time']+[x for x in list(eps45_df) if 'time' not in x and 'Value2' not in x and 'Value4' not in x]]
    eps45_df = eps45_df.drop_duplicates(subset=['forecast_time','Value_0-0','feature_type','weight'])
    if suffix not in SUFFIXES_12Z and model not in MODELS_TO_ALLOW_NANS:
        eps45_df = eps45_df[eps45_df.isna().mean(axis=1) < 0.1]

    eps45_df = eps45_df.sort_values(['forecast_time'])
    final_df_size = eps45_df.shape[0]
    if final_df_size < initial_size_of_current_df-10:
        print('WARNING: We lost a lost > 10 rows in the merge...NOT WRITING PLEASE RETRY')
        return 0
    else:
        print('INFO: About to write {} X_daily_Diffs of shape {} (initial was {})'.format(model,eps45_df.shape[0],initial_size_of_current_df))
        success = safe_write_to_csv(eps45_df, outfile,index=False)
        return success


def generate_xs_v3(model='GEPS',start=dtdt(2018,10,1),end=END_DATE,load_new=False,
                        suffix='v4gdd',asset='NG',unite_only_last_months=True):
    out_d = DEGDAYS_FULL_OUTDIR if not unite_only_last_months else DEGDAYS_LAST3_OUTDIR
    print ('Handling Xs for model: %s'%model)
    specific_days,specific_daily_days = get_specific_days(model,suffix)

    if asset == 'NG':
        outfile = r'C:\Users\<USER>\Documents\Work\Amazon\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (model,suffix)
        file = out_d+r"\Full_degdays_Full_%s.csv" % model
        if suffix in WIND_SUFFIXES:
            outfile = r'C:\Users\<USER>\Documents\Work\Amazon\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (model, suffix)
            file = os.path.join(HOME,"degdays_archive","Wind_Full_Files","Full_degdays_Full_%s.csv") % model
    elif asset == 'COFFEE':
        outfile = r'C:\Users\<USER>\Documents\Work\Amazon\%s\Xdata\\X_file_DailyDiffs_%s_%s.csv' % (asset,model, suffix)
        file = os.path.join(HOME,"%s","degdays_archive","Full_Files","Full_degdays_Full_%s.csv") % (asset,model)
    if MODE == 'linux':
        outfile = "/mnt/volume_100G/home/<USER>/Data_Home/Xdata/X_file_DailyDiffs_%s_%s.csv" % (model,suffix)
        file = "/mnt/volume_100G/home/<USER>/degdays_csvs/Full_Files/Full_degdays_Full_%s.csv" % model

    dirname = os.path.dirname(outfile)
    filename = os.path.basename(outfile)
    if not load_new:
        start = max(start, get_last_date_of_xdata_file(filename,dirname)) - td(days=4)
    else:
        current_df = pd.DataFrame()
        initial_size_of_current_df = current_df.shape[0]
    print('About to run on Model: %s | Start = %s' % (model, start))

    add_stds = True if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                                  'v10_East','v10_SouthCentral','v11',
                                  'v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX'] else False
    # Choose GDD weighting
    gdd_weights = GDD_WEIGHTS
    if suffix == 'v9_wGDD':
        gdd_weights = {'HDD': 1.3, 'CDD': 0.7}

    last_run = 6 if model in MODELS_WITH_6Z else 0

    weights, ft_types = get_weights_lst(suffix)

    if suffix in ['v5alt','v6Flna','v7Flna','v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        eps45_df = wrap_aggregation(file, start=start, model=model, resolutions=['0-0','0-2', '0-13'] + specific_days,mode='normal',
                                    fillnans=True,base_on_values=True,last_run=last_run,add_stds=add_stds,gdd_weights=gdd_weights,weights=weights,
                                    ft_types=ft_types,end=end)
    else:
        eps45_df = get_EPS45_Xs(file, start=start, model=model,ft_types=ft_types,
                                resolutions=['0-0', '0-13'] + specific_days)
    if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']: # updated daily Features
        startD = start - td(days=4 if model != 'EPS45' else 14)
        daily_aggregation = wrap_aggregation(file, start=startD, model=model,last_run=last_run,
                                             resolutions=specific_daily_days,
                                             ft_types=ft_types,
                                             mode='daily', fillnans=True,
                                             base_on_values=True,gdd_weights=gdd_weights,weights=weights,
                                             end=end)
        if last_run == 6:
            # We add daily from 0Z too
            daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model,last_run=0,
                                             resolutions=specific_daily_days,ft_types=ft_types,
                                             mode='daily', fillnans=True, base_on_values=True,gdd_weights=gdd_weights,
                                                    weights=weights,end=end)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
            if suffix == 'v11':
                daily_aggregation_0z_012 = wrap_aggregation(file, start=startD, model=model, last_run=0,
                                                            resolutions=specific_daily_days,
                                                            mode='daily', fillnans=True, base_on_values=True,
                                                            gdd_weights=gdd_weights, weights=weights,
                                                            model_runs=[0, 12],end=end)
                relevant_cols_012 = ['forecast_time', 'feature_type', 'weight']+ [x for x in list(daily_aggregation_0z_012) if '012' in x and 'diff' in x]
                daily_aggregation_0z_012 = daily_aggregation_0z_012[relevant_cols_012]
                daily_aggregation = daily_aggregation.merge(daily_aggregation_0z_012, on=['forecast_time', 'feature_type', 'weight'], how='outer')
        if suffix in SUFFIXES_12Z and model not in MODELS_WITHOUT_12Z:

            daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model,last_run=12,
                                             resolutions=specific_daily_days,ft_types=ft_types,
                                             mode='daily', fillnans=True, base_on_values=True,
                                                    gdd_weights=gdd_weights,weights=weights,end=end)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
            if model in MODELS_WITH_6Z and asset != 'COFFEE':
                daily_aggregation_0z = wrap_aggregation(file, start=startD, model=model, last_run=18,
                                                    resolutions=specific_daily_days,ft_types=ft_types,
                                                    mode='daily', fillnans=True, base_on_values=True,
                                                    gdd_weights=gdd_weights, weights=weights,end=end)
                daily_aggregation = daily_aggregation.merge(daily_aggregation_0z, on=list(daily_aggregation), how='outer')
    else:
        daily_aggregation = wrap_daily_aggregation(file,model=model,ft_types=['HDD','CDD','GDD'],last_run=last_run)
        if last_run == 6:
            # We add daily from 0Z too
            daily_aggregation_0z = wrap_daily_aggregation(file, model=model, ft_types=['HDD', 'CDD', 'GDD'],
                                                       last_run=0)
            daily_aggregation = daily_aggregation.merge(daily_aggregation_0z,on=list(daily_aggregation),how='outer')

    if suffix in ['v8','v8_12Z','v9_wGDD','v10_wMidwest',
                  'v10_East','v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        if suffix not in SUFFIXES_12Z:
            if model != 'CFS':
                how = 'inner'
                eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how=how)
            else: ## This hack if for us to be able to trade based on quasi 6Z dailies
                how = 'outer'
                first_day_of_eps45 = eps45_df['forecast_time'].min()
                try:
                    daily_aggregation = daily_aggregation[daily_aggregation['forecast_time'].dt.hour.isin([0,6])]
                except:
                    aa = 1
                eps45_df = eps45_df[eps45_df['forecast_time'].dt.hour.isin([0, 6])]
                eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how=how)
                eps45_df = eps45_df[(eps45_df['forecast_time']>=first_day_of_eps45)&(eps45_df['forecast_time']>=first_day_of_eps45)]
        else: # suffix in SUFFIXES_12Z:
            how = 'outer'
            eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'],how=how)

    if suffix not in ['v8','v8_12Z','v9_wGDD','v10_wMidwest','v10_East',
                      'v10_SouthCentral','v11','v12_BArabica','v12_BArabica12Z', 'v13_BTotalCof', 'v14_WindUS', 'v15_WindTX']:
        eps45_df = eps45_df.merge(daily_aggregation, on=['weight', 'forecast_time', 'feature_type'], how='outer')
    eps45_df = eps45_df[['forecast_time']+[x for x in list(eps45_df) if 'time' not in x and 'Value2' not in x and 'Value4' not in x]]
    eps45_df = eps45_df.sort_values('forecast_time')
    first_not_nan_date = eps45_df.dropna(subset=['Value_0-0']).iloc[0]['forecast_time']
    eps45_df = eps45_df.query('forecast_time >= @first_not_nan_date')
    eps45_df = eps45_df.drop_duplicates(subset=['forecast_time','Value_0-0','feature_type','weight'])
    if (suffix not in SUFFIXES_12Z and model not in MODELS_TO_ALLOW_NANS) or model in ['EPS','EPSCO']:
        ref_df_for_nans_filter = eps45_df.copy()
        if model in ['EPS','EPSCO']:
            ref_df_for_nans_filter = ref_df_for_nans_filter[[x for x in list(ref_df_for_nans_filter) if not x.endswith('D')
                                                             and not ('diff_12-' in x or 'diff_13-' in x)]]
        eps45_df = eps45_df[ref_df_for_nans_filter.isna().mean(axis=1) < 0.1]

    success = split_xdata_df_to_subdirs(eps45_df, dirname, filename,base_on_existing=not load_new)
    return success


def full_process_method2(models_group,suffix='v7Flna',from_new=False,
                         asset='NG',start=dtdt(2019,1,1),split_builds=False,
                         skip_full_files=False,unite_only_last_months=True,
                         xs_method='v3'):

    if not isinstance(suffix,list):
        suffix_lst = [suffix]
    else:
        suffix_lst = suffix
    for suffix in suffix_lst:
        #start = dtdt(2020,12,1)
        if asset == 'COFFEE':
            #start = dtdt(2018,10,1)
            pass
        special_file_name = None
        if suffix in COFFEE_SUFFIXES:
            dirs = COFFEE_DIRS
            out_dir = coffee_out_d
            models_full = ['PARA','PARACO','GEFS','GEFSL','GFSCO','CFS'][:3]
        elif suffix in WIND_SUFFIXES:
            dirs = WIND_DIRS
            out_dir = wind_out_d
            models_full = ['PARA', 'PARACO', 'GEFS',# 'GEFSL', 'GFSCO'
                            'EC', 'EPSCO', 'EPS'
                                                    ]
            special_file_name = 'wind'
        else:
            dirs = MODELS_DIRS
            out_dir = DEFAULT_FULL_FILES_OUTDIR
            models_full = ['EC','EPS','EPS45','GEPS','GEFS','PARA','PARACO','GFSv16',
                           'CFS','CFSCO','CFSM','GEFS35','GEFSCO35',
                       'GEM','GEMCO','ICON'] # 'GEFSL','GFSCO',

        additional_models = ['EPSCO','EPS2'] if 'v8_' in suffix else []
        models_6z = ['PARA','PARACO','GEFS','CFS','CFSCO','CFSM','GFSv16','GEFSCO35',
                     'EPS','EC']
        models_12z = ['GEPS','GEFS','PARA','PARACO','GEMCO','GEM','ICON']
        models_legacy = ['CFS','CFSCO'] # ['GEFSL','GFSCO','GFSv16']+
        models_dic = {'6Z': models_6z,'gefs':['GEFS','PARACO'], 'full': models_full,'12Z':models_12z,
                      'legacy': models_legacy,'cfs':['CFS','CFSCO','CFSM'],'eps':['EPS','EC'],'ec':['EC'],
                      'cmc':['GEM','GEMCO','GEPS'],
                      'non_legacy': [x for x in models_full if x not in models_legacy],
                      'gfsv16':['GFSv16'],'para':['PARA'],'gefsl':['GEFSL'],'gfsco':['GFSCO'],
                      'SA': ['PARA','PARACO','GEFS'], #,'GFSCO','GEFSL'],
                      '12Z_a0':['GEM','PARA'],'12Z_b':['EC','EPS','CFS','CFSCO'],
                      }
        for m in models_full:
            models_dic[m] = [m]
        if 'Wind' not in suffix and 'Arabica' not in suffix:
            models_dic['PARA'] = ['PARA','GFSv16']
        models = models_dic[models_group]

        if not skip_full_files:
            get_multimodel_df(dirs=dirs, out_d=out_dir, models=models+additional_models,special_file_name=special_file_name,
                              unite_only_last_months=unite_only_last_months,suffix=suffix)
            time.sleep(2) # sleep to give time for the disk to update

        for model in models: #,'GEM','GEMCO']:
            if model == 'CFSM':
                print('Skipping CFSM, it has no Xs')
                continue
            try:
                generate_xs_function = {'v2':generate_xs_method2, 'v3':generate_xs_v3}.get(xs_method,generate_xs_method2)

                if not split_builds or not from_new:
                    generate_xs_function(model,start=start,load_new=from_new,suffix=suffix,asset=asset,unite_only_last_months=unite_only_last_months)  # from_new
                else:
                    generate_xs_function(model, start=start, load_new=from_new, suffix=suffix, asset=asset,end=dtdt(2020,1,1))
                    generate_xs_function(model, start=dtdt(2020,1,1), load_new=from_new, suffix=suffix, asset=asset,end=dtdt(2021,1,1))
                    generate_xs_function(model, start=dtdt(2021,1,1), load_new=from_new, suffix=suffix, asset=asset)
            except Exception as e:
                print('Skipping bad model....%s' % model)
                raise e
    return True


def sync_full_files_with_last_months(model):
    print('Syncing %s'%model)
    csv_full = os.path.join(DEGDAYS_FULL_OUTDIR, 'Full_degdays_Full_%s.csv' % model)
    csv_last_months = os.path.join(DEGDAYS_LAST3_OUTDIR, 'Full_degdays_Full_%s.csv' % model)

    df_full = pd.read_csv(csv_full, parse_dates=['forecast_time'])
    df_last = pd.read_csv(csv_last_months, parse_dates=['forecast_time'])
    df_last = df_last.query('forecast_time >= @df_full.forecast_time.max()')
    df_full2 = df_full.merge(df_last,on=list(df_last),how='outer')
    if df_full2.shape[0] > df_full.shape[0]:
        df_full2.sort_values('forecast_time').to_csv(csv_full,index=False)



if __name__ == '__main__':
    # suffix = 'v12_BArabica12Z'  # wGDD' #'v8' #'v7Flna' v11
    # suffix = 'v12_BArabica12Z' #wGDD' #'v8' #'v7Flna' v11

    # todo should be run every week!
    # get_multimodel_df(#models=['GEFS'],
    #                   out_d=os.path.join(HOME,"degdays_archive","Full_Files","Full"),
    #                   unite_only_last_months=False)

    models_full = ['EC','EPS','EPSCO','EPS45','GEPS','GEFS','PARA','PARACO','GFSv16',
                           'CFS','CFSCO','CFSM','GEFS35','GEFSCO35',
                       'GEM','GEMCO','ICON'][:2] # 'GEFSL','GFSCO',
    # for model in models_full:
    #     sync_full_files_with_last_months(model)
    # raise

    for suffix in ['v8_12Z',
                    'v14_WindUS','v15_WindTX',
                   ][:1]:
        asset = 'NG'
        for model in ['EPS','EC']:
            full_process_method2(model, suffix,
                     asset='NG',
                     from_new=True,unite_only_last_months=False,
                    skip_full_files=False,split_builds=False
                         )#,#start=dtdt(2021,4,1))

        # time.sleep(60*30)
        # 'v14_WindUS','v15_WindTX'
    raise
    full_process_method2('EPS', suffix=['v8_12Z'], from_new=False, asset='NG',
                             skip_full_files=False,xs_method='v3')
    # full_process_method2('GEFSCO35', suffix=['v8_12Z'], from_new=False, asset='NG',
    #                          skip_full_files=True,xs_method='v3')
    # full_process_method2('EPSCO', suffix=['v15_WindTX','v14_WindUS'], from_new=False, asset='NG',
    #                          skip_full_files=False,xs_method='v3')

        # todo !!!!
        # COFFEE and other