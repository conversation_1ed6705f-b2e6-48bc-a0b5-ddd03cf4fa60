import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timed<PERSON>ta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import os
from Algo.Utils.files_handle import HOME

aa = 1
LAST_MODEL_HOUR = 0
prev_model_hour = 18
DAYS_AHEAD_DEFAULT = list(range(0,46))
MONTHS = range(1,13) #[10, 11, 12, 1, 2, 3, 4]
WEIGHTS = ['population_US_weight','basic_NG_weight']
out_d = os.path.join(HOME,"degdays_archive","Full_Files")
d1_march = os.path.join(HOME,"degdays_archive","Live","March19")
d2_may = os.path.join(HOME,"degdays_archive","Live","May19_Before_CDD")
d3_summer19 = os.path.join(HOME,"degdays_archive","Live","until_11.2019")
d4_last_week = os.path.join(HOME,"degdays_archive","Live","last_week_archive")
historic_completion = os.path.join(HOME,"degdays_archive","Live","historic_completion")

EXTENDED_MODELS = ['GFS','PARA','GEFS','PARACO']

if not os.path.exists(out_d):
    os.makedirs(out_d)
outfile_template = "Full_degdays_Full"

FULL_MODELS = ['GFS','PARA','GEFS','GEFSL','GEPS','GEM','PARACO',
               'EC','EPS','GEMCO']

def unite_model_degdays(model, paths_lst,outpath,t_start=dtdt(2017,10,1),t_end=dtdt(2020,1,1)):
    """
    unite the csvs in the paths into full degdays file
    :param model:
    :param paths_lst:
    :param t_start:
    :param t_end:
    :return:
    """
    final_df = pd.DataFrame()
    for csv in paths_lst:
        if os.path.exists(csv):
            df = pd.read_csv(csv,parse_dates=['forecast_time','validation_day'])
            if final_df.shape[0] == 0:
                final_df = df
            else:
                final_df = final_df.merge(df, on=list(set(list(final_df)).intersection(set(list(df)))),how="outer")
    final_df = final_df[(final_df['forecast_time'] >= t_start) & (final_df['forecast_time'] <= t_end)]
    final_df['model'] = model
    final_df.to_csv(outpath, index=False)
    return final_df

def get_multimodel_df(models=FULL_MODELS,
                      dirs=[d1_march,d2_may,d3_summer19,d4_last_week,historic_completion],out_d=out_d):
    multimodel_df = pd.DataFrame()
    for model in models:
        if model != 'GEPS':
            paths = [r"%s\live_degdays_%s.csv"%(d,model) for d in dirs]
        else:
            paths = [r"%s\live_degdays_%s_Pmean.csv" % (d, model) for d in dirs]
        outpath = r"%s\%s_%s.csv"%(out_d, outfile_template,model)
        model_df = unite_model_degdays(model,paths,outpath)
        if multimodel_df.shape[0] == 0:
            multimodel_df = model_df
        else:
            common = list(set(list(model_df)).intersection(set(list(multimodel_df))))
            multimodel_df = multimodel_df.merge(model_df,on=common,how="outer")
    multimodel_df['feature_type'] = multimodel_df['feature_type'].fillna('HDD')
    return multimodel_df


def get_daily_multimodel(multimodel_df,last_model_hour=LAST_MODEL_HOUR):
    multimodel_df = multimodel_df[(multimodel_df['forecast_time'].dt.month.isin(MONTHS)) &
                                  (multimodel_df['feature_type'] == 'HDD') & (
                                              multimodel_df['weight'].isin(WEIGHTS))]
    multimodel_df['days_ahead'] = (multimodel_df['validation_day'] - multimodel_df['forecast_time']).dt.days
    multimodel_df['fake_forecast_time'] = multimodel_df['forecast_time'] - td(hours=last_model_hour, minutes=1)
    multimodel_df['fake_day'] = multimodel_df['fake_forecast_time'].dt.date
    multimodel_df = multimodel_df.sort_values(["forecast_time", "validation_day", "model", "weight"]).drop_duplicates(
        subset=[x for x in list(multimodel_df) if x not in ['Value', 'weight']])
    aa = 1
    daily_multimodel_df = pd.DataFrame()
    for fake_day, fake_day_group in multimodel_df.groupby(["fake_day","model","weight","feature_type"]):
        print ("Handling fake_day: %s"%fake_day[0])
        fake_day_df = pd.DataFrame()
        forecast_times = []
        on = ["fake_day", "model", "weight", "feature_type", 'validation_day']
        for forecast_time,fcst_group in fake_day_group.groupby("forecast_time"):
            fcst_group = fcst_group.rename(columns={"Value":"Value_%sZ"%forecast_time.hour})
            fcst_group = fcst_group[[x for x in list(fcst_group) if x in on or 'Value' in x]]
            if fake_day_df.shape[0] == 0:
                fake_day_df = fcst_group
            else:
                fake_day_df = fake_day_df.merge(fcst_group,on=on)
            forecast_times.append(forecast_time)
            aa = 1
        fake_day_df['forecast_time'] = max(forecast_times)
        if daily_multimodel_df.shape[0] == 0:
            daily_multimodel_df = fake_day_df
        else:
            common = list(set(list(daily_multimodel_df)).intersection(set(list(fake_day_df))))
            daily_multimodel_df = daily_multimodel_df.merge(fake_day_df,on=common,how="outer")
        aa = 1
    return daily_multimodel_df

def get_expected_days(model,last_model_hour):
    expected_days = 15  # if model in ['GFS','PARA','GEFS','GEFSL','GEPS']
    if model == 'GEM':
        expected_days = 9
    if model == 'GFS':
        expected_days -=1
    if model in ['GEFS','GEFSL','GEPS','GFS']:
        if last_model_hour == 12:
            expected_days -= 1


    return expected_days


def analyze_daily_models(model,daily_multimodel_df,last_model_hour=LAST_MODEL_HOUR,method='sum',days_ahead=DAYS_AHEAD_DEFAULT):
    daily_multimodel_df = daily_multimodel_df.dropna()
    daily_multimodel_df['days_ahead'] = (daily_multimodel_df['validation_day'] - daily_multimodel_df['forecast_time']).dt.days
    daily_multimodel_df['ones'] = 1
    check_entries = daily_multimodel_df.groupby(["forecast_time","model"]).sum().reset_index()

    if method == 'sum':
        good_forecasts_models_tuples = check_entries[check_entries.apply(lambda x: x['ones'] ==get_expected_days(x['model'],last_model_hour),axis=1)][['forecast_time','model']].apply(lambda x:
                                                                                                                (x['model'],x['forecast_time']),axis=1).tolist()
        daily_multimodel_df2 = daily_multimodel_df[daily_multimodel_df.apply(lambda x: (x['model'],x['forecast_time']), axis=1).isin(good_forecasts_models_tuples)]
        d = daily_multimodel_df2.groupby(["forecast_time","model","weight","feature_type"]).sum().reset_index()
    elif method == 'mean':
        good_forecasts_models_tuples = check_entries[check_entries.apply(lambda x: x['ones'] >= get_expected_days(x['model'], last_model_hour), axis=1)][
            ['forecast_time', 'model']].apply(lambda x: (x['model'], x['forecast_time']), axis=1).tolist()
        daily_multimodel_df2 = daily_multimodel_df[daily_multimodel_df.apply(lambda x: (x['model'], x['forecast_time']), axis=1).isin(
                good_forecasts_models_tuples)]
        daily_multimodel_df2 = daily_multimodel_df2[daily_multimodel_df2['days_ahead'].isin(days_ahead)]
        d = daily_multimodel_df2.groupby(["forecast_time", "model", "weight", "feature_type"]).mean().reset_index()

    prev_hours = [(last_model_hour - 6*i)%24 for i in range(1,4)]
    if model not in EXTENDED_MODELS:
        if last_model_hour in [6,18]:
            last_model_hour -= 6
    if last_model_hour == 0:
        d['diff_0Z-12Z'] = d['Value_0Z'] - d['Value_12Z']
        if model in EXTENDED_MODELS:
            d['diff_18Z-12Z'] = d['Value_18Z'] - d['Value_12Z']
            d['diff_18Z-6Z'] = d['Value_18Z'] - d['Value_6Z']
            d['diff_12Z-6Z'] = d['Value_12Z'] - d['Value_6Z']
            d['diff_18Z-3Prev'] = d['Value_18Z'] - (d[['Value_6Z','Value_12Z']].mean(axis=1))
    elif last_model_hour == 12:
        d['diff_12Z-0Z'] = d['Value_12Z'] - d['Value_0Z']
        if model in EXTENDED_MODELS:
            d['diff_6Z-0Z'] = d['Value_6Z'] - d['Value_0Z']
            d['diff_6Z-18Z'] = d['Value_6Z'] - d['Value_18Z']
            d['diff_12Z-6Z'] = d['Value_12Z'] - d['Value_6Z']
            d['diff_0Z-18Z'] = d['Value_0Z'] - d['Value_18Z']
            d['diff_6Z-3Prev'] = d['Value_6Z'] - (d[['Value_0Z', 'Value_18Z']].mean(axis=1))
    elif last_model == 6:
        d['diff_0Z-18Z'] = d['Value_0Z'] - d['Value_18Z']
        d['diff_0Z-12Z'] = d['Value_0Z'] - d['Value_12Z']
        d['diff_18Z-12Z'] = d['Value_18Z'] - d['Value_12Z']
        d['diff_18Z-6Z'] = d['Value_18Z'] - d['Value_6Z']
        d['diff_0Z-3Prev'] = d['Value_0Z'] - (d[['Value_12Z', 'Value_18Z']].mean(axis=1))

    d3 = d[d['model'] == model]
    if last_model_hour == 0:
        if model in EXTENDED_MODELS:
            d3['is_hit_0-12'] = np.sign(d3['diff_0Z-12Z']*d3['diff_18Z-12Z']).apply(lambda x: 0 if x < 0 else 1)
            d3['is_hit_0-3Prev'] = np.sign(d3['diff_0Z-12Z']*d3['diff_18Z-3Prev']).apply(lambda x: 0 if x < 0 else 1)
    elif last_model_hour == 12:
        if model in EXTENDED_MODELS:
            d3['is_hit'] = np.sign(d3['diff_12Z-0Z'] * d3['diff_6Z-0Z']).apply(lambda x: 0 if x < 0 else 1)
            d3['is_hit_0-3Prev'] = np.sign(d3['diff_12Z-0Z'] * d3['diff_6Z-3Prev']).apply(lambda x: 0 if x < 0 else 1)
    return d3



def get_hr_of_cols(df,predictor,target,thresh=1):
    quantile = abs(df[predictor]).quantile(1-thresh)
    filter_ind = abs(df[predictor]) > quantile
    df = df[filter_ind]
    is_hit = np.sign(df[predictor] * df[target]).apply(lambda x: 0 if x <0 else 1)
    is_hit = np.sign(df[predictor] * df[target]).apply(lambda x: 0 if x <0 else 1)
    return is_hit.mean(),is_hit,quantile


load_from_new = True #False
last_model = 12
backup_file = os.path.join(HOME,"Xdata","daily_multimodel_df_allMonths_partialDates_Full_last=%s_take3.csv")%last_model
method = 'mean' #'sum'


if load_from_new:
    multimodel_df = get_multimodel_df()
    daily_multimodel = get_daily_multimodel(multimodel_df,last_model_hour=last_model)
else:
    daily_multimodel = pd.read_csv(backup_file,parse_dates=['forecast_time','validation_day','fake_day'])

## test num of entries of given model
"""
model = 'PARA' #'GFS'
daily_multimodel2 = daily_multimodel
daily_multimodel2["ones"] = 1
daily_multimodel2 = daily_multimodel[daily_multimodel['model'] == model]
a = daily_multimodel2.groupby(["forecast_time","model"]).mean().reset_index()
#a.groupby(a.forecast_time.dt.to_period("M")).sum()[["ones"]].plot(kind='bar')
a.groupby([a.forecast_time.dt.to_period("M"),"model"]).sum()[["ones"]].plot(kind='bar')
plt.tight_layout()
"""

add_month_feature= True
months = [10,11,12,1,2,3,4]
models_str = 'GFS+PARA+GEFS'


daily_multimodel = daily_multimodel[daily_multimodel['forecast_time'].dt.month.isin(months)] # 10,11,4
days_ahead = list(range(1,16)) #range(16) #range(8,16) #[10,11,12,13,14,15,16]
d3_paraco = analyze_daily_models('PARACO',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_gefs = analyze_daily_models('GEFS',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_para = analyze_daily_models('PARA',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_gfs = analyze_daily_models('GFS',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_eps = analyze_daily_models('EPS',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_ec = analyze_daily_models('EC',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_geps = analyze_daily_models('EC',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)
d3_gem = analyze_daily_models('GEM',daily_multimodel,last_model_hour=last_model,method=method,days_ahead=days_ahead)

models_tuples_for_xfile = [("PARA",d3_para),("GFS",d3_gfs),
                           ("GEFS", d3_gefs),("EC",d3_ec),("EPS", d3_eps),
                           ("GEPS", d3_geps),("GEM",d3_gem)]
if models_str == 'GFS+PARA+GEFS':
    models_tuples_for_xfile = models_tuples_for_xfile[:3]


multimodel_final_xs = pd.DataFrame()
for model_name, model_df in models_tuples_for_xfile:
    value_cols = [x for x in list(model_df) if 'Value' in x or 'diff' in x]
    renaming_dict = {k: k+"_%s"%model_name for k in value_cols}
    model_df = model_df.rename(columns=renaming_dict)[['forecast_time']+[v for k,v in renaming_dict.items()]]
    if multimodel_final_xs.shape[0] == 0:
        multimodel_final_xs = model_df
    else:
        multimodel_final_xs = multimodel_final_xs.merge(model_df,on=['forecast_time'],how="outer")
multimodel_final_xs = multimodel_final_xs.sort_values("forecast_time")

# Fill nans
for col in list(multimodel_final_xs):
    if 'diff' in col:
        multimodel_final_xs[col] = multimodel_final_xs[col].fillna(0)
    elif 'Value' in col:
        multimodel_final_xs[col] = multimodel_final_xs[col].fillna(multimodel_final_xs[col].mean())

if add_month_feature:
    multimodel_final_xs['month'] = multimodel_final_xs['forecast_time'].dt.month

multimodel_final_xs_for_write = multimodel_final_xs[[x for x in list(multimodel_final_xs) if x not in ['weight','days_ahead']]]
multimodel_final_xs_for_write.to_csv(os.path.join(HOME,"Xdata","","X_file_DailyDiffs_%s_lastModel=%s_daysAhead=[%s-%s]_Months=[%s-%s]_take5_old.csv")%(models_str,last_model,days_ahead[0],days_ahead[-1],months[0],months[-1]),index=False)

ref_df = multimodel_final_xs
for thresh in [0.1 * i for i in range(1, 11)]:
    if last_model == 0:
        hr, is_hit, quantile = get_hr_of_cols(ref_df, 'diff_18Z-12Z_GEFS', 'diff_0Z-12Z_GEFS', thresh)
    elif last_model == 12:
        hr, is_hit, quantile = get_hr_of_cols(ref_df, 'diff_6Z-0Z_PARA', 'diff_12Z-0Z_PARA', thresh)
    print("Threshold: %s (> %s) (%s Trades)  | HR: %s" % (thresh, quantile, is_hit.shape[0], hr))

d3['super_avg'] = (d3['diff_18Z-12Z_PARA'] + 2*d3['diff_18Z-12Z_PARACO'] + 2*d3['diff_18Z-12Z']) / 5
d3['is_hit3'] = np.sign(d3['super_avg'] * d3['diff_0Z-12Z']).apply(lambda x: 0 if x < 0 else x)
i = np.sign(d3['diff_18Z-12Z_PARACO'] * d3['diff_18Z-12Z']) == 1
d3[i][['forecast_time','diff_0Z-12Z','diff_0Z-12Z_PARA','diff_18Z-12Z','diff_18Z-12Z_PARACO']].plot(x='forecast_time',kind='bar')
aa = 1
