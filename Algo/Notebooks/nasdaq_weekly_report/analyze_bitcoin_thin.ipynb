#%%
from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v4
from Algo.Performance_Analysis.clusters_auto_selection import check_drop_losing_hours,check_performance_by_hour,check_performance_by_mode
from Algo.Performance_Analysis.clusters_auto_selection import *


import copy

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT,DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR
from Algo.Learning.research_plays import rolling_calmar
from matplotlib import pyplot as plt

from Algo.Utils.kpis_calculation import calc_calmar, calc_sharpe
from Algo.Utils.calendar_handle import get_opex_week_monday

BREAK = True

#%%

#%%
csv = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\NG_2018-19_frontMonth_tz_Live.csv"
csv = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data_General\BitcoinMini\MBT_frontMonth_tz_Live_15mins.csv"
#%%
from Algo.Stocks_Research.Nasdaq.analyze_nasdaq_hours_correlation import get_pivot,add_delta_ys, prepare_df,NASDAQ_CSV,BITCOIN_CSV


df = prepare_df(csv,time_col='time_utc')[0]
# df = prepare_df(NASDAQ_CSV)[0]
#%% md

#%%
from Algo.Notebooks.Utils.nasdaq_strategies_encoder import periods_list_to_strings,unite_consecutive_hours

opex_week_reference = 'is_opex_week_dummy'
# opex_week_reference = 'is_opex_week_+1w'
# opex_week_reference = 'is_opex_week'
# opex_week_reference = 'is_opex_week_+1w'
# opex_week_reference = 'is_week_of_month_2'
# opex_week_reference = 'is_fomc_week'
# opex_week_reference = 'is_fomc_day'
# opex_week_reference = 'is_cpi_week'
# opex_week_reference = 'is_cpi_day'
# opex_week_reference = 'is_pce_week'
# opex_week_reference = 'is_fomc_and_opex_week'

# weekday_for_hourly = 0
weekdays_for_hourly = [0,1,2,3,4]
# weekdays_for_hourly = [3]


grouper = 'hour'
# grouper = '15M'
grouper = '30M'
grouper = '2H'
grouper = '3H'
grouper = '4H'

weekly_upside_hours = [2,3,4,5,10,11,12,13,18,19,20,21,22]
w = 1

# hours_to_include = list(range(13,17))
hours_to_include = list(range(0,24))
hours_to_include2 = list(range(4,12))

hours_to_include_by_weekday = {
    0: [x for x in hours_to_include if x//2*2 in [2,6,8,14,20]
        if x not in [21]],
    2: [x for x in hours_to_include 
#                     if x//4*4 in [0,16]
                    if (
                     x//4*4 in [0,4,8]
                    or x in [14,15,20]
                        )
                    and x not in [] and x//2*2 not in [4,10,6]
                    ],
    3: [x for x in hours_to_include if x//2*2 in [0,6,8,10,16,22]
        if x not in [1,6,16,23]] 

}

# hours_to_include = hours_to_include_by_weekday[3]
# hours_to_include = hours_to_include2
# hours_to_include = [x for x in hours_to_include if x not in hours_to_include2]
# print(hours_to_include)

hhmm_to_exclude = []
hhmm_to_include = ['2230','2245']
hhmm_to_include = []
# hhmm_to_include = ["1330","1345",'1030','1045']

# hours_to_include = hours_to_include2
# hours_to_include = [x for x in hours_to_include if x not in hours_to_include2]
# hours_to_include = list(range(18,21))
# hours_to_include = weekly_upside_hours
df_for_pivot = df[(df['date'].dt.weekday.isin(weekdays_for_hourly))&
                  (((df['date'].dt.hour.isin(hours_to_include))&
                  ~(df['date'].dt.strftime('%H%M').isin(hhmm_to_exclude)))
                    |(df['date'].dt.strftime('%H%M').isin(hhmm_to_include)))]

print((periods_list_to_strings(unite_consecutive_hours(hours_to_include))))

pivot_opex = pd.pivot_table(df[df['date'].dt.hour.isin(range(0,20))],values=['y'],index=['week'],columns=['weekday',opex_week_reference],
                            aggfunc=np.sum).fillna(0)
pivot_opex_daily = pd.pivot_table(df_for_pivot,values=['y'],index=['day'],columns=[grouper,opex_week_reference],
                            aggfunc=np.sum).fillna(0)
pivot_opex_weekly = pd.pivot_table(df[df['date'].dt.hour.isin(range(0,20))],values=['y'],index=['week'],columns=['which_opex_week'],
                            aggfunc=np.sum).fillna(0)
pivot_opex.cumsum().plot(figsize=(10,10),style=['-','--']*7,
                         color=np.repeat(['tab:blue','tab:orange','tab:red','tab:olive','tab:gray',
                                          'tab:green'],2))
pivot_opex_daily.rolling(w,w).mean().cumsum()[list(pivot_opex_daily)[:12]].plot(figsize=(10,10),style=['-','--']*7,
                         color=np.repeat(['tab:blue','tab:orange','tab:red','tab:olive','tab:gray',
                                          'tab:green'],2),
                               title=f'Hourly Analysis for weekday = {weekdays_for_hourly}')
try:
    pivot_opex_daily.rolling(w,w).mean().cumsum()[list(pivot_opex_daily)[12:24]].plot(figsize=(10,10),style=['-','--']*7,
                         color=np.repeat(['tab:blue','tab:orange','tab:red','tab:olive','tab:gray',
                                          'tab:green'],2),
                               title=f'Hourly Analysis for weekday = {weekdays_for_hourly}')
    pivot_opex_daily.rolling(w,w).mean().cumsum()[list(pivot_opex_daily)[24:36]].plot(figsize=(10,10),style=['-','--']*7,
                         color=np.repeat(['tab:blue','tab:orange','tab:red','tab:olive','tab:gray',
                                          'tab:green'],2),
                               title=f'Hourly Analysis for weekday = {weekdays_for_hourly}')
    pivot_opex_daily.rolling(w,w).mean().cumsum()[list(pivot_opex_daily)[36:48]].plot(figsize=(10,10),style=['-','--']*7,
                         color=np.repeat(['tab:blue','tab:orange','tab:red','tab:olive','tab:gray',
                                          'tab:green'],2),
                               title=f'Hourly Analysis for weekday = {weekdays_for_hourly}')

except:
    pass
pivot_opex_weekly.cumsum().plot(figsize=(10,10),style=['-','--']*7)


d = pivot_opex_daily.reset_index()
d['weekday'] = pd.to_datetime(d['day']).dt.weekday
d['week'] = pd.to_datetime(d['day']) - pd.to_datetime(d['day']).dt.weekday.apply(lambda x: td(days=x))
d['total_pnl'] = d[[x for x in list(d) if 'y' == x[0]]].sum(axis=1) #=
d2 = pd.pivot_table(d,columns=['weekday'],index=['week'],values=['total_pnl'],aggfunc=np.sum)
ax = d2.cumsum().plot(title=f'chosen hours total returns Days = {weekdays_for_hourly}',
                      figsize=(8,8),style=['--']*7)


only_trues = pivot_opex_daily[[x for x in list(pivot_opex_daily) if x[2] == True]]
only_false = pivot_opex_daily[[x for x in list(pivot_opex_daily) if x[2] == False]]
d3 = pivot_opex_daily.sum(axis=1).reset_index() #.cumsum().plot()
d3_trues = only_trues.sum(axis=1).reset_index()
d3_false = only_false.sum(axis=1).reset_index()


d3= d3.rename(columns={0: 'Weekly returns','day':'week'})
d3_trues = d3_trues.rename(columns={0: f'Weekly returns ({opex_week_reference} = True)','day':'week'})
d3['week'] = pd.to_datetime(d3['week'])
d3_trues['week'] = pd.to_datetime(d3_trues['week'])



#### WEEKLY SUMMARY


ax = d3.set_index('week').resample('W').sum().cumsum().plot(figsize=(10,10),ax=ax)
ax = d3_trues.set_index('week').resample('W').sum().cumsum().plot(figsize=(10,10),ax=ax)

try:
    d3_false = d3_false.rename(columns={0: f'Weekly returns ({opex_week_reference} = False)','day':'week'})
    d3_false['week'] = pd.to_datetime(d3_false['week'])
    ax = d3_false.set_index('week').resample('W').sum().cumsum().plot(figsize=(10,10),ax=ax)
    add_false = True
except:
    add_false = False


plt.legend([str(x) for x in weekdays_for_hourly]+['Weekly','Weekly_Trues']+(['Weekly Falses'] if add_false else []))
#%%
# list(d3_false)
# list(d3_trues)
# d3.set_index('week').resample('W').sum()
#%%

#%%

#%%
# pivot_opex_daily
from Algo.Notebooks.Utils.nasdaq_strategies_encoder import periods_list_to_strings,unite_consecutive_hours,get_next_15min,unite_consecutive_delta_ys


df_for_pivot['HHMM'] = df_for_pivot['date'].dt.strftime('%H%M')
df_for_pivot2 = df_for_pivot.drop_duplicates(subset=['HHMM','weekday'])
hhmms_by_weekday = {}
for weekday in [0,1,2,3,4]: 
    raw_lst = df_for_pivot2.query('weekday == @weekday')['HHMM'].tolist()
    following_hhmm_lst = [get_next_15min(x) for x in raw_lst]
    delta_ys_lst = [f'y_{x}-{y}' for x,y in zip(raw_lst,following_hhmm_lst)]
    hhmms_by_weekday[weekday] = unite_consecutive_delta_ys(delta_ys_lst)
hhmms_by_weekday
#%% md
## Try to formulate the strategy!
#%%

#%%
['y_1400-1600', 'y_2030-2200','y_2230-2355']
#%%
raise
#%%
#raise
ALL_15MIN_DT_SERIES = pd.Series(pd.date_range(dtdt(1990,6,28),dtdt(1990,6,28,23,45),freq='15min'))
ALL_15MIN_HHMM = ALL_15MIN_DT_SERIES.dt.strftime('%H%M').tolist()

opex_week_reference = 'is_opex_week_+-2w'
# opex_week_reference = 'is_opex_week_-2w'
# opex_week_reference = 'is_fomc_week'
# opex_week_reference = 'is_fomc_and_opex_week'

# weekday_for_hourly = 0
weekdays_for_hourly = [0,1,2,3,4]
weekdays_for_hourly = [4]


for weekday in [0,1,2,3,4]:
    for opex_week_reference in ['is_opex_week_+-2w','is_opex_week_-1w','is_opex_week','is_opex_week_+1w']:
        for grouper in ['2H','hour','30M','15M']:
            relevant_dates = pd.to_datetime(df[(df[opex_week_reference])&(df['weekday']==weekday)]['date'].dt.date).drop_duplicates().sort_values().tolist()
            pivot_opex_daily = pd.pivot_table(df[(df['date'].dt.weekday.isin(weekdays_for_hourly))&
                                        (df['date'].dt.hour.isin(hours_to_include))],values=['y'],index=['day'],columns=[grouper,opex_week_reference],
                            aggfunc=np.sum).fillna(0)

            opex_week_cols = [x for x in list(pivot_opex_daily) if x[2] == True]
            non_opex_week_cols = [x for x in list(pivot_opex_daily) if x[2] == False]

            longterm_calmar = pivot_opex_daily.rolling(30,5).apply(lambda x: calc_calmar(x,False)[0]).shift(1)
            shorterm_calmar = pivot_opex_daily.rolling(16,5).apply(lambda x: calc_calmar(x,False)[0]).shift(1)
            super_shorterm_calmar = pivot_opex_daily.rolling(10,5).apply(lambda x: calc_calmar(x,False)[0]).shift(1)

            longterm_ppt = pivot_opex_daily.replace(0,np.nan).rolling(30,5).mean().shift(1)
            shorterm_ppt = pivot_opex_daily.replace(0,np.nan).rolling(16,5).mean().shift(1)
            super_shorterm_ppt = pivot_opex_daily.replace(0,np.nan).rolling(10,5).mean().shift(1)


            longterm_hr = (np.sign(pivot_opex_daily.replace(0,np.nan)).rolling(30,5).mean()+0.5).shift(1)
            shorterm_hr = (np.sign(pivot_opex_daily.replace(0,np.nan)>0).rolling(16,5).mean()+0.5).shift(1)
            super_shorterm_hr = (np.sign(pivot_opex_daily.replace(0,np.nan)>0).rolling(10,5).mean()+0.5).shift(1)
            # todo verify the HR!

            longterm_maxd = pivot_opex_daily.rolling(30,5).apply(lambda x: calc_calmar(x,False)[1]).shift(1)
            shorterm_maxd = pivot_opex_daily.rolling(16,5).apply(lambda x: calc_calmar(x,False)[1]).shift(1)

            LONGTERM_CALMAR = 2
            SHORTERM_CALMAR = 4
            cond1_calmar_long = (longterm_calmar > LONGTERM_CALMAR)&(SHORTERM_CALMAR>4)
            cond2_calmar_short = (shorterm_calmar>SHORTERM_CALMAR)&(super_shorterm_ppt>0)
            cond3_hr = (longterm_hr > 0.8)&(shorterm_hr>0.6)&(abs(shorterm_maxd)<150)

            for col in opex_week_cols:
                hour = col[1]
                buying_days = set(pivot_opex_daily[cond1_calmar_long[col]|cond2_calmar_short[col]|cond3_hr[col]].index.tolist()).intersection(relevant_dates)
                if grouper == '2H':
                    total_15min_periods = [x for x in ALL_15MIN_DT_SERIES if x.hour//2 * 2 == hour]
                elif grouper == 'hour':
                    total_15min_periods = [x for x in ALL_15MIN_DT_SERIES if x.hour == hour]
                elif grouper == '30M':
                    total_15min_periods = [x for x in ALL_15MIN_DT_SERIES if x.hour == int(hour[:2]) and x.minute//30 * 30 == int(hour[-2:])]
                elif grouper == '15M':
                    total_15min_periods = [x for x in ALL_15MIN_DT_SERIES if x.strftime('%H%M') == hour]

                # NEXT
                # we pick up all the long 15 min periods
pivot_opex_daily


