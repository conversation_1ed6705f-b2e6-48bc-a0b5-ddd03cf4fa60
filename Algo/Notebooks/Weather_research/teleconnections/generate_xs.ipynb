#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime as dtdt
from sklearn.linear_model import LinearRegression
#%%

#%%
CSV_PATH = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\teleconnections\teleconnections_from_weathermodels.csv"
#%%
df = pd.read_csv(CSV_PATH,parse_dates=['forecast_time','validation_day'])

start_date = dtdt(2025,2,16)
df2 = df[df['forecast_time']>=start_date]

for model in ['EPS','GEFS']:
    pivot = df2.pivot_table(index=['forecast_time','validation_day'],columns=['metric','model',],values='value_filled')
    ax = pivot['EPO'][model].unstack().T.plot(title=f'{model} x EPO',figsize=(15,8),
                                              style=['-']*7+['-*']*7+['--']*7)
    # make the line width proportional to column number 
    for i, line in enumerate(ax.get_lines()):
        line.set_linewidth(1+i/2)
    plt.show()
    
    
#%%
start_date = dtdt(2021,1,1)
df2 = df[df['forecast_time']>=start_date]


df2['days_group'] = df2['day'].apply(lambda x: '0-7' if x<8 else '8-15')



pivot = df2.pivot_table(index=['forecast_time','days_group'],columns=['metric','model',],values='value_filled')
# ax = pivot['EPO'][model].unstack().T.plot(title=f'{model} x EPO',figsize=(15,8),
#                                           style=['-']*7+['-*']*7+['--']*7)
# make the line width proportional to column number 
# for i, line in enumerate(ax.get_lines()):
#     line.set_linewidth(1+i/2)
# plt.show()

pivot = pivot.unstack()

# fill index with all dates between the start and end that have hour 00 or 12 
idx = pd.date_range(start_date,df2['forecast_time'].max(),freq='12H')
pivot = pivot.reindex(idx,level=0)
pivot.sort_index()

for metric in ['EPO','NAO','AO']:
    for model in ['EPS','GEFS']:
        pivot[(metric,model,'diff_last-12h_8-15')] = pivot[metric][model]['8-15'].diff()
        pivot[(metric,model,'diff_last-24h_8-15')] = pivot[metric][model]['8-15'].diff(2)

# turn the columns from tuples to strings by joining the tuple elements with '_'
pivot.columns = ['_'.join(col).strip() for col in pivot.columns.values]
pivot_to_save = pivot.dropna(how='all').reset_index().rename(columns={'index':'forecast_time'}).round(3)
#pivot_to_save
#%%
# list(pivot_to_save.columns)

pivot_to_save_0z = pivot_to_save[pivot_to_save['forecast_time'].dt.hour==0] 
pivot_to_save_12z = pivot_to_save[pivot_to_save['forecast_time'].dt.hour==12]
pivot_to_save_12z['forecast_time'] = pivot_to_save_12z['forecast_time'] + pd.Timedelta(hours=12)
final_pivot = pd.merge(pivot_to_save_0z,pivot_to_save_12z,on=['forecast_time'],suffixes=('_0Z','_12Z'))
final_pivot['forecast_time'] = final_pivot['forecast_time'] + pd.Timedelta(hours=8)
final_pivot = final_pivot.rename(columns={'forecast_time':'date'})
final_pivot.to_csv(r'C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\teleconnections\teleconnections_diffs_Xs.csv',index=False)

#%%

#%%
