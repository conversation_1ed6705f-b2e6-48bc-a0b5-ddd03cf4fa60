#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime as dtdt
from sklearn.linear_model import LinearRegression
#%%

#%%
CSV_PATH = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\teleconnections\teleconnections_from_weathermodels.csv"
#%%
df = pd.read_csv(CSV_PATH,parse_dates=['forecast_time','validation_day'])

start_date = dtdt(2025,2,16)
df2 = df[df['forecast_time']>=start_date]

for model in ['EPS','GEFS']:
    pivot = df2.pivot_table(index=['forecast_time','validation_day'],columns=['metric','model',],values='value_filled')
    ax = pivot['EPO'][model].unstack().T.plot(title=f'{model} x EPO',figsize=(15,8),
                                              style=['-']*7+['-*']*7+['--']*7)
    # make the line width proportional to column number 
    for i, line in enumerate(ax.get_lines()):
        line.set_linewidth(1+i/2)
    plt.show()
    
    