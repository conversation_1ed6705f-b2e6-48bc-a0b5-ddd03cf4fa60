#%% md
Yes, there are several sources that provide solar and wind electricity generation capacity data across the United States with geographical information such as latitude and longitude. Here are a few sources you can check:

National Renewable Energy Laboratory (NREL): NREL provides maps and data on renewable energy resources, including solar and wind, with a focus on the United States. They offer geospatial data that provides latitude and longitude information for various solar and wind resources across the country. You can access their data through the OpenEI website.

U.S. Energy Information Administration (EIA): EIA provides a wealth of information on energy production and consumption across the United States. They offer data on renewable energy sources, including solar and wind, with a focus on state-level capacity and generation data. Their data includes location information for wind and solar installations.

Department of Energy (DOE): The DOE offers several datasets that include information on solar and wind energy resources across the United States. These datasets provide information on the potential for solar and wind energy production at specific locations, including latitude and longitude information.

OpenPV: OpenPV is an open-source database of solar photovoltaic installations in the United States. It provides detailed information on the location, size, and capacity of solar installations, including latitude and longitude information.

WindExchange: WindExchange is a platform provided by the DOE that provides information on wind resources across the United States. It includes data on wind speed and capacity at specific locations, including latitude and longitude information.

I hope this helps!
#%%
import requests

url = "https://api.openei.org/utility_rates"

params = {
    "version": "latest",
    "api_key": "YOUR_API_KEY_HERE",
    "lat": 37.7749, # Example latitude value
    "lon": -122.4194, # Example longitude value
    "sector": "residential",
    "renewable_technologies": "Wind,Solar",
    "page": 1,
    "per_page": 10
}

response = requests.get(url, params=params)

if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print("Error occurred:", response.status_code)

#%%

#%%
import requests

url = "https://api.eia.gov/series/"

params = {
    "api_key": "YOUR_API_KEY_HERE",
    "series_id": "ELEC.GEN.WND-US-ALL.A",
    "start": "20200101",
    "end": "20201231"
}

response = requests.get(url, params=params)

if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print("Error occurred:", response.status_code)

#%%

#%%
import requests

url = "https://developer.nrel.gov/api/pvwatts/v6.json"

params = {
    "api_key": "YOUR_API_KEY_HERE",
    "lat": 37.7749, # Example latitude value
    "lon": -122.4194, # Example longitude value
    "system_capacity": 4,
    "module_type": 0,
    "losses": 14,
    "array_type": 0,
    "tilt": 25,
    "azimuth": 180
}

response = requests.get(url, params=params)

if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print("Error occurred:", response.status_code)

#%%

#%%
import requests

url = "https://openpv.nrel.gov/api/v1/pv_projs"

params = {
    "api_key": "YOUR_API_KEY_HERE",
    "state": "CA",
    "city": "San Francisco",
    "page": 1,
    "per_page": 10
}

response = requests.get(url, params=params)

if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print("Error occurred:", response.status_code)

#%%

#%%
import requests

url = "https://developer.nrel.gov/api/wind-toolkit/v2/wind/wtk-download"

params = {
    "api_key": "YOUR_API_KEY_HERE",
    "attributes": "wind_speed",
    "names": "San Francisco",
    "start_date": "20200101",
    "end_date": "20201231",
    "utc": "false",
    "interval": "60"
}

response = requests.get(url, params=params)

if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print("Error occurred:", response.status_code)
