#%%
from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features,add_seasonal_diffs,add_eps_gaps,ttf_enrichment,coal_enrichment,add_rolling_momentum,\
                            ngf_enrichment,wti_enrichment,rsi_enrichment,add_previous_weeks_momentum,\
                                wind_enrichment,cfsm_enrichment,wind_vs_30d_enrichment,gdd_vs_30d_enrichment,\
                                    add_rolling_diffs,add_regional_diffs,add_0D_trend,clusters_enrichment,add_light_Xs
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Data_Processing.hisotical_values import main_historical_calc
from Algo.Learning.models_accuracy_calc import get_multimodel_cors_df,get_cors_df
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
from Algo.Learning.predict_eps import add_eps_preds as add_eps_preds_rolling_corrs,add_eps_preds_from_gefs


#%%
from Algo.Learning.algo_wrapper2 import get_results_df, wrap_analysis, wrap_predictor_handling

def get_predictor_profit(df,predictor_name,target_strat,default_end=dtdt(2030,1,1)):
    predictor_conf = PREDICTORS_DICT[predictor_name]
    #results_df = get_results_df(target_strat,comb_conf,j=0,results_df=df,return_preds_for_comb=False)
    preds = [p for p in predictor_conf['preds_for_comb'] if p != 'MEAN']
    df['MEAN'] = df[preds].mean(axis=1)
    results_df = wrap_predictor_handling(df,predictor_name,target_strat,predictor_conf,comb_profits=True,j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=[0,1,2,3,4],results_df=None)
    return results_df

def get_predictor_profit_from_conf(df,predictor_conf,predictor_name,target_strat,default_end=dtdt(2030,1,1)):

    preds = [p for p in predictor_conf['preds_for_comb'] if p != 'MEAN']
    df['MEAN'] = df[preds].mean(axis=1)
    results_df = wrap_predictor_handling(df,predictor_name,target_strat,predictor_conf,comb_profits=True,j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=[0,1,2,3,4],results_df=None)
    return results_df


def generate_conf(chosen_ratio,preds,start=dtdt(2021,1,1)):
    return {'start': start, 'chosen_ratio': chosen_ratio,
                 'preds_for_comb': preds,
                    'is_prod': True}


#%%

PREDICTORS_DICT = {'y_1800-1830_tmpStrat': {'start': dtdt(2022,1,1), 'chosen_ratio': 0.44,
                                 'preds_for_comb':
                               ['diff_0Z_0-13_last-Prev2_EPS'],
                            'is_prod': True},
                }

#%%
main_csv = r"C:\Users\<USER>\Documents\Work\Amazon\XYs\Enriched_XYs\XY_a_GDD_v8_0Zb.csv"

clusters_v2_csv = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\Clusters\Cluster_models_Diffs_MaxHours=48_Ratio=4.0_v2.csv"
clusters_v2_long_csv = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\Clusters\Cluster_models_Diffs_MaxHours=48_Ratio=4.0_v2_long.csv"
#%%
import pandas as pd
a0 = pd.read_csv(main_csv,parse_dates=['date'])

raise
clusters_df = pd.read_csv(clusters_v2_csv,parse_dates=['date'])
clusters_df = clusters_df.rename(columns={x: x+'_v2' for x in list(clusters_df)
                                          if x != 'date'})
clusters_df['date'] = clusters_df['date'] + td(hours=2)
clusters_df = clusters_df[['date']+[x for x in list(clusters_df) if 'diff' in x]]
a = a0.merge(clusters_df,on=['date'])


#%%
a0[['date']+[x for x in list(a0) if 'diff_' in x and '12Z' in x and 'GEFS' in x
             and '0-16' in x]]
#%%
a0[['t_open']+[x for x in list(a0) if 'cluster' in x]]
#%%
[x for x in list(a) if 'cluster' in x]
#%%
days = '0to15'
clusters_v2_preds_0to12h = [#'diff_18Z_best_d7to15_(0-12)h_v2','diff_0Z_best_d7to15_(0-12)h_v2',
            # 'diff_6Z_best_d7to15_(0-12)h_v2','diff_12Z_best_d7to15_(0-12)h_v2',
            #      'diff_18Z_best_d0to15_(0-12)h_v2','diff_12Z_best_d0to15_(0-12)h_v2'
                    f'diff_0Z_best_d{days}_(0-12)h_v2',f'diff_6Z_best_d{days}_(0-12)h_v2',
                    f'diff_12Z_best_d{days}_(0-12)h_v2',f'diff_18Z_best_d{days}_(0-12)h_v2',
]
clusters_v2_preds_0to24h = [ #'diff_18Z_best_d7to15_(0-24)h_v2','diff_0Z_best_d7to15_(0-24)h_v2',
                             #'diff_6Z_best_d7to15_(0-24)h_v2','diff_24Z_best_d7to15_(0-24)h_v2',
                             # 'diff_18Z_best_d{days}_(0-24)h_v2','diff_12Z_best_d{days}_(0-24)h_v2'
                                f'diff_0Z_best_d{days}_(0-24)h_v2',f'diff_6Z_best_d{days}_(0-24)h_v2',
                                f'diff_12Z_best_d{days}_(0-24)h_v2',f'diff_18Z_best_d{days}_(0-24)h_v2',
                                ]
clusters_v2_preds_0to18h = [#'diff_18Z_best_d7to15_(0-18)h_v2','diff_0Z_best_d7to15_(0-18)h_v2',
         #'diff_6Z_best_d7to15_(0-18)h_v2','diff_12Z_best_d7to15_(0-18)h_v2',
         # 'diff_18Z_best_d{days}_(0-18)h_v2','diff_12Z_best_d{days}_(0-18)h_v2'
            f'diff_0Z_best_d{days}_(0-18)h_v2',f'diff_6Z_best_d{days}_(0-18)h_v2',
            f'diff_12Z_best_d{days}_(0-18)h_v2',f'diff_18Z_best_d{days}_(0-18)h_v2',]

clusters_v2_preds = clusters_v2_preds_0to12h+clusters_v2_preds_0to18h+clusters_v2_preds_0to24h
clusters_v2_preds_avg = [x.replace('best','avg') for x in clusters_v2_preds_0to12h+clusters_v2_preds_0to18h+clusters_v2_preds_0to24h]

#%%

# results = get_predictor_profit(a,'y_1800-1830_tmpStrat','y_0800-1845')
results = get_predictor_profit_from_conf(a,generate_conf(0.45,[x for x in clusters_v2_preds
                                                               if not ('12Z' in x or '18Z' in x)]),'y_1800-1830_tmpStrat','y_0800-1845')
results = results[['date']+[c for c in list(results) if 'profit' in c]]
results.set_index('date').cumsum().plot(figsize=(10,10),style=['-']*7+['-*']*7)
plt.show()
aa = 1
results = get_predictor_profit_from_conf(a,generate_conf(0.45,[x for x in clusters_v2_preds
                                                               if ('12Z' in x or '18Z' in x)]),'y_1800-1830_tmpStrat','y_0800-1845')
results = results[['date']+[c for c in list(results) if 'profit' in c]]
results.set_index('date').cumsum().plot(figsize=(10,10),style=['-']*7+['-*']*7)
plt.show()
#%%

# results = get_predictor_profit(a,'y_1800-1830_tmpStrat','y_0800-1845')
current_preds = clusters_v2_preds_avg
current_preds = [x for x in current_preds if x.replace('_v2','_cluster48') in list(a)]
# results = get_predictor_profit_from_conf(a,generate_conf(0.45,current_preds[:4]+[x.replace('_v2','_cluster48') for x in current_preds[:4]]),'y_1800-1830_tmpStrat','y_0800-1845')
results = get_predictor_profit_from_conf(a,generate_conf(0.45,clusters_v2_preds_avg[:4]+clusters_v2_preds[:4]),'y_1800-1830_tmpStrat','y_0800-1845')
results = results[['date']+[c for c in list(results) if 'profit' in c]]
results.set_index('date').cumsum().plot(figsize=(10,10),style=['-']*4+['-*']*4,color=['b','g','y','r']*2)
aa = 1

#%%
a0 = pd.read_csv(main_csv,parse_dates=['date'])

clusters_df = pd.read_csv(clusters_v2_long_csv,parse_dates=['date'])
clusters_df = clusters_df.rename(columns={x: x+'_v2_long' for x in list(clusters_df)
                                          if x != 'date'})
clusters_df['date'] = clusters_df['date'] + td(hours=2)
clusters_df = clusters_df[['date']+[x for x in list(clusters_df) if 'diff' in x]]
a = a0.merge(clusters_df,on=['date'])

list(clusters_df)
#%%

current_preds_0z = [x for x in list(clusters_df) if 'diff' in x and '_d' in x
                    and '_0Z_' in x]
current_preds_0z_21to42 = [x for x in list(clusters_df) if 'diff' in x and '_d' in x
                    and '_0Z_' in x and '21to42' in x]

current_preds_0z_14to28 = [x for x in list(clusters_df) if 'diff' in x and '_d' in x
                    and '_0Z_' in x and '14to28' in x]
current_preds_0z_14to35 = [x for x in list(clusters_df) if 'diff' in x and '_d' in x
                    and '_0Z_' in x and '14to35' in x]
current_preds_0z_14to21 = [x for x in list(clusters_df) if 'diff' in x and '_d' in x
                    and '_0Z_' in x and '14to21' in x]


# results = get_predictor_profit_from_conf(a,generate_conf(0.45,current_preds[:4]+[x.replace('_v2','_cluster48') for x in current_preds[:4]]),'y_1800-1830_tmpStrat','y_0800-1845')

# len(current_preds_0z)
chosen_clusters = current_preds_0z_21to42
results = get_predictor_profit_from_conf(a,generate_conf(0.45,
                                                         chosen_clusters),'y_1800-1830_tmpStrat','y_0800-1845')
results = results[['date']+[c for c in list(results) if 'profit' in c]]
results.set_index('date').cumsum().plot(figsize=(10,10),style=['-']*4+['-*']*4,color=['b','g','y','r']*2)
# aa = 1

#%% md
# Check GDDs 11to15d Vs MA
#%%
a0 = pd.read_csv(main_csv,parse_dates=['date'])
candles_csv = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\NG_2018-19_frontMonth_tz_Live.csv"

# candles_df = pd.read_csv(candles_csv,parse_dates=['date','time_utc']).drop_duplicates(subset=['date'])
candles_df = pd.read_csv(candles_csv).drop_duplicates(subset=['date'])
candles_df['date'] = pd.to_datetime(candles_df['date'])
candles_df['time_utc'] = pd.to_datetime(candles_df['time_utc'])
#%% md

#%%
eps_vals = ['Value_0Z_9-13_EPS', 'Value_12Z_9-13_EPS'] # [x for x in list(a0) if 'Value' in x and 'EPS' in x and '9-' in x]
eps_vals_12to13 = ['Value_0Z_12-13_EPS', 'Value_12Z_12-13_EPS'] # [x for x in list(a0) if 'Value' in x and 'EPS' in x and '9-' in x]
eps_vals_0to13 = ['Value_0Z_0-13_EPS', 'Value_12Z_0-13_EPS'] # [x for x in list(a0) if 'Value' in x and 'EPS' in x and '9-' in x]
gefs_vals = [x for x in list(a0) if 'Value' in x and 'GEFS' in x and '14-16' in x]
para_vals = ['Value_0Z_8-16_PARA','Value_6Z_8-16_PARA','Value_12Z_8-16_PARA','Value_18Z_8-16_PARA',]
paraco_vals = ['Value_0Z_8-16_PARACO','Value_6Z_8-16_PARACO','Value_12Z_8-16_PARACO','Value_18Z_8-16_PARACO',]
para_vals_full = [x for x in list(a0) if 'Value' in x and 'PARA' in x and '8-16' in x]


# [x for x in list(a0) if 'Value_0Z' in x and '_EPS' in x and '13' in x]

#%%
latency = 6
df_gefs = a0[['date']+gefs_vals]
stacked_df = df_gefs.set_index('date').stack().reset_index(name='Value_11-15_GEFS').rename(columns={'level_1':'X'})
stacked_df['hour'] = stacked_df['X'].apply(lambda x: int(x.split('Value_')[1].split('Z')[0]))
#stacked_df['original_date'] = stacked_df['date'] - td(hours=8) + stacked_df['hour'].apply(lambda x: td(hours=x))
stacked_df['original_date'] = stacked_df['date'] - td(hours=8) + stacked_df['hour'].apply(lambda x: td(hours=x if x in [0,6] else x-24))
stacked_df = stacked_df.sort_values('original_date')
bb = 0
stacked_df['date'] = stacked_df['original_date'] + td(hours=latency)
stacked_df['Value_11-15_GEFS_ma2'] = stacked_df['Value_11-15_GEFS'].rolling(2,2).mean()
stacked_df['Value_11-15_GEFS_ma3'] = stacked_df['Value_11-15_GEFS'].rolling(3,2).mean()
stacked_df['Value_11-15_GEFS_ma4'] = stacked_df['Value_11-15_GEFS'].rolling(4,4).mean()
stacked_df['Value_11-15_GEFS_ma6'] = stacked_df['Value_11-15_GEFS'].rolling(6,4).mean()
stacked_df['Value_11-15_GEFS_ma8'] = stacked_df['Value_11-15_GEFS'].rolling(8,4).mean()
stacked_df['Value_11-15_GEFS_ma10'] = stacked_df['Value_11-15_GEFS'].rolling(10,4).mean()
stacked_df['Value_11-15_GEFS_ma12'] = stacked_df['Value_11-15_GEFS'].rolling(12,4).mean()
stacked_df['Value_11-15_GEFS_ma15'] = stacked_df['Value_11-15_GEFS'].rolling(15,4).mean()
stacked_df['Value_11-15_GEFS_ma20'] = stacked_df['Value_11-15_GEFS'].rolling(20,4).mean()

stacked_df['Value_11-15_GEFS_vs_ma3'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma3']
stacked_df['Value_11-15_GEFS_vs_ma4'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma4']
stacked_df['Value_11-15_GEFS_vs_ma6'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma6']
stacked_df['Value_11-15_GEFS_vs_ma8'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma8']
stacked_df['Value_11-15_GEFS_vs_ma10'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma10']
stacked_df['Value_11-15_GEFS_vs_ma12'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma12']
stacked_df['Value_11-15_GEFS_vs_ma15'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma15']
stacked_df['Value_11-15_GEFS_vs_ma20'] = stacked_df['Value_11-15_GEFS'] - stacked_df['Value_11-15_GEFS_ma20']

stacked_df['Value_11-15_GEFS_ma2_vs_ma4'] = stacked_df['Value_11-15_GEFS_ma2'] - stacked_df['Value_11-15_GEFS_ma4']
stacked_df['Value_11-15_GEFS_ma2_vs_ma6'] = stacked_df['Value_11-15_GEFS_ma2'] - stacked_df['Value_11-15_GEFS_ma6']
stacked_df['Value_11-15_GEFS_ma2_vs_ma8'] = stacked_df['Value_11-15_GEFS_ma2'] - stacked_df['Value_11-15_GEFS_ma8']
stacked_df['Value_11-15_GEFS_ma2_vs_ma12'] = stacked_df['Value_11-15_GEFS_ma2'] - stacked_df['Value_11-15_GEFS_ma12']


candles_df['date'] = candles_df['time_utc']
candles_df['y'] = candles_df['open'].diff().shift(-1)



#%%
def _analyze_performance(final_df, num_bins,pred,shift):
    part1 = [-2/2**c for c in range((num_bins-1)//2)]
    part2 = [2/2**c for c in range((num_bins-1)//2)]
    part2.reverse()
    # print(part1+[0]+part2)
    final_df[f'quantity_{pred}_{num_bins}_{shift}'] = pd.qcut(final_df[pred],num_bins,labels=part1+[0]+part2)
    profit_col = f'profit_{pred}_{num_bins}_{shift}'
    final_df[profit_col] = final_df[f'quantity_{pred}_{num_bins}_{shift}'].astype(float) * final_df['y'] * 10000
    #print(f'for pred {pred} we have profit = {final_df["profit"].sum()}')
    final_df.set_index('date')[profit_col].fillna(0).cumsum().plot(title=pred,figsize=(10,10),
                                                                 style=['-'] if i <=7 else ['--'])
    calmar, max_dd = calc_calmar(final_df[profit_col],use_annual=False)
    sharpe = calc_sharpe(final_df[profit_col])
    print(f'Num_bins={num_bins} | Pred = {pred} ({shift}) | Profit = {final_df[profit_col].sum()//1000}K Calmar, Max_DD = ({round(calmar,1)},{round(max_dd)}) | Sharpe = {round(sharpe,2)}')
    return final_df
#%%
# final_df[['date','quantity_Value_11-15_GEFS_vs_ma10_7_0']].loc[(final_df['date'].dt.hour%6 == 0)&(final_df['date'].dt.minute==0)].set_index('date')
#%%
weekdays = [0,1,2,3,4]


num_bins = 9
for num_bins in [7]: #[3,5,7,9,11]:
    i = 0
    for shift in [0]:
        final_df = stacked_df.shift(shift).merge(candles_df[['date','y']],on=['date'],how='outer')
        final_df = final_df.sort_values('date').fillna(method='ffill').dropna()
        final_df = final_df[final_df['date'].dt.weekday.isin(weekdays)]
        preds = [x for x in list(final_df) if 'vs_' in x and 'profit' not in x and 'quantity' not in x]
        for pred in preds:
            i += 1
            final_df = _analyze_performance(final_df,num_bins,pred, shift)
            # final_df['quantity'] = pd.qcut(final_df[pred],7,labels=[-3,-2,-1,0,1,2,3])

    plt.legend(preds)
    plt.show()
#%%
#candles_df['date'].drop_duplicates()
#%%
#speific_pred_profits['weekday'].value_counts()
# final_df[final_df['date'].dt.weekday==6][['date','y']]
final_df[['date','quantity_Value_11-15_GEFS_vs_ma10_7_0']].loc[(final_df['date'].dt.hour%6 == 0)&(final_df['date'].dt.minute==0)].set_index('date')['quantity_Value_11-15_GEFS_vs_ma10_7_0'].astype(float)[-50:].plot(figsize=(10,10),kind='bar')

# raise
#%%
final_df[['date','quantity_Value_11-15_GEFS_vs_ma10_7_0']].drop_duplicates(subset=['date'])

#%%
raise
#%%
chosen_profit = 'profit_Value_11-15_GEFS_ma2_vs_ma8'
chosen_profit = 'profit_Value_11-15_GEFS_vs_ma10_7_0'
speific_pred_profits = final_df[['date',chosen_profit]]
speific_pred_profits['week'] = speific_pred_profits['date'] - speific_pred_profits['date'].apply(lambda x: td(days=x.weekday()))
speific_pred_profits['day'] = speific_pred_profits['date'].dt.date
speific_pred_profits['weekday'] = speific_pred_profits['date'].dt.weekday
speific_pred_profits['hour'] = speific_pred_profits['date'].dt.hour

pivot = pd.pivot_table(speific_pred_profits,values=[chosen_profit],index=['week'],columns=['weekday']).fillna(0)
pivot.reset_index().groupby(pivot.reset_index()['week'].dt.date).sum().cumsum().plot(figsize=(8,8))
plt.show()

pivot_hour = pd.pivot_table(speific_pred_profits,values=[chosen_profit],index=['day'],columns=['hour'],
                            aggfunc=np.sum).fillna(0)
pivot_hour[[x for x in list(pivot_hour) if x[1] in [0,3,5,8,15,23]]].cumsum().plot(figsize=(8,8),style=['-']*7+['--']*7+['-*']*7)
plt.show()
#pivot_hour.sum()


# todo
# We can insert here a filter by recent performance!
# Alternatively we can use it as quantity decreaser for the official positions. get out when contradictions
#pivot.rolling(4,4).mean().cumsum().plot(figsize=(10,10))

#%%
daily_filtered_hours = pivot_hour[[x for x in list(pivot_hour) if x[1] not in [0,3,5,8,15,23]]].sum(axis=1)

daily_filtered_hours = daily_filtered_hours.reset_index()
daily_filtered_hours.loc[pd.to_datetime(daily_filtered_hours['day']).dt.month.isin([5,6,7]),0] = 0
daily_filtered_hours = daily_filtered_hours.set_index('day')
daily_filtered_hours.cumsum().plot(figsize=(12,12),style=['-']*7+['--']*7+['-*']*7)
print(f'Calmar for filtered hours, daily profits: {calc_calmar(daily_filtered_hours[0],use_annual=False)}')
plt.show()

#%% md
# PARA
#%%
#para_vals_full
#%%

def check_rolling_positions_strategy(a0, name,cols,num_bins=7,shift=0,
                         weekdays = [0,1,2,3,4],months=range(1,13),latency = 6):
    df_para = a0[['date']+cols]
    stacked_df_para = df_para.set_index('date').stack().reset_index(name=name).rename(columns={'level_1':'X'})
    stacked_df_para['hour'] = stacked_df_para['X'].apply(lambda x: int(x.split('Value_')[1].split('Z')[0]))
    #stacked_df_para['original_date'] = stacked_df_para['date'] - td(hours=8) + stacked_df_para['hour'].apply(lambda x: td(hours=x))
    stacked_df_para['original_date'] = stacked_df_para['date'] - td(hours=8) + stacked_df_para['hour'].apply(lambda x: td(hours=x if x in [0,6] else x-24))
    stacked_df_para = stacked_df_para.sort_values('original_date')
    bb = 0
    stacked_df_para['date'] = stacked_df_para['original_date'] + td(hours=latency)
    stacked_df_para[f'{name}_ma2'] = stacked_df_para[f'{name}'].rolling(2,2).mean()
    stacked_df_para[f'{name}_ma3'] = stacked_df_para[f'{name}'].rolling(3,2).mean()
    stacked_df_para[f'{name}_ma4'] = stacked_df_para[f'{name}'].rolling(4,4).mean()
    stacked_df_para[f'{name}_ma6'] = stacked_df_para[f'{name}'].rolling(6,4).mean()
    stacked_df_para[f'{name}_ma8'] = stacked_df_para[f'{name}'].rolling(8,4).mean()
    stacked_df_para[f'{name}_ma10'] = stacked_df_para[f'{name}'].rolling(10,4).mean()
    stacked_df_para[f'{name}_ma12'] = stacked_df_para[f'{name}'].rolling(12,4).mean()
    stacked_df_para[f'{name}_ma15'] = stacked_df_para[f'{name}'].rolling(15,4).mean()
    stacked_df_para[f'{name}_ma20'] = stacked_df_para[f'{name}'].rolling(20,4).mean()

    stacked_df_para[f'{name}_vs_ma3'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma3']
    stacked_df_para[f'{name}_vs_ma4'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma4']
    stacked_df_para[f'{name}_vs_ma6'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma6']
    stacked_df_para[f'{name}_vs_ma8'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma8']
    stacked_df_para[f'{name}_vs_ma10'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma10']
    stacked_df_para[f'{name}_vs_ma12'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma12']
    stacked_df_para[f'{name}_vs_ma15'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma15']
    stacked_df_para[f'{name}_vs_ma20'] = stacked_df_para[f'{name}'] - stacked_df_para[f'{name}_ma20']

    stacked_df_para[f'{name}_ma2_vs_ma4'] = stacked_df_para[f'{name}_ma2'] - stacked_df_para[f'{name}_ma4']
    stacked_df_para[f'{name}_ma2_vs_ma6'] = stacked_df_para[f'{name}_ma2'] - stacked_df_para[f'{name}_ma6']
    stacked_df_para[f'{name}_ma2_vs_ma8'] = stacked_df_para[f'{name}_ma2'] - stacked_df_para[f'{name}_ma8']
    stacked_df_para[f'{name}_ma2_vs_ma12'] = stacked_df_para[f'{name}_ma2'] - stacked_df_para[f'{name}_ma12']

    candles_df['date'] = candles_df['time_utc']
    candles_df['y'] = candles_df['open'].diff().shift(-1)

    i = 0
    final_df = stacked_df_para.set_index('date').shift(shift).reset_index().merge(candles_df[['date','y']],on=['date'],how='outer')
    final_df = final_df.sort_values('date').fillna(method='ffill').dropna()
    final_df = final_df[(final_df['date'].dt.weekday.isin(weekdays))&(final_df['date'].dt.month.isin(months))]
    preds = [x for x in list(final_df) if 'vs_' in x and 'profit' not in x and 'quantity' not in x]
    for pred in preds:
        i += 1
        final_df = _analyze_performance(final_df,num_bins,pred, shift)

    plt.legend(preds)
    plt.show()
    return final_df
#%%

name = 'Value_8-16_PARACO'
# name = 'Value_11-15_GEFS'
cols = paraco_vals
# cols = gefs_vals
# cols = para_vals
# print(gefs_vals)
final_df = check_rolling_positions_strategy(a0,name,cols,num_bins=7,shift=0,
                         weekdays = [0,1,2,3,4],
                                 months=[9,10,11,12,1,2,3,4])

paraco_winners = ['profit_Value_8-16_PARACO_ma2_vs_ma4_7_0','profit_Value_8-16_PARACO_vs_ma20_7_0']
para_winners = []
gefs_winners =['profit_Value_11-15_GEFS_vs_ma10_7_0','profit_Value_11-15_GEFS_vs_ma15_7_0']
winners = {str(paraco_vals):paraco_winners,
           str(gefs_vals): gefs_winners}[str(cols)]
final_df[['date']+[w.replace('profit','quantity') for w in winners]].loc[(final_df['date'].dt.hour%6 == 0)&(final_df['date'].dt.minute==0)].set_index('date')[[w.replace('profit','quantity') for w in winners]].astype(float)[-50:].plot(figsize=(10,10),kind='bar')

#%% md
# PARA
#%% md
# EPS
#%%
latency = 8
shift = 0
eps_current_vals = eps_vals_12to13
df_eps = a0[['date']+eps_current_vals]
stacked_df_eps = df_eps.set_index('date').stack().reset_index(name='Value_9-13_EPS').rename(columns={'level_1':'X'})
stacked_df_eps['hour'] = stacked_df_eps['X'].apply(lambda x: int(x.split('Value_')[1].split('Z')[0]))
stacked_df_eps['original_date'] = stacked_df_eps['date'] - td(hours=8) + stacked_df_eps['hour'].apply(lambda x: td(hours=x))

stacked_df_eps['date'] = stacked_df_eps['original_date'] + td(hours=latency)
stacked_df_eps['Value_9-13_EPS_ma2'] = stacked_df_eps['Value_9-13_EPS'].rolling(2,2).mean().shift(shift)
stacked_df_eps['Value_9-13_EPS_ma3'] = stacked_df_eps['Value_9-13_EPS'].rolling(3,2).mean().shift(shift)
stacked_df_eps['Value_9-13_EPS_ma4'] = stacked_df_eps['Value_9-13_EPS'].rolling(4,2).mean().shift(shift)
stacked_df_eps['Value_9-13_EPS_ma5'] = stacked_df_eps['Value_9-13_EPS'].rolling(5,2).mean().shift(shift)
stacked_df_eps['Value_9-13_EPS_ma6'] = stacked_df_eps['Value_9-13_EPS'].rolling(6,2).mean().shift(shift)
stacked_df_eps['Value_9-13_EPS_ma8'] = stacked_df_eps['Value_9-13_EPS'].rolling(8,4).mean().shift(shift)


stacked_df_eps['Value_9-13_EPS_vs_ma2'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma2']
stacked_df_eps['Value_9-13_EPS_vs_ma3'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma3']
stacked_df_eps['Value_9-13_EPS_vs_ma4'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma4']
stacked_df_eps['Value_9-13_EPS_vs_ma5'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma5']
stacked_df_eps['Value_9-13_EPS_vs_ma6'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma6']
stacked_df_eps['Value_9-13_EPS_vs_ma8'] = stacked_df_eps['Value_9-13_EPS'] - stacked_df_eps['Value_9-13_EPS_ma8']

candles_df['date'] = candles_df['time_utc']
candles_df['y'] = candles_df['open'].diff().shift(-1)
final_df_eps = stacked_df_eps.merge(candles_df[['date','y']],on=['date'],how='outer')

#%%
final_df_eps = final_df_eps.sort_values('date').fillna(method='ffill').dropna()
#%%
weekdays = [0,1,2,3,4]
final_df_eps = final_df_eps[final_df_eps['date'].dt.weekday.isin(weekdays)]
preds = [x for x in list(final_df_eps) if 'vs_' in x and 'profit' not in x]
plot = True

for num_bins in [3,5,7,9,11]:
    for pred in preds:
        # final_df_eps['quantity'] = pd.qcut(final_df_eps[pred],7,labels=[-3,-2,-1,0,1,2,3])
        part1 = [-2/2**c for c in range((num_bins-1)//2)]
        part2 = [2/2**c for c in range((num_bins-1)//2)]
        part2.reverse()
        # print(part1+[0]+part2)
        final_df_eps['quantity'] = pd.qcut(final_df_eps[pred],num_bins,labels=part1+[0]+part2)
        profit_col = f'profit_{pred}'
        final_df_eps[profit_col] = final_df_eps['quantity'].astype(float) * final_df_eps['y'] * 10000
        #print(f'for pred {pred} we have profit = {final_df_eps["profit"].sum()}')
        if plot:
            final_df_eps.set_index('date')[profit_col].fillna(0).cumsum().plot(title=pred)
        calmar, max_dd = calc_calmar(final_df_eps[profit_col],use_annual=False)
        sharpe = calc_sharpe(final_df_eps[profit_col])
        print(f'Num_bins={num_bins} | Pred = {pred} | Profit = {final_df_eps[profit_col].sum()//1000}K Calmar, Max_DD = ({round(calmar,1)},{round(max_dd)}) | Sharpe = {round(sharpe,2)}')
    print ('='*15)
    if plot:
        plt.legend(preds)
        plt.show()
#%%
import pandas as pd
main_csv = r"C:\Users\<USER>\Documents\Work\Amazon\XYs\Enriched_XYs\XY_a_GDD_v8_0Zb.csv"
a0 = pd.read_csv(main_csv,parse_dates=['date'])
a0.set_index('date')[[x for x in list(a0) if 'y_1200-' in x]]
# front_month_live = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\NG_2018-19_frontMonth_tz_Live.csv"
# a0
# df_y = pd.read_csv(front_month_live,parse_dates=['time_utc'])

#%%
from datetime import datetime as dtdt
df_y[df_y['time_utc'].dt.date == dtdt(2022,11,9).date()]