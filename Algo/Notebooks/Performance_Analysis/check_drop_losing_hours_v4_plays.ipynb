#%%

#%%
import matplotlib.pyplot as plt
import pandas as pd

from Algo.Performance_Analysis.clusters_auto_selection import wrap_deep_drop_losing_hours, _get_hours_lst_by_resolution, check_performance_by_hour

from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,get_drawdown_vector

#%%
def get_current_week_of_month():
    current_week_of_month = pd.Series(dtdt.now()+td(hours=8)-td(days=(dtdt.now()-td(hours=12)).weekday())).dt.day.apply(lambda x: int((x-1)/7)+1).clip(0,5).iloc[0]
    return current_week_of_month

#%%
from Algo.Utils.files_handle import get_candles_outpath

candles_csv = get_candles_outpath('NG',True)
print(candles_csv)
candles_df = pd.read_csv(candles_csv,parse_dates=['date','time_utc'])

candles_df['t_open'] = candles_df['time_utc']

# leave the first row in each day 
candles_df = candles_df[['t_open','open']].groupby(candles_df['t_open'].dt.date).first()[['open']].reset_index()
candles_df['date'] = candles_df['t_open'] # pd.to_datetime(candles_df['t_open']).dt.da
candles_df = candles_df[['date','open']]


#candles_df.groupby(pd.to_datetime(candles_df['date']).dt.to_period('M')).size()

#%%
from Algo.Performance_Analysis.clusters_auto_selection import (handle_config_v2, handle_config_v3, handle_config_v4, DAYS_FOR_CLUSTER_PERFORMANCE, V5_DICT_FOR_DROP_LOSING_HOURS,\
    DICT_FOR_DROP_LOSING_HOURS_W3,DICT_FOR_DROP_LOSING_HOURS_W1,DICT_FOR_DROP_LOSING_HOURS_W4,DICT_FOR_DROP_LOSING_HOURS_T350, DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350,DEFAULT_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_W4_T350,NO_ACTION_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER,V5b_DICT_FOR_DROP_LOSING_HOURS)

#%% md

#%%
#final_profit_v4['total_profit'].describe()
#%%
from matplotlib import pyplot as plt
from datetime import timedelta as td
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis
import numpy as np
from datetime import datetime as dtdt

EXTENDED_PLOTS = False
BASIC_PLOT = True

NORMALIZE_TO_PCT = True

tomorrows_weekday = (dtdt.now()+td(hours=10)).weekday()
weekday = 4
# weekday = tomorrows_weekday

weekdays = [0,1,2,3,4]
# weekdays = [weekday]
print(weekdays)

# hours = list(range(0,8))+list(range(9,13))+[15,16,17,18,19]
hours = list(range(6,9))+list(range(9,13))+[14,15,16,17,18,19]
# hours = list(range(0,24))#+list(range(9,13))+[14,15,16,17,18,19]


mode = 'ens_calmar0.25_S0.5_window4'
# mode = 'ens_calmar3_S0.5_window4'

lst = []

resolution = 'weekly'
if resolution == 'daily':
    groupers = ['date','week','weekday']
elif resolution == 'weekly':
    groupers = ['date','week']
    
start_ind, start_end = 0,1

def get_style(i):
    if i <7:
        return '-'
    elif i < 14:
        return '--'
    else:
        return '-*'

i = 0

stack = {}
kpis_stack = []

for conf,conf_name in zip([V5_DICT_FOR_DROP_LOSING_HOURS,\
                        DICT_FOR_DROP_LOSING_HOURS_W3,DEFAULT_DICT_FOR_DROP_LOSING_HOURS,
                           DICT_FOR_DROP_LOSING_HOURS_W1,
                           DICT_FOR_DROP_LOSING_HOURS_T350,
                           DICT_FOR_DROP_LOSING_HOURS_W4_T350,
                           NO_ACTION_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER,
                            DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350,
                           ][start_ind:start_end], \
                       'V5_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_W3, DEFAULT_DICT_FOR_DROP_LOSING_HOURS, DICT_FOR_DROP_LOSING_HOURS_W1,DICT_FOR_DROP_LOSING_HOURS_T350,DICT_FOR_DROP_LOSING_HOURS_W4_T350,NO_ACTION_DICT_FOR_DROP_LOSING_HOURS, DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER,DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350'.split(',')[start_ind:start_end]
                          ):
    
    for hours_subset_override in [None]:
        weekdays_split = [[0, 1, 2, 3, 4]]
        if hours_subset_override is not None:
            weekdays_split = [[x] for x in [0, 1, 2, 3, 4]] # todo hack 
        for window in [3,5,7,9,12][-2:-1]:
            for bigloss_value in [#250,400,500,600,700,800,900,1000,1100,1200,1300,1400,
                                  400,800,
                                    1200,
                                  1500,#1600,1700,
                                  1800,#2000
                                  ][-1:]:
                for bigloss1_value in [200,400,600][1:2]:
                    for bigloss2_value in [600,800,1000][1:2]:
                        print(f'='*10+conf_name+f' BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}'+'='*10)
                        final_profit_v4_w9, last_week_dict_v4_w9 = handle_config_v4(
                            mode, (weekdays_split, 'all'),
                                                                                window, 0, 2, days_back=0, clip_after=False,
                                                                                initial_clip=1.25,
                                                                                # drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER
                                                                                # drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_W1
                                                                                drop_losing_hours_config=conf,
                                                                                 hours_subset_override=hours_subset_override,
                                                                                 block_14_utc=False,
                                                                                # drop_losing_hours_config=NO_ACTION_DICT_FOR_DROP_LOSING_HOURS
                                                                                big_loss_value=bigloss_value,
                                                                                    bigloss1_value=bigloss1_value,bigloss2_value=bigloss2_value
                                                                                )
                        if NORMALIZE_TO_PCT:
                            final_profit_v4_w9['date'] = final_profit_v4_w9['t_open'].dt.date
                            final_profit_v4_w9 = final_profit_v4_w9.merge(candles_df,on=['date'],how='left')
                            final_profit_v4_w9['open'] = final_profit_v4_w9['open'].ffill().bfill()
                            #print(final_profit_v4_w9[['total_profit','open']].iloc[0])
                            final_profit_v4_w9['total_profit'] = final_profit_v4_w9['total_profit']/(final_profit_v4_w9['open']*100)
                        #raise
                        final_profit_v4_w9['weekday'] = final_profit_v4_w9['t_open'].dt.weekday
                        final_profit_v4_w9['hour'] = final_profit_v4_w9['t_open'].dt.hour
                        final_profit_v4_w9['date'] = final_profit_v4_w9['t_open'].dt.date
                        final_profit_v4_w9['week'] = pd.to_datetime(final_profit_v4_w9['date']) - final_profit_v4_w9['weekday'].apply(lambda x: td(days=x))
    
                        daily_profits = final_profit_v4_w9[(final_profit_v4_w9['weekday'].isin(weekdays))&(final_profit_v4_w9['hour'].isin(hours))].groupby(['date','week','weekday']).sum().reset_index()
                        
                        pivot = pd.pivot_table(daily_profits,index=['week'],columns=['weekday'],
                                               values=['total_profit']).fillna(0)
                        hour_pivot = pd.pivot_table(final_profit_v4_w9[(final_profit_v4_w9['weekday'].isin(weekdays))&(final_profit_v4_w9['hour'].isin(hours))],index=['date'],columns=['hour'],
                                               values=['total_profit'],aggfunc=np.sum).fillna(0)
                        
                        if resolution == 'weekly':
                            daily_profits['date'] = daily_profits['week']
                            daily_profits = daily_profits.groupby('date').sum().reset_index()
                        print('------------ Final KPIS -------')
                        full_conf_name = conf_name+f'window={window}_BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}'
                        kpis = print_kpis(daily_profits.set_index('date')['total_profit'][-52*(5 if resolution != 'weekly' else 1):],full_conf_name)
                        kpis_4M = print_kpis(daily_profits.set_index('date')['total_profit'][-20*(5 if resolution != 'weekly' else 1):],full_conf_name)
                        print('-------------------')
                        
                        stack[full_conf_name] = daily_profits.set_index('date')['total_profit']
                        kpis['conf_name'] = conf_name
                        kpis['window'] = window
                        kpis['bigloss'] = bigloss_value
                        kpis['bigloss1'] = bigloss1_value
                        kpis['bigloss2'] = bigloss2_value
                        kpis['calmar_20w'] = kpis_4M['calmar']
                        kpis['sharpe_20w'] = kpis_4M['sharpe']
                        
                        kpis_stack.append(kpis)
                        if BASIC_PLOT:
                            daily_profits.set_index('date')['total_profit'].fillna(0).cumsum().plot(figsize=(10,10),title=conf_name,style=[get_style(i)])
                            i += 1
                        lst.append(f' BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}')
                        if EXTENDED_PLOTS:
                            plt.show()
                            pivot.cumsum().plot(figsize=(16,16),title=conf_name)
                            plt.show()
                            hour_pivot.cumsum().plot(figsize=(16,16),title=conf_name,style=['-']*7+['--']*7+['-*']*7)
                            plt.show()
    plt.legend(lst)
    plt.show()
#%% md
------------ Final KPIS ------- only 13
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 26.11 | calmar, max_dd = (1.25, -20.89) | sharpe = 2.17 | {0} Losses 1000$ 
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 34.77 | calmar, max_dd = (4.26, -8.17) | sharpe = 7.35 | {0} Losses 1000$ 


------------ Final KPIS ------- 13+15 - barely changes
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 25.35 | calmar, max_dd = (1.19, -21.33) | sharpe = 2.04 | {0} Losses 1000$ 
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 36.05 | calmar, max_dd = (4.41, -8.17) | sharpe = 7.42 | {0} Losses 1000$ 

------------ Final KPIS ------- 13 + 8
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 24.62 | calmar, max_dd = (1.59, -15.43) | sharpe = 2.17 | {0} Losses 1000$ 
profit for window V5_DICT_FOR_DROP_LOSING_HOURSwindow=9_BigLoss=(1800, 400, 800) = 26.74 | calmar, max_dd = (3.31, -8.08) | sharpe = 6.38 | {0} Losses 1000$ 




#%% md

#%%
raise
#%%
kpis_df = pd.DataFrame(kpis_stack)
kpis_df.sort_values('pnl')
#%%
raise
#final_profit_v4_w9#[final_profit_v4_w9['date'].isin(candles_df['date'].tolist())]
#%% md
With 14
profit 108451.25 | calmar = 10.65, MaxDD = -10185.0, sharpe = 2.5843462549046547
profit 90D: 7643.75 | calmar 90D = 1.89, MaxDD 90D = -4052.5, sharpe 90D= 2.1655591100670484
profit 116773.75 | calmar = 9.77, MaxDD = -11952.5, sharpe = 2.7055149644316496
profit 90D: 5452.5 | calmar 90D = 1.15, MaxDD 90D = -4737.5, sharpe 90D= 1.4781210068602215
#%%
from matplotlib import pyplot as plt
from datetime import timedelta as td
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d
import numpy as np
from datetime import datetime as dtdt

EXTENDED_PLOTS = True # todo
NORMALIZE_TO_PCT = False
# NORMALIZE_TO_PCT = True
WEEK_OF_MONTH_PIVOT_INDEX = 'week'

FIRST_INDEX_TO_PLOT = 0
# FIRST_INDEX_TO_PLOT = 240


tomorrows_weekday = (dtdt.now()+td(days=1,hours=-6)).weekday()
weekday = 3 #tomorrows_weekday

weekdays = [0,1,2,3,4]
# weekdays = [2]
# hours = list(range(0,8))+list(range(9,13))+[16,17]
# hours = list(range(6,13))+[14,15,16,17,18,19]
# hours = list(range(6,20))
# hours += [13,15] # todo 
# hours = [8]
# weekdays = [weekday]
print(weekdays)

resolution = 'weekly'
columns_for_pivot = ['week_of_month_v2','week_part']

# hours = list(range(5,13))+[16,17]
# hours = list(range(0,20))#+[16,17]
lst = []
stack = {}

confs_lst = [V5_DICT_FOR_DROP_LOSING_HOURS,V5b_DICT_FOR_DROP_LOSING_HOURS,\
                        DICT_FOR_DROP_LOSING_HOURS_W3,DEFAULT_DICT_FOR_DROP_LOSING_HOURS,
                           DICT_FOR_DROP_LOSING_HOURS_W1,
                           DICT_FOR_DROP_LOSING_HOURS_T350, DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350,
                           DICT_FOR_DROP_LOSING_HOURS_W4_T350,
                           NO_ACTION_DICT_FOR_DROP_LOSING_HOURS,
                           DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER
                           ]

confs_names_lst = 'V5_DICT_FOR_DROP_LOSING_HOURS,V5b_DICT_FOR_DROP_LOSING_HOURS, DICT_FOR_DROP_LOSING_HOURS_W3, DEFAULT_DICT_FOR_DROP_LOSING_HOURS, DICT_FOR_DROP_LOSING_HOURS_W1,DICT_FOR_DROP_LOSING_HOURS_T350, DICT_FOR_DROP_LOSING_HOURS_HYBRID_SUBSETS_T350,DICT_FOR_DROP_LOSING_HOURS_W4_T350,NO_ACTION_DICT_FOR_DROP_LOSING_HOURS, DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER'.split(',')

filtered_confs, filtered_confs_names = confs_lst[2:4]+confs_lst[-2:-1], confs_names_lst[2:4]+confs_names_lst[-2:-1]


filtered_confs, filtered_confs_names = filtered_confs[2:3], filtered_confs_names[2:3]

i = 0
for conf,conf_name in zip(filtered_confs,filtered_confs_names
                       ):
    
    for window in [# 3,5,7,
                   9,
                   11][:1]:
        for bigloss_value in [#250,400,500,600,700,800,900,1000,1100,1200,1300,1400,
                              #400,
                              800,
                              1200,
                              1500,1800,2000
                              #1600,1800,2000
                              ][-2:-1]:  #[-2:-1]: #[-1:]:
                              # ][-2:-1]:  #[-2:-1]: #[-1:]:
            for bigloss1_value in [200,400,600][1:2]: #[1:2]
                for bigloss2_value in [600,800,900,1000][1:2]: #[1:2]:
                    i += 1
                    print(f'='*10+conf_name+f' BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}'+'='*10)
                    final_profit_v4_w9, last_week_dict_v4_w9 = handle_config_v4(mode, ([[0, 1, 2, 3, 4]], 'all'),
                                                                            window, 0, 2, days_back=0, clip_after=False,
                                                                            initial_clip=1.25,
                                                                            # drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_MULTI_LAYER
                                                                            # drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_W1
                                                                            drop_losing_hours_config=conf,
                                                                            # drop_losing_hours_config=NO_ACTION_DICT_FOR_DROP_LOSING_HOURS
                                                                            big_loss_value=bigloss_value,
                                                                                block_14_utc=False,
                                                                                bigloss1_value=bigloss1_value,bigloss2_value=bigloss2_value
                                                                            )

                    if NORMALIZE_TO_PCT:
                        final_profit_v4_w9['date'] = final_profit_v4_w9['t_open'].dt.date
                        final_profit_v4_w9 = final_profit_v4_w9.merge(candles_df,on=['date'],how='left')
                        final_profit_v4_w9['open'] = final_profit_v4_w9['open'].ffill().bfill()
                        #print(final_profit_v4_w9[['total_profit','open']].iloc[0])
                        final_profit_v4_w9['total_profit'] = final_profit_v4_w9['total_profit']/(final_profit_v4_w9['open']*100) # 10000 / 100 to get percentage
                    
                    
                    #print(final_profit_v4_w9['week_of_month'].value_counts())
                    final_profit_v4_w9['weekday'] = final_profit_v4_w9['t_open'].dt.weekday
                    final_profit_v4_w9['hour'] = final_profit_v4_w9['t_open'].dt.hour
                    final_profit_v4_w9['date'] = final_profit_v4_w9['t_open'].dt.date
                    final_profit_v4_w9['week'] = pd.to_datetime(final_profit_v4_w9['date']) - final_profit_v4_w9['weekday'].apply(lambda x: td(days=x))
                    
                    # add columns specifying which week of the month is it
                    final_profit_v4_w9['week_of_month'] = final_profit_v4_w9['t_open'].dt.day.apply(lambda x: int((x-1)/7)+1).clip(0,5)
                    final_profit_v4_w9['week_of_month_v2'] = final_profit_v4_w9['week'].dt.day.apply(lambda x: int((x-1)/7)+1).clip(0,5)
                    final_profit_v4_w9['week_part'] = final_profit_v4_w9['weekday'].apply(lambda x: 0 if x<3 else 1)
                    
                    daily_profits = final_profit_v4_w9[(final_profit_v4_w9['weekday'].isin(weekdays))&(final_profit_v4_w9['hour'].isin(hours))].groupby(['date','week','weekday']).agg({'total_profit':'sum','week_of_month':'first','week_of_month_v2':'first',
                     'week_part':'first'}).reset_index()
                    pivot = pd.pivot_table(daily_profits,index=['week'],columns=['weekday'],
                                           values=['total_profit']).fillna(0)
                    if resolution == 'weekly':
                        
                        week_of_month_pivot = pd.pivot_table(daily_profits,index=[WEEK_OF_MONTH_PIVOT_INDEX],
                                                             columns=columns_for_pivot,
                                                             # columns=['week_of_month_v2','weekday'],
                                           values=['total_profit'],aggfunc=np.sum).fillna(0)
                        daily_profits['date'] = daily_profits['week']
                        daily_profits = daily_profits.groupby('date').sum().reset_index()
                        
                        if i ==1:
                            #week_of_month_pivot.rolling(1,1).mean().cumsum().plot(figsize=(16,16),title=conf_name,style=['-']*5+['--']*5+['-*']*5)
                            # required_weekday = (dtdt.now()+td(hours=8)).weekday()
                            required_weekday = weekday
                            
                            current_week_of_month = get_current_week_of_month()
                            current_week_part = 0 if required_weekday<3 else 1
                            col_to_emphasize = ('total_profit', current_week_of_month, current_week_part)
                            print(list(week_of_month_pivot)[0])
                            current_conf_index = list(week_of_month_pivot).index(col_to_emphasize)
                            styles = ['-']*6+['--']*6
                            styles = styles[:current_conf_index]+['-*']+styles[current_conf_index+1:]
                            widths = [1.5]*12
                            widths = {k:v for k,v in zip(list(week_of_month_pivot),widths[:current_conf_index]+[4.5]+widths[current_conf_index+1:])}
                            # assign the right width to each column while plotting 
                            ax = week_of_month_pivot.cumsum().plot(figsize=(16,16),title=conf_name,style=styles)
                            # Access the lines representing each column
                            lines = ax.lines
                            
                            # Iterate through the lines and set custom widths
                            for col, width in widths.items():
                                line = lines[week_of_month_pivot.columns.get_loc(col)]
                                line.set_linewidth(width)
                            
                            manual_current_week_of_month = int(((dtdt.now()+td(hours=12)).day-1)/7)+1
                            # add text saying "current conf is (current_week_of_month, current_week_part)"
                            plt.text(0.5, 0.5, f'Current conf is {(manual_current_week_of_month,current_week_part)}', horizontalalignment='center',
                                 verticalalignment='center', transform=ax.transAxes, fontsize=20)
                            
                            plt.show()
                            if EXTENDED_PLOTS:
                                plt.show()
                                pivot.cumsum().plot(figsize=(16,16),title=conf_name)
                                plt.show()
                                final_profit_v4_w9['30M'] = final_profit_v4_w9['t_open'].dt.strftime('%H') + final_profit_v4_w9['t_open'].dt.strftime('%M').str.replace('15','00').str.replace('45','30')
                                final_profit_v4_w9['2H'] = final_profit_v4_w9['hour'] // 2 * 2
                                for grouper in ['hour','2H','30M']:
                                    hour_pivot = pd.pivot_table(final_profit_v4_w9[(final_profit_v4_w9['weekday'].isin([required_weekday]))&(final_profit_v4_w9['hour'].isin(hours))],index=['date'],columns=[grouper],
                                               values=['total_profit'],aggfunc=np.sum).fillna(0)
                                    hour_pivot[list(hour_pivot)[:15]].cumsum().plot(figsize=(16,16),style=['-']*7+['--']*7+['-*']*7,
                                                             title=f'Hourly ({grouper}) Profits for {conf_name} x Weekday = {required_weekday}')
                                    if len(list(hour_pivot))>=15:
                                        hour_pivot[list(hour_pivot)[15:]].cumsum().plot(figsize=(16,16),style=['-']*7+['--']*7+['-*']*7,
                                                                 title=f'Hourly ({grouper}) Profits for {conf_name} x Weekday = {required_weekday}')
                                    if grouper == 'hour':
                                        print(conf_name)
                                        print(f'last day = {hour_pivot.index[-1]}')
                                        print('PROFIT = ')
                                        print(hour_pivot.iloc[-1])
                                        print('total profit',hour_pivot.iloc[-1].sum())
                                        # raise
                                    plt.show()    
                    df_to_plot = daily_profits.set_index('date')['total_profit'].fillna(0)[FIRST_INDEX_TO_PLOT:]
                    
                    print('-----')
                    print_kpis_with_90d(df_to_plot)
                    stack[f'{conf_name}_BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}'] = df_to_plot
                    df_to_plot = df_to_plot.cumsum()
                    df_to_plot.plot(figsize=(10,10),title=conf_name,style=['-'] if len(lst)<7 else ['--'])

                    lst.append(f'{conf_name} X BigLoss={(bigloss_value,bigloss1_value,bigloss2_value)}')
                    
plt.legend(lst)
#plt.style(['-'])
plt.show()

print(final_profit_v4_w9)
# print(final_profit_v4_w9['week_of_month_v2'].drop_duplicates()[-4:])
#%%
final_profit_v4_w9[final_profit_v4_w9['t_open'] >=pd.to_datetime('2023-12-14')]
#%%
# run the call for calculating final_profit_v4 in debug mode 
# follow when do we block 09:00 for Wednesday 13/12. and check if somehow it's not passed to the last_week_quantity_dict
#print(filtered_confs_names[-1])
#%%
final_profit_v4_w9[final_profit_v4_w9['date']>= pd.to_datetime((dtdt.today()-td(hours=5)).date())].set_index('t_open')[['total_quantity','total_profit']]
#%%

#%%
#final_profit_v4_w9[['t_open']+columns_for_pivot].iloc[-1]['week_of_month_v2']
#int(((dtdt.now()+td(hours=12)).day-1)/7)+1
pd.pivot_table(daily_profits,index=['week'],columns=columns_for_pivot,
                                                             # columns=['week_of_month_v2','weekday'],
                                           values=['total_profit'],aggfunc=np.sum).fillna(0)
#%% md
### Scanning of different big loss values yielded that for the window big loss we need much higher than 800
### OLD Winners:
1. W3 x bigloss=1500, bigloss1=200, bigloss2=1000
2. DEFAULT_DICT_FOR_DROP_LOSING_HOURS (W5) x 1500 x 200 x 600
### NEW (Aug 23) Winners:
1. W3 x bigloss=1800, bigloss1=400, bigloss2=800
2. DEFAULT_DICT_FOR_DROP_LOSING_HOURS (W5) x 1500 x 200 x 600
#%%
print(final_profit_v4_w9.set_index('t_open')[['week_of_month_v2','week_part','weekday']])
#%%
# todo - IMPORTANT!!!!
# add filter based on week_of_month_v2 x week_part
# we seem to have super nice signal there!
# something like - if last 2 weeks sum is negative (<-3/5/7%?), don't trade this week
#%%
# final_profit_v4_w9[final_profit_v4_w9['t_open'].dt.hour == 14]
#%%
COST_PER_TRADE = 7.5 
from Algo.Utils.kpis_calculation import calc_calmar, calc_sharpe

final_profit_v4_w9_to_plot = final_profit_v4_w9.copy()
final_profit_v4_w9_to_plot['total_profit_shift_0'] = final_profit_v4_w9_to_plot['total_quantity'].shift(0) * final_profit_v4_w9_to_plot['delta_y'] * 1e4 - \
    abs(final_profit_v4_w9_to_plot['total_quantity'].shift(0).diff()) * COST_PER_TRADE

final_profit_v4_w9_to_plot['total_profit_shift_1'] = final_profit_v4_w9_to_plot['total_quantity'].shift(1) * final_profit_v4_w9_to_plot['delta_y'] * 1e4 - \
    abs(final_profit_v4_w9_to_plot['total_quantity'].shift(1).diff()) * COST_PER_TRADE

final_profit_v4_w9_to_plot['total_profit_shift_2'] = final_profit_v4_w9_to_plot['total_quantity'].shift(2) * final_profit_v4_w9_to_plot['delta_y'] * 1e4 - \
    abs(final_profit_v4_w9_to_plot['total_quantity'].shift(2).diff()) * COST_PER_TRADE

final_profit_v4_w9_to_plot['total_profit_shift_-1'] = final_profit_v4_w9_to_plot['total_quantity'].shift(-1) * final_profit_v4_w9_to_plot['delta_y'] * 1e4 - \
    abs(final_profit_v4_w9_to_plot['total_quantity'].shift(-1).diff()) * COST_PER_TRADE

final_profit_v4_w9_to_plot['total_profit_shift_-2'] = final_profit_v4_w9_to_plot['total_quantity'].shift(-2) * final_profit_v4_w9_to_plot['delta_y'] * 1e4 - \
    abs(final_profit_v4_w9_to_plot['total_quantity'].shift(-2).diff()) * COST_PER_TRADE

#%%

profit_cols = [x for x in list(final_profit_v4_w9_to_plot) if 'profit' in x]


hourly_grouped = final_profit_v4_w9_to_plot.groupby(final_profit_v4_w9_to_plot['t_open'].dt.hour).mean()
improvement_hours = hourly_grouped[hourly_grouped['total_profit_shift_0'] < hourly_grouped['total_profit_shift_1'] * 0.8].index.tolist()
improvement_hours = [x for x in improvement_hours if x > 6 or x < 17]
print(improvement_hours)
hourly_grouped[profit_cols].plot(kind='bar')

### filter to improvement/ non improvement hours 
# final_profit_v4_w9_to_plot.loc[final_profit_v4_w9_to_plot['hour'].isin(improvement_hours),profit_cols] = 0
# final_profit_v4_w9_to_plot.loc[~final_profit_v4_w9_to_plot['hour'].isin(range(9,14)),profit_cols] = 0

# calculate the diff of improvement between shift1 and original 
final_profit_v4_w9_to_plot['total_improvement_shift_1'] = final_profit_v4_w9_to_plot['total_profit_shift_1'] - final_profit_v4_w9_to_plot['total_profit_shift_0']


profit_cols = ['total_profit_shift_0','total_profit_shift_1']

final_profit_v4_w9_to_plot['hour'] = final_profit_v4_w9_to_plot['t_open'].dt.hour
final_profit_v4_w9_to_plot['date'] = final_profit_v4_w9_to_plot['t_open'].dt.date
final = final_profit_v4_w9_to_plot.groupby(['date']).sum()
final[profit_cols].cumsum().plot(figsize=(16,16))

for c in profit_cols:
    calculated_calmar = calc_calmar(final[c],False)
    calculated_sharpe = calc_sharpe(final[c])
    pnl = final[c].sum()
    print(f'{c} | profit {pnl} | calmar = {calculated_calmar}, MaxDD = {final[c].min()}, sharpe = {calculated_sharpe}')
# final_profit_v4_w9_to_plot.groupby(['date','hour']).sum()['total_improvement_shift_1'].cumsum().plot(figsize=(16,16))
#%%

#%%
import pandas as pd 

kpis_df = pd.read_csv(r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\Performance_Analysis\tmp\kpis_df_DICT_FOR_DROP_LOSING_HOURS_W3.csv")
kpis_df.groupby('window').mean().sort_values('calmar',ascending=False)
#%%

#%% md
the main candidate for improvmeent is the shift1 
need to try and find some dynamic strategy for this by hour OR hour x weekday to choose when to use it  # todo 