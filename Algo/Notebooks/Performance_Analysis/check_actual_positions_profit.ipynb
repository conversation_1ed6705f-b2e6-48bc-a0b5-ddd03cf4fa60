#%%
import pandas as pd 
from datetime import datetime as dtdt
from datetime import timedelta as td

from Algo.Utils.files_handle import ACTUAL_TRADES_CSV, get_candles_outpath
actual_positions_csv = ACTUAL_TRADES_CSV.replace('.csv', '_NG.csv')
actual_positions = pd.read_csv(actual_positions_csv,parse_dates=['date']) 
#%%
actual_positions = actual_positions.query('mode == "ens_cluster_dynamic_v4_S0.5_w9x3d_loose"')
# actual_positions = actual_positions.query('mode == "ens_calmar0.25_S0.5_window4"')
i = 0
t = (dtdt.now()-td(hours=5+24*i)).replace(hour=0, minute=0, second=0, microsecond=0)
print(t)
actual_positions = actual_positions[pd.to_datetime(actual_positions['date'].dt.date)==t]
actual_positions
# actual_positions.sort_values('date')
#%%
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT

daily_trades, positions_df = get_ensemble_positions(dtdt(dtdt.now().year, dtdt.now().month, dtdt.now().day, dtdt.now().hour),ENSEMBLES_DICT['ens_cluster_dynamic_v4_S0.5_w9x3d_loose'], takeback_hours=24*i-3, is_naive=True)
positions_df
#%%
positions_df['date'] = positions_df['t_open']
merged = positions_df.merge(actual_positions[['date','quantity_actual','quantity_needed']], on=['date'], how='left')
merged.set_index('t_open')[[x for x in list(merged) if 'quantity' in x]].plot(kind='bar',figsize=(20,10))
#%%
candles_csv = get_candles_outpath('NG',True)
candles_df = pd.read_csv(candles_csv,parse_dates=['time_utc'])
candles_df = candles_df.query('time_utc >= @t')
#%%
quantity_col = 'quantity_actual'
# quantity_col = 'quantity_clipped'

candles_df['date'] = candles_df['time_utc']
candles_df['y'] = candles_df['open'].diff().shift(-1) * 10000

final_df = actual_positions.merge(candles_df[['date','y','open']], on=['date'], how='left')

final_df['quantity_needed_clipped'] = final_df['quantity_needed'].clip(-2,2) 
# final_df['quantity_clipped'] = final_df['quantity'].clip(-2,2) 
final_df['profit'] = final_df[quantity_col] * final_df['y']
# final_df
#%%

grouped_df = final_df.groupby(final_df['date'].dt.hour).agg({'profit':'sum','quantity_needed':'mean','quantity_actual':'mean','y':'sum'})

from matplotlib import pyplot as plt

# 2 x 1 subplots figure 
fig, axes = plt.subplots(nrows=2, ncols=1, figsize=(8,8))

pd.DataFrame({'profit_cumsum':grouped_df['profit'].cumsum(),
              'profit':grouped_df['profit']}).plot(kind='bar',title=f'Cumulative Actual profit for today: {t.strftime("%Y-%m-%d")}',ax=axes[0])
grouped_df['y_in_100$_units'] = grouped_df['y'] / 100
grouped_df[['quantity_needed','quantity_actual','y_in_100$_units']].plot(kind='bar',title=f'Quantity needed for today: {t.strftime("%Y-%m-%d")}',ax=axes[1])

print(f"Today's profit = {grouped_df['profit'].sum()}")
print(grouped_df['profit'])
#final_df.sum()['profit']

#%%
final_df[final_df['date'].dt.hour.isin([13,14,15,16])]
#%%
# final_df[final_df['date'].dt.hour == 17]
i
#%%
from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v2, handle_config_v3, handle_config_v4, DAYS_FOR_CLUSTER_PERFORMANCE, V5_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_W3

days_back2 = i
days_back2 = 0


final_profit_v4_w9, last_week_dict_v4_w9 = handle_config_v4(
    'ens_calmar0.25_S0.5_window4',
    ([[0, 1, 2, 3, 4]], 'all'),
    9, 0, 2, days_back=days_back2, clip_after=False,
    initial_clip=1.25,drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_W3,
    big_loss_value=1800,bigloss1_value=400,bigloss2_value=800,block_14_utc=False)


final_profit_v4_w9[final_profit_v4_w9['t_open']>=pd.to_datetime((dtdt.now()-td(hours=5+24*i)).strftime('%Y-%m-%d'))]
# 
# final_profit_v4_w9_7d, last_week_dict_v4_w9_7d = handle_config_v4('ens_calmar0.25_S0.5_window4', ([[0, 1, 2, 3, 4]], 'all'),
#                                                             9, 0, 2, days_back=7+i, clip_after=False,
#                                                             initial_clip=1.25,drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_W3,
#                                                                   big_loss_value=1800,bigloss1_value=400,bigloss2_value=800,block_14_utc=False)


#%%
last_week_dict_v4_w9[1][(14, 15)]
#%% md

#%%
theoretical_df = final_profit_v4_w9[pd.to_datetime(final_profit_v4_w9['t_open'].dt.date)==t]
theoretical_df = theoretical_df.rename(columns={'total_profit':'total_profit_theoretical',
                                                'total_quantity':'total_quantity_theoretical',
                                                'total_quantity_old': 'total_quantity_old_theoretical',})
theoretical_df['date'] = theoretical_df['t_open']
theoretical_df[(theoretical_df['t_open'].dt.hour.isin([5,6,7,8,9,10,11,12,14,16,17,18,19]))].groupby(theoretical_df['t_open'].dt.hour).sum().sum()
#%%
theoretical_df
#%%
theoretical_df[(theoretical_df['t_open'].dt.hour.isin([5,6,7,8,9,10,11,12,14,16,17,18,19]))].groupby(theoretical_df['t_open'].dt.hour).agg({'total_profit_theoretical':'sum','total_quantity_theoretical':'mean','total_quantity_old_theoretical':'mean'})
#%%
final_df2 = final_df.merge(theoretical_df[['date','total_profit_theoretical','total_quantity_theoretical','total_quantity_old_theoretical']], on=['date'], how='left')
final_df2.set_index('date')[['total_quantity_theoretical','quantity_actual']].plot(kind='bar',title=f'Quantity needed for today: {t.strftime("%Y-%m-%d")}',figsize=(20,10))

#%%

#%%
import json 

def extract_given_days_allowed_clusters(monday_str=None,mode="ens_cluster_dynamic_v4_S0.5_w9x3d_loose",
                                        weekday=None,
                                        hour=None):
    if monday_str is None:
        monday_str = (dtdt.now()-td(days=dtdt.now().weekday()+1)).strftime('%Y%m%d')
    print(monday_str)
    clusters_conf_json_path = r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Conf\dynamic_chosen_clusters.json"
    clusters_dict = json.load(open(clusters_conf_json_path))
    clusters_dict = clusters_dict[monday_str]
    final_dict = clusters_dict[mode]
    if weekday is not None:
        final_dict = final_dict[weekday]
        if hour is not None:
            if hour in final_dict.keys():
                final_dict = final_dict[hour]
            else:
                print('hour not in final_dict.keys()',hour,final_dict.keys())
    return final_dict

import json 

def drop_given_week_from_cluster_conf(monday_str=None,mode=None,
                                        ):
    if monday_str is None:
        monday_str = (dtdt.now()-td(days=dtdt.now().weekday()+1)).strftime('%Y%m%d')
    clusters_conf_json_path = r"C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Conf\dynamic_chosen_clusters.json"
    clusters_dict = json.load(open(clusters_conf_json_path))
    clusters_dict = {k:v for k,v in clusters_dict.items() if k!=monday_str}
    print(f'dropping {monday_str} from clusters_conf_dict: is_monday_str_in_dict = ', monday_str in clusters_dict.keys())
    json.dump(clusters_dict,open(clusters_conf_json_path,'w'))

final_dict = extract_given_days_allowed_clusters(monday_str='20240107',
                                                 hour='(14, 15)',weekday="1",
                                                 mode="ens_cluster_dynamic_v4_S0.5_w9x3d_loose")
# 
final_dict
# drop_given_week_from_cluster_conf()    
#%%
last_week_dict_v4_w9[1][(14, 15)]
# we're stuck with an even bigger problem 
# now we still have gaps but the clusters seem to be the same VERI WEIRD~!
# check the theoretical clusters quantities 
theoretical_df
#%% md
### Check monday's quantities when running with different days back     

#%%
from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v2, handle_config_v3, handle_config_v4, DAYS_FOR_CLUSTER_PERFORMANCE, V5_DICT_FOR_DROP_LOSING_HOURS,DICT_FOR_DROP_LOSING_HOURS_W3


results = {}
profit_results = {}
for days_back in [0,1,2,3,4]:
    
    final_profit_v4_w9, last_week_dict_v4_w9 = handle_config_v4('ens_calmar0.25_S0.5_window4', ([[0, 1, 2, 3, 4]], 'all'),
                                                                9, 0, 2, days_back=days_back, clip_after=False,
                                                                initial_clip=1.25,drop_losing_hours_config=DICT_FOR_DROP_LOSING_HOURS_W3,
                                                                      big_loss_value=1800*1e3,bigloss1_value=400*1e3,bigloss2_value=800*1e3,block_14_utc=False)
    results[days_back] = last_week_dict_v4_w9
    profit_results[days_back] = final_profit_v4_w9

#%%
weekday_to_check = 1

hours_keys = results[0][weekday_to_check].keys()

print_in_details = False

misalignment_counters_lst = []

for hour_key in hours_keys:
    print('hour_key =',hour_key)
    misalignment_counter = 0
    for days_back in [1,2,3,4]:
        clusters = results[days_back][weekday_to_check][hour_key]
        clusters_days_back0 = results[0][weekday_to_check][hour_key]
        is_aligned = clusters==clusters_days_back0
        if not is_aligned:
            misalignment_counter += 1
        print('clusters(days_back=0) == clusters(days_back={})'.format(days_back),is_aligned)
        if not is_aligned:
            print('\tdays_back =',days_back,f'\n allowed_clusters ({days_back}) = ',clusters,
                  f'\n allowed_clusters (0) = ',clusters_days_back0)
            print('------------------------')
    print('****MISALIGNMENT_COUNTER =',misalignment_counter,'***')
    misalignment_counters_lst.append(misalignment_counter)
    print('=====================')
    
print(max(misalignment_counters_lst))
#%% md
OK 
so the problem seems to be that whenb dealing with by-weekday filtering once a given day ( e.g. Tuesday 19/12) is included in the profits we take care of it. 
this means that currently after a given day is over we are putting in the dict the config for next week already. 
the fix for this should be one of the 2: 
    either always leave dats until last monday in the final_profit ( https://github.com/ranglat/TWS_API/blob/17df9daed1f535a1cc4ffda3939d5ec4be9412f2/Algo/Performance_Analysis/clusters_auto_selection.py#L1071)
    clustwer utils line 1071 
    OR 
    when writing the dictionary we decide to ignore weekdays that are behind us in the week 
    OR 
    we simply allow this issue as it should not have any impact in reality (only changes once the day is over )
    
    BUT bottom line there doesn't seem to be a LEAKEAGE thank god! 