#%%
import matplotlib.pyplot as plt
import pandas as pd
from meteostat import Point, Daily
from geopy.geocoders import Nominatim

# Initialize the geocoder
geolocator = Nominatim(user_agent="my_geocoder")

# Specify the location name
location_name = "Jerusalem, Israel"

# Perform geocoding to get latitude and longitude
location = geolocator.geocode(location_name)
if 1 > 2:
    if location:
        latitude = location.latitude
        longitude = location.longitude
    
        # Initialize a Point object with the coordinates
        point = Point(latitude, longitude)
    
        # Set the start and end dates for the data retrieval
        start_date = pd.to_datetime('1990-01-01') # '1990-01-01'
        end_date = pd.to_datetime('2023-08-25')
    
        # Initialize a Daily object with the location and date range
        data = Daily(point, start_date, end_date)
    
        # Retrieve the weather data
        data = data.fetch()
    
        # Print the first few rows of the retrieved data
        print(data.head())
    else:
        print("Location not found.")
    
    
    data['tmax'].isna().groupby(data.index.to_period('M')).sum().plot()

#%%
import pandas as pd
from meteostat import Point, Daily

# Set the coordinates for Corfu Island, Greece
latitude = 39.6012
longitude = 19.9105

# Hodash 
latitude = 32.15
longitude = 34.9

# Merom golan 
latitude = 33.14
longitude = 35.77

# athens 
# latitude = 38
# longitude = 23.5

#  rome 
# latitude = 41.9
# longitude = 12.5

# TLV 
# latitude = 32.1
# longitude = 34.75

# JLM
# latitude = 31.8
# longitude = 35.2



# 
# Set coordinates for Lefkada island 
# latitude = 38.8333
# longitude = 20.7000
# latitude, longitude = (38.755505, 20.932173)
# the lat, lon we have retrieved are: 
# 38.8333, 20.7000



# Initialize a Point object with the coordinates
# todo 
location = Point(latitude, longitude)

# Set the start and end dates for the data retrieval
# start_date = '1990-01-01'
# end_date = '2023-08-25'
# define start, end date based on the above but as dtdt objects 
start_date = pd.to_datetime('1970-01-01')
end_date = pd.to_datetime('2024-08-25')

# Initialize a Daily object with the location and date range
data = Daily(location, start_date, end_date)

# Retrieve the weather data
data = data.fetch()

# plot number of nans for tmax field for each year
# data = data[data.index.month.isin([8,9,19])]
data['tmax'].isna().groupby(data.index.to_period('M')).sum().plot()
# print toal num of nans for this field 
print(data['tmax'].isna().sum())
# print total num of nans for months Aug-Sept-Oct of all years 
print(data['tmax'].isna().groupby(data.index.month).mean()[8:11])
data.head()
#%%
# for each week of year, and for each decade analyze the avg temperature and the avg precipitation
data['year'] = data.index.year
data['decade'] = data.index.year // 10 * 10
data['monday'] = data.index - pd.to_timedelta(data.index.weekday, unit='d')
data['week_num'] = data['monday'].dt.week


#%%
data = data.reset_index()
# data.groupby(data.time.dt.year).max()['week']
# 2021 is a yerar with 53 weeks we use it as reference 
# create a mapping from week number of 2021 to monday 
data2021 = data[data.time.dt.year == 2021]
mapping = data2021[['week_num', 'monday']].drop_duplicates().set_index('week_num').to_dict()['monday']
#%%
# data = data.reset_index()
data['ref_monday'] = data['week_num'].map(mapping)
data['month'] = data['time'].dt.month

data['is_rain'] = data['prcp'] > 3
#%%
data
#%%
from matplotlib import pyplot as plt

ref = data[data['year']==2023].copy()
ref['year'] = 2030
ref['decade'] = 2030
data = pd.concat([data, ref])

index = 'ref_monday'
index = 'month'

pivot = pd.pivot_table(data, values=['tmax','tmin', 'prcp','is_rain'], index=[index],
                       columns=['decade'],
                       aggfunc='mean')


pivot['prcp'].plot(figsize=(10,10))
plt.show()
pivot['tmax'].plot(figsize=(10,10))
pivot['tmin'].plot(figsize=(10,10),kind='bar',title='AVG minimum temperature in Hod Hasharon by month x decade + 2023 as "2030"',ylim=(5,15))
#%%
from matplotlib import pyplot as plt
month = 10
window = 20
data2 = data[data['month']==month]
data2 = data.copy()
# data['month_part'] = data['time'].dt.day // 10 * 10
cols = []
cols = ['month']
target = 'tmin'
# target = 'tmax'
# target = 'prcp'

pivot = pd.pivot_table(data2, values=['tmax','tmin', 'prcp','is_rain'], index=['year'],
                       columns=cols,
                       aggfunc='mean' if target != 'prcp' else 'sum')



if cols == ['month']:
    pivot = pivot[target]
    if target == 'prcp':
        pivot = pivot[[10,11,12,1,2,3,4]]
# else:
#     pivot = pivot[target]
rolling10 = pivot.rolling(window, min_periods=min(5,window)).mean()
# rolling10
ax = (rolling10.iloc[-3] - rolling10.iloc[10]).plot(kind='bar',figsize=(10,10),title=f'{target} x month = {month}')

mn = (rolling10.iloc[-3] - rolling10.iloc[10]).mean()
# add dashed line for mean
ax.axhline(mn, color='r', linestyle='--', lw=2)

rolling10.plot(figsize=(10,10),title=f'Rolling {window} year averages of {target} x month = {month}')
# rolling10.iloc[-3]
# pivot['prcp']
#%%
raise
#%%
corfu_pivot = pivot.copy()