#%%
import pandas as pd

from Algo.Utils.files_handle import HOME
import os 
import pandas as pd 
#%%

#%%
model = 'EPS'
ft_types = ['HDD']

if ft_types == ['CDD']:
    weights = ['NG_total_per_CDD_regional','NG_total_per_CDD','population_US_weight']
elif ft_types == ['HDD']:
    weights = ['NG_total_per_HDD_regional','NG_total_per_HDD','population_US_weight']

LIVE_EC2_DIR = os.path.join(HOME,'degdays_archive','Live','Live_from_ec2')
csv = os.path.join(LIVE_EC2_DIR,f'live_degdays_{model}.csv')

df = pd.read_csv(csv,parse_dates=['forecast_time','validation_day'])
df = df.query(f'weight.isin(@weights) & feature_type.isin(@ft_types)')
print(df.shape)

#%%
from datetime import datetime as dtdt

fcst_time_start = dtdt(2023,11,7)
fcst_time_end = dtdt(2023,11,11)

df = pd.read_csv(csv,parse_dates=['forecast_time','validation_day'])
df2 = df[(df['forecast_time']>= fcst_time_start)&(df['forecast_time']<= fcst_time_end)&(df['weight']=='population_US_weight')&(df['feature_type']=='HDD')]
df3 = df2.groupby(df2['forecast_time']).mean()
df3['Value'].plot(kind='bar',figsize=(8,8))

df2_pivot = pd.pivot_table(df2,index=['validation_day'],columns=['forecast_time'],values=['Value'],aggfunc='mean')
df2_pivot[list(df2_pivot)[-4:]].plot()
#%%
pivot = pd.pivot_table(df,values='Value',index=['forecast_time'],columns=['weight','feature_type'],aggfunc='sum')
#df.groupby(df['forecast_time']).mean()['Value'].plot(kind='bar')
pivot.dropna(how='any').diff()[-50:].plot(kind='bar',figsize=(8,8))
pivot.diff().corr()
#%%

#%%

#%%
pivot2 = pivot.loc['2022-11-01':'2023-03-01']
#df['forecast_time'].value_counts()
new_col = ('NG_total_per_HDD', 'HDD')
#new_col = ('NG_total_per_HDD_regional', 'HDD')
old_col = ('population_US_weight', 'HDD')

pivot2 = pivot2.dropna(how='any').diff(4)
pivot2['buckets_new'] = pd.qcut(pivot2[new_col], 10, labels=False)
pivot2['buckets_old'] = pd.qcut(pivot2[old_col], 10, labels=False)

print(f"percentage of time the buckets are the same: {(pivot2['buckets_new'] == pivot2['buckets_old']).mean()}")

print(f'Avg gap between buckets: {abs(pivot2["buckets_new"] - pivot2["buckets_old"]).mean()}')


#%%
eps_x_daily_diffs = r"C:\Users\<USER>\Documents\Work\Amazon\Xdata\X_file_DailyDiffs_EPS_v8_12Z.csv"
eps_xs = pd.read_csv(eps_x_daily_diffs,parse_dates=['forecast_time'])
eps_xs.iloc[-2]