#%%
directory = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest_v4"

import os
import pandas as pd
import numpy as np
#%%

for f in os.listdir(directory):
    if f.endswith(".csv") and "[0, 1, 2, 3, 4]" in f:
        # print(f)
        df = pd.read_csv(os.path.join(directory,f))
        cols = list(df)
        cols_subset = cols[1:]
        df1 = df[:-20]
        df2 = df[-20:]
        df3 = df2.replace(0,np.nan).dropna(subset=cols_subset,how='all')
        if df3.shape[0] < df2.shape[0]:
            print(f"df3 shape: {df3.shape}",f"df2 shape: {df2.shape}",f[-40:])
            final_df = pd.concat([df1,df3])
            print(f"original_df shape: {df.shape}",f"final_df shape: {final_df.shape}",f[-40:])
            final_df.to_csv(os.path.join(directory,f),index=False)