import time

from Notebooks.new_strategies_finder.production.daily_preds_backtest import main as daily_preds_backtest_main
from Notebooks.new_strategies_finder.production.wrap_winners_choice import main as wrap_winners_choice_main

from Algo.Utils.files_handle import HOME
# from Algo.Notebooks.new_strategies_finder.definitions import *
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig as BaseConfig
from Algo.Notebooks.new_strategies_finder.look_for_new_strategies_daily_preds import *

import os

def run_daily_preds(strategies_list = ['y_1200-1415'],
                    weekdays = [0,1,2,3,4],
                    skip_existing=False,base_on_existing=True,
                    predictors_cond = lambda x: SEARCH_COND_GENERAL_BASE(x),
                    test_start=BaseConfig.TEST_START,
                    allow_negative_signal=BaseConfig.ALLOW_NEGATIVE_SIGNAL,
                    debug=False,
                    outdir_suffix='v4',
                    look_up_windows_list=[30,60,90,120,180],
                    file_suffix='',
                    num_chosen=NUM_CHOSEN,
                    max_autocorrelaiton=0.9,
                    signal_filter_ratio=0.5
                    ):
    RefConfig = BaseConfig
    RefConfig.SIGNAL_FILTER_RATIO = signal_filter_ratio
    RefConfig.NUM_CHOSEN = num_chosen
    RefConfig.MAX_AUTOCORRELATION = max_autocorrelaiton
    RefConfig.ALLOW_NEGATIVE_SIGNAL = allow_negative_signal
    RefConfig.TEST_START = test_start
    RefConfig.PREDICTORS_FILTER_COND = predictors_cond
    RefConfig.PRODUCTION_STRATS = strategies_list
    RefConfig.LOOKUP_WINDOWS = look_up_windows_list
    RefConfig.LAST_DATE_FOR_BINS = dtdt(2030, 1, 1)
    RefConfig.NAME = 'base'
    RefConfig.BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', f'look_for_new_strategies_backtest_{outdir_suffix}')


    daily_preds_backtest_main(skip_existing=skip_existing,
                              weekdays_override=weekdays,
                              base_on_existing=base_on_existing,
                              weeks_back_if_base_on_existing=0,
                              debug=debug,
                              file_suffix=file_suffix,
                              Config=RefConfig
                              )


def run_winners_choice(strategies=BaseConfig.PRODUCTION_STRATS,
                       update_based_on_existing_file_anyway=True,
                       allow_negative_signal=BaseConfig.ALLOW_NEGATIVE_SIGNAL,
                       last_date_for_bins=BaseConfig.LAST_DATE_FOR_BINS):
    wrap_winners_choice_main(base_on_existing=False,
                         last_date_for_bins=last_date_for_bins,
                         # days_back=410
                         update_based_on_existing_file_anyway=update_based_on_existing_file_anyway,
                         strategies=strategies,
                             allow_negative_signal=allow_negative_signal,
                         )

if __name__ == '__main__':
    # run_daily_preds(strategies_list=['y_0800-1100'],
    #                 weekdays=[1],
    #                 look_up_windows_list=[150])
    # run_winners_choice(update_based_on_existing_file_anyway=False,
    #                    last_date_for_bins=dtdt(2024, 12, 12))
    # raise
    strats = [
     'y_0000-0400',
     'y_0600-0800',
     'y_0700-0800',
     'y_0800-1845',
     'y_1200-1845',
     'y_1100-1845',
        'y_0800-1100',
     'y_0800-1315',
     'y_1100-1315',
    'y_1200-1415',
    'y_1300-1415',
     'y_1415-1545',
     'y_1545-1745',
     'y_1645-1845',
     'y_1745-1945'
     ]#[10:]
    strats = [
              # 'y_0630-1130',
                 'y_0630-1230',
                 'y_1230-1830',
                # 'y_0630-1830',
                 # 'y_0630-1830',
                 # 'y_0800-1100',
                 'y_1100-1230',
                'y_1200-1415',
                 # 'y_1100-1415',
                    # 'y_1415-1545','y_1545-1745',
                 # 'y_1315-1745','y_1415-1745',
                ]
    additional_strats = ['y_1000-1200','y_1100-1200','y_1200-1300','y_1300-1415',
                         'y_1400-1500','y_1515-1645','y_1645-1745','y_1745-1845'
                         ]

    # strats_tmp = strats
    # strats_tmp = strats[6:9]
    # strats_tmp = strats[3:6
    import time
    # time.sleep(60*60)

    predictors_cond = lambda x: SEARCH_COND_GENERAL_BASE(x) # Base Pres
    # predictors_cond = SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL # ALL Preds
    # predictors_cond = SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL
    outdir_suffix = 'v4'
    outdir_suffix = 'v4_Scan_May25'
    # outdir_suffix = 'v5_clusters'
    # outdir_suffix = 'v4_all_preds'

    # num_chosen = 24

    # things to check:
    # Benchmark = SEARCH_COND_GENERAL_BASE without Negatives
    # 1. clusters
        # the check showed no improvement, # some signal captures but looks like all together works better
    # 2. Current method (SEARCH_COND_GENERAL_BASE) with Negatives
        # seems to harm the performance
    # 3. Current method (SEARCH_COND_GENERAL_BASE) other strats (1415-1545, 1545-1745)
        # didn't look too promising
    # 4. "All Preds" - using SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL
        # with Neg - X
        # without Neg - Better, but still not as good as the SEARCH_COND_GENERAL_BASE
        # check without Neg, but allowing much lower auto correlatio (0.75) - DONE - not bad, but not winning 0.9
        # check without Neg, but allowing much lower auto correlatio (0.84) - DONE - not bad, but not winning 0.9
        # check auto correlatio (0.95) - looks not bad, but not winning 0.9
        # CONCLUSION: looks like 0.9 is a sweet spot to stay in for now
    # 5. check different thresholds (other than 0.5)
        # 0.4 / 0.6 - DONE - 0.5 seems to be the best
    # 5. After deciding between SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL and SEARCH_COND_GENERAL_BASE
        # check the possibilite for 0,1 x 2,3,4 division of the week
        # Results: Interesting performance on 2,3,4 - worth combining into prod.

    max_autocorrelaiton = 0.9
    signal_filter_ratio = 0.5

    # suffix_name = '_autoCorr=0.95'
    suffix_name = ''
    if signal_filter_ratio != 0.5:
        suffix_name = f'_ratio={signal_filter_ratio}'

    # strats = ['y_1000-1200','y_1200-1300', 'y_1400-1500']
    strats = ['y_1330-1745','y_1345-1615', 'y_1400-1645','y_1400-1545'][2:]
    strats = [   'y_0630-1230', 'y_1230-1830',
        'y_1100-1230', 'y_1200-1415',
        'y_1000-1200',
        'y_1200-1300',
        'y_1400-1500','y_1400-1645','y_1645-1845'
     ]
    for weekdays_list in [
                            # [[0, 1, 2, 3, 4]],
                          # [[2, 3, 4]],[[0, 1]],
                          #   [[1,2,4]],
                          #   [[1,4]]
                            [[1]],[[2]],[[4]]
                             ][2:3]: # todo

        # for suffix_name, predictors_cond in zip(['_DailyPreds','_Classic','_Wind','_LongTerm'][2:3],
        #                                     [SEARCH_COND_GENERAL_BASE_DAILY,
        #                                      SEARCH_COND_GENERAL_BASE_CLASSIC,
        #                                      SEARCH_COND_GENERAL_BASE_WIND,
        #                                      SEARCH_COND_GENERAL_BASE_LONG_TERM][2:3]):

        test_start=dtdt(2024,1,1)
        run_daily_preds(strategies_list=strats,
                        # weekdays=[[0, 1, 2, 3, 4]],
                        weekdays=weekdays_list,
                        look_up_windows_list=[ 30,60,
                                                90,120,180],
                        skip_existing=True,
                        base_on_existing=False,
                        predictors_cond=predictors_cond,
                        test_start=test_start,
                        # allow_negative_signal=False
                        allow_negative_signal=False,
                        debug=False, # todo
                        outdir_suffix=outdir_suffix,
                        file_suffix=suffix_name,
                        max_autocorrelaiton=max_autocorrelaiton,
                        signal_filter_ratio=signal_filter_ratio
                        # num_chosen=num_chosen

                        )
