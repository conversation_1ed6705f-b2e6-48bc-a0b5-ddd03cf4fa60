#%%

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.model_selection import train_test_split
import os
from datetime import datetime as dtdt
from Algo.Notebooks.new_strategies_finder.analysis.utils import load_positions_df,analyze_profits,analyze_profits_by_hour
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig

PRODUCTION_STRATS = WeeklyConfig.PRODUCTION_STRATS
#%% md
### General Overview 
#%%

COST_PER_TRADE = 10
suffix = '_endDate=20240701'

positions_df = load_positions_df(cost_per_trade=COST_PER_TRADE,
                                 no_segments=False,
                                 file_suffix=suffix
                                 # no_segments=True
                                 )
START_DATE = '2023-06-01'
ALLOWWED_HOURS = None 
# ALLOWWED_HOURS = [10,11,12,13,14,16,17,18]

analyze_profits(positions_df,START_DATE,add_metric_to_plot=True,allowed_hours=ALLOWWED_HOURS,
                resolution='weekly')

positions_df['day'] = positions_df['date'].dt.date
positions_df['hour'] = positions_df['date'].dt.hour 

daily_profits2 = positions_df.query('date > @START_DATE').groupby(['day','hour'])[['profit_clip2']].sum().unstack().fillna(0)
daily_profits2.cumsum().plot(figsize=(15,10),title='Cumulative profit Clip2 by hour',style=['-']*7+['--']*7)
print(f'FILE SUFFIX = {suffix}')
#%%

#%%

COST_PER_TRADE = 10
suffix = '_endDate=20240701'

positions_df_end_dates_full = load_positions_df(cost_per_trade=COST_PER_TRADE,
                                 no_segments=False,
                                 file_suffix=suffix
                                 # no_segments=True
                                 )
positions_df_with_segments_full = load_positions_df(cost_per_trade=COST_PER_TRADE,
                                 no_segments=False,
                                 file_suffix=''
                                 )
positions_df_no_segments_full = load_positions_df(cost_per_trade=COST_PER_TRADE,
                                 no_segments=True,
                                 file_suffix=''
                                 )

positions_df_end_dates = positions_df_end_dates_full[['date','profit_clip2','final_quantity','y']]
positions_df_with_segments = positions_df_with_segments_full[['date','profit_clip2','final_quantity','y']]
positions_df_no_segments = positions_df_no_segments_full[['date','profit_clip2','final_quantity','y']]

positions_df_end_dates['type'] = 'end_dates'
positions_df_with_segments['type'] = 'with_segments'
positions_df_no_segments['type'] = 'no_segments'

positions_df = pd.concat([positions_df_end_dates,positions_df_with_segments,positions_df_no_segments])

#%% md
# Research on Weekday suggested the Following:
### For days = [0,3] - use no segments
### For days = [1,4] - use with segments
### For days = [2] - use end dates
## So we built a hybrid mode
#%%

NORMALIZE = False
CLIP = None
CLIP = 2
COST_PER_CONTRACT = 20000

WEEKDAYS = [0,1,2,3,4]
WEEKDAYS = [2]
# WEEKDAYS = [1,4]
PROFIT_IN_PCT = False
PROFIT_IN_PCT = True

pivot = pd.pivot_table(positions_df,index='date',columns='type',values=['final_quantity','y'],aggfunc=np.sum).fillna(0)

# set up a new quantity column which will be based on : end_dates for weekdays = [2], with_segments for weekdays = [1,4] and no_segments for weekdays = [0,3]

# PRODUCTION
weekdays_for_with_segments_in_hybrid = [1,4]
weekdays_for_end_dates_in_hybrid = [2]

# test
# weekdays_for_with_segments_in_hybrid = [1]
# weekdays_for_end_dates_in_hybrid = [2,4]

pivot[('final_quantity','hybrid_by_weekday')] = pivot[('final_quantity','no_segments')] # for 0,3
pivot.loc[pivot.index.weekday.isin(weekdays_for_with_segments_in_hybrid),('final_quantity','hybrid_by_weekday')] = pivot.loc[pivot.index.weekday.isin(weekdays_for_with_segments_in_hybrid),('final_quantity','with_segments')]
pivot.loc[pivot.index.weekday.isin(weekdays_for_end_dates_in_hybrid),('final_quantity','hybrid_by_weekday')] = pivot.loc[pivot.index.weekday.isin(weekdays_for_end_dates_in_hybrid),('final_quantity','end_dates')]


pivot[('final_quantity','with_+_endDates')] = (pivot[('final_quantity','with_segments')] + pivot[('final_quantity','end_dates')])/(2 if NORMALIZE else 1)

pivot[('final_quantity','noSegments_+_endDates')] = (pivot[('final_quantity','no_segments')] + pivot[('final_quantity','end_dates')])/(2 if NORMALIZE else 1)

pivot[('final_quantity','noSegments_+_withSegments')] = (pivot[('final_quantity','no_segments')] + pivot[('final_quantity','with_segments')])/(2 if NORMALIZE else 1)

pivot[('final_quantity','all')] = (pivot[('final_quantity','no_segments')] + pivot[('final_quantity','with_segments')] + pivot[('final_quantity','end_dates')])/(3 if NORMALIZE else 1)

if CLIP:
    pivot[('final_quantity','with_+_endDates')] = pivot[('final_quantity','with_+_endDates')].clip(-CLIP,CLIP)
    pivot[('final_quantity','noSegments_+_endDates')] = pivot[('final_quantity','noSegments_+_endDates')].clip(-CLIP,CLIP)
    pivot[('final_quantity','noSegments_+_withSegments')] = pivot[('final_quantity','noSegments_+_withSegments')].clip(-CLIP,CLIP)
    pivot[('final_quantity','all')] = pivot[('final_quantity','all')].clip(-CLIP,CLIP)



final_quantity_cols = [x for x in list(pivot) if x[0] == 'final_quantity']

for final_quantity_col in final_quantity_cols:
    # print(('y',final_quantity_col[1]))
    pivot[('profit',final_quantity_col[1])] = pivot[('y','with_segments')] * pivot[final_quantity_col].clip(-CLIP,CLIP)
    abs_change = abs(pivot[final_quantity_col].diff())

    pivot[('profit',final_quantity_col[1])] -= abs_change * COST_PER_TRADE
    if PROFIT_IN_PCT:
        quantity_cols = [x for x in list(pivot) if x[0] == 'final_quantity' and x[1] == final_quantity_col[1]]
        max_per_day = pivot[quantity_cols].resample('D').max()
        min_per_day = -(pivot[quantity_cols].resample('D').min())
        max_abs_per_day = max_per_day.combine(min_per_day, np.maximum)
        margin_per_day = max_abs_per_day * COST_PER_CONTRACT
        margin_per_day.columns = [('daily_margin',x[1]) for x in margin_per_day.columns if x[0] == 'final_quantity']
        margin_per_day = margin_per_day.reset_index()
        margin_per_day.columns = ['day']+list(margin_per_day)[1:]
        # print('margin_per_day[day].dtype',margin_per_day['day'].dtype)
        pivot['day'] = pd.to_datetime(pivot.index.date)
        pivot = pivot.reset_index()
        # print('list(pivot)',list(pivot))
        pivot = pivot.merge(margin_per_day,on=['day'],how='left')

        pivot.set_index(('date',''),inplace=True)
        pivot[('profit',final_quantity_col[1]) ] /= (pivot[('daily_margin',final_quantity_col[1])]/100)


profit_cols = [x for x in list(pivot) if x[0] == 'profit']

if WEEKDAYS != [0,1,2,3,4]:
    pivot = pivot[pivot.index.weekday.isin(WEEKDAYS)]

# aggregate to weekly
pivot['week'] = pivot.index.to_period('W')
pivot_weekly = pivot.groupby('week').sum()

pivot[profit_cols].cumsum().plot(figsize=(15,10),title='Cumulative profit by type',style=['-']*7+['--']*7)
plt.show()

pivot_weekly[profit_cols].cumsum().plot(figsize=(15,10),title='Cumulative profit by type (weekly)',style=['-']*7+['--']*7)
plt.show()

from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis

for c in pivot_weekly:
    if c[0] == 'profit':
        print_kpis(pivot_weekly[c],c)

#%%
# positions_df_end_dates = positions_df_end_dates[['date','profit_clip2','final_quantity','y']]
positions_df_hybrid = positions_df_no_segments_full.copy()

positions_df_hybrid.loc[positions_df_hybrid['date'].dt.weekday.isin([1,4]),'final_quantity'] = positions_df_with_segments_full.loc[positions_df_hybrid['date'].dt.weekday.isin([1,4]),'final_quantity']
positions_df_hybrid.loc[positions_df_hybrid['date'].dt.weekday.isin([1,4]),'profit_clip2'] = positions_df_with_segments_full.loc[positions_df_hybrid['date'].dt.weekday.isin([1,4]),'profit_clip2']

positions_df_hybrid.loc[positions_df_hybrid['date'].dt.weekday.isin([2]),'final_quantity'] = positions_df_end_dates_full.loc[positions_df_hybrid['date'].dt.weekday.isin([2]),'final_quantity']
positions_df_hybrid.loc[positions_df_hybrid['date'].dt.weekday.isin([2]),'profit_clip2'] = positions_df_end_dates_full.loc[positions_df_hybrid['date'].dt.weekday.isin([2]),'profit_clip2']

# set hybrid as the official for the analysis
positions_df = positions_df_hybrid.copy()
# print(positions_df.describe())