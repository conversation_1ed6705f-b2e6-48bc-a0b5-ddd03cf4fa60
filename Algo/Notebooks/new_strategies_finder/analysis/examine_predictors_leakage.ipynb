#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

#%%
chosen_preds_history_csv = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest_fixed_metadata\chosen_preds_history\chosen_preds_history_y_1100-1315.csv"

# chosen_preds_history = pd.read_csv(chosen_preds_history_csv)
#%%
# chosen_preds_history
import pandas as pd
a_csv = r"C:\Users\<USER>\Documents\Work\Amazon\XYs\Enriched_XYs\XY_a_GDD_v8_0Zb.csv"
df = pd.read_csv(a_csv,parse_dates=['date'])
[x for x in list(df) if x.startswith('y_') and '-' in x and len(x) == 2+8+1]

#%%
# all_predictors_list_of_lists = chosen_preds_history['buy_predictors'].apply(eval).tolist() + chosen_preds_history['sell_predictors'].apply(eval).tolist()
# list1 = [item for sublist in all_predictors_list_of_lists for item in sublist]
# all_predictors_list = list(set(list1))
# all_predictors_list.sort()
#%%
# all_predictors_list
#%%
from Algo.Notebooks.new_strategies_finder.look_for_new_strategies_daily_preds import *


for c in all_predictors_list:
    if not SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_ONLY_GFS_6Z(c):
        print(c,'was not supposed to be allowed')
    else:
        print(c,'was allowed')
#%%
SEARCH_COND_GENERAL_NO8to16('macd_sign_30M_diff1_prediction')
#%% md
# Compare "a" CSVs
#%%
from datetime import datetime as dtdt
import pandas as pd 

a_csv_early = r"C:\Users\<USER>\Downloads\XY_a_GDD_v8_0Zb_0840.csv"
a_csv_latest = r"C:\Users\<USER>\Downloads\XY_a_GDD_v8_0Zb_1305.csv"

day_to_compare = dtdt(2024,11,19).date()

a_df_early = pd.read_csv(a_csv_early,parse_dates=['date'])[-10:]
a_df_latest = pd.read_csv(a_csv_latest,parse_dates=['date'])[-10:]

a_df_latest = a_df_latest[a_df_latest['date'].dt.date == day_to_compare]
a_df_early = a_df_early[a_df_early['date'].dt.date == day_to_compare]
#%%

buy_preds_0z = ['profit_y_0800-1100_diff_0Z_0-8_last-Prev4_GFSv16-', 'profit_y_0800-1100_diff_0Z_0-8_last-Prev4_PARA-', 'profit_y_0800-1100_diff_0Z_0-16_Prev1D-Prev2D_PARACO-', 'profit_y_0800-1100_diff_0Z_0-10_last-Prev2D_PARACO-']
sell_preds_0z = ['profit_y_0800-1100_diff_0Z_14-35_Prev1D-Prev3D_CFS-', 'profit_y_0800-1100_diff_0Z_14-28_Prev1D-Prev3D_CFS-', 'profit_y_0800-1100_diff_0Z_28-42_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_14-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_21-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_21-42_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_0-16_Prev1D-Prev2D_GEMCO-']

buy_preds_other = ['profit_y_0800-1100_diff_12Z_0-10_Prev1D-Prev2D_GEM-', 'profit_y_0800-1100_diff_0Z_0-8_last-Prev4_GFSv16-', 'profit_y_0800-1100_diff_0Z_0-8_last-Prev4_PARA-', 'profit_y_0800-1100_diff_18Z_0-2_last-Prev4_PARACO-', 'profit_y_0800-1100_diff_0Z_0-16_Prev1D-Prev2D_PARACO-', 'profit_y_0800-1100_diff_12Z_0-10_last-Prev1D_PARACO-', 'profit_y_0800-1100_diff_0Z_0-10_last-Prev2D_PARACO-']

sell_preds_other = ['profit_y_0800-1100_diff_12Z_28-35_last-Prev3_CFS', 'profit_y_0800-1100_diff_0Z_14-35_Prev1D-Prev3D_CFS-', 'profit_y_0800-1100_diff_18Z_14-28_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_14-28_Prev1D-Prev3D_CFS-', 'profit_y_0800-1100_diff_0Z_28-42_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_14-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_21-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_18Z_21-42_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_18Z_14-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_0Z_21-42_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_18Z_21-35_Prev1D-Prev2D_CFS-', 'profit_y_0800-1100_diff_12Z_0-10_Prev1D-Prev3D_GEM-', 'profit_y_0800-1100_diff_0Z_0-16_Prev1D-Prev2D_GEMCO-', 'profit_y_0800-1100_diff_12Z_0-16_last-Prev2D_GEMCO-', 'profit_y_0800-1100_diff_18Z_0-16_Prev1D-Prev3D_PARA-', 'profit_y_0800-1100_Value_0Z_real_0-15_-1Y_PARA-', 'profit_y_0800-1100_Value_0Z_real_14-16_-1Y_PARA-', 'profit_y_0800-1100_Value_0Z_0-16_GEFS_rs-', 'profit_y_0800-1100_Value_0Z_0-16_PARACO_rs-', 'profit_y_0800-1100_Value_0Z_0-16_GEFS-EPS_rs-', 'profit_y_0800-1100_Value_0Z_0-16_GEFS-EPS(-1)_rs-']


suffix = 'profit_y_0800-1100_'

buy_preds_0z_final = [x.replace(suffix,'') for x in buy_preds_0z + buy_preds_other]
sell_preds_0z_final = [x.replace(suffix,'') for x in sell_preds_0z + sell_preds_other]


for c in buy_preds_0z_final+sell_preds_0z_final:
    if c.endswith('-'):
        c = c[:-1]
    old_val = a_df_early[c].iloc[0]
    new_val = a_df_latest[c].iloc[0]
    if old_val != new_val:
        print(c,':',old_val,new_val)

#%%
old_strategy_csv = r"C:\Users\<USER>\Downloads\look_for_strategies_slide_build_v8_0Zb_y_0800-1100_weekday=[1]_start=20230601_150_v2_calmar=10_ppt=10.csv"

new_strategy_csv =  r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest_fixed_metadata\look_for_strategies_slide_build_v8_0Zb_y_0800-1100_weekday=[1]_start=20230601_150_v2_calmar=10_ppt=10.csv"

old_strategy_df = pd.read_csv(old_strategy_csv)
new_strategy_df = pd.read_csv(new_strategy_csv)

last_row_old = old_strategy_df.iloc[-1]
last_row_new = new_strategy_df.iloc[-1]

for c in list(old_strategy_df):
    print(c,':',last_row_old[c],last_row_new[c])
#%%
# print('Last date in 011124_1751:',a_df_early['date'].iloc[-1])
# print('Last date in latest:',a_df_latest['date'].iloc[-1])
for col in list(a_df_early.columns):
    last_val_011124_1751 = a_df_early[col].iloc[-1]
    last_val_latest = a_df_latest[col].iloc[-1]
    if last_val_011124_1751 != last_val_latest:
        if a_df_latest[col].dtype == 'float64':
            if not np.isnan(last_val_latest) and not np.isnan(last_val_011124_1751): 
                print(col,'differs',a_df_early[col].iloc[-1],a_df_latest[col].iloc[-1])
        else:
            print(col,'differs',a_df_early[col].iloc[-1],a_df_latest[col].iloc[-1])
        
#%%

#%%
import pandas as pd 
import os 
csv = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest_v4\look_for_strategies_slide_build_v8_0Zb_y_1200-1415_weekday=[0, 1, 2, 3, 4]_start=20240101_180_v2_calmar=10_ppt=10_noNeg.csv"

d = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest_v4"

for csv in os.listdir(d):
    if '[0, 1, 2' in csv:
        if csv.endswith('.csv'):
            full_path = os.path.join(d,csv)
            df = pd.read_csv(full_path)
            new_df = df[[x for x in list(df) if (sum([s in x for s in ['ppt','calmar','trades']]) == 0) or sum([s in x for s in ['q20','q80','median']]) > 0]]
            print(df.shape, '------>',new_df.shape)
            dropped_cols = [x for x in list(df) if x not in list(new_df)]
            # if dropped_cols:
            #     print(df[dropped_cols].iloc[-10])
            new_df.to_csv(full_path,index=False)
# df = pd.read_csv(csv)
# df