#%% md
# Research summary mm
#%% md
# Issues to be addressed:

## 1. num_chosen < 50? 
    * checked 24 - didn't improve - but still 50 might not be optimal 
    * 
## 2. Filter conds != SEARCH_COND_GENERAL_BASE 
    ### 1. clusters
        # the check showed no improvement, # some signal captures but looks like all together works better
    ### 2. "All Preds" - using SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL
        # with Neg - X
        # without Neg - Better, but still not as good as the SEARCH_COND_GENERAL_BASE
        # check without Neg, but allowing much lower auto correlatio (0.75) - DONE - not bad, but not winning 0.9
        # check without <PERSON>eg, but allowing much lower auto correlatio (0.84) - DONE - not bad, but not winning 0.9
        # check auto correlatio (0.95) - looks not bad, but not winning 0.9
        # CONCLUSION: looks like 0.9 is a sweet spot to stay in for now
## 3. Current method (SEARCH_COND_GENERAL_BASE) with Negatives
        # seems to harm the performance
## 4. Other Strats 
     * 1415-1545, 1545-1745 - X 
        # didn't look too promising
     * 'y_1000-1200','y_1100-1200','y_1200-1300','y_1300-1415', - ??? CHECK #TODO 
## 5. check different thresholds (other than 0.5)
        # 0.4 / 0.6 - DONE - 0.5 seems to be the best
## 6. Different weekdays split 
    ### check days 0,1 x 2,3,4 division of the week
    ### Results: Interesting performance on 2,3,4 - worth combining into prod.!!!! 
    The calmar and sharpe look better when looking only on [2,3,4]. question now is whether to combine both of the in Prod of use [0,1,2,3,4] for [0,1] and [2,3,4] for the [2,3,4] - TODO TODO TODO 

#%% md
# Examine combining the semi weekly with the weekly 
#%%
import pandas as pd 

semi_weekly_winners_df2_csv = r'C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\new_strategies_finder\winners_df2_semi_weekly.csv'

weekly_winners_df2_csv = r'C:\Users\<USER>\PycharmProjects\TWS_API\Algo\Notebooks\new_strategies_finder\winners_df2_weekly.csv'

semi_weekly_winners_df2 = pd.read_csv(semi_weekly_winners_df2_csv,
                                      parse_dates=['date'])

weekly_winners_df2 = pd.read_csv(weekly_winners_df2_csv,
                                 parse_dates=['date'])

#%%
# for num_days in ['90d','120d','150d'][:1]:
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d,print_kpis



semi_weekly_winners_df2_234 = semi_weekly_winners_df2[semi_weekly_winners_df2['weekday'].isin([2,3,4])]
semi_weekly_winners_df2_24 = semi_weekly_winners_df2[semi_weekly_winners_df2['weekday'].isin([2,4])]
semi_weekly_winners_df2_234_half_contract = (semi_weekly_winners_df2_234.set_index('date') * 0.5).reset_index()

semi_weekly_winners_df2_234_double_contract = (semi_weekly_winners_df2_234.set_index('date') * 2).reset_index()

semi_weekly_winners_df2_234_double_contract_for_24 = semi_weekly_winners_df2_234.copy()
for c in list(semi_weekly_winners_df2_234_double_contract_for_24.columns):
    if 'profit' in c:
        
        semi_weekly_winners_df2_234_double_contract_for_24.loc[semi_weekly_winners_df2_234_double_contract_for_24['date'].dt.weekday.isin([2,4]),c] = semi_weekly_winners_df2_234_double_contract_for_24.loc[semi_weekly_winners_df2_234_double_contract_for_24['date'].dt.weekday.isin([2,4]),c].apply(lambda x: x*2)
        
        semi_weekly_winners_df2_234_double_contract_for_24.loc[semi_weekly_winners_df2_234_double_contract_for_24['date'].dt.weekday.isin([3]),c] = semi_weekly_winners_df2_234_double_contract_for_24.loc[semi_weekly_winners_df2_234_double_contract_for_24['date'].dt.weekday.isin([3]),c].apply(lambda x: x*1.5)
        
        


semi_weekly_winners_df2_234_double_thu = (semi_weekly_winners_df2_234.set_index('date') * 2).reset_index()

weekly_winners_df2_01 = weekly_winners_df2[weekly_winners_df2['weekday'].isin([0,1])]
weekly_winners_df2_013 = weekly_winners_df2[weekly_winners_df2['weekday'].isin([0,1,3])]

# for c in list(weekly_winners_df2_013.columns):
#     if 'profit' in c:
#         weekly_winners_df2_013.loc[weekly_winners_df2_013['date'].dt.weekday.isin([3]),c] = weekly_winners_df2_013.loc[weekly_winners_df2_013['date'].dt.weekday.isin([3]),c].apply(lambda x: x*0.5)


#combined = [2,3,4] from semi + [0,1] from weekly
combined_df = pd.concat([semi_weekly_winners_df2_234, weekly_winners_df2_01]).sort_values(by='date', ascending=False)

#combined24 = [2,4] from semi + [0,1,3] from weekly
combined_df24 = pd.concat([semi_weekly_winners_df2_24, weekly_winners_df2_013]).sort_values(by='date', ascending=False)

# combined_both = [2,3,4] from semi + [0,1,2,3,4] from weekly
combined_df_both = pd.concat([semi_weekly_winners_df2_234, weekly_winners_df2]).sort_values(by='date', ascending=False)
combined_df_both = combined_df_both.groupby('date').sum().reset_index()

# combined_double = 2 X [2,3,4] from semi + [0,1] from weekly (semi 234
combined_df_double = pd.concat([semi_weekly_winners_df2_234_double_contract, weekly_winners_df2_01]).sort_values(by='date', ascending=False)

# thu semi 24 = 2 X [2,3,4] from semi  + 1 X [0,1,3] from weekly
combined_df_double_mixed_thu_semi_24 = pd.concat([semi_weekly_winners_df2_234_double_contract_for_24, weekly_winners_df2_013]).sort_values(by='date', ascending=False)
combined_df_double_mixed_thu_semi_24 = combined_df_double_mixed_thu_semi_24.groupby('date').sum().reset_index()


combined_df_weekly = combined_df.set_index('date').resample('W').sum()
combined_df_24_weekly = combined_df24.set_index('date').resample('W').sum()

combined_df_both_weekly = combined_df_both.set_index('date').resample('W').sum()
combined_df_double_weekly = combined_df_double.set_index('date').resample('W').sum()
combined_df_double_mixed_thu_semi_24_weekly = combined_df_double_mixed_thu_semi_24.set_index('date').resample('W').sum()
weekly_winners_df2_weekly = weekly_winners_df2.set_index('date').resample('W').sum()

# plot 
comparison_df = weekly_winners_df2_weekly[['total_profit_2_contracts']].merge(combined_df_weekly[['total_profit_2_contracts']], left_index=True, right_index=True, suffixes=('_weekly', '_combined'))

comparison_df = comparison_df.merge(combined_df_24_weekly[['total_profit_2_contracts']].rename(columns={'total_profit_2_contracts':'total_profit_2_contracts_comb24'}), left_index=True, right_index=True)

comparison_df = comparison_df.merge(combined_df_both_weekly[['total_profit_2_contracts']].rename(columns={'total_profit_2_contracts':'total_profit_2_contracts_both'}), left_index=True, right_index=True)

comparison_df = comparison_df.merge(combined_df_double_weekly[['total_profit_2_contracts']].rename(columns={'total_profit_2_contracts':'total_profit_2_contracts_double_semi234'}), left_index=True, right_index=True)

comparison_df = comparison_df.merge(combined_df_double_mixed_thu_semi_24_weekly[['total_profit_2_contracts']].rename(columns={'total_profit_2_contracts':'total_profit_2_contracts_mixed_thu_semi_24'}), left_index=True, right_index=True)

comparison_df['total_profit_2_contracts_combined_x2'] = comparison_df['total_profit_2_contracts_combined'] * 2
# comparison_df['total_profit_2_contracts_combined_mixed_thu+combined_naive'] = (comparison_df['total_profit_2_contracts_combined_x2'] + comparison_df['total_profit_2_contracts_mixed_thu_semi_24'])/2

comparison_df = comparison_df[0:]
comparison_df.cumsum().plot(title='Comparison of weekly performance', figsize=(15, 10))


print_kpis(comparison_df['total_profit_2_contracts_combined'], 'total_profit_2_contracts_combined')
print_kpis(comparison_df['total_profit_2_contracts_combined_x2'], 'total_profit_2_contracts_combined_x2')


print_kpis(comparison_df['total_profit_2_contracts_weekly'], 'total_profit_2_contracts_weekly')

print_kpis(comparison_df['total_profit_2_contracts_both'], 'total_profit_2_contracts_both')

print_kpis(comparison_df['total_profit_2_contracts_double_semi234'], 'total_profit_2_contracts_double_semi234')

print_kpis(comparison_df['total_profit_2_contracts_mixed_thu_semi_24'], 'total_profit_2_contracts_mixed_thu_semi_24')

# print_kpis(comparison_df['total_profit_2_contracts_combined_mixed_thu+combined_naive'], 'total_profit_2_contracts_combined_mixed_thu+combined_naive')

1
#%%
# check by positions df 
#%%
NO_SEGMENTER = False 
suffix = ''

if NO_SEGMENTER:
    suffix = '_no_segmenters'
csv_weekly = fr"C:\Users\<USER>\Documents\Work\Amazon\Trading\look_for_new_strategies_positions_NG_v4{suffix}.csv"
csv_01x234 = fr"C:\Users\<USER>\Documents\Work\Amazon\Trading\look_for_new_strategies_positions_NG_v4_semi01x234{suffix}.csv"


#%%

ALLOWED_WEEKDAYS = [2,4]


from Algo.Notebooks.new_strategies_finder.analysis.utils import load_positions_df
positions_df_weekly = load_positions_df(outpath_override=csv_weekly)
positions_df_01x234 = load_positions_df(outpath_override=csv_01x234)

positions_df_01x234['week'] = (positions_df_01x234['date'] - pd.to_timedelta(positions_df_01x234['date'].dt.weekday, unit='d')).apply(lambda x: x.date())
positions_df_weekly['week'] = (positions_df_weekly['date'] - pd.to_timedelta(positions_df_weekly['date'].dt.weekday, unit='d')).apply(lambda x: x.date())

positions_df_weekly = positions_df_weekly[positions_df_weekly['date'].dt.weekday.isin(ALLOWED_WEEKDAYS)]
positions_df_01x234 = positions_df_01x234[positions_df_01x234['date'].dt.weekday.isin(ALLOWED_WEEKDAYS)]

profit_cols = [c for c in positions_df_weekly.columns if 'profit' in c]
print(profit_cols)

positions_df_weekly_1w = positions_df_weekly.groupby('week').agg({'profit':'sum',})
positions_df_01x234_1w = positions_df_01x234.groupby('week').agg({'profit':'sum'})

comparison_df = positions_df_weekly_1w.merge(positions_df_01x234_1w, left_index=True, right_index=True, suffixes=('_weekly', '_combined'))



comparison_df.cumsum().plot(title='Comparison of weekly performance', figsize=(15, 10))
#%%
# Conclusion
# Semi weekly much better on [2,4]  - use double position relative to [0,1]
# On [3] - each has it's advantages - the mix of using SUM of position between weekly and semi weekly looks best. (making it double relative to [0,1])
# On [0,1] - keep with the weekly 

# ATTENTION!!!!!
    # this means that on the Production shceduler should know to trigger the weekly on [0,1], the Semi weekly on [2,4] and BOTH on [3]
    # also remember we have different segmenters_conf (forbidden_buckets_by_segmenter_new_v3_01x234) and different chosen windows 
    # need to know to support it 
    