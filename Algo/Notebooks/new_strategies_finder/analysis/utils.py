from Algo.Utils.files_handle import get_candles_outpath
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig as BaseConfig

import numpy as np
from matplotlib import pyplot as plt

import pandas as pd

COST_PER_TRADE = 7.5

def load_positions_df(cost_per_trade=COST_PER_TRADE,
                      no_segments=False,
                      file_suffix='',Config=None,
                      outpath_override=None):
    if Config is None:
        Config = BaseConfig

    outpath = Config.POSITIONS_FILE_PATH
    if no_segments:
        outpath = outpath.replace('.csv', '_no_segmenters.csv')
    outpath = outpath.replace('.csv', file_suffix + '.csv')
    if outpath_override:
        outpath = outpath_override
    positions_df = pd.read_csv(outpath, parse_dates=['date'], on_bad_lines='skip')
    positions_df['date'] = pd.to_datetime(positions_df['date'],errors='coerce')
    positions_df = positions_df.dropna(subset=['date'])
    candles_df = pd.read_csv(get_candles_outpath('NG', True), parse_dates=['date', 'time_utc'])

    candles_df['y'] = candles_df['open'].diff().shift(-1) * 10000
    candles_df['date'] = candles_df['time_utc']
    print('last candles_df date', candles_df['date'].max())

    positions_df = positions_df.merge(candles_df[['date', 'y']], on='date', how='left')
    positions_df.tail()

    cols_for_final_quantity = [x for x in list(positions_df) if x.startswith('quantity_y_')
                               if '1415-1845' not in x]
    positions_df['final_quantity'] = positions_df[cols_for_final_quantity].sum(axis=1)

    # $
    positions_df['final_quantity_abs_change'] = positions_df['final_quantity'].diff().abs()
    positions_df['final_quantity_abs_change_clip3'] = positions_df['final_quantity'].clip(-3, 3).diff().abs()
    positions_df['final_quantity_abs_change_clip2'] = positions_df['final_quantity'].clip(-2, 2).diff().abs()

    positions_df['profit'] = positions_df['y'] * positions_df['final_quantity'] - cost_per_trade * positions_df[
        'final_quantity_abs_change']
    positions_df['profit_clip3'] = positions_df['y'] * positions_df['final_quantity'].clip(-3, 3) - cost_per_trade * \
                                   positions_df['final_quantity_abs_change_clip3']

    positions_df['profit_clip2'] = positions_df['y'] * positions_df['final_quantity'].clip(-2, 2) - cost_per_trade * \
                                   positions_df['final_quantity_abs_change_clip2']

    positions_df['quantity'] = positions_df['final_quantity']
    positions_df['quantity_clip3'] = positions_df['final_quantity'].clip(-3, 3)
    positions_df['quantity_clip2'] = positions_df['final_quantity'].clip(-2, 2)
    return positions_df


def analyze_profits(positions_df, start_date='2023-06-21', profit_cols = ['profit', 'profit_clip3', 'profit_clip2'],
                    add_metric_to_plot=False, title_suffix='',
                    allowed_hours=None,resolution='daily',
                    chosen_profit_col='profit_clip2',
                    plot=True):
    from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d, print_kpis

    NORMALIZE_TO_PCT = True
    ONE_CONTRACT_MARGIN = 15000

    cond = 'date > @start_date'
    if allowed_hours:
        cond += ' and date.dt.hour in @allowed_hours'
    daily_profits = positions_df.query(cond).groupby(positions_df['date'].dt.date)[profit_cols].sum()

    if NORMALIZE_TO_PCT:
        for c in profit_cols:
            daily_profits[c] = daily_profits[c] / (
                        positions_df[c.replace('profit', 'quantity')].abs().max() * ONE_CONTRACT_MARGIN) * 100
    df_to_plot = daily_profits[profit_cols]
    if resolution == 'weekly':
        df_to_plot.index = pd.to_datetime(df_to_plot.index)
        df_to_plot = df_to_plot.resample('W').sum()
    if plot:
        ax = df_to_plot.cumsum().plot(figsize=(15, 10), title='Cumulative profit Clip' + (
        ' in %' if NORMALIZE_TO_PCT else ' in $ ') + title_suffix,
                                                  style=['-'] * 7 + ['--'] * 7)

    if add_metric_to_plot:
        # for each curve, add calmar, sharpe
        for i, c in enumerate(profit_cols):
            kpis = print_kpis(df_to_plot[c], c)
            calmar, sharpe = kpis['calmar'], kpis['sharpe']
            ax.text(0.3, 0.8 - i * 0.1, f'{c} : calmar = {calmar:.2f}, sharpe = {sharpe:.2f}',
                    horizontalalignment='center', verticalalignment='center', transform=ax.transAxes)
    try:
        return df_to_plot[chosen_profit_col]
    except:
        return df_to_plot

def analyze_profits_by_hour(weekday_df,tmp_weekday,grouper,cond_on_strats=lambda x: True,
                            title_suffix='',
                            plot=True):
    weekday_df['day'] = weekday_df['date'].dt.date
    weekday_df['hour'] = weekday_df['date'].dt.hour
    weekday_df['2H'] = weekday_df['date'].dt.hour // 2 * 2
    weekday_df['30M'] = weekday_df['date'].dt.strftime('%H:%M').str.replace(':45', ':30').str.replace(':15', ':00')
    weekday_df['15M'] = weekday_df['date'].dt.strftime('%H:%M')

    strats_cols = [x for x in list(weekday_df) if x.startswith('profit_') and cond_on_strats(x)]

    weekday_pivot = pd.pivot_table(weekday_df[['day', grouper] + strats_cols], index='day',
                                   columns=grouper, values=strats_cols, aggfunc=np.sum).fillna(
        0).swaplevel(axis=1)

    allowed_hours = range(6, 20)
    if grouper in ['15M','30M']:
        allowed_hours = [x for x in weekday_df[grouper].unique().tolist() if int(x[:2]) in allowed_hours]

    hourly_sums = {h: weekday_pivot[h].sum(axis=1) for h in allowed_hours
                   if h in weekday_pivot}
    hourly_sums = pd.DataFrame(hourly_sums)
    hourly_sums = hourly_sums[[c for c in list(hourly_sums) if hourly_sums[c].sum() != 0]]
    if plot:
        hourly_sums[sorted(list(hourly_sums))].cumsum().plot(figsize=(15, 10),
                                                         title=f'Profit by hour on weekday = {tmp_weekday}'+title_suffix,
                                                         style=['-'] * 7 + ['--'] * 7)
        plt.show()
    return hourly_sums

if __name__ == '__main__':
    positions_df = load_positions_df()
