from daily_preds_backtest import main as daily_preds_backtest_main
from wrap_winners_choice import main as wrap_winners_choice_main
from Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig, SemiWeeklyConfig234,SemiWeeklyConfig01,SemiWeeklyConfig01x234
from Notebooks.new_strategies_finder.definitions_v4 import LOOKUP_WINDOWS

# from Algo.Notebooks.new_strategies_finder.production.run_config import DEBUG
DEBUG = False
# DEBUG = True


from datetime import datetime as dtdt
# FULL_WINDOWS_LIST = [70,80,90, 120, 135,150, 180]
FULL_WINDOWS_LIST = LOOKUP_WINDOWS
BaseConfig = WeeklyConfig


DOCUMENT_CHOSEN_PREDS = False
DOCUMENT_CHOSEN_PREDS = True

ALLOWED_WEEKDAYS_SUBSETS = [0, 1, 2, 3, 4, [0, 1, 2, 3, 4],[2,3,4],[0,1]]

ADDITIONAL_BIN_END_DATES_FOR_WINNERS_CHOICE = [dtdt(2024, 7, 1), dtdt(2024, 9, 1),
                                        dtdt(2024, 11, 1), dtdt(2025, 1, 1),
                                        dtdt(2025, 3, 1), dtdt(2025, 5, 1)]

def main(base_on_existing=True,
         weekdays_override=None,only_winners_choice=False,
         strategies_conf='all',
         weeks_back_if_base_on_existing=0,
         Config=BaseConfig,document_chosen_preds=DOCUMENT_CHOSEN_PREDS,
         all_bin_end_dates=False):
    weekdays = Config.WEEKDAYS
    if weekdays_override is not None:
        weekdays = weekdays_override
    if not only_winners_choice:
        strategies_list = {'all': Config.PRODUCTION_STRATS,
                           '0z': Config.PRODUCTION_STRATS_0Z,
                           '6z_early': Config.PRODUCTION_STRATS_6Z_EARLY,
                           '6z_mid': Config.PRODUCTION_STRATS_6Z_MID,
                           '6z_late': Config.PRODUCTION_STRATS_6Z_LATE,
                           '0z+6z_early': list(set(Config.PRODUCTION_STRATS_0Z+Config.PRODUCTION_STRATS_6Z_EARLY)),
                           '0z+6z': list(set(Config.PRODUCTION_STRATS_0Z+Config.PRODUCTION_STRATS_6Z_EARLY+Config.PRODUCTION_STRATS_6Z_MID+Config.PRODUCTION_STRATS_6Z_LATE)),
                           '6z': list(set(Config.PRODUCTION_STRATS_6Z_EARLY+Config.PRODUCTION_STRATS_6Z_MID+Config.PRODUCTION_STRATS_6Z_LATE)),
                           '12z':Config.PRODUCTION_STRATS_12Z}[strategies_conf]

        if Config.SKIP_DAILY_PREDS_BACKTEST:
            print('Skipping daily_preds_backtest_main')
        elif len([w for w in weekdays if w in ALLOWED_WEEKDAYS_SUBSETS]) > 0:
            daily_preds_backtest_main(skip_existing=False,base_on_existing=base_on_existing,
                              weekdays_override=weekdays,
                              weeks_back_if_base_on_existing=weeks_back_if_base_on_existing,
                              debug=DEBUG,Config=Config,
                                      document_chosen_preds=document_chosen_preds)
        else:
            print(f'Weekdays subset {weekdays} not allowed! Skipping daily_preds_backtest_main')
            raise Exception('Weekdays subset not allowed')

    for use_segmenters in [True, False]:
        wrap_winners_choice_main(Config=Config,base_on_existing=False,use_segmenters=use_segmenters)
        if Config.NAME == 'semi234':
            additional_config_to_backfill = SemiWeeklyConfig01x234
            wrap_winners_choice_main(Config=additional_config_to_backfill, base_on_existing=False, use_segmenters=use_segmenters)
        if all_bin_end_dates and use_segmenters:
            for dt in ADDITIONAL_BIN_END_DATES_FOR_WINNERS_CHOICE:
                Config.LAST_DATE_FOR_BINS = dt
                wrap_winners_choice_main(Config=Config, base_on_existing=False, use_segmenters=use_segmenters,
                                         outpath_suffix='_endDate=%s' % dt.strftime('%Y%m%d'))


if __name__ == '__main__':
    import argparse
    from datetime import datetime as dtdt
    from typing import Union,List
    parser = argparse.ArgumentParser()
    # parser.add_argument('--weekdays', type=Union[List,int], default=[[0,1,2,3,4]])
    parser.add_argument('--only_today', type=bool, default=False)
    parser.add_argument('--only_yesterday', type=bool, default=False)
    parser.add_argument('--windows_group', type=str, default='all', choices=['all','group1','group2','group3'])
    parser.add_argument('--strat_conf', type=str, default='all', choices=['all','0z','6z_early','6z_mid','6z','0z+6z_early','0z+6z','6z_late','12z'])
    parser.add_argument('--config', type=str, default='weekly', choices=['weekly','semi234','semi01','semi01x234'])
    parser.add_argument('--all_bin_end_dates', type=bool, default=True)

    args = parser.parse_args()

    # Config = WeeklyConfig
    # use_segmenters = False
    # wrap_winners_choice_main(Config=Config, base_on_existing=False, use_segmenters=use_segmenters)
    # raise
    # weekdays = args.weekdays if isinstance(args.weekdays,list) else [args.weekdays]
    # if args.only_today:
    #     weekdays = [dtdt.today().weekday()]
    # elif args.only_yesterday:
    #     weekdays = [(dtdt.today().weekday()-1)%7]

    config = {'weekly':WeeklyConfig,'semi234':SemiWeeklyConfig234,'semi01':SemiWeeklyConfig01,
              'semi01x234':SemiWeeklyConfig01x234}.get(args.config,BaseConfig)

    windows_group_name = args.windows_group
    # windows_group_name = 'group2'

    windows_group = {'all': FULL_WINDOWS_LIST,
                     'group1': config.WINDOWS_LIST_GROUP1, 'group2': config.WINDOWS_LIST_GROUP2
                     # 'group1': WINDOWS_LIST_GROUP1A, 'group2': WINDOWS_LIST_GROUP2A, 'group3': WINDOWS_LIST_GROUP3A
                     }[windows_group_name]
    #weekdays = config.WEEKDAYS
    # wrap_winners_choice_main(
    #     last_date_for_bins=BaseConfig.LAST_DATE_FOR_BINS,
    #     update_based_on_existing_file_anyway=False,
    #     # base_on_existing=base_on_existing # OLD CODE
    #     base_on_existing=False,  # due to buckets calculation we must do from new (bug of Oct 24)!
    #     use_segmenters=True
    # )
    # raise

    main(
        base_on_existing=True,
         strategies_conf=args.strat_conf,
         # weeks_back_if_base_on_existing=0,
         weeks_back_if_base_on_existing=0,
        Config=config,
         only_winners_choice=False, # todo
         # only_winners_choice=True,
        weekdays_override=None,
        all_bin_end_dates=args.all_bin_end_dates
         )
