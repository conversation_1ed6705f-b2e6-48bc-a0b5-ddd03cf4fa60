import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from datetime import datetime as dtdt

# from Algo.Notebooks.new_strategies_finder.definitions import *
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig, SemiWeeklyConfig234,SemiWeeklyConfig01x234, WeeklyConfig2023a
from Algo.Notebooks.new_strategies_finder.utils import COST_PER_TRADE
from Algo.Stocks_Research.utils.general import choose_winner2, choose_winner, choose_winner_symmetric
from Algo.Utils.general import generate_positions_df_from_daily_signal
from Algo.Utils.general import get_utc_now_dt

BaseConfig = WeeklyConfig

TEMPLATE_WEEKDAY = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=[$DAY]_start=20230601$SUFFIX.csv"
TEMPLATE_WEEKLY = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20240101$SUFFIX.csv"

DEFAULT_CSVS_TEMPLATE = WeeklyConfig.CSVS_TEMPLATE

# for num_days in ['90d','120d','150d'][:1]:
from Algo.Performance_Analysis.clusters_auto_selection import print_kpis_with_90d, print_kpis

SEGMENTERS = [k for k,v in BaseConfig.FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW.items()
                if len(v)]


WINNERS_BY_STRATEGY_OLD = {'y_0800-1100':'winner_w150',
                       'y_1100-1315':'winner_w180',
                       'y_0800-1315':'winner_w150',
                      # 'y_1200-1415':'winner_w50',
                      'y_1200-1415':'winner_w150',
                      'y_1300-1415':'winner_w150',
                      'y_1415-1845':'winner_w180',
                      # 'y_1745-1945':'winner_w180',
                      'y_1415-1545':'winner_w100',
                        'y_1545-1745':'winner_w100',
                        'y_1645-1845':'winner_w90',
                        'y_1745-1945':'winner_w120',
                        'y_0800-1845':None,}


WINNERS_BY_STRATEGY = BaseConfig.WINNERS_BY_STRATEGY


WINNER_LOOKUP_WINDOWS = [20, 30, 40,
                   50,
                   70,
                   90,
                   100, 120, 150,
                   180
                   ]

WEEKDAYS = [0, 1, 2, 3, 4]
# BaseConfig.DA


NUM_BUCKETS = 10
PROFIT_COL = 'next_profit_voting'
FILES_SUFFIX = 'v2'
DROP_1415_to_1845_COND = lambda x: '1415-1845' not in x
ALLOW_ALL_COND = lambda x: '' in x

def filter_signals_by_segmenters(strat_df,segmenters,num_buckets=NUM_BUCKETS,
                                 last_date_for_bins=None):
    conds_stack = {}
    for c in segmenters:
        if c not in list(strat_df):
            print(f'{c} not in columns')
            print('columns: ', list(strat_df))
        ref_column = strat_df[c]
        if last_date_for_bins is not None:
            ref_column = ref_column[strat_df['date'] <= last_date_for_bins]
        try:
            buckets_limits = ref_column.quantile(np.linspace(0, 1, num_buckets + 1)).tolist()
            buckets_limits = [buckets_limits[0] - 100] + list(buckets_limits[1:-1]) + [buckets_limits[-1] + 100]
            strat_df[c + '_bins'] = pd.cut(strat_df[c], buckets_limits, labels=list(range(1, num_buckets + 1)))
        except:
            #add random noise to the values in scale of 0.01
            distorted_column = ref_column + np.random.normal(0,0.01,ref_column.shape[0])
            distorted_column[~np.isfinite(distorted_column)] = 9999 + np.random.normal(0,0.01,distorted_column[~np.isfinite(distorted_column)].shape[0])
            buckets_limits = distorted_column.quantile(np.linspace(0, 1, num_buckets + 1)).tolist()
            buckets_limits = [buckets_limits[0] - 100] + list(buckets_limits[1:-1]) + [buckets_limits[-1] + 100]
            try:
                strat_df[c + '_bins'] = pd.cut(strat_df[c], buckets_limits, labels=list(range(1, num_buckets + 1)))
            except:
                bb = 0
        # TODO drop ? --->>>
        # strat_df[c + '_bins'] = pd.qcut(strat_df[c].rank(method='first'), num_buckets,
        #                                 labels=list(range(1, num_buckets + 1)))
        # if not (strat_df[c + '_bins'] == strat_df[c + '_bins_v2']).mean() == 1:
        #     print('ERROR: bins are not equal')
        cond = strat_df[c + '_bins'].isin(BaseConfig.FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW[c])
        conds_stack[c] = cond
        print('Segmenter: ', c, 'We annihilate', cond.sum(), 'rows')
        strat_df.loc[cond, PROFIT_COL] = 0
        strat_df.loc[cond, 'action'] = 0
    return strat_df

def enrich_strat_df(strat_df):
    strat_df['action'] = (strat_df['num_buy_preds'] - strat_df['num_sell_preds']).clip(-1,1)
    strat_df['active_preds_ratio'] = strat_df['num_active_preds'] / strat_df['num_good_preds']
    strat_df['imbalance_ratio'] = strat_df[['num_buy_preds', 'num_sell_preds']].max(axis=1) / strat_df[
        ['num_buy_preds', 'num_sell_preds']].min(axis=1)
    strat_df['good_preds_ratio'] = strat_df['num_good_preds'] / (
            strat_df['num_good_preds'] + strat_df['num_bad_preds'])
    strat_df['action'] = 0
    strat_df.loc[strat_df['num_buy_preds'] > strat_df['num_sell_preds'], 'action'] = 1
    strat_df.loc[strat_df['num_buy_preds'] < strat_df['num_sell_preds'], 'action'] = -1
    return strat_df

def impute_profits_with_delta_ys(strat_df,strategy):
    from Algo.Utils.files_handle import get_delta_ys_full_df_path
    delta_ys_full_df = pd.read_csv(get_delta_ys_full_df_path('NG'),parse_dates=['date'],usecols=['date',strategy])
    bb = 0
    strat_df = strat_df.merge(delta_ys_full_df,on='date',how='left')
    strat_df['next_profit_voting_imputer'] = strat_df['action'] * strat_df[strategy] - COST_PER_TRADE*2
    strat_df['next_profit_voting'] = strat_df['next_profit_voting_imputer'].fillna(strat_df['next_profit_voting_imputer'])
    strat_df.drop(['next_profit_voting_imputer',strategy],axis=1,inplace=True)
    return strat_df


def impute_profits_backfill(Config,strategies=BaseConfig.PRODUCTION_STRATS,
                            lookup_windows=BaseConfig.LOOKUP_WINDOWS,
                            calmar_thresholds_list=BaseConfig.CALMAR_THRESHOLDS_LIST,
                            ppt_thresholds_list=BaseConfig.PPT_THRESHOLDS_LIST,
                            weekdays=[0,1,2,3,4]):
    for strategy in strategies:
        for num_days in [str(w) for w in lookup_windows]:
            for calmar in calmar_thresholds_list:
                for ppt in ppt_thresholds_list:
                    suffix_str = '_' + num_days + '_' + FILES_SUFFIX + '_calmar=' + str(calmar) + '_ppt=' + str(ppt)

                    for weekday in weekdays:
                        filename = Config.CSVS_TEMPLATE.replace("$STRAT", strategy).replace("$DAY", str(weekday)).replace(
                            "$SUFFIX",suffix_str)
                        outpath = os.path.join(BaseConfig.BACKTEST_OUTDIR, filename)

                        if os.path.exists(outpath):
                            print('handling strategy',strategy)
                            df = pd.read_csv(outpath, parse_dates=['date'])
                            # TODO if we move to corr weighted need to adjust
                            df['action'] = (df['num_buy_preds'] - df['num_sell_preds']).clip(-1, 1)
                            df = impute_profits_with_delta_ys(df, strategy)
                            df = df.drop('action',axis=1)
                            df.to_csv(outpath, index=False)


def load_all_strategies(Config,base_on_existing=False,
                        segmenters_override=None,
                        days_back=200*7//5, # to allow the rolling performance + consider weekends that produce no samples
                        ):
    strat_dfs_stack = []
    start_date = Config.FAR_PAST
    last_date_for_bins = Config.LAST_DATE_FOR_BINS
    weekdays = Config.WEEKDAYS
    lookup_windows = Config.LOOKUP_WINDOWS
    strategies = Config.PRODUCTION_STRATS
    allow_negative_signal = Config.ALLOW_NEGATIVE_SIGNAL
    calmar_thresholds_list = Config.CALMAR_THRESHOLDS_LIST
    ppt_thresholds_list = Config.PPT_THRESHOLDS_LIST
    segmenters = [k for k, v in Config.FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW.items()
                  if len(v)]
    if segmenters_override is not None:
        segmenters = segmenters_override

    for strategy in strategies:

        i = 0
        total_profits_stack = {}
        for num_days in [str(w) for w in lookup_windows]:
            for calmar in calmar_thresholds_list:
                for ppt in ppt_thresholds_list:
                    suffix_str = '_' + num_days + '_' + FILES_SUFFIX + '_calmar=' + str(calmar) + '_ppt=' + str(ppt)
                    if not allow_negative_signal:
                        suffix_str += '_noNeg'
                    strat_df = pd.DataFrame()

                    conf_name = f'{num_days}d_C{calmar}_P{ppt}'

                    for weekday in weekdays:
                        filename = Config.CSVS_TEMPLATE.replace("$STRAT", strategy).replace("$DAY", str(weekday)).replace("$SUFFIX",
                                                                                                              suffix_str)
                        outpath = os.path.join(BaseConfig.BACKTEST_OUTDIR, filename)
                        if os.path.exists(outpath):
                            df = pd.read_csv(outpath,
                                             parse_dates=['date'])
                            if base_on_existing:
                                start_date = min(start_date,df['date'].max() - pd.Timedelta(days=days_back))
                            df = df[df['date'] >= start_date]
                            strat_df = pd.concat([strat_df, df])
                        else:
                            print(f'File not found: {outpath}')
                    if strat_df.empty:
                        continue
                    if strategy == 'y_0800-1100':
                        bb = 0
                    strat_df = strat_df.sort_values('date')
                    strat_df = enrich_strat_df(strat_df)
                    strat_df = impute_profits_with_delta_ys(strat_df, strategy) # We make sure all profits are updates for the rolling mean
                    strat_df = filter_signals_by_segmenters(strat_df,segmenters,
                                                            last_date_for_bins=last_date_for_bins)
                    num_active_days_last_2_weeks = (strat_df.set_index('date')['next_profit_voting'][-15:]!=0).mean()
                    num_active_days_previous_2_weeks = (strat_df.set_index('date')['next_profit_voting'][-30:-15]!=0).mean()
                    print(f'For {strategy} and {conf_name} we have: LAST 2 WEEKS = {num_active_days_last_2_weeks} || PREVIOUS 2 WEEKS = {num_active_days_previous_2_weeks}')
                    compact_strat_df = strat_df[['date', PROFIT_COL, 'action']]
                    compact_strat_df['conf'] = conf_name
                    compact_strat_df['strategy'] = strategy
                    compact_strat_df = compact_strat_df.rename(columns={PROFIT_COL: 'profit'})

                    strat_dfs_stack.append(compact_strat_df)
                    i += 1
    final_strats_df = pd.concat(strat_dfs_stack)

    final_pivot_df = pd.pivot_table(final_strats_df, index=['date'],
                                    columns=['strategy', 'conf'],
                                    values=['profit','action'],
                                    )
    final_pivot_df = final_pivot_df.reorder_levels(['strategy', 'conf', None], axis=1)
    return final_pivot_df


def choose_winner_by_strategy(final_pivot_df,Config,resolution = 'daily',
                              allow_bfill=True
                              ):

    assert resolution == 'daily', 'currently not supporting weekly resolution'
    winners_stack = []

    strategies = Config.PRODUCTION_STRATS
    winners_by_strategy = Config.WINNERS_BY_STRATEGY

    for strat in strategies:

        strategy_df = final_pivot_df[strat]
        strat_profits_df = strategy_df[[x for x in list(strategy_df) if 'profit' in str(x)]]
        strat_actions_df = strategy_df[[x for x in list(strategy_df) if 'action' in str(x)]]

        # if resolution == 'weekly':
        #     strat_profits_df = strat_profits_df.resample('W').sum()

        original_cols = list(strat_profits_df)

        winner_lookup_window_name = winners_by_strategy.get(strat, 'winner_w120')
        winner_lookup_window = int(winner_lookup_window_name.split('_')[-1][1:])
        cols_for_winner = original_cols
        strategy_df = choose_winner_symmetric(strategy_df, winner_lookup_window, (winner_lookup_window_name,'profit'),
                                                   cols_for_winner,action_col_to_choose=(winner_lookup_window_name,'action'),
                                              allow_bfill=allow_bfill,
                                              strat_info=strat)
        for c in list(strategy_df):
            if 'winner' in str(c):
                final_pivot_df[(strat, c[0], c[1])] = strategy_df[c]
    return final_pivot_df

def update_relevant_rows_in_positions_df(final_positions_df, existing_df, base_on_existing=True):
    final_positions_df['updated_at'] = get_utc_now_dt()
    if base_on_existing and existing_df.shape[0] > 0:
        identical_rows = existing_df.drop('updated_at', axis=1).merge(final_positions_df.drop('updated_at', axis=1),
                                                                      on=[x for x in list(existing_df)
                                                                          if 'updated_at' not in x and x in list(final_positions_df)])
        residual_rows_to_update = final_positions_df[~final_positions_df['date'].isin(identical_rows['date'])]

        num_updated_rows = residual_rows_to_update.shape[0]
        if num_updated_rows > 0:
            print('Updating', num_updated_rows, 'rows')
            final_positions_df = pd.concat([existing_df, residual_rows_to_update]).sort_values(['date','updated_at'])
        else:
            final_positions_df = existing_df
        final_positions_df = final_positions_df.drop_duplicates(['date'], keep='last')
    else:
        num_updated_rows = 9999
    return final_positions_df, num_updated_rows

def build_positions_df_for_strats(Config,
                                  final_pivot_df,chosen_cols_cond=lambda x: True,
                                  ):
    strategies = Config.PRODUCTION_STRATS
    winners_by_strategy = Config.WINNERS_BY_STRATEGY
    final_positions_df = pd.DataFrame()
    # fill nans on the last day
    final_pivot_df.iloc[-1:] = final_pivot_df.iloc[-1:].fillna(0)
    for strat in strategies:
        tmp_df = pd.DataFrame({
            'date': final_pivot_df.index.tolist(),
            'quantity': final_pivot_df[strat][winners_by_strategy[strat]]['action']
        })
        tmp_df = tmp_df.dropna(subset=['quantity'])

        strat_positions = generate_positions_df_from_daily_signal(tmp_df, strategy=strat, quantity_col='quantity')
        strat_positions = strat_positions.rename(columns={'quantity': 'quantity_' + strat})
        if final_positions_df.empty:
            final_positions_df = strat_positions
        else:
            final_positions_df = final_positions_df.merge(strat_positions, on='date', how='outer')
    # drop the nans that still got there from the outer join (could have chosen inner join actually byut NM)
    final_positions_df = final_positions_df.dropna(subset=[x for x in list(final_positions_df) if 'quantity' in x],
                                                   how='any')
    final_positions_df['final_quantity'] = final_positions_df[
        [x for x in list(final_positions_df) if 'quantity' in x
         and chosen_cols_cond(x)]].sum(axis=1)
    return final_positions_df

def archive_previous_positions_df(existing_df,outpath_suffix):
    outdir = os.path.join(os.path.dirname(BaseConfig.POSITIONS_FILE_PATH),'look_for_new_strats_archive')
    filename = 'look_for_new_strategies_positions_NG_' + get_utc_now_dt().strftime('%Y%m%d_%H%M') + f'_{outpath_suffix}' + '.csv'
    only_last_week_df = existing_df[existing_df['date'] > get_utc_now_dt() - pd.Timedelta(days=7)]
    only_last_week_df.to_csv(os.path.join(outdir,filename),index=False)


def main(base_on_existing=False,
         chosen_cols_cond=ALLOW_ALL_COND,
         use_segmenters=True,
         days_back=200*7//5, # to allow the rolling performance + consider weekends that produce no samples
         update_based_on_existing_file_anyway=True,
         outpath_suffix='',
         Config=BaseConfig
         ):

    start_date = Config.FAR_PAST
    lookup_windows = Config.LOOKUP_WINDOWS
    strategies = Config.PRODUCTION_STRATS
    allow_negative_signal = Config.ALLOW_NEGATIVE_SIGNAL
    last_date_for_bins = Config.LAST_DATE_FOR_BINS
    weekdays = Config.WEEKDAYS

    existing_df = pd.DataFrame()
    outpath = Config.POSITIONS_FILE_PATH
    # if Config.NAME !='weekly':
    #     outpath = outpath.replace('.csv', f'_{Config.NAME}.csv')
    if not use_segmenters:
        outpath = outpath.replace('.csv','_no_segmenters.csv')
    outpath = outpath.replace('.csv', f'{outpath_suffix}.csv')
    if base_on_existing:
        assert update_based_on_existing_file_anyway, 'base_on_existing=True requires update_based_on_existing_file_anyway=True'
    if (base_on_existing or update_based_on_existing_file_anyway) and os.path.exists(outpath):
        existing_df = pd.read_csv(outpath,parse_dates=['date','updated_at'],
                                  on_bad_lines='skip')
        existing_df['date'] = pd.to_datetime(existing_df['date'],errors='coerce',utc=True).dt.tz_localize(None)
        existing_df['updated_at'] = pd.to_datetime(existing_df['updated_at'],errors='coerce')
        existing_df = existing_df.dropna(subset=['date','updated_at'])
        existing_df[[c for c in list(existing_df) if 'quantity' in c]] = existing_df[[c for c in list(existing_df) if 'quantity' in c]].astype(float)

        if 'updated_at' not in list(existing_df):
            existing_df['updated_at'] = get_utc_now_dt()
        if base_on_existing:
            start_date = existing_df['date'].max() - pd.Timedelta(days=days_back)
            print('overridine start_date to existing_df[date].max() - 200D:',start_date)

    allow_rolling_sums_backfill = not base_on_existing # when using base on existing we learn on a shorter windows in purpose, no need to use entries that had backfill over real entries

    #### CALCULATIONS #####
    final_pivot_df = load_all_strategies(Config=Config,base_on_existing=base_on_existing,
                                         segmenters_override=[] if not use_segmenters else None,
                                         days_back=days_back)

    print('After loading stategies, final_pivot_df.shape:',final_pivot_df.shape)
    final_pivot_df = choose_winner_by_strategy(final_pivot_df,Config=Config,
                                               resolution = 'daily',
                                               allow_bfill=allow_rolling_sums_backfill)
    # check here he saisics, how man such periods wihhou rades we had # ODO
    final_positions_df = build_positions_df_for_strats(Config,
                                                       final_pivot_df,chosen_cols_cond=chosen_cols_cond)
    final_positions_df,num_updated_rows = update_relevant_rows_in_positions_df(final_positions_df,existing_df,update_based_on_existing_file_anyway)
    if num_updated_rows > 0:
        if (base_on_existing or update_based_on_existing_file_anyway) and existing_df.shape[0]:
            archive_previous_positions_df(existing_df,outpath_suffix)
            pass
        final_positions_df.to_csv(outpath,index=False)
    else:
        print(f'No new rows to update in {outpath.replace(os.path.dirname(outpath),"")}')

if __name__ == '__main__':

    # for config in [SemiWeeklyConfig01x234,SemiWeeklyConfig234]:
    for config in [WeeklyConfig]:
        for last_date_for_bins in [
            dtdt(2024, 7, 1), dtdt(2024, 9, 1),
            dtdt(2024, 11, 1), dtdt(2025, 1, 1),
            dtdt(2025, 3, 1), dtdt(2025, 5, 1),
            dtdt(2030,1,1)
            ]:
            dt_suffix = last_date_for_bins.strftime('%Y%m%d') if last_date_for_bins < dtdt.now() else ''

            config.LAST_DATE_FOR_BINS = last_date_for_bins
            for use_segmenters in [True] if last_date_for_bins < dtdt.now() else [True, False]:
                main(base_on_existing=False,
                     use_segmenters=use_segmenters,
                     update_based_on_existing_file_anyway=False,
                     Config=config,
                     outpath_suffix=f'_{dt_suffix}' if dt_suffix else '',
                     # Config=WeeklyConfig
                     )
    # impute_profits_backfill()
    pass