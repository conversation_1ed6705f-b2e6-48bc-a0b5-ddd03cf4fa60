#!/usr/bin/env python
# coding: utf-8
import os.path
import time

from Algo.Notebooks.new_strategies_finder.production.run_config import DEBUG

from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
# import matplotlib
# from matplotlib import pyplot as plt
# import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

# matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
from Algo.Learning.performance_analysis import *
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
from Algo.Learning.predict_eps import add_eps_preds as add_eps_preds_rolling_corrs,add_eps_preds_from_gefs
from Algo.Utils.send_email import send_email_main
from tabulate import tabulate
from Algo.Utils.general import get_utc_now_dt

from Algo.Notebooks.new_strategies_finder.look_for_new_strategies_daily_preds import *
# from Algo.Notebooks.new_strategies_finder.definitions import * # Old by weekday - deprecated
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig as BaseConfig
from Algo.Notebooks.new_strategies_finder.filetr_conds_definitions import *

def _document_predictions(next_predictions,next_profit_date,look_up_window_days,strat,weekdays,file_suffix,d):
    buy_predictors = next_predictions.columns[next_predictions.iloc[0] > 0].tolist()
    sell_predictors = next_predictions.columns[next_predictions.iloc[0] < 0].tolist()
    df = pd.DataFrame(pd.Series({'date': next_profit_date, 'window': look_up_window_days, 'strat': strat,
                                 'weekday': weekdays[0],
                                 'file_suffix': file_suffix,
                                 'buy_predictors': buy_predictors, 'sell_predictors': sell_predictors,
                                 'updated_at': get_utc_now_dt()})).T
    subdir = os.path.join(d, 'chosen_preds_history')
    todays_date = get_utc_now_dt().strftime('%Y%m%d')
    outpath = os.path.join(subdir, f'chosen_preds_history_{strat}_{todays_date}.csv')
    if not os.path.exists(subdir):
        os.makedirs(subdir)
    if os.path.exists(outpath):
        old_preds_history = pd.read_csv(outpath, parse_dates=['date'],
                                        on_bad_lines='skip')
        df = pd.concat([old_preds_history, df])
    df.to_csv(outpath, index=False)

def wrap_look_for_strategies_slide_build(suffix='v8_0Zb', strat='y_0800-1315', weekdays=[0, 1, 2, 3, 4], ratio=0.5,
                                         test_start=BaseConfig.TEST_START,
                                         test_end=BaseConfig.FAR_FUTURE,
                                         backtest_start=BaseConfig.BACKTEST_START,
                                         search_cond=SEARCH_COND_0TO16_NO_6Z,
                                         num_chosen=NUM_CHOSEN,
                                         strat_for_profit=None,
                                         slide_gap=BaseConfig.SLIDE_GAP,
                                        look_up_window_days=135,
                                         file_suffix='',
                                        filters_good_preds_conf={'window1':60,'window2':30,'window_for_consecutive_wins':3},
                                         max_allowed_corr=0.9,
                                         allow_negative_signal=True,
                                         skip_existing=False,
                                         base_on_existing=True,
                                         weeks_back_if_base_on_existing=0,
                                         maximal_last_modified_date_if_base_on_existing=None,
                                        d = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest",
                                         debug=False,
                                         document_chosen_preds=False):
    """

    :param suffix:
    :param strat:
    :param weekdays:
    :param ratio:
    :param test_start:
    :param test_end:
    :param backtest_start:
    :param search_cond:
    :param num_chosen:
    :param strat_for_profit:
    :param slide_gap:
    :param look_up_window_days:
    :param file_suffix:
    :param filters_good_preds_conf:
    :param max_allowed_corr:
    :param allow_negative_signal:
    :param skip_existing:
    :param base_on_existing:
    :param weeks_back_if_base_on_existing: this should allow us to backfill 2-3 weeks if needed but still base on existing
    :param d:
    :return:
    """
    a0 = None

    if base_on_existing and skip_existing:
        raise ValueError('base_on_existing and skip_existing cannot be both True')
    os.makedirs(d,exist_ok=True)
    output_file = f'{d}\\look_for_strategies_slide_build_{suffix}_{strat}_weekday={weekdays}_start={test_start.strftime("%Y%m%d")}{file_suffix}.csv'
    if os.path.exists(output_file):
        last_modified_date = os.path.getmtime(output_file)
        last_modified_date = dtdt.fromtimestamp(last_modified_date)
        if skip_existing:
            print(f'File {output_file} already exists. Skipping')
            return
        elif maximal_last_modified_date_if_base_on_existing and (last_modified_date > maximal_last_modified_date_if_base_on_existing):
            print(f'File {output_file} already exists and is newer than {maximal_last_modified_date_if_base_on_existing}. Skipping')
            return
        else:
            if base_on_existing:
                original_df = pd.read_csv(output_file,parse_dates=['date'])
                original_df = original_df[list(original_df)[list(original_df).index('date'):]]
            else:
                original_df = pd.DataFrame()
    else:
        original_df = pd.DataFrame()

    # search_start = test_start
    if not original_df.empty:
        test_start = max(test_start,original_df.date.max()) - td(days=weeks_back_if_base_on_existing*7)
        print('File existed, test_start set to be %s'%test_start)

    a0 = load_xy_df(strat,suffix)
    a0 = a0[a0['date'].dt.weekday.isin([0,1,2,3,4])]
    try:
        last_data_point = min(a0.date.max(),get_utc_now_dt())
    except:
        print('INFO: Couldnt find last data point, setting it to 7 days ago')
        last_data_point = a0.date.max()
        # raise
    last_data_point = last_data_point.replace(hour=8,minute=0,second=0,microsecond=0)

    search_end = test_start # ATTENTION: correlations will search on data < this date (without it)
    search_start = search_end - td(days=look_up_window_days)  # 4.5 months
    stack = []

    while search_end < test_end and search_end <= last_data_point:
        if search_end.weekday() in [5,6] and slide_gap == 1: # means that the next day is Saturday or Sunday, skip
            search_end += td(days=1)
            search_start = search_end - td(days=look_up_window_days)
            continue
        elif search_end.weekday() not in weekdays:
            search_end += td(days=1)
            search_start = search_end - td(days=look_up_window_days)
            continue
        print(f'STARTEGY = {strat} and WEEKDAYS = {weekdays} || Working on {search_end} (which is {search_end.weekday()})' )
        try:
            a0, results, potentials_11to12, good_preds, bad_preds, good_preds_correlations = handle_scan(
            suffix=suffix, strat=strat, weekdays=weekdays, ratio=ratio,
                search_start=search_start, end_date=search_end,
                    backtest_start=backtest_start,
                        search_cond=search_cond, num_chosen=num_chosen,
                            strat_for_profit=strat_for_profit,
                                a0=a0,
            filter_good_preds_conf=filters_good_preds_conf,
            # max_allowed_corr=max_allowed_corr,
            max_allowed_corr=max_allowed_corr,
            allow_negative_signal=allow_negative_signal
            )
            stop = 1
        except Exception as e:
            print(f'Error in {search_end}')
            print(e)
            if debug:
                raise e
            search_end += td(days=1 * slide_gap)
            search_start = search_end - td(days=look_up_window_days)
            continue
        ### extract profit of the given day which is closest one after the search end
        # update some stacks
        # continue to next
        if len(potentials_11to12):
            try:
                trimmed_results = results[results['date'] <= search_end+td(days=slide_gap-1,hours=12)]
                #quantiles_by_pred = {}
                for c in good_preds:
                    pred = c.split(strat+'_')[-1]
                    #####
                        # could have calculated quantiles only on trimmes and save it for later for the next day, but not worth it
                        # quantiles = abs(trimmed_results[pred]).quantile([0.1*i for i in range(0,11)])
                        # trimmed_results[pred+'_prediction'] = np.sign(trimmed_results[pred]) * pd.cut(abs(trimmed_results[pred]),quantiles,labels=False)
                    #####
                    # for the sake of prediction bucket, when based on hundreds, it's not critical to avoid leakge
                    trimmed_results[pred + '_prediction'] = np.sign(trimmed_results[pred]) * pd.qcut(
                        abs(trimmed_results[pred]).rank(method='first'), 10,labels=False)
                # take good preds without the "today" row which is last one
                good_preds_recent_performance = trimmed_results.set_index('date').loc[:search_end][good_preds]#[:-1]  #todo - why was it here?
                good_preds_recent_performance = good_preds_recent_performance[good_preds_recent_performance.index.weekday.isin(weekdays)]# [-12*len(weekdays):] we filter later
                # look on last 3M
                EPSILON = 1 # tick

                kpis_dict = {}
                for num_weeks_for_recent in [12,24]:
                    recent_calmars = (good_preds_recent_performance[-num_weeks_for_recent * len(weekdays):]-EPSILON).apply(lambda x: calc_calmar(x,use_annual=False)[0])
                    recent_ppt = good_preds_recent_performance[-num_weeks_for_recent * len(weekdays):].replace(0,np.nan).mean()
                    recent_trades = good_preds_recent_performance[-num_weeks_for_recent * len(weekdays):].replace(0,np.nan).count()
                    recent_trades_q20, recent_trades_median, recent_trades_q80 = recent_trades.quantile(0.2), recent_trades.median(), recent_trades.quantile(0.8)
                    recent_calmars_q20, recent_calmars_median, recent_calmars_q80 = recent_calmars.quantile(0.2), recent_calmars.median(), recent_calmars.quantile(0.8)
                    recent_ppt_q20, recent_ppt_median, recent_ppt_q80 = recent_ppt.quantile(0.2), recent_ppt.median(), recent_ppt.quantile(0.8)
                    kpis_dict.update({
                        # f'recent_calmars_{num_weeks_for_recent}':recent_calmars,
                        # f'recent_ppt_{num_weeks_for_recent}':recent_ppt,
                        # f'recent_trades_{num_weeks_for_recent}':recent_trades,
                        # f'recent_trades_q20_{num_weeks_for_recent}':recent_trades_q20,
                        # f'recent_trades_median_{num_weeks_for_recent}':recent_trades_median,
                        # f'recent_trades_q80_{num_weeks_for_recent}':recent_trades_q80,
                        f'recent_calmars_q20_{num_weeks_for_recent}':recent_calmars_q20,
                        f'recent_calmars_median_{num_weeks_for_recent}':recent_calmars_median,
                        f'recent_calmars_q80_{num_weeks_for_recent}':recent_calmars_q80,
                        f'recent_ppt_q20_{num_weeks_for_recent}':recent_ppt_q20,
                        f'recent_ppt_median_{num_weeks_for_recent}':recent_ppt_median,
                        f'recent_ppt_q80_{num_weeks_for_recent}':recent_ppt_q80,
                    })
                # next_day_results =
                next_predictions = trimmed_results[(trimmed_results.date.dt.weekday.isin(weekdays))& (trimmed_results.date >= search_end)][[x for x in list(trimmed_results) if x.endswith('prediction')]]
                next_predictions[abs(next_predictions)<ratio*10] = 0
                next_profits = results[(results['date'].dt.weekday.isin(weekdays)) & (results['date'] >= search_end)].iloc[0].replace(0,np.nan)

                num_buy_preds = (next_predictions > 0).iloc[0].sum()
                num_sell_preds = (next_predictions < 0).iloc[0].sum()
                next_profit_voting = next_profits[good_preds].median() if num_buy_preds != num_sell_preds else 0
                next_profit_weighted = next_profits[good_preds]
                next_profit_mean = next_profits[good_preds].mean()
                next_profit_sum = next_profits[good_preds].sum()
                num_active_preds = num_buy_preds + num_sell_preds #next_profits[good_preds].replace(0,np.nan).count()

                good_preds_correlations.index = [x.replace(f'profit_{strat}_','')+'_prediction' for x in good_preds_correlations.index]
                corr_weighted_pred = np.sum(next_predictions.iloc[0] * good_preds_correlations)/good_preds_correlations.sum()
                naive_avg_pred = np.mean(next_predictions.iloc[0])
                next_profit_date = next_profits.date
                if document_chosen_preds:
                    try:
                        _document_predictions(next_predictions,next_profit_date,
                                            look_up_window_days,strat,
                                                weekdays,file_suffix,d)
                    except Exception as e:
                        print(f'Error in document predictions for {search_end}: {e}')
                        if debug:
                            raise e

                stack.append({'date':next_profit_date,'next_profit_voting':next_profit_voting,
                              'next_profit_mean':next_profit_mean,
                              'next_profit_sum':next_profit_sum,
                              'num_active_preds':num_active_preds,
                              'num_buy_preds': num_buy_preds,
                              'num_sell_preds': num_sell_preds,
                              'num_good_preds':len(good_preds),
                              'num_bad_preds':len(bad_preds),
                                'avg_pred': naive_avg_pred,
                                'corr_weighted_pred':corr_weighted_pred,
                            **kpis_dict
                              })
            except Exception as e:
                print(f'Error in calculation for search_end = {search_end}: {e}')
                if debug:
                    raise e
        else:
            print('Warning: No potentials found for this date. will add Nans to the stack')
            stack.append({'date': search_end, 'next_profit_voting': np.nan,
                            'next_profit_mean': np.nan,
                            'next_profit_sum': np.nan,
                            'num_active_preds': 0,
                            'num_buy_preds': 0,
                            'num_sell_preds': 0,
                            'num_good_preds': 0,
                            'num_bad_preds': 0,
                            'avg_pred': np.nan,
                            'corr_weighted_pred': np.nan,
                            **{f'recent_calmars_q20_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            **{f'recent_calmars_median_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            **{f'recent_calmars_q80_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            **{f'recent_ppt_q20_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            **{f'recent_ppt_median_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            **{f'recent_ppt_q80_{num_weeks_for_recent}':np.nan for num_weeks_for_recent in [12,24]},
                            })

        search_end += td(days=1*slide_gap)
        search_start = search_end - td(days=look_up_window_days)

    stack_df = pd.DataFrame(stack)
    if base_on_existing and original_df.shape[0] > 0:
        print(f'original_df is of shape {original_df.shape} and stack_df is of shape {stack_df.shape}')
        if stack_df.shape[0] > 0:
            # drop duplicate dates, leaving the new one
            original_df = original_df[~original_df['date'].isin(stack_df['date'].tolist())]
            if list(stack_df) == list(original_df):
                stack_df = pd.concat([original_df,stack_df]).drop_duplicates('date')
            else:
                common = list(set(stack_df) & set(original_df))
                stack_df = pd.merge(stack_df,original_df,on=common,how='outer')
        else:
            stack_df = original_df
        print(f'After concatenation, stack_df is of shape {stack_df.shape}')
    stack_df.to_csv(output_file,index=False)

def main(skip_existing = False,
            weekdays_override=None,
         base_on_existing=True,
         weeks_back_if_base_on_existing=0,
         debug=False,
         maximal_last_modified_date_if_base_on_existing=None,
         document_chosen_preds=False,
         file_suffix='',
         Config=BaseConfig
         ):
    outdir = Config.BACKTEST_OUTDIR
    test_start = Config.TEST_START
    allow_negative_signal = Config.ALLOW_NEGATIVE_SIGNAL
    strategies_list = Config.PRODUCTION_STRATS
    signal_filter_ratio = Config.SIGNAL_FILTER_RATIO
    num_chosen = Config.NUM_CHOSEN
    max_autocorrelaiton = Config.MAX_AUTOCORRELATION
    look_up_windows_list = Config.LOOKUP_WINDOWS
    calmar_thresholds_list = Config.CALMAR_THRESHOLDS_LIST
    ppt_thresholds_list = Config.PPT_THRESHOLDS_LIST
    predictors_filter_cond = Config.PREDICTORS_FILTER_COND
    weekdays = Config.WEEKDAYS

    if weekdays_override is not None:
        weekdays = weekdays_override
    try:
        test_end = Config.TEST_END
    except:
        test_end = dtdt.now() + td(days=1)

    for min_calmar in calmar_thresholds_list:
        for min_ppt in ppt_thresholds_list:  # 0 seemed to be worse than 10 significantly.
            # if at all need to scan 20/30 to see if more improvement shows up
            for look_up_window in look_up_windows_list:
                conf = Config.FILTERS_GOOD_PREDS_CONF1.copy()
                conf['min_calmar_w1'] = min_calmar
                conf['min_calmar_w2'] = min_calmar
                conf['min_ppt_w1'] = min_ppt
                suffix2 = f'_{look_up_window}_v2_calmar={min_calmar}_ppt={min_ppt}{file_suffix}'
                if not allow_negative_signal:
                    suffix2 += '_noNeg'

                for strat in strategies_list:
                    strat_hour = int(strat.split('_')[1].split('-')[0][:2])
                    strat_minute = int(strat.split('_')[1].split('-')[0][-2:])
                    cond = predictors_filter_cond
                    if strat_hour < 6:
                        cond = lambda x: predictors_filter_cond(x) and SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_NO_06Z(x)
                    elif 6 <= strat_hour < 8:
                        cond = lambda x: predictors_filter_cond(x) and SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_6UTC(x)
                    elif 8 <= strat_hour < 11:
                        cond = lambda x: predictors_filter_cond(x) and SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_NO_6Z(x)
                    elif strat_hour == 11:
                        cond = lambda x: predictors_filter_cond(x) and SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_ONLY_GFS_6Z(x)
                    elif strat_hour == 12:
                        cond = lambda x: predictors_filter_cond(x) and SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL_NO_CFS_6Z(x)

                    if isinstance(weekdays[0],int):
                        weekdays_list = [[w] for w in weekdays]
                    elif isinstance(weekdays[0],list):
                        weekdays_list = weekdays
                    else:
                        raise ValueError('weekdays should be list of lists or list of ints')
                    for weekdays_subset in weekdays_list:
                        if len(weekdays_subset) == 1:
                            slide_gap = 7
                        else:
                            slide_gap = 1
                        if strat in ['y_1745-1845', 'y_1745-1945', 'y_1845-1945', 'y_1945-2345']:
                            suffixes = ['v8_0Zb', 'v8_12Z']
                        elif strat in ['y_0000-0600_+1d']:
                            suffixes = ['v8_12Z']
                        else:
                            suffixes = ['v8_0Zb']

                        for suffix in suffixes:

                            try:
                                wrap_look_for_strategies_slide_build(suffix=suffix, strat=strat, weekdays=weekdays_subset,
                                                                 ratio=signal_filter_ratio,
                                                                 test_start=test_start,
                                                                 test_end=test_end,
                                                                 backtest_start=Config.BACKTEST_START,
                                                                 search_cond=cond,
                                                                 num_chosen=num_chosen,
                                                                 strat_for_profit=None,
                                                                 slide_gap=slide_gap,
                                                                 filters_good_preds_conf=conf,
                                                                 file_suffix=suffix2,
                                                                 look_up_window_days=look_up_window,
                                                                 skip_existing=skip_existing,
                                                                 d=outdir,
                                                                 max_allowed_corr=max_autocorrelaiton,
                                                                 base_on_existing=base_on_existing,
                                                                 weeks_back_if_base_on_existing=weeks_back_if_base_on_existing,
                                                                 debug=debug,
                                                                 document_chosen_preds=document_chosen_preds,
                                                                 maximal_last_modified_date_if_base_on_existing=maximal_last_modified_date_if_base_on_existing,
                                                                 allow_negative_signal=allow_negative_signal
                                                                 )
                            except Exception as e:
                                print(f'Error in {strat} and {weekdays_subset} and {suffix} and {suffix2} and {look_up_window} and {conf}')
                                print(e)
                                if debug:
                                    raise e
                                continue

if __name__ == '__main__':
    # main(base_on_existing=False,weekdays=[0,1,2,3,4],
    #      strategies_list=NIGHT_STRATS,
    #      predictors_filter_cond=SEARCH_COND_0TO16_NO_06Z)
    main(base_on_existing=False,
         # weekdays=[0,1,2,3,4],
         weekdays_override=[0,1,2,3,4],
         skip_existing=False,
         strategies_list=['y_0600-0800','y_0400-0600',
                          'y_0700-0800','y_0400-0800'][2:],
         # weeks_back_if_base_on_existing=1,
         # predictors_filter_cond=lambda x: SEARCH_COND_0TO16_NO_6Z(x),
            debug=False,
            document_chosen_preds=False,
         # maximal_last_modified_date_if_base_on_existing=dtdt(2024,11,1)
            )
    # add condition by file creation date to efficiently continue
