#!/usr/bin/env python
# coding: utf-8
import time

from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
from Algo.Learning.performance_analysis import *
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
from Algo.Learning.predict_eps import add_eps_preds as add_eps_preds_rolling_corrs,add_eps_preds_from_gefs
from Algo.Utils.send_email import send_email_main
from tabulate import tabulate
from Algo.Utils.general import get_utc_now_dt

from Algo.Notebooks.new_strategies_finder.look_for_new_strategies_daily_preds import *
from Algo.Notebooks.new_strategies_finder.definitions import *


def wrap_look_for_strategies_slide_build(suffix='v8_0Zb', strat='y_0800-1315', weekdays=[0, 1, 2, 3, 4], ratio=0.5,
                                         test_start=TEST_START,
                                         test_end=FAR_FUTURE,
                                         backtest_start=BACKTEST_START,
                                         search_cond=SEARCH_COND_0TO16_NO_6Z,
                                         num_chosen=NUM_CHOSEN,
                                         strat_for_profit=None,
                                         slide_gap=SLIDE_GAP,
                                        look_up_window_days=135,
                                         file_suffix='',
                                        filters_good_preds_conf={'window1':60,'window2':30,'window_for_consecutive_wins':3},
                                         max_allowed_corr=0.95,
                                         allow_negative_signal=True,
                                         skip_existing=False,
                                         base_on_existing=True,
                                        d = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\look_for_new_strategies_backtest"
                                         ):
    a0 = None

    if base_on_existing and skip_existing:
        raise ValueError('base_on_existing and skip_existing cannot be both True')

    output_file = f'{d}\\look_for_strategies_slide_build_{suffix}_{strat}_weekday={weekdays}_start={test_start.strftime("%Y%m%d")}{file_suffix}.csv'
    if os.path.exists(output_file):
        if skip_existing:
            print(f'File {output_file} already exists. Skipping')
            return
        else:
            original_df = pd.read_csv(output_file,parse_dates=['date'])
            original_df = original_df[list(original_df)[list(original_df).index('date'):]]
    else:
        original_df = pd.DataFrame()

    # search_start = test_start
    if not original_df.empty:
        test_start = max(test_start,original_df.date.max())
        print('File existed, test_start set to be %s'%test_start)

    a0 = load_xy_df(strat,suffix)
    last_data_point = min(a0.date.max(),get_utc_now_dt())
    last_data_point = last_data_point.replace(hour=8,minute=0,second=0,microsecond=0)

    search_end = test_start
    search_start = search_end - td(days=look_up_window_days)  # 4.5 months
    stack = []

    while search_end < test_end and search_end < last_data_point:
        print(f'STARTEGY = {strat} and WEEKDAYS = {weekdays} || Working on {search_end} ' )
        try:
            a0, results, potentials_11to12, good_preds, bad_preds = handle_scan(
            suffix=suffix, strat=strat, weekdays=weekdays, ratio=ratio,
                search_start=search_start, end_date=search_end,
                    backtest_start=backtest_start,
                        search_cond=search_cond, num_chosen=num_chosen,
                            strat_for_profit=strat_for_profit,
                                a0=a0,
            filter_good_preds_conf=filters_good_preds_conf,
            max_allowed_corr=max_allowed_corr,
            allow_negative_signal=allow_negative_signal
            )
        except Exception as e:
            print(f'Error in {search_end}')
            print(e)
            search_end += td(days=1 * slide_gap)
            search_start = search_end - td(days=look_up_window_days)
            continue
        ### extract profit of the given day which is closest one after the search end
        # update some stacks
        # continue to next
        try:
            trimmed_results = results[results['date'] <= search_end+td(days=slide_gap-1,hours=12)]
            for c in good_preds:
                pred = c.split(strat+'_')[-1]
                trimmed_results[pred+'_prediction'] = np.sign(trimmed_results[pred]) * pd.qcut(abs(trimmed_results[pred]).rank(method='first'), 10,
                                                                   labels=False)

            # take good preds without the "today" row which is last one
            good_preds_recent_performance = trimmed_results.set_index('date')[good_preds][:-1]
            good_preds_recent_performance = good_preds_recent_performance[good_preds_recent_performance.index.weekday.isin(weekdays)][-12:]
            # look on last 3M
            recent_calmars = good_preds_recent_performance[-13:].apply(lambda x: calc_calmar(x,use_annual=False)[0])
            recent_trades = good_preds_recent_performance[-13:].replace(0,np.nan).count()
            recent_trades_q20, recent_trades_median, recent_trades_q80 = recent_trades.quantile(0.2), recent_trades.median(), recent_trades.quantile(0.8)
            recent_calmars_q20, recent_calmars_median, recent_calmars_q80 = recent_calmars.quantile(0.2), recent_calmars.median(), recent_calmars.quantile(0.8)

            next_predictions = trimmed_results[(trimmed_results.date.dt.weekday.isin(weekdays))& (trimmed_results.date >= search_end)][[x for x in list(trimmed_results) if x.endswith('prediction')]]
            next_predictions[abs(next_predictions)<ratio*10] = 0
            next_profits = results[(results['date'].dt.weekday.isin(weekdays)) & (results['date'] >= search_end)].iloc[0].replace(0,np.nan)

            num_buy_preds = (next_predictions > 0).iloc[0].sum()
            num_sell_preds = (next_predictions < 0).iloc[0].sum()
            next_profit_voting = next_profits[good_preds].median() if num_buy_preds != num_sell_preds else 0
            next_profit_mean = next_profits[good_preds].mean()
            next_profit_sum = next_profits[good_preds].sum()
            num_active_preds = next_profits[good_preds].replace(0,np.nan).count()
            next_profit_date = next_profits.date

            stack.append({'date':next_profit_date,'next_profit_voting':next_profit_voting,
                          'next_profit_mean':next_profit_mean,
                          'next_profit_sum':next_profit_sum,
                          'num_active_preds':num_active_preds,
                          'num_buy_preds': num_buy_preds,
                          'num_sell_preds': num_sell_preds,
                          'num_good_preds':len(good_preds),
                          'num_bad_preds':len(bad_preds),
                            'recent_calmars_q20':recent_calmars_q20,
                            'recent_calmars_median':recent_calmars_median,
                            'recent_calmars_q80':recent_calmars_q80,
                            'recent_trades_q20':recent_trades_q20,
                            'recent_trades_median':recent_trades_median,
                            'recent_trades_q80':recent_trades_q80,
                          })
        except Exception as e:
            print(f'Error in calculation for search_end = {search_end}: {e}')
        search_end += td(days=1*slide_gap)
        search_start = search_end - td(days=look_up_window_days)

    stack_df = pd.DataFrame(stack)
    if base_on_existing and original_df.shape[0] > 0:
        print(f'original_df is of shape {original_df.shape} and stack_df is of shape {stack_df.shape}')
        if stack_df.shape[0] > 0:
            # drop duplicate dates, leaving the new one
            original_df = original_df[~original_df['date'].isin(stack_df['date'].tolist())]
            stack_df = pd.concat([original_df,stack_df]).drop_duplicates('date')
        else:
            stack_df = original_df
        print(f'After concatenation, stack_df is of shape {stack_df.shape}')
    stack_df.to_csv(output_file,index=False)

def main(skip_existing = False,outdir = BACKTEST_OUTDIR,
            weekdays=[0, 1, 2, 3, 4],
            test_start=TEST_START,
            predictors_filter_cond=SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL,
         allow_negative_signal=ALLOW_NEGATIVE_SIGNAL):

    for min_calmar in CALMAR_THRESHOLDS_LIST:
        for min_ppt in PPT_THRESHOLDS_LIST:  # 0 seemed to be worse than 10 significantly.
            # if at all need to scan 20/30 to see if more improvement shows up
            for look_up_window in LOOKUP_WINDOWS:
                conf = FILTERS_GOOD_PREDS_CONF1.copy()
                conf['min_calmar_w1'] = min_calmar
                conf['min_calmar_w2'] = min_calmar
                conf['min_ppt_w1'] = min_ppt
                suffix2 = f'_{look_up_window}_v2_calmar={min_calmar}_ppt={min_ppt}'
                if not allow_negative_signal:
                    suffix2 += '_noNeg'

                for strat in PRODUCTION_STRATS:
                    cond = predictors_filter_cond
                    if int(strat.split('_')[1].split('-')[0][:2]) == 11:
                        cond = lambda x: predictors_filter_cond(x) and COND_ONLY_GFS_6Z(x)
                    elif int(strat.split('_')[1].split('-')[0][:2]) < 11:
                        cond = lambda x: predictors_filter_cond(x) and COND_NO_6Z(x)


                    for weekdays in [[w] for w in weekdays]:
                        if strat in ['y_1745-1845', 'y_1745-1945', 'y_1845-1945', 'y_1945-2345']:
                            suffixes = ['v8_0Zb', 'v8_12Z']
                        elif strat in ['y_0000-0600_+1d']:
                            suffixes = ['v8_12Z']
                        else:
                            suffixes = ['v8_0Zb']

                        for suffix in suffixes:
                            wrap_look_for_strategies_slide_build(suffix=suffix, strat=strat, weekdays=weekdays,
                                                                 ratio=SIGNAL_FILTER_RATIO,
                                                                 test_start=test_start,
                                                                 test_end=dtdt.now() + td(days=1),
                                                                 backtest_start=BACKTEST_START,
                                                                 search_cond=cond,
                                                                 num_chosen=NUM_CHOSEN,
                                                                 strat_for_profit=None,
                                                                 slide_gap=SLIDE_GAP,
                                                                 filters_good_preds_conf=conf,
                                                                 file_suffix=suffix2,
                                                                 look_up_window_days=look_up_window,
                                                                 skip_existing=skip_existing,
                                                                 d=outdir,
                                                                 )

if __name__ == '__main__':
    main()
