import os.path
from datetime import datetime as dtdt
from Algo.Utils.files_handle import HOME
from Algo.Notebooks.new_strategies_finder.filetr_conds_definitions import SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL,BASE_MODELS_ONLY_COND

LOOKUP_WINDOWS = [30, 60, 90, 120, 180]

class BaseConfig:
    NAME = 'base'
    # Date Ranges
    BACKTEST_START = dtdt(2022, 1, 1)
    TEST_START = dtdt(2024, 1, 1)
    FAR_FUTURE = dtdt(2050, 1, 31)
    FAR_PAST = dtdt(2020, 1, 31)

    POSITIONS_FILE_SUFFIX_FOR_SEGMENT_BINS = ''

    # number of predictions to keep in the correlation stage
    NUM_CHOSEN = 50
    # dropping too similar preds
    MAX_AUTOCORRELATION = 0.9

    PREDICTORS_FILTER_COND = SEARCH_COND_GENERAL_NO8to16_PLUS_SEASONAL

    # Strategy Parameters
    PRODUCTION_STRATS = [
        'y_0630-1230', 'y_1230-1830',
        'y_1100-1230', 'y_1200-1415',
        'y_1000-1200',
        # 'y_1200-1300', # decided to drop it for now due to really bad performance for severla months across all weekdays
        'y_1400-1500'
    ]

    EARLY_MORNING_STRATS = [
        'y_0600-0800',
    ]

    NIGHT_STRATS = [
        'y_0000-0400', 'y_0400-0600', 'y_0000-0600',
    ]

    PRODUCTION_STRATS_0Z = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) <= 11]
    PRODUCTION_STRATS_6Z_EARLY = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) == 11]
    PRODUCTION_STRATS_6Z_MID = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) in [11, 12]]
    PRODUCTION_STRATS_6Z_LATE = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) >= 12]
    PRODUCTION_STRATS_12Z = []

    LOOKUP_WINDOWS = LOOKUP_WINDOWS
    WINDOWS_LIST_GROUP1 = [30, 60, 90]
    WINDOWS_LIST_GROUP2 = [120, 180]

    WINDOWS_LIST_GROUP1A = [70, 60]
    WINDOWS_LIST_GROUP2A = [90, 120]
    WINDOWS_LIST_GROUP3A = [180]

    SIGNAL_FILTER_RATIO = 0.5

    PPT_THRESHOLDS_LIST = [10]
    CALMAR_THRESHOLDS_LIST = [10]
    ALLOW_NEGATIVE_SIGNAL = False
    LAST_DATE_FOR_BINS = dtdt(2030, 1, 1)


class WeeklyConfig(BaseConfig):
    """Custom configuration inheriting from BaseConfig"""

    NAME = 'weekly'

    # Paths
    BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', 'look_for_new_strategies_backtest_v4')
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', 'look_for_new_strategies_positions_NG_v4.csv')
    CSVS_TEMPLATE = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20240101$SUFFIX.csv"


    POSITIONS_FILE_SUFFIX_FOR_SEGMENT_BINS = '_endDate=20240701' # TODO make it dynamically update by calculation as in examine_bins_end_dates

    SKIP_DAILY_PREDS_BACKTEST = False

    # backtest params
    SLIDE_GAP = 1

    WEEKDAYS = [[0, 1, 2, 3, 4]]

    # Strategy Parameters
    PRODUCTION_STRATS = [
        'y_0630-1230', 'y_1230-1830',
        'y_1100-1230', 'y_1200-1415',
        'y_1000-1200',
        # 'y_1200-1300', # decided to drop it for now due to really bad performance for severla months across all weekdays
        'y_1400-1500',
        'y_1400-1645', # New strategy from 05/2025
    ]
    PRODUCTION_STRATS_0Z = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) <= 11]
    PRODUCTION_STRATS_6Z_EARLY = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) == 11]
    PRODUCTION_STRATS_6Z_MID = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) in [11,12]]
    PRODUCTION_STRATS_6Z_LATE = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) >= 12]

    PRODUCTION_STRATS_12Z = []

    # Forbidden Buckets
    FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW = {
        'num_active_preds': [],
        'recent_calmars_median_12': [8, 9, 10],
        'recent_calmars_q20_12': [9, 10],
        'recent_calmars_q80_12': [9, 10],
        'recent_median_12': [],
        'recent_ppt_q20_12': [],
        'recent_ppt_median_12': [],
        'recent_ppt_q80_12': [],
        'recent_calmars_median_24': [],
        'recent_calmars_q20_24': [],
        'recent_calmars_q80_24': [],
        'recent_trades_24': [],
        'recent_ppt_q20_24': [],
        'recent_ppt_median_24': [],
        'recent_ppt_q80_24': [],
        'active_preds_ratio': [],
        'imbalance_ratio': [1, 2, 3],
        'good_preds_ratio': [9, 10],
    }
    WINNERS_BY_STRATEGY = {
                        'y_1200-1415':'winner_w180',
                      'y_0630-1230':'winner_w30',
                      'y_1230-1830':'winner_w120',
                      'y_1100-1230':'winner_w30',
                      'y_1545-1745':'winner_w40',
                      'y_1415-1545':'winner_w120',
                      'y_1000-1200':'winner_w180',
                      'y_1200-1300':'winner_w90',
                      'y_1400-1500':'winner_w50',
                        'y_1400-1645':'winner_w50',}


    # Filters and Thresholds
    FILTERS_GOOD_PREDS_CONF1 = {'window1': 90, 'window2': 60,
                                'window_for_consecutive_wins': 5}


class SemiWeeklyConfig234(WeeklyConfig):
    """
    Configuration for semi weekly calculation - based on wed/thu/fri

    """
    NAME = 'semi234'
    # Paths
    BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', 'look_for_new_strategies_backtest_v4')
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', f'look_for_new_strategies_positions_NG_v4_{NAME}.csv')

    SKIP_DAILY_PREDS_BACKTEST = False

    # backtest params
    SLIDE_GAP = 1
    # weekdays
    WEEKDAYS = [[2, 3, 4]]

    # Strategy Parameters
    PRODUCTION_STRATS = [
        'y_0630-1230', 'y_1230-1830',
        'y_1100-1230', 'y_1200-1415',
        'y_1000-1200', 'y_1200-1300', 'y_1400-1500'
    ]
    PRODUCTION_STRATS_0Z = [strat for strat in PRODUCTION_STRATS if int(strat.split('_')[1].split('-')[0][:2]) <= 11]
    PRODUCTION_STRATS_6Z_EARLY = [strat for strat in PRODUCTION_STRATS if
                                  int(strat.split('_')[1].split('-')[0][:2]) == 11]
    PRODUCTION_STRATS_6Z_MID = [strat for strat in PRODUCTION_STRATS if
                                int(strat.split('_')[1].split('-')[0][:2]) in [11, 12]]
    PRODUCTION_STRATS_6Z_LATE = [strat for strat in PRODUCTION_STRATS if
                                 int(strat.split('_')[1].split('-')[0][:2]) >= 12]

    # Forbidden Buckets
    FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW = {  # 6 bins
        'num_active_preds': [],

        'recent_calmars_median_12': [9, 10],
        'recent_calmars_q20_12': [9, 10],
        'recent_calmars_q80_12': [9, 10],

        'recent_ppt_q20_12': [],
        'recent_ppt_median_12': [],
        'recent_ppt_q80_12': [],
        'recent_calmars_median_24': [],
        'recent_calmars_q20_24': [],
        'recent_calmars_q80_24': [],

        'recent_ppt_q20_24': [],
        'recent_ppt_median_24': [],
        'recent_ppt_q80_24': [],

        'active_preds_ratio': [],
        'imbalance_ratio': [1, 2, 3, 4, 5],
        'good_preds_ratio': [9, 10]
    }

    WINNERS_BY_STRATEGY = {
        'y_1200-1415': 'winner_w120',
        'y_0630-1230': 'winner_w150',
        'y_1230-1830': 'winner_w120',
        'y_1100-1230': 'winner_w120',
        'y_1000-1200': 'winner_w120',
        'y_1200-1300': 'winner_w50',
        'y_1400-1500': 'winner_w120',

    }

    # Filters and Thresholds
    FILTERS_GOOD_PREDS_CONF1 = {'window1': 90, 'window2': 60,
                                'window_for_consecutive_wins': 5}

class SemiWeeklyConfig01(SemiWeeklyConfig234):
    NAME = 'semi01'
    WEEKDAYS = [[0,1]]
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', f'look_for_new_strategies_positions_NG_v4_{NAME}.csv')

class SemiWeeklyConfig01x234(SemiWeeklyConfig234):
    NAME = 'semi01x234'
    WEEKDAYS = [[0,1],[2,3,4]]

    SKIP_DAILY_PREDS_BACKTEST = True
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', f'look_for_new_strategies_positions_NG_v4_{NAME}.csv')

#### Auxiliary Configs for backfill
class WeeklyConfigScan(WeeklyConfig):
    TEST_START = dtdt(2024, 1, 1)
    TEST_END = dtdt(2030, 1, 1)

    NUM_CHOSEN = 36
    # dropping too similar preds
    MAX_AUTOCORRELATION = 0.92


    BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', 'look_for_new_strategies_backtest_v4_scan')
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', 'look_for_new_strategies_positions_NG_v4_Scan.csv')
    CSVS_TEMPLATE = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20230701$SUFFIX_ext.csv"
    PRODUCTION_STRATS = ['y_0800-0900','y_0900-1000','y_1000-1100','y_1100-1200','y_1200-1300','y_1300-1400','y_1400-1500',
                         'y_1515-1615','y_1615-1715','y_1715-1815','y_1815-1915','y_1915-2015',]
    ALLOW_NEGATIVE_SIGNAL = True
    PREDICTORS_FILTER_COND = BASE_MODELS_ONLY_COND

class SemiWeeklyConfigScan(WeeklyConfig):
    TEST_START = dtdt(2024, 1, 1)
    TEST_END = dtdt(2030, 1, 1)

    NUM_CHOSEN = 36
    # dropping too similar preds
    MAX_AUTOCORRELATION = 0.92

    WEEKDAYS = [[0,1],[2,3,4]]

    BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', 'look_for_new_strategies_backtest_v4_scan')
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', 'look_for_new_strategies_positions_NG_v4_Scan.csv')
    CSVS_TEMPLATE = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20230701$SUFFIX_ext.csv"
    PRODUCTION_STRATS = ['y_0800-0900','y_0900-1000','y_1000-1100','y_1100-1200','y_1200-1300','y_1300-1400','y_1400-1500',
                         'y_1515-1615','y_1615-1715','y_1715-1815','y_1815-1915','y_1915-2015',]
    ALLOW_NEGATIVE_SIGNAL = True
    PREDICTORS_FILTER_COND = BASE_MODELS_ONLY_COND

class DailyConfigScan(WeeklyConfig):
    TEST_START = dtdt(2024, 1, 1)
    TEST_END = dtdt(2030, 1, 1)

    NUM_CHOSEN = 36
    # dropping too similar preds
    MAX_AUTOCORRELATION = 0.92

    WEEKDAYS = [[0,1,2,3,4]]

    BACKTEST_OUTDIR = os.path.join(HOME, 'performance_analysis', 'look_for_new_strategies_backtest_v4_scan')
    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', 'look_for_new_strategies_positions_NG_v4_Scan.csv')
    CSVS_TEMPLATE = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20230701$SUFFIX_ext.csv"
    PRODUCTION_STRATS = ['y_0800-0900','y_0900-1000','y_1000-1100','y_1100-1200','y_1200-1300','y_1300-1400','y_1400-1500',
                         'y_1515-1615','y_1615-1715','y_1715-1815','y_1815-1915','y_1915-2015',]
    ALLOW_NEGATIVE_SIGNAL = True
    PREDICTORS_FILTER_COND = BASE_MODELS_ONLY_COND

class WeeklyConfig2023a(WeeklyConfig):
    TEST_START = dtdt(2022, 1, 1)
    TEST_END = dtdt(2024, 1, 1)

    POSITIONS_FILE_PATH = os.path.join(HOME, 'Trading', 'look_for_new_strategies_positions_NG_v4_2022.csv')
    CSVS_TEMPLATE = "look_for_strategies_slide_build_v8_0Zb_$STRAT_weekday=$DAY_start=20220101$SUFFIX_ext.csv"
    SKIP_DAILY_PREDS_BACKTEST = False
    PRODUCTION_STRATS = [
        'y_0630-1230', 'y_1230-1830',
        'y_1100-1230', 'y_1200-1415',
        'y_1000-1200','y_1200-1300', 'y_1400-1500'
        ]

    # Forbidden Buckets
    FORBIDDEN_BUCKETS_BY_SEGMENTER_NEW = {  # 6 bins
        'num_active_preds': [],

        'recent_calmars_median_12': [8,9, 10],
        'recent_calmars_q20_12': [9, 10],
        'recent_calmars_q80_12': [9, 10],

        'recent_ppt_q20_12': [],
        'recent_ppt_median_12': [],
        'recent_ppt_q80_12': [],
        'recent_calmars_median_24': [],
        'recent_calmars_q20_24': [],
        'recent_calmars_q80_24': [],

        'recent_ppt_q20_24': [],
        'recent_ppt_median_24': [],
        'recent_ppt_q80_24': [],

        'active_preds_ratio': [],
        'imbalance_ratio': [1, 2, 3],
        'good_preds_ratio': [9, 10]
    }

    WINNERS_BY_STRATEGY = {
                        'y_1200-1415':'winner_w30',
                      'y_0630-1230':'winner_w120',
                      'y_1230-1830':'winner_w100',
                      'y_1100-1230':'winner_w120',
                      'y_1545-1745':'winner_w40',
                      'y_1415-1545':'winner_w120',
                      'y_1000-1200':'winner_w40',
                      'y_1200-1300':'winner_w180',
                      'y_1400-1500':'winner_w180',
        }

class WeeklyConfig2023b(WeeklyConfig):
    TEST_START = dtdt(2022, 1, 1)
    TEST_END = dtdt(2024, 1, 1)

    PRODUCTION_STRATS = [
        # 'y_0630-1230', 'y_1230-1830',
        # 'y_1100-1230', 'y_1200-1415',
        'y_1000-1200', 'y_1200-1300', 'y_1400-1500'
    ]


# Usage
if __name__ == "__main__":
    base_config = BaseConfig()
