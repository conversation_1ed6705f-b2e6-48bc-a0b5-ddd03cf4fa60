from Algo.Notebooks.new_strategies_finder.look_for_new_strategies_daily_preds import *

ref_dt = dtdt.now() + td(days=-7 * 3 - 1)
weekday = 0
num_days = 180

conf = {'window1': 90, 'window2': 75, 'window_for_consecutive_wins': 5}

for strat in [
                 # 'y_0000-0600',
                 'y_0800-1100', 'y_1100-1315',
                 'y_0800-1845', 'y_1200-1745'][-1:]:
    main(suffix='v8_0Zb', strat=strat,
         # weekdays=[dtdt.now().weekday()],
         weekdays=[weekday],
         ratio=0.5,
         search_start=ref_dt - td(days=num_days),
         end_date=ref_dt,
         backtest_start=dtdt(2022, 1, 1),
         # search_cond=SEARCH_COND_0TO16_NO_6Z,
         search_cond=SEARCH_COND_GENERAL_NO8to16,
         num_chosen=NUM_CHOSEN,
         # strat_for_profit = 'y_1545-1745',
         strat_for_profit=None,
         plot=True,
         filter_good_preds_conf=conf,
         add_strategy_profit_to_plot=True,

         )