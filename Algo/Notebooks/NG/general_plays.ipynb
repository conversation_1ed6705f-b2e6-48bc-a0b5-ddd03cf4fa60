#%%
from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v4
from Algo.Performance_Analysis.clusters_auto_selection import check_drop_losing_hours,check_performance_by_hour,check_performance_by_mode
from Algo.Performance_Analysis.clusters_auto_selection import *


import copy

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,sortino_ratio,sortino_ratio_v2, calc_sharpe_v2,get_drawdown_vector
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT,DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR
from Algo.Learning.research_plays import rolling_calmar
from matplotlib import pyplot as plt

%load_ext autoreloadd
%autoreload 2
#%%
from Algo.Performance_Analysis.clusters_auto_selection import handle_config_v4
from Algo.Performance_Analysis.clusters_auto_selection import check_drop_losing_hours,check_performance_by_hour,check_performance_by_mode
from Algo.Performance_Analysis.clusters_auto_selection import *


import copy

import pandas as pd
import numpy as np
from datetime import datetime as dtdt
from datetime import timedelta as td
from Algo.Viasualization.trading_kpis import calc_calmar,max_drawdown,calc_sharpe,sortino_ratio,sortino_ratio_v2, calc_sharpe_v2,get_drawdown_vector
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT,DYNAMIC_CLUSTERS_FILTERING_BY_DAY_HOUR
from Algo.Learning.research_plays import rolling_calmar
from matplotlib import pyplot as plt

%load_ext autoreload
%autoreload 2

pd.options.display.max_columns = 250 #Changes the number of columns diplayed (default is 20)
pd.options.display.max_rows = 250 #Changes the number of rows diplayed (default is 60)
pd.options.display.max_colwidth = 250 #Changes the number of characters in a cell so that the contents don't get truncated (default is 50)
#%%

a = pd.read_csv(r"C:\Users\<USER>\Documents\Work\Amazon\XYs\Enriched_XYs\XY_a_GDD_v8_0Zb.csv",parse_dates=['date'])


#%%
b = abs(a.set_index('date')[['diff_0Z_0-13_last-Prev1_EPS','diff_0Z_0-13_last-Prev2_EPS','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev1_GEFS']]).reset_index()
#b = b[b['date']]
#b.groupby(b['date'].dt.to_period('M')).mean()[-14:].plot(figsize=(12,12))
b.set_index('date').rolling(30,5).mean().plot(figsize=(14,14))
#%%

candles_csv = r"C:\Users\<USER>\Documents\Work\Amazon\Market_Data\NG_2018-19_frontMonth_tz_Live.csv"
candles_df = pd.read_csv(candles_csv,parse_dates=['date','time_utc'])

candles_df['date'] = pd.to_datetime(candles_df['time_utc'].dt.date) + td(hours=8)
candles_df = candles_df.groupby('date').agg({'open':'min'})

candles_df

price_cols = ['y_0600-0800','y_0800-1845','y_0800-1415','y_1415-1745']
c = a[['date']+price_cols]

c = c.merge(candles_df,on=['date'])
for col in list(c):
    if col.startswith('y_'):
        #c[col] /= c['open']*1000
        pass


c = c[(c['y_0800-1845']!=0)&(c['date'].dt.weekday.isin([0,1,2,3,4]))]


#abs(c.set_index('date')[['y_0800-1845','y_0800-1415','y_1415-1745']]).rolling(30,10).mean().plot(figsize=(12,12))
c2 = abs(c.set_index('date')[price_cols]).reset_index()
# c2.set_index('date').rolling(30,10).mean().plot(figsize=(12,12))
#c2.groupby(c2['date'].dt.to_period('M')).mean().plot(figsize=(12,12))
c2['month'] = c2['date'].dt.month
c2['year'] = c2['date'].dt.year

c3 = pd.pivot_table(c2,index=['month'],values=price_cols[1:2],
                    columns=['year'])

c3.plot(kind='bar',figsize=(12,12))


