#%%
import os

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from pandas_datareader import data as pdr


NUM_YEARS = 25

# Get historical yield curve data from FRED
start_date = (datetime.today() - timedelta(days=365*NUM_YEARS)).strftime('%Y-%m-%d')
end_date = datetime.today().strftime('%Y-%m-%d')
yield_curve = pdr.DataReader(["DGS2","DGS5","DGS10"], "fred", "2023-08-01", end_date)

yield_curve
#%%

from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
from calendar import monthrange

days_in_month = lambda dt: monthrange(dt.year, dt.month)[1]
today = dtdt.today() - td(days=2)
first_day1 = today + td(days_in_month(today)-3)
first_day2 = (first_day1.replace(day=1) + td(days_in_month(first_day1)+10)).replace(day=1)
app = None
i = 0
# stupid hack to TWS problem first month using direct API, second month using Ibridgepy #todo
ref_dates = [first_day1,first_day2]
ref_dates