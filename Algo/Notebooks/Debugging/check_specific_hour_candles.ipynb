#%%
import pandas as pd

from Algo.Utils.files_handle import get_candles_outpath
from datetime import date

# candles_csv = get_candles_outpath('NG',add_tzs=True)
#
# candles_df = pd.read_csv(candles_csv,parse_dates=['date'])


#%%
candles_df[candles_df.date.dt.date == date(2023,4,28)]
#%%
base_csv = r"C:\Users\<USER>\Documents\Work\Amazon\performance_analysis\Real_Trades\Clusters_Performance_mode=$MODE$_weekdays=[0, 1, 2, 3, 4]_Alldays=365_hours=[0,20].csv"

for csv in [base_csv.replace("$MODE$","real"),base_csv.replace("$MODE$","ens_calmar0.25_S0.5_window4"),
            base_csv.replace("$MODE$","ens_calmar3_S0.5_window4")]:

    df = pd.read_csv(csv,parse_dates=['t_open'])
    print(f'initial_shape: {df.shape[0]}')
    df = df.drop_duplicates().round(3)
    print(f'final_shape: {df.shape[0]}')
    df.to_csv(csv,index=False)
#%%
df = pd.read_csv(base_csv.replace("$MODE$","ens_calmar0.25_S0.5_window4"),parse_dates=['t_open'])

df = df[df.t_open.dt.date == date(2023,6,23)]
#%%
df