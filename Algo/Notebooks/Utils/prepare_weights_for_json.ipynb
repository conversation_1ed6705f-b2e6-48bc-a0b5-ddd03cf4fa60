#%%

#%%
import pandas as pd

weights_file = r"C:\Users\<USER>\Documents\Work\Amazon\research\FINAL_US_with_Canada_gas_consumption_x_population_weights_CDDxJulAug22Avg_HDDxJan22_withREGIONAL.csv"

weights_df = pd.read_csv(weights_file)
weights_df.sum()
#%%
list(weights_df)
#%%
weight_cols = ['weight_consumption_per_hdd_per_1k',
 'weight_consumption_total_per_hdd_per_1k',

 'weight_consumption_per_hdd_per_1k_x_regional_share',
 'weight_consumption_total_per_hdd_per_1k_x_regional_share',

   'weight_consumption_per_cdd_per_1k',
 'weight_consumption_per_cdd_per_1k_x_regional_share',
   'weight_consumption_total_per_hdd_per_1k_no_canada']

weights_df['weight_consumption_total_per_hdd_per_1k_no_canada'] = weights_df['weight_consumption_total_per_hdd_per_1k']
weights_df.loc[weights_df['weight']==0,'weight_consumption_total_per_hdd_per_1k_no_canada'] = 0

for w in weight_cols:
    print(weights_df[weights_df[w]>0].shape, weights_df[weights_df[weight_cols].min(axis=1)>0].shape, weights_df[w].sum())

common_lat = weights_df[weights_df[weight_cols].max(axis=1)>0]['lat'].tolist()
common_lon = weights_df[weights_df[weight_cols].max(axis=1)>0]['lon'].tolist()
#print(f'Common Lat: \n{common_lat}')
# print(f'Common Lon: \n{len(common_lon)}')
# raise

#print(weights_df[weight_cols].sum())

tmp_df = weights_df[weights_df[weight_cols].max(axis=1)>0]

i = 6

for weight in weight_cols[0+i:1+i]:
    print(f'================= {weight} ==================')
    #tmp_df = weights_df[weights_df[weight]>0]
    # print(tmp_df['lat'].tolist())
    # print(tmp_df['lon'].tolist())
    print(tmp_df[weight].round(3).tolist())

#%%
import json

d = json.load(open(r"C:\Users\<USER>\PycharmProjects\Algo_ec2\Auxiliary_Files\config_aligned_10perline.json"))

print(d.keys())

