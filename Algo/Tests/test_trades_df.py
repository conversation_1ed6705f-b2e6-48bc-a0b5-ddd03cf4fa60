from Algo.Utils.files_handle import HOME, get_daily_predictions_path

import pandas as pd

def test_daily_prediction_duplicates(suffix='_All'):
    """
    Test if there are duplicates in the daily predictions file
    """
    daily_predictions_path = get_daily_predictions_path(suffix)
    df = pd.read_csv(daily_predictions_path,parse_dates=['date'])
    df2 = df.drop_duplicates()
    assert df.shape[0] == df2.shape[0], f'There are duplicates in the daily predictions file, shape = {df.shape[0]} Vs {df2.shape[0]}'
    # df = df.sort_values('date')
    # df.to_csv(daily_predictions_path,index=False)


def test_daily_prediction_missing_days():
    """
    Test if there are missing days in the daily predictions file
    """
    daily_predictions_path = get_daily_predictions_path('_All')
    df = pd.read_csv(daily_predictions_path,parse_dates=['date'])
    df['day'] = df['date'].dt.date
    df['count'] = 1
    df2 = df.groupby('day').agg({'count':'sum'}).reset_index()
    df3 = df2.groupby(pd.to_datetime(df2['day']).dt.to_period('M')).agg({'count':'mean'}).reset_index()
    print(df3)


if __name__ == '__main__':
    test_daily_prediction_duplicates('')
    #test_daily_prediction_duplicates('')
    #test_daily_prediction_missing_days()
