from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features,add_seasonal_diffs,add_eps_gaps,ttf_enrichment,coal_enrichment,add_rolling_momentum,\
                            ngf_enrichment,wti_enrichment,rsi_enrichment,add_previous_weeks_momentum,\
                                wind_enrichment,cfsm_enrichment,past_positions_enrichment,wind_vs_30d_enrichment,gdd_vs_30d_enrichment,\
                                    add_rolling_diffs,add_regional_diffs,add_0D_trend,clusters_enrichment,add_light_Xs,\
                                    algo_preds_enrichment,enrich_with_teleconnection_xs
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Data_Processing.hisotical_values import main_historical_calc
from Algo.Learning.models_accuracy_calc import get_multimodel_cors_df,get_cors_df
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
from Algo.Learning.predict_eps import add_eps_preds as add_eps_preds_rolling_corrs,add_eps_preds_from_gefs
import os
from Algo.Utils.files_handle import HOME, get_delta_ys_full_df_path
from Algo.Conf.predictors_conf import COMBS_PREDICTORS_DICT
from Algo.Learning.algo_wrapper_utils import add_probability, wrap_predictor_handling, _preprocess_delta_ys,_impute_shifted_features,get_results_df
from Algo.Ybox.definitions import DELTA_YS_METHOD


import warnings
warnings.filterwarnings('ignore')

COST_PER_TRADE = 0.75
# COST_PER_TRADE = 0
DEBUG_MODE = False
DEBUG_MODE = True


T_test = 200
T_train = 30
get_xy_from_conf = False
days_ahead = '[1-15]'
last_model = 12

NORMALIZE_YTAGS = True
NORMALIZE_XS = False
replace_zero_ys = 0.0001

start_year = 8
start_month = 11 if start_year < 9 else 10 # for the 2018
LEARNING_START = dtdt(2010+start_year,start_month,1)
#LEARNING_START = dtdt(2019,11,1)
START_DATE = dtdt(2017,10,1)
END_DATE = dtdt(2030,12,1)
MONTHS = range(1,13) #[10,11,12,1,2,3,4] #[10,11,12,1,2,3] # 4 # [10,11,12,1,2,3] #[1,2,11,12]
UNITE_GFS_PARA = True

MINIMAL_BACKFILL_DAYS = 21

TIME_KEY = 'time_utc' #'time_chicago' #''
FILTER_GEFS_MODE = '4'

TTF_ANALYSIS = True
TTF_PLOT = False

# elastic net conf
ALPHA = 0.3


def handle_special_configs(predictor_conf_name, model_df,normalize_y_tags=NORMALIZE_YTAGS):
    # Auxiliary func
    def _get_prev_hour(last_model, hours_back):
        return (last_model - hours_back) % 24

    if predictor_conf_name == 'bollinger_hack':
        model_df['macd_sign_4H'] /= model_df['macd_sign_4H'].std()
        model_df['location_1H_20'] /= model_df['location_1H_20'].std()
        model_df['y_tag'] = (1 * model_df['macd_sign_4H'] - 1 * model_df[
            'location_1H_20'] + 2 * model_df['macd_sign_4H_diff1'] + 2 * model_df['macd_sign_4H_trend']) / 6
        model_df['y_tag'] *= model_df['y'].std()
    elif predictor_conf_name == 'prices_hack':

        model_df['y_0100-0330'] /= model_df['y_0100-0330'].std()
        # since its weight is 0 we allow fillnans
        model_df['y_0100-0330'] = model_df['y_0100-0330'].fillna(0)

        model_df['y_1900-2030'] /= model_df['y_1900-2030'].std()
        model_df['y_tag'] = (2 * model_df['y_1900-2030'] - 0 * model_df[
            'y_0100-0330']) / 2
        model_df['y_tag'] *= model_df['y'].std()
    elif 'GFS_hack' in predictor_conf_name:
        assert UNITE_GFS_PARA

        if predictor_conf_name == "GFS_hack_v1":
            cols_to_avg = ['diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 18)),
                           'diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 24))]
        elif predictor_conf_name == "GFS_hack2_v1":
            #cols_to_avg = ['diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 12)),
             #              'diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 24)),
              #             'diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 18))]
            cols_to_avg = ['diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 24)),
                      'diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 12), _get_prev_hour(last_model, 24)),
                         'diff_%sZ-%sZ_ENS' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 18))]
        else:
            raise AssertionError("bad GFS name: %s" % predictor_conf_name)
        model_df['y_tag'] = model_df[cols_to_avg].mean(axis=1)
    elif predictor_conf_name == 'GEPS_hack_v1':
        model_df['y_tag'] = model_df['diff_%sZ-%sZ_GEPS' % (_get_prev_hour(last_model, 12), _get_prev_hour(last_model, 24))]
    elif predictor_conf_name == 'PARACO_hack_v1':
        cols_to_avg = ['diff_%sZ-%sZ_PARACO' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 12)),
                       'diff_%sZ-%sZ_PARACO' % (_get_prev_hour(last_model, 6), _get_prev_hour(last_model, 24)),
                       'diff_%sZ-%sZ_PARACO' % (_get_prev_hour(last_model, 12), _get_prev_hour(last_model, 24))]
        for col,w in zip(cols_to_avg, [1,2,1]):
            model_df[col] *= (w/4.0)
        model_df['y_tag'] = model_df[cols_to_avg].mean(axis=1)
    else:
        raise AssertionError("Bad conf name for hack model")
    if normalize_y_tags:
        model_df['y_tag'] /= model_df['y_tag'].std()
    return model_df

def run_model_config_slide_build(df,target_col,reg_engine,learning_start,
                                 max_learning_lines,slide_window,normalize_y_tags,normalize_Xs):

    X, y = df[list(df)[2:]], df[target_col]  # load_boston(return_X_y=True)
    if normalize_Xs:
        X = X / X.std()
    df['y_tag'] = np.nan

    if reg_engine == 'DTR':
        regressor = DecisionTreeRegressor(random_state=0)
    elif reg_engine == 'db':
        regressor = Deepbit(complexbits=5,depth=4,tested_bits=20,bits_per_feature=7,iterations=20)
    elif reg_engine == 'elastic':
        regressor = ElasticNet(random_state=0, alpha=ALPHA)
    elif reg_engine == 'elastic2':
        regressor = ElasticNetCV(eps=0.25, random_state=0)
    elif reg_engine == 'Xtrees':
        regressor = ExtraTreesRegressor(n_estimators=100, max_depth=1, bootstrap=False)
    elif reg_engine == 'Bag':
        regressor = BaggingRegressor()
    elif reg_engine == 'randomF':
        regressor = RandomForestRegressor()
    else:
        raise AssertionError("")

    pointer = learning_start
    while pointer < df['time'].iloc[-1]:
        next_pointer = (pointer + td(days=slide_window))
        if pointer.month != next_pointer.month:
            next_pointer = next_pointer.replace(day=1)
        train_inds = (df['time'] < pointer) & (~df[target_col].isna()) & (X.isna().mean(axis=1) == 0)  # dtdt(2019,10,1)
        time_cond = (pointer <= df['time']) & (df['time'] < next_pointer)
        test_inds = (X.isna().mean(axis=1) == 0) & time_cond
        X_train, y_train = X[train_inds][-max_learning_lines:], y[train_inds][-max_learning_lines:]
        X_test, y_test = X[test_inds], y[test_inds]
        if X_train.shape[0] == 0:
            aa = 1
        if T_train:
            y_train = y_train.apply(lambda x: min(x, T_train) if x > 0 else max(-T_train, x))
        try:
            if reg_engine == 'db':
                X_train = X_train.as_matrix()
                y_train = y_train.as_matrix()
                X_test = X_test.as_matrix()
            regressor.fit(X_train, y_train)
            if X_test.shape[0]:
                ytag = regressor.predict(X_test)
                df['y_tag'][test_inds] = ytag
        except:
            print("Failed with pointer = %s " % (pointer))

        pointer = next_pointer

    if T_test:
        inds = ~df[target_col].isna()
        df[target_col][inds] = df[target_col][inds].apply(
            lambda x: min(x, T_test) if x > 0 else max(-T_test, x))
    if normalize_y_tags:
        df['y_tag'] /= df['y_tag'].std()
    return df


def wrap_prediction_process(xy_file,strategy,target_col_0,configs,
                            learning_start=LEARNING_START,time_key=TIME_KEY,
                            normalize_Xs=NORMALIZE_XS, cond_on_time=lambda x: True):
    """
    :param xy_file:
    :param target_col:
    :param configs:  {name: {"cond": cond, "reg_engine": reg_egine, "weight":"",
                "learning_windpw":"","slide_window":""}}
    :type configs: dict
    :param learning_start:
    :param max_learning_lines:
    :param weights_override:
    :param time_key:
    :param normalize_Xs:
    :return:
    """
    target_col = target_col_0
    df0 = pd.read_csv(xy_file,parse_dates=[time_key])
    df0 = df0.drop_duplicates(subset=[time_key,"y"])
    if time_key != 'time':
        df0 = df0.rename(columns={time_key: "time"})

    df0 = df0[(df0['time'] <= END_DATE)&(df0['time'] >= START_DATE)&(df0['time'].apply(lambda x: cond_on_time(x)))]
    df0[target_col][df0[target_col] == 0] = replace_zero_ys

    # start generating ensemble members
    results_dfs = {}

    # df0[['time']+[x for x in list(df0) if 'y_' in x or 'GEM' in x or 'GEPS' in x or 'EPS' in x]]
    for predictor_conf_name in configs.keys():
        if 'prices' in predictor_conf_name and 'CORN' in xy_file:
            target_col_0 = 'y_1300-1315'
        target_col = target_col_0
        if 'bollinger_v' in predictor_conf_name and 'hack' in predictor_conf_name and strategy == '0800-1100':
            print('HACK changing target col for %s'%predictor_conf_name)
            if 'v1' in predictor_conf_name:
                target_col = 'y_1415-1745'
            elif 'v2' in predictor_conf_name:
                target_col = 'y_0800-1745'
            df0[target_col][df0[target_col] == 0] = replace_zero_ys
        current_conf = configs[predictor_conf_name]
        if 'bollinger_v1' in predictor_conf_name:
            aaaa = 1
        df = df0.copy()
        if 'gem' in predictor_conf_name:
            df['diff_GEPS-GEFS_0Z'] = df['Value_0Z_GEPS'] - df['Value_0Z_GEFS']
            df['diff_GEPS-GEFS_6Z'] = df['Value_0Z_GEPS'] - df['Value_6Z_GEFS']

        if 'bollinger_v1' in predictor_conf_name:
            boll_fts = ['macd_sign_15M_trend4', 'macd_sign_2H_trend4', 'macd_sign_4H_trend4',
                                            'macd_sign_1H_trend4', 'macd_sign_30M_trend4',
                                            'macd_sign_1H_diff1', 'macd_4H', 'macd_2H', 'macd_1H', 'macd_1D',
                                            'location_1H_20', 'location_2H_20', 'location_4H_20']

            df = df[['time', target_col] + boll_fts] #+[x for x in list(df) if x in ['weekday','season']]] # ['summerness']
            if 'bollinger' in predictor_conf_name and '_d' in predictor_conf_name:
                d = int(predictor_conf_name.split("_hack")[0].split("_")[-1].split("d")[-1])
                for ft in boll_fts:
                    df[ft][~df['time'].dt.weekday.isin([d])] = np.nan
            elif 'bollinger' in predictor_conf_name and 'hack' in predictor_conf_name:
                d = dtdt.now().weekday()
                for ft in boll_fts:
                    df[ft][~df['time'].dt.weekday.isin([d])] = np.nan
                    pass
            current_conf["learning_window"] = 100
            aa = 1
        else:
            df = df[[x for x in list(df) if x in ['time', target_col] or (current_conf["cond"](x))]]

        # pre-processing
        if UNITE_GFS_PARA:
            para_cols = [x for x in list(df) if x.endswith("PARA")]
            gfs_cols = [x.replace("PARA","GFS") for x in para_cols]
            for gfs_col,para_col in zip(gfs_cols,para_cols):
                try:
                    df[gfs_col.replace("GFS","ENS")] = df[[gfs_col,para_col]].mean(axis=1)
                except:
                    a = 1
            df = df[[x for x in list(df) if not (x.endswith('PARA') or x.endswith('GFS'))]]
        print ('df[y_1130-1315.isna().mean() = %s'%df0['y_1130-1315'].isna().mean())
        df = run_model_config_slide_build(df,target_col,current_conf["reg_engine"],learning_start,
                                          current_conf["learning_window"],current_conf["slide_window"],
                                                normalize_y_tags=NORMALIZE_YTAGS,normalize_Xs=normalize_Xs)
        print ('Finished %s (engine = %s)'% (predictor_conf_name,current_conf['reg_engine']))
        if predictor_conf_name in ['GFS_hack_v1','GFS_hack2_v1','prices_hack','GEPS_hack_v1','PARACO_hack_v1']:
            df = handle_special_configs(predictor_conf_name,df,normalize_y_tags=NORMALIZE_YTAGS)
        # ignore preds from before learning start
        df['y_tag'][df['time'] < learning_start] = np.nan
        print ('predictor: %s | Adding df of shape: %s'%(predictor_conf_name,str(df.shape)))
        #print ('conf: %s'%current_conf)
        results_dfs[predictor_conf_name] = df

    # combine models
    if len(configs):
        final_results_df = combine_ensemble_members(configs,results_dfs)
        boll_cols_to_add = [c for c in list(df0) if (c not in list(final_results_df)) and ('macd' in c or 'location' in c or 'sign' in c)]
        final_results_df = df0[['time']+boll_cols_to_add].merge(final_results_df,on=['time'],suffixes=("_-1",""))
    else:
        final_results_df = df0
    #s = final_results_df.corr()['y_tag_bollinger_v1']
    return final_results_df


def wrap_prediction_process_new(xy_file,strategy,target_col_0,configs,
                            learning_start=LEARNING_START,time_key=TIME_KEY,
                            normalize_Xs=NORMALIZE_XS, cond_on_time=lambda x: True,
                            start=None):
    target_col = target_col_0
    df0 = pd.read_csv(xy_file,parse_dates=[time_key])
    if start is not None:
        df0 = df0[df0[time_key] >= start]
    df0 = df0.drop_duplicates(subset=[time_key,"y"])
    if time_key != 'time':
        df0 = df0.rename(columns={time_key: "time"})

    df0 = df0[(df0['time'] <= END_DATE)&(df0['time'] >= START_DATE)&(df0['time'].apply(lambda x: cond_on_time(x)))]
    df0[target_col][df0[target_col] == 0] = replace_zero_ys

    # start generating ensemble members
    results_dfs = {}
    final_results_df = df0
    return final_results_df


def combine_ensemble_members(configs,results_dfs_dict,weekdays_weights_hack=False):
    sorted_keys = sorted(configs.keys())
    assert len(sorted_keys), "got empty list of configs"

    final_results_df = pd.DataFrame()
    for i,predictor_conf_name in enumerate(sorted_keys):
        model_conf = configs[predictor_conf_name]
        model_df = results_dfs_dict[predictor_conf_name]
        model_df = model_df.rename(columns={"y_tag": "y_tag_%s" % predictor_conf_name})
        # round the prices for merge
        common = ['time']
        if 'y' in list(model_df) and 'y' in list(final_results_df):
            common += ['y']
        for c in ['y_1415-1745', 'y_1130-1415']:
            if c in list(model_df) and c in list(final_results_df):
                print('%s in both dfs, roundign it' % c)
                common += [c]
                model_df[c] = model_df[c].apply(lambda x: round(x, 3))
        # merge
        if final_results_df.shape[0] == 0:
            final_results_df = model_df
        else:
            if predictor_conf_name == 'prices_v1':
                aaa= 1
            if 'y_1415-1745' in list(final_results_df):
                final_results_df['y_1415-1745'] = final_results_df['y_1415-1745'].apply(lambda x: round(x, 3))
            final_results_df = final_results_df.merge(model_df,on=common,how="outer")

    # drop duplicates
    final_results_df['order'] = 1
    final_results_df['order'][final_results_df['y'].isna()] = 2
    final_results_df = final_results_df.sort_values(['time','order']).drop_duplicates(subset=['time'])

    # give weights
    conf_weights = [configs[name]["weight"] for name in sorted_keys]
    for name,weight in zip(sorted_keys,conf_weights):
        final_results_df['y_tag_%s'%name] *= weight
    if not weekdays_weights_hack:
        y_tags = final_results_df[['y_tag_%s' % name for name in sorted_keys]]
        final_results_df['y_tag'] = y_tags.mean(axis=1) / sum(conf_weights)
    else:
        pass
    return final_results_df


def get_xy_path(template,hours_str,last_model,days_ahead,x_suffix="take8"):
    xy_file = template.replace("<h>", hours_str).replace("<last>", str(last_model)).replace("<da>",days_ahead).replace("takeX",x_suffix)
    return xy_file



def handle_strategy(strategy, last_model,configs,days_ahead='1-15',x_suffix="take9",
                    drop_partial_predictions=False
                    ,cond_on_time=lambda x: True, normalize_Xs=True,test_truncation=T_test,asset='NG',
                    start=None):

    template = TEMPLATES[asset] #XY_PREV24_BOLLINGER_TEMPLATE_V3

    f = get_xy_path(template, strategy, last_model, '[%s]'%days_ahead,x_suffix)

    targ_col = 'y'
    #configs = {k:configs[k] for k in configs.keys() if 'PARACO' in k or 'prices' in k}
    final_results_df = wrap_prediction_process_new(f,strategy, targ_col,configs,time_key=TIME_KEY,learning_start=LEARNING_START,
                                               normalize_Xs=normalize_Xs,cond_on_time=cond_on_time,
                                                   start=start)

    # drop duplicate columns
    try:
        final_results_df = final_results_df.ix[:,~final_results_df.columns.duplicated()]
    except AttributeError:
        final_results_df = final_results_df.loc[:, ~final_results_df.columns.duplicated()]
    # add prob
    final_results_df = add_probability(final_results_df)

    return final_results_df

def add_weekdays_weights(results_df,pred,weekdays_dict,new_pred_col=None):
    if new_pred_col is not None:
        results_df[new_pred_col] = results_df[pred].copy()
        relevant_col = new_pred_col
    else:
        relevant_col = pred
    multipliers = results_df['date'].apply(lambda x: weekdays_dict[x.weekday()])
    results_df[relevant_col] *= multipliers
    return results_df

def lat_games(final_results_df):
    # final_results_df[['y']+[x for x in list(final_results_df) if 'y_tag' in x]][(final_results_df.time.dt.weekday.isin([1,2,4]))&(final_results_df['time']>=dtdt(2019,11,1))].corr()
    # final_results_df[[x for x in list(final_results_df) if 'y_tag' in x]].iloc[-1]
    # analyze weekdays
    if 'y_tag_bollinger_v2' in list(final_results_df) and 'y_tag_bollinger_v2b' in list(final_results_df):
        final_results_df['y_tag_bollinger_v22b'] = (final_results_df['y_tag_bollinger_v2'] * 2 + final_results_df[
        'y_tag_bollinger_v2b']) / 3
        final_results_df['y_tag_bollinger_v22b'] /= final_results_df['y_tag_bollinger_v22b'].std()

    # Corr by weekday
    yy = 'y_1645-1745' #FS_hack2_v1'  #
    dct = {}
    df = None
    for days_lst,days_name in [([0],'Mon'),([3],'Thu'),([1,2,4],'TWF')]:
        cond = lambda x: x.weekday() in days_lst #and x.month in [2] #x.month == 3 and x.day >= 10
        df = final_results_df[
            (final_results_df['time'] >= dtdt(2019, 11, 1)) & (final_results_df['time'].apply(lambda x: cond(x)))][
            [x for x in list(final_results_df) if 'time' not in x] + [yy] + ['y']]  # 'y_' in x
        df = df.ix[:, ~df.columns.duplicated()]

        df = df[abs(df[yy]) > abs(df[yy]).quantile(0.4)]
        c = df.corr()[yy]
        ab = abs(df).mean()
        #c = c.ix[:, ~c.columns.duplicated()][yy]
        dct[days_name] = c
        dct[days_name+"_abs"] = ab
        c = None
        cond = None
    df = pd.DataFrame(dct)

    # 3xamine weekdays HR PPT
    months = [10,11,12,1,2,3,4]
    T = 40 # ticks
    stack = []
    target = 'y_1645-1745' #'y'
    predictors = [x for x in list(final_results_df) if 'y_tag' in x]
    # dd[dd['predictor']=='y_tag'].set_index('days')['ppt_val'].plot(kind='bar')
    for pred in predictors:
        for days_name, days in [('Mon', [0]),('Tue', [1]),('Wed', [2]),('Thu', [3]),('Fri', [4])]:
            dct = {}
            for t in ['train', 'val', 'all']:
                if t == 'train':
                    tmp = final_results_df[['time', target, pred]][final_results_df['time'] <= dtdt(2019, 10, 1)]
                elif t == 'val':
                    tmp = final_results_df[['time', target, pred]][final_results_df['time'] >= dtdt(2019, 11, 1)]
                else:
                    tmp = final_results_df[['time', target, pred]]
                tmp[target] = tmp[target].apply(lambda x: min(x,T) if x > 0 else max(x,-T))
                tmp = tmp[tmp.time.dt.month.isin(months)]
                tmp = tmp.ix[:, ~tmp.columns.duplicated()]
                tmp = tmp[abs(tmp[pred]) > abs(tmp[pred]).quantile(0.25)]
                tmp = _add_is_hit(tmp, pred, target)
                tmp['profit'] = tmp['is_hit_-11'] * abs(tmp[target])
                tmp2 = tmp[(tmp['time'].dt.weekday.isin(days)) & (~tmp[pred].isna()) & (~tmp[target].isna())]
                hr = tmp2['is_hit'].mean()
                ppt = tmp2['profit'].mean()
                cor = tmp2.corr()[target].loc[pred]
                dct.update(
                    {'days': days_name, 'predictor': pred, 'HR_%s' % t: hr, 'cor_%s' % t: cor, 'ppt_%s' % t: ppt})
            stack.append(dct)
    d = pd.DataFrame(stack).set_index('predictor')
    a = 1

def get_action(predictor,is_trade):
    pass

def wrap_calculation(final_results_df,strategy,calc_start,calc_end,suffix,ft,big_model_hour):
    allow_12z = '12Z' in suffix or suffix == 'v8_0Zb'

    add_delta_Ys = DELTA_YS_METHOD == 'v1'

    print('Before manual fts its %s' % dtdt.now())
    a, aa, prices_cols = add_manual_features(final_results_df, strategy, start=calc_start, end=calc_end,
                                             weekdays=[0, 1, 2, 3, 4, 6],
                                             months=range(1, 13), suffix=suffix, ft=ft, add_deltaYs=add_delta_Ys,
                                             override_big_model_hour=big_model_hour,
                                             allow_12z=allow_12z,
                                             )
    if DELTA_YS_METHOD == 'v2':
        a = a[[c for c in list(a) if not (c.startswith('y_') and len(c) == 11)]]
        delta_ys_v2_df = pd.read_csv(get_delta_ys_full_df_path('NG'),parse_dates=['date'])
        a = a.merge(delta_ys_v2_df, on=['date'], how='left')
    a = a.round(3)
    print('line 490, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
    SKIP_ENRICHMENT = False
    if not SKIP_ENRICHMENT:
        print('After manual fts its %s' % dtdt.now())
        a = add_seasonal_diffs(a, ft)
        print('After Seasonal fts its %s' % dtdt.now())
        a = add_eps_gaps(a)
        print('After eps gaps its %s' % dtdt.now())
        a = ttf_enrichment(a)
        a = ttf_enrichment(a,asset='MTF')
        print('After ttf enrichment, its %s' % dtdt.now())
        print('line 500, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        a = a.round(3)
        a = coal_enrichment(a)
        a = ngf_enrichment(a)
        a = wti_enrichment(a)
        a = rsi_enrichment(a)
        a = wind_enrichment(a)
        print('line 507, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        print(1,[x for x in list(a) if 'total_' in x])
        a = wind_vs_30d_enrichment(a)
        print(2,[x for x in list(a) if 'total_' in x])
        a = gdd_vs_30d_enrichment(a)
        # sort
        a = a.sort_values('date')
        print(3,[x for x in list(a) if 'total_' in x])
        a = add_0D_trend(a)
        print(4,[x for x in list(a) if 'total_' in x])
        a = cfsm_enrichment(a)
        print(5,[x for x in list(a) if 'total_' in x])
        a = past_positions_enrichment(a)
        print(6,[x for x in list(a) if 'total_' in x])
        print('line 522, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        a = algo_preds_enrichment(a) # todo, might need to add more targets and weekdays
        print(7,[x for x in list(a) if 'total_' in x])
        a = enrich_with_teleconnection_xs(a)
        a = a.sort_values('date')
        print('line 524, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        try:
            a = enrich_df_with_external_fts(a, ft, plot=PLOT_ENRICHMENT, suffix=suffix)
        except NameError:
            a = enrich_df_with_external_fts(a, ft, plot=False, suffix=suffix)
        a = a[a['date']>=calc_start]
        print('After enrichment with external its %s' % dtdt.now())
        a = add_correlation_filtering(a, filter_GEFS=FILTER_GEFS_MODE)
        print('After Corrleations its %s' % dtdt.now())
        if a.drop_duplicates(subset=['date']).shape[0] != a.shape[0]:
            stop = 1
        a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='EPS_0Z')
        print('After eps preds 1 its %s' % dtdt.now())
        a = a.round(3)
        print('line 538, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        if suffix == 'v8_0Zb':
            a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='EPS_12Z',
                                            eps_col='y_1815-1945')  # 'diff_12Z_0-13_last-Prev1_EPS+1d')
            a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='EC_12Z',
                                            eps_col='diff_12Z_0-8_last-Prev1_EC+1d')  #
            a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='GEFS_6Z',
                                            eps_col='diff_6Z_0-16_last-Prev1_GEFS')  # 'diff_12Z_0-13_last-Prev1_EPS+1d')
            a = add_eps_preds_from_gefs(a, window=20,suffix=suffix)
            a = add_eps_preds_from_gefs(a, window=10,suffix=suffix)
            a = add_eps_preds_from_gefs(a, window=5,suffix=suffix)
            #a = add_eps_preds_from_gefs(a, window=20, suffix=suffix,eps_col='diff_0Z_0-13_last-Prev1_EPS')
            #a = add_eps_preds_from_gefs(a, window=10, suffix=suffix,eps_col='diff_0Z_0-13_last-Prev1_EPS')
            #a = add_eps_preds_from_gefs(a, window=5, suffix=suffix,eps_col='diff_0Z_0-13_last-Prev1_EPS')
        else:
            a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='EPS_12Z')
            #a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='EC_12Z')
            if suffix == 'v8_12Z':
                a = add_eps_preds_from_gefs(a, window=20, suffix=suffix)
                a = add_eps_preds_from_gefs(a, window=10, suffix=suffix)
                a = add_eps_preds_from_gefs(a, window=5, suffix=suffix)
                a = add_eps_preds_from_gefs(a, window=10, suffix=suffix, eps_col='diff_12Z_0-8_last-Prev1_EC')
                a = add_eps_preds_from_gefs(a, window=5, suffix=suffix, eps_col='diff_12Z_0-8_last-Prev1_EC')
                a = add_eps_preds_from_gefs(a, window=20, suffix=suffix, eps_col='diff_12Z_0-8_last-Prev1_EC')
                a = add_eps_preds_from_gefs(a, window=5, suffix=suffix, eps_col='diff_12Z_9-13_last-Prev1_EPS')
                a = add_eps_preds_from_gefs(a, window=10, suffix=suffix, eps_col='diff_12Z_9-13_last-Prev1_EPS')
                a = add_eps_preds_from_gefs(a, window=15, suffix=suffix, eps_col='diff_12Z_9-13_last-Prev1_EPS')
        print('After eps preds 2 its %s' % dtdt.now())
        a = add_eps_preds_rolling_corrs(a, suffix=suffix, time_group='daily_0Z', eps_col='y_0800-1845',
                                        y_col='y_0800-1845')
        print('After eps preds 3 its %s' % dtdt.now())
        a = add_rolling_momentum(a, window=3)
        a = add_previous_weeks_momentum(a)
        print('After rolling momentumits %s' % dtdt.now())
        print('line 572, a[y_0630-1230][-20:].isna().mean() = %s' % a['y_0630-1230'][-20:].isna().mean())
        try:
            a = add_regional_diffs(a,calc_start,'EPS','GDD','last3M' if (calc_start > dtdt.now()-td(days=120)) else 'Full')
        except Exception as e:
            print(f'WARNING: Failed to add regional diffs {e}')

        # add cluster forecasts
        try:
            a = clusters_enrichment(a)
        except Exception as e:
            print(f'WARNING: Failed to add cluster forecasts {e}')
        # add EPSCO
        try:
            a = add_light_Xs(a,calc_start,'EPSCO')
            a = add_light_Xs(a,calc_start,'EC',x_version='v3.0_0Z')
        except Exception as e:
            print(f'WARNING: Failed to add EPSCO {e}')
        a = a.round(3)

    a = a[(a['date'] >= calc_start) & (a['date'] <= calc_end)].sort_values(['date'])
    a = a.loc[:, ~a.columns.duplicated()]
    a = a[a['date'].dt.second == 0]
    return a

def main_wrapper(conf_name='test',last_model=12,strategy='0800-1100',calc_As=1,print_predies=1,
                suffix='v8',ft='GDD',send_email=False,
                 start=dtdt(2019,11,1),end=dtdt(2020, 11, 1),big_model_hour=None,asset='NG',
                 base_on_existing=True,calc_start_override=None,
                 minimal_backfill_days=MINIMAL_BACKFILL_DAYS):
    # Other constants
    days_ahead_str = '1-15'
    x_suffix = "take9"
    normalize_Xs = True
    weekdays = [0, 1, 2, 3, 4]
    months_to_drop = [0]
    drop_partial_predictions = False
    cond_on_time = lambda x: x.month in MONTHS  # and not x.weekday() !=

    ################## ACTION BEGINS HERE #######################
    weights_override = {}
    learning_models = get_configs(strategy,conf_name,weights_override)

    # get predictions
    final_results_df = handle_strategy(strategy,last_model,learning_models,days_ahead_str,x_suffix,drop_partial_predictions,
                    cond_on_time,normalize_Xs,asset=asset,
                                       start=start)

    # Todo delete after making sure the solution in line 480 is working
    # if DELTA_YS_METHOD == 'v2':
    #     final_results_df = final_results_df[[c for c in list(final_results_df) if not (c.startswith('y_') and len(c) == 11)]]
    #     delta_ys_v2_df = pd.read_csv(get_delta_ys_full_df_path('NG'),parse_dates=['date']).rename(columns={'date':'time'})
    #
    #     final_results_df = final_results_df.merge(delta_ys_v2_df,on=['time'],how='left')

    # drop months from analysis
    final_results_df = final_results_df[~final_results_df.time.dt.month.isin(months_to_drop)]
    final_results_df = final_results_df[final_results_df['time']<end]

    ###############################   START OF ANALYSIS ###############################

    print ('before building A, final_results_df.shape = %s'%str(final_results_df.shape))
    if calc_As:
        if base_on_existing:
            a = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","","XY_a_%s_%s.csv") % (ft, suffix),
                            parse_dates=['time', 'date','prompt_ref_contract','report_date'])
            a = a[a['date'] <= dtdt.now() + td(days=2)] # can't have future data
            calc_start = pd.to_datetime(a['date'].dropna(),errors='coerce').max() - td(days=100+MINIMAL_BACKFILL_DAYS-7)
            if calc_start_override is not None:
                calc_start = calc_start_override
            calc_end = end
        else:
            calc_start = start
            calc_end = end
        calculated_a = wrap_calculation(final_results_df,strategy,calc_start,calc_end,suffix,ft,big_model_hour)
        # calculated_a.to_csv(r"C:\Users\<USER>\Downloads\calculated_a.csv",index=False)
        # raise
        print ('y_0800-1100_d3ma3 in calculated_a: %s'%(('y_0800-1100_d2ma3' in list(calculated_a))))
        if base_on_existing:
            a = a[a['date']<= dtdt.now() - td(days=minimal_backfill_days)]
            calculated_a = calculated_a[calculated_a['date']>a['date'].iloc[-1]]
            print('Now its %s | about to merge'%dtdt.now())
            if not set(list(calculated_a)) <= set(list(a)):
                print('WARNING: not its %s calculated_a has columns that are not in a: %s'%(dtdt.now(),str(set(list(calculated_a)) - set(list(a)))[:40]))
            common = [x for x in list(calculated_a) if x in list(a)]
            calculated_a = calculated_a[common]
            a = a[common]
            a = pd.concat([a,calculated_a])
            #a = a.merge(calculated_a,on=list(calculated_a),how='outer')

        else:
            a = calculated_a

        from Algo.Utils.general import safe_write_to_csv
        if not 'diff_18Z_0-16_last-Prev2D_PARA' in list(a):
            print(f'WARNING: not its {dtdt.now()}, diff_18Z_0-16_last-Prev2D_PARA not in a just before saving')
        safe_write_to_csv(a,os.path.join(HOME,"XYs","Enriched_XYs","","XY_a_%s_%s.csv")%(ft,suffix))
    else:
        a = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","","XY_a_%s_%s.csv")%(ft,suffix),parse_dates=['time','date'])


    a = a.dropna(subset=['date']).sort_values(['date'])
    a = a[(a['date']>=start)&(a['date']<=end)]
    print ('start, end = end = %s -----> %s'%(start,end))
    official_preds = ['EPSpost_pred2b','y_tag_bollinger_v1','combo_0800','EPS45_pred2','PM_hybrid_pred2', 'GFS_Super_PM_pred2','Avg_PM_pred3D',
                                'EarlyPM_midweek','PM_hybrid_pred1',
                      'PM_pred1','EPS_PM_pred1', 'EPS_pred3','EC_AM_Summer_pred1']  # 'PM_ENS_pred1'
    AM_preds = ['EPS_daily_hybrid_pred1b','EPS_daily_hybrid_pred2.2','EPSpost_pred2.2','EPSpost_pred2b','diff_8-16_last-Prev1D_GEFSL','EPS_daily_hybrid_pred1','EPS_daily_hybrid_pred2','EPSL_daily_hybrid_pred3','EPSL_daily_hybrid_pred4','EPS_pred3','AM_Ens1',
                'diff_0Z_0-13_Prev1D-Prev2D_EPS','EPS45_pred2','EPS45_rolling_diff3',
                        'PARA_daily_hybrid_pred2','PARACO_daily_hybrid_pred2','GEFS_daily_hybrid_pred2',
                        'GFSCO_daily_hybrid_pred2','GEFSL_daily_hybrid_pred2','GEPS_daily_hybrid_pred2',
                        'PARA_pre6Z_pred1','diff_0Z_0-8_last-Prev1_GFSCO','GEM_AM_pred1','GEM_AM_pred2','GEMEMCO_AM_pred2',
                'GEFS_AM_basic_pred1','PARA_AM_basic_pred1','PARACO_AM_basic_pred1','GEPS_AM_basic_pred1']
    bollinger_preds =['y_tag_bollinger_v1','y_tag_bollinger_v1_hack','macd_sign_30M_trend8','macd_sign_2H_trend8',
                      'macd_sign_2H_trend4''macd_sign_4H_trend8',]
    CFS_preds = ['diff_0Z_0-16_last-Prev4D_CFS','diff_0Z_0-16_last-Prev1D_CFS','CFS_AM_pred1','CFS_AM_Daily_pred1','CFS_AM_Daily_hybrid_pred1','CFSpre_6Z_pred1','CFSpre_6Z_pred2', 'CFSpost_6Z_pred1',
                'diff_0Z_14-21_last-Prev1D_CFS','diff_0Z_14-21_last-Prev2D_CFS','diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev2D_CFS',
                 'diff_0Z_28-42_last-Prev1D_CFS', 'diff_0Z_28-42_last-Prev2D_CFS',
                 'diff_0Z_14-28_last-Prev2D_CFS','diff_6Z_14-28_last-Prev3_CFS',
                 'diff_0Z_28-42_last-Prev4_CFS',
                 'diff_0Z_28-42_last-Prev4D_CFS','diff_12Z_28-42_last-Prev4D_CFS',
                 # 'diff_0Z_14-28_last-Prev1D_CFSCO','diff_0Z_14-28_last-Prev2D_CFSCO',
                 # 'diff_0Z_14-28_last-Prev2_CFSCO','diff_0Z_14-28_last-Prev4_CFSCO'
                 ]
    preds_6Z = ['PARA_6Z_pred1','PARACO_6Z_pred1','GEFS_6Z_pred1','GFSEFS_6Z_pred1','GFSEFS_6Z_pred3_basic'] #+ CFS_preds
    cash_preds = ['Cash_hybrid_pred1','Cash_pred3','diff_0Z_0-2_last-Prev2_EPS','diff_0Z_0-2_last-Prev2_EC','diff_0Z_0-2_last-Prev2_GEM']
    PM_preds = ['EarlyPM_midweek','ECGEM_AM_pred1','EC_AM_Summer_pred1','GEM_AM_pred1','PARACO_PM_pred1', 'PARA_PM_pred1', 'GEFS_PM_pred1','GFSEFS_PM_pred1','GFSEFS_PM_pred2',
                'GEFSL_PM_pred1_Prev1','GEFSL_PM_pred1_Prev2','GEFSL_PM_pred1_Prev4','EPS_PM_pred1','GEFS_PM_pred1',
                'GEPS_PM_pred1D','GEMCO_PM_pred1','GEMCO_PM_pred2',
                'diff_0Z_0-13_last-Prev1_GEFSLCO','Avg_PM_pred3D','GFS_Super_PM_pred2','PM_hybrid_pred2','PM_Ens1','PM_Ens2']
    D = '1D'
    daily_gaps_preds = (['diff_12Z_0-13_last-Prev4_EPS'] if '12Z' in suffix else [])+['diff_0Z_0-13_last-Prev4_EPS','diff_0Z_0-13_last-Prev3_EPS','EPS_daily_hybrid_pred1',
                        'diff_0Z_0-13_last-Prev1D_EPS','diff_0Z_0-13_last-Prev2D_EPS','diff_0Z_0-13_last-Prev3D_EPS','diff_0Z_0-13_last-Prev4D_EPS',
                        'diff_0Z_8-13_last-Prev1D_EPS','diff_0Z_8-13_last-Prev2D_EPS','diff_0Z_8-13_last-Prev3D_EPS']+\
                       ['diff_0Z_0-16_last-Prev%s_PARA' % D, 'diff_0Z_0-16_last-Prev%s_PARACO' % D,
                        'diff_0Z_0-16_last-Prev%s_GEFS' % D,'diff_0Z_0-16_last-Prev%s_GFSv16' % D,
                        'diff_6Z_0-16_last-Prev%s_PARA' % D, 'diff_6Z_0-16_last-Prev%s_PARACO' % D,
                        'diff_6Z_0-16_last-Prev%s_GEFS' % D,
                        'diff_0Z_0-16_last-Prev%s_GEFSL' % D, 'diff_0Z_0-16_last-Prev%s_GFSCO' % D,
                        'diff_0Z_0-16_last-Prev%s_GEPS' % D, 'diff_0Z_0-16_last-Prev%s_GEMCO' % D,'diff_0Z_0-10_last-Prev%s_GEM' % D,
                        'diff_0Z_14-35_last-Prev%s_GEFS35' % D,
                        'diff_0Z_14-35_last-Prev%s_CFS' % D,'diff_0Z_14-35_last-Prev%s_GEFS35' % D]+\
                            ['diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-8_last-Prev2D_EC',
                             'diff_0Z_0-10_last-Prev1D_GEM','diff_0Z_0-10_last-Prev2D_GEM','diff_0Z_0-10_last-Prev3D_GEM']
    #std_cols = ['Value_0Z_0-16_diff8_normed_GEFS','Value_0Z_0-16_diff8_normed_PARACO','Value_0Z_0-16_diff8_normed_PARA']
    std_cols = ['Value_0Z_0-16_diff8_normed_GEFS', 'Value_0Z_0-16_diff8_normed_PARACO','Value_0Z_0-16_diff8_normed_PARA',
                'Value_0Z_0-10_diff8_normed_GEM','Value_0Z_0-16_diff8_normed_GEPS','Value_0Z_0-13_diff8_normed_GEMCO',
                'Value_0Z_0-13_diff8_normed_EPS','std_0-16_full_rs',
                'std_0-16_top3_rs','std_0-16_american_rs',
                'std_0-8_top4_rs','std_0-8_top4_diff1',
                'Corr_20_GEFS','Corr_20_PARA','Corr_20_EPS']
    rs_cols = ['Value_0Z_0-16D_%s_rs'%model for model in ['GEPS','GEMCO','GEFS','PARA','PARACO','GFSCO','GEFSL','CFS']]+['Value_0Z_0-13D_EPS_rs']+\
        ['Value_0Z_day15trend_EPS','Value_0Z_day15trend_GEFS','Value_6Z_day15trend_GEFS']
    txt_lst = []
    if print_predies:
        txt = '\n\n------- Recent Stability (diff8 method) ----------'
        print(txt)
        txt_lst.append(txt)
        for std_col in std_cols:
            if 'Corr_20' in std_col:
                hack_0_to_100 = True
            else:
                hack_0_to_100 = False
            if not hack_0_to_100:
                a[std_col] -= a[std_col].mean()
                a[std_col] *= -1
            a = add_probability(a, std_col, 'prob_%s' % std_col, time_col='date',hack_0_to_100=hack_0_to_100)
            if not hack_0_to_100:
                sign_str = "+" if np.sign(a.iloc[-1][std_col]) == 1 else "-"
            else:
                sign_str = '+'
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], std_col, sign_str, a.iloc[-1]['prob_%s' % std_col])
            print(txt)
            txt_lst.append(txt)
        txt = '\n\n=====================================================\n\n------- Seasonal Preds ----------'
        print (txt)
        txt_lst.append(txt)
        for rs_col in rs_cols+['Value_0Z_day15trend_EPS','Value_0Z_day15trend_EPS_diff1','Value_0Z_day15trend_EPS_diff2',
                               'Value_0Z_day15trend_GEFS','Value_0Z_day15trend_GEFS_diff2']:
            a = add_probability(a, rs_col, 'prob_%s' % rs_col, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][rs_col]) == 1 else "-"
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], rs_col, sign_str, a.iloc[-1]['prob_%s' % rs_col])
            print(txt)
            txt_lst.append(txt)
        txt = '\n\n=====================================================\n\n------- AM Preds ----------'
        print (txt)
        txt_lst.append(txt)
        d = dtdt.now().weekday()
        for predie in AM_preds:
            if predie not in list(a):
                print ('%s is not in a... skipping')
                continue
            if 'bollinger_v1' in predie:
                bb = 0
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            if 'rolling' in predie:
                aa = 1
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s'%(a.iloc[-1]['date'],predie,sign_str,a.iloc[-1]['prob_%s'%predie])
            print (txt)
            txt_lst.append(txt)
        txt = '----------- Preds Bollinger ----------'
        print(txt)
        txt_lst.append(txt)

        for predie in bollinger_preds:
            if predie not in list(a):
                print ('%s is not in a... skipping')
                continue
            if 'bollinger_v1' in predie:
                bb = 0
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s'%(a.iloc[-1]['date'],predie,sign_str,a.iloc[-1]['prob_%s'%predie])
            print (txt)
            txt_lst.append(txt)
        txt = '----------- Preds 6Z ----------'
        print(txt)
        txt_lst.append(txt)

        for predie in preds_6Z:
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s'%(a.iloc[-1]['date'],predie,sign_str,a.iloc[-1]['prob_%s'%predie])
            print (txt)
            txt_lst.append(txt)
        print ('----- CFS Only ------')
        txt_lst.append('----- CFS Only ------')

        for predie in CFS_preds:
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], predie, sign_str, a.iloc[-1]['prob_%s' % predie])
            print(txt)
            txt_lst.append(txt)

        print('----------- Daily Gaps Preds ----------')
        txt_lst.append('----------- Daily Gaps Preds ----------')
        for predie in daily_gaps_preds:
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], predie, sign_str, a.iloc[-1]['prob_%s' % predie])
            print(txt)
            txt_lst.append(txt)
        print ('----------- Cash Preds ----------')
        txt_lst.append('----------- Cash Preds ----------')
        for predie in cash_preds:
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], predie, sign_str, a.iloc[-1]['prob_%s' % predie])
            print(txt)
            txt_lst.append(txt)
        print('----------- PM Preds ----------')
        txt_lst.append('----------- PM Preds ----------')
        for predie in PM_preds:
            a = add_probability(a, predie, 'prob_%s' % predie, time_col='date')
            sign_str = "+" if np.sign(a.iloc[-1][predie]) == 1 else "-"
            txt = '%s | %s : (%s)%s' % (a.iloc[-1]['date'], predie, sign_str, a.iloc[-1]['prob_%s' % predie])
            print(txt)
            txt_lst.append(txt)
            if predie == 'EarlyPM_midweek':
                print ('--------')
                txt_lst.append('--------')
            if predie == 'GEM_AM_pred1':
                print ('--------')
                txt_lst.append('--------')
        if send_email:
            print ('About to send Email with predictions!')
            now = dtdt.now()
            round_now = dtdt(now.year,now.month,now.day,now.hour,now.minute)
            subject = 'Algo | Daily Predictors for %s'%(round_now.strftime('%d/%m/%Y %H:%M:%S'))
            body = "\n".join(txt_lst)
            send_email_main(subject, body)
    eps45_preds_full = [x for x in list(a) if 'diff' in x and 'EPS45' in x]
    a[eps45_preds_full] = a[eps45_preds_full].replace(to_replace=0, value=np.nan).fillna(method='ffill')

    return a

def enrich_df_with_external_fts(a,ft,historical_models=['PARA'],
                                accuracy_models=['GEFS', 'PARA', 'EPS', 'GEFSL'],suffix='v8_12Z',
                                plot=False):
    ### cash fts
    cash_df = cash_analysis(plot=plot)
    cash_cols = [x for x in list(cash_df) if 'date' not in x]
    print ('Shape before Cash merge: %s'%a.shape[0])
    a = a.merge(cash_df, on=['date'],how='outer')
    print('Shape After Cash merge: %s' % a.shape[0])

    a[cash_cols][:-5] = a[cash_cols][:-5].fillna(method='ffill')
    ## historical fts
    final_historical = pd.DataFrame()
    for model in historical_models:
        historical_para = main_historical_calc(model, ft,plot=False,model_hour=0,suffix=suffix)
        historical_para = historical_para[['date'] + [x for x in list(historical_para) if x != 'forecast_time' and x not in list(a)]]
        if final_historical.shape[0] == 0:
            final_historical = historical_para
        else:
            final_historical = final_historical.merge(historical_para,on=['date'])
    print('Shape Before historical merge: %s' % a.shape[0])
    a = a.merge(final_historical, on=['date'],how='outer')
    print('Shape After historical merge: %s' % a.shape[0])
    ## accuracy fts
    multimodel_cors_df = get_multimodel_cors_df(accuracy_models, ft=ft)
    multimodel_cors_df['date'] = multimodel_cors_df['validation_day'] + td(hours=8)
    multimodel_cors_df = multimodel_cors_df[[x for x in list(multimodel_cors_df) if x != 'validation_day']]
    multimodel_cors_df = multimodel_cors_df[multimodel_cors_df['date']<=a['date'].max()]
    print('Shape Before Cors merge: %s' % a.shape[0])
    a = a.merge(multimodel_cors_df, on=['date'],how='outer')
    print('Shape After Cors merge: %s' % a.shape[0])
    # TTF prices
    a = a.drop_duplicates(subset=['date'],keep='first')
    print('Shape After Duplicates: %s' % a.shape[0])
    return a

def add_correlation_filtering(a,filter_GEFS='4'):

    if filter_GEFS == '0':
        a = a
    elif filter_GEFS == '1':
        a = a[(a['std_0-8_top4_rs'] < 0)]
    elif filter_GEFS == '1a':
        a = a[(a['std_0-8_top4_rs'] > a['std_0-8_top4_rs'].quantile(0)) & (
                    a['std_0-8_top4_rs'] < a['std_0-8_top4_rs'].quantile(0.67))]
    elif filter_GEFS == '2':
        a = a[(a['std_0-8_top4_rs'] < a['std_0-8_top4_rs'].quantile(0.65)) | (
                    a['std_0-8_top4_diff1'] < a['std_0-8_top4_diff1'].quantile(0.65))]
    elif filter_GEFS == '3':
        a = a[(a['std_0-8_top4_rs'] < 0) & (a['std_0-8_top4_diff1'] < a['std_0-8_top4_diff1'].quantile(0.6))]
    elif filter_GEFS == '4':
        c = 'Corr_20_GEFS'
        for quant in []:  # 0.2,0.4,0.6,0.8,1]:
            b = a[  # (a['Corr_20_EPS'] > a['Corr_20_EPS'].quantile(1))|
                (a[c] > a[c].quantile(quant - 0.2)) &
                (a[c] < a[c].quantile(quant))
                # (a['std_0-8_top4_rs'] < a['std_0-8_top4_rs'].quantile(0.7)) #|
                # (a['std_0-8_top4_diff1'] < a['std_0-8_top4_diff1'].quantile(0))
                ]
            print(
                'Quant: %s | Corr GEFS x y_0800-1945: %s' % (quant, b.corr()['EPSpost_pred2.2'].loc['y_0800-1945']))
        a['diff_0Z_0-16_last-Prev1D_GEFSfltrd'] = a['diff_0Z_0-16_last-Prev1D_GEFS']
        a['diff_0Z_8-16_last-Prev1D_GEFSfltrd'] = a['diff_0Z_8-16_last-Prev1D_GEFS']
        a['diff_6Z_0-16_last-Prev1D_GEFSfltrd'] = a['diff_6Z_0-16_last-Prev1D_GEFS']
        a.loc[(a[c] < a[c].quantile(0.3)), 'diff_0Z_0-16_last-Prev1D_GEFSfltrd'] = 0  # a[(a[c] > a[c].quantile(0.25))#&
        a.loc[(a[c] < a[c].quantile(0.3)), 'diff_0Z_8-16_last-Prev1D_GEFSfltrd'] = 0  # a[(a[c] > a[c].quantile(0.25))#&
        a.loc[(a[c] < a[c].quantile(0.3)), 'diff_6Z_0-16_last-Prev1D_GEFSfltrd'] = 0  # a[(a[c] > a[c].quantile(0.25))#&
    a = a.drop_duplicates(subset=['date'],keep='first')
    return a



def _get_potential_candidates_for_strat(a, weekday, strat, desired_sign=1, start=dtdt(2020, 9, 1),
                                        end=dtdt(2060, 9, 1),
                                        top=20, stds_required=0.15, cond_on_strats=lambda x: True,
                                        days_back=1,allow_negative_signal=True,
                                        return_correlations=False):
    if isinstance(weekday, int):
        weekdays = [weekday]
    else:
        weekdays = weekday
    corrs = a[(a['date'] >= start) & (a['date'] <= end) & (a['date'].dt.weekday.isin(weekdays))].corr()[strat].loc[
                [x for x in list(a) if (x.startswith('diff_') or
                                        'pred' in x or 'easonal' in x or '_rs' in x or '1Y' in x or 'ttf' in x or 'Coal' in x or 'omentum' in x
                                        or 'WTI' in x or 'NGF' in x or 'MTF' in x or 'CFSM' in x or 'macd' in x or 'sign' in x or 'location' in x or 'open-' in x or 'close-' in x) and cond_on_strats(
                    x)]].sort_values().dropna()
    corrs_original = corrs.copy()
    if allow_negative_signal:
        corrs = abs(corrs).sort_values()
    corrs = corrs[-top:]
    candidates = corrs.index.tolist()

    if stds_required > 0:
        candidates_today_normed = a[candidates].iloc[-days_back] / a[candidates].std()
        candidates_today_normed = candidates_today_normed[np.sign(candidates_today_normed) == desired_sign]
        candidates_today_normed = candidates_today_normed[abs(candidates_today_normed) > stds_required]
    else:
        candidates_today_normed = a[candidates].iloc[-days_back]
    candidates_today_normed = candidates_today_normed.index.tolist()

    candidates2 = []
    for candidate in candidates_today_normed:
        if corrs_original.loc[candidate] < 0:
            candidate = candidate + '-'
        candidates2.append(candidate)
    corrs = corrs.loc[candidates_today_normed]
    corrs.index = candidates2
    # drop dups
    candidates3 = pd.Series(candidates2).drop_duplicates().tolist()
    corrs = pd.Series(corrs).reset_index().drop_duplicates(subset=['index'], keep='first').set_index('index')[strat]
    if not return_correlations:
        return candidates3
    else:
        return candidates3, corrs



def main_analysis(a,start,end,required_strat=None,mode='research',stds_hack=False,strat_override=None):

    pred_col = 'GFS_Super_PM_pred2' #'PM_hybrid_pred2'#'' # # # #'PM_Ens1' #'y_tag_bollinger_v1'
    std_col = 'Value_0Z_0-13_std4_normed_EPS'  # std_col_gefs

    lower_std_thresh_ratio = 0.35
    upper_std_thresh_ratio = 1

    #a = a[a['date'].dt.month.isin([3,4,5,6,7,8,9,10])]
    months = [x for x in range(13) if x not in [11,12,1,2]]
    a = a[(a['date'] >= start) & (a['date'] <= end)]
    #a = a[a['date'].dt.month.isin(months)]
    if mode == 'prod':
        stds_hack = False
    if stds_hack:
        a['ind'] = ((a[std_col] > a[std_col].quantile(lower_std_thresh_ratio)) & (
                    a[std_col] < a[std_col].quantile(upper_std_thresh_ratio))).apply(lambda x: 1 if x else -1)
    if mode != 'prod':
        basic_weekdays_weights_dict = {0:1,1:1,2:1,3:1,4:1,5:0,6:0}
        try:
            a = add_weekdays_weights(a,pred_col,basic_weekdays_weights_dict)
        except:
            pass
        if stds_hack:
            a = a[a['ind'] == 1]
        #a['ones'] = 1
        #a.groupby(a.date.dt.to_period('M')).sum()['ones'].plot(kind='bar',title='trades number per month after filtering with Stds')
        #plt.show()

        print (a[std_col].mean())
        deep_analysis = False
        if deep_analysis:
            start = dtdt(2020,4,1)
            pred_col = 'diff_0Z_0-13_last-Prev1D_EPS'
            target_col = 'y_1245-1415'
            weekdays0 = [1,2,3] #,3,4]
            small_df = wrap_analysis(a, pred_col, target_col,weekdays0, start, end,
                                     write_results=False, plot=True,chosen_ratio=0.5,upper_threshold=None)
            ###### EVALUATION #######
            a = a.sort_values(['date'])
        predictors_dict = {'y_0700-0730':('EPS_pred3',0.5),'y_0700-0800':('EPS_pred3',0.5),
                           'y_0800-1100': ('EPSpost_pred2b',0.8), 'y_PM_weekdays': ('GFS_Super_PM_pred2',0.8), #1 / hybrid12
                           'y_ChicOpen': ('EarlyPM_midweek',0.5),
                           #'y_1415-1745':('GFS_Super_PM_pred2',0.7)
                           }

    results_df = pd.DataFrame()
    comb_profits = True
    if mode == 'prod':
        comb_profits = True

    # additional featurs
    a = add_rolling_diffs(a)
    print ('After rolling_diffs, shape = %s'%a.shape[0])
    try:
        a = add_correlation_filtering(a,filter_GEFS='4')
    except:
        print ('Failed to add correlation filtering!')
    print('After correlation_fitering, shape = %s' % a.shape[0])

    a = _preprocess_delta_ys(a)

    default_strat = 'y_1315-1415'
    only_final_comb = False
    cond_for_plot = lambda x: True
    if only_final_comb and comb_profits:
        cond_for_plot = lambda x: 'MEAN' in x or 'combined' in x
    if mode == 'prod':
        assert comb_profits
        if required_strat is not None:
            default_strat = required_strat

    if mode == 'prod':
        chosen_strats = [default_strat]
    else: #  # 0630-0745 1100-1200 1515-1615
        gfsv16_strats = ['y_1400-1745_GFSv1614D','y_1415-1715_GFSv16pre12Z','y_1545-1645_GFSv16z',
                         'y_1000-1100_GFSv160z','y_1100-1130_GFSv164D','y_1130-1200_GFSv16z']
        eps12z_strats_small = ['y_1945-2045_4a', 'y_2300-2345_EPS12Z', 'y_1845-2030_EC12Z']
        eps12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and not ('GEPS' in p or 'geps' in p) and ('12Z' in p or '12z' in p) and 'cst' not in p]
        ec_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'EC' in p or 'ec' in p]
        eps0z9to13_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and not ('GEPS' in p or 'geps' in p or 'EPS45' in p) and ('12Z' not in p and '12z' not in p)
                             and ('9to13' in p)]
        eps0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and not ('GEPS' in p or 'geps' in p or 'EPS45' in p) and ('12Z' not in p and '12z' not in p)]
        eps0z_d15_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and ('d15' in p or '12to13' in p)]
        epsco0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPSCO' in p or 'epsco' in p) and not ('GEPS' in p or 'geps' in p or 'EPS45' in p) and
                          ('12Z' not in p and '12z' not in p)]
        epsFcst_0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and ('Fcst' in p or 'fcst' in p) and ('0z' in p.lower())]
        epsFcst_6z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and ('Fcst' in p or 'fcst' in p) and ('6z' in p.lower())]
        epsFcst_12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EPS' in p or 'eps' in p) and ('Fcst' in p or 'fcst' in p) and ('12Z' in p or '12z' in p)]

        paraco_strats = ['y_0600-0800_PARACO0AMpred3','y_1415-1645_PARACO6z','y_1415-1845_PARACO6z','y_1645-1715_PARACO6z']
        geps12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEPS' in p or 'geps' in p) and ('12z' in p or '12Z' in p)]
        eps45_strats_predictors = [p for p in COMBS_PREDICTORS_DICT.keys() if 'EPS45' in p]
        geps_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'GEPS' in p or 'geps' in p or 'GEMCO' in p]
        gem_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'GEM' in p or 'gem' in p]
        gem12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEM' in p or 'gem' in p) and ('12z' in p or '12Z' in p)]
        ec0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('EC' in p or 'ec' in p) and ('12z' not in p and '12Z' not in p)]
        gemco_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'GEMCO' in p]
        gefs6z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('gefs' in p or 'GEFS' in p) and ('6z' in p or '6Z' in p or 'PM' in p)]
        gefs0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('gefs' in p or 'GEFS' in p) and ('0z' in p or '0Z' in p or 'AM' in p or 'am' in p)]
        gefs18z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('gefs' in p or 'GEFS' in p) and ('18z' in p or '18Z' in p or 'AM' in p or 'am' in p)]
        gefs18z_day15_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('gefs' in p or 'GEFS' in p) and ('18z' in p or '18Z' in p) and ('day15' in p)]
        gefs12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEFS' in p or 'gefs' in p) and ('12z' in p or '12Z' in p or 'Comb' in p)]
        gefsfcst12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEFS' in p or 'gefs' in p) and ('fcst' in p or 'Fcst' in p) and 'epsFcst' not in p]
        american_14to16 = [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEFS' in p or 'PARA' in p) and ('14to16' in p or 'day15' in p)]

        gefs35_strats =  [p for p in COMBS_PREDICTORS_DICT.keys() if ('GEFSCO35' in p or 'GEFS35' in p)]

        para0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('0z' in p or '0Z' in p) and not ('paraco' in p or 'PARACO' in p)]
        para6z_strats = ['y_1200-1245_6Zclean']+[p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('6z' in p or '6Z' in p or 'PM' in p) and not ('paraco' in p or 'PARACO' in p)]
        para12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('12z' in p or '12Z' in p) and not ('paraco' in p or 'PARACO' in p)]
        para18z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('18z' in p or '18Z' in p) and not ('paraco' in p or 'PARACO' in p)]
        paraco6z_strats = ['y_1200-1245_6Zclean']+[p for p in COMBS_PREDICTORS_DICT.keys() if ('paraco' in p or 'PARACO' in p) and ('6z' in p or '6Z' in p)]
        paraco0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('paraco' in p or 'PARACO' in p) and ('0z' in p or '0Z' in p or 'am' in p or 'AM' in p)]
        paraco18z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('paraco' in p or 'PARACO' in p) and ('18z' in p or '18Z' in p)]
        paraco12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('paraco' in p or 'PARACO' in p) and ('12z' in p or '12Z' in p)]
        gefs12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('gefs' in p or 'GEFS' in p) and ('12z' in p or '12Z' in p)]
        american0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('0z' in p or '0Z' in p or 'am' in p or '018' in p)]
        american18z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p) and ('18z' in p or '18Z' in p or '018' in p)]
        american1D_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('para' in p or 'PARA' in p or 'GFSv16' in p or 'gfsv16' in p or 'GEFS' in p) and ('1D' in p)]
        wed_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('Wed' in p or 'wed' in p)]
        ttf_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'TTF' in p or 'NGF' in p]
        midday_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'midday' in p]
        ngf_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'NGF' in p]
        coal_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'COAL' in p]
        wti_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'WTI' in p]
        wind_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'Wind' in p]
        momentum_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'momentum' in p or 'Mom' in p or 'tum' in p]
        rsi_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'RSI' in p or 'RelS' in p]
        cash_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'Cash' in p or 'cash' in p or 'CASH' in p]
        cash_vsettle_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('Cash' in p or 'cash' in p) and 'settle' in p]
        geps_cash_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('Cash' in p or 'cash' in p or 'CASH' in p) and ('GEPS' in p or 'geps' in p or 'gem' in p or 'GEM' in p)]
        seasonal_strats_old = [p for p in COMBS_PREDICTORS_DICT.keys() if 'Seasonal' in p or '1Y' in p]+['y_1300-1430_EC0dChange']
        seasonal_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'seasonal' in p.lower() or '1Y' in p]+['y_1300-1430_EC0dChange']
        cfs_15d_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and '0to16' in p]
        cfs_12z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and ('12' in p)]
        cfs_am_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and ('12' in p or '18' in p)]
        cfs_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p)]
        cfs0z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and '0z' in p.lower()]
        cfs18z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and '18z' in p.lower()]
        cfs_daily_0to16 = [p for p in COMBS_PREDICTORS_DICT.keys() if (('CFS' in p or 'cfs' in p) and 'Daily' in p and '0to16' in p)]
        cfsco_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFSCO' in p or 'cfsco' in p)]
        cfsm_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFSM' in p or 'cfsm' in p)]
        real2d_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('real-2d' in p)]
        real4d_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('real-4d' in p)]
        cfsco_6z_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('CFS' in p or 'cfs' in p) and ('6z' in p or '6Z' in p)]
        clusters_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if ('Cluster' in p or 'cluster' in p)]

        day15_trend_strats = [p for p in COMBS_PREDICTORS_DICT.keys() if 'day15' in p or 'trend' in p]
        specific_predictors_to_check = [x[0] if isinstance(x, tuple) else x for x in
                                        ['y_1200-1245_6Z', 'y_1400-1745_GFSv1614D', 'y_1845-1915_GFSv160zp4',
                                         'y_1245-1300_PARA6z',
                                         ('y_1645-1745_GEFSPM', 'y_1200-1315'), ('y_1645-1745_GEFSPM', 'y_1545-1645'),
                                         ('y_1645-1745_GEFSPM', 'y_1715-1745'),
                                         ('y_1530-1645_GEFStrend15', 'y_1430-1500'),
                                         ('y_1530-1645_EPStrend15b', 'y_1200-1230'),
                                         ('y_1315-1415_americanSeasonalb', 'y_1330-1415'),
                                         ('y_1300-1345_cfs28to42', 'y_1315-1330'),
                                         ('y_1300-1345_cfs28to42', 'y_1345-1400'),
                                         ('y_1300-1345_cfs28to42', 'y_1545-1630'),
                                         ('y_1245-1300_PARA6z', 'y_1345-1400'),
                                         ('y_1200-1415_epsFcst12ZbCorloose', 'y_1215-1545'),
                                         ('y_1200-1415_epsFcst12ZbCor', 'y_1545-1915')]
                                        ]
        # ran!
        chosen_strats = [(x,y) for x in

                         [  #'y_0800-1415_GEMCO1D',
                             # 'y_1800-1830_tmpStrat',
                             # 'y_0800-1845_windScan_wide',
                             # 'y_0800-1845_windScan',
                          # 'y_1215-1745_PARACO6zp4$W',
                          #    'y_0800-1845_Daily6ZScan',
                          #    'y_0800-1845_CFS3D'
                             'y_0800-1845_Daily0ZScan',
                             # 'y_0800-1845_DailyGEPScan',
                             # 'y_0800-1845_DailyGEFScan',
                          #    'y_0800-1845_Daily0ZminiScan',
                          #    'y_0800-1845_DailyPredsNext1d_v2'
                          #    'y_0800-1845_DailyPredsNext1d_12z',
                          #    'y_0800-1845_DailyGEFScan',
                          #    'y_0800-1845_DailyPredsNext1d'
                          #    'y_0800-1845_Daily12ZminiScan',
                          # 'y_0800-1845_GEPS0Zp1','y_0800-1845_GEPS0Zp1Strict',
                          # 'y_0800-1845_GEPS0Zp2','y_0800-1845_GEPS0Zp2Strict',
                          # 'y_0800-1845_GEPS0Zp3','y_0800-1845_GEPS0Zp4',
                          # ] #,
                         # 'y_0800-1845_GEFS_0zp2singleton','y_0800-1845_GEFS_6zp3singleton',
                         ][-1:]

                          for y in [

                                # d = 0
                                # ('y_1100-1315_GEFS18Zday15p3','y_1315-1715') (currently 1100-1745) check
                                # 15 min
                                # 'y_1100-1115','y_1115-1130','y_1130-1145','y_1145-1200',
                                # 'y_1200-1215','y_1215-1230','y_1230-1245','y_1245-1300',
                                # 'y_1300-1315','y_1315-1330','y_1330-1345','y_1345-1400',
                                # 'y_1400-1415','y_1415-1430','y_1430-1445','y_1445-1500',
                                # 'y_1500-1515','y_1515-1530','y_1530-1545','y_1545-1600',

                             # 'y_1400-1415', 'y_1415-1430',
                             #    'y_1400-1430',
                                # 30 min
                                # 'y_0600-0630','y_0630-0700','y_0700-0715','y_0700-0730',
                                # 'y_0715-0745', 'y_0730-0800',
                                # 'y_0800-0830','y_0830-0900','y_0900-0930',
                                # 'y_1000-1030','y_1030-1100',
                                # 'y_1100-1130','y_1130-1200',#'y_1145-1215',
                                # 'y_1200-1230','y_1230-1300',#'y_1245-1315',
                                # 'y_1300-1330','y_1330-1400',#'y_1345-1415',
                                # 'y_1400-1430','y_1430-1500',#'y_1445-1515',
                                #
                                    # 'y_0800-0900','y_0900-1000','y_1000-1100','y_1100-1200',
                                    # 'y_1200-1300','y_1300-1400','y_1400-1500','y_1500-1545','y_1545-1645','y_1645-1745',
                                    # 'y_1745-1845','y_1845-1945',
                                        # afternoon scan
                                        # 'y_1100-1130','y_1130-1200','y_1200-1230','y_1230-1300',
                                        # 'y_1300-1330','y_1330-1400','y_1400-1430','y_1430-1500',
                                        # 'y_1400-1415','y_1415-1430',
                                        # 'y_1500-1545','y_1545-1615','y_1615-1645','y_1645-1715'
                                        # 'y_1500-1545','y_1545-1645','y_1645-1745','y_1745-1845',
                                        # 'y_1700-1730','y_1730-1800','y_1800-1830',
                                        # 'y_1715-1815','y_1745-1845','y_1845-1945',
                                        # 'y_1715-1745','y_1745-1815','y_1815-1845',
                                        # 'y_1845-1900','y_1900-1915','y_1845-1915','y_1915-1945','y_1930-1945',

                                        # 'y_1100-1115','y_1115-1130','y_1130-1145','y_1145-1200',
                                        # 'y_1200-1215','y_1215-1230',
                                        # 'y_1230-1245','y_1245-1300',
                                        # 'y_1300-1315','y_1315-1330',
                                        # 'y_1330-1345','y_1345-1400',
                                        # 'y_1400-1415','y_1415-1430',
                                        # 'y_1215-1245',
                                        # 'y_1100-1145','y_1515-1615','y_1715-1815','y_1915-2045',
                                        #
                                        # 'y_0700-0800','y_0800-1000','y_1000-1100','y_1100-1200','y_1200-1300',
                                        # 'y_1300-1400',
                                        # 'y_0800-1200','y_1200-1415','y_1415-1545','y_1545-1745',
                                        # 'y_0700-0730','y_0730-0800','y_0800-1100',
                                        # 'y_1100-1200','y_1200-1300','y_1300-1330','y_1330-1400','y_1400-1430',
                                        # 'y_1430-1530','y_1530-1645','y_1645-1745',
                                        # 'y_1745-1945',
                                        # 'y_0600-0800'
                                        # 'y_0400-0600','y_0600-0800',
                                        # 'y_0800-1000','y_1000-1100','y_1100-1200',
                                        # 'y_1200-1315','y_1315-1415','y_1415-1545','y_1545-1645',
                                        # 'y_1645-1745','y_1745-1845','y_1845-1945',

                                        # 'y_1645-1745','y_1745-1845'
                                       #  'y_0700-0800','y_0800-0900','y_0900-1100','y_1100-1200',
                                       #  'y_1200-1315',
                                       #  'y_0800-1315','y_1315-1745',
                                       # 'y_1645-1745','y_1745-1845',
                                       'y_0800-1845'
                                       # 'y_0800-2000_ConcatedHack',
                                        # 'y_0800-1100','y_1100-1415','y_1415-1745',#'y_1645-1845'
                                        # 'y_1100-1745'
                                        ][-1:]
                         ]
        chosen_strats = sorted(chosen_strats, key=lambda x: x[1])
        #chosen_strats = [('y_0000-0600_18Z', 'y_0000-0400'), ('y_0000-0600_18Z', 'y_0530-0600'), ('y_1300-1545_PARA018Z0to8$3', 'y_0000-0600'), ('y_1715-1915_GEFSCO3514to35p3', 'y_0600-0800'), 'y_0600-0800_epsFcst', ('y_0700-0830_EPSPrev1D8to13', 'y_0700-0800'), ('y_0700-0745_general', 'y_0630-0745'), ('y_1000-1100_GFSv160z2', 'y_0700-0800'), 'y_0630-0800_MorningComb', ('y_1715-1845_PARACO018z0to10', 'y_0600-0800'), 'y_0800-1000_PARACO12zPrev1', 'y_0630-0800_GEMCOPrev1D0z8to16']
    for j,target_strat_str0 in enumerate(chosen_strats):
        if isinstance(target_strat_str0,tuple):
            target_strat = target_strat_str0[1]
            target_strat_str = target_strat_str0[0]
        else:
            target_strat_str = target_strat_str0
            if len(target_strat_str.split('_')) == 3: #and '_-1d' not in target_strat_str: ## unclear why we needed it in the first place
                target_strat = "_".join(target_strat_str.split("_")[:-1])
            else:
                target_strat = target_strat_str
        if mode != 'prod':
            # drop some months
            #a.loc[a['date'].dt.month.isin([10,11,12,1,2,3]),'y_1415-1715'] = 0
            #strats_to_concat = ['y_1315-1515','y_1615-1745','y_1815-1945']
            # 1H_trend8 (green '-') ---> [1,3] x 1200-1515 + [0] x 0800-1100
            a['y_1545-1700'] = a['y_1545-1715'] -a['y_1700-1715']
            a['y_1615-1700'] = a['y_1615-1715'] -a['y_1700-1715']
            a['y_0200-0600'] = a['y_0000-0600'] -a['y_0000-0200']
            a['y_0200-0400'] = a['y_0000-0400'] -a['y_0000-0200']
            a['y_0100-0600'] = a['y_0000-0600'] -a['y_0000-0100']
            a['y_0100-0200'] = a['y_0000-0200'] - a['y_0000-0100']

            strats_to_concat = 'y_0500-0530,y_0530-0600,y_0530-0630,y_0530-0700'.split(',')[:2]

            for s in strats_to_concat:
                if s.endswith('_-1d') and s not in list(a):
                    a[s] = a[s.split('_-1d')[0]].shift(1)
            strats_to_concat = sorted(strats_to_concat)
            concated_strat = 'y_0800-2000_ConcatedHack'
            a[concated_strat] = a[strats_to_concat].fillna(0).sum(axis=1)
            a['num_of_trades_in_concated'] = len(strats_to_concat) - sum([strats_to_concat[i].split('-')[-1]==strats_to_concat[i+1].split('-')[0].split('y_')[-1]
                                              for i,x in enumerate(strats_to_concat[:-1])])
            strats_to_concat2 = {0: [],
                                 1: [],
                                 2: ['y_0600-0800','y_1000-1400','y_1400-1500'],
                                 3: ['y_1000-1200','y_1200-1300','y_1400-1500'],
                                 4: ['y_0600-0800','y_0900-1000','y_1000-1400']
                                 } #}
            concat2 = ['y_1130-1200','y_1230-1315','y_1330-1415',
                                'y_1545-1645','y_1815-1845']
            # strats_to_concat2 = {0:concat2,1:concat2,2:concat2,3:concat2,4:concat2}
            ### print in decision_maker format
            # for d in [2, 3, 4]:
            #     print('#implement d=%s' % d)
            #     print("#" + ",".join(["('y_0800-1400_EPS12ZComb', '%s')" % s for s in strats_to_concat2[d]]) + ',')
            a['y_0800-2000_ConcatedHack2'] = 0
            for d in strats_to_concat2.keys():
                try:
                    a.loc[a['date'].dt.weekday==d,'y_0800-2000_ConcatedHack2'] = a.loc[a['date'].dt.weekday==d,strats_to_concat2[d]].sum(axis=1)
                except:
                    print('Failed to generate concatedHack2, strats that do not exist: %s'%[x for x in strats_to_concat2[d] if x not in list(a)])
                    raise
            if isinstance(target_strat_str0,str):
                target_strat = concated_strat
                pass

            # check dynamic position rule-based
                #---- > if MA_72_15M > MA_96_15M > MA_240_15M AND both have similar slope (+++) == BUY
                #---- > if MA_72_15M < MA_96_15M <  MA_240_15M AND both have similar slope (---) == SELL
                #---- > if P > MA_72_1H and P > MA_150_1H BUY
                ### 72 Vs 96 alone are also quite indicative
            # maybe ADD dynamic threshold based on recent 3 months ?! to all predcitors! it will be another filed in the config!

            pass
        elif strat_override is not None:
            target_strat = strat_override
        if target_strat == 'y_ChicOpen':
            weekdays = [0,1,2,3,4]
        elif 'friday' in target_strat_str:
            weekdays = [4]
        else:
            # weekdays = [0,1,2,3,4]
            weekdays = [0,1,2,3,4]
            # weekdays = [1]
            # weekdays = [2] #[1,2,3,4]
            # weekdays = [1,4] #[1,2,3,4]
            # weekdays = [1,2,4] #[1,2,3,4]
            # weekdays = [0,3] #[1,2,3,4]
        weekdays_for_thresh_calc = [0, 1, 2, 3, 4] if target_strat_str != 'y_2145-2345_6c' else [0,1,2,3,4,5,6]
        if mode == 'prod':
            weekdays = [0,1,2,3,4]

            if required_strat == 'y_2145-2345_6c':
                weekdays_for_thresh_calc = [0,1,2,3,4,5,6]

        default_chosen_ratio = 0.65
        default_start = dtdt(2020,5,1) # todo
        default_end = end

        if target_strat_str in COMBS_PREDICTORS_DICT.keys() and comb_profits:
            comb_conf = COMBS_PREDICTORS_DICT[target_strat_str]
            preds_for_comb_0 = [x for x in comb_conf['preds_for_comb'] if x != 'MEAN']

            a = _impute_shifted_features(a, preds_for_comb_0)


            try:
                a['MEAN'] = (a[preds_for_comb_0]/a[preds_for_comb_0].std()).mean(axis=1)
            except:
                print ('Failed on strat: %s .... Skipping'%target_strat_str)
                if DEBUG_MODE:
                    raise
                continue
        else:
            comb_conf = {'start': default_start,
                         'chosen_ratio': default_chosen_ratio,
                         'preds_for_comb': []}
        #chosen_ratio = default_chosen_ratio #
        chosen_ratio = comb_conf['chosen_ratio'] #  # todo
        start2 = comb_conf['start']

        chosen_preds = [] #wti_preds#bollingers_preds #daily_final#['NCF_open-open-1_-1d','NCF_open-close-1_-1d','NCF_open-open-2_-1d'] # ['TTF_open-Prev1to3','TTF_open-Prev1to7','TTF_close-1-Prev1to3', 'TTF_close-1-Prev1to3']
        if len(chosen_strats) > 1:
            chosen_preds = []
        for x in chosen_preds:
            if x not in list(a) and x[:-1] in list(a) and x.endswith('-'):
                a[x] = -a[x[:-1]]
            elif x.endswith('+1d') and x not in list(a):
                a[x] = a[x[:-3]].shift(-1)
            elif x.endswith('-1d') and x not in list(a) and 'shift' not in x:
                a[x] = a[x[:-3]].shift(1)
        # a[chosen_preds] -= a[chosen_preds].mean()
        #
        # a[(a['date'].dt.weekday.isin([4])) & (a['date'] >= dtdt(2020, 4, 15))].corr()[['y_1245-1345','y_1245-1300','y_1245-1315','y_1300-1330','y_1315-1415']].loc[chosen_preds+['EPS45_pred2','EPS45_rolling3']]
        if mode != 'prod':
            stability_col = 'std_0-16_full_rs'
            #a = a[(a[stability_col] > 0)] #a[~(a['std_0-16_rs'] > -0.1)&(a['std_0-16_rs'] < 0.1)]
        seasonal_preds = ['Value_0Z_0-0_EC_rs'] #
        for d in [5,10,15]:
            seasonal_preds += ['Value_0Z_0-0_EC_rs_ma_%s'%d,'Value_0Z_0-0_EC_rs_ma_%s_future'%d,'Value_0Z_0-0_EC_rs_ma_%s_past+future'%d]
        seasonal_preds = ['Value_0Z_0-16D_%s_rs'%model for model in ['GEPS','GEMCO','GEFS','PARA','PARACO','GFSCO','GEFSL','CFS']]+\
                            ['Value_0Z_0-13D_EPS_rs']
        try:
            a[seasonal_preds] -= a[seasonal_preds].mean()
            a[['std_0-16_full_rs','std_0-16_american_rs']] *= -1
        except:
            print('Couldnt nomralize seasonal preds.... skipping ')

        results_df,last_action_not_used = wrap_predictor_handling(a,target_strat_str, target_strat, comb_conf, comb_profits, j,
                            default_end,weekdays_for_thresh_calc,weekdays, results_df=results_df)
    if comb_profits and len(comb_conf['preds_for_comb']):
        print('Trades Ratio for Comb: %s'%(results_df['profit_%s_combined_%s'%(target_strat,j)]!=0).mean())
        chosen_action = 'I'
        for prd in comb_conf['preds_for_comb']:
            local_action = 'I'
            if results_df['is_trade_%s_%s'%(target_strat,prd)].iloc[-1] == 1:
                local_action = 'B' if results_df[prd].iloc[-1] > 0 else 'S'
            # results_df = add_probability(results_df,prd,'prob_%s'%prd,'date')
            prd_str = ('-' if results_df[prd].iloc[-1] < 0 else '+') #+'%s'%round(results_df['prob_%s'%prd].iloc[-1])+'%'

            print ('%s | Strategy: %s | Pred: %s | Action = %s (%s)'%(results_df['date'].iloc[-1],target_strat_str,prd,local_action,prd_str))
            if chosen_action == 'I' and local_action != 'I':
                chosen_action = local_action
        print ('-----------')
        print ('%s | Strategy: %s | Comb_Pred | Action = %s (%s)'%(results_df.iloc[-1]['date'],target_strat_str,chosen_action,prd_str))

        results_df = results_df.loc[:, ~results_df.columns.duplicated()]

        #### todo filtering preds
        results_df = add_probability(results_df, 'MEAN', 'prob_MEAN', 'date')
        if mode != 'prod':
            pass
            # results_df = results_df[(np.sign(results_df['MEAN']*results_df['GEFSL_daily_hybrid_pred2']) == -1)]
            results_df['Naive_MEAN_action'] = results_df['MEAN'].apply(lambda x: 'B' if x > 0 else 'S')
            #results_df = results_df[results_df['Naive_MEAN_action']!= results_df['action']]

        mean_action = 'B' if results_df['MEAN'].iloc[-1] > 0 else 'S'
        mean_sign = '+' if results_df['MEAN'].iloc[-1] > 0 else '-'
        if results_df['is_trade_%s_%s'%(target_strat,'MEAN')].iloc[-1] == False:
            mean_action = 'I'
        try:
            mean_prob = round(results_df.iloc[-1]['prob_MEAN'])
        except:
            mean_prob = np.nan

        print('%s | Strategy: %s | MEAN | Action = %s (%s%s)' % (results_df.iloc[-1]['date'], target_strat_str, mean_action,mean_sign,str(mean_prob)+"%"))
        if mode == 'prod':
            return results_df,chosen_action
    if not mode == 'prod':

        if j == 0:
            results_df.set_index('date')[[x for x in list(results_df) if 'profit' in x and cond_for_plot(x)]].fillna(0).cumsum().plot(style=['-']*7+['--']*7+['-*']*7)
        else:
            results_df.set_index('date')[[x for x in list(results_df) if 'profit' in x and 'combined' in x and cond_for_plot(x)]].fillna(0).cumsum().plot(style=['-'] *7 + ['--'] * 7 + ['-*']*7)
        weekday_cols_to_plot = [x for x in list(results_df) if 'profit' in x]
        if len([x for x in weekday_cols_to_plot if 'combined' in x])> 1:
            weekday_cols_to_plot = [x for x in weekday_cols_to_plot if 'combined' in x]
        results_df.groupby(results_df['date'].dt.weekday).mean()[weekday_cols_to_plot].plot(title='PPT by weekday',kind='bar')
        plt.show()

        profit_cols = [x for x in list(results_df) if 'profit' in x]
        results_df[profit_cols]= results_df[profit_cols].fillna(0)
        results_df['profit'] = results_df[profit_cols].sum(axis=1)
        results_df = results_df.set_index('date')
        c = 'profit' #[x for x in profit_cols if 'GEFS' in x][0]
        results_df['cumsum_PNL'] = results_df[c].cumsum()
        results_df["drawdown"] = results_df['cumsum_PNL'] - results_df['cumsum_PNL'].cummax()

        results_df.reset_index().groupby(results_df.reset_index()['date'].dt.month).mean()[[x for x in list(results_df) if 'profit' in x]].plot(title='PPT by Month', kind='bar')
        results_df[['cumsum_PNL']].plot(title='Cummulative PNL Combined strategies')
        plt.show()
        results_df[['drawdown']].plot(title='Combined strategies | Drawdown along the time',kind='bar')
        plt.show()

        #a[a['date'] >= dtdt(2020, 3, 10)][preds_for_comb + ['y_1030-1100', 'y_0930-1100', 'y_1000-1030', 'y_0900-1100', 'y_0800-1100']].corr()[['y_1030-1100', 'y_0930-1100', 'y_1000-1030', 'y_0900-1100', 'y_0800-1100']]
        aa = 1
    #results_df.reset_index()[['date','profit_y_0800-1745_EPSL_daily_hybrid_pred4']].to_csv(os.path.join(HOME,"performance_analysis","days_to_exp","","EPSL_pred4_PNL_D2Exp=9(normal).csv"),index=False)
    return results_df, None

def cash_analysis(cash_yesterday_hack=False,plot=False):
    cash_df = pd.read_csv(os.path.join(HOME,"Market_Data","Cash Prices","cash_prices.csv"),
                          parse_dates=['report_date'])
    if cash_yesterday_hack:
        cash_df['date'] = cash_df['report_date'] - td(days=1) +td(hours=8) #
    else:
        cash_df['date'] = cash_df['report_date'] +td(hours=8) #

    cash_df = cash_df.sort_values('date')
    cash_df = cash_df.rename(columns={'price':'price_cash'})
    cash_df['y_cash-1d'] = cash_df['price_cash'].diff()
    cash_df['y_cash-2d'] = cash_df['price_cash'].diff(2)
    cash_df['y_cash_-1-2d'] = [np.nan]+cash_df['y_cash-1d'][:-1].tolist()
    cash_df['y_cash_0-ma5'] = cash_df['price_cash'] - cash_df['price_cash'].rolling(5,1).mean()
    cash_df['y_cash_-1-ma5'] = [np.nan]+cash_df['y_cash_0-ma5'][:-1].tolist()
    ng_prices = pd.read_csv(os.path.join(HOME,"Market_Data","NG_2018-19_frontMonth_tz_Live.csv"),
                            parse_dates=['time_utc'])[['time_utc','open']].rename(columns={'time_utc':'date',
                                                                                           'open':'prompt_price'})
    h = 8
    ng_prices = ng_prices[(ng_prices['date'].dt.hour == h)&(ng_prices['date'].dt.minute == 0)]
    ng_prices['date'] -= td(hours=h-8)
    cash_df = cash_df.merge(ng_prices,on=['date'],how='outer')
    cash_df['diff_cashVprompt'] = cash_df['prompt_price'] - cash_df['price_cash']
    cash_df['diff_cashVprompt_diff1'] = cash_df['diff_cashVprompt'].diff(1)
    cash_df['prompt_ref_contract'] = cash_df['date'].apply(lambda x: (x+td(days=42)).replace(day=1))

    #monthly_prompt_avgs = cash_df.groupby('prompt_ref_contract').mean()['prompt_price'].reset_index()
    months_stack = []
    prices_stack = []
    for month, group in cash_df.groupby('prompt_ref_contract'):
        last_price = group.iloc[-1]['prompt_price']
        months_stack.append(month)
        prices_stack.append(last_price)
    aaa = 1
    monthly_prompt_avgs = pd.DataFrame(pd.Series(prices_stack,index=months_stack,name='prompt_price')).reset_index().rename(columns={'index':'prompt_ref_contract'})
    cash_df['prompt_avg_price'] = cash_df['date'].apply(
        lambda x: monthly_prompt_avgs[monthly_prompt_avgs['prompt_ref_contract'] == x.replace(day=1, hour=8)].iloc[0][
            'prompt_price'] if monthly_prompt_avgs[monthly_prompt_avgs['prompt_ref_contract'] == x.replace(day=1, hour=8)].shape[0] > 0 else np.nan)
    if plot:
        cash_df[['date','prompt_price','price_cash','prompt_avg_price']].plot(x='date')
    cash_df['diff_cashVsettle'] = cash_df['price_cash'] - cash_df['prompt_avg_price']
    cash_df['diff_cashVsettle_diff1'] = cash_df['diff_cashVsettle'].diff()

    cash_df['diff_cashVsettle-1d'] = cash_df['diff_cashVsettle'].shift(1)
    cash_df['diff_cashVsettle_diff1-1d'] = cash_df['diff_cashVsettle_diff1'].shift(1)
    cash_df['diff_cashVprompt-1d'] = cash_df['diff_cashVprompt'].shift(1)
    cash_df['diff_cashVprompt_diff1-1d'] = cash_df['diff_cashVprompt_diff1'].shift(1)

    return cash_df

def add_gefs12z_gaps(a):
    a['gap_0-16_PARAACO6z-GEFS0z'] = a[['Value_6Z_0-16_PARA', 'Value_6Z_0-16_PARACO']].mean() - a['Value_0Z_0-16_GEFS']
    a['gap_0-16_PARACO6z-GEFS0z'] = a[['Value_6Z_0-16_PARACO']].mean() - a['Value_0Z_0-16_GEFS']
    a['gap_0-16_PARA6z-GEFS0z'] = a[['Value_6Z_0-16_PARA']].mean() - a['Value_0Z_0-16_GEFS']

    a['gap_0-16_PARAACO6z-GEFS12z'] = a[['Value_6Z_0-16_PARA', 'Value_6Z_0-16_PARACO']].mean(axis=1) - a[
        'Value_12Z_0-16_GEFS']
    a['gap_0-16_PARACO6z-GEFS12z'] = a['Value_6Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_PARA6z-GEFS12z'] = a['Value_6Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_PARAACO0z-GEFS12z'] = a[['Value_0Z_0-16_PARA', 'Value_0Z_0-16_PARACO']].mean(axis=1) - a[
        'Value_12Z_0-16_GEFS']
    a['gap_0-16_PARACO0z-GEFS12z'] = a['Value_0Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_PARA0z-GEFS12z'] = a['Value_0Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_PARAACO18z-GEFS12z'] = a[['Value_18Z_0-16_PARA', 'Value_18Z_0-16_PARACO']].mean(axis=1) - a[
        'Value_12Z_0-16_GEFS']
    a['gap_0-16_PARACO18z-GEFS12z'] = a['Value_18Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_PARA18z-GEFS12z'] = a['Value_18Z_0-16_PARACO'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-13_EPS0z-GEFS12z'] = a['Value_0Z_0-13_EPS'] - a['Value_12Z_0-13_GEFS']
    a['gap_0-13_EPS12z-GEFS12z'] = a['Value_12Z_0-13_EPS'] - a['Value_12Z_0-13_GEFS']
    a['gap_0-16_GEPS0z-GEFS12z'] = a['Value_0Z_0-16_GEPS'] - a['Value_12Z_0-16_GEFS']
    a['gap_0-16_GEPS12z-GEFS12z'] = a['Value_12Z_0-16_GEPS'] - a['Value_12Z_0-16_GEFS']
    return a

#xy_template_normal = os.path.join(HOME,"XYs","Full2019","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_take4.csv")
xy_template_normal = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_take5_old.csv")
XY_PREV24_TEMPLATE = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_take7_prev24.csv")
XY_PREV24_BOLLINGER_TEMPLATE = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_take7_prev24_PrevYs+Bollinger_v1.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V2 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_take7_prev24_PrevYs+Bollinger_v2.csv")

# V3 : added mcad trend and location diffs
XY_PREV24_BOLLINGER_TEMPLATE_V3 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_prev24_PrevYs+Bollinger_v3.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V4_OPEN = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_prev24_PrevYs+Bollinger_v4Open.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V4_OPEN_FULL = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_prev24_PrevYs+Bollinger_v4Open.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V4_OPEN_FULL_WEIGHTED = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_Weighted_prev24_PrevYs+Bollinger_v4Open.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V5_OPEN_EXT = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_prev24_PrevYs+Bollinger_ExtOpen.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V5_OPEN_EXT_FULLMONTHS = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_ExtOpen.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V5_OPEN_EXT_WEIGHTED = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[10-4]_takeX_Weighted_prev24_PrevYs+Bollinger_ExtOpen.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_V3d10.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS_D2EXP30 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_V3d10_D2Exp=30.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS_D2EXP2 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_V3d10_D2Exp=2.csv")
XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS_D2EXP5 = os.path.join(HOME,"XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_V3d10_D2Exp=5.csv")
XY_CORN_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS = os.path.join(HOME,"CORN","XYs","Full2019_prev24","XY_file_<h>_15Mins_utc_DailyDiffs_Full_lastModel=<last>_daysAhead=<da>_Months=[1-12]_takeX_prev24_PrevYs+Bollinger_V3d10.csv")
TEMPLATES = {'NG': XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS,
             'CORN': XY_CORN_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS}

# hack template
#TEMPLATES['NG'] = XY_PREV24_BOLLINGER_TEMPLATE_V3d10_OPEN_EXT_FULLMONTHS_D2EXP30

xy_GFS_0Z = os.path.join(HOME,"XYs","Full2019","XY_file_0345-0615_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=0_daysAhead=%s_Months=[10-4]_take4.csv")%days_ahead
xy_opening = os.path.join(HOME,"XYs","XY_file_0915-1145_15Mins_Chicago_DailyDiffs_GFS+PARA+GEFS_lastModel=12_daysAhead=[1-15]_Months=[10-4]_take2.csv")
xy_GFS_post_6Z = os.path.join(HOME,"XYs","XY_file_1245-1345_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=12_daysAhead=[1-15]_Months=[10-4]_take2.csv")
xy_GFS_6Z = os.path.join(HOME,"XYs","XY_file_0945-1145_15Mins_utc_DailyDiffs_GFS+PARA+GEFS_lastModel=6_daysAhead=[1-15]_Months=[10-4]_take2.csv")


if __name__ == '__main__':
    # start = dtdt(2019,7,1)
    start = dtdt(2021,4,1)
    end = dtdt.today()-td(days=0) #+td(days=1)
    mode = 'test'
    # DEBUG_MODE = True
    send_email = True

    # suffix = 'v10_wMidwest'
    # suffix = 'v8_0ZbWeights'
    suffix = 'v8_0Zb'

    import time
    # time.sleep(60*7)
    # suffix = 'v8_12Z'

    # mode = 'dry'
    calc_start_override = None #dtdt(2021,11,1) # None
    # calc_start_override = dtdt(2023,9,1) # None
    base_on_existing = True
    # base_on_existing = False
    run_analysis = not mode == 'test'

    for ft in ['GDD','CDD','HDD'][:1]:
        big_model_hour = None
        if '12Z' in suffix:
            big_model_hour = 1
            big_model_hour = 0

        ASSET = 'NG'
        corn_hack = False
        PLOT_ENRICHMENT = True

        strategy = '0800-1100'
        calc_As = 1
        print_preds = 0

        if mode == 'dry':
            calc_As = 0

        d = pd.read_csv(os.path.join(HOME,"Market_Data","new1.txt"),parse_dates=['time_utc'])
        if mode == 'dry2':
            strategy = '1415-1745'
            calc_As = 0

        a = main_wrapper(conf_name=mode, last_model=12, strategy=strategy, calc_As=calc_As, print_predies=print_preds, suffix=suffix, ft=ft,
                     send_email=send_email,start=start,end=end,big_model_hour=big_model_hour,asset=ASSET,
                         base_on_existing=base_on_existing,calc_start_override=calc_start_override)

        try:
            a['is_hit_EPS_1130'] = np.sign(a['EPS_daily_hybrid_pred1']*a['y_1130-1745']).apply(lambda x: 0 if x < 0 else x)
            a['is_hit_EPS_1415'] = np.sign(a['EPS_daily_hybrid_pred1'] * a['y_1415-1745']).apply(lambda x: 0 if x < 0 else x)
            a['is_hit_EPS_1130-1315'] = np.sign(a['EPS_daily_hybrid_pred1'] * a['y_1130-1315']).apply(lambda x: 0 if x < 0 else x)
            a['is_hit_EPSpost_8-11'] = np.sign(a['EPSpost_pred2b'] * a['y_0800-1100']).apply(lambda x: 0 if x < 0 else x)
            a['is_hit_EPS_last-1_0730-0800'] = np.sign(a['diff_0Z_0-13_last-Prev1_EPS'] * a['y_0730-0800']).apply(lambda x: 0 if x < 0 else x)
            a['is_hit_EPS_last-1_0800-1100'] = np.sign(a['diff_0Z_0-13_last-Prev1_EPS'] * a['y_0800-1100']).apply(lambda x: 0 if x < 0 else x)
            a[(a['date'].apply(lambda x: x.month not in [11, 12, 1, 2])) & (a['is_hit_EPS_1130-1315'] == 0)][[x for x in list(a) if 'is_hit' in x]].mean()
        except:
            pass

        a['Value_0Z_0-16_PARA_-ma5'] = a['Value_0Z_0-16_PARA'] - a['Value_0Z_0-16_PARA'].rolling(5, 5).mean()
        a['Value_0Z_0-8_PARA_-ma5'] = a['Value_0Z_0-8_PARA'] - a['Value_0Z_0-8_PARA'].rolling(5, 5).mean()
        a['Value_0Z_0-8_PARA_-ma15'] = a['Value_0Z_0-8_PARA'] - a['Value_0Z_0-8_PARA'].rolling(15, 5).mean()
        a['Value_0Z_0-16_PARACO_-ma5'] = a['Value_0Z_0-16_PARACO'] - a['Value_0Z_0-16_PARACO'].rolling(5, 5).mean()
        a['Value_0Z_0-16_GEFS_-ma5'] = a['Value_0Z_0-16_GEFS'] - a['Value_0Z_0-16_GEFS'].rolling(5, 5).mean()
        a['Value_0Z_0-16_GEFSL_-ma5'] = a['Value_0Z_0-16_GEFSL'] - a['Value_0Z_0-16_GEFSL'].rolling(5, 5).mean()

        a['Value_0Z_8-16_PARA_-0-0ma10'] = a['Value_0Z_8-16_PARA'] - a['Value_0Z_0-0_PARA'].rolling(10, 5).mean()
        a['Value_0Z_8-16_PARA_-0-0ma20'] = a['Value_0Z_8-16_PARA'] - a['Value_0Z_0-0_PARA'].rolling(20, 5).mean()
        a['Value_0Z_0-16_PARA_-0-0ma10'] = a['Value_0Z_0-16_PARA'] - a['Value_0Z_0-0_PARA'].rolling(10, 5).mean()
        a['Value_0Z_0-8_PARA_-0-0ma10'] = a['Value_0Z_0-8_PARA'] - a['Value_0Z_0-0_PARA'].rolling(10, 5).mean()
        a['Value_0Z_0-8_PARA_-0-0ma15'] = a['Value_0Z_0-8_PARA'] - a['Value_0Z_0-0_PARA'].rolling(15, 5).mean()
        a['Value_0Z_0-16_PARACO_-0-0ma10'] = a['Value_0Z_0-16_PARACO'] - a['Value_0Z_0-0_PARACO'].rolling(10, 5).mean()
        a['Value_0Z_0-16_GEFS_-0-0ma10'] = a['Value_0Z_0-16_GEFS'] - a['Value_0Z_0-0_GEFS'].rolling(10, 5).mean()
        a['Value_0Z_0-16_GEFSL_-0-0ma10'] = a['Value_0Z_0-16_GEFSL'] - a['Value_0Z_0-0_GEFSL'].rolling(10, 5).mean()

        a['Value_0Z_0-13D_EPS_-ma5'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-13D_EPS'].rolling(5,1).mean()
        a['Value_0Z_0-13D_EPS_-ma5_0-0'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-0_EPS'].rolling(5, 1).mean()
        a['Value_0Z_0-16D_GEPS_-ma5'] = a['Value_0Z_0-16D_GEPS'] - a['Value_0Z_0-16D_GEPS'].rolling(5, 1).mean()
        a['Value_0Z_0-8_EPS_-ma5'] = a['Value_0Z_0-8_EPS'] - a['Value_0Z_0-8_EPS'].rolling(5, 1).mean()
        a['Value_0Z_0-8_GEFS_-ma5'] = a['Value_0Z_0-8_GEFS'] - a['Value_0Z_0-8_GEFS'].rolling(5, 1).mean()
        a['Value_0Z_0-16D_GEFS_-ma5_0-0'] = a['Value_0Z_0-16D_GEFS'] - a['Value_0Z_0-0_GEFS'].rolling(5, 1).mean()
        a['Value_0Z_0-8_GEFSL_-ma5'] = a['Value_0Z_0-8_GEFSL'] - a['Value_0Z_0-8_GEFSL'].rolling(5, 1).mean()
        a['Value_0Z_0-16D_GEFSL_-ma5_0-0'] = a['Value_0Z_0-16D_GEFSL'] - a['Value_0Z_0-0_GEFSL'].rolling(5, 1).mean()

        a['Value_0Z_0-13D_EPS_-ma7_0-0'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-0_EPS'].rolling(7, 1).mean()
        a['Value_0Z_0-13D_EPS_-ma4_0-0'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-0_EPS'].rolling(4, 1).mean()
        a['Value_0Z_0-13D_EPS_-ma3_0-0'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-0_EPS'].rolling(3, 1).mean()
        a['Value_0Z_0-13D_EPS_-ma2_0-0'] = a['Value_0Z_0-13D_EPS'] - a['Value_0Z_0-0_EPS'].rolling(2, 1).mean()

        summertime_cond = a['date'].dt.month.isin(range(4, 11)) | a['date'].apply(
            lambda x: (x.month == 11 and x.day < 10) or (x.month == 3 and x.day > 10))
        a['y_0900-0915_Eastern'] = a['y_1300-1315']  # a['y_1245-1415']
        a['y_0915-0930_Eastern'] = a['y_1315-1330']  # a['y_1245-1415']
        a['y_0900-0930_Eastern'] = a['y_1300-1330']  # a['y_1245-1415']
        a.loc[~summertime_cond, 'y_0900-0915_Eastern'] = a.loc[~summertime_cond, 'y_1400-1415']
        a.loc[~summertime_cond,'y_0915-0930_Eastern'] = a.loc[~summertime_cond,'y_1415-1430']
        a.loc[~summertime_cond, 'y_0900-0930_Eastern'] = a.loc[~summertime_cond, 'y_1400-1430']

        ### teleconnections hack
        teleconnection_xs_csv = r'C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\teleconnections\teleconnections_diffs_Xs.csv'
        teleconnection_xs = pd.read_csv(teleconnection_xs_csv,parse_dates=['date'])
        # teleconnection_xs = teleconnection_xs[['date']+[x for x in list(teleconnection_xs) if '' in x]]
        a = a.merge(teleconnection_xs,on='date',how='left')
        #####

        try:
            a['ran1'] = a['Value_0Z_0-0_EC_rs'] - a['Value_0Z_0-0_EC_rs_ma_15']
            a['ran2'] = a['Value_0Z_0-13D_EPS_rs'] - a['Value_0Z_0-0_EC_rs_ma_15']
        except:
            pass

        a['y_0800-1200_with16151745'] = a['y_0800-1200']+a['y_1615-1745']

        for c in ['macd_4H','macd_2H','macd_1H','macd_1D']:
            a['%s_diff1'%c] = a[c].diff()
            a['%s_diff4'%c] = a[c].diff(4)
            a['%s_trend4'%c] = a[c] - a[c].rolling(4,4).mean()
            a['%s_trend8'%c] = a[c] - a[c].rolling(8, 8).mean()
        for c in ['diff_0Z_14-35_last-Prev1D_GEFS35','diff_0Z_0-21_last-Prev1D_GEFS35','diff_0Z_0-21_last-Prev2D_GEFS35','diff_0Z_14-28_last-Prev1D_GEFS35','diff_0Z_14-21_last-Prev1D_GEFS35',
                  'diff_0Z_14-28_last-Prev2D_GEFS35','diff_0Z_14-35_last-Prev2D_GEFS35',
                  'diff_0Z_0-21_last-Prev1D_GEFS35','diff_0Z_0-21_last-Prev2D_GEFS35',
                  'diff_12Z_0-16_last-Prev1_GEPS','diff_12Z_0-16_last-Prev2_GEFS']:
            a[c+'+1d'] = a[c].shift(-1)
            a[c + '-1d'] = a[c].shift(1)

        a['Value_0Z_0-13D_EPS_diff1_shift-1d'] = a['Value_0Z_0-13D_EPS'].diff(1).shift(1)
        a['Value_0Z_0-13D_EPS_diff2_shift-1d'] = a['Value_0Z_0-13D_EPS'].diff(2).shift(1)
        a['Value_0Z_0-13D_EPS_diff1'] = a['Value_0Z_0-13D_EPS'].diff(1)#.shift(1)
        a['Value_0Z_0-13D_EPS_diff2'] = a['Value_0Z_0-13D_EPS'].diff(2)#.shift(2)


        a['y_concated'] = a[['y_1000-1200', 'y_1200-1500']].sum(axis=1)
        a['diff_12Z_0-8_last-Prev1_EC+1d'] = a['diff_12Z_0-8_last-Prev1_EC'].shift(-1)

        a['diff_0Z_14-35_last-Prev7D_CFS'] = (a['Value_0Z_14-21D_CFS'] + a['Value_0Z_21-35D_CFS'] * 2) - (a['Value_0Z_21-42D_CFS']*3).shift(7)
        a['diff_0Z_14-35_last-Prev7D_CFSCO'] = (a['Value_0Z_14-21D_CFSCO'] + a['Value_0Z_21-35D_CFSCO'] * 2) - (a['Value_0Z_21-42D_CFSCO']*3).shift(7)
        a['diff_6Z_14-35_last-Prev7D_CFSCO'] = (a['Value_6Z_14-21D_CFSCO'] + a['Value_6Z_21-35D_CFSCO'] * 2) - (a['Value_0Z_21-42D_CFSCO']*3).shift(7)
        a['diff_12Z_14-35_last-Prev7D_CFSCO'] = (a['Value_12Z_14-21D_CFSCO'] + a['Value_12Z_21-35D_CFSCO'] * 2) - (a['Value_0Z_21-42D_CFSCO']*3).shift(7)
        a['diff_0Z_14-28_last-Prev7D_CFS'] = a['Value_0Z_14-28D_CFS'] - a['Value_0Z_21-35D_CFS'].shift(7)
        a['diff_0Z_14-28_last-Prev7D_CFSCO'] = a['Value_0Z_14-28D_CFSCO'] - a['Value_0Z_21-35D_CFSCO'].shift(7)
        a['diff_6Z_14-28_last-Prev7D_CFSCO'] = a['Value_6Z_14-28D_CFSCO'] - a['Value_6Z_21-35D_CFSCO'].shift(7)
        a['diff_12Z_14-28_last-Prev7D_CFSCO'] = a['Value_12Z_14-28D_CFSCO'] - a['Value_12Z_21-35D_CFSCO'].shift(7)

        a['diff_0Z_0-8_last-Prev7D_PARA'] = a['Value_0Z_0-10D_PARA'] - a['Value_0Z_8-16D_PARA'].shift(7)
        a['diff_0Z_0-8_last-Prev7D_PARACO'] = a['Value_0Z_0-10D_PARACO'] - a['Value_0Z_8-16D_PARACO'].shift(7)
        a['diff_0Z_0-8_last-Prev7D_GEFS'] = a['Value_0Z_0-10D_GEFS'] - a['Value_0Z_8-16D_GEFS'].shift(7)

        a['y_1200-1500_-1d'] = a['y_1200-1500'].shift(1) #/ a['y_1200-1500'].std()
        a['y_1230-1330_-1d'] = a['y_1230-1330'].shift(1) #/ a['y_1230-1330'].std()
        a['y_1245-1415_-1d'] = a['y_1245-1415'].shift(1) #/ a['y_1245-1415'].std()
        a['y_1415-1500_-1d'] = a['y_1415-1500'].shift(1) #/ a['y_1415-1500'].std()

        a['y_1100-1415_-1d'] = a['y_1100-1415'].shift(1) #/ a['y_1100-1415'].std()

        # _get_potential_candidates_for_strat(a, [0,1,2,3,4], 'y_0800-1845', 1, top=40,
        #                                            start=dtdt(2022, 12, 15),stds_required=0)

        a['diff_12Z_0-16_last-Prev2_GEFS+1d'] = a['diff_12Z_0-16_last-Prev2_GEFS'].shift(-1)
        a['diff_12Z_0-16_last-Prev4_GEFS+1d'] = a['diff_12Z_0-16_last-Prev4_GEFS'].shift(-1)

        # epsco_extra = pd.read_csv(os.path.join(HOME,"Xdata","X_file_DailyDiffs_EPSCO_v3.0_0Z.csv"),
        #                           parse_dates=['date'])
        # a = a.merge(epsco_extra[['date']+[x for x in list(epsco_extra)
        #                          if x not in list(a)]],on=['date'],how='outer')
        #
        # for c in ['diff_0Z_0-15_last-Prev1_EPSCO_population_US','diff_0Z_0-15_last-Prev2_EPSCO_population_US',
        #                          'diff_12Z_0-15_last-Prev1_EPSCO_population_US','diff_12Z_0-15_last-Prev2_EPSCO_population_US']:
        #     a[c+'+1d'] = a[c].shift(-1)
        #     a[c+'-1d'] = a[c].shift(1)
        #
        # a['diff_0Z_0-10_last-Prev1_EPSCO_population_US'] = a['diff_0Z_0-15_last-Prev1_EPSCO_population_US'] - 0.33*a['diff_0Z_11-15_last-Prev1_EPSCO_population_US']
        # a['diff_0Z_0-10_last-Prev2_EPSCO_population_US'] = a['diff_0Z_0-15_last-Prev1_EPSCO_population_US'] - 0.33*a['diff_0Z_11-15_last-Prev2_EPSCO_population_US']

        # production_df = pd.read_csv(os.path.join(HOME,"General_Stocks","Natural Gas, Crude Oil and Forex Fundamentals - Balance .csv"),
        #                             parse_dates=['Date'])
        #     # b = a[['date']+[x for x in list(a) if x.startswith('y_')]]
            # c = production_df.rename(columns={'Date':'date','Dry Gas Production':'production'})[['date','production']]
            # shift = 0
            # c['date'] = pd.to_datetime(c['date'],errors='coerce') + td(hours=8)
            # c['production'] = pd.to_numeric(c['production'].str.replace(',',''),errors='coerce')
            # c['production_diff1'] = c['production'].diff().shift(shift)
            # c['production_diff2'] = c['production'].diff(2).shift(shift)
            # c['production_diff3'] = c['production'].diff(3).shift(shift)
            # c['production_diff1to3'] = pd.Series(np.mean([c['production'].diff(i) for i in range(1,4)],axis=0),index=c.index.tolist())
            # c['production_diff1to5'] = pd.Series(np.mean([c['production'].diff(i) for i in range(1,6)],axis=0),index=c.index.tolist())
            # c['production_diff1to7'] = pd.Series(np.mean([c['production'].diff(i) for i in range(1,8)],axis=0),index=c.index.tolist())
            # for col in list(c):
            #     if 'production' in c:
            #         c[col+'_+1d'] = c[col].shift(-1)
            #         c[col+'_-1d'] = c[col].shift(1)
            #         c[col+'_-2d'] = c[col].shift(2)
            #         c[col+'_-3d'] = c[col].-shift(3)
            # c = c.set_index('date').shift(2).reset_index()
            # a = a.merge(c,on=['date'])
        # corrs = d[[x for x in list(d) if 'production' in x or x in ['y_0800-1200', 'y_0800-1415', 'y_1100-1415', 'y_1415-1845',
        #                                                     'y_0800-1845']]].corr()

        # a[a['date'] >= dtdt(2022, 2, 1)][
        #     [x for x in list(a) if x.startswith('diff_') and ('0-16' in x or '0-15' in x)] + ['y_0000-0800',
        #                                                                                       'y_0600-0800']].corr()[
        #     ['diff_0Z_0-15_last-Prev2_EPSCO_population_US', 'y_0000-0800', 'y_0600-0800']].sort_values(
        #     'diff_0Z_0-15_last-Prev2_EPSCO_population_US')
        # clusters_df = pd.read_csv(os.path.join(HOME,"degdays_archive","Clusters","Cluster_models_Diffs_MaxHours=48_Ratio=4.0_v2.csv"),
        #                           parse_dates=['date'])
        # print(list(clusters_df))
        if run_analysis:
            main_analysis(a, start, end, stds_hack=False)  # ,'y_1100-1230_123',mode='prod')
            # main_analysis(b, start, end, stds_hack=False)  # ,'y_1100-1230_123',mode='prod')
            # to do