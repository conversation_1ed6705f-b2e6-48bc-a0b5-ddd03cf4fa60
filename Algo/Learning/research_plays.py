import matplotlib
from matplotlib import pyplot as plt
matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import time
import os
import json

from Algo.Learning.algo_wrapper2 import main_analysis, main_wrapper
from Algo.Learning.thin_analysis_tool import wrap_results_df, convert_predictor_results_to_new_strat
from Algo.Learning.decision_maker import strats_with_mean_action,strat_by_day_dict,ignore_contradictions_strats,temporarilyy_ignored_predictors,strats_with_specific_months_ignoring,summer_to_winter_mapping,special_predictors_to_avoid_mapping
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe,get_drawdown_vector
from Algo.Learning import decision_maker
from Algo.Learning.search_existing_strategies import get_strategies_list_v1, _split_preds
from Algo.Tests.check_starts_format import test_strategies_structure
from Algo.Utils.general import get_last_sunday

LOCAL_TZ = pytz.timezone("Asia/Jerusalem")
import os
from Algo.Utils.files_handle import HOME, COMPACT_FILTERING_DIR
from Algo.Conf import current_week_constants as cwc

DROP_IGNORED_IN_ANALYSIS = False
ONLY_IGNORED_IN_ANALYSIS = False # not DROP_IGNORED_IN_ANALYSIS

USE_OLD_CALCULATION = False
SHOW_NULL_PROFITS = False # True
GROUP_PREDICTORS = True
GROUP_PREDICTORS_BY_WEEKDAY = False # todo continue implementation
if GROUP_PREDICTORS and GROUP_PREDICTORS_BY_WEEKDAY:
    print("Warning - GROUP_PREDICTORS and GROUP_PREDICTORS_BY_WEEKDAY are both True. This is not recommended. We will first try to run on basic GROUPING")
# we have difference between the modes... needs comparison of the 2 csvs

# check for misses of the auto-selection:
# 1. 12Best x 7D x Last0
# 2. 12Best x 15D x Last0
# 3. 12Best x 25D x Last0
# 4. 12Best x 15 x Last4

ONLY_BEST = 0 # 12
DAYS_FOR_BEST = 10 # 25
LAST_TAKEN_DAY = 0
USE_SLIDING_WINDOW = False
SLIDE_WINDOW_SIZE = 1
USE_HR = False
PPT_OR_SUM = 'sum' #'calmar' #'sum' #'sum'
PLOT = True

### IMPORTANT!
# COST_PER_TRADE = 0.75
COST_PER_TRADE = 0.75
SHIFT_FOR_CHOSEN_RATIOS = 0

RECENT_DAYS_FOR_DROPPING_FROM_PROD = 0
BAD_PPT_VALUE = 10 # ticks | used to be 0 but we want little moves not to drop good strat
BAD_HR_VALUE = 0.1 # ticks | used to be 0 but we want little moves not to drop good strat
DEEP_ANALYSIS = False # decide whether to analyze weekdays
USE_CACHE = True

SKIP_IGNORED = True
REDUCE_EPSILON_FROM_ALL = False
if PPT_OR_SUM == 'calmar':
    REDUCE_EPSILON_FROM_ALL = True

CALMAR_CALC_FILL_NANS = True
CALMAR_EPSILON_REDUCTION_VALUE = 0.1

FILTER_BY_PRODUCTION_COND = False
FILTER_BY_EXCEPTIONS_ONLY = False
FILTER_BY_STAGE_ONLY = False
assert FILTER_BY_EXCEPTIONS_ONLY+FILTER_BY_PRODUCTION_COND+FILTER_BY_STAGE_ONLY <= 1

if ONLY_BEST != 0:
    FILTER_BY_PRODUCTION_COND = False
if ONLY_BEST < 0:
    USE_SLIDING_WINDOW = False
MODE = 'paper'  #
#MODE = 'real'


USE_WINTER_MAPPING = decision_maker.USE_WINTER_MAPPING

LAST_UPDATED_TOP = decision_maker.LAST_UPDATED_TOP
STRATS_BY_DAY_DICT = decision_maker.strat_by_day_dict
ALL_CONFS_COMBINED_BY_DAY = decision_maker.ALL_CONFS_COMBINED_BY_DAY
ALL_CONFS_COMBINED_BY_DAY_HDD = decision_maker.ALL_CONFS_COMBINED_BY_DAY_HDD
ALL_CONFS_COMBINED_BY_DAY_STRICT = decision_maker.ALL_CONFS_COMBINED_BY_DAY_STRICT
ALL_CONFS_COMBINED_BY_DAY_LOOSE = decision_maker.ALL_CONFS_COMBINED_BY_DAY_LOOSE
ALL_CONFS_COMBINED_BY_DAY_LAST_WEEK = decision_maker.ALL_CONFS_COMBINED_BY_DAY_LAST_WEEK
SPECIAL_IGNORES_FOR_HDD_BY_DAY = decision_maker.SPECIAL_IGNORES_FOR_HDD_BY_DAY
SPECIAL_IGNORES_FOR_SLIDE_BY_DAY = decision_maker.SPECIAL_IGNORES_FOR_SLIDE_BY_DAY
SPECIAL_ADDITIONS_FOR_HDD_BY_DAY = decision_maker.SPECIAL_ADDITIONS_FOR_HDD_BY_DAY

WEEKLY_ADDITIONS = decision_maker.WEEKLY_ADDITIONS
WEEKLY_ADDITIONS_STRICT = decision_maker.WEEKLY_ADDITIONS_STRICT



def get_chosen_this_week_but_not_previous(weekday):
    this_week_chosen = cwc.CHOSEN_STRATS_JSON[get_last_sunday()]['GDD']['hybrid'][f'{weekday}']
    last_week_chosen = cwc.CHOSEN_STRATS_JSON[get_last_sunday(7)]['GDD']['hybrid'][f'{weekday}']
    return [x for x in this_week_chosen if x not in last_week_chosen]

def get_dropped_this_week_but_not_previous(weekday):
    this_week_chosen = cwc.CHOSEN_STRATS_JSON[get_last_sunday()]['GDD']['hybrid'][f'{weekday}']
    last_week_chosen = cwc.CHOSEN_STRATS_JSON[get_last_sunday(7)]['GDD']['hybrid'][f'{weekday}']
    return [x for x in last_week_chosen if x not in this_week_chosen]



def rolling_calmar(df,epsilon_reduction=CALMAR_CALC_FILL_NANS):
    calmar, max_dd = calc_calmar(df.fillna(-CALMAR_EPSILON_REDUCTION_VALUE),use_annual=False,epsilon_reduction=epsilon_reduction)
    return calmar

def extract_strats():
    all_strats = []
    for w in strat_by_day_dict.keys():
        for mode in strat_by_day_dict[w].keys():
            for strat in strat_by_day_dict[w][mode]:
                if isinstance(strat, str):
                    all_strats.append("_".join(strat.split('_')[:-1]))
                elif isinstance(strat, tuple):
                    all_strats.append(strat[1])
    return all_strats

def analyze_specific_strategy(a,start,end,strat_obj,weekday,final_df):
    if isinstance(strat_obj, str):
        predictor = strat_obj
        fake_predictor = predictor
        strat = "_".join(strat_obj.split("_")[:2])
    elif isinstance(strat_obj, tuple) and len(strat_obj):
        predictor = strat_obj[0]
        fake_predictor = predictor
        strat = "_".join(strat_obj[1].split("_")[:2])
        assert len(strat_obj) == 2
    else:
        raise AssertionError('invalid strat_obj')
    if predictor[-2] == '$':
        # we have a copy of predictor
        print('Warning... We had a copy of predictor')
        fake_predictor = predictor[:-2]
    print('Checking strategy: %s (for day = %s)' % (predictor, weekday))
    if not cond(predictor):
        print('Skipping Strat: %s due to cond' % strat)
        return final_df
    if USE_WINTER_MAPPING:
        if strat in summer_to_winter_mapping.keys() and not predictor in special_predictors_to_avoid_mapping:
            strat = summer_to_winter_mapping[strat]
    try:
        if USE_OLD_CALCULATION:
            results_df, action = main_analysis(a, start, end, fake_predictor, 'prod', strat_override=strat)
        else:
            start = None
            results_df = wrap_results_df(a,strat,fake_predictor,default_end=end,start_override=start,
                                         shift_for_chosen_ratios=SHIFT_FOR_CHOSEN_RATIOS)
        results_df = results_df[results_df['date'].dt.weekday == weekday]
        results_df['strat'] = predictor
        results_df['secondary_strategy'] = strat
        results_df['weekday'] = weekday
        profit_col = 'profit_%s_combined_0' % "_".join(strat.split("_")[:2])
        if strat not in predictor:
            aaa = 1
        if predictor in strats_with_mean_action:
            profit_col = 'profit_%s_MEAN' % "_".join(strat.split("_")[:2])
        results_df['profit'] = results_df[profit_col]

        if predictor in ignore_contradictions_strats:
            results_df['Naive_MEAN'] = 'B'
            results_df.loc[results_df['MEAN'] < 0, 'Naive_MEAN'] = 'S'
            results_df.loc[results_df['action'] != results_df['Naive_MEAN'], 'profit'] = 0

        # if DROP_IGNORED_IN_ANALYSIS:   # todo decide on whether to drop in the main calculation already
        # if strat in temporarilyy_ignored_predictors[weekday]:
        # results_df['profit'] = 0
        if (predictor, strat) in strats_with_specific_months_ignoring[weekday] or \
                predictor in strats_with_specific_months_ignoring[weekday]:
            try:
                bad_months = strats_with_specific_months_ignoring[weekday][(predictor, strat)]
            except:
                bad_months = strats_with_specific_months_ignoring[weekday][predictor]
            results_df.loc[results_df['date'].dt.month.isin(bad_months), 'profit'] = 0
        if 'MEAN' not in list(results_df):
            stop = 1
        try:
            tmp_df = results_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy']]
        except:
            s = 1

        if final_df.shape[0] == 0:
            final_df = tmp_df
        else:
            final_df = final_df.merge(tmp_df, on=list(tmp_df), how='outer')
            final_df = final_df.drop_duplicates() # todo make sure this is not causing damage, done after discovering tones of duplicates in the final df
    except Exception as e:
        print('Skipping bad strategy (%s x %s) due to error: %s' % (predictor,strat, e))
    return final_df


def analyze_grouped_strategy(a,start,end,strat_obj,weekday,final_df,cond,use_cache=True):
    assert isinstance(strat_obj, tuple) and len(strat_obj)==3
    predictor = strat_obj[0]
    fake_predictor = predictor
    strats_lst = strat_obj[1]
    if len(strats_lst) == 1:
        strat = strats_lst[0]
    else:
        strat = 'y_0000-2359'
        a[strat] = a[strats_lst].fillna(0).sum(axis=1)
        a.loc[a[strats_lst].isna().mean(axis=1)==1,strat] = np.nan
    num_of_separated_trades = strat_obj[2]

    if predictor[-2] == '$':
        # we have a copy of predictor
        fake_predictor = predictor[:-2]
    print('Checking strategy: %s (for day = %s)' % (predictor, weekday))
    if not cond(predictor):
        print('Skipping Strat: %s due to cond' % strat)
        return final_df

    if USE_WINTER_MAPPING:
        if strat in summer_to_winter_mapping.keys() and not predictor in special_predictors_to_avoid_mapping:
            strat = summer_to_winter_mapping[strat]
    try:
        if USE_OLD_CALCULATION:
            results_df, action = main_analysis(a, start, end, fake_predictor, 'prod', strat_override=strat)
        else:
            if strat == 'y_0000-2359':
                use_cache = False

            cache_dir = os.path.join(HOME, 'performance_analysis', 'all_strategies_summary', 'predictors_cache')
            is_handled = False
            if use_cache:
                pred_file = os.path.join(cache_dir,f'{dtdt.now().strftime("%Y%m%d")}_{fake_predictor}_{strat}.csv')
                if os.path.exists(pred_file):
                    results_df = pd.read_csv(pred_file,parse_dates=['date'])
                    is_handled = True
            if not is_handled:
                results_df = wrap_results_df(a, strat, fake_predictor, default_end=end, start_override=start,
                                                 shift_for_chosen_ratios=SHIFT_FOR_CHOSEN_RATIOS, use_cache=use_cache)
                profit_col = 'profit_%s_combined_0' % "_".join(strat.split("_")[:2])
                if predictor in strats_with_mean_action:
                    profit_col = 'profit_%s_MEAN' % "_".join(strat.split("_")[:2])
                results_df['profit'] = results_df[profit_col]
                results_df['strat'] = predictor
                results_df['secondary_strategy'] = strat
                results_df['weekday'] = weekday
                results_df = results_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy', 'action']]
                if use_cache:
                    results_df.to_csv(pred_file,index=False)

        results_df = results_df[results_df['date'].dt.weekday == weekday]

        if predictor in ignore_contradictions_strats:
            results_df['Naive_MEAN'] = 'B'
            results_df.loc[results_df['MEAN'] < 0, 'Naive_MEAN'] = 'S'
            results_df.loc[results_df['action'] != results_df['Naive_MEAN'], 'profit'] = 0

        if (predictor, strat) in strats_with_specific_months_ignoring[weekday] or \
                predictor in strats_with_specific_months_ignoring[weekday]:
            try:
                bad_months = strats_with_specific_months_ignoring[weekday][(predictor, strat)]
            except:
                bad_months = strats_with_specific_months_ignoring[weekday][predictor]
            results_df.loc[results_df['date'].dt.month.isin(bad_months), 'profit'] = 0
        results_df.loc[abs(results_df['profit'])>0,'profit'] = results_df.loc[abs(results_df['profit'])>0,'profit'] - 2*COST_PER_TRADE*num_of_separated_trades
        # tmp_df = results_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy','action']]

        if final_df.shape[0] == 0:
            final_df = results_df
        else:
            final_df = final_df.merge(results_df, on=list(results_df), how='outer')
            final_df = final_df.drop_duplicates()
    except Exception as e:
        print('Skipping bad strategy (%s x %s) due to error: %s' % (predictor,strat, e))
    return final_df

def analyze_grouped_strategy_efficient(a,start,end,strat_obj,weekday,cond,use_cache=True):
    """
    avoid endless merges, create a stack and pd.concat
    :param a:
    :param start:
    :param end:
    :param strat_obj:
    :param weekday:
    :param cond:
    :param use_cache:
    :return:
    """
    assert isinstance(strat_obj, tuple) and len(strat_obj)==3
    predictor = strat_obj[0]
    fake_predictor = predictor
    strats_lst = strat_obj[1]
    if len(strats_lst) == 1:
        strat = strats_lst[0]
    else:
        strat = 'y_0000-2359'
        a[strat] = a[strats_lst].fillna(0).sum(axis=1)
        a.loc[a[strats_lst].isna().mean(axis=1)==1,strat] = np.nan
    num_of_separated_trades = strat_obj[2]

    if predictor[-2] == '$':
        # we have a copy of predictor
        fake_predictor = predictor[:-2]
    print('Checking strategy: %s (for day = %s)' % (predictor, weekday))
    if not cond(predictor):
        print('Skipping Strat: %s due to cond' % strat)
        return pd.DataFrame()

    if USE_WINTER_MAPPING:
        if strat in summer_to_winter_mapping.keys() and not predictor in special_predictors_to_avoid_mapping:
            strat = summer_to_winter_mapping[strat]
    try:
        if USE_OLD_CALCULATION:
            results_df, action = main_analysis(a, start, end, fake_predictor, 'prod', strat_override=strat)
        else:
            cache_dir = os.path.join(HOME, 'performance_analysis', 'all_strategies_summary', 'predictors_cache',f'cost={COST_PER_TRADE}')
            if not os.path.exists(cache_dir):
                os.makedirs(cache_dir)
            is_handled = False
            if use_cache:
                last_sunday = get_last_sunday()
                pred_file = os.path.join(cache_dir,f'{last_sunday}_{fake_predictor}_{strat}.csv')
                if strat == 'y_0000-2359':
                    # combined strats - we need to add the weekday to the file name
                    # to avoid using different combinations between weekdays
                    pred_file = pred_file.replace('.csv',f'_weekday={weekday}.csv')
                if os.path.exists(pred_file):
                    results_df = pd.read_csv(pred_file,parse_dates=['date'])
                    is_handled = True
            if not is_handled:
                results_df = wrap_results_df(a, strat, fake_predictor, default_end=end, start_override=start,
                                                 shift_for_chosen_ratios=SHIFT_FOR_CHOSEN_RATIOS, use_cache=use_cache)
                profit_col = 'profit_%s_combined_0' % "_".join(strat.split("_")[:2])
                if predictor in strats_with_mean_action:
                    profit_col = 'profit_%s_MEAN' % "_".join(strat.split("_")[:2])
                results_df['profit'] = results_df[profit_col]
                results_df['strat'] = predictor
                results_df['secondary_strategy'] = strat
                results_df['weekday'] = weekday
                results_df = results_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy', 'action']]
                if use_cache:
                    results_df.to_csv(pred_file,index=False)

        results_df = results_df[results_df['date'].dt.weekday == weekday]

        if predictor in ignore_contradictions_strats:
            results_df['Naive_MEAN'] = 'B'
            results_df.loc[results_df['MEAN'] < 0, 'Naive_MEAN'] = 'S'
            results_df.loc[results_df['action'] != results_df['Naive_MEAN'], 'profit'] = 0

        if (predictor, strat) in strats_with_specific_months_ignoring[weekday] or \
                predictor in strats_with_specific_months_ignoring[weekday]:
            try:
                bad_months = strats_with_specific_months_ignoring[weekday][(predictor, strat)]
            except:
                bad_months = strats_with_specific_months_ignoring[weekday][predictor]
            results_df.loc[results_df['date'].dt.month.isin(bad_months), 'profit'] = 0
        results_df.loc[abs(results_df['profit'])>0,'profit'] = results_df.loc[abs(results_df['profit'])>0,'profit'] - 2*COST_PER_TRADE*num_of_separated_trades
        return results_df
    except Exception as e:
        print('Skipping bad strategy (%s x %s) due to error: %s' % (predictor,strat, e))
        return pd.DataFrame()

def analyze_all_strats_of_predictor(a,start,end,predictor,strats_by_weekday,
                                    final_df,use_cache=True):
    seed_strat = list(strats_by_weekday.items())[0][1][0]

    if predictor[-2] == '$':
        # we have a copy of predictor
        fake_predictor = predictor[:-2]
    else:
        fake_predictor = predictor

    results_df_seed = wrap_results_df(a, seed_strat, fake_predictor, default_end=end, start_override=start,
                                 shift_for_chosen_ratios=SHIFT_FOR_CHOSEN_RATIOS, use_cache=use_cache)
    for weekday, strats_lst in strats_by_weekday.items():
        if len(strats_lst) > 1:
            strat = 'y_0000-2359'
            a[strat] = a[strats_lst].fillna(0).sum(axis=1)
            a.loc[a[strats_lst].isna().mean(axis=1)==1,strat] = np.nan
        else:
            strat = strats_lst[0]
        try:
            results_df = convert_predictor_results_to_new_strat(results_df_seed, a[['date',strat]], strat)
            results_df = results_df[results_df['date'].dt.weekday == weekday]
            results_df['strat'] = predictor
            results_df['secondary_strategy'] = strat
            results_df['weekday'] = weekday
            profit_col = f'profit_{predictor}_combined_0'
            if predictor in strats_with_mean_action:
                profit_col = f'profit_{predictor}_MEAN'
            results_df['profit'] = results_df[profit_col]

            if predictor in ignore_contradictions_strats:
                results_df['Naive_MEAN'] = 'B'
                results_df.loc[results_df['MEAN'] < 0, 'Naive_MEAN'] = 'S'
                results_df.loc[results_df['action'] != results_df['Naive_MEAN'], 'profit'] = 0

            if (predictor, strat) in strats_with_specific_months_ignoring[weekday] or \
                    predictor in strats_with_specific_months_ignoring[weekday]:
                try:
                    bad_months = strats_with_specific_months_ignoring[weekday][(predictor, strat)]
                except:
                    bad_months = strats_with_specific_months_ignoring[weekday][predictor]
                results_df.loc[results_df['date'].dt.month.isin(bad_months), 'profit'] = 0
            results_df.loc[abs(results_df['profit'])>0,'profit'] = results_df.loc[abs(results_df['profit'])>0,'profit'] - 2*COST_PER_TRADE*num_of_separated_trades
            tmp_df = results_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy']]

            if final_df.shape[0] == 0:
                final_df = tmp_df
            else:
                final_df = final_df.merge(tmp_df, on=list(tmp_df), how='outer')
                final_df = final_df.drop_duplicates()
        except Exception as e:
            print('Skipping bad strategy (%s x %s) due to error: %s' % (predictor,strat, e))
    return final_df



def group_predictors_efficiently(days,modes,substrings=['']):
    df, lst = get_strategies_list_v1(substrings=substrings, days=days, ms=modes,
                                  only_production=False)
    if len(lst) == 0:
        return lst
    # [x for x in lst if isinstance(x,str) or int("0"+str(x[1].split('-')[0].split('_')[1][:2])) < 8]
    df['predictor'] = df['strat'].apply(lambda x: _split_preds(x, pred_or_strat='pred'))
    predictors_dict = {}

    for pred, pred_group in df.groupby('predictor'):
        pred_strats = ",".join(
            pred_group['strat'].apply(lambda x: _split_preds(x, pred_or_strat='strat')).tolist()).split(",")
        predictors_dict[pred] = pred_strats

    get_num_of_separated_trades = lambda strats_to_concat: len(strats_to_concat) - sum([strats_to_concat[i].split('-')[-1] == strats_to_concat[i + 1].split('-')[0].split('y_')[-1]
         for i, x in enumerate(strats_to_concat[:-1])])

    return [(k,v,get_num_of_separated_trades(v)) for k,v in predictors_dict.items()]


def wrap_calc_profits(suffix,ft,weekdays,cond_on_strats,cond_on_time_str,
                      days_replacement_dict,start, end, additioanl_csv_suffix,
                      send_email, calc_As,strategy, big_model_hour, mode
                      ):
    a_v8_12z = main_wrapper(conf_name=mode, last_model=12, strategy=strategy, calc_As=calc_As, print_predies=0,
                            suffix='v8_12Z', ft=ft,
                            send_email=send_email, start=dtdt(2019, 7, 1), end=end, big_model_hour=big_model_hour)
    a_v8_0zb = main_wrapper(conf_name=mode, last_model=12, strategy=strategy, calc_As=calc_As, print_predies=0,
                            suffix='v8_0Zb', ft=ft,
                            send_email=send_email, start=dtdt(2019, 7, 1), end=end, big_model_hour=big_model_hour)
    final_df = pd.DataFrame()
    if not GROUP_PREDICTORS_BY_WEEKDAY:
        for weekday in weekdays:
            for time_str in strat_by_day_dict[weekday].keys():
                if not '12Z' in time_str:
                    a = a_v8_0zb
                else:
                    a = a_v8_12z
                if cond_on_time_str(time_str):
                    ref_weekday = weekday
                    if weekday in days_replacement_dict.keys():
                        ref_weekday = days_replacement_dict[weekday]

                    if GROUP_PREDICTORS:
                        predictors_tuples = group_predictors_efficiently([ref_weekday], modes=[time_str])
                        # predictors_tuples = [x for x in predictors_tuples if x[2] > 1]
                    else:
                        predictors_tuples = [x if isinstance(x, tuple) else (x, "_".join(x.split('_')[:2])) for x in
                                             strat_by_day_dict[ref_weekday][time_str]]
                    OLD_METHOD = False # todo
                    if OLD_METHOD or not GROUP_PREDICTORS:
                        for strat_obj in predictors_tuples:
                            if GROUP_PREDICTORS:
                                final_df = analyze_grouped_strategy(a, start, end, strat_obj, weekday, final_df, cond_on_strats)
                            else:
                                final_df = analyze_specific_strategy(a, start, end, strat_obj, weekday, final_df)
                    else: # efficient method
                        stack = []
                        for strat_obj in predictors_tuples:
                            tmp_df = analyze_grouped_strategy_efficient(a, start, end, strat_obj, weekday, cond_on_strats)
                            if tmp_df.shape[0] > 0:
                                try:
                                    tmp_df = tmp_df[['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy', 'action']]
                                    stack.append(tmp_df)
                                except Exception as e:
                                    print('Skipping bad strategy (%s) due to error: %s' % (strat_obj[0] if isinstance(strat_obj,tuple) else strat_obj, e))
                        relevant_results = [x for x in stack if x.shape[0] > 0]
                        if len(relevant_results) > 0:
                            day_part_df = pd.concat(relevant_results)
                            print(f'INFO| After taking care of {time_str} x {weekday} we have day_part.shape = {day_part_df.shape[0]} rows')
                            if final_df.shape[0] == 0:
                                final_df = day_part_df
                            else:
                                final_df = final_df.merge(day_part_df, on=list(day_part_df), how='outer')
                            print(f'INFO| After taking care of {time_str} x {weekday} we have final_df.shape = {final_df.shape[0]} rows')
    else:
        raise NotImplementedError()
        for a_df_type in ['v8_12Z','v8_0Zb']:
            # todo todo todo
            predictors_confs_to_scan = get_predictors_strategies_by_weekday(a_df_type)
            if a_df_type == 'v8_12Z':
                a = a_v8_12z
            else:
                a = a_v8_0zb
            for predictor, strats_by_weekday in predictors_confs_to_scan.items():
                analyze_all_strats_of_predictor(a, start, end, predictor, strats_by_weekday, final_df)

    calculation_str = '' if USE_OLD_CALCULATION else '_newCalc'
    grouped_str = '' if not GROUP_PREDICTORS else '_Grouped'
    csv_name = os.path.join(HOME, "performance_analysis", "all_strategies_summary",
                            "profits_df_full_%s_%s_start=%s_Wmap=%s%s%s%s.csv") % (
               suffix, ft, start.strftime('%Y%m%d'), USE_WINTER_MAPPING, calculation_str, grouped_str,
               additioanl_csv_suffix)
    if SHIFT_FOR_CHOSEN_RATIOS != 0:
        csv_name = csv_name.replace('.csv', '_RatioShift=%s.csv' % SHIFT_FOR_CHOSEN_RATIOS)
    final_df.to_csv(csv_name, index=False)
    return final_df

def wrap_calc_profits_v2(suffix,ft,weekdays,cond_on_strats,cond_on_time_str,
                      days_replacement_dict,start, end, additioanl_csv_suffix,
                      send_email, calc_As,strategy, big_model_hour, mode
                      ):
    """
    This version should be more efficient, not going through each weekday separately, but rather by predictor
    """
    a_v8_12z = main_wrapper(conf_name=mode, last_model=12, strategy=strategy, calc_As=calc_As, print_predies=0,
                            suffix='v8_12Z', ft=ft,
                            send_email=send_email, start=dtdt(2019, 7, 1), end=end, big_model_hour=big_model_hour)
    a_v8_0zb = main_wrapper(conf_name=mode, last_model=12, strategy=strategy, calc_As=calc_As, print_predies=0,
                            suffix='v8_0Zb', ft=ft,
                            send_email=send_email, start=dtdt(2019, 7, 1), end=end, big_model_hour=big_model_hour)
    final_df = pd.DataFrame()

    for weekday in weekdays:
        for time_str in strat_by_day_dict[weekday].keys():
            if not '12Z' in time_str:
                a = a_v8_0zb
            else:
                a = a_v8_12z
            if cond_on_time_str(time_str):
                ref_weekday = weekday
                if weekday in days_replacement_dict.keys():
                    ref_weekday = days_replacement_dict[weekday]
                if GROUP_PREDICTORS:
                    predictors_tuples = group_predictors_efficiently([ref_weekday], modes=[time_str])
                    # predictors_tuples = [x for x in predictors_tuples if x[2] > 1]
                else:
                    predictors_tuples = [x if isinstance(x, tuple) else (x, "_".join(x.split('_')[:2])) for x in
                                         strat_by_day_dict[ref_weekday][time_str]]
                for strat_obj in predictors_tuples:
                    if GROUP_PREDICTORS:
                        final_df = analyze_grouped_strategy(a, start, end, strat_obj, weekday, final_df, cond_on_strats)
                    else:
                        final_df = analyze_specific_strategy(a, start, end, strat_obj, weekday, final_df)

    calculation_str = '' if USE_OLD_CALCULATION else '_newCalc'
    grouped_str = '' if not GROUP_PREDICTORS else '_Grouped'
    csv_name = os.path.join(HOME, "performance_analysis", "all_strategies_summary",
                            "profits_df_full_%s_%s_start=%s_Wmap=%s%s%s%s.csv") % (
               suffix, ft, start.strftime('%Y%m%d'), USE_WINTER_MAPPING, calculation_str, grouped_str,
               additioanl_csv_suffix)
    if SHIFT_FOR_CHOSEN_RATIOS != 0:
        csv_name = csv_name.replace('.csv', '_RatioShift=%s.csv' % SHIFT_FOR_CHOSEN_RATIOS)
    final_df.to_csv(csv_name, index=False)
    return final_df


def load_profits_df(suffix,ft,start, end, additioanl_csv_suffix):
    calculation_str = '' if USE_OLD_CALCULATION else '_newCalc'
    grouped_str = '' if not GROUP_PREDICTORS else '_Grouped'
    csv_name = os.path.join(HOME, "performance_analysis", "all_strategies_summary",
                            "profits_df_full_%s_%s_start=%s_Wmap=%s%s%s%s.csv") % (
               suffix, ft, start.strftime('%Y%m%d'), USE_WINTER_MAPPING, calculation_str, grouped_str,
               additioanl_csv_suffix)
    if SHIFT_FOR_CHOSEN_RATIOS != 0:
        csv_name = csv_name.replace('.csv', '_RatioShift=%s.csv' % SHIFT_FOR_CHOSEN_RATIOS)
    final_df = pd.read_csv(csv_name, parse_dates=['date'])
    final_df = final_df[(final_df['date'] >= start) & (final_df['date'] <= end)]
    final_df = final_df.drop_duplicates()
    return final_df


def compact_performance_summary(suffix,ft,start, end, additional_csv_suffix, cond_on_strats,
                                weekdays=[0,1,2,3,4],
                                strings_to_include=['para','0z'],
                                strings_to_exclude=['paraco'],
                                plot_individuals=False,
                                allow_or_in_condition=False,
                                calculate_calmars=False,
                                separate_weekdays=False,
                                add_date_to_filename=True,production_mode=False):
    final_df = load_profits_df(suffix, ft, start, end, additional_csv_suffix)
    final_df = final_df[final_df['date'].dt.weekday.isin(weekdays)]
    if not allow_or_in_condition:
        cond_on_strats = lambda x: sum([s not in x.lower() for s in strings_to_include])==0 and sum([s in x.lower() for s in strings_to_exclude])==0
    else:
        cond_on_strats = lambda x: sum([s in x.lower() for s in strings_to_include]) > 0 and sum([s in x.lower() for s in strings_to_exclude]) == 0
    final_df = final_df[final_df['strat'].apply(cond_on_strats)]
    first_10_unique_strats = final_df['strat'].unique()[:10]

    if not production_mode:
        final_df.groupby('date').agg({'profit':'sum'}).cumsum().plot(title=f'include = {strings_to_include}')
    # add next monday to final_df to be filled later
    next_monday = final_df['date'].max() + pd.Timedelta(days=7-final_df['date'].max().weekday())
    final_df = final_df.merge(pd.DataFrame({'date': [next_monday],
                                            'strat':['y_0000-2345_fake'],
                                            'weekday':[0],'profit':[0],'MEAN':[0],
                                            'secondary_strategy':['y_0000-2345']}), on=['date', 'strat', 'weekday', 'profit', 'MEAN', 'secondary_strategy'], how='outer').sort_values('date')
    # plt.show()
    if plot_individuals:
        if not separate_weekdays:
            p = pd.pivot_table(final_df, index='date', columns='strat', values='profit', aggfunc='sum')#.reset_index()
        else:
            p = pd.pivot_table(final_df, index='date', columns=['weekday','strat'], values='profit', aggfunc='sum')#.reset_index()

        d = COMPACT_FILTERING_DIR
        suffix = '' if not separate_weekdays else '_byWeekday'
        p.reset_index().to_csv(os.path.join(d, f'all_strats_pivot{suffix}.csv'), index=False)

        if calculate_calmars:
            windows = [4,7,10,15,20]*separate_weekdays + [35,40,45,50]*(not separate_weekdays)
            if production_mode:
                if not separate_weekdays:
                    windows = [40]
            for w in windows:
                rolling_calmar_df = p.fillna(0).rolling(w,4).apply(lambda x: calc_calmar(x,use_annual=False)[0]).shift(1)
                rolling_sharpe_df = p.fillna(0).rolling(w,4).apply(lambda x: calc_sharpe(x)).shift(1)

                calmar_filename = f'all_strats_pivot_rolling_calmar_{w}{"_"+dtdt.now().strftime("%Y%m%d") if add_date_to_filename else ""}{suffix}.csv'
                sharpe_filename = f'all_strats_pivot_rolling_sharpe_{w}{"_"+dtdt.now().strftime("%Y%m%d") if add_date_to_filename else ""}{suffix}.csv'
                rolling_calmar_df.reset_index().to_csv(os.path.join(d,calmar_filename),index=False)
                rolling_sharpe_df.reset_index().to_csv(os.path.join(d,sharpe_filename),index=False)

            return
        if calculate_calmars:
            calmar_threshold = 2
            sharpe_threshold = 1.5
            p2 = p.copy()
            for c in list(p):
                p2.loc[(rolling_calmar_df[c]< calmar_threshold)|(rolling_sharpe_df[c]< sharpe_threshold), c] = 0
        else:
            p2 = p.copy()
        p2.sum(axis=1).cumsum().plot()
        p.sum(axis=1).cumsum().plot()
        plt.legend(['filtered','unfiltered'])
        plt.show()

    plt.show()
    bb = 0

def performance_sumary(suffix='v8',ft='GDD',calc_profits=False,weekdays = [0,1,2,3,4],
                       cond_on_strats=lambda x: True,cond_str='',run_plots=True,
                       cond_on_time_str=lambda x: True,days_replacement_dict={},
                       start = dtdt(2020, 3, 1),end = dtdt(2021, 12, 31),additioanl_csv_suffix='',
                            send_email=False,calc_As = 0,
                                strategy = '0800-1100',big_model_hour = 0,mode = 'dry'
                       ):


    if calc_profits:
        final_df = wrap_calc_profits(suffix,ft,weekdays,cond_on_strats,cond_on_time_str,
                      days_replacement_dict,start, end, additioanl_csv_suffix,
                      send_email, calc_As,strategy, big_model_hour, mode
                      )
    else:
        final_df = load_profits_df(suffix,ft,start, end, additioanl_csv_suffix)
    if run_plots:
        final_df = final_df[final_df['weekday'].isin(weekdays)]
        final_df['strat_hours'] = final_df['strat'].apply(lambda x: "_".join(x.split("_")[:2]))
        final_df['open_hh'] = final_df['strat_hours'].apply(lambda x: int(x.split("_")[1].split("-")[0][:2]))
        final_df['open_mm'] = final_df['strat_hours'].apply(lambda x: int(x.split("_")[1].split("-")[0][2:4]))
        final_df['time'] = final_df['date'] - final_df['date'].dt.hour.apply(lambda x: td(hours=x)) + final_df['open_hh'].apply(lambda x: td(hours=x))+ \
                           final_df['open_mm'].apply(lambda x: td(minutes=x))
        final_df = final_df.sort_values(['time'])
        if COST_PER_TRADE > 0 and not GROUP_PREDICTORS:
            # in GROUP mode we already subtracted in calculation
            final_df.loc[final_df['profit']!=0,'profit'] = final_df.loc[final_df['profit']!=0,'profit'] - COST_PER_TRADE*2
        final_df2 = final_df.groupby(['date','time']).sum().reset_index()

        # daily summary
        daily_sum_df = final_df2.groupby('date').sum() * 10 #0

        daily_sum_df["drawdown"] = daily_sum_df['profit'].cumsum() - daily_sum_df['profit'].cumsum().cummax()
        if PLOT:
            daily_sum_df['profit'].cumsum().plot(title='Cummulative daily profit of all strategies')
            plt.show()

            daily_sum_df['drawdown'].plot(title='max daily drawdown',kind='bar')
            plt.show()

        # hourly summary
        if PLOT:
            final_df2.set_index('time')['profit'].fillna(0).cumsum().plot(title='Cummulative profit of all strategies')
            plt.show()

        # divide to strategies
        strats_df = pd.DataFrame()
        for strat, strat_group in final_df.groupby('strat'):
            if strat== "y_0800-1945_GEFS0Zp20to16Strict":
                bb = 0
            if len(strat.split('_'))==3:
                strat_name = '_'.join(strat.split('_')[:-1])
            else:
                strat_name = strat
            if 'entum' in strat:
                bb = 0
            if cond_on_strats(strat): #or cond_on_strats(strat_name):
                if DROP_IGNORED_IN_ANALYSIS:
                    for weekday in [0,1,2,3,4]:
                        if strat in temporarilyy_ignored_predictors[weekday]:
                            strat_group.loc[strat_group['date'].dt.weekday==weekday,'profit'] = 0
                if ONLY_IGNORED_IN_ANALYSIS:
                    for weekday in [0,1,2,3,4]:
                        if strat not in temporarilyy_ignored_predictors[weekday]:
                            strat_group.loc[strat_group['date'].dt.weekday==weekday,'profit'] = 0

                strat_group = strat_group.rename(columns={'profit':'profit_%s'%strat})
                if len(strat_group['date'].tolist()) != len(set(strat_group['date'].tolist())):
                    prob = 1
                    strat_group = strat_group.groupby('date').sum().reset_index()
                strat_group = strat_group[['date', 'profit_%s' % strat]]
                if strats_df.shape[0] == 0:
                    strats_df = strat_group
                else:
                    strats_df = strats_df.merge(strat_group,on=['date'],how='outer')
                    print ('strat_df.shape = %s'%strats_df.shape[0])

        strats_df= strats_df[strats_df['date']>=start]

        strats_df = strats_df.sort_values('date').set_index('date').fillna(0)
        if not SHOW_NULL_PROFITS:
            strats_df2 = strats_df[[x for x in list(strats_df) if 'profit' in x and strats_df[x].sum()!=0]]
        else:
            strats_df2 = strats_df
        if ONLY_BEST != 0:
            if not USE_SLIDING_WINDOW:
                start_ind = 0
                end_ind = strats_df2.shape[0]
                if DAYS_FOR_BEST != 0:
                    start_ind = -DAYS_FOR_BEST
                    if LAST_TAKEN_DAY != 0:
                        start_ind -= LAST_TAKEN_DAY
                        end_ind -= LAST_TAKEN_DAY
                else:
                    end_ind -= LAST_TAKEN_DAY

                if not USE_HR:
                    if PPT_OR_SUM == 'sum':
                        performance_df = strats_df2[start_ind:end_ind].sum()
                    elif PPT_OR_SUM == 'ppt':
                        performance_df = strats_df2[start_ind:end_ind].mean()
                    elif PPT_OR_SUM == 'calmar':
                        performance_df = strats_df2[start_ind:end_ind].apply(rolling_calmar)
                    else:
                        raise AssertionError('Invalied PPT_OR_SUM: %s'%PPT_OR_SUM)
                else:
                    performance_df = np.sign(strats_df2[start_ind:end_ind]).replace(-1,0).mean()
                worst_to_best_strat = performance_df.sort_values().index.tolist()
                if ONLY_BEST < 0:
                    required_strats = worst_to_best_strat[:-ONLY_BEST]
                else:
                    required_strats = worst_to_best_strat[-ONLY_BEST:]
                strats_df2 = strats_df2[required_strats]
            else:
                if REDUCE_EPSILON_FROM_ALL:
                    strats_df2[[x for x in list(strats_df2) if 'profit' in x]] = strats_df2[[x for x in list(strats_df2) if 'profit' in x]] - CALMAR_EPSILON_REDUCTION_VALUE
                strats_df2_for_calc = strats_df2
                if SKIP_IGNORED:
                    strats_df2_for_calc = strats_df2_for_calc.replace(0,np.nan)

                # drop rows with no Ys
                strats_df2_for_calc = strats_df2_for_calc.dropna(how='all')

                if not USE_HR:
                    if PPT_OR_SUM == 'sum':
                        rolling_performance_df = strats_df2_for_calc.rolling(DAYS_FOR_BEST,2).sum()
                    elif PPT_OR_SUM == 'ppt':
                        rolling_performance_df = strats_df2_for_calc.rolling(DAYS_FOR_BEST, 2).mean()
                    elif PPT_OR_SUM == 'calmar':
                        rolling_performance_df = strats_df2_for_calc.rolling(DAYS_FOR_BEST, 4).apply(lambda x: rolling_calmar(x))
                    else:
                        raise AssertionError('Invalied PPT_OR_SUM: %s'%PPT_OR_SUM)
                else:
                    rolling_performance_df = np.sign(strats_df2_for_calc).replace(-1,0).rolling(DAYS_FOR_BEST, 3).mean()

                ### filtering out strats that didnt perform good recently
                if RECENT_DAYS_FOR_DROPPING_FROM_PROD > 0:
                    ppt_on_recent_days = strats_df2_for_calc[-RECENT_DAYS_FOR_DROPPING_FROM_PROD:].mean()
                    strats_with_negative_ppt_on_recent_days = ppt_on_recent_days[ppt_on_recent_days<BAD_PPT_VALUE].index.tolist()
                    hr_on_recent_days = np.sign((strats_df2_for_calc+CALMAR_EPSILON_REDUCTION_VALUE).replace(0,np.nan)[-RECENT_DAYS_FOR_DROPPING_FROM_PROD:]).replace(-1,0).mean()
                    strats_with_negative_hr_on_recent_days = hr_on_recent_days[hr_on_recent_days <= 0.5].index.tolist()
                    strats_with_negative_ppt_on_recent_days = [x.replace('profit_', '') for x in strats_with_negative_ppt_on_recent_days]
                    strats_with_negative_hr_on_recent_days = [x.replace('profit_', '') for x in strats_with_negative_hr_on_recent_days]
                else:
                    strats_with_negative_ppt_on_recent_days = []
                    strats_with_negative_hr_on_recent_days = []

                df = pd.DataFrame(rolling_performance_df.columns.values[np.argsort(-rolling_performance_df.values, axis=1)[:, :ONLY_BEST]],
                                  index=rolling_performance_df.index,
                                  columns=['best%s'%i for i in range(1,ONLY_BEST+1)]).reset_index()
                # We shift the dates to look back, fill the initial by original, take dict
                chosen_strats_by_day = df.set_index('date').shift(LAST_TAKEN_DAY).reset_index().fillna(df).set_index('date').T.to_dict('list')
                last_date, last_chosen_strats_by_day = df.iloc[-1]['date'],df.set_index('date').iloc[-1].tolist()
                last_chosen_strats_by_day = [x.replace('profit_','') for x in last_chosen_strats_by_day]
                last_chosen_strats_by_day_bad_recent_dropped = [x for x in last_chosen_strats_by_day
                                                                if x not in strats_with_negative_ppt_on_recent_days
                                                                and x not in strats_with_negative_hr_on_recent_days]
                dropped_strats = [x for x in last_chosen_strats_by_day if x not in last_chosen_strats_by_day_bad_recent_dropped]
                if PLOT:
                    if len(dropped_strats):
                        strats_df2[['profit_%s' % x for x in
                                    dropped_strats]].cumsum().plot(title='The strats we dropped due to recent weakness')
                        plt.show()
                # used to have here writing into json files. irrelevant by now. 7.1.7

                debug = True
                total_ignored_pnl = 0

                pointer = strats_df2.index.tolist()[0]+td(days=SLIDE_WINDOW_SIZE-1)
                i = 0
                strats_df2 = strats_df2.reset_index().merge(strats_df2_for_calc.reset_index()[['date']],
                                                            on=['date']).set_index('date')
                for date,row in strats_df2.iterrows():
                    i+= 1
                    if date < pointer and i >1:
                        continue
                    else:
                        pointer = date + td(days=SLIDE_WINDOW_SIZE - 1)

                    to_remove = [x for x in strats_df2 if x not in chosen_strats_by_day[date]]
                    filter_cond = date
                    if SLIDE_WINDOW_SIZE > 1:
                        filter_cond_0 = (strats_df2.reset_index()['date'] <= pointer)&(strats_df2.reset_index()['date'] >= date)
                        filter_cond = strats_df2.reset_index()['date'][filter_cond_0]
                    current_ignored_pnl = strats_df2.loc[filter_cond, to_remove].sum()
                    total_ignored_pnl += current_ignored_pnl
                    if debug:
                        print('for date: %s We remove %s strats with Total PNL of %s' % (date, len(to_remove), current_ignored_pnl))
                    strats_df2.loc[filter_cond,to_remove] = 0

                if debug:
                    print ('Total Ignored PNL = %s'%total_ignored_pnl)

        elif FILTER_BY_PRODUCTION_COND:
            required_strats = [x for x in list(strats_df2) if sum([s in x for s in LAST_UPDATED_TOP])]
            for w in weekdays:
                #required_strats += [x for x in list(strats_df2) if sum([s in x for s in TOP15_7D_BY_DAY[w]+
                                                                        #MUST_BY_DAY[w]+TESTING_BY_DAY[w]])]
                required_strats += [x for x in list(strats_df2) if sum([s in x for s in
                            ALL_CONFS_COMBINED_BY_DAY[w]])]
                pass
            strats_df2 = strats_df2[required_strats]
        elif FILTER_BY_EXCEPTIONS_ONLY:
            raise AssertionError('Not supporting Exceptions analysis from Jul21')
            required_strats = []
            for w in weekdays:
                required_strats += [x for x in list(strats_df2) if sum([s in x for s in SPECIAL_EXCEPTIONS_FOR_SLIDE_BY_DAY[w]])]
                pass
            strats_df2 = strats_df2[required_strats]
        filtered_daily = strats_df2[[x for x in list(strats_df2) if cond(x.replace('profit_',''))]].sum(axis=1)
        if PLOT:
            strats_df2['Total_Profit'] = strats_df2.sum(axis=1)
            strats_df2.cumsum()[list(strats_df2)[:25]].plot(title='cummulative profit by strategy',style=['-']*7+['--']*7+[':']*7+['-.']*7)
            try:
                strats_df2.cumsum()[list(strats_df2)[25:50]].plot(title='cummulative profit by strategy',style=['-']*7+['--']*7+[':']*7+['-.']*7)
            except:
                pass
            plt.show()
        filtered_daily_df = pd.DataFrame(filtered_daily).rename(columns={0:'profit'}).reset_index()
        weekdays_df = pd.DataFrame()
        for weekday, weekday_group in filtered_daily_df.groupby(filtered_daily_df.date.dt.weekday):
            weekday_group = weekday_group.rename(columns={'profit':'profit_day=%s'%weekday})
            if weekdays_df.shape[0] == 0:
                weekdays_df = weekday_group
            else:
                weekdays_df = weekdays_df.merge(weekday_group,on=['date'],how='outer')
        weekdays_df = weekdays_df.fillna(0).sort_values('date')
        filtered_daily_cumsum = filtered_daily.cumsum()
        if PLOT:
            weekdays_df.set_index('date').cumsum().plot(title='Cummulative PNL (filtered) by weekday')
            filtered_daily_cumsum.plot(title='Filtered Strats (%s) Daily profits'%cond_str)
            plt.show()
        days_back_for_last_month = len(weekdays)*6
        for name, profits in zip(['All','Last Month'],
                                 [filtered_daily,filtered_daily[-days_back_for_last_month:]]):
            if name == 'All':
                drawdown_vector = get_drawdown_vector(profits)
                if PLOT:
                    drawdown_vector.plot(kind='bar',title='drawdown of filtered trades')
                    plt.show()
            calmar, max_dd = calc_calmar(profits,epsilon_reduction=CALMAR_CALC_FILL_NANS)
            sharpe = calc_sharpe(profits)
            ppt = profits.mean()
            hr = (profits>0).mean()
            pnl = profits.sum()
            print('For Filtered Trades:')
            name2 = name
            if name == 'All':
                name2 = '       All'
            print('%s Filtered Trades | PPDay = %s | HR = %s | PNL = %s | calmar %s | Sharpe = %s | MaxDD %s' % (name2,ppt, hr, pnl, calmar, sharpe, max_dd))
        strats_df.sum().sort_values().to_csv(os.path.join(HOME,"performance_analysis","HDD_vs_GDD","","PNLs_%s_%s_weekdays=%s%s.csv") % (suffix, ft, weekdays,cond_str))
        d = daily_sum_df.reset_index()
        d['weekday'] = d['date'].dt.weekday
        best_strats = [x.replace('profit_','') for x in list(strats_df2) if cond(x)]

        production_strats = LAST_UPDATED_TOP + ALL_CONFS_COMBINED_BY_DAY[weekdays[0]]

        print('Required Strats for Regulat mode BEST %s %sDAYS: \n%s' % (ONLY_BEST, DAYS_FOR_BEST, best_strats))
        print ('======================================================')
        if ONLY_BEST > 0:
            print ('BEST that are not in ProductionL %s'%[x for x in best_strats if x not in production_strats])
            print('======================================================')
        elif ONLY_BEST < 0:
            print('BEST that are in ProductionL %s' % [x for x in best_strats if
                                                           x in production_strats and x not in temporarilyy_ignored_predictors[weekdays[0]]])
            print('======================================================')
        if USE_SLIDING_WINDOW:
            try:
                print ('LAST chosen (%s) with delay of (%s days) = \n%s'%(last_date,LAST_TAKEN_DAY,last_chosen_strats_by_day))
                print('LAST chosen (%s) with delay of (%s days) without negative performance on last %s days = \n%s' % (last_date, LAST_TAKEN_DAY, RECENT_DAYS_FOR_DROPPING_FROM_PROD,last_chosen_strats_by_day_bad_recent_dropped))
                print ('(dropped: %s)'%str(dropped_strats))
                print ('-----------------------------------------------------------------')

            except Exception as e:
                print ('Failed with some prints due to some error: %s'%e)
        d.to_csv(os.path.join(HOME,"performance_analysis","HDD_vs_GDD","","Daily_profits_%s_%s_weekdays=%s%s.csv") % (suffix, ft, weekdays,cond_str))
        a = 1
        # final_df2[final_df2['time']>=dtdt(2020,6,1)].groupby(['weekday','open_hh','open_mm']).mean()

def get_predictors_strats_from_production(predictor,weekday,exact_or_substring='exact'):
    day_strats = []

    if weekday == 'All':
        weekdays = [0,1,2,3,4]
    else:
        weekdays = [weekday]
    for day in weekdays:
        for time in STRATS_BY_DAY_DICT[day].keys():
            day_strats += STRATS_BY_DAY_DICT[day][time]
    cond_tmp = lambda predictor,x: ((predictor == x) if exact_or_substring == 'exact' else (predictor in x))
    strats = ["_".join(x.split('_')[:2]) for x in day_strats if isinstance(x,str) and cond_tmp(predictor,x)] +[x[1] for x in
           day_strats if isinstance(x,tuple) and cond_tmp(predictor,x[0])]
    predictors = ["_".join(x.split('_')) for x in day_strats if isinstance(x,str) and cond_tmp(predictor,x)] +[x[0] for x in
           day_strats if isinstance(x,tuple) and cond_tmp(predictor,x[0])]
    return [(predictor,s) for predictor, s in zip(predictors,strats)]

def drop_outlier_predictors_today(d,mode='realHDD',
                                  f=os.path.join(HOME,"Trading","daily_predictions.csv")):
    df = pd.read_csv(f,parse_dates=['date'])
    exclusion_lst = SPECIAL_IGNORES_FOR_HDD_BY_DAY[d] if 'HDD' in mode else SPECIAL_IGNORES_FOR_SLIDE_BY_DAY[d]
    cond = (df['mode']!=mode)|(df['date']<dtdt(dtdt.now().year,dtdt.now().month,dtdt.now().day))|(df['predictor']).isin([x for x in ALL_CONFS_COMBINED_BY_DAY[d]+(SPECIAL_ADDITIONS_FOR_HDD_BY_DAY[d] if 'HDD' in mode else [])
                                                                                                                            if x not in exclusion_lst
                                                                                                                          ])
    print('about to drop: %s '%df[~cond]['predictor'].tolist())
    df = df[cond]
    df.to_csv(f.replace('.csv','.csv'),index=False)


THU_CHOSEN_2110 = ['y_0800-1845_PARACO18zp20to13', 'y_1100-1745_MonComb1021',
                                         'y_1445-1545_PARA0z14to16p4', 'y_1415-1845_PARACO6z1D0to10',
                                         'y_0800-1845_WTIcloseYest', 'y_1000-1400_PARACO18z$3',
                                         'y_1515-1915_PARA6Z3D0to10', 'y_0800-1745_cfs12Z0to16p3',
                                         'y_0800-1415_PARACO0zp3', 'y_0800-1845_PARACO0zCombStrict',
                                         'y_1815-2030_epsFcst12Z', 'y_1415-1615_GFS6Zrolling2',
                                         'y_1445-1645_cfs18Z0to21', 'y_0800-1315_TTFclose', 'y_0800-1845_MTFComb',
                                         'y_1000-1100_GFSv160z2$2', 'y_1100-1130_GFSv164D',
                                         'y_1445-1545_paracoSeasonal14to16', 'y_1815-2030_epsFcst12ZbCorStrict',
                                         'y_1515-1745_PARACO6zp3', 'y_0700-0800_EC0zbasic', 'y_1745-1945_GEFS12z0to8$3',
                                         'y_1815-2030_epsFcst12ZCor', 'y_1545-1645_GFSv16z', 'y_1615-1845_GEM12zp20to2',
                                         'y_1815-1945_GEFS0to8$3', 'y_0800-1200_PARARACO18ZComb',
                                         'y_1315-1715_CFS6z0to16p34', 'y_0800-1845_MTFCombStrict',
                                         'y_0800-1315_TTFcloseYestStrict$3', 'y_1430-1715_COALopenYestd',
                                         'y_1730-1745_GEFS12Z', 'y_0000-0800_GEPFS4D', 'y_1300-1545_PARA018Z8to16',
                                         'y_0800-1315_TTFStrict', 'y_1000-1400_PARACO18z', 'y_0800-1845_CFSCOCombWed',
                                         'y_0800-1845_weeklyMomentumd3', 'y_0800-1845_GEPSCOCombD',
                                         'y_0800-1200_PARARACO0to8Comb', 'y_0800-1845_CFSM3M',
                                         'y_1100-1615_GFSv16z0to8p2Strict', 'y_1215-1315_PARA6z1D0to10',
                                         'y_1200-1315_PARA012Z0to8', 'y_1300-1330_EC12Zb1', 'y_1100-1400_ECFcst12zb',
                                         'y_1200-2030_epsFcstGEFS6Z', 'y_0000-0600_18Z']

THU_CHOSEN_1410 = ['y_0800-1845_CFSMComb', 'y_0800-1845_PARACO18zp20to13', 'y_0800-1845_GEPSCOCombD', 'y_0800-1745_cfs12Z0to16p3', 'y_0800-1845_CFSCOCombWed', 'y_0800-1200_PARARACO0to8Comb', 'y_0800-1845_MTFComb', 'y_0800-1845_PARACO0zCombStrict', 'y_1815-2030_epsFcst12Z', 'y_0800-1315_TTFclose', 'y_0800-1315_TTFStrict', 'y_1415-1615_GFS6Zrolling2', 'y_1115-1415_PARACO6Z23D', 'y_0700-0800_EC0zbasic', 'y_1200-1315_PARA012Z0to8', 'y_0000-0400_GEM12Zc$b', 'y_1315-1715_CFS6z0to16p34', 'y_1315-1415_GEPSComb3', 'y_1300-1330_EC12Zb1', 'y_1415-1845_cashVsettlediff', 'y_1000-1100_GFSv160z2$2', 'y_1515-1845_WindTX', 'y_1845-1915_GFSv160zp4', 'y_1845-1915_GFSv160zp4$2', 'y_1615-1845_GEM12zp20to2', 'y_1145-1745_GEFS1D', 'y_0700-0730_GEM0Zp4', 'y_1730-1745_GEFS12Z', 'y_0800-1845_MTFCombStrict', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1215-1315_PARA6z1D0to10', 'y_0700-0730_EC', 'y_1000-1100_GFSv160z', 'y_0630-0800_MorningComb', 'y_0800-1845_MTFCombStrict$3', 'y_1300-1545_PARA018Z8to16', 'y_1645-1745_GEFSPM', 'y_1200-1300_PARA1D', 'y_0800-1845_weeklyMomentumd3', 'y_0800-1845_RelStrengthSell', 'y_0800-1945_cfs6z10to21D', 'y_1200-1745_CFSCOp234', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1200-2030_epsFcstGEFS6Z', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_1400-1530_Comb', 'y_1815-1900_GFScash']



if __name__ == '__main__':
    # drop_outlier_predictors_today(3,'real')
    # raise

    suffix = 'v8_12Z'
    # suffix = 'v8_12ZWind'
    mode = 'live'
    # mode = 'build'
    # time.sleep(60*12)

    if mode == 'build':
        test_strategies_structure()

    additional_csv_suffix = ''
    # additional_csv_suffix = '_FrwrdDaysShift'
    # additional_csv_suffix = '_BackDaysShift'
    # default - no replacment
    strats_days_replacement_dict = {}
    # shifting forward
    # strats_days_replacement_dict = {1:0,2:1,3:2,4:3}
    # shifting back
    # strats_days_replacement_dict = {0:1,1:2, 2:3,3:4}

    d = [0,1,2,3,4]
    d = 0
    # left to check d1/2/3 backward shift
    for ft in ['GDD','CDD','HDD'][:1]:
        # l = ['']
        specific_predictors_to_check = [x for x in
                                        # get_chosen_this_week_but_not_previous(d)
                                        # get_dropped_this_week_but_not_previous(d)
                                        # ALL_CONFS_COMBINED_BY_DAY[d]
                                        # ['NCF','coal','COAL','Coal','NGF','TTF'][-2:]
                                        # ['gefs','GEFS']
                                        # ['']
                                        # ['para','PARACO']
                                        [
                                         'y_0800-1845_PARACO0zComb'
                                            # 'y_0800-1945_GEFS0Zp20to16', 'y_0800-1945_GEFS0Zp20to16Strict',
                                            # 'y_0800-1745_EPS0zp1', 'y_0800-1745_EPS0zp1Strict',
                                            # 'y_0800-1745_EPS0zp2', 'y_0800-1745_EPS0zp2Strict',
                                            # 'y_0800-1745_EPS0zp4', 'y_0800-1745_EPS0zp4Strict',
                                            # 'y_0800-1745_GEPS0Zp1', 'y_0800-1745_GEPS0Zp2', 'y_0800-1745_GEPS0Zp4',
                                            # 'y_0800-1745_PARA0Zp4', 'y_0800-1745_EC0Zp12',
                                            # 'y_0800-1945_GEMCO0Zp1', 'y_0800-1945_GEMCO0Zp4'
                                        ]
                                        #   ALL_CONFS_COMBINED_BY_DAY[d]
                                        # WEEKLY_ADDITIONS_STRICT
                                        # ALL_CONFS_COMBINED_BY_DAY_LOOSE[d] if x not in ALL_CONFS_COMBINED_BY_DAY[d]
                                        # ALL_CONFS_COMBINED_BY_DAY[d]
                                        # WEEKLY_ADDITIONS
                                        # ALL_CONFS_COMBINED_BY_DAY_LAST_WEEK[d]
                                        # WEEKLY_ADDITIONS if x not in ALL_CONFS_COMBINED_BY_DAY_STRICT[d]
                                        # ALL_CONFS_COMBINED_BY_DAY_LOOSE[d] if x not in ALL_CONFS_COMBINED_BY_DAY[d]
                                        # ALL_CONFS_COMBINED_BY_DAY[d]
                                        # WEEKLY_ADDITIONS

                                         # if x not in ALL_CONFS_COMBINED_BY_DAY[d][len(WEEKLY_ADDITIONS):]

                                        # SPECIAL_IGNORES_FOR_HDD_BY_DAY[d] if x in ALL_CONFS_COMBINED_BY_DAY[d]+SPECIAL_ADDITIONS_FOR_HDD_BY_DAY[d]
                                            ]
        all_ms = ['0Z_a', '0Z_a2', '18Z', '18Z_cfs', '0Z', '0Z_b'] + ['6Z_a', '6Z_a2', '6Z', '6Z_cfs16d', '6Z_cfs'] + ['12Z_a', '12Z_a2', '12Z']
        # exclude_lst = [x for x in ALL_CONFS_COMBINED_BY_DAY[d]]
        exclude_lst = [] #[x if not isinstance(x,tuple) else x[0] for x in get_strategies_list_v1(days=[d],ms=all_ms)[1]]
        specific_predictors_to_check = list(set([x[0] if isinstance(x, tuple) else x for x in
                                        specific_predictors_to_check
                                        ]))
        #specific_predictors_to_check = [x for x in specific_predictors_to_check if 'GEPS' in x or 'CFS' in x or 'cfs' in x]
        prd= 'y_1530-1645_GEFStrend156z'
        #
        l = get_predictors_strats_from_production('',4)

        #compare_hdd_cdd()
        weekdays = [0,1,2,3,4]
        # weekdays = [d]

        if mode == 'live':
            plot = True
            calc = False
        else:
            plot = False
            calc = True
            weekdays = [0,1,2,3,4]
            # weekdays = [3,4]

        cond = lambda x: True
        #cond = lambda x: 12 <= int(x.split('y_')[-1][:2]) < 128
        strings_cond = lambda x: sum([s in x for s in specific_predictors_to_check]) and (x.replace('profit_','') not in exclude_lst)
        exact_string_cond = lambda x: sum([s == x.replace('profit_','') for s in specific_predictors_to_check]) and x.replace('profit_','') not in exclude_lst
        exact_opposite_cond = lambda x: sum([s == x.replace('profit_','') for s in specific_predictors_to_check]) == 0
        production_cond = lambda x: sum([s in x for weekday in weekdays for s in decision_maker.ALL_CONFS_COMBINED_BY_DAY[weekday]]) or sum([s in x for s in LAST_UPDATED_TOP])
        production_weekday_cond = lambda x: sum([s in x for weekday in weekdays for s in decision_maker.ALL_CONFS_COMBINED_BY_DAY[weekday]])
        all_cond = lambda x: 'para' in x.lower() and 'paraco' not in x.lower() # or 'GEM0Z' in x or 'yearly' in x
        all_cond = lambda x: '' in x

        cond = strings_cond
        # cond = exact_string_cond
        #cond = exact_opposite_cond
        cond = all_cond
        cond_str = '' #'_cond=13:00-'
        if mode == 'build':
            cond = all_cond
            pass
        # TODO hack to enable below code
        performance_sumary(suffix, ft=ft, cond_on_strats=cond, weekdays=weekdays,
                           calc_profits=calc, cond_str=cond_str, run_plots=plot,
                           days_replacement_dict=strats_days_replacement_dict,
                           additioanl_csv_suffix=additional_csv_suffix,
                           # cond_on_time_str=lambda x: x in ['18Z','0Z_a','0Z','0Z_b'],
                           start=dtdt(2021,7,1),end=dtdt(2025,3,31))  # 'y_1315' in x)
        raise
        start = dtdt(2021, 7, 1)
        end = dtdt(2030, 3, 31)

        strings_to_include_lst = [[model,model_hour]
                                  for model in ['para','paraco','gefs','ec','eps','epsco',
                                                'gem','gemco','geps','gefs35','cfs','eps45',
                                                'mid','short','0to2','0d','wind','wti',
                                                'ttf','ngf','d123','d04',''][-1:]
                                  for model_hour in [''] #['0z','6z','12z','18z'][-2:-1]
                                  ]
        #strings_to_include_lst = [['para','gefs','cfs']] # filtering looks really good!!!!
        #strings_to_include_lst = [['']]
        ADD_EXCLUDED = False
        for strings_to_include in strings_to_include_lst:
            if ADD_EXCLUDED:
                if strings_to_include[0] == 'para':
                    strings_to_exclude = ['paraco']
                elif strings_to_include[0] == 'eps':
                    strings_to_exclude = ['epsco']
                elif strings_to_include[0] == 'gem':
                    strings_to_exclude = ['gemco']
                else:
                    strings_to_exclude = []
            else:
                strings_to_exclude = []

            # calculate_calmars = False # todo
            calculate_calmars = True # todo
            allow_or = False
            #time.sleep(60*60*3)
            for SEPARATE_WEEKDAYS in [False, True][:1]:
            # SEPARATE_WEEKDAYS = False
                #weekdays = [0,4]
                print(f'Calling compact_performance_summary with strings_to_include={strings_to_include}, strings_to_exclude={strings_to_exclude}, weekdays={weekdays}')
                compact_performance_summary(suffix,ft,start, end, additional_csv_suffix, cond,weekdays=weekdays,
                                            strings_to_include=strings_to_include,
                                            strings_to_exclude=strings_to_exclude,
                                            plot_individuals=True,
                                            allow_or_in_condition=allow_or,calculate_calmars=calculate_calmars,
                                            separate_weekdays=SEPARATE_WEEKDAYS)

        print ('Finshed Script (%s)'%dtdt.now())


