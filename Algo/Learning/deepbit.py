"""
Python deepbit.
"""

import sys, os, numpy

####### CONSTANTS #######
DEEPBIT_VERSION = "V0.3"

INVALID_COMPLEXBIT_SCORE = float(-1)
ROWS_AXIS = 1
COLUMNS_AXIS = 0
NUM_OF_COLUMNS = 1
NUM_OF_ROWS = 0
LINEAR_REGRESSION_STEPS = 100
LOGICAL_AND = 1
LOGICAL_AND_NOT = 2
NO_SIMPLEBIT = -1
FIRST_SIMPLEBIT = 0
SIMPLEBIT_INDEX = 0
TWO_DIMENSIONAL = 2
ONE_DIMESIONAL = 1
FIRST = 0
NO_SHARED_SAMPLES = 0
NO_INFO = 0
NO_DATA = 0
NO_OPERATOR = -1
NO_COMPLEXBITS = 0
SUBSTENTIAL_NUM_OF_COMPLEXBITS = 100
SUBSTENTIAL_NUM_OF_SAMPLES = 50
SUBSTENTIAL_NUM_OF_PREDICTIONS = 20
SCORE_BY_CORRELATION_FUNC_CODE = "corr"
LINEAR_REGRESSION_FUNC_CODE = "linear"
NO_SIGN_CHANGE = 0
SINGLE_ROW = 1


####### CODE #######
class Deepbit:
    """
    deepbit algorithm.

    Notes:
    1. Variables with suffix _1d or _2d are numpy arrays with 1 dimension and 2 dimensions respectively.
    2. 't_' means transposed: t_arr_2d[feature] = samples (features are rows). arr_2d[sample] = features (samples are rows)
    3. Tests inside the code (not unit tests, because they are directly dependent on input).
    """

    def __init__(self, iterations=20, complexbits=400, depth=7, tested_bits=100, bits_per_feature=5,
                 filter_diversity=0.1, features_weights=None, score_boost=1.00001, score_func="corr",
                 complexbits_weights_func="linear", lin_reg_ridge=0.99, lin_reg_eta=0.01, logger=None):
        """
        Initialize deepbit.

        param iterations: number of iterations model is trained
        type iterations: int
        param complexbits: number of complexbits in trained model
        type complexbits: int
        param depth: number of simplebits ('layers') in every complexbit
        type depth: int
        param tested_bits: number of bits tested for every layer in every complexbit
        type tested_bits: int
        param bits_per_feature: number of simplebits generated from every feature in data
        type bits_per_feature: int
        param filter_diversity: minimum percentage of ones / zeros in a valid complexbit
        type filter_diversity: float
        param features_weights: The probabilities associated with each feature in train data in simplebits generation.
            Num of weights should match number of features (traditionally there were 693 features).
        type features_weights: list of floats
        param score_boost: amplifier to the score of the complexbit, so that only layer that improve score significantly
            would be added
        type score_boost: float
        param score_func: the code to the function that determines the score by which we evaluate new
        type score_func: string
        param complexbits_weights_func: the code to the function we use to set weights for our complexbits
        type complexbits_weights_func: string
        param lin_reg_ridge: linear regression ridge
        type lin_reg_ridge: float
        param lin_reg_eta: linear regression step size
        type lin_reg_eta: float
        param logger_automation: log object
        type logger_automation: logging.log
        """

        # assign deepbit parametrs
        self.iterations = iterations
        self.complexbits = complexbits
        self.depth = depth
        self.tested_bits = tested_bits
        self.bits_per_feature = bits_per_feature
        self.filter_diversity = filter_diversity
        self.score_boost = score_boost
        self.score_func = score_func
        self.complexbits_weights_func = complexbits_weights_func
        self.lin_reg_ridge = lin_reg_ridge
        self.lin_reg_eta = lin_reg_eta
        # convert to simplebits weights by multiply number of occurences according to bits_perfeature
        self.simplebits_weights = self._generate_simplebits_weights(features_weights, bits_per_feature)

        # save model information for a trained deepbit instance. Writing to these is only done inside the fit() function.
        self.simplebits_info = None
        self.complexbits_info = None

        # saved for intersection test with the x_test in predict function.
        self.x_train_2d = None

        # prevent multiple processes/threads from running simulteneously.
        self.is_running = False

        # developers parameters #
        # used to count measure runtime of a section of the code
        self.time = 0
        # deepbit "test mode" to ignore expected exceptions on executions that are not intended for production.
        self.is_test = False

        # log file
        self.logger = logger

    def get_params(self, deep=True):
        params = dict(
            iterations=self.iterations,
            complexbits=self.complexbits,
            depth=self.depth,
            tested_bits=self.tested_bits,
            bits_per_feature=self.bits_per_feature)
        return params

    def __repr__(self):
        return 'DeepBit' + str(self.get_params())

    #################### FIT ####################
    def fit(self, x_train_2d, y_train_1d):
        """
        train deepbit to data.

        param x_train_2d: matrix of training data.
        type x_train_2d: numpy.ndarray (type float64, shape [samples x features])
        param y_train_1d: 1d array of target values.
        type y_train_1d: numpy.ndarray (type float64, shape [labels])
        """

        self._log("Fit function started. Version: %s." % (DEEPBIT_VERSION,), "INFO")

        # prevent two threads from running fit simultaneously on the same deepbit instance
        if self.is_running:
            raise Exception("Two threads/processes attempted to execute deepbit simultaneously")
        self.is_running = True

        self._test_train_data_is_valid(x_train_2d, y_train_1d)

        # Initialize variables
        self.complexbits_info = []
        normalized_y_train_1d = self._normalize(y_train_1d)

        # save train data - to notify on sample sharing with x_test.
        self.x_train_2d = x_train_2d

        # generate simplebits and their weights
        simplebits_2d, self.simplebits_info = self._generate_simplebits(x_train_2d)
        # test simplebits weights
        self._test_simplebits_weights(simplebits_2d)

        for iteration_num in range(self.iterations):
            self._log("iteration num %s" % (iteration_num,), "INFO")

            # generate complexbits
            complexbits_2d, complexbits_creation_info = self._generate_complexbits(simplebits_2d, normalized_y_train_1d)

            # generate complexbits weights
            complexbits_weights_1d = self._generate_complexbits_weights(complexbits_2d, normalized_y_train_1d)

            # generate means of complexbits (columns in complexbits_2d)
            complexbits_means_1d = complexbits_2d.mean(axis=COLUMNS_AXIS)

            # save complexbits info
            self.complexbits_info.append((complexbits_creation_info, complexbits_weights_1d, complexbits_means_1d))

        self._test_infos_are_valid(self.simplebits_info, self.complexbits_info)
        self.is_running = False
        self._log("Fit function finished successfuly.\n", "INFO")

    # simplebit generation #
    def _generate_simplebits(self, x_train_2d):
        """
        Generates simplebits

        param x_train_2d: matrix of training data.
        type x_train_2d: numpy.ndarray (type float64, shape [samples x features])

        return simplebits_2d: matrix of simplebits
        type simplebits_2d: numpy.ndarray (type bool, shape [samples x simplebits])
        return simplebits_info: list of tuples of format (feature_index, threshold)
        type simplebits_info: list of tuples
        """

        # intiates the simplebits and simplebits info
        t_simplebits, simplebits_info = [], []

        # iterate over all features
        for feature_index, single_feature_1d in enumerate(x_train_2d.transpose()):

            # generate list of thresholds (with which simplebits are made) for a given feature.
            simplebit_thresholds_1d = self._generate_thresholds(single_feature_1d)

            for threshold in simplebit_thresholds_1d:
                # generate new simplebit bsed on a given threshold
                new_simplebit_1d = single_feature_1d > threshold

                # save information of every simplebit, so that they could be reconstructed on predict method.
                simplebits_info.append((feature_index, threshold))

                # add simplebit to simplebit array
                t_simplebits.append(new_simplebit_1d)

        simplebits_2d = numpy.array(t_simplebits).transpose()

        self._test_generate_simplebits(simplebits_2d, x_train_2d)
        self._log("Simplebits generation finished successfuly", "INFO")

        return simplebits_2d, simplebits_info

    def _generate_thresholds(self, feature_1d):
        """
        Generate array of thresholds for a given feature.

        param feature_1d: 1d array with all samples of a single feature in the data.
        type feature_1d: numpy.ndarray (type float64, shape [samples])

        return simplebit_thresholds_1d: 1d array of thresholds for a given feature.
        type simplebit_thresholds_1d: numpy.ndarray (type float64, shape [thresholds])
        """

        # set the threshold step size according to how many simplebits are desired per feature.
        threshold_step_size = int(len(feature_1d) / (self.bits_per_feature + 1))

        # pick the thresholds for the simplebits generated from this feature
        thresholds_indexes_1d = threshold_step_size * numpy.array(range(1, self.bits_per_feature + 1))
        simplebit_thresholds_1d = numpy.sort(feature_1d)[thresholds_indexes_1d]

        self._test_generate_thresholds(simplebit_thresholds_1d)

        return simplebit_thresholds_1d

    def _generate_simplebits_weights(self, features_weights, bits_per_feature):
        """
        Generate weights for simplebits, deciding how likely they are to be chosen randomly when
        generating complexbits

        param features_weights: the probabilities associated with each feature in train data in simplebits generation.
            Num of weights should match number of features.
        type features_weights: list of floats
        param bits_per_feature: number of simplebits generated from every feature in data
        type bits_per_feature: int

        return simplebits_weights: list of simplebits weights
        type simplebits_weights: list
        """

        if features_weights is None:
            return None

        # convert to simplebit weights, by multiplying number of columns according to self.bits_per_feature
        simplebits_weights_1d = numpy.outer(features_weights, numpy.ones(bits_per_feature)).flatten()
        # sum of weights has to be 1
        simplebits_weights_1d /= sum(simplebits_weights_1d)
        # convert simplebits back to list from numpy array
        simplebits_weights = list(simplebits_weights_1d)

        return simplebits_weights

    # complexbits generation #
    def _generate_complexbits(self, simplebits_2d, normalized_y_train_1d):
        """
        Generate complexbits

        param simplebits_2d: matrix of simplebits
        type simplebits_2d: numpy.ndarray (type bool, shape [samples x simplebits])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return complexbits_2d: 2d array of complexbits.
        type complexbits_2d: numpy.ndarray (type bool, shape [samples x complexbits])
        return complexbits_creation_info: info describing complexbits for the reconstruction function.
        type complexbits_creation_info: list of tuples.
        """

        # list of all generated complexits and complexbits info.
        t_complexbits, complexbits_creation_info = [], []

        # generate set amount of complexbits, one in every iteration of this loop
        for complexbit_index in range(self.complexbits):
            # generate complexbit and append it and its info to their lists
            current_complexbit, complexbit_creation_info = self._generate_single_complexbit(simplebits_2d,
                                                                                            normalized_y_train_1d)
            complexbits_creation_info.append(complexbit_creation_info)
            t_complexbits.append(current_complexbit)

        # the Y value is per samples, so transform the array from (complexbits,samples) to (samples, complexbits).
        complexbits_2d = numpy.array(t_complexbits).transpose()

        #self._test_complexbits_are_valid(complexbits_2d)
        self._log("Complexbits generation finished sucessfuly.", "INFO")

        return complexbits_2d, complexbits_creation_info

    def _generate_single_complexbit(self, simplebits_2d, normalized_y_train_1d):
        """
        Generate a single complexbit

        param simplebits_2d: matrix of simplebits
        type simplebits_2d: numpy.ndarray (type bool, shape [samples x simplebits])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return complexbit_1d: 1d array describing a complexbit.
        type complexbit_1d: numpy.ndarray (type bool, shape [samples])
        return complexbit_creation_info: info describing a single complexbit for the reconstruction function.
        type complexbit_creation_info: list of tuples.
        """

        # information descirbing the current complexbit (info is important for reconstruction in predict)
        complexbit_creation_info = []

        # define new complexbit as all ones.
        complexbit_1d = numpy.ones(normalized_y_train_1d.size, dtype=int)
        # initial complexbit score is invalid
        current_score = INVALID_COMPLEXBIT_SCORE

        for depth_index in range(self.depth):
            # add a simplebit that when combined with the current complexbit yeilds the best correlation to Y.
            complexbit_1d, layer_info, current_score = \
                self._add_layer_to_complexbit(complexbit_1d, simplebits_2d, normalized_y_train_1d,
                                              current_score)

            # add layer information (index of simpelbit and operator) to the complexbit info
            complexbit_creation_info.append(layer_info)

        self._test_complexbit_is_valid(complexbit_1d, complexbit_creation_info)

        return complexbit_1d, complexbit_creation_info

    def _add_layer_to_complexbit(self, complexbit_1d, simplebits_2d, normalized_y_train_1d, current_score):
        """
        Add to complexbit a random simplebit that improves correlation to normalized y.

        param complexbit_1d: 1d array describing current complexbit.
        type complexbit_1d: numpy.ndarray (type bool, shape [samples])
        param simplebits_2d: matrix of simplebits
        type simplebits_2d: numpy.ndarray (type bool, shape [samples x simplebits])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])
        return current_score: score of current complexbit, to that only layers that improve would be added
        type current_score: numpy.float

        return best_complexbit_1d: 1d array describing the best generated complexbit found randomly.
        type best_complexbit_1d: numpy.ndarray (type bool, shape [samples])
        return layer_info: info of chosen layer (operator & simplebit index)
        type layer_info: tuple of format (index, operator)
        return best_complexbit_score: score of best complexbit from pool
        type best_complexbit_score: numpy.float
        """

        # generate potential complexbits to choose from (greedily) for next depth
        t_complexbits_pool_2d, layer_infos_1d = self._generate_potential_complexbits(complexbit_1d, simplebits_2d)
        # filter bad complexbits
        t_complexbits_pool_2d, layer_infos_1d = self._filter_bad_complexbits(t_complexbits_pool_2d, layer_infos_1d)
        # if no complexbits were supplied, then return no complexbits found
        if t_complexbits_pool_2d.shape[NUM_OF_ROWS] == NO_COMPLEXBITS:
            return complexbit_1d, (NO_SIMPLEBIT, NO_OPERATOR), current_score

        # find best complexbit of the generated complexbits pool that passed filtration
        best_complexbit_index, best_complexbit_score = \
            self._find_best_complexbit(t_complexbits_pool_2d, complexbit_1d, normalized_y_train_1d)

        # amplify current score so that only complexbits that improve results substentially would be added
        amplified_current_score = current_score * self.score_boost
        # if no improving complexbit was found, add an empty layer to the complexbit
        if amplified_current_score > best_complexbit_score:
            return complexbit_1d, (NO_SIMPLEBIT, NO_OPERATOR), current_score

        # extract the best complexbit value, and it's infos (simplebit & operator)
        best_complexbit_1d, layer_info = t_complexbits_pool_2d[best_complexbit_index], layer_infos_1d[
            best_complexbit_index]

        return best_complexbit_1d, tuple(layer_info), best_complexbit_score

    def _generate_potential_complexbits(self, complexbit_1d, simplebits_2d):
        """
        generates a pool of potential complexbit by randomizing a pool for simplbits combined with different operators.

        param complexbit_1d: 1d array describing current complexbit.
        type complexbit_1d: numpy.ndarray (type bool, shape [samples])
        param simplebits_2d: matrix of simplebits
        type simplebits_2d: numpy.ndarray (type bool, shape [samples x simplebits])

        return t_complexbits_pool_2d: 2d array of generated potential complexbits
        type t_complexbits_pool_2d: numpy.ndarray (type bool, shape [complexbits x samples])
        return layer_infos_1d: info of layer (operator & simplebit index) added for every complexbit
        type layer_infos_1d:  numpy.ndarray (type object, shape [(index, operator)])
        """

        # pick a random pool of simplebits. If weights are given then some simplebits will be more probable than others.
        pool_simplebit_indexes_1d = numpy.random.choice(simplebits_2d.shape[NUM_OF_COLUMNS], self.tested_bits,
                                                        p=self.simplebits_weights, replace=False)

        t_simplebits_pool_2d = simplebits_2d.transpose()[pool_simplebit_indexes_1d]

        # generate a pool of operators to combine simplebits and complexbits. +1 because RANDINT is exclusive to upper bound.
        pool_operators_1d = numpy.random.randint(low=LOGICAL_AND, high=(LOGICAL_AND_NOT + 1), size=(self.tested_bits,))

        # flip simplebits assigned AND_NOT before doing the AND operator
        t_simplebits_pool_2d[pool_operators_1d == LOGICAL_AND_NOT] = numpy.logical_not( \
            t_simplebits_pool_2d[pool_operators_1d == LOGICAL_AND_NOT])
        # generate complexbits from the pool of simplebits (combined by AND and AND_NOT operator)
        t_complexbits_pool_2d = numpy.logical_and(complexbit_1d, t_simplebits_pool_2d)

        # info of layer added for every complexbit
        layer_infos_1d = numpy.array(list(zip(pool_simplebit_indexes_1d, pool_operators_1d)))

        return t_complexbits_pool_2d, layer_infos_1d

    def _filter_bad_complexbits(self, t_complexbits_pool_2d, layer_infos_1d):
        """
        filter bad complexbits

        param t_complexbits_pool_2d: 2d array of generated potential complexbits
        type t_complexbits_pool_2d: numpy.ndarray (type bool, shape [complexbits x samples])
        param layer_infos_1d: info of layer (operator & simplebit index) added for every complexbit
        type layer_infos_1d:  numpy.ndarray (type object, shape [(index, operator)])

        return t_filtered_complexbits_pool_2d: filtered 2d array of generated potential complexbits
        type t_filtered_complexbits_pool_2d: numpy.ndarray (type bool, shape [complexbits x samples])
        return filtered_layer_infos_1d: filtered info of layer (operator & simplebit index) added for every complexbit
        type filtered_layer_infos_1d:  numpy.ndarray (type object, shape [(index, operator)])
        """

        # init a filter that matches every complexbit whether its valid or not
        valid_complexbits_filter_1d = numpy.ones(t_complexbits_pool_2d.shape[NUM_OF_ROWS], dtype=bool)

        # filter complexbits with too little diversity #
        # calcualte percentage of ones in every complexbits (=mean, because this is boolean)
        complexbits_means_1d = t_complexbits_pool_2d.mean(axis=ROWS_AXIS)
        # filter complexbits with too low percentage of zeros
        valid_complexbits_filter_1d[complexbits_means_1d > (1 - self.filter_diversity)] = False
        # filter complexbits with too low percentage of ones
        valid_complexbits_filter_1d[complexbits_means_1d < self.filter_diversity] = False

        # takes only unflitered complexbits
        t_filtered_complexbits_pool_2d = t_complexbits_pool_2d[valid_complexbits_filter_1d]
        filtered_layer_infos_1d = layer_infos_1d[valid_complexbits_filter_1d]

        return t_filtered_complexbits_pool_2d, filtered_layer_infos_1d

    # find best complexbit of pool #
    def _find_best_complexbit(self, t_complexbits_pool_2d, current_complexbit_1d, normalized_y_train_1d):
        """
        find best complexbit from a given pool.

        param t_all_complexbits_pool_2d: 2d array of generated potential complexbits
        type t_all_complexbits_pool_2d: numpy.ndarray (type bool, shape [complexbits x samples])
        param current_complexbit_1d: 1d array describing current complexbit.
        type current_complexbit_1d: numpy.ndarray (type bool, shape [samples])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return best_complexbit_index: index in pool of the chosen complexbit
        type best_complexbit_index: int
        return best_complexbit_score: score of best complexbit from pool
        type best_complexbit_score: numpy.float
        """

        # pick score function based on deepbit parameter
        if self.score_func == SCORE_BY_CORRELATION_FUNC_CODE:
            _get_scores = self._get_scores_corr
        else:
            raise Exception("Unsupported score function was chosen")

        # calculate scores of all complexbits pool
        complexbits_scores_1d = _get_scores(t_complexbits_pool_2d, normalized_y_train_1d)

        # get the index and the score of the complexbit with the best score
        best_complexbit_index = numpy.argmax(complexbits_scores_1d)
        best_complexbit_score = complexbits_scores_1d[best_complexbit_index]

        return best_complexbit_index, best_complexbit_score

    def _get_scores_corr(self, t_complexbits_2d, normalized_y_train_1d):
        """
        calculates pearsonr correlations of a given 2d complexbit array to normalized y train vector efficiently.

        param t_complexbits_2d: 2d array of complexbits (assumes complexbits are boolean)
        type t_complexbits_2d: numpy.ndarray (type bool, shape [complexbits x samples])
        param normalized_y_train_1d: 1d array of normalized target values. (assumes y is normalized)
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return correlations_1d: vector of pearsonr correlation of every complexbit to the normalized y vector.
        type correlations_1d: numpy.ndarray (type float64, shape [correlations])
        """

        # normalize complexbits matrix
        t_normalized_complexbits_2d = self._normalize(t_complexbits_2d)

        # calculate "pearsonr correlation" on normalized vectors
        yy_sum = numpy.dot(normalized_y_train_1d, normalized_y_train_1d)
        xy_sums_1d = numpy.dot(t_normalized_complexbits_2d, normalized_y_train_1d)
        xx_sums_1d = (t_normalized_complexbits_2d ** 2).sum(axis=ROWS_AXIS)
        denominators_1d = numpy.sqrt(xx_sums_1d * yy_sum)
        correlations_1d = xy_sums_1d / denominators_1d

        # score of absolute value of correlation
        correlations_1d = abs(correlations_1d)

        self._test_correlations_are_valid(correlations_1d, normalized_y_train_1d)

        return correlations_1d

    # complexbits weights generation #
    def _generate_complexbits_weights(self, complexbits_2d, normalized_y_train_1d):
        """
        generate weights to complexbits according to the picked weights function.

        param complexbits_2d: 2d array of complexbits
        type complexbits_2d: numpy.ndarray (type bool, shape [samples x complexbits])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return weights_1d: vector of linear weights
        type weights_1d: numpy.ndarray (type float64, shape [weights])
        """

        self._test_vector_is_normalized(normalized_y_train_1d)

        # pick weights functions according to the one defined in the instance init
        if self.complexbits_weights_func == LINEAR_REGRESSION_FUNC_CODE:
            _generate_complexbits = self._linear_regression
        else:
            raise Exception("Unrecognized weights function")

        # generate weights according to the chosen weights function
        weights_1d = _generate_complexbits(complexbits_2d, normalized_y_train_1d)

        self._test_weights_are_valid(weights_1d)
        self._log("Complexbits weight generation finished successfuly.", "INFO")

        return weights_1d

    def _linear_regression(self, complexbits_2d, normalized_y_train_1d):
        """
        generate linear weights to complexbits

        param complexbits_2d: 2d array of complexbits
        type complexbits_2d: numpy.ndarray (type bool, shape [samples x complexbits])
        param normalized_y_train_1d: 1d array of normalized target values.
        type normalized_y_train_1d: numpy.ndarray (type float64, shape [labels])

        return weights_1d: vector of linear weights
        type weights_1d: numpy.ndarray (type float64, shape [weights])
        """

        # initialize variables #
        updated_normalized_y_train_1d = normalized_y_train_1d.copy()
        t_normalized_complexbits_2d = self._normalize(complexbits_2d.transpose())
        weights_1d = numpy.zeros(t_normalized_complexbits_2d.shape[NUM_OF_ROWS], dtype=float)

        for cur_step in range(LINEAR_REGRESSION_STEPS):
            for index, complexbit_1d in enumerate(t_normalized_complexbits_2d):
                # calculate step
                sumxx = numpy.dot(complexbit_1d, complexbit_1d)
                sumxy = numpy.dot(complexbit_1d, updated_normalized_y_train_1d)
                alpha = (sumxy / sumxx) * self.lin_reg_eta

                # increment weights and apply ridge
                old_weight = weights_1d[index]
                weights_1d[index] += alpha
                weights_1d[index] *= self.lin_reg_ridge

                # update target vector
                dydx = weights_1d[index] - old_weight
                updated_normalized_y_train_1d -= complexbit_1d * dydx

        return weights_1d

    # fit utilities #
    def _normalize(self, arr_1d2d):
        """
        Reduce mean from every value of a given vector. If vector is 2d, then every row in vector is normalized.

        param arr_1d2d: a 1 dimensional or 2 dimensional vector
        type arr_1d2d: numpy.ndarray (type float, shape [values] or [values x values])

        return normalized_arr_1d2d: normalized vector
        type normalized_arr_1d2d: numpy.ndarray (type float64, shape [values] or [values x values])
        """

        # one dimensional
        if len(arr_1d2d.shape) == ONE_DIMESIONAL:
            normalized_arr_1d2d = arr_1d2d - arr_1d2d.mean()
        # two dimensional
        elif len(arr_1d2d.shape) == TWO_DIMENSIONAL:
            # calculate mean of every row
            arr_means_1d = arr_1d2d.mean(axis=ROWS_AXIS)
            # normalize row and return vector untransposed
            normalized_arr_1d2d = (arr_1d2d.transpose() - arr_means_1d).transpose()
        else:
            raise Exception("_normalize() received a vector that is neither one dimensional nor two dimensional")

        return normalized_arr_1d2d

    ################## PREDICT ##################
    def predict(self, x_test_2d):
        """
        calculates estimated y for every sample in x_test

        param x_test_2d: matrix of testing data.
        type x_test_2d: numpy.ndarray (type float64, shape [samples x features])

        return avg_of_estimated_y_1d: array of estimated y value for every sample in x_test
        type avg_of_estimated_y_1d: numpy.ndarray (type float64, shape [predictions])
        """

        self._log("Predict function started. Version: %s." % (DEEPBIT_VERSION,), "INFO")

        # prevent two threads from running fit simultaneously on the same deepbit instance
        if self.is_running:
            raise Exception("Two threads/processes attempted to execute deepbit simultaneously")
        self.is_running = True

        estimated_y_sums_1d = numpy.zeros(x_test_2d.shape[NUM_OF_ROWS])

        self._test_deepbit_is_trained()
        # self._test_no_train_test_intersection(self.x_train_2d, x_test_2d) # IDO 24-Nov-2019

        # extract complexbit info for every iteration
        for complexbits_creation_info, complexbits_weights_1d, complexbits_means_1d in self.complexbits_info:
            # reconstruct complexbit from trained model info
            complexbits_2d = self._reconstruct_complexbits(x_test_2d, complexbits_creation_info)

            # semi-normalizes columns (complexbits). "semi" because the mean deducted not theirs.
            semi_normalized_complexbits_2d = complexbits_2d - complexbits_means_1d

            # sum y estimations of all iterations
            estimated_y_sums_1d += numpy.dot(semi_normalized_complexbits_2d, complexbits_weights_1d)

        # average estimated Y of all iterations
        avg_of_estimated_y_1d = estimated_y_sums_1d / self.iterations

        self._test_predictions_are_valid(avg_of_estimated_y_1d)
        self.is_running = False
        self._log("Prediction generation finished successfuly.\n", "INFO")

        return avg_of_estimated_y_1d

    # complexbits reconstructio #
    def _reconstruct_complexbits(self, x_test_2d, complexbits_creation_info):
        """
        reconstruct complexbits with test data according to trained model info

        param x_test_2d: matrix of testing data.
        type x_test_2d: numpy.ndarray (type float64, shape [samples x features])
        param complexbits_creation_info: info describing complexbits of the trained model.
        type complexbits_creation_info: list of tuples.

        return reconstructed_complexbits_2d: array of reconstructed complexbits
        type reconstructed_complexbits_2d: numpy.ndarray (type float64, shape [complexbits x samples])
        """

        t_reconstructed_complexbits = []

        # iteration over all complexbit infos and reconstruct every complexbit
        for complexbit_creation_info in complexbits_creation_info:
            complexbit_1d = self._reconstruct_single_complexbit(x_test_2d, complexbit_creation_info)

            t_reconstructed_complexbits.append(complexbit_1d)

        reconstructed_complexbits_2d = numpy.array(t_reconstructed_complexbits).transpose()

        self._test_reconstructed_complexbits_are_valid(reconstructed_complexbits_2d)

        return reconstructed_complexbits_2d

    def _reconstruct_single_complexbit(self, x_test_2d, complexbit_creation_info):
        """
        reconstruct a single complexbit with test data according to given info

        param x_test_2d: matrix of testing data.
        type x_test_2d: numpy.ndarray (type float64, shape [samples x features])
        param complexbit_creation_info: info describing a single complexbit for the reconstruction function.
        type complexbit_creation_info: list of tuples.

        return reconstructed_complexbit_1d: single reconstructed complexbit
        type reconstructed_complexbit_1d: numpy.ndarray (type float64, shape [samples])
        """

        # initialize new complexbit we construct according to saved information with all 1s.
        reconstructed_complexbit_1d = numpy.ones(x_test_2d.shape[NUM_OF_ROWS])

        # iterate over every simplebit in the complexbit we are reconstructing now.
        for simplebit_index, simplebit_operator in complexbit_creation_info:

            # if complexbit saved no simplebit at the current layer, then skip this layer.
            if simplebit_index == NO_SIMPLEBIT:
                continue

            # reconstruct specified simplebit according to its information
            reconstructed_simplebit_1d = self._reconstruct_single_simplebit(x_test_2d,
                                                                            self.simplebits_info[simplebit_index])

            # if operator is AND NOT, then computer NOT of simplebit before AND operation
            if simplebit_operator == LOGICAL_AND_NOT:
                reconstructed_simplebit_1d = numpy.logical_not(reconstructed_simplebit_1d)

            reconstructed_complexbit_1d = numpy.logical_and(reconstructed_complexbit_1d, reconstructed_simplebit_1d)

        return reconstructed_complexbit_1d

    def _reconstruct_single_simplebit(self, x_test_2d, simplebit_info):
        """
        reconstruct a single simplebit from trained model info

        param x_test_2d: matrix of testing data.
        type x_test_2d: numpy.ndarray (type float64, shape [samples x features])
        param simplebit_info: info describing all simplebits of the trained model
        type simplebit_info: list of tuples.

        return reconstructed_simplebit_1d: single reconstructed simplebit
        type reconstructed_simplebit_1d: numpy.ndarray (type float64, shape [samples])
        """

        # translate simplebit index to the simplebit characteristics: feature and threshold	.
        feature_index, threshold = simplebit_info

        # extract all samples values in the specified index (taken from the current simplebit info)
        relevant_feature_1d = x_test_2d.transpose()[feature_index]

        # reconstruct specified simplebit according to its information
        reconstructed_simplebit_1d = relevant_feature_1d > threshold

        return reconstructed_simplebit_1d

    ################## OTHER ##################
    def _log(self, message, severity):
        """
        log or print to console a message according to format

        param message: message content
        type message: string
        param severity: severity level of log
        type severity: string
        """

        if self.logger == None:
            # print to console only logs that are not info
            if severity != "INFO":
                print("{} - {}".format(severity, message))
        else:
            if severity == "INFO":
                self.logger.info(message)
            elif severity == "WARNING":
                self.logger.warning(message)
            elif severity == "ERROR":
                self.logger.error(message)
            else:
                raise Exception("Invalid log message severity: {}. msg: {}".format(severity, message))

    ################## TESTS ##################
    def _test_simplebits_weights(self, simplebits_2d):
        if simplebits_2d.shape[NUM_OF_COLUMNS] < self.tested_bits:
            raise Exception("Number of tested bits is larger than total number of simplebits:"
                            "(simplebits: {}. tested bits: {}.)".format(
                simplebits_2d.shape[NUM_OF_COLUMNS], self.tested_bits))

        if self.simplebits_weights is None:
            return

        if numpy.count_nonzero(self.simplebits_weights) < self.tested_bits:
            raise Exception("there are less simplebits with non-zero weights than tested_bits:"
                            "(non-zeros: {}. tested bits: {}.)".format(
                self.simplebits_weights, self.tested_bits))

        if len(self.simplebits_weights) != len(simplebits_2d.transpose()):
            raise Exception("Num of simplebits weights ({}) was different from num of simplebits ({})".format(
                len(self.simplebits_weights), len(simplebits_2d.transpose())))

        if not numpy.isclose(sum(self.simplebits_weights), 1):
            raise Exception("simplebits weights were not normalized properly")

    def _test_generate_thresholds(self, simplebit_thresholds_1d):
        if len(simplebit_thresholds_1d) != self.bits_per_feature:
            self._log("Too many/little thresholds were generated", "ERROR")

    def _test_generate_simplebits(self, simplebits_2d, x_train_2d):
        t_simplebits_2d = simplebits_2d.transpose()
        simplebits_means_1d = t_simplebits_2d.mean(axis=ROWS_AXIS)

        if (len(x_train_2d.transpose()) * self.bits_per_feature) != len(t_simplebits_2d):
            raise Exception("number of simplebits ({}) was not as expected ({})".format(
                len(t_simplebits_2d), len(x_train_2d.transpose()) * self.bits_per_feature))

        if (not self.is_test) and numpy.isclose(simplebits_means_1d, 0).any():
            self._log("%s simplebits had all 0".format(
                numpy.count_nonzero(numpy.isclose(simplebits_means_1d, 0)), ), "WARNING")

        if (not self.is_test) and len(simplebits_2d) > SUBSTENTIAL_NUM_OF_SAMPLES:
            if (simplebits_2d.mean(axis=COLUMNS_AXIS) > 0.98).any():
                self._log("some simplebits had an unexpectedly high number of ones", "WARNING")

    def _test_train_data_is_valid(self, x_train_2d, y_train_1d):
        if x_train_2d.shape[NUM_OF_ROWS] != len(y_train_1d):
            raise Exception("Number of train samples does not match number of train labels. "
                            "(samples: {}. labels: {})".format(x_train_2d.shape[NUM_OF_ROWS], len(y_train_1d)))

        if len(x_train_2d) == NO_DATA:
            raise Exception("Fit was called with empty x_train data.")

        # if numpy.isclose(x_train_2d.sum(axis=ROWS_AXIS), 0).any() or \ !!! idan
        # 	numpy.isclose(x_train_2d.sum(axis=COLUMNS_AXIS), 0).any():
        # 	self._log("There was an all-zeros sample/feature in xy file data.", "WARNING")

        if (not self.is_test) and (x_train_2d.shape[NUM_OF_ROWS] < SUBSTENTIAL_NUM_OF_SAMPLES):
            self._log("Not enough train data for deepbit to operate properly ({} samples)".format(
                x_train_2d.shape[NUM_OF_ROWS], ), "ERROR")

    def _test_complexbits_are_valid(self, complexbits_2d):
        # these tests are only relevant if there's a substential number of complexbits
        if (not self.is_test) and (self.complexbits >= SUBSTENTIAL_NUM_OF_COMPLEXBITS):
            # 1
            # calculate correlation matrix of complexbits to themselves
            co_correlations_2d = numpy.corrcoef(complexbits_2d.transpose())
            # zerofy the correlation of complexbit to itself
            co_correlations_2d[numpy.isclose(co_correlations_2d, 1.0)] = 0
            # for every complexbit, find the correlation to the other complexbit in pool that is most correlated to it
            max_co_correlation_per_complexbit_1d = numpy.array(map(numpy.max, co_correlations_2d))  ###!!!
            # max_co_correlation_per_complexbit_1d = numpy.max(co_correlations_2d, axis=1) ###!!!
            # calculate the mean of these max correlations
            complexbits_correlation_score = max_co_correlation_per_complexbit_1d.mean()
            if complexbits_correlation_score > 0.97:
                self._log("Complexbits are unexpectedly too correlated to eachother", "WARNING")
            # if there is a substential amount of complexbits in pool, and their correlations are extremely low
            if complexbits_correlation_score < 0.03:
                self._log("Complexbits are unexpectedly uncorrelated to eachother", "WARNING")

            # 2
            if complexbits_2d.mean() > 0.9:
                self._log("overall complexbits had an unexpectedly high number of ones", "WARNING")
            if complexbits_2d.mean() < 0.03:
                self._log("overall complexbits had an unexpectedly low number of ones", "WARNING")

    def _test_reconstructed_complexbits_are_valid(self, reconstructed_complexbits_2d):
        # these tests are only relevant if there's a substential number of complexbits
        if (not self.is_test) and (self.complexbits >= SUBSTENTIAL_NUM_OF_COMPLEXBITS):
            if reconstructed_complexbits_2d.mean() < 0.02:
                self._log("Reconstructed complexbits had an unexpectedly low number of ones", "WARNING")
            if reconstructed_complexbits_2d.mean() > 0.98:
                self._log("Reconstructed complexbits had an unexpectedly high number of ones", "WARNING")

    def _test_infos_are_valid(self, simplebits_info, complexbits_info):
        for complexbits_creation_info, _, _ in complexbits_info:
            # verify there's no empty complexbit (all '-1')
            if ([(-1, -1)] * self.depth) in complexbits_creation_info:
                self._log("An empty complexbit was saved in info", "ERROR")

            # calcuolte percentage of layers that are empty
            num_of_nonempty_layers = numpy.count_nonzero(
                numpy.array(complexbits_creation_info != numpy.array([-1, -1]))) / 2
            nonempty_layers_percentage = float(num_of_nonempty_layers) / (self.complexbits * self.depth)
            if (nonempty_layers_percentage < 0.2) and (not self.is_test):
                self._log("Number of empty layers in complexbits is too large (>80%)", "WARNING")

    def _test_weights_are_valid(self, complexbits_weights_1d):

        # test no weight abs value goes beyond estimated limit
        if (not self.is_test) and (self.complexbits > SUBSTENTIAL_NUM_OF_COMPLEXBITS) and \
                (abs(complexbits_weights_1d) > 15).any():
            self._log("Weights value too high ({})".format(complexbits_weights_1d, ), "WARNING")

        # test no weight is nan or inf
        if numpy.isnan(complexbits_weights_1d).any() or numpy.isinf(complexbits_weights_1d).any():
            self._log("Some weights were 'nan' or 'inf'", "ERROR")

    def _test_correlations_are_valid(self, correlations_1d, normalized_y_train_1d):
        # test no correlation is nan or inf
        if numpy.isnan(correlations_1d).any() or numpy.isinf(correlations_1d).any():
            self._log("Some correlations were 'nan' or 'inf'", "ERROR")

        if (correlations_1d < -1).any() or (correlations_1d > 1).any():
            if not numpy.isclose(sum(normalized_y_train_1d), 0):
                self._log("Calculate correlations func expects y that is normalized.", "ERROR")

            self._log("Bug in correlation calculation... correlation was above 1 or below -1.", "ERROR")

        if (not self.is_test) and (len(normalized_y_train_1d) >= SUBSTENTIAL_NUM_OF_SAMPLES) and \
                ((correlations_1d < -0.8).any() or (correlations_1d > 0.8).any()):
            self._log("Atleast one generated complexbit had an unreasonably high correlation (>0.8)", "WARNING")

    def _test_vector_is_normalized(self, normalized_y_train_1d):
        if not numpy.isclose(numpy.sum(normalized_y_train_1d), 0):
            self._log("Y values given to linear regression were not normalized as expected.", "ERROR")

    def _test_complexbit_is_valid(self, complexbit_1d, complexbit_creation_info):
        if (not self.is_test) and \
                (not (complexbit_1d == 0).any()) or (not (complexbit_1d == 1).any()) or numpy.isnan(
            complexbit_1d).any():
            self._log("All values of complexbit were the same.", "WARNING")

        if complexbit_creation_info[FIRST_SIMPLEBIT][SIMPLEBIT_INDEX] == NO_SIMPLEBIT:
            self._log("First simplebit in a complexbit was signed as -1.", "ERROR")

    def _test_deepbit_is_trained(self):
        if (self.simplebits_info is None) or (self.complexbits_info is None) or (self.x_train_2d is None) or \
                (len(self.simplebits_info) == NO_INFO) or (len(self.complexbits_info) == NO_INFO) or \
                (len(self.x_train_2d) == NO_DATA):
            raise Exception("Deepbit is not trained.")

    def _test_no_train_test_intersection(self, x_train_2d, x_test_2d):
        if len(x_test_2d) == NO_DATA:
            raise Exception("Prediction was called with empty x_test data.")

        if (len(x_train_2d.shape) != TWO_DIMENSIONAL) or (len(x_test_2d.shape) != TWO_DIMENSIONAL):
            raise Exception("x_test_2d or x_train_2d were not two dimensional as expected."
                            " (x_train_2d shape: {}. x_test_2d shape: {})".format(x_train_2d.shape, x_test_2d.shape))

        if x_train_2d.shape[NUM_OF_COLUMNS] != x_test_2d.shape[NUM_OF_COLUMNS]:
            if not self.is_test:
                self._log("x_test_2d or x_train_2d had different number of features."
                          " Train: {}. Test: {} ".format(x_train_2d.shape[NUM_OF_COLUMNS],
                                                         x_test_2d.shape[NUM_OF_COLUMNS]), "ERROR")
        else:
            nrows, ncols = x_train_2d.shape
            dtype = {'names': ['f{}'.format(i) for i in range(ncols)], 'formats': ncols * [x_train_2d.dtype]}

            C = numpy.intersect1d(x_train_2d.view(dtype), x_test_2d.view(dtype))
            C = C.view(x_train_2d.dtype).reshape(-1, ncols)

            if (not self.is_test) and (C.size > NO_SHARED_SAMPLES):
                self._log("Train and test data shared samples.", "WARNING")

    def _test_predictions_are_valid(self, avg_of_estimated_y_1d):
        if (not (abs(avg_of_estimated_y_1d) < 1000).all()) and (not self.is_test):
            self._log("some predictions had an unreasonable value (>1000)", "WARNING")

        if (len(avg_of_estimated_y_1d) > SUBSTENTIAL_NUM_OF_PREDICTIONS) and (not self.is_test) and \
                ((numpy.all(avg_of_estimated_y_1d >= 0) or numpy.all(avg_of_estimated_y_1d <= 0))):
            self._log("All predictions were to either buy or sell or don't trade." \
                      " Value of first prediction: {}.".format(avg_of_estimated_y_1d[FIRST], ), "ERROR")

        if (len(avg_of_estimated_y_1d) >= SUBSTENTIAL_NUM_OF_SAMPLES) and \
                (self.complexbits >= SUBSTENTIAL_NUM_OF_COMPLEXBITS) and (not self.is_test):
            sell_purchase_ratio = float(numpy.count_nonzero(avg_of_estimated_y_1d > 0)) \
                                  / float(numpy.count_nonzero(avg_of_estimated_y_1d < 0))

            if sell_purchase_ratio < 0.1:
                self._log("On %s samples, Deepbit predicted too many sells. (ratio: %s)" % \
                          (len(avg_of_estimated_y_1d), sell_purchase_ratio), "WARNING")

            if sell_purchase_ratio > 5.0:
                self._log("On %s samples, Deepbit predicted too many purchases. (ratio: %s)" % \
                          (len(avg_of_estimated_y_1d), sell_purchase_ratio), "WARNING")