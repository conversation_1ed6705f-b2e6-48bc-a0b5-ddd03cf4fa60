from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features,add_seasonal_diffs
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Learning.algo_wrapper2 import run_model_config_slide_build
import os
from Algo.Utils.files_handle import HOME

import warnings
warnings.filterwarnings('ignore')

def create_smart_mean(a):
    correlations_csv = os.path.join(HOME,"Accuract Weights","rolling_cors_window=45_days=range(8, 15)")
    correlation_df = pd.read_csv(correlations_csv, parse_dates=['validation_day'])

    D = '4D'
    eps_days = '0-13' #'8-13'
    others_days = '0-16'
    representing_features = {'EPS':'diff_0Z_%s_last-Prev%s_EPS'%(eps_days,D),
                             'GEPS':'diff_0Z_%s_last-Prev%s_GEPS'%(others_days,D),
                             'GEMCO': 'diff_0Z_%s_last-Prev%s_GEMCO'%(others_days,D),
                             'GEFSL':'diff_0Z_%s_last-Prev%s_GEFSL'%(others_days,D),
                             'GFSCO': 'diff_0Z_%s_last-Prev%s_GFSCO'%(others_days,D),
                             'GEFS': 'diff_0Z_%s_last-Prev%s_GEFS'%(others_days,D),
                             'PARACO': 'diff_0Z_%s_last-Prev%s_PARACO'%(others_days,D),
                             'PARA': 'diff_0Z_%s_last-Prev%s_PARA'%(others_days,D)
                             }
    models = representing_features.keys()
    relevant_features = [representing_features[model] for model in models]
    additional_fts = [x.replace('8-13', '0-13').replace('8-16','0-16') for x in relevant_features]+ \
                     [x.replace('8-13', '0-8').replace('8-16', '0-8') for x in relevant_features]
    additional_fts = [x for x in additional_fts if x in list(a)]

    corr_fts = [x for x in list(correlation_df) if 'Corr' in x and sum([model in x for model in models])]
    weight_fts = [x.replace('Corr_20','weight') for x in corr_fts]
    correlation_df['sum'] = correlation_df[corr_fts].sum(axis=1).replace(to_replace=0,value=np.nan)
    for weight_ft, cor_ft in zip(weight_fts,corr_fts):
        correlation_df[weight_ft] = correlation_df[cor_ft] / correlation_df['sum']

    correlation_df['date'] = pd.to_datetime(correlation_df['validation_day'].dt.date)+td(hours=8)
    merged = a[['date','y_0800-1745','y_0800-1200','y_1145-1745']+relevant_features+additional_fts].merge(correlation_df[['date']+weight_fts])
    merged = merged.loc[:, ~merged.columns.duplicated()]

    for model in representing_features.keys():
        try:
            merged[representing_features[model]+'_weighted'] = merged[representing_features[model]] * merged['weight_%s'%model]
        except:
            aaa = 1
    merged['weighted_avg'] = merged[[x+'_weighted' for x in relevant_features]].mean(axis=1)

    merged['top2_avg'] = merged['diff_0Z_%s_last-Prev%s_EPS'%(eps_days,D)]+ 0.5*merged['diff_0Z_%s_last-Prev%s_GEFSL'%(others_days,D)]
    merged[merged['date'] >= dtdt(2020, 6, 1)].corr()['y_0800-1745']

    cols_for_reg = ['diff_0Z_0-13_last-Prev1D_EPS','diff_0Z_0-13_last-Prev2D_EPS',
                    'diff_0Z_8-16_last-Prev1D_GEFSL','diff_0Z_0-16_last-Prev4D_GEFSL',
                    #'diff_0Z_0-16_last-Prev4D_PARA','diff_0Z_8-16_last-Prev4D_PARACO',
                    #'diff_0Z_8-16_last-Prev1D_GEPS','diff_0Z_0-16_last-Prev4D_GEPS',
                    #'diff_0Z_0-16_last-Prev1D_GFSCO','diff_0Z_0-16_last-Prev2D_GFSCO',
                    #'diff_0Z_0-16_last-Prev4D_GFSCO',
                    #'diff_0Z_0-8_last-Prev1D_EC',
                    #'diff_0Z_0-16_last-Prev1D_GEMCO','diff_0Z_0-16_last-Prev4D_GEMCO'
                    ]
    a['y'] = a['y_0800-1745'] #+a['y_1745-1945']
    sub_df = a[['date','y']+cols_for_reg]
    sub_df = sub_df.rename(columns={'date':'time'})
    learn_window = 30
    slide_window = 7
    sub_df = sub_df.fillna(0)
    sub_df = sub_df[sub_df['time'].dt.weekday.isin([1,2,3,4])]
    ans = run_model_config_slide_build(sub_df,'y','Xtrees',dtdt(2020,1,1),learn_window,slide_window=slide_window,
                                       normalize_Xs=True,normalize_y_tags=True)
    ans[ans['time']>=dtdt(2020,4,1)].corr()
    aa = 1

a = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","XY_a_GDD_v8.csv"),parse_dates=['date'])
create_smart_mean(a)