from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features,add_seasonal_diffs
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Data_Processing.hisotical_values import main_historical_calc
from Algo.Learning.models_accuracy_calc import get_multimodel_cors_df,get_cors_df
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
import os
from Algo.Utils.files_handle import HOME



suffix = 'v8_12Z'
a = pd.read_csv(os.path.join(HOME,"XYs","Enriched_XYs","","XY_a_%s_%s.csv")%('GDD',suffix),parse_dates=['time','date'])

a = a[a['date'].dt.weekday.isin([0,1,2,3,4])]

weekdays_to_ignore_in_profit = [1,2,3]
WINDOW = 20

a.loc[a['date'].dt.weekday.isin(weekdays_to_ignore_in_profit),'y_1545-1745'] = np.nan

rolling = a[a['date']>=dtdt(2020,4,1)][['diff_12Z_0-16_last-Prev2_GEFS']+[x for x in list(a) if 'diff_' in x and 'GEM' in x and '12Z' in x and not 'GEMCO' in x and '0-8' in x]].rolling(WINDOW,5).corr().reset_index()
rolling_profit = a[a['date']>=dtdt(2020,4,1)][['y_1545-1745']+[x for x in list(a) if 'diff_' in x and 'GEM' in x and '12Z' in x and not 'GEMCO' in x and '0-8' in x]].rolling(WINDOW,5).corr().reset_index()
rolling = rolling[rolling['level_1']=='diff_12Z_0-16_last-Prev2_GEFS']
rolling_profit = rolling_profit[rolling_profit['level_1']=='y_1545-1745']

rolling['diff_12Z_0-8_last-Prev2_GEM'].plot()
rolling['diff_12Z_0-8_last-Prev2_GEM_Vs_1545-1745'] = rolling_profit['diff_12Z_0-8_last-Prev2_GEM']
rolling[['diff_12Z_0-8_last-Prev2_GEM','diff_12Z_0-8_last-Prev2_GEM_Vs_1545-1745']].plot()
plt.show()
rolling[['diff_12Z_0-8_last-Prev1_GEM','diff_12Z_0-8_last-Prev2_GEM',
         'diff_12Z_0-8_last-Prev3_GEM','diff_12Z_0-8_last-Prev4_GEM',
         'diff_12Z_0-2_last-Prev1_GEM', 'diff_12Z_0-2_last-Prev2_GEM',
         'diff_12Z_0-2_last-Prev3_GEM', 'diff_12Z_0-2_last-Prev4_GEM'
         ]]

for start in [dtdt(2020,10,1),dtdt(2020,11,1),dtdt(2020,12,1),
              dtdt(2021,1,1),dtdt(2021,2,1),dtdt(2020,3,1),dtdt(2020,4,1)]:
    tmp = a[(a['date']>=start)&(a['date']<=start+td(days=30,hours=12))]
    c = tmp[['diff_12Z_0-16_last-Prev2_GEFS']+[x for x in list(tmp) if 'diff_' in x and 'GEM' in x and not ('12Z' in x and sum([s in x for s in
                                                                                 ['CFS','EPS','EC','GEFS','PARACO']]))]].corr()['diff_12Z_0-16_last-Prev2_GEFS']
    top10 = c.sort_values().dropna().index.tolist()[-11:-1]
    print ('For start: %s ----> +30days, Top10 foe GEFS_12Z_last-Prev2 = \n %s'%(start,
                    c.sort_values().dropna()[-11:-1])) #top10))
    print (' ------------------------- ')
