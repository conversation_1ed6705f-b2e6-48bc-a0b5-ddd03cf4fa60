from datetime import datetime as dtdt
import numpy as np
import pandas as pd

from Algo.Learning.performance_analysis import wrap_analysis
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
from Algo.Learning.algo_constants import COST_PER_TRADE
from Algo.Conf.predictors_conf import COMBS_PREDICTORS_DICT

"""_______________________ Preprocessing of the df before predicting _________________________"""
"""
small preprocessing that was done in the air while predictin, prehaps should move to the backfill part Feb23
"""
def _impute_shifted_features(a, preds_for_comb):
    for pred in preds_for_comb:
        if pred[:-1] in list(a) and pred.endswith('-'):
            a[pred] = -a[pred[:-1]]
        elif pred[:-3] in list(a) and pred.endswith('+1d'):
            a[pred] = a[pred[:-3]].shift(-1)
        elif pred[:-3] in list(a) and pred.endswith('-1d'):
            a[pred] = a[pred[:-3]].shift(1)
        elif (pred[:-4] in list(a) and pred[-4] == '_') and pred.endswith('-1d'):
            a[pred] = a[pred[:-4]].shift(1)
        elif pred[:-4] in list(a) and pred.endswith('-') and pred[-4:-1] == '-1d':
            a[pred] = -a[pred[:-4]].shift(1)
        elif pred[:-5] in list(a) and pred.endswith('-') and pred[-4:-1] == '-1d':
            a[pred] = -a[pred[:-5]].shift(1)
    return a
def _preprocess_delta_ys(a):
    # More delta Ys
    a['y_0600-1200_no8to9'] = a['y_0600-0800'] + a['y_0900-1200']
    a['y_1200-1845_no13to14'] = a['y_1200-1745'] + a['y_1745-1845'] - a['y_1300-1400']

    a['y_1415-16845'] = a['y_1415-1645'] + a['y_1745-1845']
    a['y_1745-1800_hack'] = a['y_1745-1800'] + a['y_1900-1915']
    a['y_1300-1330_hack'] = a['y_1300-1330'] + a['y_1430-1530']
    a['y_1300-1330_hack2'] = a['y_1300-1330'] + a['y_1430-1530'] + a['y_1615-1645']
    a['y_12345-13400'] = a['y_1245-1300'] + a['y_1345-1400']
    a['y_12345-13415'] = a['y_1245-1315'] + a['y_1345-1415']
    a['y_0800-1645hack'] = a['y_0800-1200'] + a['y_1415-1645']
    a['y_0800-1200_no10hack'] = a['y_0800-1000'] + a['y_1100-1200']

    a['y_111245-121345'] = a['y_1145-1200'] + a['y_1245-1300']
    a['y_0745-2330FixedFri'] = a['y_0745-1745'] + a['y_1745-2330']
    a['y_0800-1945_skippedPM'] = a['y_0800-1415'] + a['y_1415-1545'] + a['y_1745-1945'] + a['y_1645-1745']

    a['y_1100-1315_friday'] = a['y_1100-1315']
    a.loc[a['date'].dt.weekday != 4, 'y_1100-1315_friday'] = np.nan
    a['y_1100-1315_friday'] = a['y_1100-1315_friday'].fillna(method='ffill')
    a['y_1100-1315_friday_-1d'] = a['y_1100-1315_friday'].shift(1)
    a['y_1200-1300_friday'] = a['y_1200-1300']
    a.loc[a['date'].dt.weekday != 4, 'y_1200-1300_friday'] = np.nan
    a['y_1200-1300_friday'] = a['y_1200-1300_friday'].fillna(method='ffill')
    a['y_1200-1300_friday_-1d'] = a['y_1200-1300_friday'].shift(1)
    a['y_1100-1400_monday'] = a['y_1100-1400']
    a.loc[a['date'].dt.weekday != 0, 'y_1100-1400_monday'] = np.nan
    a['y_1100-1400_monday'] = a['y_1100-1400_monday'].fillna(method='ffill')
    a['y_1100-1400_monday_-1d'] = a['y_1100-1400_monday'].shift(1)

    a['y_0600-1845_no0700to0730'] = a['y_0600-1845'] - a['y_0700-0730']
    a['y_1030-1100_EasternEIA'] = a['y_1430-1500']
    a['y_1015-1045_EasternEIA'] = a['y_1415-1445']
    a['y_0745-0800_Eastern'] = a['y_1145-1200']
    a['y_1515-1745_-1d'] = a['y_1515-1745'].shift(1).fillna(method='ffill')
    a['y_1515-1745_-2d'] = a['y_1515-1745'].shift(2).fillna(method='ffill')
    a['y_1515-1745_-1to3d'] = a['y_1515-1745'].shift(1).rolling(3, 3).mean().fillna(method='ffill')
    a['y_1515-1745_-1to5d'] = a['y_1515-1745'].shift(1).rolling(5, 5).mean().fillna(method='ffill')
    a['y_1445-1500'] = a['y_1445-1515'] - a['y_1500-1515']

    a['diff_cashVsettle-1d'] = a['diff_cashVsettle'].shift(1)
    winter_time_cond = lambda x: x.month in [12, 1, 2] or (x.month == 11 and x.day > 10) or (
                x.month == 3 and x.day < 10)
    a.loc[a.date.apply(winter_time_cond), 'y_0745-0800_Eastern'] = a.loc[a.date.apply(winter_time_cond), 'y_1245-1300']
    a.loc[a['date'].dt.weekday == 4, 'y_0745-2330FixedFri'] = a.loc[a['date'].dt.weekday == 4, 'y_0745-2030']
    a['GEFSL_PM_pred1_Prev14'] = (a['GEFSL_PM_pred1_Prev1'] / a['GEFSL_PM_pred1_Prev1'].std() +
                                  (a['GEFSL_PM_pred1_Prev4'] / a['GEFSL_PM_pred1_Prev4'].std())) / 2
    a['diff_0Z_8-16_last-Prev234_GEFSL'] = a[['diff_0Z_8-16_last-Prev%s_GEFSL' % i for i in [2, 3, 4]]].mean(axis=1)

    return a

"""_______________________ Prediction Wrapping tools _________________________"""

def get_results_df(target_strat,comb_conf,j=0,results_df = pd.DataFrame(),return_preds_for_comb=True,
                   print_info=True):

    preds_for_comb = comb_conf['preds_for_comb']
    results_df['profit_%s_combined_%s' % (target_strat, j)] = results_df[
        'profit_%s_%s' % (target_strat, preds_for_comb[0])]
    results_df['action'] = 'I'
    for i, pred in enumerate(preds_for_comb):
        if print_info:
            print(
            'combined profit tmp j=%s --- >>>  %s' % (results_df['profit_%s_combined_%s' % (target_strat, j)].sum(), j))
        profit_col = 'profit_%s_%s_%s' % (target_strat, pred, j) if pred == 'MEAN' and j < 0 else 'profit_%s_%s' % (
        target_strat, pred)
        is_trade_col = 'is_trade_%s_%s_%s' % (
        target_strat, pred, j) if pred == 'MEAN' and j < 0 else 'is_trade_%s_%s' % (target_strat, pred)
        older_trades = ['is_trade_%s_%s' % (target_strat, p) for p in preds_for_comb[:i]]
        if j < 0:
            older_trades = [c.replace('MEAN', 'MEAN_%s' % j) for c in older_trades]

        results_df['tmp'] = results_df[profit_col]
        results_df['tmp_action'] = 'I'
        results_df.loc[(results_df[is_trade_col] == 1) & (results_df[pred] > 0), 'tmp_action'] = 'B'
        results_df.loc[(results_df[is_trade_col] == 1) & (results_df[pred] < 0), 'tmp_action'] = 'S'
        existing_trades = results_df[older_trades].mean(axis=1) > 0
        results_df.loc[~existing_trades, 'action'] = results_df.loc[~existing_trades, 'tmp_action']
        if i == 0:
            continue
        if print_info:
            print('%s PPT : %s' % (pred, results_df['tmp'].mean()))
        results_df.loc[existing_trades, 'tmp'] = 0
        if print_info:
            print('%s marginal PPT : %s' % (pred, results_df['tmp'].mean()))
        results_df['profit_%s_combined_%s' % (target_strat, j)] += results_df['tmp']

    results_df = results_df.loc[:, ~results_df.columns.duplicated()].copy()
    if return_preds_for_comb:
        return results_df,preds_for_comb
    else:
        return results_df

def wrap_predictor_handling(a,predictor_name,target_strat,predictor_conf,comb_profits=True,j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=[0,1,2,3,4],results_df=None,
                            print_info=True,cost_per_trade=COST_PER_TRADE):
    last_action = 'I'
    if results_df is None:
        results_df = pd.DataFrame()
    chosen_ratio = predictor_conf['chosen_ratio']
    chosen_preds = predictor_conf['preds_for_comb']
    chosen_preds = list(dict.fromkeys(chosen_preds))
    start2 = predictor_conf['start']
    if comb_profits:
        chosen_preds = [x for x in predictor_conf['preds_for_comb']] + [x for x in chosen_preds if x not in predictor_conf['preds_for_comb']]
    mean_in_chosen = 'MEAN' in chosen_preds
    final_preds = chosen_preds+['MEAN'] * (comb_profits and not mean_in_chosen)

    if isinstance(chosen_ratio,float) or isinstance(chosen_ratio,int):
        chosen_ratios = [chosen_ratio] * (len(chosen_preds)+(1 if (comb_profits and not mean_in_chosen) else 0))
    elif isinstance(chosen_ratio,list):
        chosen_ratios = chosen_ratio + [chosen_ratio[-1]]*len(set(chosen_preds)-set(predictor_conf['preds_for_comb'])) + ([max(chosen_ratio)] if (comb_profits and not mean_in_chosen) else [])
    else:
        raise AssertionError('Invalid chosen_ratio: %s'%chosen_ratio)
    assert len(chosen_ratios) == len(final_preds)

    for i,pred in enumerate(final_preds): #predictor_conf['preds_for_comb']): #): # cfs_preds2):
        small_df = wrap_analysis(a,pred,target_strat,weekdays_for_thresh_calc,start2,default_end,write_results=False,plot=False,chosen_ratio=chosen_ratios[i])
        small_df['profit'][(~small_df['is_trade'])|(~small_df['date'].dt.weekday.isin(weekdays))] = 0
        small_df = small_df.rename(columns={'profit':'profit_%s_%s'%(target_strat,pred),
                                            'is_trade':'is_trade_%s_%s'%(target_strat,pred)})
        if results_df.shape[0] == 0:
            results_df = small_df
        else:
            if 'profit_%s_%s'%(target_strat,pred) in list(results_df):
                bb = 0
                #results_df = results_df[[x for x in list(results_df)  if x != 'profit_%s_%s'%(target_strat,pred)]]
            results_df = results_df.merge(small_df,on=['date'],how='outer',suffixes=("_old",''))
    if len(predictor_conf['preds_for_comb']):
        results_df,preds_for_comb = get_results_df(target_strat,predictor_conf,j,results_df=results_df,print_info=print_info)
        if cost_per_trade > 0:
            results_df = results_df.loc[:, ~results_df.columns.duplicated()]

            # handle only the current profit cols each time
            profit_cols = [x for x in list(results_df) if 'profit' in x and target_strat in x and 'Concated' not in x
                           and ('combined' not in x or 'combined_%s'%j in x)]
            concated_profit_cols = [x for x in list(results_df) if 'profit' in x and target_strat in x and ('ConcatedHack_' in x or 'ConcatedHack2_' in x)
                                     and (sum([s in x for s in predictor_conf['preds_for_comb']]) or 'combined_%s'%j in x)]
            if print_info:
                print('INFO: concated profit cols to reduce from: %s' % profit_cols)
            for col in profit_cols:
                results_df.loc[results_df[col] != 0, col] = results_df.loc[results_df[col] != 0, col] - cost_per_trade * 2
            if print_info:
                print('INFO: concated profit cols to reduce from: %s' % concated_profit_cols)
            for col in concated_profit_cols:
                try:
                    results_df.loc[results_df[col] != 0, col] = results_df.loc[results_df[col] != 0, col] - cost_per_trade * 2 * a[
                                                                    'num_of_trades_in_concated'].max()
                except:
                    bb = 0
                    raise
        actual_trades = (results_df['profit_%s_combined_%s' % (target_strat,j)] != 0) & (~results_df['profit_%s_combined_%s' % (target_strat,j)].isna())
        total_ppt = results_df[actual_trades]['profit_%s_combined_%s' % (target_strat,j)].mean()
        total_pnl = results_df[actual_trades]['profit_%s_combined_%s' % (target_strat,j)].sum()
        hr = (results_df[actual_trades]['profit_%s_combined_%s' % (target_strat,j)] > 0).mean()
        try:
            calmar, max_dd = calc_calmar(results_df[actual_trades]['profit_%s_combined_%s' % (target_strat,j)])
            sharpe = calc_sharpe(results_df[actual_trades]['profit_%s_combined_%s' % (target_strat,j)])
        except:
            calmar, sharpe, max_dd = np.nan, np.nan, np.nan
        try:
            hr = str(round(hr * 100)) + "%"
        except:
            hr = 'nan'

        if print_info:
            print('Combined Strategy %s | PPT = %s | HR = %s | PNL = %s | calmar %s | Sharpe = %s | MaxDD %s' % (predictor_name,
        total_ppt, hr, total_pnl, calmar, sharpe, max_dd))
        # print ('HACK !!! projecting on common sign predictions')
        results_df['MEAN_action'] = 'I'
        results_df.loc[(results_df['is_trade_%s_MEAN' % target_strat] == 1) & (results_df['MEAN'] > 0), 'MEAN_action'] = 'B'
        results_df.loc[(results_df['is_trade_%s_MEAN' % target_strat] == 1) & (results_df['MEAN'] < 0), 'MEAN_action'] = 'S'
        c = results_df[
            ['date', 'profit_%s_combined_%s' % (target_strat,j)] + ['profit_%s_%s' % (target_strat, pred) for pred in
                                                             preds_for_comb]]
        results_df = results_df.sort_values('date')
        last_action = results_df['action'].iloc[-1]
        last_mean = round(results_df['MEAN'].iloc[-1],1)
        if print_info:
            print ('==================')
            print('%s | Strategy: %s | Action = %s (%s)' % (results_df['date'].iloc[-1], predictor_name,last_action,last_mean))
            print ('==================')

    results_df = results_df.sort_values('date')
    return results_df, last_action

def add_probability(results_df,predictor='y_tag',prob_col='probability',time_col='time',drop_zeros=True,
                    hack_0_to_100=False):
    """
    :param hack_0_to_100: if True, We have a non symmetrical around 0 feature
    We just want the quantile
    :return:
    """
    if predictor not in list(results_df):
        print ('predictor %s didnt appear in Lat | returning NaN'%(predictor))
        results_df[prob_col] = np.nan
        return results_df
    results_df = results_df[[x for x in list(results_df) if x != 'level_0']]
    results_df = results_df.reset_index()
    results_df[prob_col] = np.nan
    r2 = results_df[[time_col, predictor, prob_col]]
    if drop_zeros:
        r2[predictor][r2[predictor] == 0] = np.nan
    r2 = r2.dropna(subset=[predictor])

    if not hack_0_to_100:
        r2[prob_col] = (r2[predictor].apply(lambda x: abs(r2[predictor]).sort_values().values.searchsorted(abs(x)) / float(r2.shape[0]) * 100.0) / 2) + 50
    else:
        r2[prob_col] = (r2[predictor].apply(lambda x: (r2[predictor]).sort_values().values.searchsorted(x) / float(r2.shape[0]) * 100.0))
    results_df = results_df[[x for x in list(results_df) if x != prob_col]].merge(r2,on=[time_col,predictor],how='outer')
    return results_df


def add_probability_v2(results_df,predictor='y_tag',prob_col='probability',time_col='time',drop_zeros=True,
                    hack_0_to_100=False):
    """
    :param hack_0_to_100: if True, We have a non symmetrical around 0 feature
    We just want the quantile
    :return:
    """
    if predictor not in list(results_df):
        print ('predictor %s didnt appear in Lat | returning NaN'%(predictor))
        results_df[prob_col] = np.nan
        return results_df

    results_df[prob_col] = (abs(results_df[predictor].replace(0,np.nan)).rank(pct=True)/2 +0.5)*100
    return results_df


def get_predictor_profit_from_conf(df,predictor_conf,predictor_name,target_strat,
                                   is_comb_profit=True,weekdays=[0,1,2,3,4],
                                   return_only_action=False):

    preds = [p for p in predictor_conf['preds_for_comb'] if p != 'MEAN']
    if len(preds) != len(set(list(df)).intersection(preds)):
        df = _impute_shifted_features(df,preds)

    df['MEAN'] = (df[preds]/df[preds].std()).mean(axis=1)
    results_df,last_action = wrap_predictor_handling(df,predictor_name,target_strat,predictor_conf,comb_profits=is_comb_profit,
                             j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=weekdays,results_df=None)
    if return_only_action:
        return last_action
    results_df = add_probability_v2(results_df, 'MEAN', 'prob_MEAN', 'date')
    return results_df,last_action

def backtest_predictor_conf(df,predictor_conf,predictor_name,target_strat,default_end=dtdt(2030,1,1),weekdays=[0,1,2,3,4],
                            print_info=True):

    preds = [p for p in predictor_conf['preds_for_comb'] if p != 'MEAN']
    if len(preds) != len(set(list(df)).intersection(preds)):
        df = _impute_shifted_features(df,preds)

    df['MEAN'] = df[preds].mean(axis=1)
    results_df,action = wrap_predictor_handling(df,predictor_name,target_strat,predictor_conf,comb_profits=True,j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=weekdays,results_df=None,
                                                print_info=print_info)
    try:
        profits_col = results_df[[x for x in list(results_df) if 'combined_0' in x][0]].fillna(0)
    except:
        print(f'Couldnt find combined_0 col among cols:{[x for x in list(results_df) if "profit" in x]}')
        raise
    calmar, max_dd = calc_calmar(profits_col,use_annual=False)
    calmar_last10, max_dd_last10 = calc_calmar(profits_col[-10*len(weekdays):],use_annual=False)
    calmar_last20, max_dd_last20 = calc_calmar(profits_col[-20*len(weekdays):],use_annual=False)
    try:
        calmar_mid20, max_dd_mid20 = calc_calmar(profits_col[20*len(weekdays):20*2*len(weekdays)],use_annual=False)
    except:
        calmar_mid20, max_dd_mid20 = np.nan, np.nan

    kpis = {'calmar':calmar,'max_dd':max_dd,
            'calmar_last10': calmar_last10,'max_dd_last10':max_dd_last10,
            'calmar_last20': calmar_last10,'max_dd_last20':max_dd_last20,
            'calmar_mid20': calmar_mid20,'max_dd_mid20':max_dd_mid20,
            'sharpe':calc_sharpe(profits_col),
            'profit':profits_col.sum(),}
    return results_df, kpis



def get_predictor_profit(df,predictor_name,target_strat,default_end=dtdt(2030,1,1)):
    predictor_conf = COMBS_PREDICTORS_DICT[predictor_name]
    #results_df = get_results_df(target_strat,comb_conf,j=0,results_df=df,return_preds_for_comb=False)
    preds = [p for p in predictor_conf['preds_for_comb'] if p != 'MEAN']
    df['MEAN'] = df[preds].mean(axis=1)
    results_df,action = wrap_predictor_handling(df,predictor_name,target_strat,predictor_conf,comb_profits=True,j=0,
                            default_end=dtdt(2050,1,1),
                            weekdays_for_thresh_calc=[0,1,2,3,4,5,6],
                            weekdays=[0,1,2,3,4],results_df=None)
    return results_df


def generate_conf(chosen_ratio,preds,start=dtdt(2021,1,1)):
    return {'start': start, 'chosen_ratio': chosen_ratio,
                 'preds_for_comb': preds,
                    'is_prod': True}


if __name__ == '__main__':
    pass