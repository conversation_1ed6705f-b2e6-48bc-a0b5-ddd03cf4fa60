from matplotlib import pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime as dtdt
import os
from Algo.Utils.files_handle import HOME

CHOSEN_RATIO = 0.6
SCAN_ALL_RATIOS = False
# SCAN_ALL_RATIOS = True

def _add_is_hit(df,predictor_col,target_col):
    df['is_hit_-11'] = np.sign(df[predictor_col] * df[target_col])
    df['is_hit'] = df['is_hit_-11'].apply(lambda x: x if x == 1 else 0)
    return df


def get_hr_df(df,predictor_col, target_col,time_col='time',plot=True,
              weekdays = [0,1,2,3,4],start=dtdt(2018,10,1),end=dtdt(2030,1,1),chosen_ratio=CHOSEN_RATIO,
                            upper_threshold=None):
    df = df.sort_values(time_col)
    df = df[df[time_col].dt.weekday.isin(weekdays)]
    df = df[(df[time_col]>=start)&(df[time_col]<=end)]
    cummulative_df = df[[time_col]]
    if SCAN_ALL_RATIOS:
        ratios = [1,0.9,0.8, 0.7,0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    else:
        ratios = [chosen_ratio]
    df_hr = pd.DataFrame({"ratio": ratios})
    col2_hrs = []
    ppts = []
    pnls = []

    try:
        df = _add_is_hit(df,predictor_col,target_col)
        df['profit'] = abs(df[target_col]) * df['is_hit_-11']
        try:
            df['abs_y'] = abs(df['y'])
        except:
            pass

    except Exception as e:
        print ('couldnt calculate performance due to missing y_col: \n%s'%e)
        df['profit'] = np.nan
        df['abs_y'] = np.nan
        df['is_hit'] = np.nan
        df['is_hit_-11'] = np.nan
        if target_col not in list(df):
            df[target_col] = np.nan
    for trades_ratio in ratios:
        ratio = 1 - trades_ratio
        quant = abs(df[predictor_col]).quantile(ratio)
        df['is_trade'] = (abs(df[predictor_col]) >= abs(df[predictor_col]).quantile(ratio))
        if upper_threshold is not None:
            df['is_trade'] = (df['is_trade'])&(abs(df[predictor_col]) <= abs(df[predictor_col]).quantile(1-upper_threshold))
        df['profit_adjusted'] = df['profit']
        df.loc[(~df['is_trade']),'profit_adjusted'] = 0
        a = df['profit_adjusted'].sum()
        cummulative_df['Cumsum_Ratio=%s'%trades_ratio] = df['profit_adjusted'].fillna(0).cumsum()
        df["drawdown"] = cummulative_df['Cumsum_Ratio=%s'%trades_ratio] - cummulative_df['Cumsum_Ratio=%s'%trades_ratio].cummax()
        maxdd = abs(df["drawdown"]).max()

        df2 = df[df['is_trade']]
        hr = df2['is_hit'].mean()
        ppt = df2['profit'].mean()

        total_profit = df2['profit'].sum()
        #print('%s Vs %s | %s' % (predictor_col, target_col, hr))
        col2_hrs.append(hr)
        ppts.append(ppt)
        pnls.append(total_profit)
        if trades_ratio == chosen_ratio:
            #a = df[(~df[target_col].isna())&(~df[predictor_col].isna())].groupby(df[time_col].dt.weekday).mean()['is_trade']
            """
            c = df2[df2['profit'] < 0].corr()['y'].sort_values().reset_index()
            d = c[c['index'].apply(lambda x: ('CFS' in x or 'EC' in x) and 'diff' in x and '0Z' in x)]
            df2[[time_col, 'y', `'diff_0Z_0-13_last-Prev1_EPS', 'diff_0Z_14-28_last-Prev2_CFS',`
                                    'diff_0Z_14-28_last-Prev1_CFS', 'diff_0Z_28-42_last-Prev2_CFS',
                                 df2[df2['profit'] < 0]   'diff_0Z_14-28_last-Prev1_EPS45', 'diff_0Z_28-42_last-Prev1_EPS45',
                                    'diff_0Z_14-28_last-Prev2_EPS45', 'diff_0Z_28-42_last-Prev2_EPS45',
                                    'diff_0Z_0-16_last-Prev1_GEFSL', 'diff_0Z_0-16_last-Prev2_GEFSL',
                                    'diff_0Z_0-8_last-Prev2_GFSCO', 'diff_0Z_0-16_last-Prev2_GFSCO']].corr()
            """
            if plot:
                df2.groupby(df2[time_col].dt.weekday).mean()['profit'].plot(kind='bar',title="PPT by Weekday | Trades Ratio: %s"%chosen_ratio)
                df2.set_index(time_col)[['profit']].plot(kind='bar',title="profits along time for Trades Ratio: %s"%chosen_ratio)
                df2.set_index(time_col)[['profit']].cumsum().plot(
                                                       title="Cummulative profits along time for Trades Ratio: %s" % chosen_ratio)
                df2.set_index(time_col)[['drawdown']].plot(kind='bar',title="max_drawdown")
                plt.tight_layout()
                plt.show()
                df2.groupby(df2[time_col].dt.to_period("M")).mean()[["profit"]].plot(kind='bar',title="PPT by month for TradesRatio = %s"%chosen_ratio)
                plt.tight_layout()
                plt.show()
                df2.groupby(df2[time_col].dt.to_period("M")).mean()[["is_hit"]].plot(kind='bar',
                                                                              title="HR by month for TradesRatio = %s" % chosen_ratio)
                plt.show()
            a= 1
            # df2['weekday'] = df2[time_col].dt.weekday
            # df2[df2[time_col].dt.month.isin([4])].set_index(time_col)[['profit']].plot(kind='bar',title="profits along time for Trades Ratio: %s"%chosen_ratio)
            # df2.groupby('weekday').mean()
            # df2.groupby(df2.time.dt.to_period("M")).mean()
            pass

    df['is_trade'] = (abs(df[predictor_col]) >= abs(df[predictor_col]).quantile(1-chosen_ratio))
    df_hr[target_col+"_HR"] = col2_hrs
    df_hr[target_col + "_PPT"] = ppts
    df_hr[target_col + "_PNL"] = pnls
    if plot:
        df_hr[['ratio',(target_col+"_HR")]].plot(kind='bar', x='ratio', title='HR between %s to %s x weekdays=all'%(predictor_col,target_col))
        df_hr[['ratio', (target_col + "_PPT")]].plot(kind='bar', x='ratio', title='PPT between %s to %s x weekdays=all'%(predictor_col,target_col))
        df_hr[['ratio', (target_col + "_PNL")]].plot(kind='bar', x='ratio',
                                                     title='PPT between %s to %s x weekdays=all' % (predictor_col, target_col))
        plt.show()
    return df, df_hr, cummulative_df


def wrap_analysis(a,pred_col,target_col,weekdays,start,end,write_results=False,plot=True,chosen_ratio=CHOSEN_RATIO,
                                                upper_threshold=None):
    big_df, hr_df, cummulative_df = get_hr_df(a, pred_col, target_col, time_col='date', weekdays=weekdays,
                                              start=start, end=end,chosen_ratio=chosen_ratio,plot=plot,upper_threshold=upper_threshold)  # 'y',weekdays=weekdays)
    small_df = big_df[['date',target_col,pred_col,'profit','is_hit','is_trade']]

    if write_results:
        d = os.path.join(HOME,"performance_analysis","Lats","","")
        f = 'NG_%s_pred=%s_%s-%s.csv' % (target_col.replace('_y', ''), pred_col, start.strftime('%Y%m%d'), end.strftime('%Y%m%d'))
        small_df.to_csv(d+f,index=False)
    if plot:
        cummulative_df.plot(x='date')
        plt.show()
    return small_df

