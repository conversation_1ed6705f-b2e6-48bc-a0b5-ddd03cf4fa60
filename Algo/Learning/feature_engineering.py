from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os

from Algo.Utils.files_handle import get_x_diffs_path
from Algo.Utils.files_handle import HOME
from Algo.Data_Processing import algo_preds_features_generator
from Algo.Data_Processing.seasonal_values import _extend_seasonal_values_with_previous_years
LOCAL_TZ = pytz.timezone("Asia/Jerusalem")
## how much to add to seasonal line
SEASONAL_SHIFT = {'HDD':-1,'CDD':1,'GDD':1}
MODELS_HOURS_OF_TOMORROW = [18,12] #12
FAKE_SUFFIXES = {'v8_0Zb':'v8_12Z'}
USE_LAST_3_MONTHS_X_DIFFS = False
SUFFIXES_SUPPORTED_FOR_3_MONTHS = ['v8_12Z','v14_WindUS','v15_WindTX']

COAL_PROCESSED_FEATURES_CSV = os.path.join(HOME,"General_Stocks","COAL_features_from_candles.csv")
WIND_FEATURES_CSV = os.path.join(HOME,"General_Stocks","Wind_features.csv")
from Algo.Data_Retrieval.ttf_retrieval import NGF_FEATURES_CSV,WTI_FEATURES_CSV
from Algo.Data_Processing.CFS_Monthly_features_generator import CFSM_FEATURES_CSV
import os
from Algo.Utils.files_handle import HOME,get_regression_predictions_enrichment_features_csv


def _normalize_series(series):
    series_nonz = series[series != 0]
    if series_nonz.shape[0] == 0:
        std = 1
    else:
        std = series_nonz.std()
    return series / std

def _drop_some_weekdays(time_series,pred_series,weekdays_to_drop=[]):
    pred_series2 = pred_series.copy()
    pred_series2.loc[(time_series.dt.weekday.isin(weekdays_to_drop))] = 0
    return pred_series2

def get_daily_feature_name_by_version(original_name,version='v7Flna',res=None):
    resolutions_dict = {'EPS': '0-13','PARA':'0-16','GEFS':'0-16','GFS':'0-16','PARACO':'0-16',
                        'GEPS':'0-16','GEMCO':'0-16','GEFSL':'0-16','GFSCO':'0-16',
                        'GEM':'0-10','EC':'0-8'
                        }
    if version == 'v7Flna':
        return original_name
    elif version in ['v8','v8_12Z','v8_0Zb','v9_wGDD','v11','v10_wMidwest']:
        sp = original_name.split('_')
        model = sp[-1]
        # orifinal : 'diff_0Z_last-D2_EPS'
        # new : 'diff_0Z_0-16_last-Prev2D_EPS'
        prev_days = sp[-2].split('-')[1].split('D')[-1] # (D)1 / (D)2 / (D)3
        if res is None:
            res = resolutions_dict[model]
        new = '%s_%s_%s_last-Prev%sD_%s'%(sp[0],sp[1],res,prev_days,model)
        return new
    else:
        raise AssertionError('Unsupported version of data: %s'%version)

def add_eps_gaps(a):
    for h in ['0Z','12Z']:
        try:
            a['Value_%s_0-16_GEFS-EPS_rs'%h] = a['Value_%s_0-16_GEFS_rs'%h] - a['Value_%s_0-13_EPS_rs'%h]
            a['Value_%s_0-16_GEPS-EPS_rs'%h] = a['Value_%s_0-16_GEPS_rs'%h] - a['Value_%s_0-13_EPS_rs'%h]
            a['Value_%s_0-16_GEFS-EPS_rs_diff1'%h] = a['Value_%s_0-16_GEFS-EPS_rs'%h].diff()
            a['Value_%s_0-16_GEPS-EPS_rs_diff1'%h] = a['Value_%s_0-16_GEPS-EPS_rs'%h].diff()
            a['Value_%s_0-16_CFS-EPS_rs'%h] = a['Value_%s_0-16_CFS_rs'%h] - a['Value_%s_0-13_EPS_rs'%h]
            a['Value_%s_0-16_CFS-EPS_rs_diff1'%h] = a['Value_%s_0-16_CFS-EPS_rs'%h].diff()
            if h == '0Z':
                a['Value_0Z_0-16_GEFSL-EPS_rs'] = a['Value_0Z_0-16_GEFSL_rs'] - a['Value_0Z_0-13_EPS_rs']
                a['Value_0Z_0-16_GEFSL-EPS_rs_diff1'] = a['Value_0Z_0-16_GEFSL-EPS_rs'].diff()
        except Exception as e:
            print('Failed partially on EPS gaps: %s' % e)
            pass
    try:
        a['Value_0Z_0-16_GEFS-EPS(-1)_rs'] = a['Value_0Z_0-16_GEFS_rs'] - a['Value_12Z_0-13_EPS_rs']
        a['Value_0Z_0-16_GEPS-EPS(-1)_rs'] = a['Value_0Z_0-16_GEPS_rs'] - a['Value_12Z_0-13_EPS_rs']
        a['Value_0Z_0-16_GEFS-EPS(-1)_rs_diff1'] = a['Value_0Z_0-16_GEFS-EPS(-1)_rs'].diff()
        a['Value_0Z_0-16_GEPS-EPS(-1)_rs_diff1'] = a['Value_0Z_0-16_GEPS-EPS(-1)_rs'].diff()

        a['Value_12Z_0-16_GEFS-EPS(-1)_rs'] = a['Value_12Z_0-16_GEFS_rs'] - a['Value_0Z_0-13_EPS_rs']
        a['Value_12Z_0-16_GEPS-EPS(-1)_rs'] = a['Value_12Z_0-16_GEPS_rs'] - a['Value_0Z_0-13_EPS_rs']
        a['Value_12Z_0-16_GEFS-EPS(-1)_rs_diff1'] = a['Value_12Z_0-16_GEFS-EPS(-1)_rs'].diff()
        a['Value_12Z_0-16_GEPS-EPS(-1)_rs_diff1'] = a['Value_12Z_0-16_GEPS-EPS(-1)_rs'].diff()
    except Exception as e:
        print ('Failed partially on EPS gaps: %s'%e)
        pass
    try:
        a['Value_6Z_0-16_CFS-EPS(-1)_rs'] = a['Value_6Z_0-16_CFS_rs'] - a['Value_0Z_0-13_EPS_rs']
        a['Value_6Z_0-16_CFS-EPS(-1)_rs_diff1'] = a['Value_6Z_0-16_CFS-EPS(-1)_rs'].diff()
    except:
        print ('Failed to add CFS-EPS gaps for 6Z')
    return a

def coal_enrichment(a):
    final_df3 = pd.read_csv(COAL_PROCESSED_FEATURES_CSV,parse_dates=['date'])
    final_df3[['NCF_open-open-1_-1d', 'NCF_open-close-1_-1d', 'NCF_open-open-2_-1d']] = final_df3[['NCF_open-open-1', 'NCF_open-close-1', 'NCF_open-open-2']].shift(1)
    a = a.merge(final_df3,on=['date'],how='outer')
    return a

def ngf_enrichment(a):
    final_df3 = pd.read_csv(NGF_FEATURES_CSV,parse_dates=['date'])
    final_df3 = final_df3[[c for c in list(final_df3) if c != 'contract']]
    final_df3[['NGF_open-open-1_-1d', 'NGF_open-close-1_-1d', 'NGF_open-open-2_-1d']] = final_df3[['NGF_open-open-1', 'NGF_open-close-1', 'NGF_open-open-2']].shift(1)
    final_df3 = final_df3.merge(a[['date']])
    a = a.merge(final_df3,on=['date'],how='outer')
    return a

def wti_enrichment(a):
    final_df3 = pd.read_csv(WTI_FEATURES_CSV,parse_dates=['date'])
    final_df3 = final_df3[[c for c in list(final_df3) if c != 'contract']]
    final_df3[['WTI_open-open-1_-1d', 'WTI_open-close-1_-1d', 'WTI_open-open-2_-1d']] = final_df3[['WTI_open-open-1', 'WTI_open-close-1', 'WTI_open-open-2']].shift(1)
    final_df3 = final_df3.merge(a[['date']])
    a = a.merge(final_df3,on=['date'],how='outer')
    return a

def wind_enrichment(a):
    wind_features_df = pd.read_csv(WIND_FEATURES_CSV,parse_dates=['date'])
    a_cols = list(a)
    # wind_features_df = wind_features_df[[x for x in list(wind_features_df) if x in a_cols]]
    wind_features_df = wind_features_df.merge(a[['date']])
    a = a.merge(wind_features_df,on=['date'],how='outer')
    return a

def algo_preds_enrichment(a,suffix='_start=2021_100D',
                          ):
    targets = ['y_0800-1845', 'y_1200-1400'][:1]
    targets2 = ['y_1200-1745']
    train_days_groups = [#('weekly',[0, 1, 2, 3, 4]),
                         ('d123',[1, 2, 3]),
                        ('d04',[0, 4])
                        ]
    model_groups = ['gefs', 'para', 'paraco', 'para8to16', 'gefs0z', 'para0z', 'paraco0z', 'american0z',
                        'gefs_strict', 'para_strict', 'paraco_strict', 'american_strict',
                          'ec', 'eps', 'euro_strict', 'full0z', 'canadian0z', 'canadian12z', 'gemco', 'gem',
                            'ec_shorterm', 'eps_shorterm', 'canadian_shorterm', 'para_shorterm', 'paraco_shorterm', 'gefs_shorterm',
                                'ec_daily', 'eps_daily', 'euro_daily', 'euro12z_daily', 'gem_daily', 'gemco_daily', 'canadian_daily', 'canadian12z_daily',
                                    'para_daily', 'paraco_daily', 'gefs_daily', 'american_daily', 'american12z_daily', 'euro', 'canadian', 'american',

                    ]
    model_groups2 = ['gefs6z','para6z','paraco6z','para6z_midterm','paraco6z_midterm']

    for name,train_days in train_days_groups:
        desired_suffix = '_4_150' if name == 'd04' else '_4_100'
        cond_on_cols = lambda x: x.endswith(desired_suffix) and \
                                 '_'.join(x.split('_')[1:3]) in targets and \
                                 '_'.join(x.split('_')[3:]).split(desired_suffix)[0] in model_groups
        cond_on_cols2 = lambda x: x.endswith(desired_suffix) and \
                                  '_'.join(x.split('_')[1:3]) in targets2 and \
                                  '_'.join(x.split('_')[3:]).split(desired_suffix)[0] in model_groups2
        cond_on_cols3 = lambda x: True
        csv = get_regression_predictions_enrichment_features_csv(train_days, suffix)
        if name == 'd04':
            cond_on_cols3 = lambda x: sum([s in x for s in algo_preds_features_generator.d04_allowed_cols]) > 0
            csv = get_regression_predictions_enrichment_features_csv(train_days, suffix.replace('100D','150D'))
        elif name == 'd123':
            cond_on_cols3 = lambda x: cond_on_cols(x) or cond_on_cols2(x)
        algo_preds_features_df = pd.read_csv(csv,parse_dates=['date'])
        algo_preds_features_df = algo_preds_features_df[[x for x in list(algo_preds_features_df)
                                                         if cond_on_cols3(x) or x == 'date']]
        algo_preds_features_df = algo_preds_features_df.merge(a[['date']])
        algo_preds_features_df.columns = ['date'] + [x+f'_{name}' for x in list(algo_preds_features_df)
                                                     if x != 'date']
        a = a.merge(algo_preds_features_df,on=['date'],how='outer')
    return a

def enrich_with_teleconnection_xs(a):
    from Algo.Utils.files_handle import TELECONNECTIONS_XS_CSV
    teleconnections_df = pd.read_csv(TELECONNECTIONS_XS_CSV,parse_dates=['date'])
    chosen_cols = [x for x in list(teleconnections_df) if 'diff' in x]
    a = a.merge(teleconnections_df[['date']+chosen_cols],on=['date'],how='outer')
    return a

def gdd_vs_30d_enrichment(a):
    a['diff_0Z_0-0_0d-ma5D_PARA'] = a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(5, 3).mean()
    a['diff_0Z_0-0_0d-ma10D_PARA'] = a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(10, 4).mean()
    a['diff_0Z_0-0_0d-ma20D_PARA'] = a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(20, 4).mean()
    a['diff_0Z_0-0_0d-ma30D_PARA'] = a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(30, 4).mean()
    a['diff_0Z_0-0_5d-ma5D_PARA'] = a['Value_0Z_0-0_PARA'].rolling(5, 1).mean() - a['Value_0Z_0-0_PARA'].rolling(5,3).mean()
    a['diff_0Z_0-0_5d-ma10D_PARA'] = a['Value_0Z_0-0_PARA'].rolling(5, 1).mean() - a['Value_0Z_0-0_PARA'].rolling(10,4).mean()
    a['diff_0Z_0-0_5d-ma20D_PARA'] = a['Value_0Z_0-0_PARA'].rolling(5, 1).mean() - a['Value_0Z_0-0_PARA'].rolling(20,4).mean()
    a['diff_0Z_0-0_5d-ma30D_PARA'] = a['Value_0Z_0-0_PARA'].rolling(5, 1).mean() - a['Value_0Z_0-0_PARA'].rolling(30,4).mean()

    return a

def add_0D_trend(a):
    for i in [1, 2, 3, 5, 7]:
        a['diff_0Z_0-0_last-%sD' % i] = a['Value_0Z_0-0_EC'].diff(i)
    return a

def wind_vs_30d_enrichment(a):
    # wind features
    a['diff_0Z_0-0_0d-ma5D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,3).mean()
    a['diff_0Z_0-0_0d-ma10D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(10,4).mean()
    a['diff_0Z_0-0_0d-ma20D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(20,4).mean()
    a['diff_0Z_0-0_0d-ma30D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(30,4).mean()
    a['diff_0Z_0-0_5d-ma5D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,1).mean() - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,3).mean()
    a['diff_0Z_0-0_5d-ma10D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,1).mean() - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(10,4).mean()
    a['diff_0Z_0-0_5d-ma20D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,1).mean() - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(20,4).mean()
    a['diff_0Z_0-0_5d-ma30D_PARA_ws10mTX'] = a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,1).mean() - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(30,4).mean()

    a['diff_0Z_0-8_0d-ma5D_PARA_ws10mTX'] = a['Value_0Z_0-8_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,3).mean()
    a['diff_0Z_0-8_0d-ma10D_PARA_ws10mTX'] = a['Value_0Z_0-8_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(10,4).mean()
    a['diff_0Z_0-8_0d-ma20D_PARA_ws10mTX'] = a['Value_0Z_0-8_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(20,4).mean()
    a['diff_0Z_0-8_0d-ma30D_PARA_ws10mTX'] = a['Value_0Z_0-8_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(30,4).mean()
    a['diff_0Z_0-16_0d-ma5D_PARA_ws10mTX'] = a['Value_0Z_0-16_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(5,3).mean()
    a['diff_0Z_0-16_0d-ma10D_PARA_ws10mTX'] = a['Value_0Z_0-16_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(10,4).mean()
    a['diff_0Z_0-16_0d-ma20D_PARA_ws10mTX'] = a['Value_0Z_0-16_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(20,4).mean()
    a['diff_0Z_0-16_0d-ma30D_PARA_ws10mTX'] = a['Value_0Z_0-16_PARA_ws10mTX'] - a['Value_0Z_0-0_PARA_ws10mTX'].rolling(30,4).mean()
    return a

def clusters_enrichment(a,max_hours=48):
    cluster_diffs_relevant_features = ['diff_0Z_avg_d11to15_(0-12)h', 'diff_6Z_avg_d11to15_(0-12)h', 'diff_12Z_avg_d11to15_(0-12)h', 'diff_18Z_avg_d11to15_(0-12)h',
                                       'diff_0Z_avg_d11to15_(0-18)h', 'diff_6Z_avg_d11to15_(0-18)h', 'diff_12Z_avg_d11to15_(0-18)h', 'diff_18Z_avg_d11to15_(0-18)h',
                                       'diff_0Z_avg_d11to15_(0-24)h', 'diff_6Z_avg_d11to15_(0-24)h', 'diff_12Z_avg_d11to15_(0-24)h', 'diff_18Z_avg_d11to15_(0-24)h',
                                       'diff_0Z_avg_d11to15_(0-36)h', 'diff_6Z_avg_d11to15_(0-36)h', 'diff_12Z_avg_d11to15_(0-36)h', 'diff_18Z_avg_d11to15_(0-36)h',
                                       'diff_0Z_avg_d11to15_(0-48)h', 'diff_6Z_avg_d11to15_(0-48)h', 'diff_12Z_avg_d11to15_(0-48)h', 'diff_18Z_avg_d11to15_(0-48)h',
                                       'diff_0Z_avg_d11to15_(0-6)h', 'diff_6Z_avg_d11to15_(0-6)h', 'diff_12Z_avg_d11to15_(0-6)h', 'diff_18Z_avg_d11to15_(0-6)h',
                                       'diff_0Z_best_d0to15_(0-6)h', 'diff_12Z_avg_d0to15_(0-6)h',

                                       'diff_0Z_best_d0to15_(0-6)h','diff_0Z_best_d0to15_(0-12)h','diff_0Z_best_d0to15_(0-18)h','diff_0Z_best_d0to15_(0-24)h',
                                       'diff_0Z_best_d7to15_(0-6)h','diff_0Z_best_d7to15_(0-12)h','diff_0Z_best_d7to15_(0-18)h','diff_0Z_best_d7to15_(0-24)h',
                                       'diff_0Z_best_d11to15_(0-12)h','diff_0Z_best_d11to15_(0-24)h',
                                       'diff_6Z_best_d0to15_(0-6)h','diff_6Z_best_d0to15_(0-12)h','diff_6Z_best_d0to15_(0-18)h','diff_6Z_best_d0to15_(0-24)h',
                                       'diff_6Z_best_d7to15_(0-6)h','diff_6Z_best_d7to15_(0-12)h','diff_6Z_best_d7to15_(0-18)h','diff_6Z_best_d7to15_(0-24)h',
                                       'diff_6Z_best_d11to15_(0-12)h', 'diff_6Z_best_d11to15_(0-24)h',
                                       'Value_12Z_best_Vs_second_best_d0to15']
    cluster_diffs = pd.read_csv(
        os.path.join(HOME,"degdays_archive","Clusters","Cluster_models_Diffs_MaxHours=%s_Ratio=4.0.csv")%max_hours,
        parse_dates=['date'])[['date'] + cluster_diffs_relevant_features]
    cluster_diffs = cluster_diffs.rename(columns={x: x+"_cluster48" for x in [c for c in list(cluster_diffs) if c != 'date']})
    cluster_diffs['date'] += td(hours=2)
    a = a.merge(cluster_diffs[cluster_diffs['date']>=a['date'].min()], on=['date'], how='outer')
    return a

def rsi_enrichment(a):
    relative_stength_fts = pd.read_csv(
        os.path.join(HOME,"General_Stocks","Relative_Strength_by_profit.csv"), parse_dates=['date'])
    # relative_stength_fts = relative_stength_fts.set_index('date')
    #relative_stength_fts -= relative_stength_fts.rolling(90,120).mean().fillna(relative_stength_fts.mean())
    # relative_stength_fts = relative_stength_fts.reset_index()
    a = a.merge(relative_stength_fts, on=['date'], how='outer')
    return a

def past_positions_enrichment(a):
    past_positions = pd.read_csv(
        os.path.join(HOME,"General_Stocks","past_positions_features.csv"), parse_dates=['date'])
    past_positions.query('date>=@a.date.min()', inplace=True)
    common = list(set(a.columns).intersection(set(past_positions.columns)))
    assert common == ['date']

    a = a.merge(past_positions, on=['date'], how='outer')
    return a
def cfsm_enrichment(a):
    """
    currently keeping only GDD Diffs from the fts DataFrame
    :param a:
    :return:
    """
    cfsm_fts_df = pd.read_csv(CFSM_FEATURES_CSV,parse_dates=['date'])
    cfsm_fts_df = cfsm_fts_df.merge(a[['date']])
    cfsm_fts_df = cfsm_fts_df[['date']+[x for x in list(cfsm_fts_df) if 'Diff' in x and 'GDD' in x]]
    a = a.merge(cfsm_fts_df, on=['date'], how='outer')
    return a

def ttf_enrichment(a,asset='TTF'):
    try:
        ttf = pd.read_csv(os.path.join(HOME,"General_Stocks","%s_Prices.csv")%asset,parse_dates=['date'])
    except:
        ttf = pd.read_csv(os.path.join(HOME,"General_Stocks","%s_Prices.csv")%asset,parse_dates=['index']).rename(columns={'index':'date'})
    ttf['date'] += td(hours=8)

    max_date = ttf.dropna(subset=['open'])['date'].max()
    if asset == 'MTF':
        ttf = ttf.merge(a[['date']],on=['date'],how='outer').sort_values('date')
        ttf_filled = ttf.copy()
        ttf_filled[['close','open','high','low','volume']] = ttf_filled[['close','open','high','low','volume']].fillna(method='ffill')
        ttf.loc[ttf['date']<=max_date] = ttf_filled.loc[ttf['date']<=max_date]
    else:
        ttf = ttf.merge(a[['date']], on=['date'], how='outer').sort_values('date')

    ttf['close-open'] = ttf['close'] - ttf['open']
    ttf['close-open_d-1'] = (ttf['close'] - ttf['open']).shift(1)
    ttf['close-open_d+1'] = (ttf['close'] - ttf['open']).shift(-1)

    ttf['open-close-1'] = ttf['open'] - ttf['close'].shift(1)
    ttf['close-Prev1'] = ttf['close'].diff()
    ttf['open-Prev1'] = ttf['open'].diff()
    ttf['open-Prev2'] = ttf['open'].diff(2)
    ttf['open-Prev1_d-1'] = ttf['open-Prev1'].shift(1)
    ttf['open-Prev1_d+1'] = ttf['open-Prev1'].shift(-1)
    ttf['open-Prev1to7'] = ttf['open'] - ttf['open'].rolling(7,7).mean()
    ttf['close-1-Prev1to7'] = ttf['close'].shift(1) - ttf['close'].shift(1).rolling(7,7).mean()
    ttf['open-Prev1to3'] = ttf['open'] - ttf['open'].rolling(3,3).mean()
    ttf['close-1-Prev1to3'] = ttf['close'].shift(1) - ttf['close'].shift(1).rolling(3,3).mean()
    ttf = ttf.rename(columns={x:'%s_%s'%(asset,x) for x in list(ttf) if x != 'date'})
    # fillnans + merge with a
    ttf[[x for x in list(ttf) if x != 'date']] = ttf[[x for x in list(ttf) if x != 'date']].fillna(method='ffill')
    common = [x for x in list(a) if x in list(ttf)]
    a = a.merge(ttf,on=common).sort_values('date')
    return a

def add_rolling_diffs(a):
    ## rolling diffs
    # if {'diff_6Z_8-16_last-Prev1234_PARA','diff_6Z_8-16_last-Prev1234_PARACO','diff_11Z_14-35_last-Prev1D_CFS',
    #     'diff_0Z_28-42_last-Prev1234_CFS'} <= set(list(a)):
    #     return a

    a['diff_6Z_8-16_last-Prev1234_PARA'] = a[['diff_6Z_8-16_last-Prev%s_PARA' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_6Z_8-16_last-Prev1234_PARACO'] = a[['diff_6Z_8-16_last-Prev%s_PARACO' % i for i in range(1, 5)]].mean(
        axis=1)
    a['diff_6Z_0-16_last-Prev1234_PARA'] = a[['diff_6Z_0-16_last-Prev%s_PARA' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_0Z_0-16_last-Prev1234_PARA'] = a[['diff_0Z_0-16_last-Prev%s_PARA' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_0Z_0-16_last-Prev14_PARA'] = a[['diff_0Z_0-16_last-Prev%s_PARA' % i for i in [1,4]]].mean(axis=1)
    a['diff_6Z_0-16_last-Prev1234_PARACO'] = a[['diff_6Z_0-16_last-Prev%s_PARACO' % i for i in range(1, 5)]].mean(
        axis=1)
    a['diff_6Z_0-16_last-Prev12_PARACO'] = a[['diff_6Z_0-16_last-Prev%s_PARACO' % i for i in range(1, 3)]].mean(axis=1)
    a['diff_0Z_0-16_last-Prev1234_PARACO'] = a[['diff_0Z_0-16_last-Prev%s_PARACO' % i for i in range(1, 5)]].mean(
        axis=1)
    a['diff_6Z_0-16_last-Prev1234_GFSv16'] = a[['diff_6Z_0-16_last-Prev%s_GFSv16' % i for i in range(1, 5)]].mean(
        axis=1)
    a['diff_0Z_0-16_last-Prev1234_GFSv16'] = a[['diff_0Z_0-16_last-Prev%s_GFSv16' % i for i in range(1, 5)]].mean(
        axis=1)

    a['diff_6Z_0-8_last-Prev1234_GEFS'] = a[['diff_6Z_0-8_last-Prev%s_GEFS' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_6Z_0-16_last-Prev1234_GEFS'] = a[['diff_6Z_0-16_last-Prev%s_GEFS' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_6Z_0-16_last-Prev1234_PARARACO16'] = a[
        ['diff_6Z_0-16_last-Prev1234_%s' % model for model in ['PARA', 'PARACO', 'GFSv16']]].mean(axis=1)
    a['diff_6Z_0-16_last-Prev1_PARARACO16'] = a[
        ['diff_6Z_0-16_last-Prev1_%s' % model for model in ['PARA', 'PARACO', 'GFSv16']]].mean(axis=1)
    a['diff_12Z_0-8_last-Prev1234_GEFS'] = a[['diff_12Z_0-8_last-Prev%s_GEFS' % i for i in range(1, 5)]].mean(axis=1)
    a['diff_0Z_0-15_last-Prev14_GEPS'] = (a['diff_0Z_0-15_last-Prev1_GEPS']+a['diff_0Z_0-15_last-Prev4_GEPS'])/2
    a['Value_0Z_0-16_Ens-EPS_rs'] = (
                a[['Value_0Z_0-16_GEPS-EPS_rs', 'Value_0Z_0-16_GEFSL-EPS_rs', 'Value_0Z_0-16_GEFS-EPS_rs']] / a[
            ['Value_0Z_0-16_GEPS-EPS_rs', 'Value_0Z_0-16_GEFSL-EPS_rs', 'Value_0Z_0-16_GEFS-EPS_rs']].std()).mean(
        axis=1)
    a['Value_12Z_0-16_Ens-EPS_rs'] = (
                a[['Value_12Z_0-16_GEPS-EPS_rs', 'Value_0Z_0-16_GEFSL-EPS_rs', 'Value_12Z_0-16_GEFS-EPS_rs']] / a[
            ['Value_12Z_0-16_GEPS-EPS_rs', 'Value_0Z_0-16_GEFSL-EPS_rs', 'Value_12Z_0-16_GEFS-EPS_rs']].std()).mean(
        axis=1)
    a['Value_0Z_0-16_Ens-EPS_rs_diff1'] = a['Value_0Z_0-16_Ens-EPS_rs'].diff()
    a['Value_12Z_0-16_Ens-EPS_rs_diff1'] = a['Value_12Z_0-16_Ens-EPS_rs'].diff()
    try:
        a['diff_18Z_0-16_last-Prev1234_PARA'] = a[['diff_18Z_0-16_last-Prev%s_PARA' % i for i in range(1, 5)]].mean(
            axis=1)
        a['diff_18Z_0-16_last-Prev1234_GEFS'] = a[['diff_18Z_0-16_last-Prev%s_GEFS' % i for i in range(1, 5)]].mean(
            axis=1)
        a['diff_12Z_0-16_last-Prev1234_PARA'] = a[['diff_12Z_0-16_last-Prev%s_PARA' % i for i in range(1, 5)]].mean(
            axis=1)
    except:
        a['diff_18Z_0-16_last-Prev1234_PARA'] = np.nan
        a['diff_18Z_0-16_last-Prev1234_GEFS'] = np.nan
        a['diff_12Z_0-16_last-Prev1234_PARA'] = np.nan
    a['diff_0Z_0-16_last-rolling2_PARA'] = a['diff_0Z_0-16_last-Prev2_PARA'] + a['diff_18Z_0-16_last-Prev2_PARA']
    a['diff_6Z_0-16_last-rolling2_PARA'] = a['diff_6Z_0-16_last-Prev2_PARA'] + a['diff_0Z_0-16_last-Prev2_PARA']
    a['diff_0Z_0-16_last-rolling2_PARACO'] = a['diff_0Z_0-16_last-Prev2_PARACO'] + a['diff_18Z_0-16_last-Prev2_PARACO']
    a['diff_6Z_0-16_last-rolling2_PARACO'] = a['diff_6Z_0-16_last-Prev2_PARACO'] + a['diff_0Z_0-16_last-Prev2_PARACO']
    a['diff_6Z_0-16_last-rolling2_GFSv16'] = a['diff_6Z_0-16_last-Prev2_GFSv16'] + a['diff_0Z_0-16_last-Prev2_GFSv16']
    a['diff_0Z_0-16_last-rolling2_GFSv16'] = a['diff_0Z_0-16_last-Prev2_GFSv16'] + a['diff_18Z_0-16_last-Prev2_GFSv16']
    a['diff_12Z_0-16_last-rolling2_PARA'] = a['diff_12Z_0-16_last-Prev2_PARA'] + a['diff_6Z_0-16_last-Prev2_PARA']
    a['diff_12Z_0-16_last-rolling2_PARACO'] = a['diff_12Z_0-16_last-Prev2_PARACO'] + a['diff_6Z_0-16_last-Prev2_PARACO']
    a['diff_12Z_0-16_last-rolling2_GFSv16'] = a['diff_12Z_0-16_last-Prev2_GFSv16'] + a['diff_6Z_0-16_last-Prev2_GFSv16']

    # a['diff_0Z_0-13_last-rolling2_GEFSL'] = a[['diff_0Z_0-13_last-Prev2_GEFSL', 'diff_0Z_0-13_Prev1-Prev3_GEFSL']].mean(
    #     axis=1)

    a['diff_0Z_14-21_last-Prev12_CFS'] = a['diff_0Z_14-21_last-Prev1_CFS'] + a['diff_0Z_14-21_last-Prev2_CFS']
    a['diff_0Z_0-14_last-Prev1234_EPS'] = a[['diff_0Z_0-14_last-Prev1_EPS', 'diff_0Z_0-14_last-Prev2_EPS',
                                             'diff_0Z_0-14_last-Prev3_EPS', 'diff_0Z_0-14_last-Prev4_EPS']].mean(axis=1)
    a['diff_0Z_0-16_last-Prev123_GEPS'] = a[['diff_0Z_0-16_last-Prev1_GEPS', 'diff_0Z_0-16_last-Prev2_GEPS',
                                             'diff_0Z_0-16_last-Prev3_GEPS']].mean(axis=1)
    a['diff_0Z_0-16_last-Prev1234_GEPS'] = a[['diff_0Z_0-16_last-Prev1_GEPS', 'diff_0Z_0-16_last-Prev2_GEPS',
                                              'diff_0Z_0-16_last-Prev3_GEPS', 'diff_0Z_0-16_last-Prev4_GEPS']].mean(
        axis=1)

    a['diff_0Z_14-28_last-Prev112_EPS45'] = a['diff_0Z_14-28_last-Prev2_EPS45'] + a['diff_0Z_14-28_Prev1-Prev2_EPS45']
    # a['diff_0Z_0-16_last-Prev124_GFSCO'] = a['diff_0Z_0-16_last-Prev1_GFSCO'] + a['diff_0Z_0-16_last-Prev2_GFSCO'] + a[
    #     'diff_0Z_0-16_last-Prev4_GFSCO']
    # a['diff_0Z_0-16_last-Prev124_GEFSL'] = a['diff_0Z_0-16_last-Prev1_GEFSL'] + a['diff_0Z_0-16_last-Prev2_GEFSL'] + a[
    #     'diff_0Z_0-16_last-Prev4_GEFSL']
    a['diff_5Z_14-28_last-Prev1D_CFS'] = a['diff_6Z_14-28_last-Prev1D_CFS'] + \
                                         1 / 12 * (a['Value_6Z_14-28_Prev1_CFS'] + a['Value_6Z_14-28_Prev2_CFS'] + a['Value_6Z_14-28_Prev3_CFS']) - \
                                         0.25 * a['Value_6Z_14-28_Prev1_CFS']
    for i in [1, 2, 3, 4]:
        try:
            a['diff_6Z_14-35_last-Prev%s_CFS' % i] = 2 * a['diff_12Z_14-28_last-Prev%s_CFS' % i] + a[
                'diff_12Z_28-35_last-Prev%s_CFS' % i]
            a['diff_0Z_14-35_last-Prev%s_CFS' % i] = 2 * a['diff_12Z_14-28_last-Prev%s_CFS' % i] + a[
                'diff_12Z_28-35_last-Prev%s_CFS' % i]
            a['Value_12Z_14-35_Prev%s_CFS' % i] = 2 * a['Value_12Z_14-28_Prev%s_CFS' % i] + a[
                'Value_12Z_28-35_Prev%s_CFS' % i]
            a['diff_12Z_14-35_last-Prev%s_CFS' % i] = 2 * a['diff_12Z_14-28_last-Prev%s_CFS' % i] + a[
                'diff_12Z_28-35_last-Prev%s_CFS' % i]
            a['diff_18Z_14-35_last-Prev%s_CFS' % i] = 2 * a['diff_12Z_14-28_last-Prev%s_CFS' % i] + a[
                'diff_12Z_28-35_last-Prev%s_CFS' % i]
        except:
            pass
    try:
        a['CFS_6Zvs0Z_14-28'] = a['diff_0Z_14-28_last-Prev4_CFS'] * -1 + a['diff_6Z_14-28_last-Prev1_CFS']
        a['diff_0Z_14-42_last-Prev24_CFS'] = a['diff_0Z_14-28_last-Prev2_CFS'] + a['diff_0Z_14-28_last-Prev4_CFS'] + \
                                             a['diff_0Z_28-42_last-Prev2_CFS'] + a['diff_0Z_28-42_last-Prev4_CFS']
        a['diff_0Z_14-42_last-Prev1D_CFS'] = a['diff_0Z_14-28_last-Prev1D_CFS'] + a['diff_0Z_28-42_last-Prev1D_CFS']
        a['Value_12Z_14-35_CFS'] = 2 * a['Value_12Z_14-28_CFS'] + a['Value_12Z_28-35_CFS']
        a['diff_11Z_14-35_last-Prev1D_CFS'] = a['diff_12Z_14-35_last-Prev1D_CFS'] + \
                                              1 / 12 * (a['Value_12Z_14-35_Prev1_CFS'] + a[
            'Value_12Z_14-35_Prev2_CFS'] + a['Value_12Z_14-35_Prev3_CFS']) - 0.25 * a['Value_12Z_14-35_CFS']
        a['diff_11Zb_14-35_last-Prev1D_CFS'] = a['diff_6Z_14-35_last-Prev4_CFS'] + a['diff_0Z_14-35_last-Prev4_CFS'] + \
                                               a['diff_18Z_14-35_last-Prev4_CFS']
        a['diff_11Zc_14-35_last-Prev1D_CFS'] = a['diff_6Z_14-35_last-Prev4_CFS'] + a['diff_0Z_14-35_last-Prev4_CFS'] + \
                                               a['diff_18Z_14-35_last-Prev4_CFS'] + \
                                               (a['diff_6Z_14-35_last-Prev3_CFS'] + a['diff_0Z_14-35_last-Prev2_CFS'] +
                                                a['diff_18Z_14-35_last-Prev1_CFS']) / 3
    except Exception as e:
        print('Failed to generate CFS features (line 2981) wuth error: %s' % e)
    try:
        a['diff_18Z_28-42_last-Prev1234_CFS'] = a['diff_18Z_28-42_last-Prev1_CFS'] + a[
            'diff_18Z_28-42_last-Prev2_CFS'] + \
                                                a['diff_18Z_28-42_last-Prev3_CFS'] + a['diff_18Z_28-42_last-Prev4_CFS']
        a['diff_18Z_14-28_last-Prev1234_CFS'] = a['diff_18Z_14-28_last-Prev1_CFS'] + a[
            'diff_18Z_14-28_last-Prev2_CFS'] + a['diff_18Z_14-28_last-Prev3_CFS'] + a['diff_18Z_14-28_last-Prev4_CFS']
        a['diff_18Z_14-42_last-Prev24_CFS'] = a['diff_18Z_14-28_last-Prev2_CFS'] + a['diff_18Z_14-28_last-Prev4_CFS'] + \
                                              a['diff_18Z_28-42_last-Prev2_CFS'] + a['diff_18Z_28-42_last-Prev4_CFS']
        a['diff_12Z_28-42_last-Prev1234_CFS'] = a['diff_12Z_28-42_last-Prev1_CFS'] + a[
            'diff_12Z_28-42_last-Prev2_CFS'] + \
                                                a['diff_12Z_28-42_last-Prev3_CFS'] + a['diff_12Z_28-42_last-Prev4_CFS']
        a['diff_12Z_28-42_last-Prev12D_CFS'] = a['diff_12Z_28-42_last-Prev1D_CFS'] + a['diff_12Z_28-42_last-Prev2D_CFS']
        a['diff_12Z_14-28_last-Prev1234_CFS'] = a['diff_12Z_14-28_last-Prev1_CFS'] + a[
            'diff_12Z_14-28_last-Prev2_CFS'] + \
                                                a['diff_12Z_14-28_last-Prev3_CFS'] + a['diff_12Z_14-28_last-Prev4_CFS']

    except Exception as e:
        print('Failed to generate rolling diff with error: %s' % e)
    a['diff_0Z_28-42_last-Prev1234_CFS'] = a['diff_0Z_28-42_last-Prev1_CFS'] + a['diff_0Z_28-42_last-Prev2_CFS'] + \
                                           a['diff_0Z_28-42_last-Prev3_CFS'] + a['diff_0Z_28-42_last-Prev4_CFS']
    a['diff_0Z_14-28_last-Prev1234_CFS'] = a['diff_0Z_14-28_last-Prev1_CFS'] + a['diff_0Z_14-28_last-Prev2_CFS'] + \
                                           a['diff_0Z_14-28_last-Prev3_CFS'] + a['diff_0Z_14-28_last-Prev4_CFS']
    a['diff_0Z_0-28_last-Prev2_CFS'] = a['diff_0Z_0-21_last-Prev2_CFS']+0.5*a['diff_0Z_21-28_last-Prev2_CFS']
    a['diff_0Z_0-28_last-Prev4_CFS'] = a['diff_0Z_0-21_last-Prev4_CFS'] + 0.5 * a['diff_0Z_21-28_last-Prev4_CFS']
    a['diff_6Z_28-42_last-Prev1234_CFS'] = a['diff_6Z_28-42_last-Prev1_CFS'] + a['diff_6Z_28-42_last-Prev2_CFS'] + \
                                           a['diff_6Z_28-42_last-Prev3_CFS'] + a['diff_6Z_28-42_last-Prev4_CFS']
    a['diff_6Z_14-28_last-Prev1234_CFS'] = a['diff_6Z_14-28_last-Prev1_CFS'] + a['diff_6Z_14-28_last-Prev2_CFS'] + \
                                           a['diff_6Z_14-28_last-Prev3_CFS'] + a['diff_6Z_14-28_last-Prev4_CFS']
    a['diff_6Z_0-21_last-Prev1234_CFS'] = a['diff_6Z_0-21_last-Prev1_CFS'] + a['diff_6Z_0-21_last-Prev2_CFS'] + \
                                           a['diff_6Z_0-21_last-Prev3_CFS'] + a['diff_6Z_0-21_last-Prev4_CFS']
    a['diff_6Z_0-16_last-Prev1234_CFS'] = a['diff_6Z_0-16_last-Prev1_CFS'] + a['diff_6Z_0-16_last-Prev2_CFS'] + \
                                          a['diff_6Z_0-16_last-Prev3_CFS'] + a['diff_6Z_0-16_last-Prev4_CFS']
    cfs_rolling_0z_cols = ['diff_0Z_0-16_last-Prev1234_CFS', 'diff_0Z_14-28_last-Prev1234_CFS','diff_0Z_14-35_last-Prev1234_CFS', 'diff_0Z_0-21_last-Prev1234_CFS']
    for c in cfs_rolling_0z_cols:
        a[c] = a[[c.replace('Prev1234','Prev%s'%i) for i in range(1,5)]].mean(axis=1)

    a['diff_0Z_14-28_last-Prev1234D_GEFS35'] = (
                a[['diff_0Z_14-28_last-Prev%sD_GEFS35' % i for i in [1, 2, 3, 4]]] * pd.Series(
            {'diff_0Z_14-28_last-Prev%sD_GEFS35' % i: (5 - i) for i in [1, 2, 3, 4]})).mean(axis=1)
    try:
        a['diff_12Z_0-13_last-Prev1_EPS+1d'] = a['diff_12Z_0-13_last-Prev1_EPS'].shift(-1)
        a['diff_12Z_28-42_last-Prev1234_CFS'] = a['diff_12Z_28-42_last-Prev1_CFS'] + a[
            'diff_12Z_28-42_last-Prev2_CFS'] + \
                                                a['diff_12Z_28-42_last-Prev3_CFS'] + a['diff_12Z_28-42_last-Prev4_CFS']
        a['diff_12Z_14-28_last-Prev1234_CFS'] = a['diff_12Z_14-28_last-Prev1_CFS'] + a[
            'diff_12Z_14-28_last-Prev2_CFS'] + \
                                                a['diff_12Z_14-28_last-Prev3_CFS'] + a['diff_12Z_14-28_last-Prev4_CFS']
        a['EPS_daily_hybrid_pred1b12Z'] = a['diff_12Z_0-13_last-Prev4D_EPS'] + 0.5 * a[
            'diff_12Z_0-13_last-Prev3D_EPS'] + 0.25 * a['diff_12Z_0-13_last-Prev2D_EPS']
        a['EPS_daily_hybrid_pred2b12Z'] = a['diff_12Z_0-13_last-Prev3D_EPS'] + 0.5 * a[
            'diff_12Z_0-13_last-Prev2D_EPS'] + 0.25 * a['diff_12Z_0-13_last-Prev1D_EPS']
    except:
        pass

    a['EPS_daily_hybrid_pred1blast'] = a['EPS_daily_hybrid_pred1b'] + a['diff_0Z_0-13_last-Prev1_EPS']
    pararaco_cols = ['diff_0Z_0-16_last-Prev4D_PARA', 'diff_0Z_0-16_last-Prev1D_PARA',
                     'diff_0Z_0-16_last-Prev1D_PARACO', 'diff_0Z_0-16_last-Prev4D_PARACO']
    pararaco_cols_18z = ['diff_12Z_0-16_last-Prev4D_PARA', 'diff_12Z_0-16_last-Prev1D_PARA',
                         'diff_12Z_0-16_last-Prev1D_PARACO', 'diff_12Z_0-16_last-Prev4D_PARACO']
    a['diff_0Z_0-16_last-Prev14D_PARARACO'] = (a[pararaco_cols] * pd.Series([1, 1, 1, 1], index=pararaco_cols)).mean(
        axis=1)
    a['diff_12Z_0-16_last-Prev14D_PARARACO'] = (
                a[pararaco_cols_18z] * pd.Series([1, 1, 1, 1], index=pararaco_cols_18z)).mean(axis=1)
    a['diff_0Z_0-16_last-Prev4D_PARARACO'] = a[
        ['diff_0Z_0-16_last-Prev4D_PARA', 'diff_0Z_0-16_last-Prev4D_PARACO']].mean(axis=1)
    a['diff_0Z_0-16_last-Prev24D_GEMCO'] = a[['diff_0Z_0-16_last-Prev2D_GEMCO', 'diff_0Z_0-16_last-Prev4D_GEMCO']].mean(
        axis=1)
    a['diff_0Z_0-16_last-Prev14D_GFSv16'] = a[
        ['diff_0Z_0-16_last-Prev1D_GFSv16', 'diff_0Z_0-16_last-Prev4D_GFSv16']].mean(axis=1)

    # mixes
    a['diff_0Z_0-16_last-Prev1D_GFSComb'] = a['diff_0Z_0-16_last-Prev1D_GEFS']+a['diff_0Z_0-16_last-Prev1D_PARA']+a['diff_0Z_0-16_last-Prev1D_PARACO']

    return a

def add_light_Xs(a,calc_start,model,x_version='v2.0_0Z'):
    csv = get_x_diffs_path(model,x_version)
    light_x_df = pd.read_csv(csv,parse_dates=['date'])
    light_x_df = light_x_df[light_x_df['date']>=calc_start]
    common = [x for x in list(a) if x in list(light_x_df)]
    # a = a[[x for x in list(a) if '_EPSCO_' not in x]]
    a = a.merge(light_x_df,on=common,how='outer',suffixes=('','_light'))
    return a


def add_regional_diffs(a,calc_start,model='EPS',ft_type='GDD',fullfiles_dirname='Full'):

    eps_full_csv = os.path.join(HOME,"degdays_archive","FULL_FILES","%s","Full_degdays_Full_%s.csv")%(fullfiles_dirname,model)
    df = pd.read_csv(eps_full_csv,parse_dates=['forecast_time','validation_day'])
    df = df[df['forecast_time']>=calc_start]
    df2 = df.groupby(['forecast_time','weight','feature_type']).mean().reset_index()

    if ft_type == 'GDD':
        columns_for_pivot = ['weight']
    else:
        columns_for_pivot = ['weight','feature_type']

    pivot_df = pd.pivot_table(df2,values='Value',index=['forecast_time'],columns=columns_for_pivot,aggfunc=np.mean)
    pivot_df = pivot_df[[x for x in list(pivot_df) if (ft_type if ft_type != 'GDD' else '') in x]]
    if ft_type != 'GDD':
        pivot_df.columns = [str(k[0])+'_'+str(k[1]) for k in pivot_df.columns]
    else:
        pivot_df *= 2
    pivot_df_diff1 = pivot_df.diff(1).rename(columns={k: 'diff_0Z_'+k+'_last-Prev1_%s'%model for k in list(pivot_df)})
    pivot_df_diff2 = pivot_df.diff(2).rename(columns={k: 'diff_0Z_'+k+'_last-Prev2_%s'%model for k in list(pivot_df)})
    pivot_df_diff3 = pivot_df.diff(3).rename(columns={k: 'diff_0Z_'+k+'_last-Prev3_%s'%model for k in list(pivot_df)})
    pivot_df_diff4 = pivot_df.diff(4).rename(columns={k: 'diff_0Z_'+k+'_last-Prev4_%s'%model for k in list(pivot_df)})

    pivot_df_diff1prev1 = pivot_df.shift(1).diff(1).rename(columns={k: 'diff_0Z_'+k+'_Prev1-Prev2_%s'%model for k in list(pivot_df)})
    pivot_df_diff2prev1 = pivot_df.shift(1).diff(2).rename(columns={k: 'diff_0Z_'+k+'_Prev1-Prev3_%s'%model for k in list(pivot_df)})
    final_df = pivot_df.rename(columns={k:'Value_0Z_%s_%s'%(k,model) for k in list(pivot_df)})
    for other in [pivot_df_diff1,pivot_df_diff2,pivot_df_diff3,pivot_df_diff4,
                  pivot_df_diff1prev1,pivot_df_diff2prev1]:
        final_df = final_df.merge(other,left_index=True,right_index=True)

    final_df = final_df.drop([x for x in list(final_df) if 'Prev3' in x or 'Prev4' in x],axis=1)

    final_df = final_df.reset_index()
    final_df = final_df[final_df['forecast_time'].dt.hour == 0]
    final_df['date'] = final_df['forecast_time'] + td(hours=8)
    final_df = final_df.merge(a[['date']],on=['date'])
    a = a.merge(final_df,on=['date'],how='outer')
    return a


def add_rolling_momentum(a,window = 3):
    y_cols_for_corr = ['y_0800-1315', 'y_0800-1745', 'y_0800-1100', 'y_1100-1200', 'y_1200-1300',
                       'y_1315-1415', 'y_1415-1515',
                       'y_1515-1615', 'y_1615-1745', 'y_1745-1845', 'y_1845-1945']
    for day in [0, 1, 2, 3, 4]:
        momentum_cols_names = [x + '_d%sma%s' % (day, window) for x in y_cols_for_corr]
        a[momentum_cols_names] = 0
        corrs_cols_df = a.loc[a['date'].dt.weekday == day, ['date'] + y_cols_for_corr].set_index('date').rolling(window,1).mean().shift(1).reset_index().rename(
            columns={k: v for k, v in zip(y_cols_for_corr, momentum_cols_names)})

        a = a[[x for x in list(a) if x not in momentum_cols_names]].merge(corrs_cols_df, on=['date'], how='outer')
        a[momentum_cols_names] = a[momentum_cols_names].fillna(method='ffill')
    return a

def add_previous_weeks_momentum(a,cols=['y_0800-1845','y_0800-1415']):
    fillnans_dict = {'y_0800-1845':'y_0800-1415','y_0800-1415':'y_0800-1415'}
    for d in [0,1,2,3,4]:
        tmp_df = a[['date']+cols][a['date'].dt.weekday == d]
        for c in cols:
            for weeks_back in [0,1,2,3]:
                tmp_df[c+'_d%s-%sw'%(d,weeks_back)] = tmp_df[c].fillna(tmp_df[fillnans_dict[c]]).fillna(0).shift(weeks_back)
        a = a.merge(tmp_df,on=['date']+cols,how='outer')
        a[list(tmp_df)] = a[list(tmp_df)].fillna(method='ffill')
    return a



def add_seasonal_diffs(a,ft,normalize=False):
    seasonal_df = pd.read_csv(
        os.path.join(HOME,"degdays_archive","Live","Seasonal Values","Seasonal_Values.csv"),
        parse_dates=['validation_day'])

    # no need in this one anymore, as we perform it in seasonal_values.py
    #seasonal_df = _extend_seasonal_values_with_previous_years(seasonal_df)

    seasonal_df['date'] = pd.to_datetime(seasonal_df['validation_day']) + td(hours=8)
    seasonal_df = seasonal_df.rename(columns={'Value': 'Seasonal_Value_0-0'})
    seasonal_df = seasonal_df[seasonal_df['feature_type'] == ft]
    seasonal_df['Seasonal_Value_0-0'] += SEASONAL_SHIFT[ft]
    seasonal_df['Seasonal_Value_0-16'] = seasonal_df['Seasonal_Value_0-0'].rolling(16,1).mean().tolist()[16:]+[np.nan]*16
    seasonal_df['Seasonal_Value_0-14'] = seasonal_df['Seasonal_Value_0-0'].rolling(14, 1).mean().tolist()[14:] + [np.nan]*14
    seasonal_df['Seasonal_Value_0-4'] = seasonal_df['Seasonal_Value_0-0'].rolling(5, 1).mean().tolist()[5:] + [np.nan]*5
    seasonal_df['Seasonal_Value_0-2'] = seasonal_df['Seasonal_Value_0-0'].rolling(3, 1).mean().tolist()[3:] + [np.nan]*3
    seasonal_df['Seasonal_Value_0-9'] = seasonal_df['Seasonal_Value_0-0'].rolling(9, 1).mean().tolist()[9:] + [np.nan] * 9
    seasonal_df['Seasonal_Value_0-8'] = seasonal_df['Seasonal_Value_0-0'].rolling(8, 1).mean().tolist()[8:] + [np.nan] * 8
    seasonal_df['Seasonal_Value_9-13'] = seasonal_df['Seasonal_Value_0-0'].rolling(4, 1).mean().tolist()[13:] + [np.nan] * 13
    #seasonal_df['Seasonal_Value_12-13'] = seasonal_df['Seasonal_Value_0-0'].rolling(2, 1).mean().tolist()[13:] + [np.nan] * 13
    seasonal_df['Seasonal_Value_8-16'] = seasonal_df['Seasonal_Value_0-0'].rolling(8, 1).mean().tolist()[16:] + [np.nan] * 16
    seasonal_df['Seasonal_Value_14-16'] = seasonal_df['Seasonal_Value_0-0'].rolling(3, 1).mean().tolist()[16:] + [np.nan] * 16
    seasonal_df['Seasonal_Value_14-14'] = seasonal_df['Seasonal_Value_0-0'].rolling(1, 1).mean().tolist()[14:] + [np.nan] * 14
    last_a_date = a['date'].iloc[-1]
    seasonal_df = seasonal_df[seasonal_df['date']<=last_a_date]
    a = a.merge(seasonal_df[['date']+[x for x in list(seasonal_df) if 'Seasonal_' in x]],
                on=['date'],how='outer')
    a['Value_0Z_0-0_EC_ma_20'] = a['Value_0Z_0-0_EC'].rolling(20, 1).mean()

    a['Value_0Z_0-0_EC_rs'] = a['Value_0Z_0-0_EC'] - a['Seasonal_Value_0-0']
    a['Value_0Z_0-0_EC_location_20'] = a['Value_0Z_0-0_EC'] - a['Value_0Z_0-0_EC_ma_20']
    for d in [5,7,10,13,15]:
        a['Value_0Z_0-0_EC_location_20_ma_%s'%d] = a['Value_0Z_0-0_EC_location_20'].rolling(d, 1).mean()
        a['Value_0Z_0-0_EC_rs_ma_%s'%d] = a['Value_0Z_0-0_EC_rs'].rolling(d, 1).mean()
        a['Value_0Z_0-0_EC_location_20_ma_%s_future'%d] = a['Value_0Z_0-0_EC_location_20_ma_%s'%d][d:].tolist() + [np.nan] * d
        a['Value_0Z_0-0_EC_rs_ma_%s_future'%d] = a['Value_0Z_0-0_EC_rs_ma_%s'%d][d:].tolist() + [np.nan] * d
        a['Value_0Z_0-0_EC_rs_ma_%s_past+future' % d] = a['Value_0Z_0-0_EC_rs_ma_%s_future'%d]+a['Value_0Z_0-0_EC_rs_ma_%s'%d]
    try:

        for model in ['GEPS','GEMCO','GEFS','PARA','PARACO','GFSCO','GEFSL','CFS']:
            gap_with_ec = (a['Value_0Z_0-0_EC'] - a['Value_0Z_0-0_%s' % model])
            try:
                gap_with_ec_12z = (a['Value_12Z_0-0_EC'] - a['Value_12Z_0-0_%s'%model])
                gap_with_ec = gap_with_ec.fillna(gap_with_ec_12z)
            except:
                print ('Couldnt use 12Z to fill EC_0-0 nans')
            a['Value_0Z_0-16D_%s_rs'%model] = (a['Value_0Z_0-16D_%s'%model] - (a['Seasonal_Value_0-16']-gap_with_ec))
            a['Value_0Z_0-16_%s_rs' % model] = (a['Value_0Z_0-16_%s' % model] - (a['Seasonal_Value_0-16'] - gap_with_ec))
            try:
                a['Value_12Z_0-16_%s_rs' % model] = (a['Value_12Z_0-16_%s' % model] - (a['Seasonal_Value_0-16'] - gap_with_ec_12z))
            except:
                pass
        a['Value_0Z_0-13D_EPS_rs'] = a['Value_0Z_0-13D_EPS'] - a['Seasonal_Value_0-14']
        try:
            a['Value_12Z_0-13_EPS_rs'] = a['Value_12Z_0-13_EPS'] - a['Seasonal_Value_0-14']
        except KeyError as e:
            print('Skipping Seasonals for 12Z')
        a['Value_0Z_0-13_EPS_rs'] = a['Value_0Z_0-13_EPS'] - a['Seasonal_Value_0-14']
        a['Value_0Z_0-2_EC_rs'] = a['Value_0Z_0-2_EC'] - a['Seasonal_Value_0-2']
        a['Value_0Z_0-4_EC_rs'] = a['Value_0Z_0-4_EC'] - a['Seasonal_Value_0-4']

        a['Value_0Z_0-8_EPS_rs'] = a['Value_0Z_0-8_EPS'] - a['Seasonal_Value_0-9']
        a['Value_0Z_9-13_EPS_rs'] = a['Value_0Z_9-13_EPS'] - a['Seasonal_Value_9-13']
        a['Value_0Z_13-13_EPS_rs'] = a['Value_0Z_13-13_EPS'] - a['Seasonal_Value_14-14']
        a['Value_12Z_13-13_EPS_rs'] = a['Value_12Z_13-13_EPS'] - a['Seasonal_Value_14-14']
        #a['Value_0Z_12-13_EPS_rs'] = a['Value_0Z_12-13_EPS'] - a['Seasonal_Value_12-13']
        a['Value_0Z_0-8D_EC_rs'] = a['Value_0Z_0-8D_EC'] - a['Seasonal_Value_0-9']
        a['Value_6Z_0-16_CFS_rs'] = (a['Value_6Z_0-16_CFS'] - (a['Seasonal_Value_0-16'] - gap_with_ec))
        a['Value_0Z_8-16D_PARA_rs'] = a['Value_0Z_8-16D_PARA'] - a['Seasonal_Value_8-16']
        a['Value_0Z_8-16D_PARACO_rs'] = a['Value_0Z_8-16D_PARACO'] - a['Seasonal_Value_8-16']
        a['Value_0Z_14-16_PARA_rs'] = a['Value_0Z_14-16_PARA'] - a['Seasonal_Value_14-16']
        a['Value_6Z_14-16_PARA_rs'] = a['Value_6Z_14-16_PARA'] - a['Seasonal_Value_14-16']
        a['Value_6Z_8-16_PARA_rs'] = a['Value_6Z_8-16_PARA'] - a['Seasonal_Value_8-16']
        a['Value_6Z_0-8_PARA_rs'] = a['Value_6Z_0-8_PARA'] - a['Seasonal_Value_0-8']
        a['Value_0Z_0-8_PARA_rs'] = a['Value_0Z_0-8_PARA'] - a['Seasonal_Value_0-8']
        a['Value_6Z_14-16_GFSv16_rs'] = a['Value_0Z_14-16_GFSv16'] - a['Seasonal_Value_14-16']
        a['Value_6Z_8-16_GFSv16_rs'] = a['Value_0Z_8-16_GFSv16'] - a['Seasonal_Value_8-16']
        a['Value_6Z_0-8_GFSv16_rs'] = a['Value_6Z_0-8_GFSv16'] - a['Seasonal_Value_0-8']
        a['Value_0Z_0-8_GFSv16_rs'] = a['Value_0Z_0-8_GFSv16'] - a['Seasonal_Value_0-8']
        a['Value_0Z_14-16_PARACO_rs'] = a['Value_0Z_14-16_PARACO'] - a['Seasonal_Value_14-16']
        a['Value_6Z_14-16_PARACO_rs'] = a['Value_6Z_14-16_PARACO'] - a['Seasonal_Value_14-16']
        a['Value_6Z_8-16_PARACO_rs'] = a['Value_0Z_8-16_PARACO'] - a['Seasonal_Value_8-16']
        a['Value_0Z_14-16_GEFS_rs'] = a['Value_0Z_14-16_GEFS'] - a['Seasonal_Value_14-16']
        a['Value_6Z_14-16_GEFS_rs'] = a['Value_6Z_14-16_GEFS'] - a['Seasonal_Value_14-16']
        a['Value_6Z_0-8_PARACO_rs'] = a['Value_6Z_0-8_PARACO'] - a['Seasonal_Value_0-8']
        a['Value_0Z_0-8_PARACO_rs'] = a['Value_0Z_0-8_PARACO'] - a['Seasonal_Value_0-8']

        if normalize:
            a[[x for x in list(a) if ('13D' in x or '16D' in x) and 'rs' in x]] -= a[[x for x in list(a) if ('13D' in x or '16D' in x) and 'rs' in x]].mean()
    except Exception as e:
        print ('couldnt generate seasonal values due to error : %s'%e)
    try:
        a['Value_0Z_day15trend_EPS'] = a['Value_0Z_13-13_EPS'] - a['Value_0Z_9-13_EPS']

        a['Value_0Z_day15trend_GEFS'] = a['Value_0Z_14-14_GEFS'] - a['Value_0Z_11-15_GEFS']
        a['Value_0Z_day15trend_EPS_diff1'] = a['Value_0Z_day15trend_EPS'].diff()
        a['Value_0Z_day15trend_EPS_diff2'] = a['Value_0Z_day15trend_EPS'].diff(2)
        a['Value_0Z_day15trend_GEFS_diff1'] = a['Value_0Z_day15trend_GEFS'].diff()
        a['Value_0Z_day15trend_GEFS_diff2'] = a['Value_0Z_day15trend_GEFS'].diff(2)
        a['Value_6Z_day15trend_GEFS'] = a['Value_6Z_14-14_GEFS'] - a['Value_6Z_11-15_GEFS']
        a['Value_6Z_day15trend_GEFS_diff1'] = a['Value_6Z_day15trend_GEFS'].diff()
        a['Value_6Z_day15trend_GEFS_diff2'] = a['Value_6Z_day15trend_GEFS'].diff(2)
        # 12Z if possible
        a['Value_12Z_day15trend_EPS'] = a['Value_12Z_13-13_EPS'] - a['Value_12Z_9-13_EPS']
        a['Value_12Z_day15trend_GEFS'] = a['Value_12Z_14-14_GEFS'] - a['Value_12Z_11-15_GEFS']
        a['Value_0Z_day15trend_GEFS_Fdiff2'] = a['Value_0Z_day15trend_GEFS'] - a['Value_12Z_day15trend_GEFS']
        a['Value_0Z_day15trend_EPS_Fdiff1'] = a['Value_0Z_day15trend_EPS'] - a['Value_12Z_day15trend_EPS']

    except Exception as e:
        print ('Couldnt extract day15 trend due to Error: %s'%e)

    # stability based on seasonal vals
    a['std_0-16_full'] = a[
        ['Value_0Z_0-13D_EPS_rs', 'Value_0Z_0-16D_GEFS_rs', 'Value_0Z_0-16D_PARA_rs', 'Value_0Z_0-16D_GFSCO_rs',
         'Value_0Z_0-16D_GEFSL_rs',
         'Value_0Z_0-16D_PARACO_rs']].std(axis=1)
    a['std_0-16_full_rs'] = a['std_0-16_full'] - a['std_0-16_full'].rolling(30, 1).mean()
    a['std_0-16_american'] = a[
        ['Value_0Z_0-16D_GEFS_rs', 'Value_0Z_0-16D_PARA_rs', 'Value_0Z_0-16D_GFSCO_rs', 'Value_0Z_0-16D_GEFSL_rs',
         'Value_0Z_0-16D_PARACO_rs']].std(axis=1)
    a['std_0-16_american_rs'] = a['std_0-16_american'] - a['std_0-16_american'].rolling(30, 1).mean()
    a['std_0-16_top3'] = a[['Value_0Z_0-16D_GEFS_rs', 'Value_0Z_0-16D_GEFSL_rs', 'Value_0Z_0-13D_EPS_rs']].std(axis=1)
    a['std_0-16_top3_rs'] = a['std_0-16_top3'] - a['std_0-16_top3'].rolling(30, 1).mean()

    # 0-8 days stds
    full_cols = ['Value_0Z_0-8_EPS','Value_0Z_0-8_EC','Value_0Z_GEPS','Value_0Z_0-8_GEMCO',
                 'Value_0Z_0-8_GEM',
                 'Value_0Z_0-8_GEFS', 'Value_0Z_0-8_PARA','Value_0Z_0-8_PARACO',
                 'Value_0Z_0-8_GFSCO', 'Value_0Z_0-8_GEFSL']
    american_cols = ['Value_0Z_0-8_GEFS', 'Value_0Z_0-8_PARA','Value_0Z_0-8_PARACO',
                 'Value_0Z_0-8_GFSCO', 'Value_0Z_0-8_GEFSL']
    top4_cols = ['Value_0Z_0-8_EPS','Value_0Z_0-8_GEPS','Value_0Z_0-8_GEFS','Value_0Z_0-8_GEFSL']
    for c in full_cols:
        a = normalize_value_col_by_EC(a,c)
    a['std_0-8_full'] = (a[full_cols].T - a[full_cols].mean(axis=1).T).T.std(axis=1)
    a['std_0-8_american'] = (a[american_cols].T - a[american_cols].mean(axis=1).T).T.std(axis=1)
    a['std_0-8_top4'] = (a[top4_cols].T - a[top4_cols].mean(axis=1).T).T.std(axis=1)
    # rs
    a['std_0-8_full_rs'] = a['std_0-8_full'] - a['std_0-8_full'].rolling(20, 1).mean()
    a['std_0-8_american_rs'] = a['std_0-8_american'] - a['std_0-8_american'].rolling(20, 1).mean()
    a['std_0-8_top4_rs'] = a['std_0-8_top4'] - a['std_0-8_top4'].rolling(20, 1).mean()
    # diff
    a['std_0-8_full_diff1'] = a['std_0-8_full'].diff()
    a['std_0-8_american_diff1'] = a['std_0-8_american'].diff()
    a['std_0-8_top4_diff1'] = a['std_0-8_top4'].diff()

    return a.sort_values(by=['date'])

def normalize_value_col_by_EC(a,col,days_for_ref='0-0'):
    first_day_col = col.replace('0-8',days_for_ref).replace('0-13',days_for_ref).replace('0-10',days_for_ref)
    gap = a[first_day_col] - a['Value_0Z_%s_EC'%days_for_ref]
    a[col+'_ECNorm'] = a[col]- gap
    return a

def add_deltaYs_columns(a):

    a['y_1245-1430'] = a['y_1245-1315'] + a['y_1315-1345'] + a['y_1345-1415'] + a['y_1415-1430']
    a['y_1130-1745'] = a['y_1130-1415'] + a['y_1415-1745']
    a['y_1245-1745'] = a['y_1245-1315'] + a['y_1315-1345'] + a['y_1345-1415'] + a['y_1415-1745']
    a['y_1245-1645'] = a['y_1245-1315'] + a['y_1315-1345'] + a['y_1345-1415'] + a['y_1415-1645']
    a['y_1245-1945'] = a['y_1245-1745'] + a['y_1745-1845'] + a['y_1845-1945']
    a['y_1245-1345'] = a['y_1245-1315'] + a['y_1315-1345']
    a['y_1245-1415'] = a['y_1245-1315'] + a['y_1315-1345'] + a['y_1345-1415']
    a['y_0745-1100'] = a['y_0745-0800'] + a['y_0800-1100']
    a['y_0700-0745'] = a['y_0700-0800'] - a['y_0745-0800']
    a['y_0630-0730'] = a['y_0530-0730'] - a['y_0530-0630']
    a['y_0630-0700'] = a['y_0630-0730'] - a['y_0700-0730']
    a['y_0600-0630'] = a['y_0600-0700'] - a['y_0630-0700']

    a['y_1130-1445'] = a['y_1130-1415'] + a['y_1415-1445']
    a['y_1415-1845'] = a['y_1415-1745'] + a['y_1745-1845']
    a['y_1100-1315'] = a['y_1100-1130'] + a['y_1130-1315']
    a['y_1100-1245'] = a['y_1100-1315'] - a['y_1245-1315']

    a['y_1145-1245'] = a['y_1145-1415'] - a['y_1245-1315'] - a['y_1315-1415']
    a['y_1145-1315'] = a['y_1145-1415'] - a['y_1315-1415']
    a['y_1145-1200'] = a['y_1145-1415'] - a['y_1200-1415']
    a['y_1145-1745'] = a['y_1145-1245'] + a['y_1245-1745']
    a['y_1315-1745'] = a['y_1315-1415'] + a['y_1415-1745']
    a['y_1245-1715'] = a['y_1245-1745'] - a['y_1715-1745']
    a['y_1400-1445'] = a['y_1400-1415'] + a['y_1415-1445']
    a['y_1300-1345'] = a['y_1300-1315'] + a['y_1315-1345']
    a['y_1100-1200'] = a['y_1100-1130'] + a['y_1130-1200']
    a['y_1145-1515'] = a['y_1145-1315'] + a['y_1315-1515']
    a['y_1245-1515'] = a['y_1245-1315'] + a['y_1315-1515']

    a['y_1230-1245'] = a['y_1215-1245'] - a['y_1215-1230']
    a['y_1230-1315'] = a['y_1230-1245'] + a['y_1245-1315']
    a['y_1200-1215'] = a['y_1145-1215'] - a['y_1145-1200']
    a['y_1200-1230'] = a['y_1200-1215'] + a['y_1215-1230']
    a['y_1145-1230'] = a['y_1145-1200'] + a['y_1200-1230']
    a['y_1100-1200'] = a['y_1100-1130'] + a['y_1130-1200']
    a['y_1130-1230'] = a['y_1130-1200'] + a['y_1200-1230']
    a['y_1100-1230'] = a['y_1100-1200'] + a['y_1200-1230']
    a['y_1100-1115'] = a['y_1100-1130'] - a['y_1115-1130']

    a['y_1245-1300'] = a['y_1245-1315'] - a['y_1300-1315']
    a['y_1200-1300'] = a['y_1200-1230'] + a['y_1230-1245'] + a['y_1245-1300']
    a['y_1200-1330'] = a['y_1200-1300'] + a['y_1300-1330']
    a['y_1345-1400'] = a['y_1345-1415'] - a['y_1400-1415']
    a['y_1130-1245'] = a['y_1130-1415'] - a['y_1245-1415']
    a['y_1130-1300'] = a['y_1130-1315'] - a['y_1300-1315']

    a['y_0800-0930'] = a['y_0800-0900'] + a['y_0900-0930']
    a['y_1515-1615'] = a['y_1515-1745'] - a['y_1615-1745']
    a['y_1245-1615'] = a['y_1245-1515'] + a['y_1515-1615']
    a['y_1615-1715'] = a['y_1615-1745'] - a['y_1715-1745']
    a['y_1615-1845'] = a['y_1615-1745']+a['y_1745-1845']
    a['y_1530-1615'] = a['y_1530-1715'] - a['y_1615-1715']
    a['y_1415-1615'] = a['y_1415-1515'] + a['y_1515-1615']

    a['y_0730-0800'] = a['y_0700-0800'] - a['y_0700-0730']

    a['y_0730-1100'] = a['y_0730-0800'] + a['y_0800-1100']
    a['y_0745-1100'] = a['y_0745-0800'] + a['y_0800-1100']
    a['y_1115-1200'] = a['y_1115-1130'] + a['y_1130-1200']
    a['y_0900-1100'] = a['y_0800-1100'] - a['y_0800-0900']

    a['y_1745-1945'] = a['y_1745-1845'] + a['y_1845-1945']

    a['y_1145-1845'] = a['y_1145-1745'] + a['y_1745-1845']
    a['y_1245-1845'] = a['y_1245-1745'] + a['y_1745-1845']
    a['y_1145-1615'] = a['y_1145-1745'] - a['y_1615-1745']

    a['y_1815-1845_-1d'] = [np.nan] + a['y_1815-1845'].tolist()[:-1]
    a['y_1130-1315_+1d'] = a['y_1130-1315'].tolist()[1:]+ [np.nan]
    a['y_1100-1130_+1d'] = a['y_1100-1130'].tolist()[1:]+ [np.nan]
    a['y_0000-0400_+1d'] = a['y_0000-0400'].tolist()[1:] + [np.nan]
    a['y_0400-0530_+1d'] = a['y_0400-0530'].tolist()[1:] + [np.nan]
    a['y_0600-0800_+1d'] = a['y_0600-0800'].tolist()[1:] + [np.nan]
    a['y_0000-0600_+1d'] = a['y_0000-0600'].tolist()[1:] + [np.nan]
    a['y_0000-0100_+1d'] = a['y_0000-0100'].tolist()[1:] + [np.nan]
    a['y_0100-0200_+1d'] = a['y_0000-0100'].tolist()[1:] + [np.nan]
    a['y_0200-0300_+1d'] = a['y_0200-0300'].tolist()[1:] + [np.nan]
    a['y_0000-0300_+1d'] = a['y_0000-0300'].tolist()[1:] + [np.nan]
    a['y_0000-0600_+1d'] = a['y_0000-0600'].tolist()[1:] + [np.nan]
    a['y_0600-0800_+1d'] = a['y_0600-0800'].tolist()[1:] + [np.nan]
    a['y_2145-0800'] = a['y_2145-2345'] + a['y_0000-0400_+1d'] + a['y_0400-0530_+1d']+a['y_0600-0800_+1d']

    a['y_1130-1445'] = a['y_1130-1415'] + a['y_1415-1445']
    a['y_1415-1845'] = a['y_1415-1745'] + a['y_1745-1845']
    a['y_1100-1315'] = a['y_1100-1130'] + a['y_1130-1315']
    a['y_1100-1245'] = a['y_1100-1315'] - a['y_1245-1315']
    a['y_1100-1145'] = a['y_1100-1315'] - a['y_1145-1315']
    a['y_0800-1415'] = a['y_0800-1100'] + a['y_1100-1130'] + a['y_1130-1415']
    a['y_0800-1230'] = a['y_0800-1100'] + a['y_1100-1130'] + a['y_1130-1230']
    a['y_1715-1845'] = a['y_1715-1745'] + a['y_1745-1845']
    a['y_1315-1645'] = a['y_1315-1745'] - a['y_1645-1745']
    a['y_1615-1645'] = a['y_1515-1645'] - a['y_1515-1615']

    try:
        a['y_2200-2330_-1d'] = [np.nan] + a['y_2200-2330'].tolist()[:-1]
        a['y_2145-2330_-1d'] = [np.nan] + a['y_2145-2330'].tolist()[:-1]
        a['y_2200-2300_-1d'] = [np.nan] + a['y_2200-2300'].tolist()[:-1]
        a['y_2300-2345_-1d'] = [np.nan] + a['y_2300-2345'].tolist()[:-1]
        a['y_1745-2045'] = a['y_1745-1945'] + a['y_1945-2045']
    except:
        print ('Skipping bad deltaYs... ')
    a['y_PM_weekdays'] = np.nan
    a['y_PM_weekdays'][a['date'].dt.weekday.isin([0, 4])] = a['y_1130-1745'][a['date'].dt.weekday.isin([0, 4])]
    a['y_PM_weekdays'][a['date'].dt.weekday.isin([1, 2, 3])] = (a['y_1130-1300'] + a['y_1415-1745'])[
        a['date'].dt.weekday.isin([1, 2, 3])]
    a['y_PM_summer'] = a['y_1145-1215'] + a['y_1315-1345'] + a['y_1415-1745']

    summertime_cond = a['date'].dt.month.isin(range(4, 11)) | a['date'].apply(
        lambda x: (x.month == 11 and x.day < 10) or (x.month == 3 and x.day > 10))
    a['y_ChicOpen'] = a['y_1345-1415']  # a['y_1245-1415']
    a['y_ChicOpen'][summertime_cond] = a['y_1245-1315'][summertime_cond]  # a['y_1145-1315'][summertime_cond]
    a['y_ChicOpen2'] = a['y_1400-1445']
    a['y_ChicOpen2'][summertime_cond] = a['y_1300-1345'][summertime_cond]  # a['y_1145-1315'][summertime_cond]
    a['y_ChicOpen3'] = a['y_1400-1415']
    a['y_ChicOpen3'][summertime_cond] = a['y_1300-1315'][summertime_cond]  # a['y_1145-1315'][summertime_cond]

    a['y_ChicOpen-1'] = a['y_1245-1315']  # a['y_1245-1415']
    a['y_0700-0800Chic'] = a['y_1300-1400']

    summertime_cond = a['date'].dt.month.isin(range(4, 11)) | a['date'].apply(
        lambda x: (x.month == 11 and x.day < 10) or (x.month == 3 and x.day > 10))
    a['y_ChicOpen-1'][summertime_cond] = a['y_1145-1215'][summertime_cond]  # a['y_1145-1315'][summertime_cond]
    a.loc[summertime_cond, 'y_0700-0800Chic'] = a.loc[summertime_cond,'y_1200-1300']
    a['y_1130-1745_EPS_adjusted'] = a['y_1130-1745']
    cond = a['date'].dt.weekday.isin([0])
    cond2 = a['date'].dt.weekday.isin([3])
    a.loc[cond, 'y_1130-1745_EPS_adjusted'] = a.loc[cond, 'y_1315-1745']
    a.loc[cond2, 'y_1130-1745_EPS_adjusted'] = a.loc[cond2, 'y_1130-1245'] + a.loc[cond2, 'y_1315-1745']

    a['y_1000-1130'] = a['y_1000-1100'] + a['y_1100-1130']
    a['y_1100-1745'] = a['y_1100-1245'] + a['y_1245-1745']
    a['y_0800-1745'] = a['y_0800-1100'] + a['y_1100-1745']

    a['y_0730-1200'] = a['y_0730-1100'] + a['y_1100-1200']
    a['y_0800-1200'] = a['y_0800-1100'] + a['y_1100-1200']

    # more calcs
    a['y_0730-1745'] = a['y_0730-0800'] + a['y_0800-1745']
    a['y_0730-1200'] = a['y_0730-1100'] + a['y_1100-1200']
    a['y_1415-1845'] = a['y_1415-1745'] + a['y_1745-1845']
    a['y_0800-1000'] = a['y_0800-0900'] + a['y_0900-1000']
    a['y_1100-1400'] = a['y_1100-1200'] + a['y_1200-1300'] + a['y_1300-1400']
    a['y_1300-1745'] = a['y_1300-1315'] + a['y_1315-1745']
    a['y_1315-1330'] = a['y_1300-1330'] - a['y_1300-1315']

    try:
        a['y_2200-0400'] = a['y_2200-2330_-1d'] + a['y_0000-0400']
    except:
        print ('Skipping on 2200-0400 Ys')
    # remove bad calculations
    a.loc[~a[['y_1515-1745', 'y_1645-1745', 'y_1515-1645']].isna().mean(axis=1).isin([0,1]),['y_1515-1745']] = np.nan
    a.loc[~a[['y_1415-1745', 'y_1645-1745', 'y_1415-1645']].isna().mean(axis=1).isin([0, 1]), ['y_1415-1745']] = np.nan
    a.loc[~a[['y_1245-1745', 'y_1645-1745', 'y_1245-1645']].isna().mean(axis=1).isin([0, 1]), ['y_1245-1745']] = np.nan

    ### More more calcs
    a['y_1130-1145'] = a['y_1130-1200'] - a['y_1145-1200']
    a['y_0600-0800'] = a['y_0600-0700'] + a['y_0700-0800']
    a['y_0800-1315'] = a['y_0800-1100'] + a['y_1100-1130'] + a['y_1130-1315']
    a['y_0800-1230'] = a['y_0800-1200'] + a['y_1200-1230']
    a['y_0900-1200'] = a['y_0900-1000'] + a['y_1000-1200']
    a['y_0700-1000'] = a['y_0700-0800'] + a['y_0800-1000']
    a['y_0600-0800_-'] = -a['y_0600-0800']
    a['y_0600-0730'] = a['y_0600-0700'] + a['y_0700-0730']
    a['y_0600-1745'] = a['y_0600-0800'] + a['y_0800-1745']
    a['y_0600-1845'] = a['y_0600-1745'] + a['y_1745-1845']
    if 'y_0800-1845' not in list(a):
        a['y_0800-1845'] = a['y_0800-1745'] + a['y_1745-1845']
    else:
        a['y_0800-1845'] = a['y_0800-1845'].fillna(a['y_0800-1745'] + a['y_1745-1845'])
    a['y_0800-1945'] = a['y_0800-1745'] + a['y_1745-1945']
    a['y_0000-1745'] = a['y_0000-0400'] + a['y_0400-0530'] + a['y_0600-0800'] + a['y_0800-1100'] + a['y_1100-1130'] + a[
        'y_1130-1315'] + a['y_1315-1745']
    a['y_1345-1445'] = a['y_1345-1415'] + a['y_1415-1445']
    a['y_1245-1445'] = a['y_1245-1345'] + a['y_1345-1445']
    a['y_1445-1515'] = a['y_1245-1515'] - a['y_1245-1445']
    a['y_1645-1715'] = a['y_1615-1715'] - a['y_1615-1645']
    a['y_0800-1130'] = a['y_0800-1100'] + a['y_1100-1130']
    a['y_0830-0900'] = a['y_0800-0900'] - a['y_0800-0830']
    a['y_0800-0815'] = a['y_0745-0815'] - a['y_0745-0800']
    a['y_0815-0830'] = a['y_0800-0830'] - a['y_0800-0815']
    a['y_0630-0800'] = a['y_0600-0800'] - a['y_0600-0630']
    a['y_1330-1400'] = a['y_1300-1400'] - a['y_1300-1330']
    a['y_1330-1745'] = a['y_1315-1745'] - a['y_1315-1330']
    a['y_1515-1845'] = a['y_1515-1745'] + a['y_1745-1845']
    a['y_1200-1315'] = a['y_1130-1315'] - a['y_1130-1200']
    a['y_1230-1300'] = a['y_1200-1300'] - a['y_1200-1230']
    a['y_0700-1845'] = a['y_0700-0800'] + a['y_0800-1745'] + a['y_1745-1845']
    a['y_1815-1845'] = a['y_1745-1845'] - a['y_1745-1815']
    a['y_1945-0800'] = a['y_1945-2045'] + a['y_2145-0800']
    a['y_2200-0800'] = a['y_2200-2330'] + a['y_0000-0400_+1d'] + a['y_0400-0530_+1d'] + a['y_0600-0800_+1d']
    a['y_0930-1000'] = a['y_0900-1000'] - a['y_0900-0930']
    a['y_1200-1430'] = a['y_1200-1300'] + a['y_1300-1400'] + a['y_1400-1430']
    a['y_1145-2045'] = a['y_1145-1745'] + a['y_1745-2045']
    a['y_1315-2045'] = a['y_1315-1745'] + a['y_1745-2045']
    a['y_1030-1130'] = a['y_1030-1100'] + a['y_1100-1130']
    a['y_1100-1315'] = a['y_1100-1130'] + a['y_1130-1315']
    a['y_1100-1415'] = a['y_1100-1315'] + a['y_1315-1415']
    a['y_1100-1515'] = a['y_1100-1415'] + a['y_1415-1515']
    a['y_1100-1645'] = a['y_1100-1315'] + a['y_1315-1645']
    a['y_1715-1845'] = a['y_1715-1745'] + a['y_1745-1845']
    a['y_1730-1745'] = a['y_1700-1745'] - a['y_1700-1730']
    a['y_1715-1730'] = a['y_1715-1745'] - a['y_1730-1745']
    a['y_1645-1730'] = a['y_1645-1745'] - a['y_1730-1745']
    a['y_1915-1945'] = a['y_1845-1945'] - a['y_1845-1915']
    a['y_1300-1500'] = a['y_1300-1400'] + a['y_1400-1500']
    a['y_1315-1545'] = a['y_1315-1515'] + a['y_1515-1545']
    a['y_1245-1545'] = a['y_1245-1315'] + a['y_1315-1545']
    a['y_1300-1545'] = a['y_1300-1315'] + a['y_1315-1545']
    a['y_1300-1645'] = a['y_1300-1315'] + a['y_1315-1515']+a['y_1515-1645']
    a['y_1300-1645'] = a['y_1300-1315'] + a['y_1315-1515']+a['y_1515-1645']
    a['y_1145-1345'] = a['y_1145-1245']+a['y_1245-1345']
    a['y_0000-0800'] = a['y_0000-0400'] + a['y_0400-0530'] + a['y_0600-0800']
    a['y_0000-0800_+1d'] = a['y_0000-0800'].tolist()[1:] + [np.nan]

    ## additional strats
    a['y_0400-0800'] = a['y_0400-0600'] + a['y_0600-0800']
    a['y_0400-0730'] = a['y_0400-0800'] - a['y_0730-0800']
    a['y_0745-1200'] = a['y_0745-0800'] + a['y_0800-1200']
    a['y_0745-0900'] = a['y_0745-0800'] + a['y_0800-0900']
    a['y_0745-0830'] = a['y_0745-0800'] + a['y_0800-0830']
    a['y_0730-0900'] = a['y_0730-0800'] + a['y_0800-0900']
    a['y_0600-2045'] = a['y_0600-0800'] + a['y_0800-1945'] + a['y_1945-2045']
    a['y_0800-2045'] = a['y_0800-1745'] + a['y_1745-2045']
    a['y_0900-0915'] = a['y_0845-0915'] - a['y_0845-0900']
    a['y_0800-0915'] = a['y_0800-0900'] + a['y_0900-0915']
    a['y_0630-0745'] = a['y_0630-0700'] + a['y_0700-0745']
    a['y_0530-0600'] = a['y_0530-0700'] - a['y_0600-0700']
    a['y_0600-1000'] = a['y_0600-0800'] + a['y_0800-1000']
    a['y_0600-1200'] = a['y_0600-1000'] + a['y_1000-1200']

    a['y_1130-1215'] = a['y_1130-1200'] + a['y_1200-1215']
    a['y_1200-1345'] = a['y_1200-1300'] + a['y_1300-1345']
    a['y_1200-1745'] = a['y_1200-1315'] + a['y_1315-1745']
    a['y_1200-1845'] = a['y_1200-1745'] + a['y_1745-1845']
    a['y_1145-1715'] = a['y_1145-1745'] - a['y_1715-1745']
    a['y_1200-1715'] = a['y_1200-1245'] + a['y_1245-1315'] + a['y_1315-1745']
    a['y_1215-1715'] = a['y_1215-1245'] + a['y_1245-1715']
    a['y_1145-1300'] = a['y_1145-1200'] + a['y_1200-1300']
    a['y_1430-1530'] = a['y_1430-1715'] - a['y_1530-1715']

    a['y_1300-1415'] = a['y_1300-1400'] + a['y_1400-1415']
    a['y_1330-1415'] = a['y_1330-1400'] + a['y_1400-1415']
    a['y_1330-1345'] = a['y_1315-1345'] - a['y_1315-1330']
    a['y_1345-1515'] = a['y_1345-1415'] + a['y_1415-1515']
    a['y_1415-1845'] = a['y_1415-1745'] + a['y_1745-1845']
    a['y_1415-1545'] = a['y_1415-1515'] + a['y_1515-1545']
    a['y_1515-1815'] = a['y_1515-1745'] + a['y_1745-1815']
    a['y_1415-1815'] = a['y_1415-1515'] + a['y_1515-1815']

    a['y_1745-2045hack1'] = a['y_1745-1845'] + a['y_1945-2045']
    a['y_1845-2045'] = a['y_1845-1945'] + a['y_1945-2045']
    a['y_1745-2030'] = a['y_1745-1945'] + a['y_1945-2030']
    a['y_1845-2030'] = a['y_1845-1945'] + a['y_1945-2030']
    a['y_1815-1915'] = a['y_1815-1845'] + a['y_1845-1915']
    a['y_1815-1945'] = a['y_1815-1915'] + a['y_1915-1945']
    a['y_1815-2030'] = a['y_1815-1945'] + a['y_1945-2030']
    a['y_1945-2345'] = a['y_1945-2145'] + a['y_2145-2345']

    ## More **4 preds
    a['y_1100-1615'] = a['y_1100-1415'] + a['y_1415-1615']
    a['y_2300-0400'] = a['y_2300-0000'] + a['y_0000-0400_+1d']
    a['y_1715-2030'] = a['y_1715-1745'] + a['y_1745-2030']
    a['y_1345-1615'] = a['y_1345-1415'] + a['y_1415-1615']
    a['y_1745-1915'] = a['y_1745-1845'] + a['y_1845-1915']
    a['y_1715-1915'] = a['y_1715-1815'] + a['y_1815-1915']
    a['y_1545-1645'] = a['y_1515-1645'] - a['y_1515-1545']
    a['y_1500-1530'] = a['y_1400-1415']+ a['y_1415-1645'] - a['y_1400-1500'] - a['y_1530-1645']
    a['y_1515-1530'] = a['y_1500-1530'] - a['y_1500-1515']
    a['y_1530-1745'] = a['y_1530-1645'] + a['y_1645-1745']
    a['y_0800-2330'] = a['y_0000-2330'] - a['y_0000-0800']
    a['y_1745-2330'] = a['y_0800-2330'] - a['y_0800-1745']
    a['y_1000-1300'] = a['y_1000-1200'] + a['y_1200-1300']
    a['y_0600-1945'] = a['y_0600-0800'] + a['y_0800-1945']
    a['y_1000-1400'] = a['y_1000-1200'] + a['y_1200-1400']
    a['y_1000-1515'] = a['y_1000-1100'] + a['y_1100-1515']
    a['y_1100-1300'] = a['y_1100-1200'] + a['y_1200-1300']
    a['y_1400-1630'] = a['y_1400-1415'] + a['y_1415-1745'] - a['y_1630-1745']
    a['y_1145-1645'] = a['y_1145-1415'] + a['y_1415-1645']
    a['y_2030-2330'] = a['y_1745-2330'] - a['y_1745-2030']
    a['y_0745-1745'] = a['y_0745-0800'] + a['y_0800-1745']
    a['y_0745-1845'] = a['y_0745-0800'] + a['y_0800-1845']
    a['y_0745-2030'] = a['y_0745-0800'] + a['y_0800-1745'] + a['y_1745-2030']
    a['y_0400-0700'] = a['y_0400-0800'] - a['y_0700-0800']
    a['y_1645-1815'] = a['y_1645-1745'] + a['y_1745-1815']
    a['y_0730-0830'] = a['y_0730-0800'] + a['y_0800-0830']
    a['y_1430-1500'] = a['y_1430-1530'] - a['y_1500-1530']

    a['y_1530-1545'] = a['y_1530-1615'] - a['y_1545-1615']
    a['y_1545-1745'] = a['y_1515-1745'] - a['y_1515-1545']
    a['y_0600-1200'] = a['y_0600-0800'] + a['y_0800-1200']
    a['y_1400-1745'] = a['y_1400-1415'] + a['y_1415-1745']
    a['y_1400-1645'] = a['y_1400-1415'] + a['y_1415-1645']
    a['y_1315-1945'] = a['y_1315-1745'] + a['y_1745-1945']
    a['y_1100-1945'] = a['y_1100-1315'] + a['y_1315-1945']
    a['y_1130-1400'] = a['y_1130-1300'] + a['y_1300-1400']
    a['y_1645-1915'] = a['y_1645-1745'] + a['y_1745-1915']
    a['y_1100-1215'] = a['y_1100-1200'] + a['y_1200-1215']

    # 07/01/2021
    a['y_0600-0830'] = a['y_0600-0800'] + a['y_0800-0830']
    a['y_0600-0930'] = a['y_0600-0800'] + a['y_0800-0930']
    a['y_0400-0830'] = a['y_0400-0600'] + a['y_0600-0830']
    a['y_0730-0745'] = a['y_0730-0800'] - a['y_0745-0800']
    a['y_0730-0930'] = a['y_0730-0800'] + a['y_0800-0930']
    a['y_0800-1500'] = a['y_0800-1415'] + a['y_1415-1500']
    a['y_0800-1300'] = a['y_0800-1200'] + a['y_1200-1300']
    a['y_1100-1430'] = a['y_1100-1415'] + a['y_1415-1430']
    a['y_1230-1330'] = a['y_1230-1300'] + a['y_1300-1330']
    a['y_1200-1500'] = a['y_1200-1300'] + a['y_1300-1500']
    a['y_1330-1430'] = a['y_1330-1400'] + a['y_1400-1430']
    a['y_1400-1545'] = a['y_1400-1415'] + a['y_1415-1545']
    a['y_0600-2030'] = a['y_0600-0800'] + a['y_0800-1945'] + a['y_1945-2030']
    a['y_1415-2030'] = a['y_1415-1715'] + a['y_1715-2030']
    a['y_1615-1945'] = a['y_1615-1845'].fillna(0) + a['y_1845-1945'].fillna(0)
    a['y_1545-1945'] = a['y_1545-1615'] + a['y_1615-1945'].fillna(0)
    a['y_1415-1945'] = a['y_1415-1745'].fillna(0) + a['y_1745-1945'].fillna(0)
    a['y_1200-1945'] = a['y_1200-1745'] + a['y_1745-1945']

    ### 27/1
    a['y_0815-0900'] = a['y_0800-0900'] - a['y_0800-0815']
    a['y_0600-1100'] = a['y_0600-0800']+a['y_0800-1100']
    a['y_0800-1400'] = a['y_0800-1415'] - a['y_1400-1415']
    a['y_0800-1530'] = a['y_0800-1415']+a['y_1415-1515']+a['y_1515-1530']
    a['y_0800-1545'] = a['y_0800-1415'] + a['y_1415-1545']
    a['y_0930-1030'] = a['y_0900-1030'] - a['y_0900-0930']
    a['y_0930-1000'] = a['y_0930-1030'] - a['y_1000-1030']
    a['y_0930-0945'] = a['y_0930-1000'] - a['y_0945-1000']

    a['y_0930-0945'] = a['y_0930-1000']
    a['y_1100-1845'] = a['y_1100-1200']+a['y_1200-1415'] + a['y_1415-1845']
    a['y_0900-1845'] = a['y_0900-1100'] + a['y_1100-1845']
    a['y_1100-1215'] = a['y_1100-1200'] + a['y_1200-1215']
    a['y_1200-1615'] = a['y_1200-1400'] + a['y_1400-1415']+a['y_1415-1615']
    a['y_1400-1545'] = a['y_1400-1415'] + a['y_1415-1545']

    a['y_1215-1345'] = a['y_1215-1245']+a['y_1245-1345']
    a['y_1215-1545'] = a['y_1215-1345']+a['y_1345-1415']+a['y_1415-1545']#+a['y_1545-1745']
    a['y_1300-1645'] = a['y_1300-1400'] + a['y_1400-1415'] + a['y_1415-1645']

    a['y_1400-1530'] = a['y_1400-1430'] + a['y_1430-1530']
    a['y_1400-1745'] = a['y_1400-1415']+a['y_1415-1745']  #- a['y_1615-1645']
    a['y_1400-1945'] = a['y_1400-1415'] + a['y_1415-1745'] + a['y_1745-1945']
    a['y_1445-1615'] = a['y_1445-1645'] - a['y_1615-1645']
    a['y_1430-1630'] = a['y_1400-1630'] - a['y_1400-1430']
    a['y_1430-1615'] = a['y_1415-1615'] - a['y_1415-1430']
    a['y_1445-1545'] = a['y_1445-1515']+a['y_1515-1545']
    a['y_1430-1515'] = a['y_1415-1445']+a['y_1445-1515'] - a['y_1415-1430']
    a['y_1445-1530'] = a['y_1400-1500']+a['y_1500-1530'] - a['y_1400-1445']

    a['y_1545-1715'] = a['y_1545-1645'] + a['y_1645-1715']
    a['y_1630-1715'] = a['y_1630-1745'] - a['y_1715-1745']
    a['y_1545-1630'] = a['y_1545-1745'] - a['y_1630-1715']
    a['y_1700-1715'] = a['y_1700-1745'] - a['y_1715-1745']
    a['y_1645-1700'] = a['y_1645-1715'] - a['y_1700-1715']

    a['y_1615-1630'] = a['y_1430-1630'] - a['y_1430-1615']
    a['y_1630-1645'] = a['y_1615-1645'] - a['y_1615-1630']
    a['y_1715-1945'] = a['y_1715-1745'] + a['y_1745-1945']
    a['y_1745-1800'] = a['y_1730-1800'] - a['y_1730-1745']
    a['y_1800-1815'] = a['y_1745-1815'] - a['y_1745-1800']
    a['y_1915-2030'] = a['y_1915-1945']+a['y_1945-2030']

    ### 1/2/21
    a['y_0915-0930'] = a['y_0900-0930'] - a['y_0900-0915']
    a['y_1030-1200'] = a['y_1030-1100'] + a['y_1100-1200']
    a['y_1200-1645'] = a['y_1200-1500'] + a['y_1500-1515'] + a['y_1515-1645']
    a['y_1215-1300'] = a['y_1215-1315'] - a['y_1300-1315']
    a['y_1215-1545'] = a['y_1215-1315'] + a['y_1315-1415'] + a['y_1415-1545']
    a['y_1300-1430'] = a['y_1300-1400'] + a['y_1400-1430']
    a['y_1330-1545'] = a['y_1330-1400'] + a['y_1400-1545']
    a['y_1400-1515'] = a['y_1400-1430'] + a['y_1430-1515']
    a['y_1415-1915'] = a['y_1415-1845'] + a['y_1845-1915']
    a['y_1645-1845'] = a['y_1645-1745'] + a['y_1745-1845']
    a['y_1815-1900'] = a['y_1800-1900'] - a['y_1800-1815']
    a['y_1900-1915'] = a['y_1815-1915'] - a['y_1815-1900']
    a['y_1200-2030'] = a['y_1200-1945'] + a['y_1945-2030']

    a['y_0600-0900'] = a['y_0600-0800'] + a['y_0800-0900']
    a['y_0600-0930'] = a['y_0600-0900'] + a['y_0900-0930']
    a['y_0715-0730'] = a['y_0700-0730'] - a['y_0700-0715']
    a['y_0715-0745'] = a['y_0715-0730'] + a['y_0730-0745']
    a['y_1115-1145'] = a['y_1115-1130'] + a['y_1130-1145']
    a['y_1115-1215'] = a['y_1115-1145'] + a['y_1145-1215']

    a['y_0600-1415'] = a['y_0600-0800'] + a['y_0800-1415']
    a['y_0830-0930'] = a['y_0830-0900'] + a['y_0900-0930']
    a['y_1430-1615'] = a['y_1430-1500'] + a['y_1500-1515'] + a['y_1515-1615']
    a['y_1545-1815'] = a['y_1545-1745'] + a['y_1745-1815']
    a['y_1845-1900'] = a['y_1845-1915'] - a['y_1900-1915']
    a['y_1915-1930'] = a['y_1900-1930'] - a['y_1900-1915']
    a['y_1930-1945'] = a['y_1915-1945'] - a['y_1915-1930']
    a['y_1945-2000'] = a['y_1900-2000'] - a['y_1915-1945'] - a['y_1900-1915']
    a['y_1845-1930'] = a['y_1845-1915'] + a['y_1915-1930']
    a['y_1930-2030'] = a['y_1915-2030'] - a['y_1915-1930']
    a['y_1815-1900'] = a['y_1815-1845'] + a['y_1845-1900']
    a['y_1745-1900'] = a['y_1745-1845'] + a['y_1845-1900']

    # 13/3
    a['y_0000-0200'] = a['y_0000-0100'] + a['y_0100-0200']
    a['y_0800-1215'] = a['y_0800-1200'] + a['y_1200-1215']
    a['y_0830-1000'] = a['y_0800-1000'] - a['y_0800-0830']
    a['y_0900-1230'] = a['y_0900-1200'] + a['y_1200-1230']
    a['y_1000-1145'] = a['y_1000-1200'] - a['y_1145-1200']
    a['y_1215-1515'] = a['y_1200-1500'] - a['y_1200-1215'] + a['y_1500-1515']
    a['y_1230-1400'] = a['y_1200-1400'] - a['y_1200-1230']
    a['y_1245-1400'] = a['y_1245-1300'] + a['y_1300-1400']
    a['y_1300-1515'] = a['y_1300-1500'] + a['y_1500-1515']
    a['y_1315-1430'] = a['y_1315-1330'] + a['y_1330-1430']
    a['y_1330-1500'] = a['y_1330-1400'] + a['y_1400-1500']
    a['y_1500-1645'] = a['y_1415-1645'] - a['y_1415-1500']
    a['y_1545-1915'] = a['y_1545-1745'] + a['y_1745-1915']
    a['y_1615-1815'] = a['y_1615-1745'] + a['y_1745-1815']
    # 18/3/21
    a['y_1030-1045'] = a['y_1030-1100'] - a['y_1045-1115'] + a['y_1100-1115']
    a['y_1045-1100'] = a['y_1030-1100'] - a['y_1030-1045']
    a['y_1045-1130'] = a['y_1045-1100'] + a['y_1100-1130']
    a['y_1000-1615'] = a['y_1000-1400'] + a['y_1400-1415'] + a['y_1415-1615']
    a['y_1215-1415'] = a['y_1215-1315'] + a['y_1315-1415']
    a['y_1400-1515'] = a['y_1400-1500'] + a['y_1500-1515']
    a['y_1430-1445'] = a['y_1415-1445'] - a['y_1415-1430']
    a['y_1315-1430'] = a['y_1315-1415'] + a['y_1415-1430']
    a['y_0800-1215'] = a['y_0800-1200'] + a['y_1200-1215']
    # 7/4
    a['y_0600-0815'] = a['y_0600-0800'] + a['y_0800-0815']
    a['y_0800-0845'] = a['y_0800-0900'] - a['y_0845-0900']
    a['y_0700-0845'] = a['y_0700-0900'] - a['y_0845-0900']
    a['y_1030-1045'] = a['y_1030-1100'] - a['y_1045-1115'] + a['y_1100-1115']
    a['y_1045-1100'] = a['y_1030-1100'] - a['y_1030-1045']
    a['y_1045-1130'] = a['y_1045-1100'] + a['y_1100-1130']
    a['y_1145-1330'] = a['y_1145-1315'] + a['y_1315-1330']
    a['y_1615-1945'] = a['y_1615-1815'] + a['y_1815-1945']
    a['y_1615-1730'] = a['y_1615-1715']+a['y_1715-1730']

    # last
    a['y_0630-0745'] = a['y_0630-0730']+a['y_0730-0745']
    a['y_0600-0745'] = a['y_0600-0700']+a['y_0700-0745']
    a['y_1115-1245']  =a['y_1115-1215']+a['y_1215-1245']
    a['y_1230-1415'] = a['y_1230-1400']+a['y_1400-1415']
    a['y_1315-1530'] = a['y_1315-1515']+a['y_1515-1530']
    a['y_1300-1530'] = a['y_1300-1400']+a['y_1400-1530']

    a['y_0730-0815'] = a['y_0730-0745']+a['y_0745-0815']
    a['y_1730-1830'] = a['y_1730-1800']+a['y_1800-1830']
    a['y_1030-1100_EasternEIA'] = a['y_1430-1500']
    a['y_1015-1045_EasternEIA'] = a['y_1415-1445']
    a['y_0745-0800_Eastern'] = a['y_1145-1200']
    a['y_1515-1745_-1d'] = a['y_1515-1745'].shift(1).fillna(method='ffill')
    a['y_1515-1745_-2d'] = a['y_1515-1745'].shift(2).fillna(method='ffill')
    a['y_1515-1745_-1to3d'] = a['y_1515-1745'].shift(1).rolling(3, 3).mean().fillna(method='ffill')
    a['y_1515-1745_-1to5d'] = a['y_1515-1745'].shift(1).rolling(5, 5).mean().fillna(method='ffill')

    a['y_1315-1400'] = a['y_1300-1400'] - a['y_1300-1315']
    a['y_1345-1430'] = a['y_1345-1400'] + a['y_1400-1430']
    a['y_1500-1545'] = a['y_1500-1515'] + a['y_1515-1545']
    a['y_1645-1700'] = a['y_1645-1715'] - a['y_1700-1715']
    a['y_1200-2030'] = a['y_1200-1945'] + a['y_1945-2030']
    a['y_1200-1915'] = a['y_1200-1945'] - a['y_1915-1945']
    a['y_1515-1915'] = a['y_1515-1745'] + a['y_1745-1915']
    a['y_1900-1945'] = a['y_1900-1915'] + a['y_1915-1945']
    a['y_0530-0730'] = a['y_0530-0630'] + a['y_0630-0730']
    a['y_0530-0800'] = a['y_0530-0630'] + a['y_0630-0730'] + a['y_0730-0800']
    a['y_1315-1815'] = a['y_1315-1715'] + a['y_1715-1815']
    a['y_0800-1815'] = a['y_0800-1315'] + a['y_1315-1815']

    # Nov21
    a['y_1545-1700'] = a['y_1545-1715'] - a['y_1700-1715']
    a['y_1615-1700'] = a['y_1615-1715'] - a['y_1700-1715']
    a['y_0200-0600'] = a['y_0000-0600'] - a['y_0000-0200']
    a['y_0200-0400'] = a['y_0000-0400'] - a['y_0000-0200']
    a['y_0100-0600'] = a['y_0000-0100'] + a['y_0200-0600']
    a['y_0100-0200'] = a['y_0000-0200'] + a['y_0000-0100']

    winter_time_cond = lambda x: x.month in [12, 1, 2] or (x.month == 11 and x.day > 10) or (
                x.month == 3 and x.day < 10)
    a.loc[a.date.apply(winter_time_cond), 'y_1030-1100_EasternEIA'] = a.loc[a.date.apply(winter_time_cond), 'y_1530-1545']
    a.loc[a.date.apply(winter_time_cond), 'y_1015-1045_EasternEIA'] = a.loc[a.date.apply(winter_time_cond), 'y_1515-1545']
    a['y_1015-1045_EasternEIA_-1d'] = a['y_1015-1045_EasternEIA'].shift(1)
    # put EIA reaction on all days
    a.loc[a['date'].dt.weekday.isin([5, 6, 0, 1, 2, 3]), 'y_1015-1045_EasternEIA_-1d'] = np.nan
    a['y_1015-1045_EasternEIA_-1d'] = a['y_1015-1045_EasternEIA_-1d'].fillna(method='ffill')

    a.loc[a.date.apply(winter_time_cond), 'y_0745-0800_Eastern'] = a.loc[a.date.apply(winter_time_cond), 'y_1245-1300']

    # momentum fts
    for strat in ['y_1100-1415','y_1000-1200','y_1200-1515','y_1515-1745']:
        a['%s_-1d'%(strat)] = a[strat].shift(1).fillna(method='ffill')
        a['%s_-2d'%strat] = a[strat].shift(2).fillna(method='ffill')
        a['%s_-1to3d'%strat] = a[strat].shift(1).rolling(3, 3).mean().fillna(method='ffill')
        a['%s_-1to5d'%strat] = a[strat].shift(1).rolling(5, 5).mean().fillna(method='ffill')

    return a

def add_coffee_deltaYs(xy_df):
    ### ADD Delta Ys
    xy_df['y_1130-1715'] = xy_df['y_1130-1415'] + xy_df['y_1415-1715']
    xy_df['y_1130-1745'] = xy_df['y_1130-1415'] + xy_df['y_1415-1745']
    xy_df['y_0900-1100'] = xy_df['y_0900-1000'] + xy_df['y_1000-1100']
    xy_df['y_1100-1200'] = xy_df['y_1100-1130'] + xy_df['y_1130-1200']
    xy_df['y_0900-1745'] = xy_df['y_0900-1100'] + xy_df['y_1100-1130'] + xy_df['y_1130-1745']
    xy_df['y_1545-1645'] = xy_df['y_1515-1645'] - xy_df['y_1515-1545']
    xy_df['y_1415-1745_no12Z'] = xy_df['y_1415-1745'] - xy_df['y_1545-1645']
    xy_df['y_1515-1815'] = xy_df['y_1515-1715'] + xy_df['y_1715-1815']
    xy_df['y_1315-1645'] = xy_df['y_1315-1415'] + xy_df['y_1415-1645']
    xy_df['y_1315-1615'] = xy_df['y_1315-1515'] + xy_df['y_1515-1615']
    xy_df['y_1315-1745'] = xy_df['y_1315-1615'] + xy_df['y_1615-1745']
    xy_df['y_1415-1745'] = xy_df['y_1315-1615'] + xy_df['y_1615-1745'] - xy_df['y_1315-1415']
    xy_df['y_1615-1645'] = xy_df['y_1615-1745'] - xy_df['y_1645-1745']
    xy_df['y_0900-1415'] = xy_df['y_0900-1100'] + xy_df['y_1100-1130'] + xy_df['y_1130-1415']
    xy_df['y_0900-1745'] = xy_df['y_0900-1415'] + xy_df['y_1415-1745']
    xy_df['y_1100-1745'] = xy_df['y_1100-1130'] + xy_df['y_1130-1745']
    return xy_df

def merge_with_xs(a,allow_12z,suffix,weights,ft,start=dtdt(2018,1,1),end=dtdt(2030,1,1)):
    unnecessary_value_cond = lambda x: 'Value_' in x and 'Prev' in x and not ('CFS' in x and '28' in x and ('6Z' in x or '0Z' in x or '12Z' in x)) and not \
        sum([s in x and ('0-13' in x or '0-16' in x or '8-16' in x) for s in ['EPS','GEFS','GEPS']]) and x not in \
        ['Value_0Z_0-0_Prev1_EC','Value_0Z_0-0_Prev2_EC']
    models_tuples = [(model, hour) for model in ['EC', 'EPS', 'GEM', 'GEMCO', 'GEPS','ICON'] for hour in [0, 12]] + \
                    [(model, hour) for model in ['GEFS', 'PARA', 'PARACO', 'CFS','CFSCO','GFSv16'] for hour in [0, 6, 12]] + \
                    [(model, hour) for model in ['GEFSL', 'GFSCO', 'GEFS35','GEFSCO35', 'EPS45'] for hour in [0]] + \
                    [('PARA', 18),('PARACO', 18), ('GEFS', 18),('GFSv16',18), ('CFS', 18)]  # []
    real_suffix = suffix
    if suffix in FAKE_SUFFIXES.keys():
        real_suffix = FAKE_SUFFIXES[suffix]
    for mod, last_hour in models_tuples:
        print('merging model: %s' % mod)
        s = ''  # if mod != 'GEPS' else '_Pmean'
        if USE_LAST_3_MONTHS_X_DIFFS and real_suffix in SUFFIXES_SUPPORTED_FOR_3_MONTHS \
                and abs(dtdt.now() - start).days < 90:
            input_path = os.path.join(HOME, "Xdata", "Last3M", "X_file_DailyDiffs_%s%s_%s.csv") % (mod, s, real_suffix)
        else:
            input_path = os.path.join(HOME, "Xdata", "X_file_DailyDiffs_%s%s_%s.csv") % (mod, s, real_suffix)
            if not allow_12z:
                continue
        if mod == 'GFSv16':
            bb = 0
        if mod == 'EPS' and last_hour == 0:
            bb = 0

        eps45 = pd.read_csv(
            input_path,
            parse_dates=['forecast_time'])

        eps45 = eps45[(eps45['forecast_time']>=min(start,eps45['forecast_time'].max()))&(eps45['forecast_time']<=end)]
        # Filter bas lines
        eps45 = eps45[(eps45['feature_type'] == ft) & (eps45['weight'].isin(weights))]
        eps45 = eps45.drop_duplicates(subset=['forecast_time'], keep='last')
        print('handling model: %s | Hour: %s | A.shape = %s | eps45.shape = %s' % (mod, last_hour, str(a.shape), str(eps45.shape)))
        if eps45.shape[0] == 0:
            continue
        if mod == 'GFSv16' and last_hour == 6:
            bb = 0
        if (last_hour != 12 and not (mod in ['CFSCO','CFS','PARACO','GEFS','PARA'] and last_hour in [0,6,12, 18])) and not mod in ['GEFS','EPS']:
            eps45 = eps45[eps45.isna().mean(axis=1) < 0.3]

        model_hours_of_tomorrow = MODELS_HOURS_OF_TOMORROW
        if suffix == 'v8_12Z':
            model_hours_of_tomorrow = [x for x in model_hours_of_tomorrow if x != 12]
        eps45['time'] = eps45['forecast_time'] + td(
            days=1 if (mod in ['EPS45', 'GEFS35','GEFSCO35'] or last_hour in model_hours_of_tomorrow) else 0)
        eps45 = eps45[(eps45['forecast_time'].dt.hour == last_hour) & (eps45['forecast_time'] < dtdt.today())]

        # rename the columns
        l = list(eps45)
        eps45 = eps45.rename(columns={x: x.replace('diff', 'diff_%sZ' % last_hour) +
                                         '_%s' % (mod) for x in l if 'diff' in x and 'diff8' not in x})
        eps45 = eps45.rename(columns={x: x.replace('Value', 'Value_%sZ' % last_hour) +
                                         '_%s' % (mod) for x in l if 'Value' in x})
        # todo check if working
        eps45 = eps45[[x for x in list(eps45) if not unnecessary_value_cond(x)]]

        eps45['date'] = eps45.time.dt.date
        if eps45.shape[0] != eps45.drop_duplicates(subset=['date', 'feature_type', 'weight']).shape[0]:
            raise AssertionError('eps45 file has several weight which will cause Error when merging')

        eps45 = eps45[[x for x in list(eps45) if x not in ['time', 'forecast_time']]]

        b = a[[x for x in list(a) if x != 'weight']].merge(eps45[[x for x in list(eps45) if x != 'weight' and 'index_level' not in x]],
                                                           on=['date', 'feature_type'], suffixes=('', '_%s' % mod),
                                                           how='outer')
        a = b
    return a

def light_X_loader(model,x_daily_diffs_path,ft,hours=[0,12],use_yesterdays_12z=True,
                   start=None,allow_value_columns=False):
    df = pd.read_csv(x_daily_diffs_path,parse_dates=['forecast_time'])

    df['hour'] = df['forecast_time'].dt.hour
    if 12 in hours and use_yesterdays_12z:
        df['forecast_time'] += td(hours=12)
    df['date'] = pd.to_datetime(df['forecast_time'].dt.date)+td(hours=8)
    if start is not None:
        df = df[df['forecast_time']>=start]
    df = df.query('feature_type==@ft')
    df = df.query('hour.isin(@hours)')


    cond = lambda x: 'diff' in x.lower()
    if allow_value_columns:
        cond = lambda x: 'diff' in x.lower() or 'value' in x.lower()
    df = df[['date', 'hour'] + [x for x in list(df) if cond(x)]]
    pivot = pd.pivot_table(df, values=[x for x in list(df) if cond(x)], index=['date'],columns=['hour'])

    pivot.columns = [x[0].replace('diff_',f'diff_{x[1]}Z_').replace('Value_',f'Value_{x[1]}Z_')+f'_{model}_{ft}' for x in list(pivot)]
    return pivot


def add_manual_features(final_results_df, strategy,start=dtdt(2019, 11, 1),end=dtdt(2020, 9, 1),
                        weekdays=[0,1,2,3,4],months=range(1,13),suffix='v3',ft='GDD',add_deltaYs=True,override_big_model_hour=None,
                        allow_12z=False):
    a = final_results_df[
        (final_results_df['time'] >=start) & (final_results_df['time'] <= end) &
        (final_results_df.time.dt.weekday.isin(weekdays)) &  #
        (final_results_df.time.dt.month.isin(months))]  # not summertime_cond(x))]
    a['date'] = a.time.dt.date

    daily_features_names_dict = {'EPS':''}
    big_model_hour = 0  # 0
    secondary_model_hour = 6
    prev_big_model = 0  # (big_model_hour - 12) % 24

    if strategy == '0000-0400':
        big_model_hour = 12
        secondary_model_hour = 18
        prev_big_model = 12
    if override_big_model_hour is not None:
        big_model_hour = override_big_model_hour
        prev_big_model = override_big_model_hour
    weights = ['population_US_weight','basic_NG_weight']  # ' # 'CDD' ##['NG16_midwest_weight']  #
    if 'Midwest' in suffix:
        weights = ['NG16_midwest_weight']
    if len(weights) ==2:
        a['weight'] = weights[0]
    a['feature_type'] = ft

    a = merge_with_xs(a,allow_12z,suffix,weights,ft,start=start,end=end)
    a['date'] = pd.to_datetime(a['date'])

    ## Add more features based on existing

    # Fill nans with 0, also for Vals ?  todo
    #a[[x for x in list(a) if 'diff' in x]][(a['date']>=start)&(a['date']<=end)] = a[[x for x in list(a) if 'diff' in x]][(a['date']>=start)&(a['date']<=end)].fillna(0)
    a.loc[(a['date'] >= start)&(a['date'] <= end), [x for x in list(a) if 'diff' in x]] = a.loc[(a['date'] >= start)&(a['date'] <= end), [x for x in list(a) if 'diff' in x]].fillna(0)
    # a[['date','time']+[x for x in list(a) if 'PARA' in x]][a['date'].dt.month == 4]
    a = a.sort_values(['date'])[['date', 'time'] + [x for x in list(a) if x not in ['date', 'time']]]
    ref_time = a['time'].dropna().iloc[-1]
    a['date'] = pd.to_datetime(a['date']) + td(hours=ref_time.hour, minutes=ref_time.minute)
    normalize_values = True

    """
    stack = {}
    for pred in ['diff_0Z_0-16_last-Prev1_GEFSL','diff_0Z_0-16_last-Prev2_GEFS','diff_0Z_0-16_last-Prev4_PARA']:
        s = a[a['date'] >= dtdt(2020, 3, 1)][[x for x in list(a) if 'diff' in x or x.startswith('y_')]].corr()[
            pred].loc[sorted([x for x in list(a) if 'y_' in x])]
        stack[pred+'_midwest']= s
    d = pd.DataFrame(stack)
    d.to_csv(os.path.join(HOME,"performance_analysis","Midwest_Vs_US","Midwest_chosen_preds_corr.csv"))
    #d_mid = pd.read_csv(os.path.join(HOME,"performance_analysis","Midwest_Vs_US","Midwest_chosen_preds_corr.csv"))
    #dd = d.merge(d_mid,on=)
    """
    try:
        if normalize_values and 'Midwest' not in suffix:
            avg_day1 = a[['Value_%sZ_0-0_GEPS' % big_model_hour, 'Value_%sZ_0-0_EPS' % big_model_hour,
                          'Value_%sZ_0-0_GEFS' % big_model_hour]].mean(axis=1)
            gaps = a[['Value_%sZ_0-0_GEPS' % big_model_hour, 'Value_%sZ_0-0_EPS' % big_model_hour,
                      'Value_%sZ_0-0_GEFS' % big_model_hour]] - pd.DataFrame(
                {'Value_%sZ_0-0_GEPS' % big_model_hour: avg_day1,
                 'Value_%sZ_0-0_GEFS' % big_model_hour: avg_day1,
                 'Value_%sZ_0-0_EPS' % big_model_hour: avg_day1})
            for gap_name in list(gaps):
                model = gap_name.split('_')[-1]
                gap = gaps[gap_name]
                a['gap_%s' % model] = gap
                if model in ['GEFS', 'GEPS']:
                    l = ['Value_%sZ_0-13_%s' % (big_model_hour, model), 'Value_%sZ_0-16_%s' % (big_model_hour, model),
                         'Value_%sZ_8-16_%s' % (big_model_hour, model)] + \
                        ['Value_%sZ_0-13_Prev1_%s' % (big_model_hour, model),
                         'Value_%sZ_0-16_Prev1_%s' % (big_model_hour, model),
                         'Value_%sZ_8-16_Prev1_%s' % (big_model_hour, model)] + \
                        ['Value_%sZ_0-13_Prev2_%s' % (big_model_hour, model),
                         'Value_%sZ_0-16_Prev2_%s' % (big_model_hour, model),
                         'Value_%sZ_8-16_Prev2_%s' % (big_model_hour, model)]
                else:
                    l = ['Value_%sZ_0-13_%s' % (big_model_hour, model), 'Value_%sZ_0-13_Prev1_%s' % (big_model_hour, model),
                         'Value_%sZ_0-13_Prev2_%s' % (big_model_hour, model)]
                for c in l:
                    a[c] = a[c] - a['gap_%s' % model]
            a['diff_%sZ_other-EPS(1)_0-13' % big_model_hour] = a[['Value_%sZ_0-13_GEPS' % big_model_hour,
                                                                  'Value_%sZ_0-13_GEFS' % big_model_hour]].mean(axis=1) - a[
                                                                   'Value_%sZ_0-13_Prev1_EPS' % prev_big_model]
            a['diff_%sZ_EPS-GEFS_0-13' % big_model_hour] = a[['Value_%sZ_0-13_EPS' % big_model_hour]].mean(axis=1) - a[
                'Value_%sZ_0-13_GEFS' % big_model_hour]
            a['diff_%sZ_GEFS-GEPS(1)_0-13' % big_model_hour] = a[['Value_%sZ_0-13_GEFS' % big_model_hour]].mean(axis=1) - a[
                'Value_%sZ_0-13_Prev1_GEPS' % big_model_hour]
            a['diff_%sZ_GEPS-EPS(1)_0-13' % big_model_hour] = a[['Value_%sZ_0-13_GEPS' % big_model_hour]].mean(axis=1) - a[
                'Value_%sZ_0-13_Prev1_EPS' % prev_big_model]

            a['diff_%sZ_GEPS-EPS_0-13' % big_model_hour] = a[['Value_%sZ_0-13_GEPS' % big_model_hour]].mean(axis=1) - a[
                'Value_%sZ_0-13_Prev1_EPS' % big_model_hour]
            a['diff_%sZ_GEFS-EPS(1)_0-13' % big_model_hour] = a[['Value_%sZ_0-13_GEFS' % big_model_hour]].mean(axis=1) - a[
                'Value_%sZ_0-13_Prev1_EPS' % prev_big_model]

            a['diff_0Z_0-13_last-Prev1_GEFSLCO'] = (2 * a['diff_0Z_0-13_last-Prev1_GEFSL'] + a[
                'diff_0Z_0-13_last-Prev1_GFSCO']) / 3
            a['diff_0Z_0-16_last-Prev1_GEFSLCO'] = (a['diff_0Z_8-16_last-Prev1_GEFSL'] + a['diff_0Z_0-16_last-Prev1_GEFSL'] + a['diff_0Z_0-8_last-Prev1_GFSCO'])
            a['diff_0Z_0-16_last-Prev2_GEFSLCO'] = (a['diff_0Z_8-16_last-Prev2_GEFSL'] + a['diff_0Z_0-16_last-Prev2_GEFSL'] + a['diff_0Z_0-8_last-Prev2_GFSCO'])
            a['diff_0Z_0-16_last-Prev12_GEFSLCO'] = a['diff_0Z_0-16_last-Prev1_GEFSLCO'] + a['diff_0Z_0-16_last-Prev2_GEFSLCO']
            a['diff_0Z_0-16_last-Prev1_GEFSLCO'] = (a['diff_0Z_8-16_last-Prev1_GEFSL'] + a['diff_0Z_0-8_last-Prev1_GFSCO'])

            a['diff_0Z_0-13_last-Prev2_GEFSLCO'] = (2 * a['diff_0Z_0-13_last-Prev2_GEFSL'] + a[
                'diff_0Z_0-13_last-Prev2_GFSCO']) / 3
            a['EPS_pred1'] = 3 * a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour] + 2 * a[
                'diff_%sZ_0-8_last-Prev2_EC' % big_model_hour]  # + a['diff_%sZ_GEFS-EPS(1)_0-13']
            a['EPS_pred2'] = 3 * (a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]) / (
            a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]).std() + \
                             2 * (a['diff_%sZ_0-8_last-Prev2_EC' % big_model_hour] / a[
                'diff_%sZ_0-8_last-Prev2_EC' % big_model_hour].std()) + \
                             (a['diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour] / a[
                                 'diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour].std())
            bb = 111
            a['EPS_pred3'] = 6 * (a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]) / (
                        a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]).std() + \
                             4 * (a['diff_%sZ_0-8_last-Prev2_EC' % big_model_hour] / a[
                        'diff_%sZ_0-8_last-Prev2_EC' % big_model_hour].std()) + \
                             2 * (a['diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour] / a[
                        'diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour].std()) + \
                             (a['diff_%sZ_0-16_last-Prev2_GEFS' % big_model_hour] / a[
                                 'diff_%sZ_0-16_last-Prev2_GEFS' % big_model_hour].std()) + \
                             (a['diff_%sZ_0-13_last-Prev1_GEPS' % big_model_hour] / a[
                                 'diff_%sZ_0-13_last-Prev1_GEPS' % big_model_hour].std()) + \
                             (a['diff_%sZ_GEFS-EPS(1)_0-13' % big_model_hour] / a[
                                 'diff_%sZ_GEFS-EPS(1)_0-13' % big_model_hour].std()) + \
                             (a['diff_%sZ_GEPS-EPS(1)_0-13' % big_model_hour] / a[
                                 'diff_%sZ_GEPS-EPS(1)_0-13' % big_model_hour].std())
        a['EC_AM_Summer_pred1'] = _normalize_series(_normalize_series(a['diff_%sZ_0-8_last-Prev2_EC' % big_model_hour])+
                                _normalize_series(a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]) +
                                0.5 *_normalize_series(a['diff_%sZ_0-16_last-Prev4_PARA' % big_model_hour]))
        a['EC_AM_Summer_pred2'] = _normalize_series(_normalize_series(a['diff_%sZ_0-8_last-Prev2_EC' % big_model_hour]) +
                                                    _normalize_series(a['diff_%sZ_0-8_last-Prev1_EC' % big_model_hour]) +
                                                    _normalize_series(a[get_daily_feature_name_by_version('diff_%sZ_last-D2_EC' % big_model_hour,version=suffix)]) +
                                                    0.5 * _normalize_series(a['diff_%sZ_0-16_last-Prev4_PARA' % big_model_hour]))
        a['GEM_AM_pred1'] = _normalize_series(_normalize_series(a['diff_%sZ_0-10_last-Prev2_GEM' % big_model_hour]) +
                                _normalize_series(a['diff_%sZ_0-10_Prev1-Prev2_GEM' % big_model_hour]) +
                                0.5 * _normalize_series(a['diff_%sZ_0-10_last-Prev4_GEM' % big_model_hour]))
        a['ECGEM_AM_pred1'] = _normalize_series(_normalize_series(a['GEM_AM_pred1'])+ _normalize_series(a['EC_AM_Summer_pred1']))

        a['GEM_AM_pred2'] = _normalize_series(_normalize_series(a['diff_%sZ_0-10_last-Prev1_GEM' % big_model_hour]) +
                                     _normalize_series(a['diff_%sZ_0-10_last-Prev4_GEM' % big_model_hour]))
        a['GEMCO_AM_pred2'] = _normalize_series(_normalize_series(a['diff_%sZ_0-16_last-Prev1_GEMCO' % big_model_hour]) +
                                               _normalize_series(a['diff_%sZ_0-16_last-Prev4_GEMCO' % big_model_hour]))
        a['GEMEMCO_AM_pred2'] = a['GEM_AM_pred2'] + a['GEMCO_AM_pred2']

        a['diff_%sZ_0-2_last-Prev12_GEM'%big_model_hour] = 0.66 * a['diff_%sZ_0-2_last-Prev1_GEM'%big_model_hour] + 0.33 * a['diff_%sZ_0-2_last-Prev2_GEM'%big_model_hour]

        a['EPSpost_pred1'] = 3 * (a['diff_%sZ_0-13_last-Prev2_EPS' % big_model_hour]) / (
        a['diff_%sZ_0-13_last-Prev2_EPS' % big_model_hour]).std()
        a['EPSpost_pred2'] = 6 * (a['diff_%sZ_0-13_last-Prev2_EPS' % big_model_hour]) / (
        a['diff_%sZ_0-13_last-Prev2_EPS' % big_model_hour]).std() + \
                             4 * (a['diff_%sZ_0-13_last-Prev1_EPS' % big_model_hour]) / (
                             a['diff_%sZ_0-13_last-Prev1_EPS' % big_model_hour]).std() + \
                             3 * (a['diff_%sZ_0-13_Prev1-Prev2_EPS' % big_model_hour]) / (
                             a['diff_%sZ_0-13_Prev1-Prev2_EPS' % big_model_hour]).std()  # + \
        a['EPSpost_pred2.1'] = a['EPSpost_pred2'] + \
                               1 * (a['diff_%sZ_14-28_last-Prev2_CFS' % big_model_hour] / a[
            'diff_%sZ_14-28_last-Prev2_CFS' % big_model_hour].std()) + \
                               1 * (a['diff_0Z_0-16_last-Prev2_GEFSL'] / a['diff_0Z_0-16_last-Prev2_GEFSL'].std()) + \
                               1 * (a['diff_0Z_0-8_last-Prev2_GFSCO'] / a['diff_0Z_0-8_last-Prev2_GFSCO'].std())  # + \
        a['GEFSLCO_pred1'] = 1 * (a['diff_0Z_0-16_last-Prev1_GEFSL'] / a['diff_0Z_0-16_last-Prev1_GEFSL'].std()) + \
                             1 * (a['diff_0Z_0-16_last-Prev2_GEFSL'] / a['diff_0Z_0-16_last-Prev2_GEFSL'].std()) + \
                             0.5 * (a['diff_0Z_0-8_Prev1-Prev2_GFSCO'] / a['diff_0Z_0-8_Prev1-Prev2_GFSCO'].std())+ \
                             0.5 * (a['diff_0Z_0-8_last-Prev1_GFSCO'] / a['diff_0Z_0-8_last-Prev1_GFSCO'].std())
        # 1 * (a['diff_%sZ_0-8_Prev1-Prev2_EC' % big_model_hour] / a['diff_%sZ_0-8_Prev1-Prev2_EC' % big_model_hour].std()) + \
        # 1 * (a['diff_0Z_0-8_last-Prev4_EC'%big_model_hour] / a['diff_0Z_0-8_last-Prev4_EC'%big_model_hour].std()) #+\
        # 1 * (a['diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour] / a['diff_%sZ_0-16_last-Prev2_PARA' % big_model_hour].std()) + \
        a['EPSpost_pred2.2'] = a['EPSpost_pred2'] / a['EPSpost_pred2'].std() + a['GEFSLCO_pred1'] / a['GEFSLCO_pred1'].std()
        a['EPSpost_pred2b'] = 3 * a['EPSpost_pred2'] / a['EPSpost_pred2'].std() + a[
            'diff_%sZ_8-16_last-Prev2_GEPS' % big_model_hour] / a['diff_%sZ_8-16_last-Prev2_GEPS' % big_model_hour].std()
        a['EPSpost_pred2.1b'] = 4*_normalize_series(a['EPSpost_pred2.1']) +\
                                _normalize_series(a['diff_%sZ_8-16_last-Prev2_GEPS' % big_model_hour])
        a['EPSpost_pred2.2b'] = a['EPSpost_pred2b'] + a['GEFSLCO_pred1'] / a['GEFSLCO_pred1'].std()

        if prev_big_model == 0:
            a['Cash_pred1'] = a['diff_0Z_0-2_last-Prev2_EPS'] + a['diff_0Z_0-2_last-Prev4_EPS'] + a['diff_0Z_0-2_last-Prev2_EC'] + a['diff_0Z_0-2_last-Prev4_EC']
            a['Cash_pred2'] = 3 * a['diff_0Z_0-2_last-Prev2_EPS'] + a['diff_0Z_0-2_last-Prev4_EPS'] + 1.5 * a['diff_0Z_0-2_last-Prev2_EC'] + a['diff_0Z_0-2_last-Prev4_EC']
            a['Cash_pred3'] = 3 * a['diff_0Z_0-2_last-Prev2_EPS'] + a['diff_0Z_0-2_last-Prev4_EPS'] + 1.5 * a['diff_0Z_0-2_last-Prev2_EC'] + 1.5*a['diff_0Z_0-2_last-Prev1_EPS'] + a['diff_0Z_0-2_last-Prev1_EC']
            a['Cash_american_pred4'] = 3 * a['diff_0Z_0-2_last-Prev2_EPS'] + a['diff_0Z_0-2_last-Prev4_EPS'] + 1.5 * a['diff_0Z_0-2_last-Prev2_EC'] + 1.5 * a['diff_0Z_0-2_last-Prev1_EPS'] + a['diff_0Z_0-2_last-Prev1_EC']
            a['Cash_hybrid_pred1'] = 0.6*_drop_some_weekdays(a['date'],a['diff_0Z_0-2_last-Prev2_EPS'],weekdays_to_drop=[1,3,4]) + \
                                     0.2*_drop_some_weekdays(a['date'], a['diff_0Z_0-2_last-Prev2_EC'],weekdays_to_drop=[1, 3, 4])+ \
                                     0.2 * _drop_some_weekdays(a['date'], a['diff_0Z_0-2_last-Prev4_EPS'],weekdays_to_drop=[1, 3, 4]) + \
                                     0.6 * _drop_some_weekdays(a['date'], a['diff_0Z_0-2_last-Prev4_EPS'],weekdays_to_drop=[0,1,2,3]) + \
                                     0.2 * _drop_some_weekdays(a['date'], a['diff_0Z_0-2_last-Prev4_EC'],weekdays_to_drop=[0,1,2,3]) + \
                                     0.2 * _drop_some_weekdays(a['date'], a['diff_0Z_0-2_last-Prev2_EPS'],weekdays_to_drop=[0,1,2,3])

            a['EPS45_pred1'] = 2 * a['diff_0Z_14-21_last-Prev1_EPS45'] + 2 * a['diff_0Z_14-21_last-Prev2_EPS45'] + \
                               2 * a['diff_0Z_14-28_last-Prev1_EPS45'] + 2 * a['diff_0Z_14-28_last-Prev2_EPS45'] + \
                               1 * a['diff_0Z_28-42_last-Prev1_EPS45'] + 1 * a['diff_0Z_28-42_last-Prev2_EPS45']
            a['EPS45_pred2'] = 2 * a['diff_0Z_14-21_last-Prev1_EPS45'] + 2 * a['diff_0Z_14-21_last-Prev2_EPS45'] + \
                               a['diff_0Z_21-28_last-Prev1_EPS45']
            a['EPS45_rolling_diff3'] = a[['diff_0Z_14-28_last-Prev1_EPS45', 'diff_0Z_14-28_last-Prev2_EPS45',
                 'diff_0Z_14-28_last-Prev3_EPS45']].mean(axis=1)
            a['EPS45_pred1'] = a['EPS45_pred1'].fillna(0)
            a['EPS45_pred2'] = a['EPS45_pred2'].fillna(0)

            a['EPS45_pred2Fr'] = a['EPS45_pred2'].copy()
            a['EPS45_pred2Fr'][a['date'].dt.weekday != 4] = 0
            # a['EPS_pred3'] = 2*(a['EPSpost_pred2'] / a['EPSpost_pred2'].std()) + (a['macd_sign_30M_trend8'] / a['macd_sign_30M_trend8'].std())
            a['EPSpost_pred3'] = (a['EPS45_pred1'] / a['EPS45_pred1'].std()) + 6 * (
                        a['EPSpost_pred2'] / a['EPSpost_pred2'].std())
            # add macd_Sign_30M + 4H location
            a['EPSpost_pred3'] = a['EPSpost_pred3'].fillna(7 * (a['EPSpost_pred2'] / a['EPSpost_pred2'].std()))
            if big_model_hour == 0:
                a['EPS_PM_pred1'] = a['diff_0Z_0-13_last-Prev2_EPS'] / a['diff_0Z_0-13_last-Prev2_EPS'].std() + \
                                    a['diff_0Z_0-13_Prev1-Prev2_EPS'] / a['diff_0Z_0-13_Prev1-Prev2_EPS'].std() + \
                                    a[get_daily_feature_name_by_version('diff_0Z_last-D2_EPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_EPS',version=suffix,res=None)].std()
            else:
                a['EPS_PM_pred1'] = np.nan
        if secondary_model_hour == 6 and big_model_hour == 0:
            ### Old PMs
            a['PARA_PM_pred0'] = a['diff_0Z_0-16_last-Prev4_PARA'] / a['diff_0Z_0-16_last-Prev4_PARA'].std() + \
                                 a['diff_0Z_0-16_last-Prev2_PARA'] / a['diff_0Z_0-16_last-Prev2_PARA'].std() + \
                                 a['diff_6Z_0-16_last-Prev2_PARA'] / a['diff_6Z_0-16_last-Prev2_PARA'].std() + \
                                 a['diff_6Z_0-16_last-Prev1_PARA'] / a['diff_6Z_0-16_last-Prev1_PARA'].std()
            a['GEFS_PM_pred0'] = _normalize_series(
                a['diff_0Z_0-16_last-Prev2_GEFS'] / a['diff_0Z_0-16_last-Prev2_GEFS'].std() + \
                a['diff_6Z_0-16_last-Prev2_GEFS'] / a['diff_6Z_0-16_last-Prev2_GEFS'].std() + \
                a['diff_6Z_0-16_last-Prev1_GEFS'] / a['diff_6Z_0-16_last-Prev1_GEFS'].std())
            a['PARACO_PM_pred0'] = (a['diff_6Z_8-16_last-Prev1_PARACO'] / a['diff_6Z_8-16_last-Prev1_PARACO'].std() +
                                    a['diff_0Z_8-16_last-Prev2_PARACO'] / a['diff_0Z_8-16_last-Prev2_PARACO'].std()).fillna(0)

            # these refer mainly to 1415-1745 (midweek! 0,4 We look Dailies)
            a['PARA_PM_pred1'] =  _normalize_series(
                                    0.75 * _normalize_series(a['diff_0Z_0-16_last-Prev1_PARA']) +
                                    1*_normalize_series(a['diff_0Z_0-16_last-Prev4_PARA'])+
                                    1*_normalize_series(a['diff_0Z_0-16_last-Prev3_PARA']) +
                                    2 * _normalize_series(a['diff_0Z_0-16_last-Prev2_PARA']) +
                                    _normalize_series(a['diff_6Z_0-16_last-Prev4_PARA'])+
                                    0.75*_normalize_series(a['diff_6Z_0-16_last-Prev2_PARA']) +
                                   2*_normalize_series(a['diff_6Z_0-16_last-Prev3_PARA'])
                                            )
            a['GEFS_PM_pred1'] = _normalize_series(
                                0.75*_normalize_series(a['diff_0Z_8-16_last-Prev2_GEFS'])+
                                 1* _normalize_series(a['diff_6Z_8-16_last-Prev2_GEFS'])+
                                 1.5* _normalize_series(a['diff_6Z_8-16_last-Prev1_GEFS'])+
                                1.5 * _normalize_series(a['diff_6Z_8-16_last-Prev3_GEFS']))

            a['PARACO_PM_pred1'] = _normalize_series(
                                            1 * _normalize_series(a['diff_0Z_0-16_last-Prev1_PARACO']) +
                                            1 * _normalize_series(a['diff_0Z_0-16_last-Prev4_PARACO']) +
                                            2 * _normalize_series(a['diff_0Z_0-16_last-Prev2_PARACO'])+
                                            0.5*_normalize_series(a['diff_6Z_0-16_last-Prev1_PARACO']) +
                                            0.5 *_normalize_series(a['diff_6Z_0-16_last-Prev2_PARACO']) +
                                            2 * _normalize_series(a['diff_6Z_0-16_last-Prev4_PARACO'])).fillna(0)
            # these refer to 1130-1245 (mianly days 1,2,3)
            a['GEFS_6Z_pred1'] = _normalize_series(0.75*_normalize_series(a['diff_0Z_0-16_last-Prev3_GEFS']) +
                                                   0.5 * _normalize_series(a['diff_6Z_0-16_last-Prev1_GEFS']) +
                                                   0.5 * _normalize_series(a['diff_6Z_0-16_last-Prev2_GEFS']) +
                                                   0.75 * _normalize_series(a['diff_6Z_0-16_last-Prev3_GEFS']) +
                                                   1.5 * _normalize_series(a['diff_6Z_0-16_last-Prev4_GEFS'])
                                                   )
            a['PARA_6Z_pred1'] = _normalize_series(
                                                   0.4 * _normalize_series(a['diff_0Z_0-16_last-Prev2_PARA']) +
                                                    0.3 * _normalize_series(a['diff_0Z_0-16_last-Prev1_PARA']) +
                                                    1*_normalize_series(a['diff_0Z_0-16_last-Prev3_PARA']) +
                                                    1 * _normalize_series(a['diff_6Z_0-16_last-Prev3_PARA'])+
                                                   2 * _normalize_series(a['diff_6Z_0-16_last-Prev4_PARA'])
                                                    )
            a['PARA_pre6Z_pred1'] = _normalize_series(
                                                 _normalize_series(a['diff_0Z_0-16_last-Prev1_PARA'])+
                                                 0.6*_normalize_series(a['diff_0Z_0-16_last-Prev3_PARA'])+
                                                 +0.6*_normalize_series(a['diff_0Z_0-16_last-Prev4_PARA'])
                                                    )
            a['PARA_pre6Z_pred2'] = _normalize_series(
                _normalize_series(a['diff_0Z_8-16_last-Prev2_PARA']) +
                 _normalize_series(a['diff_0Z_8-16_Prev1-Prev3_PARA']) +
                _normalize_series(a['diff_0Z_8-16_Prev1-Prev2_PARA']))
            a['GEFS_pre6Z_pred2'] = _normalize_series(
                _normalize_series(a['diff_0Z_8-16_last-Prev2_GEFS']) +
                _normalize_series(a['diff_0Z_8-16_Prev1-Prev3_GEFS']) +
                _normalize_series(a['diff_0Z_8-16_Prev1-Prev2_GEFS']))

            a['PARACO_6Z_pred1'] =  _normalize_series(
                                                    1*_normalize_series(a['diff_0Z_0-16_last-Prev1_PARACO']) +
                                                    1*_normalize_series(a['diff_6Z_0-16_last-Prev1_PARACO'])+
                                                    1 * _normalize_series(a['diff_6Z_0-16_last-Prev2_PARACO']) +
                                                    1 * _normalize_series(a['diff_6Z_0-16_last-Prev3_PARACO']) +
                                                    2*_normalize_series(a['diff_6Z_0-16_last-Prev4_PARACO'])
                                                    )
            a['GFSEFS_6Z_pred1'] = _normalize_series(a['GEFS_6Z_pred1']+a['PARA_6Z_pred1']+0.5*a['PARACO_6Z_pred1'])
            preds_6z_basic = ['diff_6Z_0-16_last-Prev1_PARACO', 'diff_6Z_0-16_last-Prev1_PARA',
                                'diff_6Z_0-16_last-Prev4_PARACO', 'diff_6Z_0-16_last-Prev4_PARA',
                                    'diff_6Z_0-16_last-Prev3_PARACO', 'diff_6Z_0-16_last-Prev3_PARA',
                                        'diff_6Z_0-16_last-Prev4_GEFS']
            a['GFSEFS_6Z_pred2_basic'] = _normalize_series((a[preds_6z_basic] * pd.Series([1,1,1,1,1,1,3],index=preds_6z_basic)).mean(axis=1))
            a['GFSEFS_6Z_pred3_basic'] = _normalize_series((a[preds_6z_basic] * pd.Series([1.5, 1.5, 1, 1, 0, 0, 2.5], index=preds_6z_basic)).mean(axis=1))
            a['PARA_PM_pred2D'] = a[get_daily_feature_name_by_version('diff_0Z_last-D2_PARA',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_PARA',version=suffix,res=None)].std() + \
                                  a[get_daily_feature_name_by_version('diff_0Z_last-D3_PARA',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D3_PARA',version=suffix,res=None)].std()
            a['PARACO_PM_pred2D'] = (a[get_daily_feature_name_by_version('diff_0Z_last-D2_PARACO',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_PARACO',version=suffix,res=None)].std() +
                                    a[get_daily_feature_name_by_version('diff_0Z_last-D3_PARACO',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D3_PARACO',version=suffix,res=None)].std()).fillna(0)
            a['GEFS_PM_pred1D'] = _normalize_series(2* _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEFS',version=suffix,res=None)]) + \
                                  _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEFS',version=suffix,res=None)]) +\
                                  _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D3_GEFS',version=suffix,res=None)]))
            a['GEFSL_PM_pred1D'] = _normalize_series(2 * _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D1_GEFSL',version=suffix,res=None)]) +
                                                    _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEFSL',version=suffix,res=None)]) +
                                                    _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D3_GEFSL',version=suffix,res=None)])+
                                                0.5*_normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D1_GFSCO',version=suffix,res=None)]) +
                                                0.5 * _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_GFSCO',version=suffix,res=None)]))
            a['GEMCO_PM_pred1'] = _normalize_series(
                                        1 * _normalize_series(a['diff_0Z_0-16_last-Prev1_GEMCO']) +
                                        1 * _normalize_series(a['diff_0Z_0-16_Prev1-Prev2_GEMCO']) +
                                        1 * _normalize_series(a['diff_0Z_0-16_last-Prev4_GEMCO']) +
                                        1 * _normalize_series(a['diff_0Z_0-16_last-Prev2_GEMCO'])
                                            )
            a['GEMCO_PM_pred2'] = _normalize_series(
                                2 * _normalize_series(a['diff_0Z_0-16_last-Prev1_GEMCO']) +
                                1 * _normalize_series(a['diff_0Z_0-16_last-Prev2_GEMCO'])
                                )
            a['GEPS_PM_pred1'] = _normalize_series(
                1 * _normalize_series(a['diff_0Z_0-16_last-Prev1_GEPS']) +
                1 * _normalize_series(a['diff_0Z_0-16_Prev1-Prev2_GEPS']) +
                1 * _normalize_series(a['diff_0Z_0-16_last-Prev4_GEPS']) +
                1 * _normalize_series(a['diff_0Z_0-16_last-Prev2_GEPS'])
                                                )

            a['GEPS_PM_pred1D'] = a[get_daily_feature_name_by_version('diff_0Z_last-D1_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D1_GEPS',version=suffix,res=None)].std() + \
                                  a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)].std() + \
                                  a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)].std()
            a['GEPS_PM_pred2D'] = _normalize_series(2* a[get_daily_feature_name_by_version('diff_0Z_last-D1_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D1_GEPS',version=suffix,res=None)].std() + \
                                  a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)].std() + \
                                  a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)] / a[get_daily_feature_name_by_version('diff_0Z_last-D2_GEPS',version=suffix,res=None)].std())
            a['EPS_PM_pred1D'] = _normalize_series(_normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D1_EPS',version=suffix,res=None)]) +\
                                  _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_EPS',version=suffix,res=None)]) +\
                                  _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D3_EPS',version=suffix,res=None)]))
            a['EPS_PM_pred2D'] = _normalize_series(2 * _normalize_series(a['diff_0Z_0-13_Prev1-Prev2_EPS']) +\
                                _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D1_EPS',version=suffix,res=None)]) + \
                                 0.5 * _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D2_EPS',version=suffix,res=None)]) + \
                                 0.5 * _normalize_series(a[get_daily_feature_name_by_version('diff_0Z_last-D3_EPS',version=suffix,res=None)]))

            a['EPS_daily_hybrid_pred1'] = _normalize_series(0.75*_drop_some_weekdays(a['date'],_normalize_series(a['diff_0Z_0-13_last-Prev3D_EPS']),weekdays_to_drop=[1,2,3,4]) + # monday
                                                            0.25*_drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev1D_EPS']),weekdays_to_drop=[1, 2, 3, 4]) +
                                                            # tuesday
                                                            0.75 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev1D_EPS']), weekdays_to_drop=[0,2,3,4])+
                                                            0.25 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev3D_EPS']), weekdays_to_drop=[0, 2, 3, 4])+
                                                            # wednesday
                                                            0.33 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev1D_EPS']), weekdays_to_drop=[0,1, 3, 4]) +
                                                            0.33 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev2D_EPS']), weekdays_to_drop=[0, 1, 3, 4])+
                                                            0.33 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev4D_EPS']), weekdays_to_drop=[0, 1, 3, 4])+
                                                            # Thur
                                                            0.5 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev1D_EPS']),weekdays_to_drop=[0,1,2,4]) +
                                                            0.5 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_Prev1-Prev2_EPS']),weekdays_to_drop=[0,1,2,4]) +
                                                            # Fri
                                                            0.5 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_last-Prev2D_EPS']), weekdays_to_drop=[0,1,2,3]) +
                                                            0.5 * _drop_some_weekdays(a['date'], _normalize_series(a['diff_0Z_0-13_Prev1-Prev2_EPS']), weekdays_to_drop=[0,1,2,3])
                                                            )
            a['EPS_daily_hybrid_pred2'] = _drop_some_weekdays(a['date'], _normalize_series(a['EPSpost_pred2b']), weekdays_to_drop=[3,4])+\
                                                _drop_some_weekdays(a['date'], _normalize_series(a['EPS_daily_hybrid_pred1']), weekdays_to_drop=[1, 2])
            a['EPS_daily_hybrid_pred2.2'] = a['EPS_daily_hybrid_pred1']+a['EPSpost_pred2.2']

            # hybrid dailies (other than EPS)
            for tmp_model in ['PARA','PARACO','GEFS','GFSCO','GEFSL','GEPS']:
                a['%s_daily_hybrid_pred1'%tmp_model] = a['diff_0Z_0-16_last-Prev4D_%s'%tmp_model] +\
                                             0.5*a['diff_0Z_0-16_last-Prev3D_%s'%tmp_model]+ \
                                                0.25 * a['diff_0Z_0-16_last-Prev2D_%s' % tmp_model]
                a['%s_daily_hybrid_pred2' % tmp_model] = a['diff_0Z_0-16_last-Prev4D_%s' % tmp_model] + \
                                                     0.3 * a['diff_0Z_0-16_last-Prev3D_%s' % tmp_model]
                a['%s_daily_hybrid_8-16_pred2' % tmp_model] = a['diff_0Z_8-16_last-Prev4D_%s' % tmp_model] + \
                                                         0.3 * a['diff_0Z_8-16_last-Prev3D_%s' % tmp_model]
                a['%s_daily_hybrid_pred3' % tmp_model] = a['diff_0Z_0-16_last-Prev4D_%s' % tmp_model] + \
                                                     0.3 * a['diff_0Z_0-16_last-Prev3D_%s' % tmp_model]+\
                                                    0.3 * a['diff_0Z_0-16_last-Prev2D_%s' % tmp_model]+ \
                                                     0.4 * a['diff_0Z_0-16_last-Prev1D_%s' % tmp_model]
            for tmp_model in ['PARA', 'PARACO', 'GEFS']:
                a['%s_daily_hybrid_6Z_pred2' % tmp_model] = a['diff_6Z_0-16_last-Prev4D_%s' % tmp_model] + \
                                                     0.3 * a['diff_6Z_0-16_last-Prev3D_%s' % tmp_model]
                a['%s_daily_hybrid_6Z_pred3' % tmp_model] = a['diff_6Z_0-16_last-Prev4D_%s' % tmp_model] + \
                                                         0.3 * a['diff_6Z_0-16_last-Prev3D_%s' % tmp_model] + \
                                                         0.3 * a['diff_6Z_0-16_last-Prev2D_%s' % tmp_model] + \
                                                         0.4 * a['diff_6Z_0-16_last-Prev1D_%s' % tmp_model]
            tmp_model = 'EPS'
            a['%s_daily_hybrid_pred1b' %tmp_model] = a['diff_0Z_0-13_last-Prev4D_%s' % tmp_model] + \
                                                 0.5 * a['diff_0Z_0-13_last-Prev3D_%s' % tmp_model] + \
                                                 0.25 * a['diff_0Z_0-13_last-Prev2D_%s' % tmp_model]
            a['%s_daily_hybrid8to13_pred1b' % tmp_model] = a['diff_0Z_8-13_last-Prev4D_%s' % tmp_model] + \
                                                      0.5 * a['diff_0Z_8-13_last-Prev3D_%s' % tmp_model] + \
                                                      0.25 * a['diff_0Z_8-13_last-Prev2D_%s' % tmp_model]
            a['%s_daily_hybrid_pred2b' % tmp_model] = a['diff_0Z_0-13_last-Prev4D_%s' % tmp_model] + \
                                                 0.3 * a['diff_0Z_0-13_last-Prev3D_%s' % tmp_model]
            a['%s_daily_hybrid_pred3b' % tmp_model] = a['diff_0Z_0-13_last-Prev4D_%s' % tmp_model] + \
                                                 0.3 * a['diff_0Z_0-13_last-Prev3D_%s' % tmp_model] + \
                                                 0.3 * a['diff_0Z_0-13_last-Prev2D_%s' % tmp_model] + \
                                                 0.4 * a['diff_0Z_0-13_last-Prev1D_%s' % tmp_model]
            tmp_model = 'EC'
            a['EC_daily_hybrid_pred1'] =  0.3 * a['diff_0Z_0-8_last-Prev3D_%s' % tmp_model] + \
                                            0.3 * a['diff_0Z_0-8_last-Prev2D_%s' % tmp_model] + \
                                                0.2 * a['diff_0Z_0-8_last-Prev1D_%s' % tmp_model]
            a['CFS_daily_hybrid_pred1b'] = a['diff_0Z_14-28_last-Prev4D_CFS'] + \
                                                 0.5 * a['diff_0Z_14-28_last-Prev3D_CFS'] + \
                                                 0.25 * a['diff_0Z_14-28_last-Prev2D_CFS']

            a['GEPS_AM_basic_pred1'] = _normalize_series(0.75*a['diff_0Z_0-16_last-Prev1_GEPS']+0.25*a['diff_0Z_0-16_last-Prev2_GEPS'])
            a['GEFS_AM_basic_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-16_last-Prev2_GEFS'] + 0.25 * a['diff_0Z_0-16_last-Prev4_GEFS'])
            a['PARACO_AM_basic_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-16_last-Prev2_PARACO'] + 0.25 * a['diff_0Z_0-16_last-Prev4_PARACO'])
            a['PARACO_AM_8-16_pred2'] = _normalize_series(0.75 * a['diff_0Z_8-16_last-Prev2_PARACO'] + 0.25 * a['diff_0Z_8-16_last-Prev4_PARACO']+0.25*a['diff_0Z_8-16_last-Prev1_PARACO'])
            a['PARACO_AM_pred3'] = a['diff_0Z_0-16_last-Prev2_PARACO'] + a['diff_0Z_0-16_Prev1-Prev3_PARACO']
            a['PARA_AM_basic_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-16_last-Prev2_PARA'] + 0.25 * a['diff_0Z_0-16_last-Prev4_PARA'])
            a['GEFS_AM_0-8_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-8_last-Prev2_GEFS'] + 0.5 * a['diff_0Z_0-8_last-Prev1_GEFS']+ 0.25 * a['diff_0Z_0-8_last-Prev4_GEFS'])
            a['PARACO_AM_0-8_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-8_last-Prev2_PARACO'] + 0.5 * a['diff_0Z_0-8_last-Prev1_PARACO'] + 0.25 * a['diff_0Z_0-8_last-Prev4_PARACO'])
            a['PARA_AM_0-8_pred1'] = _normalize_series(0.75 * a['diff_0Z_0-8_last-Prev2_PARA']+ 0.5 * a['diff_0Z_0-8_last-Prev1_PARA'] + 0.25 * a['diff_0Z_0-8_last-Prev4_PARA'])
            a['mix_0600-0700_pred1'] = _normalize_series(a['diff_0Z_0-16_last-Prev2_GEPS']+a['diff_0Z_0-16_last-Prev4_GEFS']+
                                                         0.33*a['diff_0Z_0-16_last-Prev4_PARACO']+0.33*a['diff_0Z_0-16_last-Prev2_PARA']+0.25*a['diff_0Z_0-10_last-Prev2_GEM']
                                                         )
            a['mix_0600-0700_pred2'] = _normalize_series(
                        a['diff_0Z_0-13_Prev1-Prev2_EPS']+ a['diff_0Z_0-16_last-Prev2_GEPS'] + a['diff_0Z_0-16_last-Prev4_GEFS'] +
                                0.33 * a['diff_0Z_0-16_last-Prev4_PARACO'] + 0.33 * a['diff_0Z_0-16_last-Prev2_PARA'] + 0.25 * a['diff_0Z_0-10_last-Prev2_GEM'])
            a['EPSpost_pred3b'] = _normalize_series(_normalize_series(a['EPSpost_pred2b'])+
                                  0.15* _normalize_series(a['GEPS_AM_basic_pred1'])+
                                  0.15*_normalize_series(a['GEFS_AM_basic_pred1'])+
                                  0.15* _normalize_series(a['PARACO_AM_basic_pred1']))
            try:
                a['y_tag_GFS_hack2_v1b'] = a['y_tag_GFS_hack2_v1'].fillna(0)
                a['y_tag_GFS_hack2_v1b'] = _normalize_series(a['y_tag_GFS_hack2_v1b'])
            except:
                pass
            if strategy not in ['0800-1100','0745-1100']:
                try:
                    a['diff_6Z_PARAACO-GEFS_b'] = a['diff_6Z_PARAACO-GEFS_x'].fillna(0)
                except:
                    a['diff_6Z_PARAACO-GEFS_b'] = a['diff_6Z_PARAACO-GEFS'].fillna(0)
            else:
                a['diff_6Z_PARAACO-GEFS_b'] = 0

            try:
                a['y_tag_GEFS_v1b'] = a['y_tag_GEFS_v1'].fillna(0)
                a['y_tag_GEFS_v1b'] = _normalize_series(a['y_tag_GEFS_v1b'])

                a['y_tag_GFS_hackFr'] = _normalize_series(a['y_tag_GFS_hack2_v1b'])
                a['y_tag_GFS_hackFr'][a['time'].dt.weekday != 4] = 0
                a['y_tag_GEFS_v1Fr'] = _normalize_series(a['y_tag_GEFS_v1b'])
                a['y_tag_GEFS_v1Fr'][a['time'].dt.weekday != 4] = 0
            except:
                print('No y_tag bollingers... skipping')
                a['y_tag_GEFS_v1b'] = np.nan
                a['y_tag_GEFS_v1Fr'] = np.nan
                a['y_tag_GFS_hackFr'] = np.nan
            a['Avg_PM_pred3D'] = 0.6 * a['GEFS_PM_pred1D'] + a['PARACO_PM_pred2D'] + a['PARA_PM_pred2D']
            a['Avg_PM_pred4D'] = 0.6 * a['GEFS_PM_pred1D'] + a['PARACO_PM_pred2D'] + a['PARA_PM_pred2D'] + 2 * a[
                'GEPS_PM_pred1D'] #+ a['EPS_PM_pred2D'] # used to be 0.6
            a['Avg_PM_Daily_pred1'] = a['GEFS_PM_pred1D'] + 0.5*a['PARACO_PM_pred2D'] + 0.5*a['PARA_PM_pred2D'] +\
                                      a['GEPS_PM_pred1D'] + a['EPS_PM_pred2D'] + a['GEFSL_PM_pred1D'] # used to be 0.6
            r = a[(a['date'] >= start) & (a['date'] <= end)]['EPS45_pred2Fr'].isna().mean()
            a['Avg_PM_pred5D_hybrid'] = 4 * _normalize_series(a['Avg_PM_pred4D']) + \
                                        _normalize_series(a['diff_6Z_PARAACO-GEFS_b']) + \
                                         _normalize_series(a['EPS45_pred2Fr'])
            a['Avg_PM_pred6D_hybrid'] = 4 * _normalize_series(a['Avg_PM_pred4D'])+\
                                        _normalize_series(a['diff_6Z_PARAACO-GEFS_b'])+\
                                        _normalize_series(a['EPS45_pred2'])+\
                                        2*_normalize_series(a['GEFSLCO_pred1'])

            a['GEFS_PM_pred2'] = _normalize_series(a['GEFS_PM_pred1']) + _normalize_series(a['PARACO_PM_pred1'])
            a['GFSEFS_PM_pred0'] = 2 * _normalize_series(a['GEFS_PM_pred0']) + 3 * _normalize_series(a['PARA_PM_pred0'])
            a['GFSEFS_PM_pred1'] = 2 * _normalize_series(a['GEFS_PM_pred1']) + 3 * _normalize_series(a['PARA_PM_pred1'])
            a['GFSEFS_PM_pred2'] = _normalize_series(_normalize_series(a['GEFS_PM_pred1']) +
                                                     _normalize_series(a['PARA_PM_pred1']) +
                                                    0.5*_normalize_series(a['PARACO_PM_pred1']))
            # combine american with EPS
            a['PM_hybrid_pred1'] = np.nan
            a['Avg_PM_pred5D_hybrid'] = _normalize_series(a['Avg_PM_pred5D_hybrid'])
            a['EPS_PM_pred1'] = _normalize_series(a['EPS_PM_pred1'])
            a['GFSEFS_PM_pred1'] = _normalize_series(a['GFSEFS_PM_pred1'])

            cond_34 = a['date'].dt.weekday.isin([3, 4])
            cond_0 = a['date'].dt.weekday.isin([0])
            cond_4 = a['date'].dt.weekday.isin([4])
            cond_12 = a['date'].dt.weekday.isin([1, 2])


            a['PM_hybrid_pred1'][cond_0] = _normalize_series(a['Avg_PM_pred5D_hybrid'][cond_0])
            a['PM_basic_pred1'] = _normalize_series(2 * _normalize_series(a['EPS_PM_pred1']) + _normalize_series(a['GFSEFS_PM_pred1']) + _normalize_series(a['diff_0Z_0-16_last-Prev2_GEFSL']))
            try:
                a['PM_hybrid_pred1'][cond_34] = _normalize_series(a['Avg_PM_pred5D_hybrid'][cond_34] + 0.25 * a['EPS_PM_pred1'][cond_34] + 0.25 * \
                                                a['y_tag_GFS_hack2_v1b'][cond_34])

                a['PM_hybrid_pred1'][cond_12] = _normalize_series(0.5 * a['y_tag_GEFS_v1b'][cond_12] + a['y_tag_GFS_hack2_v1b'][cond_12] + \
                                                a['EPS_PM_pred1'][cond_12])
                a['PM_hybrid_pred2'] = np.nan
                a['PM_hybrid_pred2'][cond_34] = _normalize_series(a['Avg_PM_pred5D_hybrid'][cond_34] + 0.25 * a['EPS_PM_pred1'][cond_34] + 0.25 * \
                                                0.5*a['y_tag_GFS_hack2_v1b'][cond_34] + 0.5 * a['GFSEFS_PM_pred1'])

                a['PM_hybrid_pred2'][cond_12] = _normalize_series(0.5 * a['y_tag_GEFS_v1b'][cond_12] + 0.5* a['y_tag_GFS_hack2_v1b'][cond_12] + \
                                                0.5 * a['GFSEFS_PM_pred1'][cond_12]+ a['EPS_PM_pred1'][cond_12])
                a['PM_hybrid_pred2'][cond_0] = _normalize_series(a['Avg_PM_pred5D_hybrid'][cond_0])
            except:
                a['PM_hybrid_pred2'] = np.nan
                a['PM_hybrid_pred1'] = np.nan
                print ('Skipping y_tags')
        elif secondary_model_hour == 18:
            a['GEFS_AM_pred1'] = 3 * a['diff_18Z_8-16_last-Prev1_GEFS'] + 2 * a['diff_18Z_8-16_last-Prev2_GEFS'] + a[
                'diff_18Z_8-16_last-Prev4_GEFS']
            a['PARA_AM_pred1'] = 2 * a['diff_18Z_0-13_last-Prev1_PARA'] + 3 * a['diff_18Z_0-13_last-Prev4_PARA'] + 2 * a[
                'diff_18Z_0-13_last-Prev2_PARA']
            paraco_days = '8-16'
            a['PARACO_AM_pred1'] = 2 * a['diff_18Z_%s_last-Prev1_PARACO' % paraco_days] + 1 * a[
                'diff_18Z_%s_last-Prev2_PARACO' % paraco_days] + 2 * a['diff_18Z_%s_last-Prev4_PARACO' % paraco_days]
            a['GFSEFS_AM_pred1'] = a['GEFS_AM_pred1'] + a['PARA_AM_pred1'] + a['PARACO_AM_pred1']
            if big_model_hour == 0:
                a['GFSEFS_AM_pred2'] = a['GFSEFS_AM_pred1'] + a['diff_0Z_0-16_Prev1-Prev2_GEPS']
        else:
            for c in ['GFS_Super_PM_pred1','GFS_Super_PM_pred2','Early_PM_midweek','GFSEFS_PM_pred1','GEPS_PM_pred1D']:
                a[c] = np.nan
        if prev_big_model == 0:
            a['GEFSL_PM_pred1_Prev1'] = _normalize_series(1.5*_normalize_series(a['diff_0Z_8-16_last-Prev1_GEFSL']) +
                                                          0.6 * _normalize_series(a['diff_0Z_0-8_last-Prev1_GFSCO']) +
                                                          0.3 * _normalize_series(a['diff_0Z_8-16_last-Prev1_GFSCO']))
            a['GEFSL_PM_pred1_Prev2'] = _normalize_series(1.5*_normalize_series(a['diff_0Z_8-16_last-Prev2_GEFSL']) +
                                    0.6 * _normalize_series(a['diff_0Z_0-8_last-Prev2_GFSCO'])+
                                        0.3 * _normalize_series(a['diff_0Z_8-16_last-Prev2_GFSCO']))
            a['GEFSL_PM_pred1_Prev4'] = _normalize_series(1.5*_normalize_series(a['diff_0Z_8-16_last-Prev4_GEFSL']) +
                                                          0.6 * _normalize_series(a['diff_0Z_0-8_last-Prev4_GFSCO']) +
                                                          0.3 * _normalize_series(a['diff_0Z_8-16_last-Prev4_GFSCO']))
            a['EPSL_daily_hybrid_pred3'] = _drop_some_weekdays(a['date'], _normalize_series(a['EPS_daily_hybrid_pred2']),
                                                              weekdays_to_drop=[]) + \
                                          0.5*_drop_some_weekdays(a['date'], _normalize_series(a['GEFSL_PM_pred1_Prev4']),
                                                              weekdays_to_drop=[])
            a['EPSL_daily_hybrid_pred4'] = _drop_some_weekdays(a['date'], _normalize_series(a['EPS_daily_hybrid_pred2']),
                                                               weekdays_to_drop=[]) + \
                                           0.5*_drop_some_weekdays(a['date'], _normalize_series(a['GEFSL_PM_pred1_Prev4']),
                                                               weekdays_to_drop=[1,2])
            a['EPSL_daily_hybrid_pred4.2'] = _drop_some_weekdays(a['date'],_normalize_series(a['EPS_daily_hybrid_pred2.2']),
                                                               weekdays_to_drop=[]) + \
                                           0.5 * _drop_some_weekdays(a['date'],
                                                                     _normalize_series(a['GEFSL_PM_pred1_Prev4']),
                                                                     weekdays_to_drop=[1, 2,3])+ \
                                             0.5 * _drop_some_weekdays(a['date'],
                                                                       _normalize_series(a['GEFSL_PM_pred1_Prev4']),
                                                                       weekdays_to_drop=[0,4])
            a['GEFSL_PM_pred1'] = np.nan
            a['GEFSL_PM_pred1'][a['date'].dt.weekday.isin([1,2,3])] = _normalize_series(1 * a['GEFSL_PM_pred1_Prev1'] +
                                                                                        0.7 * a['GEFSL_PM_pred1_Prev2'] +
                                                                                        0.3 * a['GEFSL_PM_pred1_Prev4'])
            a['GEFSL_PM_pred1'][a['date'].dt.weekday.isin([0,4])] = _normalize_series(0.7 * a['GEFSL_PM_pred1_Prev4'] +
                                                                                    0.3 * a['GEFSL_PM_pred1_Prev2']+
                                                                                      0.3 * a['GEFSL_PM_pred1_Prev1'])
            a['GFS_Super_PM_pred1'] = 0.65 * _normalize_series(a['GEFSL_PM_pred1']) + 0.35 *_normalize_series(a['GFSEFS_PM_pred1'])
            a['GFS_Super_PM_pred2'] = _normalize_series(_normalize_series(a['GFS_Super_PM_pred1']) +
                                                        0.5*_normalize_series(a['GEPS_PM_pred1D']))
            a['EarlyPM_midweek'] = _normalize_series(0.5*_normalize_series(a['diff_6Z_0-13_last-Prev1_PARA'])+
                                   0.25 * _normalize_series(a['diff_6Z_0-16_last-Prev1_PARACO']) +
                                   _normalize_series(a['diff_6Z_0-16_last-Prev1_GEFS']) +
                                   (-1)*_normalize_series(a['diff_0Z_0-16_last-Prev2_GEFS']) +
                                   1*_normalize_series(a['diff_0Z_0-8_last-Prev1_EC']) +
                                   0.5* _normalize_series(a['diff_0Z_0-8_last-Prev2_EC']))

        """__________________________ Add_Combos _______________________________"""
        if 'y_tag_bollinger_v1' not in list(a):
            a['y_tag_bollinger_v1'] = np.nan
        try:
            a['Combo_pred1'] = a['GFS_Super_PM_pred2'] #(a['GFS_Super_PM_pred1']/a['GFS_Super_PM_pred1'].std()) + a['PM_hybrid_pred1']/a['PM_hybrid_pred1'].std()
            #a['PM_hybrid_pred1'] #GFS_Super_PM_pred1']
            no_agreement_inds = np.sign(a['GFS_Super_PM_pred2']*a['Avg_PM_pred3D']) == -1
            a['Combo_pred1'][no_agreement_inds] = 0 #a['PM_pred1']
        except:
            a['Combo_pred1'] = np.nan
        try:
            a['combo_0800'] = ((a['y_tag_bollinger_v1']/a['y_tag_bollinger_v1'].std())+ (a['EPSpost_pred2b'] / a['EPSpost_pred2b'].std())) /2
            a['combo_0800_b'] = ((a['y_tag_bollinger_v1']/a['y_tag_bollinger_v1'].std())+ 2*(a['EPSpost_pred2b'] / a['EPSpost_pred2b'].std())) /3
            a['combo_0800_c'] = ((a['y_tag_bollinger_v1'] / a['y_tag_bollinger_v1'].std()) +
                                2 * (a['EPSpost_pred2b'] / a['EPSpost_pred2b'].std())+
                                 0.5 * (a['diff_0Z_0-8_last-Prev1_GFSCO'] / a['EPSpost_pred2b'].std())+
                                 0.5 * (a['PARA_pre6Z_pred1'] / a['PARA_pre6Z_pred1'].std())) / 4
        except:
            a['combo_0800'] = np.nan
            a['combo_0800_b'] = np.nan
            a['combo_0800_c'] = np.nan
        """_______________________________ CFS ________________________________"""
        try:
            a['CFSpre_6Z_pred1'] = _normalize_series(_normalize_series(a['diff_0Z_0-13_last-Prev3_CFS'])+
                                 _normalize_series(a['diff_0Z_0-13_last-Prev4_CFS'])+
                                 _normalize_series(a['diff_0Z_14-21_last-Prev1_CFS']))
            a['CFSpre_6Z_pred2'] = _normalize_series(a['diff_0Z_14-28_last-Prev1_CFS'])
        except:
            a['CFSpre_6Z_pred1'] = np.nan
            a['CFSpre_6Z_pred2'] = np.nan
        try:
            a['CFSpost_6Z_pred1'] = _normalize_series(0.5*_normalize_series(a['diff_6Z_0-16_last-Prev1_CFS']) +
                                                     _normalize_series(a['diff_6Z_14-21_last-Prev4_CFS']) +
                                                     0.75*_normalize_series(a['diff_6Z_14-21_last-Prev1_CFS'])+
                                                      0.75*_normalize_series(a['diff_6Z_14-28_last-Prev1_CFS']))
        except:
            a['CFSpost_6Z_pred1'] = np.nan
        if suffix in ['v8','v8_12Z','v8_0Zb','v11'] and big_model_hour == 0:
            a['CFS_AM_pred1'] = _normalize_series(_normalize_series(a['diff_0Z_14-21_last-Prev2_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev4_CFS']) +
                                                  0.5*_normalize_series(a['diff_0Z_14-28_last-Prev2_CFS']) +
                                                  0.5*_normalize_series(a['diff_0Z_14-28_last-Prev2_CFS']) +
                                                  0.5*_normalize_series(a['diff_0Z_14-28_Prev1-Prev2_CFS'])+
                                                  0.5 * _normalize_series(a['diff_0Z_14-28_Prev1-Prev3_CFS'])
                                                  )
            a['CFS_AM_Daily_pred1'] = _normalize_series(_normalize_series(a['diff_0Z_14-21_last-Prev1D_CFS']) +
                                                        _normalize_series(a['diff_0Z_14-21_last-Prev2D_CFS']) +
                                                        0.5*_normalize_series(a['diff_0Z_14-21_last-Prev3D_CFS'])+
                                                        0.5 * _normalize_series(a['diff_0Z_14-21_last-Prev4D_CFS'])
                                                        )
            a['CFS_AM_Daily_pred2'] = _normalize_series(_normalize_series(a['diff_0Z_14-21_last-Prev2D_CFS']) +
                                                        _normalize_series(a['diff_0Z_14-21_last-Prev4D_CFS']) +
                                                        _normalize_series(a['diff_0Z_14-28_last-Prev2D_CFS'])+
                                                        _normalize_series(a['diff_0Z_14-28_last-Prev4D_CFS'])
                                                        )
            a['CFS_AM_Daily_hybrid_pred1'] = np.nan
            a['CFS_AM_Daily_hybrid_pred1'][a.date.dt.weekday == 0] = _normalize_series(a['diff_0Z_14-28_last-Prev3D_CFS'])
            a['CFS_AM_Daily_hybrid_pred1'][a.date.dt.weekday.isin([1,2,3])] = _normalize_series(_normalize_series(a['diff_0Z_14-28_last-Prev1D_CFS'])+
                                                                              0.5*_normalize_series(a['diff_0Z_14-28_last-Prev2D_CFS']))
            a['CFS_AM_Daily_hybrid_pred1'][a.date.dt.weekday.isin([4])] = _normalize_series(_normalize_series(a['diff_0Z_14-28_last-Prev2D_CFS']) +
                                                                        0.5*_normalize_series(a['diff_0Z_14-28_last-Prev4D_CFS']))

            a['CFS_PM_Daily_hybrid_pred2'] = np.nan
            a['CFS_PM_Daily_hybrid_pred2'] = _normalize_series(_drop_some_weekdays(a['date'],a['diff_0Z_14-28_last-Prev3D_CFS'],weekdays_to_drop=[1,2,3,4]) +
                                                               _drop_some_weekdays(a['date'],a['diff_0Z_14-28_last-Prev2D_CFS'],weekdays_to_drop=[1,2,3,4]) +
                                                               _drop_some_weekdays(a['date'],a['diff_0Z_14-28_Prev1D-Prev2D_CFS'],weekdays_to_drop=[1, 2, 3, 4])) + \
                                            _normalize_series(_drop_some_weekdays(a['date'],a['diff_0Z_14-28_Prev1D-Prev2D_CFS'],weekdays_to_drop=[0,2,3,4]) +
                                                              0.5*_drop_some_weekdays(a['date'],a['diff_0Z_14-28_last-Prev2D_CFS'],weekdays_to_drop=[0, 2, 3, 4])
                                                              )+\
                                            _normalize_series(_drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev1D_CFS'], weekdays_to_drop=[0,1,4])+
                                                              0.3*_drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev2D_CFS'], weekdays_to_drop=[0,1,4])+
                                                              0.3*_drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev4D_CFS'], weekdays_to_drop=[0,1,4]))+\
                                            _normalize_series(
                                                _drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev1D_CFS'], weekdays_to_drop=[0, 1,2, 3]) +
                                                0.3 * _drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev2D_CFS'], weekdays_to_drop=[0, 1,2, 3,]) +
                                                0.3 * _drop_some_weekdays(a['date'], a['diff_0Z_14-28_last-Prev4D_CFS'], weekdays_to_drop=[0, 1, 2,3])+
                                                _drop_some_weekdays(a['date'], a['diff_0Z_28-42_last-Prev1D_CFS'],weekdays_to_drop=[0, 1, 2, 3]) +
                                                0.3 * _drop_some_weekdays(a['date'], a['diff_0Z_28-42_last-Prev2D_CFS'],weekdays_to_drop=[0, 1, 2, 3]) +
                                                0.3 * _drop_some_weekdays(a['date'], a['diff_0Z_28-42_last-Prev4D_CFS'],weekdays_to_drop=[0, 1, 2, 3]))
            a['CFS_PM_daily_basic_pred1'] = a['diff_0Z_14-28_last-Prev4D_CFS'] + a['diff_0Z_14-28_Prev1D-Prev2D_CFS']
            a['CFS_0Z_week2'] = a['diff_0Z_0-16_last-Prev2_CFS'] + a['diff_0Z_0-16_last-Prev4_CFS']
            a['CFS_6Z_week2'] = a['diff_6Z_0-16_last-Prev1_CFS'] + a['diff_0Z_0-16_last-Prev3_CFS']
            a['CFS_PM_pred1'] = _normalize_series(_normalize_series(a['diff_6Z_14-21_last-Prev4_CFS'])+
                                              _normalize_series(a['diff_0Z_14-21_last-Prev4_CFS']) +
                                              _normalize_series(a['diff_0Z_14-21_last-Prev1D_CFS']) +
                                              0.5*_normalize_series(a['diff_0Z_14-21_last-Prev2D_CFS'])
                                              )
            a['CFS_PM_pred2'] = _normalize_series(_normalize_series(a['diff_6Z_14-21_last-Prev4_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev4_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev1D_CFS']) +
                                                  0.5 * _normalize_series(a['diff_0Z_14-21_last-Prev2D_CFS'])+
                                                        _normalize_series(a['diff_0Z_14-28_last-Prev1D_CFS'])+
                                                        _normalize_series(a['diff_0Z_14-28_last-Prev2D_CFS'])
                                                )
            a['CFS_PM_pred3'] = _normalize_series(_normalize_series(a['diff_6Z_14-21_last-Prev4_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev4_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev1D_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev2D_CFS']) +
                                                  _normalize_series(a['diff_0Z_14-21_last-Prev3D_CFS']))
        else:
            a['CFS_PM_pred1'] = np.nan
            a['CFS_PM_pred2'] = np.nan
            a['CFS_PM_pred3'] = np.nan
            a['CFS_AM_pred1'] = np.nan
            a['CFS_AM_Daily_pred1'] = np.nan
            a['CFS_AM_Daily_pred2'] = np.nan

        # rolling mean Daily diffs
        try:
            a['diff_12Z_0-16_last-Prev24_PARACO'] = a['diff_12Z_0-16_last-Prev2_PARACO'] + a['diff_12Z_0-16_last-Prev4_PARACO']
            a['diff_12Z_0-16_last-Prev24_PARA'] = a['diff_12Z_0-16_last-Prev2_PARA'] + a['diff_12Z_0-16_last-Prev4_PARA']
            a['diff_12Z_0-16_last-Prev24_PARARACO'] = a['diff_12Z_0-16_last-Prev24_PARA'] + a['diff_12Z_0-16_last-Prev24_PARACO']

            a['diff_0Z_28-42_last-Prev24_CFS'] = a['diff_0Z_28-42_last-Prev2_CFS'] + a['diff_0Z_28-42_last-Prev4_CFS']
            a['diff_0Z_0-16_last-Prev1234_CFS'] = a[['diff_0Z_0-16_last-Prev%s_CFS'%i for i in range(1,5)]].mean(axis=1)
            a['diff_0Z_14-28_last-Prev1234_CFS'] = a[['diff_0Z_14-28_last-Prev%s_CFS'%i for i in range(1,5)]].mean(axis=1)
            for i in [1,2,3,4]:
                a['diff_0Z_14-35_last-Prev%s_CFS'%i] = a['diff_0Z_14-28_last-Prev%s_CFS'%i]*2+a['diff_0Z_28-35_last-Prev%s_CFS'%i]

            a['diff_0Z_14-35_last-Prev1234_CFS'] = a[['diff_0Z_14-35_last-Prev%s_CFS'%i for i in range(1,5)]].mean(axis=1)
            a['diff_0Z_0-21_last-Prev1234_CFS'] = a[['diff_0Z_0-21_last-Prev%s_CFS'%i for i in range(1,5)]].mean(axis=1)

            a['diff_0Z_14-28_last-Prev12_EPS45'] = a['diff_0Z_14-28_last-Prev1_EPS45'] + a['diff_0Z_14-28_last-Prev2_EPS45']
            a['diff_0Z_14-28_last-Prev1234_EPS45'] = a['diff_0Z_14-28_last-Prev12_EPS45'] + a['diff_0Z_14-28_last-Prev3_EPS45']+a['diff_0Z_14-28_last-Prev4_EPS45']
            a['diff_0Z_28-42_last-Prev1234_EPS45'] = a['diff_0Z_28-42_last-Prev1_EPS45']+ a['diff_0Z_28-42_last-Prev2_EPS45'] + a['diff_0Z_28-42_last-Prev3_EPS45'] + a['diff_0Z_28-42_last-Prev4_EPS45']
            a['diff_0Z_14-42_last-Prev1234_EPS45'] = a['diff_0Z_14-28_last-Prev1234_EPS45']+a['diff_0Z_28-42_last-Prev1234_EPS45']

            a['diff_0Z_14-42_last-Prev1_EPS45'] = a['diff_0Z_14-28_last-Prev1_EPS45'] + a['diff_0Z_28-42_last-Prev1_EPS45']

            a['EC_prev234'] = (a['diff_0Z_0-13_last-Prev2_EC'] + a['diff_0Z_0-13_last-Prev3_EC'] + a[
                'diff_0Z_0-13_last-Prev4_EC']) / 3
            a['EPS_9-13_prev12'] = a['diff_0Z_9-13_last-Prev1_EPS'] + a['diff_0Z_9-13_last-Prev2_EPS']

            a['CFS_14-28_prev24'] = a['diff_0Z_14-28_last-Prev2_CFS'] + 0.5 * a['diff_0Z_14-28_last-Prev4_CFS']
        except Exception as e:
            print ('Failed on some of the rolling diffs: %s'%e)

        if big_model_hour == 0:
            try:
                a['PM_pred1'] = (2 * a['diff_0Z_0-13_Prev1-Prev2_EPS'] +
                                 a['diff_0Z_0-13_Prev1-Prev2_EPS']+
                                a['diff_6Z_0-16_last-Prev1_GEFS'] +
                                 a['diff_0Z_0-16_Prev1-Prev2_GEFSL'] +
                                 a['diff_6Z_0-16_last-Prev1_PARA'] + a['diff_0Z_0-16_last-Prev4_GFSCO']+
                                  a['diff_0Z_0-16_last-Prev1_GEFSL'] +
                                  a['diff_0Z_0-16_last-Prev4_GFSCO'] +
                                  a['diff_0Z_0-16_last-Prev1_GFSCO'] +
                                  a['diff_6Z_0-16_last-Prev1_GEFS'] +
                                  a['diff_6Z_0-16_last-Prev2_GEFS']+
                                  a['diff_0Z_0-16_last-Prev1_GEPS']+
                                  0.5* a['diff_6Z_0-16_last-Prev1_PARA'] +
                                  0.5 * a['diff_6Z_0-16_last-Prev1_PARACO'])
                cols_for_AM_ens = ['EPSpost_pred2b', 'EC_AM_Summer_pred1', 'CFS_AM_pred1', 'PARA_pre6Z_pred1']
                weights = pd.Series(
                    {'EPSpost_pred2b': 0.5, 'EC_AM_Summer_pred1': 0.5, 'CFS_AM_pred1': 0.3, 'PARA_pre6Z_pred1': 0.2})
                a['AM_Ens1'] = (a[cols_for_AM_ens] * weights).mean(axis=1)

                cols_for_AM_ens1 = ['EPSpost_pred2b', 'EC_AM_Summer_pred1', 'GEM_AM_pred1', 'CFS_AM_pred1',
                                    'PARA_pre6Z_pred1']
                weights = pd.Series(
                    {'EPSpost_pred2b': 0.5, 'EC_AM_Summer_pred1': 0.5, 'GEM_AM_pred1': 0.33, 'CFS_AM_pred1': 0.3,
                     'PARA_pre6Z_pred1': 0.2})
                a['AM_Ens2'] = (a[cols_for_AM_ens] * weights).mean(axis=1)

                a['PM_Ens1'] = a['AM_Ens1'] + 0.35 * a['GFSEFS_PM_pred2'] + 0.35 * a['GFS_Super_PM_pred2'] + 0.35 * a[
                    'PM_hybrid_pred2']
                # a['PM_Ens1b'] = a['AM_Ens1'] + 0.35 * a['GFSEFS_PM_pred2'] + 0.35 * a['GFS_Super_PM_pred2'] + 0.35 * a['PM_hybrid_pred2']
                a['PM_Ens2'] = 0.75 * a['AM_Ens1'] + 0.35 * a['GFSEFS_PM_pred2'] + 0.35 * a['GFS_Super_PM_pred2'] + 0.75 * \
                               a['PM_hybrid_pred2']
                a['PM_Early_Ens3'] = a['AM_Ens1'] + a['EarlyPM_midweek']

            except:
                pass
        else:
            a['PM_pred1'] = 0
            a['AM_Ens1'] = 0
            a['AM_Ens2'] = 0
            a['PM_Ens1'] = 0
            a['PM_Ens2'] = 0
            a['PM_Early_Ens3'] = 0
        # 2* (a['diff_0-13_Prev1-Prev2_EPS']) / (a['diff_0-13_Prev1-Prev2_EPS']).std()
    except Exception as e:
        # raise
        print ('Failed on weather features... skipping\n Error: %s'%e)

    ### current weather features
    a['rans_test_pred0'] = a['Value_0Z_0-0_EC'].shift().diff()
    a['rans_test_pred1'] = a['Value_0Z_0-0_EC'].shift(3).diff()
    a['rans_test_pred2'] = a['Value_0Z_0-0_EC'].shift(2).diff()
    a['rans_test_pred3'] = a['Value_0Z_0-0_EC'].shift(1).diff()
    a['rans_test_04_pred0'] = a['Value_0Z_0-4_EC'].shift().diff()
    a['rans_test_04_pred1'] = a['Value_0Z_0-4_EC'].shift(3).diff()
    a['rans_test_04_pred2'] = a['Value_0Z_0-4_EC'].shift(2).diff()
    a['rans_test_04_pred3'] = a['Value_0Z_0-4_EC'].shift(1).diff()
    a['yesterday_Vs_lastWeek'] = (a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(10, 1).mean()).shift(2)
    a['yesterday_Vs_last5d'] = (a['Value_0Z_0-0_PARA'] - a['Value_0Z_0-0_PARA'].rolling(5, 1).mean()).shift(2)
    a['yesterday_Vs_d-2'] = (a['Value_0Z_0-0_PARA'].diff(1)).shift(2)

    prices_cols = [x for x in list(a) if
                   x in ['y', '1315-1745', 'y_1415-1500', 'y_1415-1515', 'y_1415-1645', 'y_1515-1715', 'y_1515-1645',
                         'y_1645-1745',
                         'y_1515-1745', 'y_1415-1715', 'y_1315-1515', 'y_1315-1615', 'y_1315-1715', 'y_1700-1745',
                         'y_1715-1745', 'y_1715-1815'] + \
                   ['y_1130-1415', 'y_1130-1315', 'y_1200-1245', 'y_1245-1315', 'y_1100-1130', 'y_1130-1230',
                    'y_1230-1315', 'y_1300-1315', 'y_1315-1345', 'y_1315-1415', 'y_1245-1315'
                                                                                'y_1245-1315_y_1245-1315',
                    'y_1345-1415', 'y_1345-1400', 'y_1400-1415', 'y_1415-1430', 'y_1400-1430', 'y_1415-1745'] + \
                   [x for x in list(a) if 'y_07' in x or 'y_08' in x or 'y_05' in x or 'y_06' in x or '07' in x or \
                    'y_09' in x or 'y_04' in x or 'y_10' in x or 'y_15' in x or 'y_16' in x or 'y_17' in x or 'y_18' in x or 'y_19' in x
                    or 'y_20' in x or 'y_21' in x or 'y_01' in x or 'y_00' in x or 'y_22' in x or 'y_23' in x or 'y_21' in x]
                   ]
    prices_cols2 = []
    for c in prices_cols:
        if c not in prices_cols2:
            prices_cols2.append(c)
    prices_cols = prices_cols2
    T = 0  # 0
    if T > 0:
        for col in prices_cols:
            a[col] = a[col].apply(lambda x: max(x, -T) if x < 0 else min(x, T))

    if add_deltaYs:
        a = add_deltaYs_columns(a)

    a = a.sort_values(['date'])
    # aa = a.corr()[prices_cols + ['y_1245-1430', 'y_1245-1745', 'y_1245-1645', 'y_1130-1745',
    #                              'y_1245-1945','y_1245-1415','y_0745-1100']]
    aa = pd.DataFrame()
    return a,aa,prices_cols