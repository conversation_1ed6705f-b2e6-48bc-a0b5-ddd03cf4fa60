from sklearn import svm
from sklearn.tree import DecisionTreeRegressor
import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

import sys
sys.path.append("...") # Adds higher directory to python modules path.

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor, BaggingRegressor
from Algo.Learning.deepbit import Deepbit
from Algo.Learning.old_configuration import *
from Algo.Learning.feature_engineering import add_manual_features,add_seasonal_diffs
from Algo.Viasualization.visualize_live_degdays import _last_available_model
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Data_Processing.hisotical_values import main_historical_calc
from Algo.Learning.models_accuracy_calc import get_multimodel_cors_df,get_cors_df
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe
import os
from Algo.Utils.files_handle import HOME
import warnings
warnings.filterwarnings('ignore')

WEEKDAYS = [0,1,2,3,4]#[0,1,2,3,4]
PNL_WEEKDAYS = [0,2,3]

### IMPORTANT
USE_WEIGHTED_AVG = False
NORMALIZE_YS = False
MOMENTUM_STEP = 0.15 # 0 or the step ratio
SMOOTH_CORR = 0 # 0
START = dtdt(2019,11,1) #dtdt(2019,11,1)
DEFAULT_ENS_PREDS = [2, 3, 4, 5, 6, 7, 8, 9]
MAX_CORR_TO_PREVIOUS = 1 #0.94

preds_6Z = ['PARA_6Z_pred1','PARACO_6Z_pred1','GEFS_6Z_pred1','GFSEFS_6Z_pred1','GFSEFS_6Z_pred3_basic']+\
            ['diff_%sZ_%s_last-Prev%s_%s'%(model_hour,days_gap,prev,model) for
             model_hour in [0,6] for days_gap in ['0-16','8-16','0-8'] for prev in [1,2,3,4] for model in
             ['PARA','PARACO','GEFS']]


dailies_old = ['diff_0Z_0-16_last-Prev1D_PARA','diff_0Z_0-16_last-Prev1D_PARACO',
            'diff_6Z_8-16_last-Prev1D_GEFS',
            'diff_6Z_0-16_last-Prev1D_PARA','diff_6Z_0-16_last-Prev1D_PARACO',
              'diff_0Z_0-16_last-Prev1D_GEFS','diff_0Z_0-16_last-Prev1D_GEFSL',
              'diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-13_last-Prev2D_EPS',
               'diff_0Z_0-16_last-Prev1D_CFS','diff_0Z_0-10_last-Prev2D_GEM',
        'diff_0Z_0-16_last-Prev2D_PARA','diff_0Z_0-16_last-Prev2D_PARACO',
            'diff_0Z_0-16_last-Prev2D_PARACO',
            'diff_0Z_0-16_last-Prev14D_PARARACO',

           'CFS_AM_Daily_pred1','CFS_PM_Daily_hybrid_pred2','diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev2D_CFS',
            'diff_12Z_14-28_last-Prev2D_CFS','diff_12Z_14-21_last-Prev1D_CFS',
            'diff_12Z_14-21_Prev1D-Prev2D_CFS',
            'diff_0Z_14-28_last-Prev3D_CFS','diff_0Z_14-28_last-Prev4D_CFS',
            'diff_0Z_28-42_last-Prev1D_CFS','diff_0Z_28-42_last-Prev2D_CFS',
            'diff_0Z_28-42_last-Prev3D_CFS','diff_0Z_28-42_last-Prev4D_CFS'
                ] +\
              ['GEFS_daily_hybrid_pred3','GEPS_daily_hybrid_pred3','PARACO_daily_hybrid_pred2','PARA_daily_hybrid_pred2','EPS_daily_hybrid_pred2.2']+\
            ['EPSpost_pred2.2','EC_AM_Summer_pred1','ECGEM_AM_pred1','GEFS_AM_basic_pred1','PARA_AM_basic_pred1','PARACO_AM_basic_pred1'] # +\
            #['diff_0Z_0-8_last-Prev4_EPS','diff_0Z_8-16_last-Prev4D_PARA','diff_0Z_0-16_last-Prev1D_GEFSL','diff_0Z_8-16_last-Prev4D_GEFSL']  ## temp?

to_drop = ['diff_0Z_0-16_last-Prev1D_GEFS','diff_0Z_0-16_last-Prev2D_GEFS','diff_0Z_14-28_last-Prev1D_CFS','PARA_AM_basic_pred1','diff_0Z0-16_last-Prev2D_PARA',
           'CFS_AM_Daily_pred1','diff_0Z_14-28_last-Prev4D_CFS']
to_add = ['diff_0Z_0-16_last-Prev4_PARA','diff_0Z_0-16_last-Prev1_PARACO','diff_0Z_0-16_last-Prev4_PARACO',
           'diff_0Z_8-16_last-Prev2_GEPS','diff_0Z_8-16_last-Prev1D_GEPS','diff_0Z_8-16_last-Prev3D_GEFS',
          'diff_0Z_8-16_last-Prev3D_GEFS','diff_0Z_8-16_last-Prev4D_GEFS','diff_0Z_8-16_last-Prev3D_PARACO',
          'diff_0Z_0-16_last-Prev4D_GEMCO','diff_0Z_8-16_last-Prev4D_GEM','diff_0Z_14-35_last-Prev2D_CFS',
            'GFSCO_daily_hybrid_pred2','GEFSL_daily_hybrid_8-16_pred2',
          'PARACO_daily_hybrid_pred1','PARACO_daily_hybrid_pred3',
          'EPS_daily_hybrid_pred1b','EPS_daily_hybrid_pred2b','GEFS_daily_hybrid_pred2','GEFS_AM_0-8_pred1','GEFS_PM_pred1D',
          'CFS_PM_Daily_hybrid_pred2','CFS_daily_hybrid_pred1b','CFS_AM_Daily_hybrid_pred1','EPSpost_pred2','EPSpost_pred3',
          'mix_0600-0700_pred1','EPS_PM_pred1']

dailies = ['diff_0Z_0-16_last-Prev1D_PARA','diff_0Z_0-16_last-Prev1D_PARACO',
            'diff_6Z_8-16_last-Prev1D_GEFS','diff_0Z_0-16_last-Prev1D_GEFS','diff_0Z_8-16_last-Prev3D_GEFS',
            'diff_0Z_0-16_Prev1D-Prev3D_GEFS',
            'diff_6Z_0-16_last-Prev1D_PARA','diff_6Z_0-16_last-Prev1D_PARACO',
            'diff_0Z_0-16_last-Prev2D_PARA','diff_0Z_8-16_last-Prev4D_PARA',
           'diff_0Z_0-16_last-Prev2D_PARACO',
           'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_8-16_last-Prev3D_PARACO','diff_0Z_0-16_last-Prev14D_PARARACO',
              'diff_18Z_0-16_last-Prev3_PARACO',

              'diff_0Z_0-16_last-Prev1D_GEFSL','diff_0Z_8-16_last-Prev4D_GEFSL',
              'diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-10_last-Prev2D_GEM',
                'diff_0Z_0-13_last-Prev2D_EPS','diff_0Z_0-8_last-Prev4_EPS',
            'diff_0Z_0-16_last-Prev1D_CFS',
           'CFS_AM_Daily_pred1','CFS_PM_Daily_hybrid_pred2',
            'diff_12Z_14-28_last-Prev2D_CFS','diff_12Z_14-21_last-Prev1D_CFS',
            'diff_12Z_14-21_Prev1D-Prev2D_CFS',
           'diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev2D_CFS',
            'diff_0Z_14-28_last-Prev3D_CFS','diff_0Z_14-28_last-Prev4D_CFS',
            'diff_0Z_28-42_last-Prev1D_CFS','diff_0Z_28-42_last-Prev2D_CFS',
            'diff_0Z_28-42_last-Prev3D_CFS','diff_0Z_28-42_last-Prev4D_CFS'
           ] + ['GEFS_daily_hybrid_pred3','GEPS_daily_hybrid_pred3',
                'PARACO_daily_hybrid_pred2','PARA_daily_hybrid_pred2','EPS_daily_hybrid_pred2.2']+\
            ['EPSpost_pred2.2','EC_AM_Summer_pred1','ECGEM_AM_pred1','GEFS_AM_basic_pred1',
             'PARA_AM_basic_pred1','PARACO_AM_basic_pred1']
dailies_minimized = ['diff_0Z_0-16_last-Prev1D_PARA','diff_0Z_0-16_last-Prev1D_PARACO',
            'diff_6Z_8-16_last-Prev1D_GEFS','diff_0Z_0-16_last-Prev1D_GEFS','diff_0Z_8-16_last-Prev3D_GEFS',
                     'diff_0Z_0-16_last-Prev2D_PARA', 'diff_0Z_8-16_last-Prev4D_PARA',
                     'diff_0Z_0-16_last-Prev2D_PARACO','diff_0Z_0-16_last-Prev14D_PARARACO',
                    'diff_18Z_0-16_last-Prev3_PARACO','diff_12Z_14-21_Prev1D-Prev2D_CFS',
                    'diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_14-28_last-Prev3D_CFS',
                    'diff_0Z_0-8_last-Prev1D_EC','diff_0Z_0-10_last-Prev2D_GEM',
                'CFS_AM_Daily_pred1','CFS_PM_Daily_hybrid_pred2',
                'diff_0Z_0-8_last-Prev4_EPS',
                'diff_0Z_0-16_last-Prev1D_GEFSL','diff_0Z_8-16_last-Prev4D_GEFSL',
                     ]+['GEFS_daily_hybrid_pred3','GEPS_daily_hybrid_pred3',
                'PARACO_daily_hybrid_pred2','PARA_daily_hybrid_pred2','EPS_daily_hybrid_pred2.2']+\
            ['EPSpost_pred2.2','EC_AM_Summer_pred1','ECGEM_AM_pred1','GEFS_AM_basic_pred1',
             'PARA_AM_basic_pred1','PARACO_AM_basic_pred1']

dailies3 = ['diff_0Z_%s_last-Prev%sD_%s'%(days,i,model) for days in ['0-16','8-16'] for i in range(1,5) for model in ['PARACO','GEMCO','GEPS','GEFS','PARA','CFS','GFSv16']]+\
            ['diff_0Z_0-13_last-Prev%sD_%s'%(i,model) for i in range(1,5) for model in ['EPS']]+\
            ['diff_0Z_0-8_last-Prev%sD_%s'%(i,model) for i in range(1,5) for model in ['EC']]+\
            ['diff_0Z_0-10_last-Prev%sD_%s'%(i,model) for i in range(1,5) for model in ['GEM']]+ \
           ['diff_0Z_%s_last-Prev%sD_%s' % (d,i, model) for d in ['14-35','14-28','28-42'] for i in range(1, 5) for model in ['CFS']]
small_hybrid = ['EPS_daily_hybrid_pred2.2','EPS_daily_hybrid_pred2','PARA_daily_hybrid_pred2','PARACO_daily_hybrid_pred2','PARACO_daily_hybrid_pred3',
                'GEFS_daily_hybrid_8-16_pred2','GFSCO_daily_hybrid_pred2','GEFSL_daily_hybrid_8-16_pred2','CFS_PM_Daily_hybrid_pred2','CFS_daily_hybrid_pred1b',
                'EPSpost_pred2.2','EC_daily_hybrid_pred1','GEPS_daily_hybrid_pred3',
                'ECGEM_AM_pred1','diff_0Z_0-16_last-Prev1D_PARACO']

preds_0z = ['diff_0Z_0-13_last-Prev1_EPS','EPSpost_pred2.2','EPSpost_pred2b','EPS_daily_hybrid_pred2.2',
            'EC_AM_Summer_pred1','ECGEM_AM_pred1','GEPS_AM_basic_pred1','PARA_AM_basic_pred1',
            'GEFS_AM_basic_pred1','PARACO_AM_basic_pred1',
            'CFS_AM_pred1','CFS_AM_Daily_pred1','diff_12Z_14-28_last-Prev1234_CFS',
            'diff_0Z_14-28_last-Prev1D_CFS','diff_0Z_28-42_last-Prev1D_CFS',
            'diff_0Z_14-28_last-Prev4D_CFS','diff_0Z_28-42_last-Prev4D_CFS',
            'diff_0Z_14-28_last-Prev1234_EPS45','diff_0Z_28-42_last-Prev1234_EPS45',
            'diff_18Z_0-16_last-Prev1_PARACO','diff_18Z_0-16_last-Prev2_PARACO','diff_18Z_0-16_last-Prev3_PARACO',
            'diff_18Z_0-16_last-Prev4_PARACO','diff_12Z_0-16_last-Prev2_PARACO',
            'GEFSL_PM_pred1_Prev2','GEFSL_PM_pred1_Prev4','GEFSL_PM_pred1_Prev1',
            'diff_0Z_0-16_Prev1D-Prev2D_GEFS'
            ]


preds_gefs12z_pre6z = ['diff_0-10_last-Prev3D_GEM','diff_0Z_0-16_last-Prev3_GEFSL',
                       'diff_0Z_0-16_Prev1D-Prev3D_GEFSL',
                       'diff_0Z_0-16_last-Prev2_GEFSL','diff_0Z_0-13_last-Prev2D_EPS',
                       'diff_0Z_0-14_last-Prev4_EPS','diff_0Z_0-10_Prev1-Prev3_GEM',
                       # from here relevant to predict 6Z last-1
                       'diff_0Z_0-8_last-Prev1_GEMCO','diff_0Z_0-13_last-Prev3_GEMCO',
                       'diff_0Z_8-16_Prev1-Prev3_GEMCO',
                       'diff_18Z_14-16_Prev1-Prev3_PARA',
                       'diff_0Z_0-16_Prev1D-Prev2D_CFS',
                       'diff_0Z_0-15_last-Prev3_PARACO','diff_0Z_0-16_last-Prev1D_CFS-',
                       'diff_12Z_0-13_Prev1-Prev3_CFS-',
                       #'diff_0Z_GEFS-EPS(1)_0-13-','diff_0Z_GEFS-GEPS(1)_0-13-'
                       ]
preds_gefs12z_post6z = preds_gefs12z_pre6z + ['diff_6Z_0-16_last-Prev1_GEFS','diff_6Z_8-16_last-Prev1_GEFS',
                                              'diff_6Z_11-15_last-Prev1_GEFS','diff_6Z_8-16_last-Prev4_GEFS',
                                              'diff_6Z_0-15_last-Prev3_PARACO','diff_18Z_0-16_last-Prev1_GEFS-',
                                              'Value_6Z_0-2_diff8_GEFS-']

preds_eps_12Z = ['diff_6Z_0-8_last-Prev3_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                             'diff_6Z_0-8_last-Prev4_GEFS','diff_6Z_0-8_last-Prev4_PARACO',
                             'diff_6Z_0-2_last-Prev3_PARA','diff_0Z_0-2_last-Prev4_PARA',
                             'diff_0Z_0-2_Prev1-Prev3_PARA','diff_0Z_0-2_Prev1-Prev2_PARA',
                             'diff_0Z_0-16_last-Prev2_GEFS','diff_12Z_0-16_last-Prev4_GEFS',
                             'diff_12Z_0-10_last-Prev1D_GEFS','diff_12Z_0-16_last-Prev2D_GEFS',
                            'diff_12Z_0-8_last-Prev2_GEFS','diff_12Z_0-8_last-Prev3_GEFS',
                             'diff_12Z_0-2_last-Prev1_GEFS','diff_0Z_0-13_last-Prev1_GEFSLCO',
                             'diff_12Z_0-10_last-Prev1D_GEM','diff_12Z_8-16_last-Prev2D_GEPS',
                             'diff_0Z_8-16_last-Prev3D_GEMCO','diff_0Z_8-16_last-Prev4D_GEMCO',
                             'diff_0Z_0-10_last-Prev1D_GEFSL']

preds_for_gefs6z = ['diff_18Z_14-16_last-Prev4_PARACO','diff_18Z_14-16_Prev1-Prev3_PARACO','diff_18Z_0-15_last-Prev3_PARACO',
                           #'diff_18Z_14-16_last-Prev4_GEFS-','diff_18Z_14-16_last-Prev1_GEFS-',
                            'diff_0Z_8-16_last-Prev3_GEFSL','diff_0Z_0-21_Prev1-Prev2_GEFSL35','diff_0Z_0-16_Prev1D-Prev2D_CFS',
                            'diff_6Z_0-16_last-Prev1_PARA','diff_6Z_8-16_last-Prev1_PARA','diff_6Z_0-16_last-Prev4_PARA','diff_6Z_8-16_last-Prev4_PARA',
                            # new
                            'diff_0Z_0-8_last-Prev1_GEMCO',
                            'diff_0Z_0-0_last-Prev1_GEPS','diff_0Z_0-0_last-Prev3_GEPS','diff_0Z_8-16_Prev1-Prev3_GEPS','diff_0Z_8-16_last-Prev3_GEPS',
                            'diff_0Z_21-35_last-Prev4D_CFS','diff_0Z_14-28_last-Prev2_GEFS35'
                            #'diff_6Z_0-8_last-Prev1_PARACO','diff_6Z_0-8_last-Prev2_PARACO','diff_6Z_0-8_last-Prev3_PARACO'
                    ]

preds_eps_12Zb = ['diff_6Z_0-8_last-Prev3_GEFS','diff_6Z_0-16_last-Prev3_GEFS','diff_6Z_0-16_last-Prev4_GEFS',
                     'diff_6Z_0-8_last-Prev4_GEFS','diff_6Z_0-8_last-Prev4_PARACO',
                     'diff_6Z_0-2_last-Prev3_PARA','diff_0Z_0-2_last-Prev4_PARA',
                     'diff_0Z_0-2_Prev1-Prev3_PARA','diff_0Z_0-2_Prev1-Prev2_PARA',
                     'diff_0Z_0-16_last-Prev2_GEFS','diff_12Z_0-10_last-Prev1D_GEFS',
                    'diff_0Z_0-13_last-Prev1_GEFSLCO','diff_0Z_0-10_last-Prev1D_GEFSL',
                    'diff_12Z_8-16_last-Prev2D_GEPS','diff_0Z_8-16_last-Prev3D_GEMCO','diff_0Z_8-16_last-Prev4D_GEMCO',
                    'diff_12Z_8-16_last-Prev2D_GEMCO','diff_12Z_8-16_last-Prev4D_GEMCO',
                    'diff_12Z_0-21_last-Prev1_CFS','diff_18Z_0-21_last-Prev2_CFS',
                  'diff_6Z_8-16_Prev1D-Prev2D_GEFS',
                  'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS',
                  'diff_12Z_5-8_last-Prev3_EC']

additional_preds_for_eps = ['diff_12Z_0-16_last-Prev1_PARACO','diff_12Z_0-8_last-Prev2_PARACO','diff_12Z_0-8_last-Prev1_PARACO','diff_18Z_0-2_last-Prev1_PARA',
                        'diff_18Z_0-16_last-Prev4_GEFS','diff_18Z_14-16_last-Prev2_GEFS','diff_0Z_11-15_last-Prev1_GEFS',
                        'diff_0Z_0-10_last-Prev1D_GEM','diff_0Z_0-13_last-Prev4_GEM','diff_0Z_0-13_last-Prev2D_GEM','diff_12Z_0-10_last-Prev1_GEM']
additional_preds_for_eps = []

preds_ec_12z_from6z = [
                        'diff_6Z_0-13_last-Prev1_GEFS','diff_6Z_0-16_last-Prev1_PARACO','diff_6Z_0-2_last-Prev3_PARA-',
                        'diff_6Z_0-16_last-Prev1_GEFS', 'diff_6Z_0-8_last-Prev1_GEFS','diff_6Z_0-2_last-Prev3_PARA-','diff_6Z_0-2_last-Prev3_GEFS-']
preds_ec_12z = ['diff_12Z_0-16_last-Prev1D_GFSv16',
                            'diff_12Z_0-10_last-Prev1_GEM','diff_12Z_0-10_last-Prev3_GEM',
                            'diff_0Z_0-10_last-Prev3_GEM','diff_12Z_0-10_Prev1D-Prev3D_GEM',
                            'diff_12Z_0-8_last-Prev1_GEPS',
                            'diff_0Z_14-16_Prev1-Prev3_GEPS',
                            'diff_0Z_14-16_Prev1-Prev3_GEMCO'
                            'diff_0Z_0-8_last-Prev1_PARA','diff_0Z_0-8_last-Prev2_PARA',
                            'diff_18Z_0-8_last-Prev3_PARA','diff_18Z_0-8_last-Prev4_PARA',
                            'diff_0Z_0-16_last-Prev1_PARACO','diff_12Z_8-16_last-Prev2_PARACO',
                            'diff_12Z_0-10_Prev1-Prev3_PARACO',
                    'diff_0Z_14-28_last-Prev1_CFS'
                            'diff_12Z_0-10_last-Prev1_GEM','diff_12Z_0-10_last-Prev1_GEM',
                            'diff_0Z_0-10_last-Prev1_GEM','diff_0Z_0-10_last-Prev1_GEM',
                            'diff_12Z_0-2_last-Prev1_GEM','diff_12Z_0-@_last-Prev1_GEM',
                            'diff_0Z_0-2_last-Prev1_GEM','diff_0Z_0-2_last-Prev1_GEM',
                ]+['diff_0Z_0-8_last-Prev1_EPS-','diff_0Z_0-8_last-Prev3_EPS-',
                    'diff_12Z_0-13_last-Prev1_EPS',
                         'diff_0Z_0-8_last-Prev1_EC-',
                         'diff_0Z_8-16_Prev1-Prev2_GEPS',
                         'diff_12Z_0-2_last-Prev4_GEFS-',
                         'diff_12Z_11-15_Prev1-Prev3_GEFS'
                                         ]#+preds_ec_12z_from6z

preds_eps12Z_unicors = ['diff_0Z_0-16_last-Prev1_GEFS',
           'diff_6Z_0-16_last-Prev1_GEFS', 'diff_6Z_0-16_last-Prev2_GEFS',
           'diff_18Z_0-16_last-Prev1_GEFS',
           'diff_6Z_0-16_last-Prev3_GEFS', 'diff_6Z_0-16_last-Prev4_GEFS',
           'diff_12Z_0-16_last-Prev1_GEFS', 'diff_12Z_0-16_last-Prev2_GEFS',
           'diff_12Z_0-16_last-Prev1_PARA', 'diff_12Z_0-16_last-Prev2_PARA',
           'diff_12Z_0-16_last-Prev1_PARACO', 'diff_12Z_0-16_last-Prev2_PARACO',
           'diff_6Z_0-16_last-Prev1_GFSv16', 'diff_6Z_0-16_last-Prev2_GFSv16',
           'diff_0Z_0-16_last-Prev1_GFSv16', 'diff_0Z_0-16_last-Prev2_GFSv16',
           'diff_12Z_0-16_last-Prev1_GFSv16', 'diff_12Z_0-16_last-Prev2_GFSv16',
           'diff_12Z_0-16_last-Prev1_GEPS', 'diff_12Z_0-16_last-Prev2_GEPS',
           'diff_0Z_0-16_last-Prev1_GEPS', 'diff_0Z_0-16_last-Prev2_GEPS','diff_12Z_0-16_last-Prev3_GEPS',
           'diff_0Z_0-16_last-Prev3_GEPS', 'diff_0Z_0-16_Prev1-Prev3_GEPS']

preds_eps9to13_unicors = ['diff_0Z_14-16_last-Prev1_GEMCO','diff_0Z_14-16_last-Prev2_GEMCO',
                          'diff_12Z_14-16_last-Prev1_GEMCO',
                          'diff_0Z_14-16_last-Prev1_GEPS','diff_0Z_14-16_last-Prev2_GEPS',
                          'diff_12Z_14-16_last-Prev1_GEPS',
                          'diff_0Z_14-16_last-Prev1_GEFS', 'diff_0Z_14-16_last-Prev2_GEFS',
                            'diff_0Z_14-16_last-Prev3_GEFS', 'diff_0Z_14-16_last-Prev4_GEFS',
                        'diff_6Z_14-16_last-Prev1_GEFS', 'diff_6Z_14-16_last-Prev2_GEFS',
                            'diff_6Z_14-16_last-Prev3_GEFS',
                          'diff_12Z_14-16_last-Prev1_GEFS','diff_12Z_14-16_last-Prev2_GEFS',
                            'diff_12Z_14-16_last-Prev4_GEFS',
                        'diff_6Z_14-16_last-Prev1_PARACO', 'diff_6Z_14-16_last-Prev2_PARACO',
                        'diff_0Z_14-16_last-Prev1_PARACO', 'diff_0Z_14-16_last-Prev2_PARACO',
                        'diff_12Z_14-16_last-Prev1_PARACO', 'diff_12Z_14-16_last-Prev2_PARACO',
                        'diff_6Z_14-16_last-Prev1_PARA', 'diff_6Z_14-16_last-Prev2_PARA',
                        'diff_0Z_14-16_last-Prev1_PARA', 'diff_0Z_14-16_last-Prev2_PARA',
                        'diff_12Z_14-16_last-Prev1_PARA', 'diff_12Z_14-16_last-Prev2_PARA',
                          ]

dailies_old_ext = [x for x in dailies_old+ to_add] #if x not in to_drop]

POTENTIAL_PREDS = {'EPS_0Z': list(set(['diff_18Z_14-16_last-Prev1_PARA','diff_18Z_0-15_last-Prev1_PARA','diff_0Z_14-16_last-Prev2_PARA',
                            'diff_18Z_0-8_last-Prev1_GEFS','diff_18Z_0-8_last-Prev3_GEFS', 'diff_18Z_0-2_last-Prev1_GEFS','diff_18Z_0-2_last-Prev4_GEFS',
                            'diff_18Z_0-13_last-Prev4_GEFS'
                            
                            'diff_12Z_0-16_last-Prev4_GEPS','diff_12Z_0-16_last-Prev3_GEPS', 'diff_12Z_0-16_last-Prev2_GEPS',
                        'diff_0Z_0-13_last-Prev2_GEPS','diff_0Z_0-13_last-Prev3_GEPS','diff_0Z_0-13_Prev1-Prev2_GEPS','diff_0Z_8-16_Prev1-Prev3_GEPS',
                            'diff_0Z_14-16_last-Prev1_GEPS','diff_0Z_14-16_last-Prev2_GEPS',
                            'diff_0Z_0-8_last-Prev2_GEPS','diff_0Z_0-13_last-Prev2_GEPS', 'diff_0Z_0-8_Prev1-Prev2_GEPS',
                            'diff_0Z_0-8_last-Prev2_GEMCO','diff_0Z_0-8_last-Prev1_GEMCO',
                        'diff_0Z_0-13_last-Prev2_GEMCO','diff_0Z_0-13_last-Prev1_GEMCO',
                            'diff_0Z_0-16_last-Prev1D_GEPS',

                            'diff_0Z_0-2_last-Prev3_GEFS35',
                            'diff_0Z_0-8_last-Prev1_EC','diff_0Z_0-8_last-Prev2_EC','diff_0Z_0-8_last-Prev3_EC','diff_0Z_0-8_last-Prev4_EC'
                            'diff_0Z_0-4_last-Prev1_EC','diff_0Z_0-4_last-Prev2_EC','diff_0Z_0-4_last-Prev3_EC','diff_0Z_0-4_last-Prev4_EC',
                            'diff_12Z_0-8_last-Prev1_EC','diff_12Z_0-8_last-Prev2_EC','diff_12Z_0-8_last-Prev3_EC','diff_12Z_0-8_last-Prev4_EC','diff_12Z_0-8_Prev1-Prev2_EC'
                            'diff_12Z_0-4_last-Prev1_EC','diff_12Z_0-4_last-Prev2_EC','diff_12Z_0-4_last-Prev3_EC','diff_12Z_0-4_last-Prev4_EC',

                            'diff_12Z_0-8_last-Prev1_EPS','diff_12Z_0-8_last-Prev2_EPS','diff_12Z_0-8_last-Prev3_EPS','diff_12Z_0-8_last-Prev4_EPS','diff_12Z_0-8_Prev1-Prev2_EPS'
                            'diff_12Z_0-13_last-Prev1_EPS','diff_12Z_0-13_last-Prev2_EPS','diff_12Z_0-13_last-Prev3_EPS','diff_12Z_0-13_last-Prev4_EPS',

                            'diff_12Z_8-16_last-Prev1_PARA-', 'diff_12Z_0-16_last-Prev2_PARA-',
                            'diff_12Z_8-16_last-Prev1_PARA-',
                            'diff_12Z_0-10_Prev1-Prev3_GEM-','diff_0Z_0-10_Prev1D-Prev2D_GEM-',

                            'diff_12Z_14-28_last-Prev4_CFS','diff_12Z_14-21_last-Prev4_CFS',
                            'diff_12Z_28-42_last-Prev1_CFS','diff_18Z_28-42_last-Prev2_CFS',
                            'diff_0Z_0-16_last-Prev4_PARACO', 'diff_0Z_0-13_Prev1-Prev2_CFS',
                            'diff_12Z_0-10_last-Prev1D_GEFS',
                            'diff_12Z_0-15_last-Prev4_PARA', 'diff_12Z_0-8_last-Prev1_PARA',
                            'diff_12Z_0-8_last-Prev3_PARACO', 'diff_12Z_0-2_last-Prev1_GEFS',
                            'diff_12Z_0-8_last-Prev4_GEFS', 'diff_12Z_0-13_last-Prev1_GEMCO',
                            'diff_0Z_0-16_last-Prev2_GEFS']))+additional_preds_for_eps,
                   '6Z': preds_6Z,
                   'EPS_12Z':preds_eps_12Z,
                   'GEFS_6Z': preds_for_gefs6z,
                    'GEFS_12Z': preds_gefs12z_post6z,
                    'GEFS_12Za': preds_gefs12z_pre6z,
                   'EC_12Z': preds_ec_12z,
                   'daily_0Z': [x for x in dailies_old if x]}#'6Z' not in x]}


def main_calculator(a,window,eps_col,y_col,start=START,weekdays=WEEKDAYS,ens_preds=DEFAULT_ENS_PREDS,
                    time_group='EPS_0Z',smoothing=SMOOTH_CORR,momentum_step=MOMENTUM_STEP):
    cond = lambda x: True
    cond = lambda x: not ('EC' in x and '0Z' in x and '8' in x)


    chosen_preds = POTENTIAL_PREDS[time_group]

    ### todo hack need to separate the chosen_preds
    if eps_col == 'diff_12Z_0-13_last-Prev1_EPS+1d':
        a['diff_12Z_0-13_last-Prev1_EPS+1d'] = a['diff_12Z_0-13_last-Prev1_EPS'].shift(-1)
        chosen_preds = preds_eps_12Zb
    elif eps_col == 'diff_12Z_0-8_last-Prev1_EC+1d':
        a['diff_12Z_0-8_last-Prev1_EC+1d'] = a['diff_12Z_0-8_last-Prev1_EC'].shift(-1)
    #chosen_preds = small_hybrid#[x for x in list(a) if 'pred' in x and 'hybrid' in x and '6Z' not in x]
    #chosen_preds = [x for x in dailies if '6Z' not in x]
    chosen_preds = list(set(chosen_preds))

    prefix_cols = ['date', y_col, eps_col]
    if eps_col == y_col:
        prefix_cols = ['date', y_col]
    for x in chosen_preds:
        if x not in list(a) and x[:-1] in list(a) and x.endswith('-'):
            a[x] = -a[x[:-1]]
            pass
    a2 = a[prefix_cols + [x for x in chosen_preds if x in list(a) and cond(x) and x not in prefix_cols]]
    preds_covariance = a2[[x for x in list(a2) if x not in prefix_cols]].corr()

    # [x for x in list(a) if x.startswith('diff_') and sum([s in x for s in ['PARA','GEFS']]) and x != eps_col and ('0-16' in x or '14-' in x)]]
    NUM_OF_PREDS = 5
    a2 = a2[(a2['date'] >= start) & (a['date'].dt.weekday.isin(weekdays))]
    a2[list(a2)[2:]] = (a2[list(a2)[2:]] / a2[list(a2)[2:]].std())

    prefix_cols = ['date', y_col, eps_col]
    if eps_col == y_col:
        prefix_cols = ['date', y_col]
    rolling_cors = a2.set_index('date').shift(1).rolling(window, 4).corr().reset_index()
    rolling_cors = rolling_cors[rolling_cors['level_1'].isin([eps_col])]
    rolling_cors = rolling_cors.dropna(how='all',
                                       subset=[x for x in list(rolling_cors) if x not in ['date', 'level_1']])

    bad_cols = list(rolling_cors)[4:]
    final_cols = []
    for c in bad_cols:
        try:
            nans_ratio = rolling_cors[c].isna().mean()
            if nans_ratio > 0.5:
                continue
            else:
                final_cols.append(c)
        except:
            aa = 1
    if window == 10:
        bb = 0
    rolling_cors = rolling_cors[list(rolling_cors)[:4] + final_cols].reset_index()
    if smoothing > 0:
        rolling_cors[final_cols] = rolling_cors[final_cols].rolling(smoothing, 1).mean()
    if momentum_step > 0:
        rolling_cors[final_cols] = rolling_cors[final_cols] + momentum_step * rolling_cors[final_cols].diff(1)

        # Plays
        negative_and_momentum_change = (rolling_cors[final_cols] < 0) & (rolling_cors[final_cols].diff(1) > 0)
        negative_to_positive = (rolling_cors[final_cols] > 0) & (rolling_cors[final_cols].shift(1) < 0)
        addition = pd.DataFrame(np.ones(shape=(rolling_cors.shape[0], len(final_cols))))
        addition.columns = final_cols
        rolling_cors[final_cols] = rolling_cors[final_cols] + addition*momentum_step

    rolling_cors = rolling_cors[[x for x in list(rolling_cors) if 'index' not in x]]
    common = list(set(list(rolling_cors)).intersection(set(list(a2))))
    real_vals = rolling_cors[['date']].merge(a2, on=['date'])
    real_vals['best6'] = np.nan
    for n in ens_preds:
        real_vals['Ens_top%s' % n] = np.nan

    all_used_cols = {}
    top3_preds = []
    for ind, day_cors in rolling_cors.iterrows():
        for n in ens_preds:
            sorted_columns = day_cors[len(prefix_cols) + 1:].sort_values().index.tolist()
            sorted_vals = day_cors[len(prefix_cols) + 1:].sort_values()
            best_columns, best_vals = [],[]
            i = 0
            while len(best_columns) < n and i < len(sorted_columns):
                if len(best_columns) == 0:
                    best_columns.append(sorted_columns[-i-1])
                    best_vals.append(sorted_vals[-i - 1])
                elif preds_covariance[[sorted_columns[-i-1]]].loc[best_columns].max().iloc[0] <= MAX_CORR_TO_PREVIOUS:
                    best_columns.append(sorted_columns[-i - 1])
                    best_vals.append(sorted_vals[-i - 1])
                i += 1
            try:
                best_vals = best_vals / abs(best_vals).sum()
                best_vals = best_vals * (n / abs(best_vals).sum())
            except:
                bb = 0
                pass

            for c in best_columns:
                if c in all_used_cols:
                    all_used_cols[c] += 1
                else:
                    all_used_cols[c] = 1
            if n == 3:
                top3_preds.append(best_columns)
            if USE_WEIGHTED_AVG:
                real_vals.loc[ind, 'Ens_top%s' % n] = (real_vals.loc[ind, best_columns] * best_vals).mean()
            else:
                real_vals.loc[ind, 'Ens_top%s' % n] = (real_vals.loc[ind, best_columns]).mean()
            if n == 6:
                real_vals.loc[ind, 'best%s'%n] = str(best_columns)
    #print ('Inside eps_prediction \\ time_group = %s \\ last used features: \n %s'%(time_group,best_columns))
    #print ('---------------')
    return real_vals, all_used_cols

def add_eps_preds_from_gefs(a,window=5,suffix='v8_12Z',eps_col = 'diff_12Z_0-13_last-Prev1_EPS'):
    if suffix in ['v8_0Zb']:
        if '12Z' in eps_col:
            eps_col2 = eps_col+'+1d'
            a[eps_col2] = a[eps_col].shift(-1)
            eps_col = eps_col2

    preds_eps12Z_unicors_final = preds_eps12Z_unicors
    if eps_col.replace('+1d','') in ['diff_0Z_9-13_last-Prev1_EPS','diff_0Z_9-13_last-Prev2_EPS',
                       'diff_12Z_9-13_last-Prev1_EPS','diff_12Z_9-13_last-Prev2_EPS',]:
        preds_eps12Z_unicors_final = preds_eps9to13_unicors

    b = a[[eps_col]+preds_eps12Z_unicors_final].rolling(window, window).corr().reset_index()
    b = b.set_index('level_0')
    b = b[b['level_1'] == eps_col]
    b = b.loc[:, ~b.columns.duplicated()]
    b[[x for x in list(b) if 'diff' in x]] = b[[x for x in list(b) if 'diff' in x]] + b[
        [x for x in list(b) if 'diff' in x]].diff() * 0
    b = b.shift(1)
    square = False
    if '12Z' in eps_col:
        model_h = '12Z'
    elif '0Z' in eps_col:
        model_h = '0Z'
    elif '6Z' in eps_col:
        model_h = '6Z'
    else:
        raise
    model = eps_col.replace('+1d','').split('_')[-1]
    prefix = '%s_%s'%(model,model_h)

    if not square:
        # GEFS_6Zb_pred_EnsTop4_window=10
        if eps_col.replace('+1d','') in ['diff_0Z_9-13_last-Prev1_EPS','diff_0Z_9-13_last-Prev2_EPS',
                       'diff_12Z_9-13_last-Prev1_EPS','diff_12Z_9-13_last-Prev2_EPS',]:
            prefix +='_9to13'
            a['%s_Pgefs0Z_window=%s' % (prefix, window)] = b['diff_0Z_14-16_last-Prev1_GEFS'] * a['diff_0Z_14-16_last-Prev1_GEFS']+b['diff_0Z_14-16_last-Prev2_GEFS'] * a['diff_0Z_14-16_last-Prev2_GEFS']
            a['%s_Pgefs12Z_window=%s' % (prefix, window)] = b['diff_12Z_14-16_last-Prev1_GEFS'] * a['diff_12Z_14-16_last-Prev1_GEFS']+b['diff_12Z_14-16_last-Prev2_GEFS'] * a['diff_12Z_14-16_last-Prev2_GEFS']
            a['%s_Pgefs6Z_window=%s' % (prefix, window)] = b['diff_6Z_14-16_last-Prev1_GEFS'] * a['diff_6Z_14-16_last-Prev2_GEFS']+b['diff_6Z_14-16_last-Prev2_GEFS'] * a['diff_6Z_14-16_last-Prev2_GEFS']
            a['%s_Pparaco0Z_window=%s' % (prefix, window)] = b['diff_0Z_14-16_last-Prev1_PARACO'] * a['diff_0Z_14-16_last-Prev1_PARACO']+b['diff_0Z_14-16_last-Prev2_PARACO'] * a['diff_0Z_14-16_last-Prev2_PARACO']
            a['%s_Pparaco12Z_window=%s' % (prefix, window)] = b['diff_12Z_14-16_last-Prev1_PARACO'] * a['diff_12Z_14-16_last-Prev1_PARACO']+b['diff_12Z_14-16_last-Prev2_PARACO'] * a['diff_12Z_14-16_last-Prev2_PARACO']
            a['%s_Pparaco6Z_window=%s' % (prefix, window)] = b['diff_6Z_14-16_last-Prev1_PARACO'] * a['diff_6Z_14-16_last-Prev2_PARACO']+b['diff_6Z_14-16_last-Prev2_PARACO'] * a['diff_6Z_14-16_last-Prev2_PARACO']
            a['%s_Ppara0Z_window=%s' % (prefix, window)] = b['diff_0Z_14-16_last-Prev1_PARA'] * a['diff_0Z_14-16_last-Prev1_PARA']+b['diff_0Z_14-16_last-Prev2_PARA'] * a['diff_0Z_14-16_last-Prev2_PARA']
            a['%s_Ppara12Z_window=%s' % (prefix, window)] = b['diff_12Z_14-16_last-Prev1_PARA'] * a['diff_12Z_14-16_last-Prev1_PARA']+b['diff_12Z_14-16_last-Prev2_PARA'] * a['diff_12Z_14-16_last-Prev2_PARA']
            a['%s_Ppara6Z_window=%s' % (prefix, window)] = b['diff_6Z_14-16_last-Prev1_PARA'] * a['diff_6Z_14-16_last-Prev2_PARA']+b['diff_6Z_14-16_last-Prev2_PARA'] * a['diff_6Z_14-16_last-Prev2_PARA']
            a['%s_Pgemco0Z_window=%s' % (prefix, window)] = b['diff_0Z_14-16_last-Prev1_GEMCO'] * a['diff_0Z_14-16_last-Prev1_GEMCO']+b['diff_0Z_14-16_last-Prev2_GEMCO'] * a['diff_0Z_14-16_last-Prev2_GEMCO']
            a['%s_Pgemco12Z_window=%s' % (prefix, window)] = b['diff_12Z_14-16_last-Prev1_GEMCO'] * a['diff_12Z_14-16_last-Prev1_GEMCO']

        else:
            a['%s_Pgefs12Z_window=%s'%(prefix, window)] = b['diff_12Z_0-16_last-Prev2_GEFS'] * a['diff_12Z_0-16_last-Prev2_GEFS']
            a['%s_Pgefs18Z_window=%s'%(prefix, window)] = b['diff_18Z_0-16_last-Prev1_GEFS'] * a['diff_18Z_0-16_last-Prev1_GEFS']
            a['%s_Pgefs6Z_window=%s'%(prefix, window)] = b['diff_6Z_0-16_last-Prev1_GEFS'] * a['diff_6Z_0-16_last-Prev1_GEFS']
            a['%s_Pgefs6Z2_window=%s'%(prefix, window)] = b['diff_6Z_0-16_last-Prev2_GEFS'] * a['diff_6Z_0-16_last-Prev2_GEFS']
            a['%s_Pgefs6Z3_window=%s'%(prefix, window)] = b['diff_6Z_0-16_last-Prev3_GEFS'] * a['diff_6Z_0-16_last-Prev3_GEFS']
            a['%s_Pgefs6Z4_window=%s'%(prefix, window)] = b['diff_6Z_0-16_last-Prev4_GEFS'] * a['diff_6Z_0-16_last-Prev4_GEFS']
            a['%s_Pgefs12Z_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev1_GEFS'] * a['diff_12Z_0-16_last-Prev1_GEFS']
            a['%s_Pgefs12Z2_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev2_GEFS'] * a['diff_12Z_0-16_last-Prev2_GEFS']
            a['%s_Pgeps0Z_window=%s' %(prefix, window)] = b['diff_0Z_0-16_last-Prev1_GEPS'] * a['diff_0Z_0-16_last-Prev1_GEPS']
            a['%s_Pgeps12Z_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev1_GEPS'] * a['diff_12Z_0-16_last-Prev1_GEPS']
            a['%s_Pgeps12Z3_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev3_GEPS'] * a['diff_12Z_0-16_last-Prev3_GEPS']
            a['%s_Pgeps0Z2_window=%s' %(prefix, window)] = b['diff_0Z_0-16_last-Prev2_GEPS'] * a['diff_0Z_0-16_last-Prev2_GEPS']
            a['%s_Pgeps0Z3_window=%s' %(prefix, window)] = b['diff_0Z_0-16_last-Prev3_GEPS'] * a['diff_0Z_0-16_last-Prev3_GEPS']
            a['%s_Pgeps0Z13_window=%s' %(prefix, window)] = b['diff_0Z_0-16_Prev1-Prev3_GEPS'] * a['diff_0Z_0-16_Prev1-Prev3_GEPS']
            a['%s_Ppara12Z_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev1_PARA'] * a['diff_12Z_0-16_last-Prev1_PARA']
            a['%s_Ppara12Z2_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev2_PARA'] * a['diff_12Z_0-16_last-Prev2_PARA']
            a['%s_Pparaco12Z_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev1_PARACO'] * a['diff_12Z_0-16_last-Prev1_PARACO']
            a['%s_Pparaco12Z2_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev2_PARACO'] * a['diff_12Z_0-16_last-Prev2_PARACO']
            a['%s_Pgfsv166Z_window=%s' % (prefix, window)] = b['diff_6Z_0-16_last-Prev1_GFSv16'] * a['diff_6Z_0-16_last-Prev1_GFSv16']
            a['%s_Pgfsv166Z2_window=%s' % (prefix, window)] = b['diff_6Z_0-16_last-Prev2_GFSv16'] * a['diff_6Z_0-16_last-Prev2_GFSv16']
            a['%s_Pgfsv160Z_window=%s' % (prefix, window)] = b['diff_0Z_0-16_last-Prev1_GFSv16'] * a['diff_0Z_0-16_last-Prev1_GFSv16']
            a['%s_Pgfsv160Z2_window=%s' % (prefix, window)] = b['diff_0Z_0-16_last-Prev2_GFSv16'] * a['diff_0Z_0-16_last-Prev2_GFSv16']
            a['%s_Pgfsv1612Z_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev1_GFSv16'] * a['diff_12Z_0-16_last-Prev1_GFSv16']
            a['%s_Pgfsv1612Z2_window=%s' %(prefix, window)] = b['diff_12Z_0-16_last-Prev2_GFSv16'] * a['diff_12Z_0-16_last-Prev2_GFSv16']

    else:
        raise

    a['%s_PgefsMEAN_window=%s'%(prefix, window)] = a[[x for x in list(a) if '%s_Pgefs'%prefix in x]].mean(
        axis=1)
    plus_cond = a['%s_Pgefs6Z_window=%s'%(prefix, window)] > 0  # 'EPS12Z_Fcst_GEFS6Z']>0
    minus_cond = ~plus_cond
    old_method = False
    if old_method:
        a.loc[a['%s_PgefsMEAN_window=%s'%(prefix, window)]<0,[x for x in list(a) if '%s_Pgefs'%prefix in x]] = a.loc[a['%s_PgefsMEAN_window=%s'%(prefix, window)]<0,[x for x in list(a) if '%s_Pgefs'%prefix in x]]*0.000001
        #a.loc[a['%s_Pgeps0Z13_window=%s'%(prefix, window)]<0,[x for x in list(a) if '%s_Pgeps'%prefix in x]] = a.loc[a['%s_Pgeps0Z13_window=%s'%(prefix, window)]<0,[x for x in list(a) if '%s_Pgeps'%prefix in x]]*0.05
        a.loc[b['diff_0Z_0-16_Prev1-Prev3_GEPS'] < 0, [x for x in list(a) if 'Pgeps' in x]] = a.loc[b['diff_0Z_0-16_Prev1-Prev3_GEPS'] < 0, [x for x in list(a) if 'Pgeps' in x]]*0.0001
    else:
        for c in [x for x in list(a) if prefix+'_P' in x and 'window=' in x and 'MEAN' not in x]:
            ref_model = None
            for m in ['geps','gfsv16','gefs','para0','para6','para12','para18','gemco','ec','paraco']:
                if m in c:
                    ref_model = m.replace('0','').replace('12','').replace('6','').upper().replace('V16','v16')
                    break
            print('predict_eps unicors | ref_model extracted from %s was %s'%(c,ref_model))
            try:
                ref_h = c.split(ref_model.lower())[1].split('Z')[0]
            except Exception as e:
                print ('Failed on: %s'%c)
                raise
            last = 'last'
            prev = 'Prev1'
            if 'Z2' in c:
                prev = 'Prev2'
            elif 'Z3' in c:
                prev = 'Prev3'
            elif 'Z13' in c:
                prev = 'Prev3'
                last = 'Prev1'

            ref_col = 'diff_%sZ_0-16_%s-%s_%s'%(ref_h,last,prev,ref_model)
            if '9-13' in eps_col:
                ref_col = 'diff_%sZ_14-16_%s-%s_%s'%(ref_h,last,prev,ref_model)
            if ref_col not in list(b):
                continue
            a.loc[b[ref_col]<0,c] = a.loc[b[ref_col]<0,c]*0.001
    return a

def print_kpis(df,profit_col):
    real_vals = df[abs(df[profit_col]) > 0]
    calmar, max_dd = calc_calmar(real_vals[profit_col])
    sharpe = calc_sharpe(real_vals[profit_col])
    total_pnl, total_ppt = real_vals[profit_col].sum(), real_vals[profit_col].mean()
    hr = (real_vals[profit_col] > 0).mean()
    print('Profit_Col = %s | Window=%s, %s | PPT = %s | HR = %s | PNL = %s | calmar %s | Sharpe = %s | MaxDD %s' % (
    profit_col,window, y_col, total_ppt, hr, total_pnl, calmar, sharpe, max_dd))

winners = {'v8_0Zb':{
            'EPS_0Z':
                {'EPS_pred_window5':{'window':5,'ens':6,'smooth':0,'momentum':0.15,'thresh':0.35},
            'EPS_pred_window10':
            {'window':10,'ens':3,'smooth':0,'momentum':0.15,'thresh':0.4},
            'EPS_pred_window25':
                {'window':25,'ens':8,'smooth':0,'momentum':0.15,'thresh':0.4}
                 },
            'EPS_12Z':{
                'EPS12Zb_pred_window25':
                #{'window':25,'ens':6,'smooth':0,'momentum':0.15,'thresh':0.4},
                {'window':25,'ens':5,'smooth':0,'momentum':0.15,'thresh':0.4},
            'EPS12Zb_pred_window5':
                #{'window':5,'ens':8,'smooth':0,'momentum':0.15,'thresh':0.4},
                {'window':5,'ens':5,'smooth':0,'momentum':0.15,'thresh':0.4},
            'EPS12Zb_pred_window15':
                #{'window':15,'ens':8,'smooth':0,'momentum':0.15,'thresh':0.4}},
                {'window':15,'ens':5,'smooth':0,'momentum':0.15,'thresh':0.4}},
            'EC_12Z':{
                'EC12Zb_pred_window20':
                {'window':20,'ens':7,'smooth':0,'momentum':0.15,'thresh':0.5},
                'EC12Zb_pred_window50':
                {'window':50,'ens':6,'smooth':0,'momentum':0.15,'thresh':0.5}},

            'daily_0Z':{
                'Daily0Z_pred_window10':
                    {'window':10,'ens':8,'smooth':0,'momentum':0.15,'thresh':0.5},
                'Daily0Z_pred_window15':
                    #{'window':15,'ens':9,'smooth':0,'momentum':0.15,'thresh':0.5}, # old
                    {'window':15,'ens':3,'smooth':0,'momentum':0.1,'thresh':0.5},
                'Daily0Z_pred_window20':
                      #{'window': 20, 'ens': 3, 'smooth': 0, 'momentum': 0.15, 'thresh': 0.5}, old
                    {'window':20,'ens':6,'smooth':0,'momentum':0.1,'thresh':0.5},
                'Daily0Z_pred_window100':
                    #{'window': 100, 'ens': 5, 'smooth': 0, 'momentum': 0.15, 'thresh': 0.5},
                    {'window': 100, 'ens': 6, 'smooth': 0, 'momentum': 0.1, 'thresh': 0.5},

                },
            'GEFS_6Z':{
                'GEFS6Z_pred_window5':
                    #{'window':5,'ens':2,'smooth':0,'momentum':0.15,'thresh':0.5}, # ens 5??  THIS IS WITH eps_col='diff_6Z_0-16_last-Prev1_GEFS'
                    {'window':5,'ens':2,'smooth':0,'momentum':0.15,'thresh':0.5}, # ens 5?? THIS IS WITH eps_col='y_0800-1100'
                'GEFS6Z_pred_window10':
                    {'window':10,'ens':4,'smooth':0,'momentum':0.15,'thresh':0.5},
                'GEFS6Z_pred_window20':
                    {'window':20,'ens':6,'smooth':0,'momentum':0.15,'thresh':0.5},
                'GEFS6Z_pred_window50':
                    {'window':50,'ens':9,'smooth':0,'momentum':0.15,'thresh':0.5},
                'GEFS6Z_pred_window100':
                    {'window':100,'ens':5,'smooth':0,'momentum':0.15,'thresh':0.5},
                }
             },
            #  based on v8_12Z
           'v8_12Z':{
               'EPS_12Z':
                {'EPS12Z_pred_window5':
                      {'window':5,'ens':4,'smooth':0,'momentum':0.15,'thresh':0.4},
                'EPS12Z_pred_window25':
                      {'window':25,'ens':2,'smooth':0,'momentum':0.15,'thresh':0.4},
                        },
                'EC_12Z':
                {'EC12Z_pred_window50':
                      {'window':50,'ens':7,'smooth':0,'momentum':0.15,'thresh':0.4},
                'EC12Z_pred_window25':
                      {'window':25,'ens':7,'smooth':0,'momentum':0.15,'thresh':0.4},
                        },
                'EC12Z_pred_window15':
                      {'window':15,'ens':7,'smooth':0,'momentum':0.15,'thresh':0.4},
                        }
                }

def add_eps_preds(a,eps_col='diff_0Z_0-13_last-Prev1_EPS',y_col='y_0600-0800',
                  parent_preds_conf=winners,time_group='EPS_0Z',suffix='v8_0Zb'):
    if suffix not in parent_preds_conf.keys():
        return a
    preds_confs_of_suffix = parent_preds_conf[suffix]
    if time_group not in preds_confs_of_suffix.keys():
        return a

    preds_conf = preds_confs_of_suffix[time_group]
    for conf_name in preds_conf.keys():
        conf = preds_conf[conf_name]
        window = conf['window']
        preds = [conf['ens']]
        momentum,smoothing = conf['momentum'], conf['smooth']

        real_vals, all_used_cols = main_calculator(a, window, eps_col, y_col, start=START,
                                               weekdays=WEEKDAYS, time_group=time_group,
                                                   ens_preds=preds,momentum_step=momentum,
                                                   smoothing=smoothing)
        suffix_str = '' if suffix == 'v8_12Z' else 'b'
        final_col = '%s%s_pred_EnsTop%s_window=%s'%(time_group,suffix_str,conf['ens'],window)
        real_vals[final_col] = real_vals['Ens_top%s'%conf['ens']]
        a = a.merge(real_vals[['date',final_col]],on=['date'],how='outer')

    return a

if __name__ == '__main__':

    suffix = 'v8_0Zb'
    #suffix = 'v8_12Z'
    csv = os.path.join(HOME,"XYs","Enriched_XYs","XY_a_GDD_%s.csv")%suffix
    a = pd.read_csv(csv,parse_dates=['date'])
    a['y_0800-2345'] = a['y_0800-1945']+a['y_1945-2030']+a['y_2045-2145'].fillna(0)+a['y_2145-2345'].fillna(0)
    a['y_1745-2345'] = a['y_0800-2345'] - a['y_0800-1745']
    a['y_concated'] = a['y_0800-1415']+a['y_1745-1945']
    #eps_col = 'diff_0Z_0-13_last-Prev1_EPS'
    y_col = 'y_0800-1415' # y_1415-1845'
    y_col = 'y_concated'
    weekdays = WEEKDAYS
    #weekdays = [0,2,3]

    time_group = 'EC_12Z' #'GEFS_6Z'
    eps_col = 'diff_12Z_0-8_last-Prev1_EC+1d' #'diff_6Z_0-16_last-Prev1_GEFS' #y_col #'diff_12Z_0-13_last-Prev1_EPS' #y_col# ##y_col
    control_pred = eps_col#'diff_12Z_0-10_last-Prev1_EC'  # 'diff_6Z_0-16_last-Prev1_GEFS' #'EPS_daily_hybrid_pred2.2' #'diff_0Z_14-28_last-Prev3D_CFS' #'diff_0Z_0-13_last-Prev1_EPS'
    thresh = 0.35
    momentum_step = 0#.15 # MAX = 0.94

    if NORMALIZE_YS:
        a[eps_col+"_Normed"] = np.sqrt(abs(a[eps_col])) * np.sign(a[eps_col])
        eps_col = eps_col+"_Normed"

    multi_window_df = pd.DataFrame()
    used_cols_by_window = {}
    for window in [5,10,15,20,30,50]:#15,25 ,35,50,100]:
        real_vals, all_used_cols = main_calculator(a,window,eps_col,y_col,start=START,
                                                   weekdays=weekdays,time_group=time_group,momentum_step=momentum_step)
        used_cols_by_window[window] = all_used_cols
        hist = pd.Series(all_used_cols)
        mega_ens = real_vals[[x for x in list(real_vals) if 'Ens' in x]].mean(axis=1)
        real_vals['MegaEns'] = mega_ens

        for n in DEFAULT_ENS_PREDS:
            real_vals['profit_Ens%s'%n] = abs(real_vals[y_col])*np.sign(real_vals[y_col]*real_vals['Ens_top%s'%n])
            real_vals.loc[abs(real_vals['Ens_top%s'%n]) < abs(real_vals['Ens_top%s'%n]).quantile(1-thresh),'profit_Ens%s'%n] = 0
        real_vals['profit_MegaEns'] = abs(real_vals[y_col])*np.sign(real_vals[y_col]*real_vals['MegaEns'])
        real_vals.loc[abs(real_vals['MegaEns']) < abs(real_vals['MegaEns']).quantile(1-thresh),'profit_MegaEns'] = 0

        # Create y_col and control profits
        real_vals['profit_%s'%y_col] = abs(real_vals[y_col])
        real_vals.loc[abs(real_vals[y_col]) < abs(real_vals[y_col]).quantile(1-thresh),'profit_%s'%y_col] = 0
        if control_pred is not None and control_pred != '':
            real_vals['profit_%s'%control_pred] = abs(real_vals[y_col])*np.sign(real_vals[y_col]*real_vals[control_pred])
            real_vals.loc[abs(real_vals[control_pred]) < abs(real_vals[control_pred]).quantile(1-thresh),'profit_%s'%control_pred] = 0


        real_vals = real_vals[real_vals.date.dt.weekday.isin(PNL_WEEKDAYS)]
        real_vals.set_index('date')[[x for x in list(real_vals) if 'profit' in x]].cumsum().plot(title='Profits with EC, Window=%s | Thresh = %s | Y_Col = %s | EPS_Col = %s'%(window,thresh,y_col,eps_col),

                                                                                            style=['-']*6+['--']*(len(DEFAULT_ENS_PREDS)+(2 if control_pred is None else 3)-6))
        profit_col = 'profit_MegaEns'
        print_kpis(real_vals,profit_col)
        to_merge = real_vals[['date',y_col,'MegaEns','profit_MegaEns']].rename(columns={'MegaEns':'MegaEns_W=%s'%window,'profit_MegaEns':'profit_MegaEns_W=%s'%window})
        if multi_window_df.shape[0] == 0:
            multi_window_df = to_merge
        else:
            multi_window_df = multi_window_df.merge(to_merge,on=['date',y_col])
    plt.show()

    multi_window_df['MetaEns'] = multi_window_df['MegaEns_W=5']+multi_window_df['MegaEns_W=20']
    # Avg pred on windows
    multi_window_df['profit_MetaEns'] = abs(multi_window_df[y_col])*np.sign(multi_window_df[y_col]*multi_window_df['MetaEns'])
    multi_window_df.loc[abs(multi_window_df['MetaEns']) < abs(multi_window_df['MetaEns']).quantile(1-thresh),'profit_MetaEns'] = 0
    # lexicographic
    multi_window_df.loc[multi_window_df['profit_MegaEns_W=10']!=0,'profit_MegaEns_W=50'] = 0
    multi_window_df['profit_MetaEnsLexi'] = multi_window_df['profit_MegaEns_W=10']+multi_window_df['profit_MegaEns_W=50']

    print_kpis(multi_window_df,'profit_MetaEnsLexi')
    print_kpis(multi_window_df,'profit_MetaEns')

    ref_profit = 'profit_Ens5'
    d = {}
    for i, row in real_vals[real_vals['date']>=dtdt(2020,7,1)].iterrows():
        for pred in eval(row['best6']):
            if pred not in d.keys():
                d[pred] = []
            if row[ref_profit] != 0:
                d[pred].append(row[ref_profit])
    stack = []
    for pred in d.keys():
        ppt = np.nanmean(d[pred])
        n = len(d[pred])
        stack.append({'pred':pred,'ppt':ppt,'n':n})
    df = pd.DataFrame(stack)
    df.to_csv(os.path.join(HOME,"performance_analysis","","eps_ppt_by_preds.csv"))
    df_hist = pd.DataFrame(used_cols_by_window)
    df_hist.to_csv(os.path.join(HOME,"performance_analysis","","eps_prediction_used_cols.csv"))

    bb = 0




