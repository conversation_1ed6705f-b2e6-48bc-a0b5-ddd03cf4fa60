import os
import subprocess


def compress_videos(input_dir, output_dir, quality=23):
    """
    Compresses all MP4 files in the input directory and saves them in the output directory.

    Args:
        input_dir (str): Directory containing MP4 files to compress.
        output_dir (str): Directory to save the compressed MP4 files.
        quality (int): CRF value (Constant Rate Factor) for quality reduction (lower is better quality).
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Iterate through all files in the input directory
    for file_name in os.listdir(input_dir):
        if file_name.lower().endswith(".mp4"):
            input_path = os.path.join(input_dir, file_name)
            output_path = os.path.join(output_dir, file_name.replace(".mp4", "_compressed.mp4"))

            # Compress video using ffmpeg
            command = [
                r"C:\Users\\<USER>\Downloads\\ffmpeg-2024-12-04-git-2f95bc3cb3-essentials_build\\bin\\ffmpeg.exe",
                "-i", input_path,  # Input file
                "-vcodec", "libx264",  # Video codec
                "-crf", str(quality),  # Quality level
                "-preset", "medium",  # Compression speed (trade-off between speed and quality)
                output_path
            ]

            print(f"Compressing {file_name}...")
            print(command)
            subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print(f"Saved compressed file to {output_path}")
            break

# Specify input and output directories
input_directory = r"C:\\Users\\<USER>\\Documents\\Work\\videos"
output_directory = r"C:\\Users\\<USER>\\Documents\\Work\\videos"

# Call the function to compress videos
compress_videos(input_directory, output_directory, quality=28)  # Use quality=28 for lower file size
