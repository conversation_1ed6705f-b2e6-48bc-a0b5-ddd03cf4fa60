import pandas as pd




model = 'PARACO'
base_file = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\Live\Live_Wind\live_wind_%s.csv"%model
ref_file = r"C:\Users\<USER>\Documents\Work\Amazon\degdays_archive\Live\Live_Wind\live_wind_%s_1.csv"%model

df_base = pd.read_csv(base_file,parse_dates=['forecast_time','validation_day'])
df_ref = pd.read_csv(ref_file,parse_dates=['forecast_time','validation_day'])

df_base2 = df_base.merge(df_ref,on=list(df_base),how='outer').sort_values(['forecast_time','validation_day'])
# df_base2['ones'] = 1
# df_base2.groupby(df_base2['forecast_time']).sum()['ones']
df_base2.to_csv(base_file,index=False)
aa= 1