import os.path
import time

import pandas as pd
from datetime import datetime as dtdt

from Algo.Trading import positions_check
from Algo.Utils.general import get_utc_now_dt
from Algo.Trading.main_trader_v4 import trade_strategy
from IBridge.v25 import trading_utils
from IBridge.v25 import constants
from Algo.Conf.assets import Asset
from Algo.Trading.constants import WHICH_TRADER,LOOK_FOR_NEW_STRATEGIES_MODES
from Algo.Utils.files_handle import ORDER_IDS_CSV, ACTUAL_TRADES_CSV
from Algo.Utils.check_internet import internet_on as check_if_internet_on
from Algo.Utils.yaml_handle import load_trading_conf_yaml
from Algo.Notebooks.new_strategies_finder.definitions_v4 import WeeklyConfig

import argparse

GLOBAL_CLIP = 2

ALLOW_REAL_TRADING = {
                        'ens_cluster_dynamic_v4T5_S0.5_w9': False,
                        'ens_cluster_dynamic_v4_S0.5_w9x3d_loose': False,
                        'ens_cluster_dynamic_v3b_S0.5_w9':False,
                        'ens_cluster_dynamic_v3_S0.5_w9_loose':False,
                        'ens_calmar0.25_S0.5_window4': False,
                        'ens_cluster_dynamic_v4_S0.5_w9x5d_loose': False,
                        'real':False,
                        'real-1d':False,
                        'real-2d':False,
                        'dynamic_search_ens_clip2_v1':False,
                        'dynamic_search_ens_clip2_v1_dynamic':False,
                        'dynamic_search_ens_clip2_v2_no_segment':False,
                        'ens_cluster_dynamic_v5T5_S0.5_dynamicW': False
                      }

CANCEL_CONTRADICTIONS = True

ADD_NOISE_TO_ENTRY_TIME = False
ENTRY_TAKEBACK_IN_SECONDS = 0

# Aug23
# FORBIDDEN_HOURS = [13,15]
FORBIDDEN_HOURS = [] # for "look_for_new_strategies" we have no forbidden hours

FORBIDDEN_HOURS_FULL_DAY = list(range(0,24))

C25_FORBIDEEN_HOURS_BY_WEEKDAY = {0: FORBIDDEN_HOURS_FULL_DAY,
                                  1: list(range(0,8))+list(range(15,20)),
                                  2: FORBIDDEN_HOURS_FULL_DAY,
                                  3: [],
                                  4: list(range(0,8))+list(range(15,20)),
                                  }

W9x3D_FORBIDEEN_HOURS_BY_WEEKDAY = {0: [13,15],
                                  1: [13,15],
                                  2: [13,15],
                                  3: []+list(range(0,6)),
                                  4: [13],
                                  }

W9xV5_DICT_FORBIDEN_HOURS_BY_WEEKDAY = {
    0: [13,15,12], # 12 is unique to monday
    1: [13],
    2: [13,14],
    3: [13],
    4:[8,13]
    }

LOOK_FOR_NEW_STRATS_FORBIDDEN_HOURS = {
        0: [15],
         1: [15,18],
         2: [18],
         3: [6,7,8],
         4: []
         }



FORBIDDEN_HOURS_DICT = {
                        'real':FORBIDDEN_HOURS,
                        'ens_calmar0.25_S0.5_window4': C25_FORBIDEEN_HOURS_BY_WEEKDAY.get(dtdt.now().weekday(),FORBIDDEN_HOURS_FULL_DAY),
                        'ens_cluster_dynamic_v4_S0.5_w9x3d_loose': W9x3D_FORBIDEEN_HOURS_BY_WEEKDAY.get(dtdt.now().weekday(),FORBIDDEN_HOURS_FULL_DAY),
                        'ens_cluster_dynamic_v5T5_S0.5_dynamicW': W9xV5_DICT_FORBIDEN_HOURS_BY_WEEKDAY,
                        'dynamic_search_ens_clip2_v1': LOOK_FOR_NEW_STRATS_FORBIDDEN_HOURS,
                        'dynamic_search_ens_clip2_v1_dynamic': LOOK_FOR_NEW_STRATS_FORBIDDEN_HOURS,
                        'dynamic_search_ens_clip2_v2_no_segment': LOOK_FOR_NEW_STRATS_FORBIDDEN_HOURS,
                        }

def execute_trades_stack(trades_stack,order_type='MKT',utc_now_dt=None,app=None,
                         skip_closing_trade_for_last=False,
                         quantity_multiplier=1,
                         add_noise_to_entry_time=ADD_NOISE_TO_ENTRY_TIME,
                         entry_takeback_in_seconds=ENTRY_TAKEBACK_IN_SECONDS):
    if utc_now_dt is None:
        utc_now_dt = get_utc_now_dt(use_midnight=True)

    for i,trade_conf in enumerate(trades_stack):
        skip_closing_trade_for_last_tmp = skip_closing_trade_for_last
        is_last = i == len(trades_stack) - 1
        print(f'Handling trade_conf with stategy = {trade_conf["strategy"]}')
        quantity = abs(trade_conf['quantity'])

        if WHICH_TRADER == 'ib':
            app = trade_strategy(trade_conf['strategy'],trade_conf['action'],order_type,app,trade_conf['mode'],
                             trade_conf['predictor'],trade_conf['asset'],probability=trade_conf['score'],
                             quantity_override=quantity*quantity_multiplier)
        elif WHICH_TRADER == 'ibridge':
            asset = Asset(trade_conf['asset'])
            try:
                # if trade_conf['date'] > utc_now_dt.date():
                #     skip_closing_trade_for_last_tmp = True
                trade_date_str = trade_conf['date'].strftime('%Y%m%d')
                app, orders_stack = trading_utils.place_position(asset.name,asset._get_contract_dt(utc_now_dt),trade_conf['quantity']*quantity_multiplier,
                                                                 trade_conf['strategy'],
                           trade_conf['action'],'MKT',trader=app,skip_closing_trade=skip_closing_trade_for_last and is_last,
                                                                 add_noise_to_entry_time=add_noise_to_entry_time,
                                                                 entry_takeback_in_seconds=entry_takeback_in_seconds,
                                                                 trade_date=trade_date_str)
                assert orders_stack != ['error']
                trading_utils.update_ib_orders_csv(orders_stack)
            except Exception as e:
                #app.disconnect()
                print(f'Error in placing order:\n{e}')
        else:
            raise NotImplementedError()
    return app

def update_actual_positions_csv(mode = 'ens_cluster_dynamic_v4T5_S0.5_w9',
    clip = GLOBAL_CLIP,order_type='MKT',asset='QG',
         forbidden_hours=[],
         day_override=None,
        positions_file_suffix=''
         ):
    actual_trades_csv = ACTUAL_TRADES_CSV
    if asset == 'NG':
        actual_trades_csv = actual_trades_csv.replace('.csv', '_NG.csv')

    if mode in LOOK_FOR_NEW_STRATEGIES_MODES and ('no_segment' not in mode and mode.endswith('dynamic')):
        positions_file_suffix = WeeklyConfig.POSITIONS_FILE_SUFFIX_FOR_SEGMENT_BINS

    print(
        'batch trader started with mode={mode}, clip={clip}, order_type={order_type}, asset={asset}, forbidden_hours={forbidden_hours}'.format(
            mode=mode, clip=clip, order_type=order_type, asset=asset, forbidden_hours=forbidden_hours))
    # make sure we are in paper mode
    if constants.DEFAULT_ACCOUNT != constants.PAPER_ACCOUNT:
        return

    if day_override is not None:
        utc_now_dt = dtdt.strptime(day_override,'%Y%m%d')
    else:
        utc_now_dt = get_utc_now_dt(use_midnight=True)
    final_merged_df, trades_stack = positions_check.get_diff_between_actual_and_needed(utc_now_dt, mode, clip, False,
                                               positions_file_suffix=positions_file_suffix,
                                               asset=asset)
    if final_merged_df.shape[0]:
        positions_check.write_actual_positions(final_merged_df, actual_trades_csv)


def main(mode = 'ens_cluster_dynamic_v4T5_S0.5_w9',
         clip = GLOBAL_CLIP,order_type='MKT',asset='QG',
         forbidden_hours=[],
         run_fix=True,
         quantity_multiplier=1,
        positions_file_suffix=''
         ):

    if mode in LOOK_FOR_NEW_STRATEGIES_MODES and 'dynamic' in mode:
        positions_file_suffix = WeeklyConfig.POSITIONS_FILE_SUFFIX_FOR_SEGMENT_BINS

    actual_trades_csv = ACTUAL_TRADES_CSV
    if asset == 'NG':
        actual_trades_csv = actual_trades_csv.replace('.csv', '_NG.csv')

    print('batch trader started with mode={mode}, clip={clip}, order_type={order_type}, asset={asset}, forbidden_hours={forbidden_hours}'.format(mode=mode,clip=clip,order_type=order_type,asset=asset,forbidden_hours=forbidden_hours))
    # make sure we are in paper mode
    if constants.DEFAULT_ACCOUNT != constants.PAPER_ACCOUNT:
        return

    utc_now_dt = get_utc_now_dt(use_midnight=True)
    final_merged_df, trades_stack = positions_check.get_diff_between_actual_and_needed(utc_now_dt,mode,clip,False,
                                                                                       asset=asset,
                                                                                       forbidden_hours=forbidden_hours,
                                                                                       positions_file_suffix=positions_file_suffix)
    positions_check.write_actual_positions(final_merged_df, actual_trades_csv)
    print('Now its {t} | trades stack has {n} trades: {l}'.format(t=dtdt.now().strftime('%Y%m%d %H:%M:%S'),n=len(trades_stack), l=[x['strategy'] for x in trades_stack]))
    if len(trades_stack) == 0:
        print('INFO: no trades to place')
        return

    app = None

    success = False
    tries = 0
    while not success and tries < 10:
        if check_if_internet_on():
            success = True
        tries += 1
        time.sleep(12)
    if not success:
        print('ERROR: no internet connection...Skipping this iteration')
        return

    if mode in ALLOW_REAL_TRADING.keys():
        if ALLOW_REAL_TRADING[mode] or constants.DEFAULT_ACCOUNT == constants.PAPER_ACCOUNT:
            app = execute_trades_stack(trades_stack,utc_now_dt=utc_now_dt,order_type=order_type)

    print('INFO: cancelling contradictions if exists')
    if CANCEL_CONTRADICTIONS:
        try:
            trading_utils.cancel_contradicting_orders(app, iters=2)
        except Exception as e:
            print(f'ERROR: couldnt cancel contradictions due Error: {e}')

    if WHICH_TRADER == 'ibridge' and app is not None:
        try:
            app.disconnect()
        except:
            pass

    if run_fix:
        print('INFO: Checking for needed fixed')
        fix_tws_gaps(asset)
    print('INFO: writing actual positions to CSV')
    positions_check.write_actual_positions(final_merged_df,actual_trades_csv)
    try:
        app.disconnect()
    except Exception as e:
        print(f'couldnt disconnect app, Error : {e}')

def fix_tws_gaps(asset,app=None,specific_mode=None,
                 clip=GLOBAL_CLIP):
    trades_stack,app = positions_check.get_correction_trades_for_position_gaps(asset,trader=app,
                                                                               specific_mode=specific_mode,
                                                                               clip=clip)
    app = execute_trades_stack(trades_stack,order_type='MKT',skip_closing_trade_for_last=True,
                               app=app)
    try:
        app = trading_utils.cancel_contradicting_orders(app, iters=2)
        app.disconnect()
    except:
        pass
    return app

def execute_strategy(mode='dynamic_search_ens_clip2_v2_no_segment',asset='NG',
                     order_type='MKT',clip=2,quantity_multiplier=1,
                     number_of_fix_iterations=2):
    update_actual_positions_csv(mode=mode, clip=clip, order_type=order_type,
                                asset=asset,
                                forbidden_hours=FORBIDDEN_HOURS_DICT.get(mode, FORBIDDEN_HOURS))
    # main(mode, clip=2, order_type='MKT',asset='NG')
    app = None
    for iteration in range(number_of_fix_iterations):
        app = fix_tws_gaps(asset,app=app)


if __name__ == '__main__':
    # fix_tws_gaps('NG')
    # fix_tws_gaps('NG')
    #
    # trading_utils.cancel_contradicting_orders(None, iters=2)
    # main(mode='ens_cluster_dynamic_v3b_S0.5_w9',
    #          clip=2, order_type='MKT', asset='NG',
    #          forbidden_hours=[13,15],
    #          run_fix=True,
    #          quantity_multiplier=1,
    #          )
    # raise

    parser = argparse.ArgumentParser()
    parser.add_argument('--mode', type=str, default='dynamic_search_ens_clip2_v2_no_segment')
    parser.add_argument('--second_mode', type=str, default=None)
    parser.add_argument('--clip', type=int, default=2)
    parser.add_argument('--quantity_multiplier', type=int, default=1)
    parser.add_argument('--order_type', type=str, default='MKT')
    parser.add_argument('--asset', type=str, default='NG')
    parser.add_argument('--trading_conf', type=str, default='paper')
    parser.add_argument('--only_fix', type=bool, default=False)
    parser.add_argument('--only_cancel_contradictions', type=bool, default=False)
    parser.add_argument('--only_actual_positions_update', type=bool, default=False)
    parser.add_argument('--execute_strategy_only', type=bool, default=False)

    args = parser.parse_args()
    #
    # for mode in [#'ens_calmar0.25_S0.5_window4','ens_cluster_dynamic_v4T5_S0.5_w9',
    #              # 'ens_cluster_dynamic_v4_S0.5_w9x3d_loose'
    #              # 'ens_calmar0.25_S0.5_window4',
    #              # 'real',
    mode = args.mode

    #              ]:
    # update_actual_positions_csv(mode=args.mode, clip=args.clip, order_type=args.order_type,
    #                             asset=args.asset, forbidden_hours=FORBIDDEN_HOURS_DICT.get(args.mode, FORBIDDEN_HOURS),
    #                             run_fix=False, quantity_multiplier=args.quantity_multiplier)
    # main(mode, clip=2, order_type='MKT',asset='NG')
    # fix_tws_gaps(args.asset)
    # raise
    # mode = 'dynamic_search_ens_clip2_v2_no_segment'
    # mode = 'dynamic_search_ens_clip2_v1'
    # execute_strategy(mode=mode, asset='NG',
    # order_type=args.order_type, clip=args.clip,
    #                  quantity_multiplier=args.quantity_multiplier)
    # raise
    # trading_utils.cancel_contradicting_orders(None, iters=2)
    # raise

    if args.only_fix:
        fix_tws_gaps(args.asset)
        trading_utils.cancel_contradicting_orders(None, iters=2)
    elif args.only_cancel_contradictions:
        trading_utils.cancel_contradicting_orders(None, iters=2)
    elif args.only_actual_positions_update:
        update_actual_positions_csv(mode=args.mode, clip=args.clip, order_type=args.order_type,
                                asset=args.asset,forbidden_hours=FORBIDDEN_HOURS_DICT.get(args.mode,FORBIDDEN_HOURS),
                         run_fix=False,quantity_multiplier=args.quantity_multiplier)
    elif args.execute_strategy_only:
        execute_strategy(mode=args.mode,asset=args.asset,
                         order_type=args.order_type,clip=args.clip,quantity_multiplier=args.quantity_multiplier)
    else:
        current_trading_conf = load_trading_conf_yaml()['trading_mode']
        if current_trading_conf != args.trading_conf:
            print(f"Can't run batch trader with trading_conf={args.trading_conf} while current_trading_conf={current_trading_conf}")
        else:
            for mode in [mode, args.second_mode]:
                if mode is not None:
                    main(mode, clip=args.clip, order_type=args.order_type,
                                asset=args.asset,forbidden_hours=FORBIDDEN_HOURS_DICT.get(mode,FORBIDDEN_HOURS),
                         run_fix=False,quantity_multiplier=args.quantity_multiplier)



