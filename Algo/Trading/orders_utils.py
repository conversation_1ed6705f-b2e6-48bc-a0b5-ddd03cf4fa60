from datetime import datetime as dtdt
from datetime import timedelta as td
import json
from threading import Timer
import pytz
import pandas as pd
import time
import numpy as np

from Algo.Trading.constants import *
import tests.Rans_Draft as rans_ib_handle
try:
    from ibapi import (decoder, reader, comm)
    from ibapi.comm import (make_field, make_field_handle_empty)
    from ibapi.decoder import Decoder
except:
    from brokerapi import (decoder, reader, comm)
    from brokerapi.comm import (make_field, make_field_handle_empty)
    from brokerapi.decoder import Decoder
from Algo.Ybox.Analyze_Ys import _parse_ib_trades_csv
from Algo.Trading.contracts_handle import *



def cancel_order(app,order_id):
    app.cancelOrder(order_id)
    current_orders_ids_dict = json.load(open(ORDERS_JSON_FILE))
    try:
        current_orders_ids_dict[str(order_id)]['is_active'] = 0
        with open(ORDERS_JSON_FILE,"w") as f:
            json.dump(current_orders_ids_dict,f,indent=True)
    except:
        print ('couldnt change order info in the JSON... skipping it')


def cancel_all_orders(app):
    order_ids_dict = json.load(open(ORDERS_JSON_FILE))  # ,
    for order_id in order_ids_dict.keys():
        cancel_order(app,order_id)


def _add_order_to_csv(order_specs):
    df = pd.DataFrame(pd.Series(order_specs)).T
    try:
        current_df = pd.read_csv(LIVE_ORDERS_CSV)
    except:
        current_df = pd.DataFrame()
    if current_df.shape[0] == 0:
        current_df = df
    else:
        current_df = current_df.merge(df,on=list(current_df),how='outer')
    current_df.to_csv(LIVE_ORDERS_CSV,index=False)


def generate_order_id(suggested_id=None):
    #with open(orders_json_file) as orders_json_fp:
    order_ids_dict = json.load(open(ORDERS_JSON_FILE))  # ,

    success = False
    i = 0
    if suggested_id is None:
        suggested_id = 1
    while not success:
        if str(suggested_id) not in order_ids_dict.keys():
            success = True
        elif order_ids_dict[str(suggested_id)]['is_active'] == 0:
            success = True
        else:
            suggested_id += 1

    order_ids_dict[suggested_id] = {"generation_date":dtdt.now().strftime("%Y-%m-%d %H:%M:%S"), 'is_active':1}
    with open(ORDERS_JSON_FILE,"w") as orders_json_fp:
        json.dump(order_ids_dict,orders_json_fp)
    return suggested_id


def place_order(order_time_open_dt,direction, order_type='LMT',quantity=10,app=None,port=7497,asset='NG'):
    if app is None:
        app = rans_ib_handle.initialize_app(paper_trading_port=port)
    default_client_id = port
    alternative_client_id = port+1
    app.clientId = default_client_id #+dtdt.now().second

    contract = prepare_prompt_month_contract(app,asset=asset)

    #last_dt, last_open = get_last_quote(app,contract)
    last_open = 1.85

    # prepare order + contract
    buy_lmt_order = rans_ib_handle.generate_order('BUY','LMT',quantity,last_open+0.001)
    sell_lmt_order = rans_ib_handle.generate_order('SELL','LMT',quantity,last_open-0.001)
    buy_mkt_order = rans_ib_handle.generate_order('BUY','LMT',quantity,last_open+0.1)
    sell_mkt_order = rans_ib_handle.generate_order('SELL','LMT',quantity,last_open-0.1)

    direction_sign = -1 if direction == 'BUY' else 1
    lmt_order_timed = rans_ib_handle.generate_order(direction,'LMT',quantity,last_open-direction_sign*0.01,good_after_time= "%s UTC"%order_time_open_dt.strftime("%Y%m%d %H:%M:%S"))
    lmt2_order_timed = rans_ib_handle.generate_order(direction,'LMT',quantity,last_open-direction_sign*0.1,good_after_time= "%s UTC"%order_time_open_dt.strftime("%Y%m%d %H:%M:%S"))
    mkt_order_timed = rans_ib_handle.generate_order(direction,'MKT',quantity,good_after_time= "%s UTC"%order_time_open_dt.strftime("%Y%m%d %H:%M:%S"))
    if order_type == 'LMT':
        order = lmt_order_timed
    elif order_type == 'LMT2':
        order = lmt2_order_timed
    elif order_type == 'MKT':
        order = mkt_order_timed

    order_id = get_max_active_order_id_from_csv(port,order_time_open_dt.date())+1
    if np.isnan(order_id):
        order_id = get_max_active_order_id_from_csv(port, None)+1
    order_id = int(order_id)
    app.nextValidId(order_id)
    print ('Initial Order_id is : %s (ClientID = %s)'%(order_id,app.clientId))

    tries = 1
    success = False
    while not success and tries < 10:
        try:
            app.placeOrder(order_id,contract,order)
            app, is_duplicated = check_order_id_is_duplicate(app,order_id)
            if is_duplicated:
                order_id += 5*tries
                print('INFO: had duplicate order_id, will try a new order_id: %s'%order_id)
            else:
                success = True
        except ConnectionAbortedError as e:
            print ('Warning.. Try #%s | couldnt submit order due to Connection Error:\n%s'%(tries,e))
            print('Going to sleep for 5 seconds...')
            time.sleep(5)
            app.disconnect()
            app = rans_ib_handle.initialize_app(paper_trading_port=port)
            if app.clientId == default_client_id:
                app.clientId = alternative_client_id
            else:
                app.clientId = default_client_id

        finally:
            tries += 1
    order_specs = {'order_type': order_type, 'order_id': order_id, 'StartTime': order.goodAfterTime,
                   'quantity': order.totalQuantity, 'action': order.action, 'port': port}
    _add_order_to_csv(order_specs)
    #cancel_order(app,order_id)
    #cancel_all_orders(app)
    return app



### orders management

def check_order_id_is_duplicate(app,relevant_order_id):
    for i in range(100):
        try:
            text = app.msg_queue.get(block=True, timeout=0.2)
            fields = comm.read_fields(text)
            if str(relevant_order_id) in str(fields):
                yes = True
                last_val = fields[-1].decode('utf-8')
                if 'Duplicate order id' in last_val:
                    return app, True
                #print (str(fields))
        except Exception as e:
            if e.args == ():
                print ('No more messages... breaking')
                break
            print ('ERROR: Failed with %s'%e)
            continue
    return app, False

def get_open_orders(port=4001,app=None,disconnect=True):
    columns_indices = [('order_id',2),('contract',4),('type',5),('exchange',10),
                    ('contract_name',12),('direction',14),
                        ('order_type',16),('start_time',30),('quantity',15),
                       ('IB_order_id',26)]
    if app is None:
        app = rans_ib_handle.initialize_app(paper_trading_port=port)
    app.reqAllOpenOrders()
    #app.reqOpenOrders()
    orders_df = pd.DataFrame()
    for i in range(100):
        try:
            text = app.msg_queue.get(block=True, timeout=0.2)
            fields = comm.read_fields(text)
            app.decoder.interpret(fields)
            if 'BUY' in str(fields) or 'SELL' in str(fields):
                order_dict = {}
                for col_name,ref_ind in columns_indices:
                    if col_name == 'quantity':
                        bb = 0
                    order_dict[col_name] = fields[ref_ind].decode('utf-8')
                current_df = pd.DataFrame(order_dict,index=[0])
                if orders_df.shape[0] == 0:
                    orders_df = current_df
                else:
                    orders_df = orders_df.merge(current_df,on=list(current_df),how='outer')
        except Exception as e:
            print ('ERROR: Failed with %s'%e)
            continue
    if orders_df.shape[0] == 0:
        return orders_df,app
    israel_tz = pytz.timezone('Asia/Jerusalem')
    israel_rows = orders_df['start_time'].apply(lambda x: 'Israel' in x)
    if israel_rows.sum() >0:
        orders_df.loc[israel_rows, 'start_time'] = orders_df.loc[israel_rows, 'start_time'].apply(
            lambda x: x[:17])
        orders_df['start_time'] = pd.to_datetime(orders_df['start_time'])
        orders_df.loc[israel_rows, 'start_time'] = orders_df.loc[israel_rows, 'start_time'].apply(
            lambda x: israel_tz.localize(x).astimezone(pytz.utc))
        orders_df['start_time'] = pd.to_datetime(orders_df['start_time'],utc=True)
        bb = 0
    else:
        orders_df['start_time'] = pd.to_datetime(orders_df['start_time'], utc=True)
    orders_df['order_id'] = pd.to_numeric(orders_df['order_id'])
    orders_df['IB_order_id'] = pd.to_numeric(orders_df['IB_order_id'])
    if disconnect:
        app.disconnect()
    return orders_df,app


def get_max_active_order_id_from_tws(port=4001):
    df,app = get_open_orders(port)
    return df['order_id'].max()

def get_max_active_order_id_from_csv(port=4001,date=None):
    df = pd.read_csv(os.path.join(HOME,"Trading","live_orders.csv"),parse_dates=['StartTime'])
    df = df[df['port']==port]
    if date is not None:
        df = df[df['StartTime'].dt.date == date]
    return df['order_id'].max()
