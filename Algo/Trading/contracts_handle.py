from datetime import datetime as dtdt
from datetime import timedelta as td
import json
from threading import Timer
import pytz
import pandas as pd
import time
import numpy as np

import tests.Rans_Draft as rans_ib_handle
from Algo.Ybox.Analyze_Ys import _parse_ib_trades_csv
from Algo.Trading.constants import *
from Algo.Trading.orders_utils import *
from Algo.Trading.positions_check import *




def get_last_quote(app,contract,contract_dt=CONTRACT_DT):
    ref_date = dtdt.today()+td(days=2) #contract_dt - td(days=7)  #
    ugaz_hist = app.reqHistoricalData(101, contract, ref_date.strftime('%Y%m%d %H:%M:%S'),
                                      '5 D', '5 mins', 'TRADES', 0, 1, False, [])
    Timer(5, app.stop).start()
    app.run()
    lines = app.current_historical_stack
    with open(TMP_TRADES_PATH,"w") as f:
        f.writelines(lines)
    trades_df = _parse_ib_trades_csv(TMP_TRADES_PATH,CONTRACT_DT.year,CONTRACT_DT.month)
    return trades_df.iloc[-1]['date'], float(trades_df.iloc[-1]['open'])


def prepare_prompt_month_contract(app,asset='NG'):
    ref_date = dtdt.now()
    # last fridays of month should be using next month already
    if ref_date.weekday() == 4:
        ref_date += td(days=3)
    contract_dt = rans_ib_handle._get_contract_dt(ref_date,asset,delay_next_contract_days=DELAY_NEXT_CONTRACT)
    print ('Contract dt: %s'%contract_dt)
    prompt_month_local_name = rans_ib_handle._get_prompt_month_name(asset,contract_dt,contract_dt.year)
    app.current_contract_name = contract_dt.strftime("%Y%m")
    current_prompt_month_contract = rans_ib_handle.get_contract(asset, prompt_month_local_name)
    return current_prompt_month_contract

