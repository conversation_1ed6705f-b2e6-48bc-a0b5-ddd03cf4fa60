from datetime import datetime as dtdt
from datetime import time as dt_time

from datetime import timedelta as td
import json
from threading import Timer
import pytz
import pandas as pd
import time
import numpy as np

from Algo.Trading.constants import *
from Algo.Viasualization.trading_strategies_summary import summarize_positions as summarize_open_positions
from Algo.Viasualization.trading_strategies_summary import get_ensemble_positions, ENSEMBLES_DICT
from Algo.Viasualization.trading_strategies_summary import adjust_positions_to_restrictions
from Algo.Utils.files_handle import ACTUAL_TRADES_CSV, get_daily_predictions_path, ORDER_IDS_CSV,LOOK_FOR_NEW_STRATS_POSITIONS_FILE_PATH,LOOK_FOR_NEW_STRATS_NO_SEGMENTERS_POSITIONS_FILE_PATH
from Algo.Utils.general import get_utc_now_dt
from trading_utils import get_total_positions
from Algo.Utils.general import safe_write_to_csv

#### OLD METHOD

GLOBAL_CLIP = 2

def _drop_next_concatenation(d2):
    #d2['concatenated_strats'] = ''
    for i, row in d2.sort_values('opening_time').iterrows():
        tmp_cond = d2['closing_time'] < row['opening_time']+td(minutes=1)
        if d2[tmp_cond].shape[0] > 0:
            a = 1
            first_bad_i = d2[tmp_cond].index.tolist()[0]
            d2.loc[[first_bad_i], 'closing_time'] = row['closing_time']
            print ('INFO | Concatenating Strategy: %s into --->>>> %s'%(row['strategy'],d2.loc[first_bad_i, 'strategy']))
            #d2.loc[[first_bad_i], 'concatenated_strats'] = row['strategy']
            d2 = d2.loc[[x for x in d2.index.tolist() if x != i]]
            break
    return d2

def wrap_concatenation(d):
    cnt = 0
    prev_d = d
    while prev_d.shape[0] > d.shape[0] or cnt == 0:
        cnt += 1
        prev_d = d
        d = _drop_next_concatenation(d)
    return d

def aggregate_opposite_positions(opposite_positions):
    aggregated_opposite = []
    for i, seed_opposite in enumerate(opposite_positions):
        others = [(i_other, x) for i_other, x in enumerate(opposite_positions) if
                  x != seed_opposite]
        for i_other, other in others:
            if other[0] + td(minutes=1) >= seed_opposite[0] and other[0] - td(minutes=1) < seed_opposite[1]:
                # We have override from the open
                new_opposite = (
                max(seed_opposite[0], other[0]), min(seed_opposite[1], other[1]), seed_opposite[2] + other[2])
                aggregated_opposite.append(new_opposite)
            elif other[1] + td(minutes=1) >= seed_opposite[0] and other[1] - td(minutes=1) <= seed_opposite[1]:
                # We have override from the close
                new_opposite = (max(seed_opposite[0], other[0]), min(seed_opposite[1], other[1]), seed_opposite[2] + other[2])
                aggregated_opposite.append(new_opposite)
    return aggregated_opposite

def get_independent_positions(opposite_positions_inside_current_strat):
    indepndent_opposite_positions_inside_current = []
    for opposite_t_open,opposite_t_close,q_tmp in opposite_positions_inside_current_strat:
        is_real = ((opposite_t_close - opposite_t_open).total_seconds() / 60) > 1
        if is_real:
            if len(indepndent_opposite_positions_inside_current) == 0:
                indepndent_opposite_positions_inside_current.append((opposite_t_open,opposite_t_close,q_tmp))
            else:
                is_latest = opposite_t_open >= max([x[1] for x in indepndent_opposite_positions_inside_current])
                is_first = opposite_t_close <= min([x[0] for x in indepndent_opposite_positions_inside_current])
                if (is_first or is_latest):
                    indepndent_opposite_positions_inside_current.append((opposite_t_open,opposite_t_close,q_tmp))
    return indepndent_opposite_positions_inside_current

##############
### NEW METHOD

def get_relevant_trades_for_new_order(tws_mode,order_direction,t_open,
                                      t_close,quantity=1,ref_mode=None,allow_other_fixes=False,
                                      allow_contradicting_trades_fixes=False,max_minutes_gap_opening=10,
                                        allow_exceeding_quantity=False,
                                      takeback_hours=0):
    if ref_mode is None:
        ref_mode = tws_mode
    sign = -1 if order_direction == 'SELL' else 1

    now_naive = dtdt.now() - td(hours=takeback_hours)
    local_dt = LOCAL_TZ.localize(now_naive, is_dst=None)
    utc_now_dt = local_dt.astimezone(pytz.utc).replace(tzinfo=None)

    if tws_mode in ['real0.95', 'real0.9', 'real0.75', 'real0.3', 'realF0.3', 'realF0.5', 'realF0.87', 'realF0.75']:
        min_score = float(tws_mode.replace('realF', 'real').split('real')[-1])
    else:
        min_score = 0

    extreme_signal_threshold = 10000
    extreme_signal_addition_cap = 0
    if isinstance(MAX_CONTRACTS_DICT_NEW[tws_mode],tuple):
        max_position_for_sells = min(MAX_CONTRACTS_DICT_NEW[tws_mode][0],4)
        max_position_for_buys = min(MAX_CONTRACTS_DICT_NEW[tws_mode][1],4)
        if len(MAX_CONTRACTS_DICT_NEW[tws_mode]) == 4:
            extreme_signal_threshold = MAX_CONTRACTS_DICT_NEW[tws_mode][2]
            extreme_signal_addition_cap = MAX_CONTRACTS_DICT_NEW[tws_mode][3]
    else:
        max_position_for_sells = min(MAX_CONTRACTS_DICT_NEW[tws_mode], 4)
        max_position_for_buys = min(MAX_CONTRACTS_DICT_NEW[tws_mode], 4)
    maxmax_position = max(max_position_for_buys, max_position_for_sells)
    print ('Warning.... We Truncate the MAX Contracts to 4')
    actual_trades, final_df_actual = summarize_open_positions(
        day=(dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, utc_now_dt.hour)).replace(hour=0),
        mode=tws_mode, consider_broken_trades=True, naive_opening_times=False,use_15min_resolution=True,
            min_score=min_score)

    naive_trades, final_df_naive = summarize_open_positions(
        day=(dtdt(utc_now_dt.year, utc_now_dt.month, utc_now_dt.day, utc_now_dt.hour)).replace(hour=0),
            mode=ref_mode, consider_broken_trades=False, naive_opening_times=True,use_15min_resolution=True,
                min_score=min_score)
    #final_df_naive = final_df_actual.copy()

    location_of_new_trade = (final_df_naive['t_open']>=t_open-td(minutes=max_minutes_gap_opening))&(final_df_naive['t_open']<t_close-td(minutes=2))
    final_df_naive.loc[location_of_new_trade,'quantity'] = final_df_naive.loc[location_of_new_trade,'quantity']+quantity*sign
    merged_positions = final_df_naive.merge(final_df_actual, suffixes=('_naive', '_broken'), on=['t_open'], how='outer').sort_values('t_open').fillna(method='ffill')

    location_of_new_trade2 = (merged_positions['t_open'] >= t_open - td(minutes=max_minutes_gap_opening)) & (merged_positions['t_open'] < t_close - td(minutes=2))
    merged_positions['quantity_naive_capped'] = merged_positions['quantity_naive']
    merged_positions.loc[(merged_positions['quantity_naive_capped'] > max_position_for_buys),'quantity_naive_capped'] = max_position_for_buys
    merged_positions.loc[(merged_positions['quantity_naive_capped'] < -max_position_for_sells), 'quantity_naive_capped'] = -max_position_for_sells

    merged_positions['is_equal'] = merged_positions['quantity_naive_capped'] == merged_positions['quantity_broken']
    merged_positions['quantity_extreme_naive_capped'] = merged_positions['quantity_naive']
    cond_under_extreme = abs(merged_positions['quantity_extreme_naive_capped']) < extreme_signal_threshold
    merged_positions.loc[cond_under_extreme, 'quantity_extreme_naive_capped'] = merged_positions.loc[
        cond_under_extreme, 'quantity_naive_capped']
    merged_positions.loc[~cond_under_extreme, 'quantity_extreme_naive_capped'] = np.clip(merged_positions.loc[~cond_under_extreme, 'quantity_extreme_naive_capped'],
                            -max_position_for_sells - extreme_signal_addition_cap,max_position_for_buys + extreme_signal_addition_cap)

    # add capping to specific times by dict
    merged_positions = adjust_positions_to_restrictions(merged_positions,tws_mode,utc_now_dt)

    merged_positions['opening_time'] = merged_positions['t_open']
    merged_positions['closing_time'] = merged_positions['t_open'].shift(-1)
    try:
        # try filling the close by actual trades
        merged_positions['closing_time'].iloc[-1] = actual_trades['closing_time'].iloc[-1]
    except:
        # probably due to no existing trades, we just complete with next 1t
        merged_positions['closing_time'].iloc[-1] = merged_positions['closing_time'].iloc[-2]+td(minutes=15)
    merged_positions['gap'] = merged_positions['quantity_naive_capped'] - merged_positions['quantity_broken']
    # we calc extreme gap, making sure it's only for cases where no original gap was found

    merged_positions['extreme_gap'] = merged_positions['quantity_extreme_naive_capped'] - merged_positions['quantity_naive_capped']

    merged_positions['extreme_gap_initial'] = merged_positions['extreme_gap'].copy()
    # merged_positions['extreme_gap'] = merged_positions['quantity_extreme_naive_capped'] - merged_positions['extreme_gap_initial']

    merged_positions['extreme_gap'] = np.clip(merged_positions['extreme_gap'],-extreme_signal_addition_cap,extreme_signal_addition_cap)
    merged_positions['total_gap'] = merged_positions['gap']+merged_positions['extreme_gap']

    # merged_positions.loc[merged_positions['gap']!=0,'extreme_gap'] = 0
    # merged_positions.loc[(merged_positions['quantity_broken'] > max_position_for_buys), 'extreme_gap'] = 0
    # merged_positions.loc[(merged_positions['quantity_broken'] < -max_position_for_sells), 'extreme_gap'] = 0

    # modify changes to drop unwanted
    if not allow_other_fixes:
        merged_positions.loc[~location_of_new_trade2,'gap'] = 0
        merged_positions.loc[~location_of_new_trade2,'extreme_gap'] = 0
    if not allow_contradicting_trades_fixes:
        merged_positions.loc[np.sign(merged_positions['gap'])!=sign, 'gap'] = 0
        merged_positions.loc[np.sign(merged_positions['extreme_gap'])!=sign, 'extreme_gap'] = 0

    merged_positions['direction'] = 'I'
    merged_positions.loc[merged_positions['gap']>0,'direction'] = 'BUY'
    merged_positions.loc[merged_positions['gap']<0,'direction'] = 'SELL'
    merged_positions.loc[merged_positions['extreme_gap']>0,'direction'] = 'BUY'
    merged_positions.loc[merged_positions['extreme_gap']<0,'direction'] = 'SELL'

    # unite normal + extreme gaps
    merged_positions['gap'] += merged_positions['extreme_gap']

    # new 20/08/2021 we ask the total gap to be != 0, else gap is due to extreme intervention and allowed
    cond_have_gap = (abs(merged_positions['gap']) >0)&(merged_positions['total_gap']!=0)
    # 21/10 in order to avoid endless loop, I try to ask that both gaps are > 0 # todo make sure its not dropping necessary trades
    cond_have_gap = (abs(merged_positions['gap']) >0)&(abs(merged_positions['extreme_gap_initial']) >0)&(merged_positions['total_gap']!=0)
    # 14/1 after supporting smarter way for extreme gaps
    cond_have_gap = (abs(merged_positions['gap']) >0)&(merged_positions['total_gap']!=0)

    relevant_fixes = merged_positions[cond_have_gap]


    relevant_fixes['is_new_gap'] = relevant_fixes['gap'].diff() != 0 | (relevant_fixes['opening_time'].diff().dt.total_seconds()/60 > 20)
    relevant_fixes['is_concat'] = (abs((relevant_fixes['closing_time'] - relevant_fixes['opening_time'].shift(-1)).dt.total_seconds()) < 180)#|
                                        #(abs((relevant_fixes['closing_time'].shift(1) - relevant_fixes['opening_time']).dt.total_seconds()) < 180))
    relevant_fixes['new_closing_time'] = np.nan

    # since we now always have 00:00 and 23:45 we dont need the 'is_new_gap' cond (Trader V4)
    #cond = (~relevant_fixes['is_concat'])|relevant_fixes['is_new_gap']
    # changing back to previous cond but better (21/10) - it turns out it is important to keep it
    # cond = (~relevant_fixes['is_concat'])
    cond = (~relevant_fixes['is_concat']) | relevant_fixes['is_new_gap'].shift(-1)


    try:
        relevant_fixes.loc[cond,'new_closing_time'] = relevant_fixes.loc[cond,'closing_time']
    except:
        bb = 0
    relevant_fixes['new_closing_time'] = relevant_fixes['new_closing_time'].fillna(method='bfill')
    relevant_fixes['new_closing_time'] = relevant_fixes['new_closing_time'].fillna(relevant_fixes['closing_time'])

    relevant_fixes = relevant_fixes.drop_duplicates(subset=['quantity_naive', 'quantity_broken', 'quantity_naive_capped', 'is_concat', 'is_new_gap'])
    strict_cond1 = (np.sign(relevant_fixes['quantity_naive_capped']*relevant_fixes['quantity_broken']) == -1)
    strict_cond2 = abs(relevant_fixes['quantity_naive_capped'] - relevant_fixes['quantity_broken']) >= maxmax_position-2
    strict_cond2b = (abs(relevant_fixes['quantity_naive'] - relevant_fixes['quantity_broken']) > maxmax_position - 2)&(abs(relevant_fixes['quantity_broken']+relevant_fixes['gap'])<=maxmax_position)
    strict_cond3 = (relevant_fixes['quantity_broken'] == 0)|(relevant_fixes['quantity_naive_capped'] == 0)

    new_strict_cond = (abs(relevant_fixes['quantity_broken']+relevant_fixes['gap'])<maxmax_position)|(abs(relevant_fixes['quantity_broken'])>maxmax_position)
    final_strict_cond = new_strict_cond|strict_cond3|strict_cond2|strict_cond2b #strict_cond1|strict_cond2|strict_cond3|strict_cond4
    cond_no_contradictions1 = abs(relevant_fixes['quantity_naive_capped'] - relevant_fixes['quantity_broken']) >=\
                                    abs(relevant_fixes['quantity_naive_capped'] - (relevant_fixes['quantity_broken']+relevant_fixes['gap']))
    cond_no_contradictions2 = abs(relevant_fixes['quantity_extreme_naive_capped'] - relevant_fixes['quantity_broken']) >=\
                                    abs(relevant_fixes['quantity_extreme_naive_capped'] - (relevant_fixes['quantity_broken']+relevant_fixes['gap']))
    # new 1309 todo check
    relevant_fixes = relevant_fixes[cond_no_contradictions1|cond_no_contradictions2]

    if relevant_fixes.shape[0] == 0:
        print('due to strict_cond, we had nothing to handle... skipping')
        return pd.DataFrame()


    ## no need in final strict cond anymore
    #relevant_fixes = relevant_fixes[final_strict_cond]

    relevant_fixes['abs_gap'] = abs(relevant_fixes['gap'])
    relevant_fixes = relevant_fixes.sort_values(['t_open','new_closing_time','abs_gap'])
    relevant_fixes = relevant_fixes.drop_duplicates(subset=['new_closing_time'],keep='first')
    relevant_fixes['closing_time'] = relevant_fixes['new_closing_time']
    relevant_fixes['strategy'] = 'y_' + relevant_fixes['opening_time'].dt.strftime('%H%M') + '-' + relevant_fixes['closing_time'].dt.strftime('%H%M')

    # filtet nans, short trades, too close to present
    relevant_fixes = relevant_fixes[~relevant_fixes['strategy'].isna()]
    if tws_mode not in FAKE_MODES:
        relevant_fixes = relevant_fixes[relevant_fixes['closing_time']>=utc_now_dt+td(minutes=5)]
        relevant_fixes = relevant_fixes[(relevant_fixes['new_closing_time']-relevant_fixes['opening_time']).dt.total_seconds() > 180]
        relevant_fixes.loc[relevant_fixes['opening_time']<=utc_now_dt,'opening_time'] = utc_now_dt.replace(microsecond=0)+td(minutes=1)
    if not allow_exceeding_quantity:
        relevant_fixes[['total_gap','abs_gap']] = np.clip(relevant_fixes[['total_gap','abs_gap']],-quantity,quantity)
    return relevant_fixes


def get_positions_from_actual_trades_csv(day,modes='All',asset='QG'):
    """
    df should contain ['date','quantity','last_updated'?]
    :param day: dtdt with Hour=0
    :return:
    """
    if isinstance(modes,str):
        modes = [modes]

    actual_trades_csv = ACTUAL_TRADES_CSV
    if asset == 'NG':
        actual_trades_csv = actual_trades_csv.replace('.csv', '_NG.csv')
        actual_trades_csv_backup = actual_trades_csv.replace('.csv', '_NG.csv')
    try:
        actual_positions = pd.read_csv(actual_trades_csv,error_bad_lines=False)
        for col in ['last_updated','last_updated_no_leakage','date']:
            if col in list(actual_positions):
                actual_positions['date'] = pd.to_datetime(actual_positions['date'],errors='coerce')
    except:
        actual_positions = pd.DataFrame()
    if actual_positions.shape[0] > 0:
        if modes != ['All']:
            modes_cond = actual_positions['mode'].isin(modes)
        else:
            modes_cond = actual_positions['mode'].str.contains('')
        actual_positions = actual_positions[(actual_positions['date'].dt.date==day.date())&(modes_cond)]

    if len(modes) > 1 or modes == ['All']:
        actual_positions = actual_positions.groupby(['date']).agg({'quantity_actual':'sum',
                                                                   'quantity_needed':'sum',
                                                                   'quantity_no_leakage':'sum',
                                                                   'last_updated':'max',
                                                                   'last_updated_no_leakage':'max',
                                                                   })
        actual_positions['mode'] = str(modes)
    return actual_positions

def get_needed_positions_from_daily_predictions_csv(day,mode,
                                                    min_score=0.5,clip=2):
    """
    :param day: dtdt with Hour=0
    :return:
    """
    try:
        if 'ens_' in mode:
            daily_trades, needed_positions = get_ensemble_positions(day,
                ENSEMBLES_DICT[mode], takeback_hours=-8,
                    use_15min_resolution=True)
        else:
            daily_trades, needed_positions = summarize_open_positions(
                            day=day,mode=mode, consider_broken_trades=False,
                        naive_opening_times=True, use_15min_resolution=True,
                            min_score=min_score)
    except:
        print('WARNING, seems like we could not get needed positions from daily predictions csv (probably due to the file being open by the Main Flow')
        print('returning empty df (try again later)')
        raise
        return pd.DataFrame()
    needed_positions = needed_positions.rename(columns={'t_open':'date',
                                                'quantity':'quantity_needed'}).set_index('date').resample('15min').asfreq().reset_index()
    needed_positions['mode'] = mode
    needed_positions['quantity_actual'] = needed_positions['quantity_needed'].clip(-clip, clip)
    return needed_positions

def get_needed_positions_from_positions_csv(day,mode,clip=2,positions_file_suffix=''):
    """
    :param day: dtdt with Hour=0
    :return:
    """
    positions_csv = {
                    'dynamic_search_ens_clip2_v1':LOOK_FOR_NEW_STRATS_POSITIONS_FILE_PATH,
                    'dynamic_search_ens_clip2_v1_dynamic':LOOK_FOR_NEW_STRATS_POSITIONS_FILE_PATH,
                    'dynamic_search_ens_clip2_v2_no_segment':LOOK_FOR_NEW_STRATS_NO_SEGMENTERS_POSITIONS_FILE_PATH,
                     }.get(mode)
    positions_csv = positions_csv.replace('.csv',positions_file_suffix+'.csv')
    try:
        positions_df = pd.read_csv(positions_csv,parse_dates=['date'],on_bad_lines='skip')
    except:
        positions_df = pd.read_csv(positions_csv, on_bad_lines='skip')
        positions_df['date'] = pd.to_datetime(positions_df['date'],errors='coerce')
        positions_df = positions_df.dropna(subset=['date'])
    positions_df = positions_df[(positions_df['date'].dt.date==day.date())]  #&(positions_df['mode']==mode)] todo maybe in the future we'll have several modes in it

    needed_positions = positions_df.rename(columns={'final_quantity': 'quantity_needed'})
    needed_positions['mode'] = mode
    needed_positions['quantity_actual'] = needed_positions['quantity_needed'].clip(-clip, clip)
    return needed_positions


def extract_trades_from_diff(diff_df,time_col='date',diff_col='diff_actual',
                              quantity_col='quantity_actual_needed',
                              asset='QG',one_way_trades=False):
    """
    :param diff_df: a dataframe with time col and diff col
    :return:
    """
    diff_df = diff_df[[time_col, diff_col]]
    diff_df[diff_col] = np.floor(diff_df[diff_col])
    cond_change_from_prev = diff_df[diff_col].diff() != 0
    cond_change_from_next = (diff_df[diff_col].shift(-1).diff() != 0).shift(1)
    cond_change_from_prev_cumsum = cond_change_from_prev.cumsum()
    cond_change_from_next_cumsum = cond_change_from_next.cumsum()
    diff_df['t_close'] = diff_df[time_col].shift(-1)
    diff_df['t_close'] = diff_df['t_close'].fillna(diff_df['date']+td(minutes=15))

    diff_df['cond_change_from_prev_cumsum'] = cond_change_from_prev_cumsum
    diff_df['cond_change_from_next_cumsum'] = cond_change_from_next_cumsum

    trades_stack = []
    for quantity_group_name, quantity_group in diff_df.groupby('cond_change_from_prev_cumsum'):
        if quantity_group[diff_col].iloc[0] != 0:
            open_dt = quantity_group[time_col].iloc[0]
            close_dt = quantity_group['t_close'].iloc[-1]
            quantity = quantity_group[diff_col].iloc[0]
            direction = 'BUY' if quantity > 0 else 'SELL'
            strategy_str = f'y_{open_dt.strftime("%H%M")}-{close_dt.strftime("%H%M")}'
            trades_stack.append({'date': open_dt.date(), 'strategy': strategy_str,
                                   'predictor': strategy_str+'_ACTUAL', 'mode': 'real_actual',
                                    'action': direction,'quantity': abs(quantity),
                                   'asset': asset, 'real_open_time': get_utc_now_dt(),
                                   'closing_time': close_dt,'opening_time': open_dt,
                                   'needed_trades': strategy_str,
                                   'score': 1.0})
    return trades_stack

def get_diff_between_actual_and_needed(day,mode,clip=2,write_csv=True,asset='QG',
                                       round_actuals=True,forbidden_hours=[],
                                       positions_file_suffix=''):
    assert asset in {'QG','NG'}
    actual_trades_csv = ACTUAL_TRADES_CSV
    if asset == 'NG':
        actual_trades_csv = actual_trades_csv.replace('.csv','_NG.csv')
    try:
        full_actual_df = pd.read_csv(actual_trades_csv,parse_dates=['date'])
    except:
        full_actual_df = pd.DataFrame()

    if isinstance(mode,str):
        modes = [mode]
    else:
        modes = mode
    try:
        full_actual_df_today = get_positions_from_actual_trades_csv(day,modes,asset=asset)
    except:
        full_actual_df_today = pd.DataFrame()

    needed_positions_stack = []
    for mode in modes:
        if mode in LOOK_FOR_NEW_STRATEGIES_MODES:
            clip = int(mode.split('clip')[-1].split('_')[0])
            tmp_needed_positions = get_needed_positions_from_positions_csv(day,mode,clip=clip,
                                                                       positions_file_suffix=positions_file_suffix)
        else:
            tmp_needed_positions = get_needed_positions_from_daily_predictions_csv(day,mode,clip=clip).rename(columns={'t_open':'date'})
        needed_positions_stack.append(tmp_needed_positions)

    needed_positions = pd.concat(needed_positions_stack,axis=0)

    needed_positions = needed_positions.groupby('date').agg({'quantity_actual':'sum',
                                                                   'quantity_needed':'sum',
                                                                    'updated_at':'max',
                                                                   'mode':'first'}).reset_index()

    mode_str = str(modes) if len(modes) > 1 else modes[0]
    needed_positions['mode'] = mode_str
    if needed_positions.shape[0] == 0:
        return pd.DataFrame(),[]

    needed_positions.loc[needed_positions['date'].dt.hour.isin(forbidden_hours),['quantity_actual','quantity_needed']] = 0
    if round_actuals:
        needed_positions['quantity_actual'] = np.floor(abs(needed_positions['quantity_actual']))*np.sign(needed_positions['quantity_actual'])

    merged_df = needed_positions
    if 'last_updated' not in list(merged_df):
        merged_df['last_updated'] = get_utc_now_dt().strftime('%Y-%m-%d %H:%M:%S')
    else:
        merged_df.loc[merged_df['date'].dt.date == day.date(),'last_updated'] = get_utc_now_dt().strftime('%Y-%m-%d %H:%M:%S')

    merged_df = merged_df[['date', 'mode', 'quantity_actual', 'quantity_needed', 'last_updated']]
    merged_df[['quantity_actual','quantity_needed']] = merged_df[['quantity_actual','quantity_needed']].fillna(0)
    if full_actual_df_today.shape[0]>0:
        final_merged_df = full_actual_df_today.merge(merged_df,on=['date', 'mode'],how='outer',suffixes=('','_new'))
    else:
        final_merged_df = merged_df.copy()
        final_merged_df['quantity_actual_new'] = final_merged_df['quantity_actual']
        final_merged_df['quantity_needed_new'] = final_merged_df['quantity_needed']
        final_merged_df['quantity_actual'] = 0
        final_merged_df['quantity_needed'] = 0
        final_merged_df['last_updated_new'] = final_merged_df['last_updated']

    cond_changes = (final_merged_df['date'].dt.date==day.date())&(final_merged_df['mode']==mode_str)&((final_merged_df['quantity_needed']!=final_merged_df['quantity_needed_new'])|(final_merged_df['quantity_actual']!=final_merged_df['quantity_actual_new']))

    current_time_utc = get_utc_now_dt()
    cond_changes_and_future = cond_changes & (final_merged_df['date'] >= current_time_utc)
    cond_future = (final_merged_df['date'] >= current_time_utc)

    changes_df = final_merged_df.loc[cond_changes].copy()
    changes_df['quantity_actual'] = changes_df['quantity_actual'].fillna(0)
    changes_df['diff_actual'] = changes_df['quantity_actual_new'] - changes_df['quantity_actual']
    trades_stack = extract_trades_from_diff(changes_df,asset=asset)


    for c in ['quantity_actual','quantity_needed','last_updated']:
        final_merged_df.loc[cond_changes,c] = final_merged_df.loc[cond_changes,c+'_new']
    try:
        # take care of the non-leakage columns
        # final_merged_df['last_updated_no_leakage'] = final_merged_df['last_updated']
        final_merged_df.loc[cond_changes_and_future,'last_updated_no_leakage'] = final_merged_df.loc[cond_changes_and_future,'last_updated_new']
        final_merged_df.loc[cond_changes_and_future,'quantity_no_leakage'] = final_merged_df.loc[cond_changes_and_future,'quantity_actual_new']
        final_merged_df.loc[cond_changes_and_future,'last_updated_no_leakage'] = final_merged_df.loc[cond_changes_and_future,'last_updated_new']
    except:
        print('Warning, couldnt handle no_leakage quantity columns')

    if full_actual_df.shape[0] == 0:
        # final_merged_df = merged_df
        raise AssertionError('must have some reference actual positions')
    final_merged_df = final_merged_df[['date', 'mode', 'quantity_actual', 'quantity_needed', 'last_updated',
                                       'quantity_no_leakage', 'last_updated_no_leakage',
                                       ]]
    final_merged_df['date_clean'] = final_merged_df['date'].dt.date
    final_merged_df = final_merged_df.sort_values(['date_clean', 'mode','date'])
    final_merged_df = final_merged_df[['date', 'mode', 'quantity_actual', 'quantity_needed','quantity_no_leakage',
                                       'last_updated','last_updated_no_leakage',
                                       ]]
    # merge final_merged_df with full_actual_df, in case of duplicates by ['date','mode'] keep final_merged_df's version
    df_to_write = pd.concat([full_actual_df, final_merged_df], axis=0)
    df_to_write = df_to_write.drop_duplicates(subset=['date', 'mode'], keep='last')

    if write_csv:
        write_actual_positions(df_to_write,actual_trades_csv)
    return df_to_write, trades_stack

from Algo.Utils.general import get_utc_now_dt

def write_actual_positions(final_merged_df,actual_trades_csv):
    # load existing positions, and then keep a quantity column  "quantity_no_leakage"
    # that takes the previous positions until dtdt.utc_now() and from there on takes the new positions
    # TODO TODO TODO
    # existing_df = pd.read_csv(actual_trades_csv, parse_dates=['date'])
    # existing_df['file'] = 'existing'
    # if 'quantity_no_leakage' not in list(existing_df):
    #     existing_df['quantity_no_leakage'] = np.nan
    # new_positions_today = final_merged_df[final_merged_df['date'].dt.date==get_utc_now_dt().date()]
    # new_positions_today['file'] = 'new'
    # final_df = pd.concat([existing_df, new_positiond_today], axis=0)

    # moved to the safe method after several deletions of file content
    safe_write_to_csv(final_merged_df,actual_trades_csv)

def get_correction_trades_for_position_gaps(asset='NG',trader=None,
                                            specific_mode=None,
                                            clip=None):
    positions_df, trader = get_total_positions(contract_name=asset,trader=trader)
    positions_df = positions_df.rename(columns={'t_open':'date',
                                                f'{asset}_quantity_final':'quantity_tws'})
    positions_df['date'] = positions_df['date'].dt.tz_localize(None)

    actuals_df = get_positions_from_actual_trades_csv(get_utc_now_dt(),asset=asset)
    if specific_mode is not None:
        actuals_df = actuals_df.loc[actuals_df['mode']==specific_mode]
    actuals_df = actuals_df.groupby('date').agg({'quantity_actual':np.sum,
                                           'quantity_needed':np.sum,
                                           'last_updated':np.max,
                                           'last_updated_no_leakage':np.max,
                                           }).reset_index()
    actuals_df = actuals_df[actuals_df['date'].dt.minute % 15 == 0]
    if clip:
        actuals_df['quantity_actual'] = actuals_df['quantity_actual'].clip(-clip,clip)
    final_df = positions_df.merge(actuals_df,on='date',how='outer').fillna(method='ffill').sort_values('date')
    final_df['gap'] = final_df['quantity_actual'] - final_df['quantity_tws']
    trades_stack = extract_trades_from_diff(final_df, asset=asset,diff_col='gap')
    return trades_stack, trader


if __name__ == '__main__':
    # check_for_position_gaps()
    # get_correction_trades_for_position_gaps()
    # get_diff_between_actual_and_needed(dtdt(2022,9,23),'real')
    # get_diff_between_actual_and_needed(dtdt(2022,9,22),'real')
    #get_diff_between_actual_and_needed(dtdt(2022,10,5),'ens_cluster_dynamic_v4T5_S0.5_w9')
    get_diff_between_actual_and_needed(dtdt(2025,5,23),
                                       ['dynamic_search_ens_clip2_v1'],
                                       asset='NG')
    raise
    day = dtdt(2023,1,27)
    a = get_positions_from_actual_trades_csv(day,'ens_cluster_dynamic_v3b_S0.5_w9')
    aa = 1
    # todo add a multi mode listener that goes on all of the actual
    #  trades of a given day, making sure it doesn't exceed the CLIP

    # todo add