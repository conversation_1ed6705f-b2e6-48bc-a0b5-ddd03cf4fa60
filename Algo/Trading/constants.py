from datetime import datetime as dtdt
from datetime import timedelta as td
import pytz
import copy
import os
import json

from Algo.Utils.files_handle import get_ALL_PREDS_json_path, HOME, get_daily_predictions_path


LOCAL_TZ_NAME = "Asia/Jerusalem"
LOCAL_TZ = pytz.timezone(LOCAL_TZ_NAME)
CET_TZ = pytz.timezone("CET")

TZ_DELTA_FROM_TLV_TIME = 3

ALLOW_REAL_TRADING = True
WHICH_TRADER = 'ibridge' #'ib'

ORDERS_JSON_FILE = os.path.join(HOME,"Trading","orders_status.json")
TMP_TRADES_PATH = os.path.join(HOME,"Trading","tmp_trades.csv")
CONTRACT_DT = dtdt(2020,8,1)
SLEEP_WHEN_NO_CONNECTION = False

SUBMITTED_ORDERS_CSV = get_daily_predictions_path()
LIVE_ORDERS_CSV = os.path.join(HOME,"Trading","live_orders.csv")

LOCAL_TZ = pytz.timezone("Asia/Jerusalem")

NG_ASSET = 'QG' #NG'
BASE_QUANTITY_BY_ASSET = {'NG':1,'QG':1}


SYNTHETIC_MODES = ['ens_calmar3_S0.75_window4','ens_calmar10_S0.5_window7','real-1d','real-3d',
                   'ens_calmar0.25_S0.75_window4_DQv4','ens_calmar0.25_S0.75_window4_Mv2',
                    'ens_calmar0.25_S0.87_window4','ens_cluster_filter_v1','ens_cluster_filter_v2',
                   ]

LOOK_FOR_NEW_STRATEGIES_MODES = [
                                'dynamic_search_ens_clip2_v1', # look for new strategies, take positions from the look_for_new_strategies_positions_NG.csv file
                                'dynamic_search_ens_clip2_v1_dynamic', # with segmenters, but the bins end dates are dynamic
                                'dynamic_search_ens_clip2_v2_no_segment', # look for new strategies, take positions from the look_for_new_strategies_positions_NG.csv file

                                 ]

ALLOWED_MODES = ['real','real2','real2b','real_actual','realoose','realPrev1w','realPrev2w',
                 'realStrict','realP20','realHDD','paper',
                 'realG','realG2b',
                 'real0.9','real0.95','real0.75',
                    'realF0.5','realF0.87','realF0.75','realF0.3','realF',
                 'paperCDD','paperHDD']+SYNTHETIC_MODES + LOOK_FOR_NEW_STRATEGIES_MODES

PAPER_TRADING_MODES = ['paper', 'paperHDD', 'paperCDD']
FAKE_MODES = ['paper','paperCDD','paperHDD','realG',
                    'realHDD','real2','real2b','realoose','realPrev1w','realPrev2w',
                            'realStrict','realG2b','realP20',
                    ]+['real','real0.95',
                       'real0.9','real0.5',
                        'real0.75',
                       'realF0.87',
                       'realF0.75',
                       'realF0.5',
                       'realF0.3',
                       'realF',
                       'realCDD',
                       ]+SYNTHETIC_MODES

REAL_MODES = [x for x in ALLOWED_MODES if x not in FAKE_MODES]


MODES_NAMES_DICT = {'realExtWind':'real',
                    'real-1d':'real',
                    'realF0.87-1d':'realF0.87',
                    'real0.75-1d':'real0.75',
                    'real-2d':'real',
                    'real-3d':'real',
                    'real-4d':'real',
                    'realPrev1w':'real'}


MAX_CONTRACTS_DICT = {'paper':20,'realHDD':3,'real':3,'realStrict':3,'realG':2,'realGuy':1,'paperHDD':10,'paperCDD':10}
MAX_CONTRACTS_DICT_NEW = {'paper':(2,2,5,1),'real':(2,2,5,1),'real_actual':(5,5,100,1),
                          'realG':(2,2,5,1),'realGuy':(1,1),
                          'paperHDD':(2,2,5,1),'paperCDD':(2,2,5,1)}
for mode in SYNTHETIC_MODES:
    MAX_CONTRACTS_DICT_NEW[mode] = (3,3,5,1)
for mode in [x for x in ALLOWED_MODES if x not in MAX_CONTRACTS_DICT_NEW.keys()]:
    MAX_CONTRACTS_DICT_NEW[mode] = (3, 3, 5, 1)

# if NG_ASSET == 'QG':
#     # MAX_CONTRACTS_DICT_NEW['real'] = (2,2,3,1)
#     pass

MAX_CONTRACTS_DICT_NEW['realHDD'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['real2'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['real2b'] = MAX_CONTRACTS_DICT_NEW['real']

MAX_CONTRACTS_DICT_NEW['realStrict'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realP20'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realoose'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['real0.75'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['real0.9'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['real0.95'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realF0.5'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realF0.87'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realF0.75'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realF0.3'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realF'] = MAX_CONTRACTS_DICT_NEW['real']

MAX_CONTRACTS_DICT_NEW['realPrev1w'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realPrev2w'] = MAX_CONTRACTS_DICT_NEW['real']
MAX_CONTRACTS_DICT_NEW['realG2b'] = MAX_CONTRACTS_DICT_NEW['realG']

for mode in SYNTHETIC_MODES:
    MAX_CONTRACTS_DICT_NEW[mode] = MAX_CONTRACTS_DICT_NEW['realF0.75']

# (3,3,5,1) --- > min,max = (3,3), if naive >=5 add 1 bonus
SPECIAL_QUANTITIES_PREDS = {'realG':{},'realG2b':{},'real0.75':{},'realF0.87':{},'realF0.75':{},'realF0.5':{},'realF0.3':{},'realF':{},
                           'real0.9':{},'real0.95':{},
                            'real':{},'real2':{},'real2b':{},'realoose':{},
                            'realPrev1w':{},'realPrev2w':{},'real_actual':{},'realStrict':{},
                            'realHDD':{},
                            'paper':{},'paperHDD':{},'paperCDD':{}
                            }
for mode in SYNTHETIC_MODES:
    SPECIAL_QUANTITIES_PREDS[mode] = SPECIAL_QUANTITIES_PREDS['realF0.75']
for mode in [x for x in ALLOWED_MODES if x not in SPECIAL_QUANTITIES_PREDS.keys()]:
    SPECIAL_QUANTITIES_PREDS[mode] = SPECIAL_QUANTITIES_PREDS['realF0.75']

SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY = {'real': {0:[
                                                        ('0015-0030',0),('0030-0100',0),
                                                        ('0130-0230',0),('0230-0315',0),
                                                        ('0330-0400',0),('0400-0530',0),
                                                        ('0545-0715',0),('0730-0800',0),
                                                        ('0800-0900',0),('0930-0945',0),
                                                        ('1015-1030',0),('1115-1145',1),
                                                        ('1315-1330',0),
                                                        ('1430-1445',1),
                                                        ('1500-1515',1),('1515-1600',0),
                                                        ('1600-1645',2),('1645-1730',0),
                                                        ('1745-1815',1),
                                                        ('1815-1845',2),('1915-1930',0),
                                                        ],
                                                     1:[('0800-0815',0),('0815-0845',0),('0945-1030',0),
                                                        ('1100-1115',0),('1145-1215',0),
                                                        ('1245-1300',0),('1330-1345',0),('1345-1400',1),
                                                        ('1530-1545',0),#('1545-1600',1),
                                                        ('1615-1630',0),('1645-1900',0),

                                                        ],
                                                     2:[#('0300-0700', 0),('0715-0745', 0),
                                                        ('0000-0045', 0),('0045-0130', 2),('0130-0200', 0),('0200-0230', 2),
                                                         ('0230-0600', 0),
                                                         ('0600-0630', 2),('0630-0700', 1),
                                                          ('0700-0730', 0),
                                                          ('0730-0800', 2),('0800-0830', 0),
                                                         ('0830-1300', 2),('1300-1320', 1),
                                                            ('1330-1400', 2),
                                                         ('1400-1615', 0),('1700-1900', 0),
                                                            ('1930-2000', 0),
                                                        ],
                                                     3:[('0000-0015',0),('0045-0100',0),
                                                        ('0200-0230',0),('0230-0300',1),('0330-0400',1),
                                                        ('0400-0630',0),('0730-0800',0),
                                                        ('0900-0930',0),('1030-1100',0),
                                                        ('1100-1115',1),('1130-1145',0),
                                                        ('1230-1300',1),('1400-1530',0),('1530-1600',1),
                                                        ('1600-2000',0)
                                                        ],
                                                     4:[('0130-0400',0),
                                                        ('0400-0500',1),('0500-0645',2),('0645-0700',0),
                                                        ('0700-0730',2),('0730-0745',0),
                                                        ('0800-0930',0),('0930-1030',1),
                                                        ('1100-1145',2),

                                                        ('1145-1215',0),('1415-1445',0),
                                                        ('1515-1545',1),('1545-1745',0),
                                                        ('1815-1830',1),('1830-2000',0),

                                                        ]},
                                    # 'real0.75': {
                                    #     0: [],
                                    #     1: [('0000-0530',0),('0600-0630',0),('0800-2000',0),],
                                    #     2: [],
                                    #     3: [],
                                    #     4: [],
                                    # },
                            'realF0.5': {
                                        0: [('0000-0945', 0),('1015-1200', 0),('1200-1300',2),('1300-1315',5),
                                            ('1315-1400',2),('1400-1500',5),
                                            ('1500-1700',2),('1800-1900',0),('1900-1915',1),
                                            ('1930-1945',1),
                                            ],
                                        1: [('0000-0830',0),('0900-0915',0),('0915-1200',0),
                                            ('1200-1215',0),('1230-1300',1),
                                            ('1300-1600',0),('1630-1700',0),('1900-2000',0),
                                            ('1730-1800',5),('1830-1900',5)
                                            ],
                                        2: [#('0300-0700', 0),('0715-0745', 0),
                                            ('0000-0045', 0),('0115-0645', 0),('0600-0715', 1),
                                            ('0715-0730', 0),('0745-0830', 0),('0945-1000', 1),
                                            ('1100-1130', 0),('1315-1330', 0),('1345-1500', 0),('1515-1615', 0),('1630-1645', 1),('1715-1745', 0),('1745-1815', 1),('1815-1830', 2),
                                            ('1830-2000', 0),
                                            ],
                                        3: [('0000-0830',0),
                                            ('0830-1030',2),('1030-1100',0),
                                            ('1130-1145',0),('1145-1200',1),('1330-1345',1),
                                            ('1500-1745',0),('1830-2000',0),

                                            ],
                                        4: [('0000-0800', 0), ('0800-1000', 1),
                                            ('1200-1300',0), ('1400-1500',2),
                                            ('1500-1600',0),('1600-1700',1)],
                            }
                                        ,
                                    'real0.75':
                                        {
                                            0:[],#[('1100-1200', 0),('1600-1700', 0),('1900-2000', 0)],
                                            1:[],#[('1100-1200', 1),('1400-1500', 0),('1600-1700', 0),('1900-2000', 0)],
                                            2:[],#[('1230-1400',0),('1545-1645',0),('1545-1845',1)],
                                            3:[],#[('1100-1200', 0),('1400-1500', 0),('1600-1700', 0),('1900-2000', 0)],
                                            4:[],#[('1100-1200', 0),('1400-1500', 0),('1600-1700', 0),('1900-2000', 0)],
                                        },
                                    'real0.75H':
                                        {
                                            0:[('1100-1200', 0),('1600-1700', 1),('1900-2000', 0)],
                                            1:[('1100-1200', 1),('1400-1500', 0),('1500-1600', 0),('1600-1700', 1),('1900-2000', 0)],
                                            2:[('1100-1200', 2),
                                               ('1400-1500', 0),('1600-1700', 1),('1900-2000', 0)],
                                            3:[('1100-1200', 2),
                                               ('1400-1500', 0),
                                               ('1600-1700', 1),('1900-2000', 0)],
                                            4:[('1100-1200', 2),('1400-1500', 0),('1600-1700', 1),('1900-2000', 0)],

                                        },
                                    'bollinger_positions_v1':
                                        {
                                            0:[],
                                            1:[('1400-2000', 0),],
                                            2:[],
                                            3:[('0000-1400', 0),('1800-1900', 0),('1900-2345', 0),],
                                            4:[('0000-1400', 0),('1500-2000', 0),
                                               ],

                                        }
                                    }


for k in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY.keys():
    for d in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[k].keys():
        assert len([x for x in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[k][d] if
                    isinstance(x, tuple)]) == len(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[k][d])
        assert len([x for x in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[k][d] if
                    x[0].startswith('y_')])==0

REPRESENTATIVE_MODE_BY_DAY = {
                                0:'real0.75',
                                1:'real0.75',
                                2:'real0.75',
                                3:'real0.75',
                                4:'ens_d23',
                              }

# hack for combining modes todo
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'] = SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real0.75H']
#
# SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'][3] = [('0000-0515',0),('0530-0615',0),('0630-0730',0),
#                                                           ('0800-2000',0)]


SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['paperCDD'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realHDD'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realStrict'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realP20'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
# SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realG'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realG2b'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real2'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real2b'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realoose'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realPrev1w'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realPrev2w'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
if 'real0.75' not in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY.keys():
    SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real0.75'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.5'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.3'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.5'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.87'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.5'])
SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.75'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.5'])

for mode in SYNTHETIC_MODES:
    if mode not in ['ens_calmar0.25_S0.75_window4_DQv4','real-3d']:
        SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real0.75H']
    else:
        if mode == 'real-3d':
            SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = [('0800-1100',0),('1200-2000',0)]
        elif mode == 'realG':
            SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = [('0000-1200', 0), ('1500-2000', 0)]
        else:
            SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = [('1200-1500',0)]
for mode in [x for x in ALLOWED_MODES if x not in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY.keys()]:
    SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY[mode] = SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real0.75H']

# implemented special case for it
#SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realF0.5'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])

SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real0.95'] = copy.deepcopy(SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['real'])

for d in SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realHDD'].keys():
    SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY['realHDD'][d].append(('1400-2000',0))


DELAY_NEXT_CONTRACT = 2
SPECIAL_STRATS_TO_MAP_EIA = {'y_1530-1545':'y_1528-1545',
                             'y_1400-1430':'y_1400-1429'}
SPECIAL_STRATS_TO_MAP = {
                         'y_2300-2345':'y_2158-2345',
                         'y_0800-1400':'y_0800-1359',
                        'y_1300-1330':'y_1259-1330',
                        'y_1300-1315':'y_1259-1315',
                        'y_1100-1300':'y_1059-1259',
                        'y_1100-1200':'y_1059-1200',
                         'y_1100-1400':'y_1059-1359',
                        'y_1300-1400':'y_1259-1359',
                         'y_1645-1715':'y_1645-1714',
                         'y_1715-1745':'y_1714-1744'}


# ALL_PREDS = ['y_1300-1330_EC12Zb1', 'y_1300-1430_cashGEM', 'y_0800-1845_PARA0Zrolling2Strict', 'y_1445-1615_Coalpred', 'y_1745-1845_cashPARACO12z0to2', 'y_1415-1715_GFSv16pre12Z', 'y_1315-1615_PARA6Zp1', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_1515-1615_GEFS6z0to8p1$4', 'y_1445-1545_paracoSeasonal14to16', 'y_1715-1945_RansTest', 'y_1100-1400_ECFcst12zb$4', 'y_1100-1315_ECGEM', 'y_0700-0800_cfs18Z28to42', 'y_1100-1300_EPSSeasonalday15', 'y_1415-1745_PMComb', 'y_0800-1000_PARACO12zPrev1', 'y_1215-1315_eps12Zp40to8', 'y_0800-1845_MTFCombStrict', 'y_0800-1845_WindUS', 'y_1330-1430_cashCFS6zp1', 'y_1215-1315_eps12Zp40to8$2', 'y_1530-1645_pre12Z', 'y_0800-1845_MTFComb$2', 'y_1100-1400_GEFS18Zday15$7', 'y_0800-1945_GEPSCO', 'y_0800-1500_EPSComb', 'y_0730-0800_CFSCO0z0to16p24', 'y_1745-1945_Cash0dGEFS', 'y_1100-1315_cfsMon', 'y_0000-0400_GEM12Zc', 'y_0800-1200_EPSCFS', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_0700-0800_EC0zbasic', 'y_1745-1945_Coalpred', 'y_0600-0800_epsFcstStrict', 'y_1115-1815_GEPSpm', 'y_1100-1745_PARAws10mNeg', 'y_0000-0400_GEM12Zc$b', 'y_1330-1430_PARAPrev1Dx2D8to16', 'y_0700-0830_EPSPrev1D8to13', 'y_0630-0800_GEMCOPrev1D0z8to16$2', 'y_1845-1915_GFSv160zp4', 'y_1100-1200_GFSCO14D', 'y_0600-0815_GEMCO', 'y_0630-0730_GEPS0Zp1', 'y_1815-1945_epsFcstGEPFS012Zgap', 'y_0800-1845_MTFComb$4', 'y_0800-1100_WindStrat', 'y_1945-2045_4a', 'y_1415-1545_PARACashComb6zp3$4', 'y_1615-1715_cash', 'y_1745-2045_12Zb124b', 'y_0800-1945_GEFS0z0to8', 'y_1415-1715_PARA6Zp14comb', 'y_1100-1315_COALopen', 'y_1200-1400_cashGEFS0d$2', 'y_1745-1815_GEPS12z0to8', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_1100-1715_epsFcstPgeps0Z', 'y_0000-0800_GEPFS4D', 'y_1200-1500_EPStrend15', 'y_1200-1415_CFSvsEPS0Z$2', 'y_1400-1430_PARARACOam', 'y_1400-1530_CFSCOPrev12DStrict', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_0800-1400_EPShybrid1b$2', 'y_1100-1745_MonComb1021$1', 'y_1415-1545_Comb', 'y_1430-1500_GFSv16Zp40to10', 'y_1100-1745_MonComb1021', 'y_1200-1500_EC12Z0to4', 'y_0800-1400_cash2', 'y_1200-1415_CFSvsEPS0Z', 'y_1515-1915_PARA6Z3D0to10', 'y_0800-1745_PARA18z4D', 'y_1330-1415_cashPARA6Z0to2', 'y_1715-1900_WindStrat', 'y_1715-1915_GEPSComb', 'y_1100-1245_WindStrat', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_0800-1745_cfs14to211D', 'y_0730-0800_cfs12Z28to42', 'y_1645-1815_PARACO6Z12D', 'y_1315-1415_momentumWed1d', 'y_0800-1845_PARACO18zp20to13', 'y_1100-1130_GFSv164D', 'y_1200-1215_EPSpred2b', 'y_1100-1745_MonComb1021Strict', 'y_1745-1845_fritest', 'y_0800-1315_GEFS0z0to8Prev1d', 'y_0800-1845_MTFComb', 'y_0800-1200_PARA18Zp3', 'y_1145-1745_GEFS1D6zStrict', 'y_1100-1615_GFSv16z0to8p2', 'y_1315-1745_momentumWed2', 'y_1100-1145_PARA6Zp4', 'y_1200-1400_TueComb', 'y_0400-0800_EIAeffect$2', 'y_1815-2030_epsFcst12ZCor', 'y_1645-1745_TTFclosevs1to3', 'y_1145-1215_c', 'y_1215-1515_GEFStrend15$1', 'y_0800-1100_NGFopenStrict$6', 'y_1000-1200_PARACO0zp1$2', 'y_0800-1945_PARAVs1Y', 'y_0600-0800_epsFcstMEAN', 'y_1300-1545_PARA018Z0to8$3', 'y_1130-1300_ICONCashStrict', 'y_0800-1500_GEFS350to13p2', 'y_0800-1845_CFSM3M', 'y_0800-1100_WindTX', 'y_1300-1315_CFSFSCO21to351Dcomb', 'y_1300-1330_PARACO6zMean', 'y_1815-1915_34EC', 'y_1315-1415_cfs6Z14to42', 'y_0600-0900_PARA0z0to8WindTX', 'y_1000-1400_PARACO18z$1', 'y_1000-1400_PARACO18z$3', 'y_1300-1430_EC0dChange$b', 'y_1300-1330_fri', 'y_1545-1645_WindStrat', 'y_0800-1315_PARA12z8to16p3', 'y_1145-1315_fri', 'y_1815-1945_epsFcstGEPS0Z', 'y_0800-1415_eps12Z', 'y_0800-1745_PARA1D0to10', 'y_0630-0800_GEPSamloose', 'y_1400-1530_CFSCOPrev12D$b', 'y_1415-1615_GFS6Zrolling2', 'y_1300-1400_cfs0Z0to21', 'y_1400-1500_cfs0Z$2', 'y_0800-1400_EPSlastclean$b', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_0800-1100_NGFopen', 'y_1715-1915_GEFSCO3514to35p3', 'y_1415-1545_Comb$2', 'y_1100-1515_momentumMonNegative', 'y_2300-2345_GFS12Z', 'y_1100-1315_PARA12z14to16', 'y_0800-1845_WTIcloseYest', 'y_1315-1400_GEFS18z14to16', 'y_2300-2345_EPS12Z$2', 'y_1715-1745_GEFS12z0to8p2clean', 'y_1815-1945_GEFS0to8', 'y_1100-1200_GEMCash0d', 'y_1100-1515_momentumThFr', 'y_2200-2300_LongBiasCorrection222', 'y_1100-1230_EChybrid1', 'y_1715-1915_CFSCO0z21to28', 'y_0800-1845_RelStrengthSell', 'y_1200-1400_cashGEFS0d', 'y_1545-1645_PARA12z14to16p1', 'y_0800-1845_SeasonalCombWed', 'y_1315-1415_americanSeasonal', 'y_1815-1945_GEFS12Zp1', 'y_0800-1945_PARAVs1Yb', 'y_1315-1415_GEFS6zp48to16Comb', 'y_0000-0600_RANSTEST', 'y_0400-0700_CFSCOPrev1D$4', 'y_1400-1500_RansTest', 'y_1515-1845_PARAPrev1DWind$b', 'y_0800-1315_TTFcloseStrict', 'y_1430-1715_CoalYest', 'y_1415-1515_WindStrat', 'y_1000-1615_Moncash', 'y_0800-1100_NGFopen$0', 'y_1100-1430_WedComb', 'y_1130-1200_PARA12z13DComb', 'y_1230-1315_PARACO1D0zStrict', 'y_0800-1945_Seasonal', 'y_0800-1845_WindUS$2', 'y_0800-1745_yearly', 'y_1300-1330_PARA018Z', 'y_1845-1945_GEFSPrev1dp4', 'y_1345-1415_cfsMean', 'y_1215-1515_PARACO6z14to16', 'y_1130-1200_PARACO12z23DComb$7', 'y_0800-1400_CFSCOPrev1D0to16', 'y_0730-0800_EPS0to8', 'y_1515-1615_PARA6Zp24Strict', 'y_1200-1415_epsFcst12ZbCor', 'y_1100-1415_CFSCO21to351D', 'y_1230-1400_GEFSCO35Prev10to2', 'y_0600-0800_EC12z2D', 'y_0800-1100_PARA0z14to16', 'y_0800-1945_EPSPrev1D$6', 'y_1100-1400_ECFcst12zb', 'y_0800-1315_TTF', 'y_1200-1300_EPS4514to423D', 'y_1100-1300_PARA18Z3D8to16', 'y_1215-1315_CFS0z0to16rolling4', 'y_1815-1915_GEFS6Z', 'y_0800-1945_PARACO24D', 'y_0800-1100_NGFopen$6', 'y_1100-1415_GEMCOPrev1D', 'y_0800-1945_EPShybrid2.2$1', 'y_1645-1715_GEM12Z', 'y_0800-1000_eps9to13', 'y_1000-1400_PARACO18z$5', 'y_1615-1645_PARA6Z', 'y_1745-1845_GEPS12Z', 'y_1100-1945_CFSCO6z0to16Yest', 'y_1300-1315_PARA18z0to2p3', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_0800-1845_MTFCombStrict$3', 'y_1400-1745_GFSv1614D', 'y_0800-1415_TueComb$6', 'y_1315-1715_CFS6z0to16p34', 'y_1215-1315_cashEC', 'y_1100-1515_momentumMonNegativeMEAN', 'y_0800-1845_PARACO0zCombStrict', 'y_0800-1845_CFSFSCOcomb28to4214D', 'y_1200-1315_PARA6z14to16p1', 'y_0000-0600_18Z', 'y_1315-1745_momentumWed', 'y_0600-0800_PARACO0AMpred3', 'y_1100-1515_momentumThFrNegative', 'y_1415-1745_GEFSLp4b', 'y_0800-1315_PARA0zPrev1D8to16', 'y_1130-1200_PARACO0z0to8WindTXNeg', 'y_1315-1745_d', 'y_0600-0800_epsFcst', 'y_1245-1615_epsFcstGEFS12Zb', 'y_0800-1945_cfs6z10to21D', 'y_1100-1400_CFSCOcomb21to42', 'y_1745-1945_RansTest', 'y_0800-1845_WTIopen$1', 'y_1745-1815_WindStrat', 'y_1415-1745_TuesdaysMomentum', 'y_1000-1400_PARACO18z', 'y_1845-1945_PARA12Zp40to10', 'y_1000-1200_PARACO0zp1', 'y_0800-1200_WindStrat', 'y_0700-0730_PARA18Zp14', 'y_1400-1500_GEFSCO12z14to16Prev1', 'y_1815-1845_PARACO12z0to10p1', 'y_1300-1345_RansTest', 'y_1140-1200_WindStrat', 'y_0630-0800_MorningComb', 'y_0800-1845_RelStrengthSell$0', 'y_1200-1300_EPS4514to421D', 'y_1315-1745_eps9to13basic', 'y_1815-1915_PARACO12zPrev1D', 'y_1200-1415_FridaysMomentum$2', 'y_0800-1845_CFSMComb', 'y_1945-2345_d3', 'y_1400-1415_fri', 'y_1100-1400_GFSv1612zp4', 'y_1100-1200_PARACO18z', 'y_1100-1615_GFSv16Zp2', 'y_1200-1400_WindStrat', 'y_1430-1715_COALopenYestd', 'y_1745-1945_Cash0dGEFSv2', 'y_0800-1100_NGFopenStrict', 'y_0400-0800_EIAeffectNegative', 'y_1415-1615_PARACO0z14to16', 'y_1100-1745_epsFcstPcomb06Z', 'y_0800-1845_GEPSCOCombD', 'y_1000-1400_PARACO18z$0', 'y_1100-1400_GEFS18Zday15$3', 'y_1915-2030_cashGFS12z0dp1', 'y_1815-2030_epsFcst12ZbCor', 'y_0800-1845_GEFS18Zday15p3', 'y_1715-1915_CFSCO6Zp10to13Comb', 'y_1415-1845_PARACO6z1D0to10', 'y_0800-1545_cashVsettle1d', 'y_1815-1915_GFSv16Zrolling2', 'y_1415-1545_PARACashComb6zp3', 'y_0600-0700_GEPS0Zp2', 'y_1415-1645_PARACO6z', 'y_1000-1200_MonComb', 'y_1100-1615_GFSv16Zp2$2', 'y_0800-1845_weeklyMomentumd2', 'y_1000-1100_04c', 'y_0800-1100_comb', 'y_1100-1200_d', 'y_1200-1415_epsFcst12ZbCorloose', 'y_1245-1515_d', 'y_1445-1515_WindStrat', 'y_0800-1845_CFSMComb$2', 'y_0800-1845_ECEPSPrev1D', 'y_1100-1200_GEPS12zp1loose', 'y_1200-1315_PARA6zws10mTX', 'y_0000-0800_CFSvsEPS12Z', 'y_1200-1945_epsFcst12ZbCorW15', 'y_0800-1200_PARARACO0to8Comb', 'y_0800-1100_CFS0zrolling4', 'y_0800-1845_WTIopenStrict', 'y_1330-1415_GEPS12z14to16', 'y_1315-1745_EPS4528to423DStrict', 'y_1315-1745_CFSFSCOComb6Z0to16', 'y_0800-1100_NGFopenVsclose', 'y_1345-1415_WindStrat', 'y_0400-0800_EIAeffectloose', 'y_1315-1415_americanSeasonalb$6', 'y_0800-1400_EPShybrid1b', 'y_1815-1900_GEPS12Zp1', 'y_1645-1745_PARACO6zStrict2Mean', 'y_1200-1715_GEFS353D', 'y_0600-0800_GEPFS1D', 'y_1515-1845_WindTX', 'y_1545-1845_PARACO0z14to16p4$0', 'y_1645-1745_PARACO6zStrict2Mean$2', 'y_0800-1845_WTIopen$0', 'y_1145-1745_GEFS1D6z', 'y_0800-1315_TTFStrict$9', 'y_0800-1745_cfsDaily0Z', 'y_1315-1515_EPS9to13rs$2', 'y_0800-1945_PARAVs1YbStrict', 'y_0000-0600_cfs12Z28to42', 'y_1715-1845_PARACO018z0to10', 'y_1100-1515_momentumMon', 'y_1515-1845_cashFt123', 'y_1100-1400_GEPSCO12Zp30to8', 'y_1645-1745_GEFSPM', 'y_1445-1645_cfs18Z0to21', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1715-1915_GEPSComb2', 'y_1315-1615_PARA6Zp1only', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_0600-1200_GEFSCOam', 'y_1100-1745_ThuMomentumEIAWed', 'y_1215-1315_PARA6z1D0to10', 'y_0800-1945_EPSPrev1DStrict', 'y_1445-1545_PARA0z14to16p4', 'y_1100-1745_EPS4514to28rolling2', 'y_1315-1415_americanSeasonal$1', 'y_0800-1100_WindTX$4', 'y_1300-1400_PARAPrev1DWindNeg', 'y_1400-1530_CFSCOPrev12D', 'y_0800-1845_WindStrat', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_1130-1200_LongBiasCorrection', 'y_1400-1545_GEFS0z3D0to10', 'y_1100-1515_mon', 'y_1200-1415_GEMCO12zPrev1Doto10', 'y_1845-1915_GFSv160zp4$2', 'y_1730-1900_WindStrat', 'y_1715-1845_PARACO12z0to8$2', 'y_1230-1315_PARACO1D0zStrict$2', 'y_1300-1545_PARA018Z8to16', 'y_1715-1915_EPS450to13', 'y_1845-1945_CFS0z14to21p3', 'y_1745-1945_amerimix23D', 'y_1745-1845_012', 'y_1000-1100_GFSv160z2$2', 'y_1715-1915_PARACO6z0to8WindTXNeg', 'y_0800-1945_PARACOhybrid1$4', 'y_1200-1745_WindTX', 'y_1815-2030_epsFcst12Z', 'y_1200-1300_PARACO1D', 'y_1200-1330_paracoSeasonal8to16D', 'y_1515-1745_WindStrat', 'y_0400-0700_cfs18Z14to28', 'y_1200-1330_CFSpre6z', 'y_2000-2010_cashVsettle1d', 'y_1545-1715_PARA0Zp2', 'y_1300-1545_CFS6ZPrev1D28to42$1', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_0900-1200_MTFopenPrev13', 'y_1200-1300_EPS4514to421Dloose', 'y_1645-1715_PARACO6z', 'y_1900-1915_epsFcstGEFS12Z', 'y_0800-1745_cfsDaily06Z', 'y_1200-1245_monMean', 'y_0800-1945_PARAVs1Y14to16', 'y_1100-1300_WindStrat', 'y_1745-1945_PARA12Zp1', 'y_1100-1415_GEMCOPrev1DSummerMean', 'y_1100-1230_GFSv16z0to8', 'y_1415-1745_TuesdaysMomentum$1', 'y_1145-1415_cashGFS', 'y_0800-1100_NGFopenloose', 'y_0800-1845_CFSCombDaily', 'y_1245-1300_1c', 'y_1415-1845_cashVsettlediff', 'y_1400-1430_WindStrat', 'y_0600-0900_GFSCO0zp23Strict', 'y_1300-1430_PARA18Zp2', 'y_0800-1415_PARACOCombWed', 'y_0800-1215_cashGFSEPS0d', 'y_1400-1530_Comb', 'y_1400-1500_EC', 'y_0630-0730_GEPS0Zp1$2', 'y_1530-1645_GEFStrend15', 'y_0800-1100_CFSM12M', 'y_1745-2030_GEFSLCO', 'y_0800-1845_WTIopen', 'y_1100-1200_MondaysMomentum', 'y_1515-1645_CFSCO12zp24', 'y_1645-1745_PARAPrev1D8to16', 'y_0800-1315_TTFopenBasic', 'y_0800-1315_TTFcloseYestStrict', 'y_1200-1400_TueComb$1', 'y_1315-1745_3c', 'y_0600-0800_eps12Z', 'y_1315-1415_PARACO18zPrev1D8to16', 'y_0800-1200_PARARACO18ZComb', 'y_1730-1830_eps12Z9to13p1', 'y_0800-1845_NGFopenStrict2', 'y_1715-1845_cashGFS0z2d', 'y_1715-1915_GEFSComb', 'y_1200-1945_epsFcstGEPS12Zgap', 'y_1300-1430_WindStrat', 'y_1315-1415_GEPSComb3', 'y_1115-1245_PARACO6zp10to8', 'y_1130-1200_PARA12z13DComb$z', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_1415-1745_GEFSLp4', 'y_1315-1745_cfs28to42', 'y_0600-0800_GFSv160z', 'y_1315-1415_americanSeasonalb$1', 'y_1315-1745_CFSCO6Zp314to21Comb', 'y_0700-0730_GEM0Zp4', 'y_0800-1315_cashVsettle1d', 'y_0800-1745_cfs12Z0to16p3', 'y_1745-1945_GEFS12z0to8$3', 'y_1100-1200_gefsFcst6Z', 'y_1430-1500_PARApre6z', 'y_1530-1715_PARACO18z14to16', 'y_1200-1500_EPSlast', 'y_0800-1315_momentumWed1d', 'y_1615-1745_PARACO18zp1clean$2', 'y_1100-1245_RANSTEST', 'y_0800-1845_GEMCO12zComb', 'y_0800-1200_f234b', 'y_0800-1315_TTFcloseYestStrict$3', 'y_1315-1415_cfs6Z14to42on12', 'y_1315-1415_cfs6Z28to42', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_1700-1745_cashCFSCO0d', 'y_1745-1915_GEMCO12zp1', 'y_0800-1945_EPShybrid2.2Strict', 'y_1515-1645_CFSCO0Z14to2812D', 'y_1130-1300_ICONCash', 'y_1300-1430_EC0dChange', 'y_0800-1415_GEFSCombWed', 'y_1130-1415_PARACO18zp4comb', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_1845-1915_WindStrat', 'y_0900-1200_momentumBollinger4H', 'y_1815-2030_epsFcst12ZbCorStrict', 'y_1200-1300_PARA1D', 'y_0800-1845_NGFopen2', 'y_0800-1315_TTFStrict', 'y_0800-1100_NGFopen$1', 'y_1530-1645_GEFStrend15$2', 'y_0630-0800_GEMCOPrev1D0z8to16', 'y_0000-0600_18Z$2', 'y_1100-1115_WindStrat', 'y_1100-1400_GEPSCO12Zp30to8$2', 'y_1245-1545_EPS45', 'y_1415-1545_PARACO6zp1', 'y_1645-1730_GEM12Zp2', 'y_1745-1845_mon', 'y_1300-1745_daily2', 'y_1430-1500_CFSCO12zp1', 'y_1000-1100_GFSv160z2', 'y_0400-0700_GEPS12Zp2', 'y_1815-1945_CFScomb', 'y_0800-1100_PARA1DComb', 'y_1200-1515_momentumBollinger1Ht8', 'y_1415-1615_fri', 'y_1615-1745_cfsDaily0Z0to162D', 'y_0800-1000_PARACO12zPrev1$2', 'y_1815-1945_12ECGEM', 'y_0800-1845_weeklyMomentumd4', 'y_1615-1845_GEM12zp20to2', 'y_1100-1515_momentumTuWe', 'y_1845-1945_cfs12z12D', 'y_1115-1415_PARACO6Z23D', 'y_1330-1430_WindStrat', 'y_1245-1745_GEFSCO4D', 'y_1100-1215_cfs0z10to214D', 'y_1130-1200_PARACO12z23DComb$0', 'y_0800-1945_EPShybrid2.2', 'y_1200-1245_mon', 'y_1815-1945_WindStrat', 'y_1100-1945_CFS14to28Comb', 'y_0700-0745_general', 'y_1200-1315_ICONCash0dp2', 'y_1145-1745_GEFSCO1D', 'y_1315-1415_americanSeasonalb', 'y_1615-1745_234', 'y_0430-0530_RANSTES22T', 'y_0800-1945_PARACOhybrid1', 'y_1545-1845_PARACO0z14to16p4', 'y_1000-1300_cfs28to422D', 'y_1100-1515_momentumMon$0', 'y_1415-1615_americanSeasonal', 'y_1200-1400_MixComb', 'y_1645-1745_TTFclosevs1to3$2', 'y_1315-1415_mon', 'y_0800-1315_american18z4D', 'y_1615-1715_WindStrat', 'y_0800-1100_NGFopenStrict$1', 'y_1730-1745_GEFS12Z', 'y_1300-1315_PARA12zp28to16', 'y_1000-1100_GFSv160z', 'y_0700-0730_EC', 'y_1200-1745_GEFSCO35Prev1D', 'y_1130-1200_PARACO12z23DComb', 'y_0600-0700_EPS45week3Mean', 'y_1330-1415_GEPSam', 'y_1745-1945_12Zb2', 'y_1215-1245_124b', 'y_1300-1315_CFSFSCO21to351Dcomb$1', 'y_0600-1415_epsFcstPgeps0Z', 'y_1130-1300_GEFS6zp1', 'y_1200-1300_EPS4528to422D', 'y_1300-1545_PARA018Z0to8$2', 'y_0800-1845_PARACO0zComb', 'y_0400-0800_EPS4528to42', 'y_1130-1200_PARA12zp1Neg', 'y_0600-0930_cfs12Z', 'y_0800-1845_MTFComb$1', 'y_1100-1745_momentumBollinger2Ht8', 'y_0630-0800_MorningComb$2', 'y_1315-1745_momentumWed2$2', 'y_1215-1415_GEFS6z8to16Comb', 'y_1300-1330_WindStrat', 'y_1430-1500_cashGEM0zp30to2', 'y_0400-0800_EPS4528to42p1', 'y_1200-1300_EPS4514to28', 'y_1200-1745_GEFSCO35Prev1DNeg', 'y_1815-1945_GEFS0to8$3', 'y_0630-0730_cfs12z10to211D', 'y_0800-1400_EPSlast', 'y_1200-1715_GEFS35', 'y_1145-1200_WindStrat', 'y_1100-1515_momentumThFrNegative$7', 'y_1100-1315_EPSVsGEPS', 'y_0800-1945_EPSPrev1D', 'y_1315-1745_americanV3b', 'y_1515-1845_PARAPrev1DWind', 'y_0800-1945_cfs6z10to21D$1', 'y_1300-1400_WindStrat', 'y_1215-1515_GEFStrend15', 'y_0530-0630_cashGEPSEPS', 'y_1815-1945_CFSCO14to21Comb', 'y_1100-1415_WindStrat', 'y_1715-1815_PARA12Z', 'y_1130-1200_GFSv16z', 'y_0400-0700_CFSCOPrev1D', 'y_0800-1200_fMean', 'y_0800-1845_CFSCO14to28p2', 'y_1315-1515_EPS9to13rs', 'y_1100-1200_cashGEM0zp10to2', 'y_1415-1845_WindStrat', 'y_1515-1645_CFSCO0Z14to281D', 'y_1145-1230_CFSCOcomb', 'y_1245-1315_mon', 'y_1230-1300_WindStrat', 'y_0800-1100_PARA1DComb$2', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_1100-1300_cashCFS', 'y_1615-1645_EC12Zb2', 'y_1230-1315_PARACO1D0zStrict$1', 'y_1845-1945_CFS0z14to21p3$1', 'y_1745-1945_GEFS12z0to8', 'y_1100-1230_WindStrat', 'y_1515-1745_PARACO6zp3', 'y_1100-1945_RSIS', 'y_1315-1745_american024', 'y_0630-0730_GEPSam2', 'y_1715-1845_PARACO12z0to8', 'y_1130-1200_PARACO0z0to8WindTXNeg$2', 'y_0800-1100_WedComb2MEAN', 'y_1330-1445_WindStrat', 'y_1745-1815_cashPrice', 'y_0800-1315_TTFcloseYest', 'y_0800-1845_weeklyMomentumd0', 'y_0400-0800_Seasonal', 'y_1245-1415_american4D', 'y_1000-1300_GEFS12zComb', 'y_1245-1300_PARA6z', 'y_0600-0815_GEMCOSummer', 'y_0800-1845_CFS0Z14to284D', 'y_1715-1845_cashGFS0z2d$7', 'y_1700-1745_cashGEFS18Z0to2p4', 'y_0800-1845_cfs0z10to21CombD', 'y_1300-1545_PARA018Z0to8$1', 'y_1100-1515_momentumTuWeStrict', 'y_1915-1945_WindStrat', 'y_1200-1330_PARACO18zp4', 'y_0600-0700_cfs1218ZMix', 'y_1515-1545_EIA2', 'y_1615-1730_PARACO12zp2b', 'y_1100-1400_GFSv1612zp4Strict', 'y_1445-1500_RansTest', 'y_1100-1400_WindStrat', 'y_1645-1730_PARA12Z', 'y_1145-1745_GEFS1D', 'y_1200-1300_EPS4514to281D', 'y_0800-1300_PARACO12zPrev1p2', 'y_1100-1515_momentumThu1dNegative', 'y_1100-1615_gfsv160z', 'y_1245-1330_PARACO6zp10to10', 'y_0400-0600_PARA18Zp23', 'y_1415-1545_PARACO6zp10to8', 'y_1315-1745_EPS4528to423D', 'y_1100-1400_epsFcstGFSv16', 'y_1300-1515_GEFSCO6Z23D', 'y_0800-1200_mon3', 'y_1845-2030_EC12Z', 'y_1800-1900_CFSFSCO6Zcomb', 'y_1715-1915_PARACO12Z1D8to16', 'y_1845-1900_GEPS12Zp4', 'y_1615-1745_cfsDaily0Z0to162DMean', 'y_1900-1945_RansTest', 'y_0800-1315_TTFStrict$3', 'y_1845-2030_GEFS12Z', 'y_1100-1300_NGFmidday', 'y_0630-0800_GEPSamloose$1', 'y_1100-1400_GEFS18Zday15', 'y_0800-1400_EPSlastclean', 'y_1645-1715_GEM12Zloose', 'y_0800-1845_WTImidday', 'y_1000-1100_GFSv160z2$3', 'y_1130-1745_PARACO6zp10to16', 'y_0000-0800_GEFS352D', 'y_1200-1245_6Z', 'y_1545-1645_GFSv16z', 'y_2300-2345_EPS12Z', 'y_0600-0700_EPS45week3', 'y_1315-1745_234new', 'y_1100-1200_EPS4528to42b', 'y_1100-1515_momentumThFrNegative$2', 'y_1100-1200_PARACO18zp1', 'y_1415-1845_GEFSCO3521to28p3', 'y_0000-0800_GEPFS4D$0', 'y_0800-1415_ThursdaysMomentum', 'y_0800-1415_ThursdaysMomentumStrict', 'y_1315-1515_PARACO18zp30to8', 'y_1300-1345_cfs28to42', 'y_1100-1315_amerimix23D', 'y_0800-1315_GEFSCO354D', 'y_1330-1415_cashPARA6Z0to2$1', 'y_1130-1400_WindStrat', 'y_1130-1200_PARACO6z0to2prev1', 'y_0800-1845_CFSCOCombWed', 'y_1245-1415_cfs28to42', 'y_0800-1200_GEPS0Z12', 'y_1400-1630_GFSEFS6z', 'y_0600-0800_CFSPrev1D21to35', 'y_1100-1430_WedCombStrict', 'y_0800-1845_weeklyMomentumd3', 'y_0800-1200_PARA18Zp3Strict', 'y_1200-1245_6Zclean', 'y_1815-2030_cfs0Z28to42', 'y_0800-1845_NGFmidday2', 'y_1400-1500_cfs0ZMean', 'y_1915-2030_GEFSCFSCOcomb', 'y_1815-1915_GEMCO0zPrev1D0to10', 'y_0800-1415_TueComb', 'y_1530-1645_GEFStrend156z', 'y_0700-0800_EC0zbasic$3', 'y_0400-0800_EIAeffect', 'y_1615-1645_PARAGEM', 'y_0800-1000_1c', 'y_1200-1315_PARA012Z0to8', 'y_1415-1615_PARAPrev1D', 'y_1845-1945_RansTest', 'y_1745-2045_GEFS12Z', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_1300-1545_PARA018Z0to8', 'y_1815-1900_GFScash', 'y_1245-1415_wed4dailies', 'y_1515-1615_GEFS6z0to8p1', 'y_1200-1415_FridaysMomentum', 'y_0800-1845_WTIopen$6', 'y_1100-1615_GFSv16Zp2Strict', 'y_1745-1915_WindStrat', 'y_1100-1745_PARACO12Zp2', 'y_1200-2030_epsFcstGEFS6Z', 'y_0800-1100_PARA1DCombStrict', 'y_0800-1845_CFS0Z14to28Yest', 'y_1200-1745_GEFS6zfcst', 'y_0800-1415_PARACO0zp3', 'y_0800-1845_weeklyMomentumd1', 'y_1230-1315_RansTest', 'y_0800-1415_EPS0zp2', 'y_1100-1245_cashGFS0d', 'y_1100-1315_EPSVsGEPStrict', 'y_1415-1645_PARACO6zStrict', 'y_1415-1645_23', 'y_1430-1530_WindStrat', 'y_0800-1845_RelStrengthSellloose', 'y_1100-1315_WindStrat', 'y_1000-1200_eps12Z9to13', 'y_1415-1745_monComb', 'y_1230-1330_WindStrat', 'y_1000-1200_MonCombStrict', 'y_1230-1315_6ZPM', 'y_0800-1200_GEM0Z', 'y_1100-1200_GEFS0z0to8', 'y_1545-1645_EIAeffect', 'y_1745-1945_PARA12Zp4', 'y_0000-0600_cash', 'y_1200-1245_6Zb', 'y_1130-1745_PARACO6zp10to10', 'y_1615-1745_PARACO18zp1clean', 'y_1745-1945_PARACO0zp13', 'y_1200-1745_CFSCOp234', 'y_1330-1415_CFSCO6Zp18to16', 'y_1415-1845_PARACO18zp10to10', 'y_1100-1300_NGFmidday$b', 'y_0800-1315_TTFclose', 'y_0800-1945_PARAVs1Yb$2', 'y_1300-1315_CFSCO6Z21to353D', 'y_0800-1845_CFSCO0z14to28p1', 'y_0800-1945_EPSPrev1D$1', 'y_1400-1500_cfs0Z']
# ALL_PREDS = ['y_2300-2345_GFS12Z', 'y_1100-1300_PARA18Z3D8to16Strict', 'y_1100-1615_GFSv16z0to8p2', 'y_0600-1415_epsFcstPgeps0Zc2$b', 'y_0800-1400_EPShybrid1b$2', 'y_1200-1300_EPS4514to421D', 'y_1845-1915_GFSv160zp4$2', 'y_1245-1415_cfs28to42', 'y_1200-1245_6Zclean$1', 'y_1445-1615_Coalpred', 'y_1200-1300_EPS4514to281D', 'y_1100-1315_cfsMon', 'y_0800-1845_CFSCombDaily$1', 'y_1815-1945_epsFcstGEPFS012Zgap$b', 'y_0800-1945_PARAVs1Yseasonal', 'y_0800-1545_cashVsettle1d', 'y_0800-1845_CFSMComb$1', 'y_0800-1945_PARACOhybrid1', 'y_1200-1300_PARA1D', 'y_1100-1400_epsFcstGFSv16', 'y_0800-1845_COALopenclose2d1', 'y_1200-1245_6Z', 'y_1515-1645_CFSCO12zp24', 'y_1200-1315_PARA012Z0to8', 'y_0800-1000_PARACO12zPrev1$2', 'y_1400-1500_GEFSCO12z14to16Prev1$0', 'y_0600-0815_GEMCO', 'y_1715-1845_PARACO12z0to8', 'y_1845-1945_CFS0z14to21p3$1', 'y_1815-2030_cfs0Z28to42', 'y_1815-1915_GFSv16Zrolling2', 'y_1100-1415_GEMCOPrev1D', 'y_1100-1515_momentumThFrNegative$8', 'y_1430-1500_GFSv16Zp40to10$2', 'y_0800-1945_EPShybrid2.2$1', 'y_0000-0800_clustersAvg18Z', 'y_1230-1315_PARACO1D0zStrict$1', 'y_1745-1945_GEFS12z8to16', 'y_1315-1745_americanV3b', 'y_1000-1100_GFSv160z2$2', 'y_1000-1400_PARACO18z$z', 'y_0000-0600_RANSTEST', 'y_1815-1945_CFSCO14to21Comb', 'y_0800-1945_PARACOhybrid1$9', 'y_1715-1915_CFSCO6Zp10to13Comb', 'y_0800-1945_EPShybrid2.2$5', 'y_0715-0745_EC0Zp12', 'y_1100-1300_PARA18Z3D8to16', 'y_0600-0800_epsFcstMEAN', 'y_0700-0800_cfs18Z28to42', 'y_0800-1945_EPShybrid2.2', 'y_1515-1745_PARACO6zp3', 'y_1745-1845_012', 'y_1845-1945_GEFSPrev1dp4$W', 'y_0000-0800_GEPFS4D', 'y_0400-0700_cfs18Z14to28', 'y_1415-1645_PARACO6zStrict', 'y_0800-1845_cfs0z10to21CombD', 'y_0800-1945_Seasonal$4', 'y_0800-1415_TueComb', 'y_1845-1945_GEPS12z0to8p1', 'y_1430-1500_PARApre6z$7', 'y_1100-1515_momentumMon', 'y_1415-1845_GEFSCO3521to28p3', 'y_1230-1315_PARACO1D0zStrict$5', 'y_0800-1000_eps9to13p2', 'y_1300-1430_EC0dChange$b', 'y_0700-0830_EPSPrev1D8to13', 'y_0800-1745_GEFSFcst12z', 'y_1215-1315_CFS0z0to16rolling4', 'y_1100-1315_EPSVsGEPStrict', 'y_0400-0700_GEPS12Zp2', 'y_1715-1915_GEFSComb', 'y_1200-1945_epsFcstGEPS12Zgap$b', 'y_1330-1415_GEPS12z14to16$4', 'y_1100-1300_NGFmidday$b', 'y_0800-1000_PARACO12zPrev1', 'y_1645-1715_GEM12Zloose', 'y_1000-1100_04c', 'y_1130-1200_PARACO12z23DComb', 'y_0800-1845_EPSsouthStrict', 'y_0800-1845_WTIopen$6', 'y_1515-1715_GFSEFSCO0Zp20to16$b', 'y_0800-1945_PARAVs1Y', 'y_1745-1945_PARA12Zp4', 'y_1100-1945_CFSCO6z0to16Yest', 'y_1415-1515_WindStrat', 'y_1200-1300_EPS4514to421Dloose', 'y_1100-1945_RSIS', 'y_0800-1845_clustersBest0Z6h', 'y_0800-1845_MTFComb$1', 'y_0800-1945_Seasonal', 'y_1515-1745_GEFSCO3514to21Prev1D$4', 'y_1330-1430_cashCFS6zp1', 'y_1915-1945_WindStrat', 'y_0630-0800_GEMCOPrev1D0z8to16$2', 'y_1615-1715_WindStrat', 'y_0900-1200_MTFopenPrev13', 'y_1315-1745_EPS4528to423DStrict$4', 'y_1515-1915_PARA6Z3D0to10', 'y_0800-1945_PARAVs1Yb$3', 'y_1430-1715_COALopenYestd', 'y_0000-0800_clustersAvg18Z$n', 'y_1300-1315_CFSFSCO21to351Dcomb', 'y_1200-1215_EPSpred2b', 'y_1100-1200_GEFS0z0to8', 'y_1715-1745_GEFS12z0to8p2clean', 'y_1515-1615_PARA6Zp24Strict', 'y_1300-1430_cashGEM', 'y_0800-1845_GEPSCOCombD', 'y_0800-1845_SeasonalCombWed', 'y_1200-1245_6Zb', 'y_0800-1845_RelStrengthSell', 'y_1100-1300_cashCFS$2', 'y_1300-1315_CFSFSCO21to351Dcomb$1', 'y_1200-1400_cashGEFS0d$2', 'y_1645-1815_PARACO12Z12D', 'y_1815-1945_12ECGEM', 'y_1745-1845_GEPS12Z', 'y_1515-1845_cashFt123', 'y_0800-1845_CFSEFSCOcomb14to42', 'y_1445-1545_PARA0z14to16p4$1', 'y_1315-1415_EPS450to13Comb', 'y_1430-1500_cashGEM0zp30to2', 'y_0600-0700_cfs1218ZMix', 'y_1115-1245_PARA6Zp30to16Strict', 'y_1200-1400_TueComb', 'y_1100-1315_EPSVsGEPS', 'y_1400-1430_WindStrat', 'y_1415-1615_GFS6Zrolling2', 'y_1300-1400_cfs0Z0to21', 'y_1715-1815_PARA12Z', 'y_1100-1615_gfsv160z', 'y_0800-1945_PARAVs1Yb$2', 'y_1300-1315_CFSFSCO21to351Dcomb$b', 'y_0800-1745_GEFSFcst12zStrict', 'y_0630-0730_GEPS0Zp1', 'y_0600-0900_PARA0z0to16WindTX', 'y_0800-1300_PARACO12zPrev1p2', 'y_1815-1945_epsFcstAll06Z', 'y_0800-1415_EPS0zp2', 'y_1115-1145_GEFS6z0to8', 'y_0800-1100_NGFopenStrict$1', 'y_1545-1645_EIAeffect', 'y_1100-1515_momentumThFrNegative$7', 'y_0800-1845_weeklyMomentumd1', 'y_0800-1315_TTFopenBasic', 'y_1715-1845_cashGFS0z2d', 'y_1745-1945_PARA12Zp1', 'y_1700-1745_cashCFSCO0d', 'y_1200-1400_cashGEFS0d', 'y_1100-1400_GEFS18Zday15Strict', 'y_0800-1845_ECEPSPrev1D', 'y_1230-1315_PARACO1D0zStrict$2', 'y_0800-1845_0Dtrend$b', 'y_0730-0800_EPS0to8', 'y_1300-1545_PARA018Z0to8$3', 'y_1615-1745_cfsDaily0Z0to162DMean', 'y_0630-0730_GEPSam2', 'y_1300-1400_PARAPrev1DWindNeg', 'y_1745-1845_mon', 'y_1245-1745_SeasonalComb2$b', 'y_1100-1745_EPS9to13comb', 'y_0900-1200_momentumBollinger4H', 'y_1300-1330_fri$4', 'y_1245-1515_d', 'y_1745-1845_cashPARACO12z0to2', 'y_1130-1200_GFSv16z$4', 'y_0800-1200_GEPS0Z12', 'y_1000-1400_PARACO18z$5', 'y_0600-0900_PARA0z0to8WindTX', 'y_1100-1200_GFSCO14D', 'y_0800-1315_TTFStrict$4', 'y_1200-1315_CFS0z0to16p34', 'y_0800-1845_0Dtrend', 'y_1300-1345_cfs28to42', 'y_0800-1030_PARApre6Z', 'y_1415-1845_PARACO18zp10to10', 'y_0800-1945_PARACOhybrid1$4', 'y_1315-1515_EPS9to13rsStrict', 'y_1315-1745_EPS4528to423DStrict', 'y_1515-1745_PARACO0Zp20to16', 'y_1745-1945_amerimix23D', 'y_1745-1945_Cash0dGEFS', 'y_0800-1100_NGFopen$1', 'y_1100-1515_momentumThFrNegative', 'y_1530-1645_pre12Z', 'y_1400-1500_RansTest', 'y_1100-1615_GFSv16Zp2$2', 'y_0800-1845_PARACO0zp4W', 'y_1200-1300_EPS4528to422D$4', 'y_1315-1415_GEPSComb3$2', 'y_0700-0745_general', 'y_1100-1400_GEPSCO12Zp30to8', 'y_0800-1845_MTFComb', 'y_1130-1400_WindStrat', 'y_1315-1745_EPS4528to423D', 'y_1345-1415_WindStrat', 'y_0800-1315_TTFcloseYestStrict$3', 'y_1645-1815_PARACO6Z12D', 'y_1815-2030_epsFcst12Z', 'y_1100-1230_EChybrid1', 'y_0800-1945_EPSPrev1DStrict', 'y_1130-1200_PARACO6z0to2prev1', 'y_2300-2345_EPS12Z$2', 'y_1300-1315_PARA18z0to2p3$3', 'y_0800-1315_TTFcloseStrict', 'y_0700-0730_EC', 'y_0800-1945_Seasonal$9', 'y_0800-1315_TTFcloseYest', 'y_1300-1745_daily2', 'y_1100-1715_epsFcstPgeps0Z$b', 'y_0800-1945_PARAVs1Yb', 'y_0800-1100_PARA1DComb$2', 'y_1315-1745_cfs28to42', 'y_0800-1100_CFS0zrolling4', 'y_1530-1615_GEFS6zp18to16Strict', 'y_0000-0800_GEFS352D$1', 'y_1000-1200_PARACO0zp1$2', 'y_1130-1200_PARACO12z23DComb$0', 'y_1115-1415_PARACO6Z23D$1', 'y_1200-1745_GEFSCO35Prev1D', 'y_1715-1745_GEFS12z0to8p2clean$b', 'y_0400-0800_Seasonal', 'y_0800-1945_EPSPrev1D$1', 'y_0715-0745_EC0Zp34', 'y_0800-1200_PARA18Zp3Strict', 'y_1130-1745_PARACO6zp10to10$1', 'y_1230-1315_PARACO18z14to16p1', 'y_0800-1100_comb', 'y_1645-1745_PARAPrev1D8to16', 'y_1100-1400_CFSCOcomb21to42', 'y_1300-1545_PARA018Z0to8', 'y_0800-1100_PARA0z14to16', 'y_0800-1845_WTIopenStrict', 'y_1330-1415_GEPSam', 'y_1615-1745_cfs', 'y_1715-1845_PARACO12z0to8$2', 'y_1200-1415_FridaysMomentum$2', 'y_1100-1200_PARACO18zp1$z', 'y_0800-1745_PARA18z4D$2', 'y_0800-1400_EPSlastclean', 'y_1230-1315_GEFSCO18z0dCash', 'y_1115-1245_PARA6Zp30to16', 'y_0600-0800_epsFcstGEPFS0Zgap', 'y_1415-1645_23', 'y_0600-0800_epsFcstStrict', 'y_1415-1745_monComb', 'y_1745-1945_GEFS12z0to8$3', 'y_1100-1745_momentumBollinger2Ht8', 'y_1130-1200_PARACO0z0to8WindTXNeg$2', 'y_1100-1115_WindStrat', 'y_1430-1530_WindStrat', 'y_0800-1845_PARACO0zCombStrict', 'y_1100-1745_EPS4514to28rolling2', 'y_0800-1100_CFSM12M', 'y_1545-1645_PARACashComb6zp3Negative', 'y_0000-0400_GEM12Zc$b', 'y_1745-1945_PARA12Zp1Strict$b', 'y_1100-1430_WedCombStrict', 'y_0800-1400_cash2', 'y_0600-0900_PARA0z0to8WindTX$4', 'y_1200-1430_WindTXPARACO0zStrict', 'y_1745-1945_PARA12Zp4$2', 'y_1245-1615_epsFcstGEFS12Zb', 'y_1645-1745_TTFclosevs1to3$3', 'y_1100-1145_PARA6Zp4$b', 'y_1745-1945_GEFS12z8to16$W', 'y_1315-1415_americanSeasonalb$6', 'y_1100-1615_GFSv16z0to8p2Strict', 'y_0800-1845_WTIopen$0', 'y_0800-1100_WindTX', 'y_1000-1400_PARACO18z$1', 'y_1645-1745_PARACO6zStrict2Mean', 'y_1615-1730_PARACO12zp2b', 'y_2000-2010_cashVsettle1d', 'y_1515-1715_GFSEFSCO0Zp20to16', 'y_1200-1515_momentumBollinger1Ht8', 'y_1415-1645_PARACO6z', 'y_0400-0800_EPS4528to42p1', 'y_1545-1715_PARA0Zp2', 'y_1515-1645_CFSCO0Z14to2812D', 'y_1515-1745_GEFSCO3514to21Prev1D', 'y_1445-1500_RansTest', 'y_1515-1745_GEFSCO3514to28Prev1D', 'y_0800-1845_CFSMComb', 'y_1100-1815_cfsDaily0Z0to16WinterStrict', 'y_1915-2030_cashGFS12z0dp1', 'y_1100-1745_MonComb1021Strict', 'y_0800-1945_PARACO24D', 'y_0400-0800_EIAeffectNegative', 'y_1200-1415_FridaysMomentum', 'y_1530-1645_EPStrend15b', 'y_1230-1315_PARACO1D0zStrict$0', 'y_0800-1845_PARA0zp12W', 'y_1200-1715_GEFS35', 'y_1100-1145_PARA6Zp4', 'y_1100-1315_WindStrat', 'y_0800-1400_EPShybrid1b', 'y_1400-1745_GFSv1614D', 'y_0800-1845_PARA0zp1W', 'y_0800-1845_COALopenclose1d', 'y_0800-1200_GEM0Z$2', 'y_1100-1400_ECFcst12zb$4', 'y_1300-1330_PARA018Z', 'y_0800-1415_TueComb$2', 'y_1315-1745_234new', 'y_1300-1545_PARA018Z0to8$1', 'y_0800-1945_EPSPrev1D', 'y_1815-1915_34EC', 'y_1515-1745_GEFS18z14d', 'y_0800-1845_weeklyMomentumd3', 'y_1100-1400_GEFS18Zday15SuperStrict', 'y_1000-1400_PARACO18z', 'y_1315-1745_momentumWed2', 'y_1315-1745_CFSFSCOComb6Z0to16', 'y_0800-1845_americanV4', 'y_1130-1200_PARA12z13DComb$0', 'y_1245-1745_GEFSCO4D', 'y_1200-1415_epsFcst12ZbCor', 'y_0800-1845_GEPS0zPrev1D0to10', 'y_1515-1615_GEFS6z0to8p1', 'y_0600-0800_GFSv160z', 'y_1100-1400_GEFS18Zday15$3', 'y_1815-1900_GFScash', 'y_1100-1615_GFSv16Zp2$3', 'y_0800-1845_GEMCOOPrev14D0to10', 'y_1130-1200_PARA12zp1Neg', 'y_1415-1545_PARACO6zp1', 'y_1330-1430_WindStrat', 'y_1300-1315_CFSCO6Z21to353D$1', 'y_1745-2030_GEFSLCO', 'y_0800-1200_f234b$2', 'y_1745-1945_PARACO0zp13', 'y_0800-1100_WindTX$2', 'y_1530-1645_GEFStrend15', 'y_1645-1715_GEM12Z', 'y_1100-1315_EPSVsGEPS$3', 'y_0800-1845_CFS0Z14to284D', 'y_0800-1845_WTIcloseYest', 'y_1645-1730_GEM12Zprev1', 'y_1845-1945_CFS0z14to21p3Strict', 'y_1815-2030_epsFcst12Z$Z', 'y_0000-0400_GEM12Zc', 'y_0800-1415_ThursdaysMomentum$1', 'y_1515-1845_PARAPrev1DWind$b', 'y_0800-1845_GEFSCFSCO14to21', 'y_1300-1315_CFSFSCO21to351Dcomb$2', 'y_1815-1915_PARACO12zPrev1D', 'y_1145-1745_GEFS1D6z', 'y_1100-1615_GFSv16Zp2Strict', 'y_0600-0800_eps12Z', 'y_1100-1230_GFSv16z0to8$b', 'y_1400-1500_CFS12Z4D21to35', 'y_0800-1415_GEFSCombWed', 'y_1815-1915_CFSCO12zPrev1D21to42', 'y_1415-1745_TuesdaysMomentum$1', 'y_1430-1500_GFSv16Zp40to10', 'y_1200-1400_EPSCO12z11to15$d', 'y_1200-1415_CFSvsEPS0Z$2', 'y_1515-1845_WindTX', 'y_1315-1745_EPS4528to423DPrev1Neg', 'y_1315-1745_eps9to13basic$1', 'y_1745-1945_PARA12Zp1Strict', 'y_0800-1100_WedComb2MEAN', 'y_0800-1200_EPSCFS', 'y_1845-2030_GEFS12Z', 'y_0800-1845_CFSCO14to28p2', 'y_1100-1200_PARACO18z', 'y_1815-1915_PARACO12z0to8p1', 'y_0800-1315_PARA12z8to16p3', 'y_1345-1415_cfsMean', 'y_0400-0600_PARA18Zp23', 'y_1315-1415_PARACO18zPrev1D8to16', 'y_1200-1230_EC0zbasicYestStrict', 'y_1000-1300_cfs28to422D', 'y_1415-1745_PMComb', 'y_1430-1500_PARApre6z', 'y_0800-1100_WindTX$4', 'y_1315-1415_mon', 'y_1300-1400_cfs0Z0to21Strict', 'y_1140-1200_WindStrat', 'y_0800-1845_GEFS0zp28to16W', 'y_1300-1400_EPS12zp12', 'y_1430-1715_CoalYest', 'y_0800-1315_GEFS0z0to8Prev1d', 'y_1815-2030_epsFcst12ZbCor', 'y_0800-1845_PARACO0zp38to16W', 'y_0800-1845_GEMCO12zComb', 'y_0700-0800_EC0zbasic$3', 'y_1200-1245_6Zclean$2', 'y_1100-1400_GEPSCO12Zp30to8$2', 'y_1230-1300_WindStrat', 'y_1745-1845_fritest', 'y_1100-1400_CFSCOcomb21to42$2', 'y_1100-1300_NGFmiddayStrict', 'y_1245-1300_1c', 'y_0800-1845_EPSsouth', 'y_1615-1645_PARA6Z$3', 'y_1100-1945_CFS14to28Comb', 'y_1315-1415_americanSeasonal$6', 'y_1200-1415_GEMCO12zPrev1Doto10', 'y_1100-1745_MonComb1021$0', 'y_0800-1845_weeklyMomentumd4', 'y_1315-1415_americanSeasonalb', 'y_0000-0800_clustersAvg12Z$n', 'y_1100-1515_momentumThFr', 'y_0800-1100_NGFopenVscloseStrict', 'y_1715-1900_WindStrat', 'y_1230-1315_RansTest', 'y_1300-1345_RansTest', 'y_1730-1745_GEFS12Z', 'y_1100-1430_WedComb$2', 'y_1200-1745_GEFS6zfcst', 'y_1100-1230_PARA6Zp30to8', 'y_1545-1645_WindStrat', 'y_1100-1200_GEPS12zp1loose$z', 'y_0530-0630_cashGEPSEPS', 'y_1515-1845_GEM0zp20to10', 'y_1230-1330_WindStrat', 'y_1000-1100_GFSv160z', 'y_1300-1430_EC0dChangeStrict', 'y_1200-1400_EPSCO0z$2', 'y_0800-1200_PARARACO0to8Comb', 'y_1100-1300_NGFmidday$c', 'y_0800-1100_WindTX$b', 'y_1530-1715_PARACO18z14to16', 'y_1100-1515_momentumMonNegative', 'y_0800-1945_EPSPrev1D$6', 'y_1730-1900_WindStrat', 'y_1200-1300_EPS4528to422D', 'y_0400-0700_CFSCOPrev1D$4', 'y_0800-1000_eps9to13MEAN', 'y_1815-2030_epsFcst12ZbCor$b', 'y_0600-1415_epsFcstPgeps0Zc2', 'y_1745-1945_GEFS12z0to8Strict', 'y_1200-1300_EPS4514to28', 'y_1145-1315_fri', 'y_1130-1200_PARACO0z0to8WindTXNeg', 'y_0800-1100_NGFopenStrict', 'y_1645-1730_GEM12Zp2', 'y_1415-1715_PARA6Zp14comb', 'y_0700-0730_GEM0Zp4', 'y_1145-1230_CFSCOcomb', 'y_1815-1845_PARACO12z0to10p1', 'y_0730-0815_GEMCO0z14to16', 'y_0800-1100_NGFopenloose', 'y_1215-1515_GEFStrend15$1', 'y_0600-0700_EPS45week3', 'y_1615-1745_PARACO18zp1clean', 'y_0800-1215_cashGFSEPS0d', 'y_0630-0730_GEPS0Zp1$2', 'y_1130-1300_GEFS6zp1$0', 'y_0800-1315_TTFStrict', 'y_1615-1645_PARAGEM', 'y_1415-1845_WindStrat', 'y_1100-1615_GFSv16Zp2', 'y_1130-1300_GEFS6zp1', 'y_1330-1415_CFSCO6Zp18to16', 'y_1315-1515_EPS9to13rs$2', 'y_0800-1845_NGFopen2', 'y_0800-1845_GEFS18Zday15p3$0', 'y_1100-1315_ECGEM', 'y_1300-1430_EC0dChange', 'y_1200-1430_WindTXPARACO0z', 'y_0000-0600_18Z', 'y_1715-1915_EPS450to13', 'y_1200-1245_monMean', 'y_1100-1715_epsFcstPcomb06Z$4', 'y_1515-1545_EIA2', 'y_1330-1415_cashPARA6Z0to2$1', 'y_1100-1615_GFSv16z0to8p34', 'y_1300-1330_EC12Zb1', 'y_0800-1845_WTIopen$1', 'y_0800-1200_GEM0Z', 'y_0930-1030_GEFS18z$z', 'y_1215-1315_eps12Zp40to8$2', 'y_0800-1315_TTFStrict$9', 'y_1100-1315_PARA12z14to16$Z', 'y_1245-1300_PARA6z', 'y_1515-1845_PARAPrev1DWind', 'y_0800-1300_PARACO1D6z', 'y_1815-1915_GFSv16Zrolling2$3', 'y_1815-1945_epsFcstGEPFS012Zgap', 'y_1100-1315_PARA12z14to16', 'y_1415-1615_fri', 'y_0800-1845_WTImidday', 'y_0800-1100_NGFopenVsclose$b', 'y_1400-1430_PARARACOam', 'y_1100-1400_GEFS18Zday15', 'y_1315-1415_americanSeasonalb$1', 'y_0800-1200_PARARACO18ZComb', 'y_0800-1415_TueComb$c', 'y_0800-1315_TTFclose$1', 'y_1100-1200_d', 'y_1100-1515_momentumTuWe', 'y_1200-1400_WindStrat', 'y_1745-1915_WindStrat', 'y_1315-1745_EPS4528to423DNeg', 'y_0800-1845_weeklyMomentumd2', 'y_0800-1845_NGFmidday2', 'y_1000-1300_cfs28to422D$0', 'y_1700-1745_cashGEFS18Z0to2p4', 'y_0800-1100_PARA1DComb$3', 'y_1100-1815_cfsDaily0Z0to16Summer', 'y_0800-1500_EPSComb', 'y_0800-1845_GDDVs30d', 'y_1000-1400_PARACO18z$3', 'y_1745-1815_WindStrat', 'y_1100-1515_momentumMon$0', 'y_1215-1515_GEFStrend15', 'y_0800-1845_CFSM3M', 'y_1545-1715_CFS14to214D', 'y_0600-0930_cfs12Z', 'y_1145-1200_WindStrat', 'y_0700-0730_PARA18Zp14', 'y_1100-1745_MonComb1021$1', 'y_0800-1200_fMean', 'y_1100-1400_GEFS18Zday15Strict$b', 'y_0800-1745_PARA1D0to10', 'y_0800-1100_PARA0ZSeasonal', 'y_0800-1745_PARA18z4D$z', 'y_0800-1845_GEFSCFSCO14to21Strict', 'y_1745-2045_12Zb124b', 'y_1715-1845_PARACO018z0to10', 'y_1145-1745_GEFS1D6zStrict', 'y_1200-1230_EC0zbasicYest$b', 'y_1745-1945_12Zb2', 'y_1845-1945_CFS0z14to21p3', 'y_1100-1400_ECFcst12zb', 'y_1530-1645_GEFStrend15$2', 'y_1715-1915_CFSMComb', 'y_1315-1645_GEFS6zp48to16Strict', 'y_1330-1445_WindStrat', 'y_1315-1415_GEFS6zp48to16Comb$2', 'y_0800-1845_MTFComb$4', 'y_1615-1945_PARA1D18Z', 'y_1200-1745_GEFSCO35Prev1DNeg', 'y_1700-1745_PARA12Zp34', 'y_0000-0800_clustersAvg12Z', 'y_1100-1745_PARACO12Zp2', 'y_1715-1845_cashGFS0z2d$3', 'y_1300-1430_WindStrat', 'y_1200-1500_EC12Z0to4', 'y_0800-1500_CFSPrev1D14to21', 'y_1100-1415_clustersBestVs2nd', 'y_1530-1645_GEFStrend156z', 'y_0800-1200_PARACO12Zp1', 'y_1945-2030_CFSCO18z14to28', 'y_1315-1745_momentumWed2$2', 'y_1900-1915_epsFcstGEFS12Z', 'y_1315-1745_momentumWed', 'y_1100-1715_epsFcstPgeps0Z$2', 'y_0800-1315_TTFStrict$3', 'y_1130-1200_PARA12z13DComb', 'y_0800-1845_CFSCOCombWed', 'y_1115-1815_GEPSpm', 'y_1100-1315_PARA12z14to16$W', 'y_2300-2345_EPS12Z', 'y_1415-1545_PARACashComb6zp3', 'y_1130-1200_GFSv16z$c', 'y_1100-1200_PARA6Zp28to16$b', 'y_1800-1900_CFSFSCO6Zcomb', 'y_1130-1200_GFSv16z$9', 'y_0800-1000_eps9to13MEANStrict', 'y_0800-1845_CFS0Z14to28Prev1D$W', 'y_1100-1245_RANSTEST', 'y_1200-1330_paracoSeasonal8to16D', 'y_1315-1515_EPS9to13rs', 'y_1100-1200_PARACO18zp1', 'y_0400-0600_american', 'y_1745-1945_GEFS12z0to8', 'y_1130-1200_GFSv16z$0', 'y_1200-1400_EPSCO0z', 'y_0800-1845_CFSCombDaily', 'y_1100-1300_NGFmidday', 'y_1300-1330_eps9to13p4Strict', 'y_1715-1915_GEPSComb$1', 'y_0800-1315_GEFSCO354D', 'y_1315-1745_eps9to13basic$6', 'y_0800-1845_NGFmidday2$0', 'y_1100-1130_GFSv164D$4', 'y_1245-1315_mon', 'y_0600-0815_GEMCOSummer', 'y_0800-1100_NGFopenVsclose', 'y_1000-1200_eps12Z9to13', 'y_1100-1515_momentumThFrNegative$2', 'y_1100-1200_cashGEM0zp10to2', 'y_0800-1945_GEFS35yesterdayStrict', 'y_1400-1530_Comb', 'y_1845-1915_WindStrat', 'y_1100-1300_EPSSeasonalday15', 'y_0800-1745_PARA1D0to10$1', 'y_1315-1415_GEPSComb3', 'y_1845-1945_RansTest', 'y_1215-1300_PARA6zp10to8', 'y_0600-0700_GEPS0Zp2', 'y_1145-1745_GEFSCO1D', 'y_1515-1745_WindStrat', 'y_0600-0800_epsFcst$2', 'y_1615-1645_EC12Zb2', 'y_1415-1545_PARACO6zp10to8', 'y_0600-0800_epsFcst', 'y_1245-1330_PARACO6zp10to10', 'y_1000-1100_GFSv160z2', 'y_1100-1300_WindStrat', 'y_1200-1315_ICONCash0dp2', 'y_1115-1415_PARACO6Z23D', 'y_0800-1315_TTFclose', 'y_0800-1845_WTIopen', 'y_1915-2030_GEFSCFSCOcomb', 'y_1200-2030_epsFcstGEFS6Z', 'y_1315-1745_american024', 'y_1330-1415_GEPS12z14to16', 'y_1300-1330_eps9to13p4', 'y_1200-1300_EPS4514to423D', 'y_1815-2030_epsFcst12ZbCorStrict', 'y_1315-1415_PARACO18zPrev1D8to16$1', 'y_0600-0800_CFSPrev1D21to35', 'y_1100-1815_cfsDaily0Z0to16Winter', 'y_1130-1300_ICONCash', 'y_1845-1915_GEFS1D', 'y_1400-1500_cfs0Z', 'y_1100-1400_WindStrat', 'y_1215-1415_GEFS6z8to16Comb', 'y_1215-1415_GEFS6z8to16Comb$2', 'y_0800-1845_WindUS', 'y_0000-0800_GEFS352D', 'y_1200-1400_EPSCO0z$b', 'y_1100-1300_EPSSeasonalday15Strict', 'y_0000-0600_cfs12Z28to42$2', 'y_0400-0800_EIAeffectloose', 'y_0800-1200_WindStrat', 'y_0800-1415_PARACOCombWed', 'y_0630-0800_MorningComb$2', 'y_0800-1000_1c', 'y_0600-1200_GEFSCOam', 'y_0600-0800_GEPFS1D', 'y_0630-0800_MorningComb$b', 'y_1100-1400_GFSv1612zp4', 'y_0000-0600_cash', 'y_0800-1100_PARA1DCombStrict', 'y_1145-1215_c', 'y_1645-1815_PARACO12Z12D$W', 'y_1415-1545_Comb$2', 'y_0800-1100_GEFS0Z14to16Seasonal', 'y_0400-0700_CFSCOPrev1D', 'y_0800-1000_eps9to13', 'y_1100-1200_gefsFcst6Z', 'y_1230-1315_6ZPM', 'y_1815-1915_GFSv16Zrolling2$0', 'y_1000-1400_PARACO18z$0', 'y_0800-1745_GEFS354D14to28', 'y_1415-1615_PARACO0z14to16', 'y_1100-1200_GEMCash0d', 'y_0430-0530_RANSTES22T', 'y_1000-1100_GFSv160z2$3', 'y_0730-0800_cfs12Z28to42', 'y_0630-0800_GEMCOPrev1D0z8to16', 'y_0800-1945_EPShybrid2.2Strict', 'y_1315-1745_d', 'y_1115-1245_PARA6Zp20to16', 'y_1445-1545_PARA0z14to16p4', 'y_1100-1230_GFSv16z0to8Strict', 'y_0800-1845_PARA0Zrolling2Strict', 'y_1315-1715_CFS6z0to16p34', 'y_0400-0800_EIAeffect', 'y_1100-1745_ThuMomentumEIAWed', 'y_1700-1745_PARA12z0to8p34', 'y_1715-1915_GEPSComb2', 'y_1715-1915_PARACO12Z1D8to16', 'y_0800-1845_CFS0Z14to28Yest', 'y_0800-1100_NGFopen$6', 'y_1315-1745_3c', 'y_1415-1615_GFS6Zrolling2$2', 'y_0800-1845_COALopenclose2d', 'y_1100-1200_MondaysMomentum', 'y_0800-1845_PARACO18zp20to13', 'y_1200-1400_EPSCO12z$b', 'y_1545-1845_PARACO0z14to16p4', 'y_0600-0800_epsFcstGEPFS0Zgap$b', 'y_1545-1715_PARA0Zp1', 'y_1400-1500_cfs0ZMean', 'y_1315-1415_momentumWed1d', 'y_1100-1415_clustersBestVs2nd$b', 'y_1845-1900_GEPS12Zp4', 'y_1100-1400_GEFS18Zday15$7', 'y_1845-2030_EC12Z', 'y_1300-1430_PARA18Zp2$z', 'y_0800-1745_cfs12Z0to16p3', 'y_1715-1915_GEFSCO3514to35p3$b', 'y_1300-1315_CFSCO6Z21to353D', 'y_1100-1245_WindStrat', 'y_1400-1530_CFSCOPrev12D$b', 'y_1400-1530_CFSCOPrev12D', 'y_1545-1715_PARA0Zp2$1', 'y_0800-1415_ThursdaysMomentumStrict', 'y_0800-1315_GEFSCO354D$0', 'y_1845-1945_GEFSPrev1dp4', 'y_0600-0800_PARACO0AMpred3', 'y_1445-1515_WindStrat', 'y_1445-1545_PARA0z14to16p4$b', 'y_1300-1545_PARA018Z0to8$2', 'y_1300-1545_CFS6ZPrev1D28to42', 'y_1645-1715_PARACO6z$2', 'y_1315-1415_cfs6Z14to42', 'y_1200-1245_6Zclean', 'y_0800-1845_MTFComb$6', 'y_1000-1200_MonCombStrict', 'y_1415-1745_TuesdaysMomentum', 'y_1845-1945_cfs12z12D', 'y_1315-1400_GEFS18z14to16', 'y_1615-1715_GEFS6zp248to16$3', 'y_0630-0730_cfs12z10to211D', 'y_0800-1845_CFSMComb$2', 'y_1315-1615_PARA6Zp1only$b', 'y_0800-1945_PARAVs1YbStrict', 'y_1615-1715_cash', 'y_0800-1100_PARA1DComb', 'y_1215-1315_eps12Zp40to8', 'y_0800-1315_momentumWed1d', 'y_0800-1315_cashVsettle1d', 'y_1315-1415_americanSeasonal$1', 'y_1315-1615_PARA6Zp1', 'y_1200-1400_EPSCO0z$1', 'y_0800-1315_TTFStrict$2', 'y_1000-1200_eps12Z9to13$2', 'y_1315-1415_cfs6Z14to42on12', 'y_0800-1500_GEFSCO35Prev1mix0to13', 'y_1100-1615_GFSv16Zp2$4', 'y_0800-1315_american18z4D', 'y_0630-0800_MorningComb', 'y_1745-1945_RansTest', 'y_1300-1545_PARA018Z8to16', 'y_0800-1400_GEFSFcst12zAM', 'y_1945-2145_EC12ZComb', 'y_1715-1945_RansTest', 'y_1200-1400_EPSCO12z11to15Strict', 'y_1200-1400_TueComb$1', 'y_1530-1615_GEFS6zp18to16', 'y_0800-1845_GEPS12zPrev1D0to10', 'y_0800-1845_CFS0Z14to28Prev1D', 'y_0800-1400_CFSCOPrev1D0to16', 'y_0730-0800_CFSCO0z0to16p1', 'y_1130-1745_PARACO6zp10to10', 'y_1000-1615_Moncash', 'y_1100-1745_epsFcstPcomb06Z', 'y_1200-1300_PARACO1D', 'y_1615-1845_GEM12zp20to2', 'y_1200-1315_CFS0z0to16p4', 'y_1100-1130_GFSv164D', 'y_1300-1330_PARACO6zMean', 'y_1815-1945_GEFS0to8', 'y_1215-1245_124b', 'y_1500-1545_GEMPrev1D', 'y_1645-1745_TTFclosevs1to3', 'y_1815-1945_CFScomb', 'y_1815-1915_GEMCO0zPrev1D0to10', 'y_0600-0900_GFSCO0zp23Strict', 'y_1715-1915_CFSCO0z21to28', 'y_0800-1315_TTFcloseNegative', 'y_0000-0600_PARA18zp2', 'y_1400-1530_CFSCOPrev12DStrict', 'y_0800-1845_RelStrengthSellloose', 'y_1100-1315_amerimix23D', 'y_1645-1745_PARACO6zStrict2Mean$2', 'y_1745-1945_Cash0dGEFSv2', 'y_1945-2345_d3', 'y_1615-1645_PARA6Z', 'y_1215-1515_PARACO6z14to16', 'y_1415-1715_GFSv16pre12Z', 'y_1300-1400_EPS0zp12Strict', 'y_1545-1645_PARA12z14to16p1', 'y_1615-1845_GEM12zp20to2$c', 'y_0800-1100_NGFopen', 'y_0800-1845_weeklyMomentumd0', 'y_0000-0600_cfs12Z28to42', 'y_0800-1200_f234b', 'y_1415-1845_PARACO18zp10to10$4', 'y_1315-1415_GEFS12z0to8p2', 'y_1200-1400_EPSCO0z$3', 'y_1715-1845_PARACO018z0to10$4', 'y_0800-1400_EPSlast', 'y_1215-1515_PARACO6z14to16$W', 'y_1315-1415_GEFS6zp48to16Comb', 'y_1815-2030_epsFcst12ZCor', 'y_1100-1230_GFSv16z0to8', 'y_0000-0800_CFSvsEPS12Z', 'y_1230-1400_GEFSCO35Prev10to2', 'y_0800-1315_TTFcloseYestStrict', 'y_1415-1845_cashVsettlediff$3', 'y_1200-1330_PARACO18zp4', 'y_1815-1900_GEPS12Zp1$2', 'y_1245-1415_american4D$2', 'y_0800-1845_cfsDaily12Z14to284Dclean', 'y_0800-1400_EPSlastclean$b', 'y_0800-1415_eps12Z', 'y_1200-1945_epsFcst12ZbCorW15', 'y_0800-1845_MTFCombStrict$3', 'y_0800-1845_clustersBest0Z', 'y_1645-1745_GEFSPM', 'y_1945-2045_4a', 'y_1415-1545_PARACashComb6zp3$4', 'y_0800-1315_PARA18zPrev1D8to16', 'y_0600-0800_GEFS0zComb', 'y_0800-1845_cfsDaily0Z0to162Dclean', 'y_1100-1145_PARA6Zp4$1', 'y_0600-0800_EC12z2D', 'y_1645-1715_PARACO6z', 'y_1415-1845_PARACO18zp10to10Strict', 'y_1645-1745_TTFclosevs1to3$2', 'y_1100-1200_PARA6Zp28to16', 'y_0000-0800_GEFS352D$0', 'y_1300-1400_WindStrat', 'y_1615-1745_PARACO18zp1clean$2', 'y_1200-1230_EC0zbasicYest', 'y_1100-1615_GFSv16Zp2$7', 'y_0800-1000_eps9to13MEAN$1', 'y_1715-1845_cashGFS0z2d$7', 'y_1100-1200_EPS4528to42b$1', 'y_1315-1745_d$2', 'y_0800-1200_mon3', 'y_0000-0600_18Z$2', 'y_0800-1845_RelStrengthSell$0', 'y_1845-1915_GFSv160zp4', 'y_0730-0800_CFSCO0z0to16p24', 'y_1245-1745_SeasonalComb2', 'y_1100-1745_MonComb1021', 'y_1200-1715_GEFS353D', 'y_1300-1515_GEFSCO6Z23D', 'y_1100-1400_GEFS18Zday15$2', 'y_1230-1315_PARACO1D0zStrict', 'y_0800-1845_CFSCO0z14to28p1', 'y_1615-1745_234', 'y_1400-1530_cashPARACO0Z0to2p2', 'y_1130-1200_PARACO12z23DComb$7', 'y_1100-1315_COALopen', 'y_1200-1400_EPSCO12z11to15', 'y_1200-1745_CFSCOp234', 'y_1100-1400_GFSv1612zp4Strict', 'y_1200-1315_PARA6zws10mTX', 'y_1200-1945_epsFcstGEPS12Zgap', 'y_0400-0800_EIAeffect$2', 'y_1145-1415_cashGFS', 'y_1230-1330_PARACO6zp4$b', 'y_1200-1945_clustersAvg6Z12h', 'y_1200-1415_FridaysMomentum$4', 'y_1730-1830_eps12Z9to13p1', 'y_1200-1245_mon', 'y_1745-1945_PARA12Zp1$4', 'y_0800-1845_CFSFSCO6zcomb28to424D', 'y_0700-0800_EC0zbasic$2', 'y_0800-1745_cfs14to211D', 'y_1100-1245_cashGFS0d', 'y_1100-1200_PARA6Zp28to16$4', 'y_1245-1415_wed4dailies', 'y_1415-1615_americanSeasonal', 'y_1230-1330_PARACO6zp4', 'y_1100-1315_COALopen$2', 'y_0800-1845_COALopenclose', 'y_1430-1500_CFSCO12zp1', 'y_1400-1500_cfs0Z$2', 'y_0800-1500_CFSPrev1D14to21Strict', 'y_1445-1645_cfs18Z0to21', 'y_1400-1445_cfs6Z14to21D', 'y_1230-1315_GEFSCO18z0dCash$1', 'y_1315-1745_CFSCO6Zp314to21Comb', 'y_1315-1745_EPS4528to423DPrev1', 'y_1745-1945_PARA12Zp1$2', 'y_0800-1945_cfs6z10to21D$1', 'y_1315-1415_cfs6Z28to42', 'y_1900-1945_RansTest', 'y_1645-1730_PARA12Z', 'y_0800-1845_WindStrat', 'y_1130-1300_GEFS6zp1Strict', 'y_0800-1415_ThursdaysMomentum', 'y_1400-1545_GEFS0z3D0to10', 'y_0800-1315_PARA0zPrev1D8to16', 'y_0800-1100_NGFopenStrict$6', 'y_0800-1845_MTFCombStrict', 'y_0800-1415_WindVs30dTXStrict', 'y_1300-1430_PARA18Zp2', 'y_1100-1230_WindStrat', 'y_1115-1215_PARACO6z0to10', 'y_1200-1300_EPS4514to423D$b', 'y_1145-1745_GEFS1D', 'y_1815-1945_GEFS0to8$3', 'y_1400-1630_GFSEFS6z', 'y_1300-1545_PARA018Z0to8$z', 'y_1515-1615_GEFS6z0to8p1$1', 'y_1745-1945_Coalpred', 'y_1100-1745_GEMCO14to16', 'y_0800-1845_WindUS$2', 'y_0700-0800_EC0zbasic', 'y_1215-1315_cashEC', 'y_1100-1515_mon', 'y_0800-1745_yearly', 'y_0800-1745_cfsDaily12Z', 'y_1515-1745_GEFS0Zp20to16', 'y_1545-1845_PARACO0z14to16p4$0', 'y_1300-1315_PARA12zp28to16', 'y_1300-1545_PARA018Z8to16$z', 'y_0630-0800_GEPSamloose', 'y_1100-1300_cashCFS', 'y_2200-2300_LongBiasCorrection222', 'y_1100-1415_CFSCO21to351D', 'y_1300-1330_WindStrat', 'y_1100-1200_GEPS12zp1loose', 'y_1330-1415_cashPARA6Z0to2', 'y_1415-1745_GEFSLp4', 'y_0800-1945_cfs6z10to21D', 'y_1200-1500_EPSlast', 'y_1100-1515_momentumMonNegativeMEAN', 'y_1300-1400_cfs0Z0to21Strict$b', 'y_1115-1245_PARACO6zp10to8', 'y_1315-1745_eps9to13basic', 'y_1815-1945_epsFcstGEPFS0Zgap', 'y_1100-1230_PARA6Zp30to8Strict', 'y_1000-1400_PARACO18zStrict', 'y_1400-1500_EC', 'y_1200-1330_CFSpre6z', 'y_1130-1415_PARACO18zp4comb', 'y_1245-1415_PARA6Zp18to16', 'y_1130-1200_PARA12z13DComb$z', 'y_0800-1845_GEFS18Zday15p3$2', 'y_1315-1615_PARA6Zp1only', 'y_1200-1400_EPSCO12z', 'y_1615-1745_cfsDaily0Z0to162D', 'y_1715-1915_GEPSComb', 'y_1200-1415_epsFcst12ZbCorloose', 'y_1200-1245_PARA6Zp40to16Neg', 'y_0800-1845_MTFComb$2', 'y_1200-1945_epsFcst12ZbCorNEW$2', 'y_1130-1300_ICONCashStrict', 'y_1300-1545_PARA018Z8to16$2', 'y_1230-1315_GEPS0Z0dp3', 'y_1100-1615_GFSv16Zp2$b', 'y_0800-1200_PARA18Zp3', 'y_1745-1945_GEPS12zVsEPS0zgap', 'y_1715-1915_PARACO6z0to8WindTXNeg', 'y_1100-1745_PARAws10mNeg', 'y_1415-1845_PARACO6z1D0to10', 'y_0800-1845_clustersBest12Z', 'y_1130-1200_LongBiasCorrection', 'y_0800-1415_EPS0zp12loose', 'y_0800-1945_GEFS35yesterday', 'y_1100-1415_GEMCOPrev1DSummerMean', 'y_1315-1745_eps9to13basicStrict', 'y_1000-1200_MonComb', 'y_0600-1415_epsFcstPgeps0Z', 'y_1430-1500_PARApre6z$2', 'y_0800-1845_NGFopenStrict2', 'y_1000-1200_MonComb$4', 'y_1000-1200_PARACO0zp1', 'y_1215-1315_PARA6z1D0to10', 'y_0800-1100_WindStrat', 'y_0800-1415_WindVs30dTX', 'y_1230-1315_GEFS0zComb2', 'y_0800-1945_GEFS0z0to8', 'y_0800-1945_GEPSCO', 'y_1245-1415_american4D', 'y_1815-1900_GEPS12Zp1', 'y_1330-1430_PARAPrev1Dx2D8to16', 'y_1200-1500_EPStrend15', 'y_1200-1400_MixComb', 'y_1300-1315_PARA18z0to2p3', 'y_1415-1845_cashVsettlediff', 'y_1745-1815_GEPS12z0to8', 'y_1445-1545_paracoSeasonal14to16', 'y_1000-1300_GEFS12zComb', 'y_1200-1415_CFSvsEPS0Z', 'y_0800-1845_AmericanDaily0zW', 'y_1100-1215_cfs0z10to214D', 'y_0800-1745_cfs14to211D$1', 'y_1100-1430_WedComb', 'y_0400-0800_EPS4528to42', 'y_0630-0800_GEPSamloose$1', 'y_1300-1330_fri', 'y_0800-1845_clustersAvg18Z', 'y_1100-1200_EPS4528to42b', 'y_1100-1230_GFSv16z0to8$2', 'y_1745-2045_GEFS12Z', 'y_1315-1515_PARACO18zp30to8', 'y_1845-1945_PARA12Zp40to10$Z', 'y_1200-1945_epsFcst12ZbCorNEW', 'y_1415-1545_Comb', 'y_0800-1745_cfsDaily0Z', 'y_1815-1945_epsFcstGEPS0Z', 'y_0800-1845_CFSFSCOcomb28to4214D', 'y_1845-1945_PARA12Zp40to10', 'y_0800-1845_CFS1D0to16', 'y_1745-1945_GEFS12zVsEPS0zgap', 'y_0800-1100_NGFopen$0', 'y_1200-1315_cashGFS0d', 'y_1100-1415_WindStrat', 'y_1815-1945_GEFS12Zp1', 'y_0800-1415_TueComb$6', 'y_1115-1215_PARACO6z0to8WindTX', 'y_1115-1215_PARA6Zp48to16', 'y_1400-1415_fri', 'y_1715-1915_GEFSCO3514to35p3', 'y_1245-1545_EPS45', 'y_0800-1945_PARAVs1Y14to16', 'y_1815-1845_PARACO12z0to10p14', 'y_1745-1815_cashPrice', 'y_0800-1745_PARA18z4D', 'y_1130-1200_GFSv16z', 'y_1130-1745_PARACO6zp10to16', 'y_0800-1845_GEFS18Zday15p3', 'y_1100-1715_epsFcstPgeps0Z', 'y_1815-1915_GEFS6Z', 'y_1130-1200_PARA12zp1Neg$1', 'y_1815-1945_WindStrat', 'y_1415-1745_GEFSLp4b', 'y_1515-1615_GEFS6z0to8p1$4', 'y_0600-0700_EPS45week3Mean', 'y_1200-1745_WindTX', 'y_0800-1415_PARACO0zp3', 'y_0800-1945_EPSPrev1D$b', 'y_1245-1615_epsFcstGEFS12Zb$b', 'y_1615-1715_GEFS6zp248to16', 'y_0600-0800_GEFS0zCombFull', 'y_0800-1845_PARACO0zComb', 'y_1100-1515_momentumTuWeStrict', 'y_1745-1845_fritest$P', 'y_1415-1615_PARAPrev1D', 'y_1745-2045_GEFS12Z$8', 'y_1200-1300_PARA1D$1', 'y_1515-1645_CFSCO0Z14to281D', 'y_0800-1500_GEFS350to13p2', 'y_1400-1500_GEFSCO12z14to16Prev1', 'y_1100-1715_epsFcstPcomb06Z', 'y_1100-1230_PARA6Zp30to8$c', 'y_0800-1745_cfsDaily06Z', 'y_1545-1645_GFSv16z', 'y_0000-0800_GEPFS4D$0', 'y_0800-1845_COALopenclose1d2', 'y_0600-1415_epsFcstPgeps0Z$1', 'y_0800-1315_TTF', 'y_1300-1430_EC0dChange$5', 'y_1745-1915_GEMCO12zp1', 'y_1315-1415_americanSeasonal', 'y_1200-1315_PARA6z14to16p1', 'y_1400-1500_GEFSCO12z14to16Prev1$b', 'y_1100-1515_momentumThu1dNegative', 'y_1200-2030_epsFcstGEFS6Z$0', 'y_1300-1545_CFS6ZPrev1D28to42$1']
with open(get_ALL_PREDS_json_path(),'r') as f:
    ALL_PREDS = json.load(f)

# todo use json file for these too. OR just put in a function so not every import will trigger the calc
WIND_PREDS_OLD = [x for x in ALL_PREDS if 'WindStrat' in x]
WIND_PREDS_NEW = [x for x in ALL_PREDS if ('Wind' in x and 'Strat' not in x) or 'ws10m' in x or 'gust' in x or 'WIND' in x]
WIND_PREDS = WIND_PREDS_NEW+WIND_PREDS_OLD
CASH_PREDS = [x for x in ALL_PREDS if ('cash' in x or 'Cash' in x or '0d' in x or ('0to2' in x and '0to21' not in x))
                                and x not in WIND_PREDS]
DAYS0to8_PREDS = [x for x in ALL_PREDS if '0to8' in x and x not in WIND_PREDS]
DAYS8to16_PREDS = [x for x in ALL_PREDS if ('8to16' in x or '9to13' in x) and x not in WIND_PREDS]

CFS_LONGTERM_PREDS = [x for x in ALL_PREDS if (('21' in x or '28' in x or '35' in x or '42' in x) and 'CFS' in x)
                                        or (('CFS' in x or 'cfs' in x) and (('0to16' not in x) and x not in CASH_PREDS))
                      ]
GEFS35_LONGTERM_PREDS = [x for x in ALL_PREDS if ('GEFSCO35' in x or 'GEFS35' in x) and '0to13' not in x and
                                    '0to16' not in x and '0to2' not in x]
LONGTERM_PREDS = [x for x in ALL_PREDS if ('21' in x or '28' in x or '35' in x or '42' in x or 'EPS45' in x) if ('CFS' not in x)]+CFS_LONGTERM_PREDS+GEFS35_LONGTERM_PREDS

EPS_PREDS = [x for x in ALL_PREDS if ('EPS' in x or 'eps' in x) and ('GEPS' not in x and 'geps' not in x
                                                                     and 'EPS45' not in x)]
EC_PREDS = [x for x in ALL_PREDS if ('_EC' in x or '_ec' in x)]

CFS_0to16_PREDS = [x for x in ALL_PREDS if 'CFS' in x and '16' in x]

PARACO_PREDS = [x for x in ALL_PREDS if 'PARACO' in x or 'paraco' in x]
GFS_PREDS = [x for x in ALL_PREDS if ('GFSv16' in x or 'gfsv16' in x or 'PARA' in x or 'para' in x or 'GFS' in x)
             and x not in PARACO_PREDS]

AMERICAN_PREDS = [x for x in ALL_PREDS if ((('GEFS' in x or 'GEPFS' in x) and ('GEFS35' not in x and 'GEFSCO35' not in x))
                     or ('PARA' in x or 'GFSv16' in x or 'para' in x or 'gefs' in x or 'gfsv16' in x)
                  or (('6Z' in x or '6z' in x or '18Z' in x or '18z' in x) and sum([s in x for s in ['CFS','cfs']])==0)
                  or ('ameri' in x.lower())) and x not in WIND_PREDS]

# AMERICAN_NO_CASH_PREDS = [x for x in ALL_PREDS if sum([s in x for s in ['GEFS','gefs','PARA','para','GFSv16','gfsv16',
#                                                                  '6Z','6z','18Z','18z','american']])
#                   and x not in CASH_PREDS+LONGTERM_PREDS+WIND_PREDS]

CANADIAN_PREDS = [x for x in ALL_PREDS if sum([s in x for s in ['GEM','gem','GEPS','geps']])]
MOMENTUM_PREDS = [x for x in ALL_PREDS if 'Momentum' in x or 'momentum' in x or 'EIA' in x or 'RelStrength' in x
                  or 'real-' in x]
TTF_PREDS = [x for x in ALL_PREDS if 'TTF' in x or 'NGF' in x]
COAL_PREDS = [x for x in ALL_PREDS if 'COAL' in x or 'coal' in x or 'Coal' in x or 'MTF' in x]
WTI_PREDS = [x for x in ALL_PREDS if 'WTI' in x]
EXTERNAL_PREDS = TTF_PREDS+COAL_PREDS+WTI_PREDS+[x for x in ALL_PREDS if 'RelStrength' in x]
EXTERNAL_NO_TTF = COAL_PREDS+WTI_PREDS+[x for x in ALL_PREDS if 'RelStrength' in x]

PREDS_6Z = [x for x in ALL_PREDS if '6Z' in x or '6z' in x]
PREDS_18Z = [x for x in ALL_PREDS if '18Z' in x or '18z' in x]
PREDS_12Z = [x for x in ALL_PREDS if '12Z' in x or '12z' in x]
PREDS_1D = [x for x in ALL_PREDS if '1D' in x]

GENRAL_WEATHER_PREDS_EXCLUSIVE = [x for x in ALL_PREDS if (sum([s in x for s in ['GEFS','GEPS','PARA','PARACO','GEM','EC','EPS','CFS','GFSv16']+
                                                [x.lower() for x in ['GEFS','GEPS','PARA','PARACO','GEM','EC','EPS','CFS','GFSv16']]
                                                ]) or 'cluster' in x) and x not in CASH_PREDS+DAYS0to8_PREDS+DAYS8to16_PREDS+WIND_PREDS+LONGTERM_PREDS+\
                                                    WIND_PREDS+EXTERNAL_PREDS+EPS_PREDS+EC_PREDS+CFS_0to16_PREDS+CANADIAN_PREDS+AMERICAN_PREDS+
                                                    PREDS_6Z+PREDS_12Z+PREDS_18Z+GFS_PREDS+PARACO_PREDS]
GENRAL_WEATHER_PREDS = [x for x in ALL_PREDS if (sum([s in x for s in ['GEFS','GEPS','PARA','PARACO','GEM','EC','EPS','CFS','GFSv16']+
                                                [x.lower() for x in ['GEFS','GEPS','PARA','PARACO','GEM','EC','EPS','CFS','GFSv16']]
                                                ]) or sum([s in x.lower() for s in ['cluster','Mix','comb','fMean','4a','_23',
                                                                                    'daily','yearly','04c','012','234',
                                                                                    'mon','tue','wed','thu','fri',
                                                                                    '1c','3c','general','trend','_d']])) and x not in WIND_PREDS+EXTERNAL_PREDS+MOMENTUM_PREDS]

SEASONAL_PREDS = [x for x in ALL_PREDS if 'seasonal' in x.lower()]

##### NEW CLUSTERS, overlapping previous
DAYS0to8_PREDS


RESIDUALS = [x for x in ALL_PREDS if x not in CASH_PREDS+DAYS0to8_PREDS+GENRAL_WEATHER_PREDS+DAYS8to16_PREDS+
                                        EPS_PREDS+AMERICAN_PREDS+LONGTERM_PREDS+
                                            WIND_PREDS+MOMENTUM_PREDS+EXTERNAL_PREDS]
PREDS_CLUSTERS_DICT = {k:v for k,v in {'cash':CASH_PREDS,'d0to8':DAYS0to8_PREDS,'d8to16':DAYS8to16_PREDS,
              'eps':EPS_PREDS, 'ec': EC_PREDS,
                       'american':AMERICAN_PREDS,'canadian':CANADIAN_PREDS,
            'longT':LONGTERM_PREDS,
            'gfs':GFS_PREDS,'paraco':PARACO_PREDS,
            #'wind':WIND_PREDS,
                'cfs0to16': CFS_0to16_PREDS,
                'seasonal':SEASONAL_PREDS,
                'windOld':WIND_PREDS_OLD,'windNew':WIND_PREDS_NEW,
                'momentum':MOMENTUM_PREDS,'ttf':TTF_PREDS,'coal':COAL_PREDS,
              'external': EXTERNAL_PREDS,'external2':EXTERNAL_NO_TTF,'weatherGen':GENRAL_WEATHER_PREDS,
              'weatherGenExclusive':GENRAL_WEATHER_PREDS_EXCLUSIVE,
            '6Z':PREDS_6Z,'12Z':PREDS_12Z,'18Z':PREDS_18Z,
                                       '1D':PREDS_1D}.items()
                       if len(v) > 0
                       }


def split_to_unique_clusters():
    import json
    from pathlib import Path

    preds_cluster_unique = {}
    unique_keys = ['windNew','cash', 'seasonal',
                   'eps', 'ec', 'canadian',
                   'gfs', 'paraco','longT','american',
                     'cfs0to16','momentum', 'external', '1D'] + [
                        'weatherGen','external2','coal','ttf','6Z',
                                               '12Z','18Z','d8to16','d0to8']
    stack = []
    for k in unique_keys:
        v = PREDS_CLUSTERS_DICT[k]
        #print ('Key = %s'%k)
        if len(set(stack).intersection(set(v))) == 0:
            stack += v
            preds_cluster_unique[k] = v
        else:
            # print('stack has intersection with %s'%k)
            # print(set(stack).intersection(set(v)))
            # print ('Additional features are:')
            # print(set(v)-set(stack))
            additionals = list(set(v) - set(stack))
            if len(additionals):
                preds_cluster_unique[k] = additionals
            stack += additionals
    preds_cluster_unique['residual'] = list(set(ALL_PREDS) - set(stack))
    stack += list(set(ALL_PREDS) - set(stack))

    json_path = os.path.join(Path(__file__).parent.absolute().parent,'Conf','clusters_division.json')
    with open(json_path, "r") as f:
        try:
            final_dict = {} #json.load(f)
        except:
            final_dict = {}
    final_dict['ALL_PREDS'] = ALL_PREDS
    final_dict['PREDS_CLUSTERS_DICT'] = PREDS_CLUSTERS_DICT
    final_dict['PREDS_CLUSTERS_DICT_UNIQUE'] = preds_cluster_unique
    with open(json_path,"w") as f:
        json.dump(final_dict,f,indent=True)


def check_for_new_preds():
    import pandas as pd
    trades_df = pd.read_csv(os.path.join(HOME,"Trading","daily_predictions.csv"))
    a2 = [x for x in list(set(trades_df['predictor'].tolist())) if 'Residual' not in x and 'Manual' not in x and 'ACTUAL' not in x]
    a1 = ALL_PREDS
    diff = len(set(a1+a2)) - len(set(a1))
    if diff > 0:
        print(f'We have {diff} new preds, will dump to json')
        new = list(set(a1+a2))
        with open(get_ALL_PREDS_json_path(),"w") as f:
            json.dump(new,f)

def wrap_all_preds_update():
    check_for_new_preds()
    split_to_unique_clusters()

if __name__ == '__main__':
    # check_for_new_preds()
    # split_to_unique_clusters()
    wrap_all_preds_update()
    pass
