import matplotlib
from matplotlib import pyplot as plt
import matplotlib.dates as mdates

matplotlib.style.use('ggplot')
from datetime import datetime as dtdt
from datetime import time as dt_time
from datetime import timedelta as td
import pandas as pd
import numpy as np
import pytz
import os
from mpl_finance import candlestick_ohlc
from Algo.Learning.performance_analysis import *
from Algo.Utils.send_email import send_email_main
from Algo.Trading.constants import PREDS_CLUSTERS_DICT, SPECIAL_RESTRICTED_TIME_WINDOWS_BY_MODE_DAY,MODES_NAMES_DICT
from Algo.Viasualization.trading_kpis import calc_calmar,calc_sharpe,get_drawdown_vector


from Algo.Utils.files_handle import HOME


def compare_trades_of_2_modes(day,mode1,mode2,naive_opening_times=False,
                              min_score=0.75,
                              daily_trades_csv = os.path.join(HOME,"Trading","daily_predictions.csv")):
    real_mode1 = mode1
    real_mode2 = mode2
    if mode1 in MODES_NAMES_DICT:
        real_mode1 = MODES_NAMES_DICT[mode1]
    if mode2 in MODES_NAMES_DICT:
        real_mode2 = MODES_NAMES_DICT[mode2]

    trades_df = pd.read_csv(daily_trades_csv,
                            parse_dates=['date', 'real_open_time', 'closing_time', 'opening_time'])
    if naive_opening_times:
        trades_df = trades_df[trades_df['predictor'].apply(lambda x: 'Residual' not in str(x))]

    predictors_dict = {}
    predictors_strats_dict = {}
    for mode,real_mode in [(mode1,real_mode1),(mode2,real_mode2)]:
        days_shift = 0
        if mode.endswith('-1d') or mode.endswith('-2d') or mode.endswith('-3d') or mode.endswith('-4d') or mode.endswith(
                '-7d'):
            days_shift = int(mode[-3:].replace('-', '').replace('d', ''))

        daily_trades = trades_df[(trades_df['date'] == day - td(days=days_shift)) &
                                 ((trades_df['score'] >= min_score) | (trades_df['score'].isna())) &
                                 (trades_df['mode'] == real_mode)]
        daily_trades['date'] += td(days=days_shift)
        daily_trades['opening_time'] += td(days=days_shift)
        daily_trades['closing_time'] += td(days=days_shift)
        predictors_dict[mode] = daily_trades['predictor'].drop_duplicates().tolist()
        predictors_strats_dict[mode] = list(daily_trades[['predictor','strategy']].drop_duplicates().to_records(index=False))

    only_in_mode1 = sorted(list(set(predictors_dict[mode1])-set(predictors_dict[mode2])))
    only_in_mode2 = sorted(list(set(predictors_dict[mode2])-set(predictors_dict[mode1])))
    strats_only_in_mode1 = sorted([x for x in predictors_strats_dict[mode1] if x not in predictors_strats_dict[mode2]],key=lambda x: x[0])
    strats_only_in_mode2 = sorted([x for x in predictors_strats_dict[mode2] if x not in predictors_strats_dict[mode1]],key=lambda x: x[0])
    print ('predictors only in %s(%s):\n%s'%(mode1,min_score,only_in_mode1))
    print ('Strats only in %s(%s):\n%s'%(mode1,min_score,strats_only_in_mode1))
    print('============================')
    print ('predictors only in %s(%s):\n%s'%(mode2,min_score,only_in_mode2))
    print('Strats only in %s(%s):\n%s' % (mode2, min_score, strats_only_in_mode2))
    bb = 0

compare_trades_of_2_modes(dtdt(2022,3,9),'real','real0.75')