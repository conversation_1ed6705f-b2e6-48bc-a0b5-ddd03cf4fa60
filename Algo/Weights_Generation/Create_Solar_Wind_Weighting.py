from datetime import datetime as dtdt
from datetime import timedelta as td
from numpy import array as npa
import pandas as pd
import urllib, bs4
import os
import numpy as np



# based on 2017 data from :
#   https://ec.europa.eu/eurostat/statistics-explained/images/3/33/Fuels_in_the_final_energy_consumption_in_the_households_2017.png
RESIDENTIAL_PART_OF_GAS_BY_COUNTRY = {"DE": 0.382,"FR": 0.279,"IT": 0.525,
                                      "NL":0.71, "AT":0.22,"PL": 0.182, "CZ": 0.279,
                                      "HU":0.472,"SK": 0.545,"GB":0.62, "RO": 0.315,"HR":0.202,"ES": 0.243,
                                      "BE": 0.408,"GR": 0.082, "PT": 0.097, "DK":0.13}

# from
# https://www.oxfordenergy.org/wpcms/wp-content/uploads/2018/04/Natural-gas-demand-in-Europe-in-2017-and-short-term-expectations-Insight-35.pdf
TOTAL_GAS_CONSUMPTION_BY_COUNTRY = {'TR': 53598, 'IT': 75150, 'LT': 2306, 'LU': 785, 'FR': 42874, 'SK': 4722,
                                                   'IE': 5148, 'NO': 5565, 'SL': 902, 'DE': 92010, 'BE': 17265, 'TOTAL': 548201, 'ES': 31007,
                                                   'NL': 46438, 'DK': 3095, 'PL': 20381, 'FI': 2314, 'MK': 276, 'SW': 752, 'LV': 1300,
                                                   'HR': 3095, 'MT': 286, 'CH': 3591, 'BG': 3199, 'RO': 12289, 'EE': 493, 'PT': 6206,
                                                   'CZ': 8726, 'GB': 79562, 'AT': 9494, 'GR': 4922, 'HU': 10450}


RESIDENTIAL_NORMALIZATION = True
MULTIPLY_WEIGHTS_BY = 100


"""
POSTAL CODES ----> COORDS (MERGED GRID OPERATORS
"""

def round_lat_or_lon(lat, min_lat, max_lat, max_dist):
    if lat < min_lat:
        if lat + max_dist >= min_lat:
            lat = min_lat
        else:
            lat = np.nan
    elif lat > max_lat:
        if lat - max_dist <= max_lat:
            lat = max_lat
        else:
            lat = np.nan
    return lat




def find_nearest_grid_point(lat, resolution=0.5,direction="high"):
    lat = round(lat,2)

    lat_x100 = lat*100
    #lon_x100 = lon * 100
    res_x100 = resolution* 100

    lat_diff_low = lat_x100 % res_x100
    lat_diff_high = res_x100 - lat_diff_low
    #if lat_diff_high <= lat_diff_low:
    lat1 = (lat_x100 - lat_diff_low) / 100.0
    lat2 = (lat_x100 +lat_diff_high) / 100.0

    if direction == "abs":
        return lat1 if (lat_diff_low <= lat_diff_high) else lat2
    elif direction == "high":
        return lat2
    else:
        return lat1

def find_nearest_latlon_naive(lat,lon, resoultion, min_lat=None, min_lon=None, max_lat=None, max_lon=None, max_dist_in_degrees_for_fill=3):
    nearest_lat = find_nearest_grid_point(lat, resoultion, "abs")
    nearest_lon = find_nearest_grid_point(lon, resoultion, "abs")
    if min_lat is not None:
        assert max_lat is not None, 'if min_lat is given so must max_lat be'
        nearest_lat = round_lat_or_lon(nearest_lat,min_lat,max_lat,max_dist_in_degrees_for_fill)
    if min_lon is not None:
        assert max_lon is not None, 'if min_lon is given so must max_lon be'
        nearest_lon = round_lat_or_lon(nearest_lon, min_lon, max_lon, max_dist_in_degrees_for_fill)
    return nearest_lat, nearest_lon

def find_nearest_latlons_weights(lat,lon, resoultion):
    """
    We give weights to the surrounding latlons according to distances
    :param lat:
    :param lon:
    :param resoultion:
    :return:
    """
    lat1,lat2 = find_nearest_grid_point(lat,resoultion,"high"),find_nearest_grid_point(lat,resoultion,"low")
    lon1, lon2 = find_nearest_grid_point(lon, resoultion, "high"), find_nearest_grid_point(lon, resoultion, "low")
    minimals = []
    for lt in [lat1,lat2]:
        for ln in [lon1,lon2]:
            d = np.sqrt((lt-lat)**2+ (ln-lon)**2)
            minimals.append([lt,ln,d])

    minimal = sorted(minimals, key=lambda x: x[2])[0]
    df = pd.DataFrame(minimals,columns=['lat','lon','d'])

    sm = df['d'].sum()
    df['dist_weight'] = 1.0 / (df['d'] * 100)
    df['dist_weight'] = df['dist_weight'] / df['dist_weight'].sum()
    df = df.sort_values(['lat','lon'])
    return df



def add_latlonBox_to_postal_codes_csv(postals_csv, resolution=0.5):
    df = pd.read_csv(postals_csv)
    df["lat00"] = df["Latitude"].apply(lambda x: find_nearest_grid_point(x, resolution,direction="abs"))
    #df["lat2"] = df["Latitude"].apply(lambda x: find_nearest_grid_point(x, resolution, direction="high"))
    df["lon00"] = df["Longitude"].apply(lambda x: find_nearest_grid_point(x, resolution, direction="abs"))
    #df["lon2"] = df["Longitude"].apply(lambda x: find_nearest_grid_point(x, resolution, direction="high"))
    return df

def prepare_merged_caacities():
    capacities_dir = r"C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\installed_capacities"
    csvs_names_tuples = [(csv, csv.split("\\")[-1].split("_2016")[0]) for csv in [capacities_dir+"\\"+fl for fl in os.listdir(capacities_dir)]]

    dic = {}
    dic["50Hertz"] = {"type": ' TITLE ',
                      "postal": ' "Postal Code"',
                      "capacity":(' "Installed Power [kW]"','kw'),
                      "time":(' Time of commissioning ','%d.%m.%Y')}

    dic["Amprion"] = {"type": ' Energy carrier', "postal": ' Post code',"capacity":(' Installed power','mw'),"time":(' Startup time', '%d.%m.%Y')}

    dic["Tenne"] = {"type": ' Energy carrier',
                    "postal": ' ZIP code',
                    "capacity":(' Installed power','mw'),
                    "time":(' Commissioning', '%d.%m.%Y')}
    dic["TransnetBW"] = {"type": ' Energy carrier',
                    "postal": " Post code",
                    "capacity":(' Installed power','mw'),
                    "time":(' Time of commissioning', '%d.%m.%Y')}

    i = 0
    dfs = []
    for csv,name in csvs_names_tuples:
        #checked = []
        df = pd.read_csv(csv,sep=";")
        if "Trans" in name:
            df["TSO"] = df.index
        elif "50" in name:
            df["TSO"] = df["RULE ZONE"]
        """
        print df[dic[name]["capacity"][0]][:10]
        #print df.head()
        lst = list(df)
        if "Trans" in name:
            stp = True
        for k in dic[name].keys():
            try:
                assert dic[name][k] in lst, "Name: %s \\ didnt find column: %s" %(name,dic[name][k])
            except AssertionError:
                try:
                    assert dic[name][k][0] in lst, "Name: %s \\ didnt find column: %s" % (name, dic[name][k])
                except:
        """

        for k in dic[name].keys():
            try:
                df[k] = df[dic[name][k]]
            except:
                df[k] = df[dic[name][k][0]]
        df = df[["TSO"]+sorted(dic[name].keys())]
        #df["capacity_parts"] = df["capacity"].apply(lambda x: len(x.split(",")))
        df["capacity2"] = df["capacity"].apply(lambda x: float(x.split(",")[0])+(float(x.split(",")[1]) / 10**(len(x.split(",")[1]))) if len(x.split(",")) == 2 else float(x))
        dfs.append(df)
        i += 1

        a = 1

    df0 = dfs[0]
    for other in dfs[1:]:
        print(df0.shape)
        df0 = df0.merge(other, on=list(df0), how="outer")
    return df0


"""
OFFSHORE HANDLING
"""
def convert_degree_to_decimal(deg_str):
    """
    :param deg_str: e.g. unicode u"54\xb050'2?N'"
    :return:
    """
    d,m,s = deg_str.split(u'\xb0')[0], deg_str.split(u'\xb0')[1].split("'")[0], deg_str.split(u"?")[0].split("'")[1]
    d,m,s = float(d), float(m),float(s)
    dd = d + float(m)/60 + float(s)/3600
    return dd

def strip_degree(deg_str):
    """
    :param deg_str: e.g. unicode u"54\xb050'2?N'"
    :return:
    """
    d = float(deg_str.split(u'\xb0')[0])
    return d

def get_offshore_table(out_csv,local_html=r"C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Offshore_WIKI.html"):

    dic = {"names":[], "capacity":[], "Latitude":[], "Longitude":[]}

    text = open(local_html)
    soup = bs4.BeautifulSoup(text)
    table_obj = soup.find("table", {"class": "wikitable sortable"})

    table_2 = table_obj.find_all('tr')
    for tr in table_2:
        try:
            tds = tr.find_all("td")
            name = tds[0].get_text()
            cap = tds[1].get_text()
            lat_deg = convert_degree_to_decimal(tds[3].find("span",{"class":"latitude"}).get_text())
            lon_deg = convert_degree_to_decimal(tds[3].find("span", {"class": "longitude"}).get_text())
            print("Name: %s || Capacity: %s || LAT: %s, LON: %s" % (name, cap,lat_deg,lon_deg))
            dic["names"].append(name)
            dic["capacity"].append(cap)
            dic["Latitude"].append(lat_deg)
            dic["Longitude"].append(lon_deg)
        except IndexError:
            print("skupping")
        except:
            raise
        a = 1
    df = pd.DataFrame(dic)
    df["names"] = df["names"].apply(lambda x: x.encode('utf-8').strip())
    df.to_csv(out_csv, index=False)

def get_NG_consumption_table(out_csv,local_html=r"C: \ Users\ User\Documents\Ran\FlowPractice\NG\consumption_by_state.html"):

    keys = ["State","Stam"]
    dic = {"State":[]}
    for year in range(12,18):
        keys.append("Consumption_%s"%year)
    keys.append("View")
    for k in keys:
        dic[k] = []
    text = open(local_html)
    soup = bs4.BeautifulSoup(text)
    table_obj = soup.find("table", {"class": "data1"})

    table_2 = table_obj.find_all('tr',{"class":"DataRow"})
    j = 0
    for tr in table_2:
        #if j < 3:
        #    j+= 1
        #    continue
        try:
            tds = tr.find_all("td")
            i = 0
            for ind, td in enumerate(tds):
                if ind < 2:
                    continue
                txt = td.get_text()
                txt2 = txt
                try:
                    if "Cons" in keys[i] and "17" not in keys[i]:
                        txt2 = txt.replace(",","")
                        txt2 = int(txt2)
                    dic[keys[i]].append(txt2)
                except:
                    dic[keys[i]].append(txt)
                i += 1

        except IndexError:
            raise
            print("skupping")
        except:
            raise
        j+= 1
        a = 1
    df = pd.DataFrame(dic)
    df  = df[keys]
    for k in keys:
        try:
            df[k] = df[k].apply(lambda x: x.encode('utf-8').strip())
        except:
            print("Skipping column: %s" % k)
    df.to_csv(out_csv, index=False)
    df2 = df[["State","Consumption_14","Consumption_16"]]
    df2.to_csv(out_csv.split(".csv")[0]+"_14+16.csv", index=False)



"""
GENERAL FUNCTIONS
"""
def get_latlonBox_from_postal(postals_guide_df, postal_code,asset="NG"):
    """
    :param postals_guide_df: Contains 'Postal Code' with lat-lon box + closest couple (pd.DataFrame)
    :param postal_code: float
    :return: ints
    """
    if asset == "NG":
        key = "ZIP"
    else:
        key = "Postal Code"
    try:
        row = postals_guide_df[postals_guide_df[key] == postal_code].iloc[0]
    except IndexError:
        if asset == "NG":
            postals_guide_df["ZIP_int"] = postals_guide_df["ZIP"].apply(lambda x: int(x))
            row = postals_guide_df.ix[(postals_guide_df[key+"_int"] - int(postal_code)).abs().argsort()[:1]].iloc[0]
        else:
            row = postals_guide_df.ix[(postals_guide_df[key] - postal_code).abs().argsort()[:1]].iloc[0]
    #except:
    #    postals_guide_df["ZIP_int"] = postals_guide_df["ZIP_int"].apply(lambda x: int(x))
    #    row = postals_guide_df.ix[(postals_guide_df[key+"_int"] - postal_code).abs().argsort()[:1]].iloc[0]
    if asset == "EPEX":
        lat0, lon0, lat1,lat2,lon1,lon2 = row["lat00"],row["lon00"],row["lat1"],row["lat2"],row["lon1"],row["lon2"]
    elif asset == "NG":
        lat0, lon0, lat1, lat2, lon1, lon2 = row["LAT"], row["LNG"],None, None, None, None
    return lat0,lon0,lat1,lat2,lon1,lon2

def main(postal_ref_csv,capacities_csv="C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\merged_capacities.csv",
                        res=0.5,asset="NG"):

    if asset == "NG":
        postal_ref = pd.read_csv(postal_ref_csv, dtype={"ZIP":"str"})
    else:
        postal_ref = pd.read_csv(postal_ref_csv)

    ### Handle capacities
    df0 = pd.read_csv(capacities_csv)

    weights_dic = {}
    if asset == "EPEX":
        # Filter to relevant TYPES
        wind_solar_df = df0[(df0["type"] == "Solar") | (df0["type"] == "Windenergie Offshore") | (df0["type"] == "Windenergie")]

        for generation_type, type_group in wind_solar_df.groupby("type"):
            weights_dic[generation_type] = {"lat00":[],"lon00":[],"sum":[]}
            i = 0
            for postal, postal_subgroup in type_group.groupby("postal"):
                if postal == 156:
                    postal = 1561
                postal_total_capacity = postal_subgroup.sum()["capacity2"]
                lat0,lon0,lat1,lat2,lon1,lon2 = get_latlonBox_from_postal(postal_ref,postal)
                stack = [(lat0,"lat00"),(lon0,"lon00"),(postal_total_capacity,"sum")]
                for val, key in stack:
                    weights_dic[generation_type][key].append(val)
                print("inside Type: %s \\ postal # %s === %s ----->>>> CAPACITY = %s" % (generation_type, i, postal, postal_total_capacity))
                i += 1
    elif asset == "NG":
        i = 0
        weights_dic['general'] = {"lat00": [], "lon00": [], "sum": []}
        for postal, postal_subgroup in df0.groupby("Zip Code"):
            postal_total_capacity = postal_subgroup.sum()[' Consumption (GJ) ']
            success = False
            if len(postal.split("\n")) == 2:
                if "(" in postal and ")" in postal:
                    postal2 = postal.split("\n")[0]
                    suffix = postal.split("\n")[1][1:-1]
                    try:
                        lat0,lon0 = float(suffix.split(",")[0]), float(suffix.split(",")[1])
                    except:
                        a = 1
                    para_lat0, para_lon0,a,b,c,d = get_latlonBox_from_postal(postal_ref, postal2)
                    diff = abs(lat0 - para_lat0), abs(lon0 - para_lon0)
                    postal = postal2

                    success = True
            if not success:
                lat0, lon0, lat1, lat2, lon1, lon2 = get_latlonBox_from_postal(postal_ref, postal2)

            stack = [(lat0, "lat00"), (lon0, "lon00"), (postal_total_capacity, "sum")]
            for val, key in stack:
                weights_dic["general"][key].append(val)
            print("inside\\ postal # %s === %s ----->>>> CAPACITY = %s" % ( i, postal, postal_total_capacity))
            i += 1
    return weights_dic

def create_weights_by_capacity(capacities_df, lat_array,lon_array,rescale=100):
    """
    :param capacities_df: should include "lat00", 'lon00', 'sum', normalized
    :param lat_array: (with duplictes (like in the Mongo - all the couples)
    :param lon_array: (with duplictes (like in the Mongo - all the couples)
    :param rescale: Multiply all the weights by this scalar to avoid too small entries
    :return:
    """
    weights_array = []
    for lat,lon in zip(lat_array,lon_array):
        sub_df = capacities_df[(capacities_df["lat00"] == lat) & (capacities_df["lon00"] == lon)]
        if sub_df.shape[0] == 0:
            weights_array.append(0)
        else:
            weights_array.append((sub_df.iloc[0]["normalized"])*100)
    return weights_array


def create_weights_by_population_Europe(csv_file, geo_config, residential_normalization=RESIDENTIAL_NORMALIZATION):
    europe_stats = pd.read_csv(csv_file)
    europe_stats = europe_stats.rename(columns={"Population":"population","Latitude":"lat","Longitude":"lon", "City Name":"city","CountryCode":"country"})
    #for geo_config in geo_configs:
    name, countries = geo_config[0], geo_config[1]
    df = europe_stats[europe_stats["country"].isin(countries)]

    if residential_normalization:
        df['population_weighted'] = df.apply(lambda x: x['population'] * RESIDENTIAL_PART_OF_GAS_BY_COUNTRY[x['country']], axis=1)
        df['weight'] = df['population_weighted'] / df['population_weighted'].sum()
    else:
        df['weight'] = df['population'] / df['population'].sum()
    df['weight'] = df['weight'] * MULTIPLY_WEIGHTS_BY

    df2 = df.copy()

    df[['nearest_lat','nearest_lon']] = pd.DataFrame(df.apply(lambda x: find_nearest_latlon_naive(x['lat'],x['lon'],0.5), axis=1).values.tolist(), index=df.index)[[0,1]]
    df = df.reset_index()
    latlons_final_df = pd.DataFrame()
    s = df.shape[0]
    for i, row in df.iterrows():
        current_df = find_nearest_latlons_weights(row['lat'], row['lon'], 0.5)
        current_df['population_weight_weighted_corners'] = row['weight'] * current_df['dist_weight']
        if i == 0:
            latlons_final_df = current_df
        else:
            latlons_final_df = latlons_final_df.merge(current_df,how="outer", on=list(latlons_final_df))
        if i % 100 == 0:
            print('%s / %s'%(i,s))
    latlons_final_df = latlons_final_df.groupby(['lat','lon']).sum()[['population_weight_weighted_corners']].reset_index().rename(columns={'lat':'nearest_lat', 'lon': 'nearest_lon'})
    latlons_final_df.sort_values(['nearest_lat', 'nearest_lon']).to_csv(r'C:\ Users\ User\Documents\Ran\FlowPractice\NG_EU\weights\\%s_Population_weights_corners_normalization.csv'%name,index=False)

    df_grouped = df.groupby(['nearest_lat','nearest_lon']).sum().reset_index()
    df_grouped = df_grouped[['nearest_lat', 'nearest_lon', 'weight']]
    df_grouped.to_csv(r'C:\ Users\ User\Documents\Ran\FlowPractice\NG_EU\weights\\%s_Population_weights_naive_nearest.csv'%name,index=False)
    # merge 2 methods
    mrgd = latlons_final_df.merge(df_grouped, on=['nearest_lat', 'nearest_lon'], how="left").fillna(0)
    mrgd.to_csv(r'C:\ Users\ User\Documents\Ran\FlowPractice\NG_EU\weights\%s_Population_weights_both_methods_multipliedBy=%s.csv'%(name, MULTIPLY_WEIGHTS_BY),index=False)

def aggregate_by_min_latlon(csv_file,direction='Lon',min_or_max='min',val=0, write=True):
    # sum the points out of the grid to the nearest lat / lon
    df = pd.read_csv(csv_file)
    col = 'nearest_lon' if direction == 'Lon' else 'nearest_lat'
    df[col] = df[col].apply(lambda x: max(val,x) if min_or_max == 'min' else min(val,x))
    df = df.groupby(['nearest_lon','nearest_lat']).sum().reset_index()
    new_csv = csv_file.split('.csv')[0]+'_%s%s=%s.csv'%(min_or_max,direction,val)
    if write:
        df.to_csv(new_csv, index=False)
    return df, new_csv



if __name__ == '__main__':
    a = 1
    # Germany weights
    """
    csv = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\de_postal_codes.csv"
    csv_05 = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\de_postal_codes_latlonBox_Res=0.5.csv"
    csv_025 = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\de_postal_codes_latlonBox_Res=0.25.csv"
    
    csv_05N = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\de_postal_codes_latlonBox_Res=0.5_NEW.csv"
    csv_025N = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\de_postal_codes_latlonBox_Res=0.25_NEW.csv"
    """
    # Germany first source
    #csv_germany_poulation = "C:\ Users\ User\Downloads\population_DE_raw.csv"
    #df = pd.read_csv(csv_germany_poulation)

    csv_file = r"C:\ Users\ User\Documents\Ran\FlowPractice\NG_EU\weights\Info from Web\Europe_population_by_city.csv"
    core_countries = ['DE','NL','GB'] #,'DK','FR']
    surrounding1 = ['DK', 'FR']
    surrounding2 = ['PL','CZ', 'SK','AT','IT']


    geo_configs = [('DE-NL', ['DE','NL']),('core1', core_countries),('wide1', core_countries+surrounding1), ('wide2', core_countries+surrounding1+surrounding2)]
    #geo_configs = geo_configs[1:2]
    # weight HDD CDD population Europe NG Europe NG EU NG_EU

    ### Wrap the DFs generation
    """
    
    latlon_capacities_dic_by_type_05 = main(csv_025N,csv_05N,res=0.25)
    types_dic = {}
    for generation_type in latlon_capacities_dic_by_type_05.keys():
        df = pd.DataFrame(latlon_capacities_dic_by_type_05[generation_type])
        final_df = df.groupby(["lat00","lon00"]).sum()
        final_df["lat"] = final_df.index.get_level_values(0)
        final_df["lon"] = final_df.index.get_level_values(1)
        final_df = final_df.reset_index()
        types_dic[generation_type] = final_df
    
        
        #types_dic[generation_type] = {"lat00":[],"lon00":[], "sum":[]}
        
        #for latlon_tup, grp in df.groupby(["lat00", "lon00"]):
        #    lat = latlon_tup[0]
        #    lon = latlon_tup[1]
        #    a = 1
        #types_dic[generation_type]
        #a = 1
    
    for k in types_dic.keys():
        df = types_dic[k]
        total = df.sum()["sum"]
        df["normalized"] = df["sum"].apply(lambda x: x / total)
        df = df[["lat00", "lon00","sum","normalized"]]
        types_dic[k] = df
    """

    aa = 1


    ### generate normalized offshore WIKI
    """
    
    solar_df = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Final_Sums_Solar.csv")
    wind_df = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Final_Sums_Windeneregie.csv")
    off_wiki_05 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_OffShore_Wiki_latlonBox_Res=0.5.csv")
    united_wind_05 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_United_Res=0.5.csv")
    
    solar_df_025 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Final_Sums_Solar_0.25.csv")
    wind_df_025 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Final_Sums_Windenergie_0.25.csv")
    off_wiki_025 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_OffShore_Wiki_latlonBox_Res=0.25.csv")
    united_wind_025 = pd.read_csv("C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_United_Res=0.25.csv")
    """

    ### Unite winds
    """
    i = 0
    for off,on in zip([off_wiki_05,off_wiki_025],[wind_df, wind_df_025]):
        new = on.merge(off,on=["lat00","lon00","sum"], how="outer")
        new = new[["lat00","lon00","sum"]]
    
        new2 = new.groupby(["lat00","lon00"]).sum()
    
        new2["lat00"] = new2.index.get_level_values(0)
        new2["lon00"] = new2.index.get_level_values(1)
    
        new_total= new2.sum()["sum"]
        new2["normalized"] = new2["sum"] / new_total
        new2 = new2[["lat00","lon00","sum","normalized"]]
    
        f = "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_United_Res=0.25.csv" if i ==0 else "C:\ Users\ User\Documents\Ran\FlowPractice\EEX\DWD\Solar Capacities\Windenergie_United_Res=0.5.csv"
        new2.to_csv(f,index=False)
        i += 1
    """

    ### Wrap the weights array generation
    """
    db = MongoClient("mongoresearch")["Xconfiguration"]
    col = db["DE_DWD_parameters"]
    #col = db["GFS_parameters"]
    
    GFS_lat_array = col.find_one({"type": "lat"})["val"]
    GFS_lon_array = col.find_one({"type": "lon"})["val"]
    #GFS_lat_array = col.find_one({"type": "05Weight"})["val"][1]
    #GFS_lon_array = col.find_one({"type": "05Weight"})["val"][0]
    
    
    ## rescaling by 100
    solar_05 = create_weights_by_capacity(solar_df,GFS_lat_array, GFS_lon_array)
    wind_05 = create_weights_by_capacity(united_wind_05,GFS_lat_array, GFS_lon_array)
    
    #solar_025 = create_weights_by_capacity(solar_df_025,GFS_lat_array, GFS_lon_array)
    #wind_025 = create_weights_by_capacity(united_wind_025,GFS_lat_array, GFS_lon_array)
    
    model = "ICON"
    #model = "GFS"
    
    stack = []
    for weighting,name in [(solar_05,"05Weight_Solar"),(wind_05,"05Weight_Wind")]: #, (solar_025,"025Weight_Solar"),(wind_025,"025Weight_Wind")]: # WindWeight
        str = (name.split("_")[1]) if model == "ICON" else (name.split("Weight")[0])
        stack.append({"type": name, "val": [GFS_lon_array, GFS_lat_array, weighting],"Info":" This weighting is proportional to the %s Capcities of every coord. Based on Open Source Data from 'https://www.netztransparenz.de/EEG/Anlagenstammdaten' "% str})
    
    col.insert_many(stack)
    """


    ### NG handling
    """
    """
    ## 1 : take the original consumptions and cumsum on zip
    cons_csv = r"C:\Users\<USER>\Documents\Ran\FlowPractice\NG\consumption_weights\Natural_Gas_Consumption_by_ZIP_Code_-_2010.csv"
    postal_csv = r"C:\Users\<USER>\Documents\Ran\FlowPractice\NG\consumption_weights\ZipCodes_coords.csv"
    #main(postal_csv,cons_csv)
    original_consumptions = pd.read_csv(cons_csv)
    zips_coords = pd.read_csv(postal_csv, dtype={"ZIP":'str'})
    for zip, zip_df in original_consumptions.groupby("Zip Code"):
        a = 1




