import requests
import ast
import pandas as pd
import numpy as np
import time
from datetime import datetime as dtdt
from matplotlib import pyplot as plt
import json
from Algo.Weights_Generation.Create_Solar_Wind_Weighting import find_nearest_latlons_weights
from _ctypes import PyObj_FromPtr
import json
import re
from Algo.General.json_handle import *
#def create_weights_from_prod_latlon(production_latlon_csv):
import os
from Algo.Utils.files_handle import HOME




MAX_WEIGHTS_RATIO = 5000 # max weight / min weight must be < 5000
MODE = 'SA'


SPECIAL_WEIGHTS = ['South_Arabica']
SPECIAL_WEIGHTS_LATLONS_LIMITS = {'South_Arabica':[-9999,-15,-9999,9999]} # min_lat,max_lat,min_lon,max_lon

def add_weights_to_config_file(mode='Total',addition_str=''):
    import pandas as pd
    import json

    config = r"C:\Users\<USER>\PycharmProjects\Algo_ec2\Auxiliary_Files\config_aligned.json"
    latlon_total = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","Final_%saggregated_weights_Total_MaxRatio=5000.csv")%addition_str
    latlon_arabica = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","Final_%saggregated_weights_Arabica_MaxRatio=5000.csv")%addition_str
    latlon_robusta = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","Final_%saggregated_weights_Robusta_MaxRatio=5000.csv")%addition_str
    f = {'Total':latlon_total,'Arabica':latlon_arabica,'Robusta':latlon_robusta}[mode]
    df = pd.read_csv(f)

    df['nearest_lon'] = df['nearest_lon'] % 360
    df2 = df.rename(
        columns={'nearest_lon': 'longitude', 'nearest_lat': 'latitude', 'production_weight_corners': 'weight'})
    df2[['latitude', 'longitude', 'weight']].to_dict()
    with open(config) as f:
        input_json = json.load(f)
    model_latlon_df = pd.DataFrame({'latitude': input_json["GFS_SA"]['model_lat'],
                                    'longitude': input_json["GFS_SA"]['model_lon']})
    original_latlon_df_size = model_latlon_df.shape[0]
    model_latlon_df = model_latlon_df.merge(df2,on=['latitude','longitude'],how='outer')
    model_latlon_df['weight'] = model_latlon_df['weight'].fillna(0)
    assert model_latlon_df.shape[0] <= original_latlon_df_size
    #dct = df2[['latitude', 'longitude', 'weight']].to_dict('list')
    aligned_dct = model_latlon_df[['latitude', 'longitude', 'weight']].to_dict('list')

    input_json['BR_%sTrunc_weight'%mode] = aligned_dct

    with open(config, "w") as f_out:
        json.dump(input_json, f_out, indent=True)


def calculate_cornered_weights(production_latlon_csv,outfile,typ):
    df = pd.read_csv(production_latlon_csv)
    df = df.dropna()
    try:
        df['quantity'] = pd.to_numeric(df['quantity'].apply(lambda x: x.replace(' ','')))
    except:
        pass

    if typ in SPECIAL_WEIGHTS:  ##
        df = df[(df['lat']>=SPECIAL_WEIGHTS_LATLONS_LIMITS[typ][0])&
                (df['lat']<=SPECIAL_WEIGHTS_LATLONS_LIMITS[typ][1])&
                (df['lon']>=SPECIAL_WEIGHTS_LATLONS_LIMITS[typ][2])&
                (df['lon']<=SPECIAL_WEIGHTS_LATLONS_LIMITS[typ][3])]
    df['weight'] = df['quantity'] / df['quantity'].sum()

    distance_normalization = pd.DataFrame()

    for i, row in df.iterrows():
        current_df = find_nearest_latlons_weights(row['lat'],row['lon'],0.5)
        current_df['production_weight_corners'] = row['weight'] * current_df['dist_weight']
        current_df['production_corners'] = row['quantity'] * current_df['dist_weight']

        if distance_normalization.shape[0] == 0:
            distance_normalization = current_df
        else:
            distance_normalization = distance_normalization.merge(current_df,on=list(current_df),how='outer')
        if i % 20 == 0:
            print ('Reached i = %s'%i)

    # sum up the few out-of-bounds points
    assert MODE in ['SA','SAold']

    total_prod = distance_normalization['production_corners'].sum()
    if MODE == 'SAold':  # we have some areas missing
        out_of_lon = (distance_normalization['lon'] < -65)
        out_of_lon2 = (distance_normalization['lon'] > -45)
        out_of_lat = distance_normalization['lat'] > -10

        out_of_lon_prod_ratio = distance_normalization[out_of_lon]['production_corners'].sum() / total_prod
        out_of_lon2_prod_ratio = distance_normalization[out_of_lon2]['production_corners'].sum() / total_prod
        out_of_lat_prod_ratio = distance_normalization[out_of_lat]['production_corners'].sum() / total_prod
        distance_normalization.loc[out_of_lon,'lon'] = -65
        distance_normalization.loc[out_of_lon2, 'lon'] = -45
        distance_normalization.loc[out_of_lat, 'lat'] = -10
        print('Had (%s) percent of total production in out of Lon'%out_of_lon_prod_ratio)
        print('Had (%s) percent of total production in out of Lon2' % out_of_lon2_prod_ratio)
        print('Had (%s) percent of total production in out of Lat' % out_of_lat_prod_ratio)
    elif MODE == 'SA':
        # The only gap left in the Extended gribs
        out_of_lon2 = (distance_normalization['lon'] > -40)
        distance_normalization.loc[out_of_lon2, 'lon'] = -40

    final_df = distance_normalization.groupby(['lat','lon']).sum()[['production_weight_corners','production_corners']].reset_index().rename(columns={'lat':'nearest_lat', 'lon': 'nearest_lon'}).sort_values('production_corners')
    final_df['production_weight_corners'] *= 1000

    max_weight = final_df['production_weight_corners'].max()
    min_allowed_weight = max_weight / MAX_WEIGHTS_RATIO
    final_df = final_df[final_df['production_weight_corners']>min_allowed_weight]

    final_df.to_csv(outfile,index=False)
    aa = 1

def json_to_10perline():
    config = r"C:\Users\<USER>\PycharmProjects\Algo_ec2\Auxiliary_Files\config_aligned_Ext.json"
    config_out = r"C:\Users\<USER>\PycharmProjects\Algo_ec2\Auxiliary_Files\config_aligned_TEST.json"
    with open(config) as f:
        input_json = json.load(f)
    #with open(config_out,"w") as g:
    json_str = json.dumps(input_json,cls=MyEncoder,sort_keys=True,indent=True)
    json2 = json.loads(json_str)
    for k in json2.keys():
        if 'weight' in k and 'BR' in k:
            latlen = len(json2[k]["latitude"])
            lonlen = len(json2[k]["latitude"])
            weightlen = len(json2[k]["weight"])
            latlenold = len(input_json[k]["latitude"])
            lonlenold = len(input_json[k]["latitude"])
            weightlenold = len(input_json[k]["weight"])
            print ("%s ||| %s | %s | %s "% (k,latlen,lonlen,weightlen))
            print("OLD %s ||| %s | %s | %s " % (k, latlenold, lonlenold, weightlenold))
            print ('-------')
    aa = 1

json_to_10perline()
## calculating
for typ in ['Total','Arabica','Robusta','South_Arabica']:
    outfile = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","Final_SAnew_aggregated_weights_%s_MaxRatio=%s.csv")%(typ,MAX_WEIGHTS_RATIO)
    production_latlon_csv = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","brazil_production_tab20_%s_latlon.csv")%typ
    if typ == 'South_Arabica':
        production_latlon_csv = os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","brazil_production_tab20_%s_latlon.csv")%'Arabica'
    #calculate_cornered_weights(production_latlon_csv,outfile,typ)
    pass

## final saving
for mode in ['Total','Arabica','Robusta']:
    #add_weights_to_config_file(mode,addition_str='SAnew_')
    pass
df = pd.read_csv(os.path.join(HOME,"COFFEE","Brazil_produciton","final_production_latlons","Final_aggregated_weights_Total_MaxRatio=5000_first.csv"))
aa =1